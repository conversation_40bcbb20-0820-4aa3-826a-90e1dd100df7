namespace PaySpace.Venuta.Modules.Leave.Abstractions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions;

    public interface IEmployeeLeaveService : IGenericService<EmployeeLeaveAdjustment>
    {
        Task<Tenant> GetTenantFromRelatedPrimaryKeyAsync(long leaveAdjustmentId);

        Task<EmployeeLeaveAdjustment> FindByIdWithNavigationAsync(Tenant tenant, long id);

        Task<EmployeeLeaveAdjustment> FindByIdAsync(Tenant tenant, long id);

        Task<EmployeeLeaveSetup> GetLeaveSetupAsync(long employeeId, DateTime effectiveDate);

        Task<long?> GetCompanySchemeIdAsync(long employeeId, long? companyLeaveSetupId, DateTime effectiveDate);

        Task<long?> GetCompanySchemeIdAsync(long employeeId, DateTime effectiveDate);

        long? GetCompanySchemeId(long employeeId, DateTime effectiveDate);

        Task<bool> IsLinkedToLeaveSchemeAsync(long employeeId, DateTime effectiveDate);

        Task<bool> LeaveTypeValidAsync(long employeeId, LeaveType leaveType, DateTime effectiveDate);

        Task<double> GetLeaveBalanceAsync(
            long companyId,
            long employeeId,
            long frequencyId,
            long? currentLeaveApplicationId,
            LeaveType leaveType,
            long? companyLeaveSetupId,
            DateTime? startDate,
            long? concessionId = null);

        Task<double> GetLeaveBalanceAsync(long employeeId, LeaveType leaveType, long runId, long? companyLeaveSetupId, long? historicalConcessionId);

        Task<double> GetLeaveBalanceAsync(long employeeId, IEnumerable<long> companyLeaveSetupIds, long? runId, DateTime? effectiveDate);

        Task<(double Total, IList<(long RunId, double TotalDays)> Adjustments)> GetPendingTotalAsync(
            long employeeId,
            long? currentLeaveApplicationId,
            LeaveType leaveType,
            long? companyLeaveSetupId,
            long? concessionId = null);

        Task<(double Total, IList<(long RunId, double TotalDays)> Adjustments)> GetFutureTotalAsync(
            long companyId,
            long employeeId,
            long frequencyId,
            long? currentLeaveApplicationId,
            LeaveType leaveType,
            long? companyLeaveSetupId,
            DateTime? startDate,
            long? concessionId = null);

        Task<UpcomingLeaveResult> GetUpcomingLeaveAsync(long employeeId, DateTime effectiveDate);

        Task<List<UpcomingLeaveResult>> GetUpcomingLeaveAsync(long[] employeeIds, DateTime effectiveDate);

        Task<ICollection<DateTime>> GetValidPartialDayDatesAsync(long employeeId, LeaveType leaveType, long? companyLeaveSetupId);

        Task<double> GetPartialTotalAsync(long employeeId, long leaveAdjustmentId, DateTime startDate, DateTime? endDate);

        IQueryable<EmployeeLeaveAdjustmentDetailResult> GetLeaveAdjustments(bool includeHidden = false);

        IQueryable<EmployeeLeaveAdjustmentDetailResult> GetLeaveAdjustments(long employeeId, bool includeHidden = false);

        Task<(DateTime StartDate, DateTime EndDate)> GetStartEndDateAsync(long leaveAdjustmentId);

        Task<EmployeeLeaveAdjustment> CancelLeaveAsync(long userId, long companyId, long leaveAdjustmentId);

        Task AddLeaveTransactionAsync(long componentId, CompanyRunPeriodResult runPeriodResult, double amount, LeaveType leaveType, long companyLeaveSetupId, long employeeId, string additionalComment);

        Task<EmployeeLeaveAdjustment> AddAsync(long userId, long companyId, long employeeId, EmployeeLeaveAdjustment entity);

        Task<EmployeeLeaveAdjustment> UpdateAsync(long userId, long companyId, long employeeId, EmployeeLeaveAdjustment entity);

        Task DeleteAsync(long companyId, long employeeId, EmployeeLeaveAdjustment entity);

        Task OverrideLeaveBalanceAsync(long employeeId, EmployeeLeaveAdjustment leaveAdjustment);

        Task<IList<EmployeeLeaveAdjustmentDetailResult>> FilterCanceledLeaveAsync(IList<long> employeeIds, IList<EmployeeLeaveAdjustmentDetailResult> leaveAdjustments);

        bool IsLeaveStatusChanged(EmployeeLeaveAdjustment adjustment);

        Task<EmployeeLeaveAdjustment> CreateCancellationAsync(EmployeeLeaveAdjustment leaveAdjustment, LeaveStatus leaveStatus, long userId, long companyId, long frequencyId, string comment);

        Task<bool> IsLeaveApplicationCancelledAsnyc(long employeeId, long leaveAdjustmentId, long cancellationId);

        bool IsEntityModified(EmployeeLeaveAdjustment entity);

        Task<bool> IsCompanyRunClosedAsync(long leaveAdjustmentId, long companyRunId);

        Task<int> GetNumberOfApplicationsAsync(long employeeId, LeaveType leaveType, DateTime startDate, DateTime endDate);

        Task<bool> ShowThirteenthChequeAsync(long employeeId, long companyId, long companyRunId);

        Task<double?> GetSellDays(long employeeId, long leaveAdjustmentId);

        Task DeleteLeaveSoldAdjustmentAsync(long employeeId, EmployeeLeaveAdjustment leaveAdjustment);

        Task<long> GetUpdatedLeaveSetupIdForExpiredSchemeApplicationAsync(long employeeId, long companyId, long runId, long companyLeaveSetupId);

        double CalculateTotalDays(EmployeeLeaveAdjustment leaveAdjustment, double payRateHours, bool allowHours);

        void CalculateDaysAndHours(EmployeeLeaveAdjustment leaveAdjustment, bool allowHours, double payRateHours);

        Task<bool> IsActiveLeavePresentOnTerminationAsync(long employeeId, DateTime terminationDate);
    }
}