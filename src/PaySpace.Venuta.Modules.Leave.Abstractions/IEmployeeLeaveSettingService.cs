namespace PaySpace.Venuta.Modules.Leave.Abstractions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;

    public interface IEmployeeLeaveSettingService
    {
        Task<EmployeeLeaveSettings> GetLeaveSettingsAsync(long companyId, long employeeId, LeaveType leaveType, long? companyLeaveSetupId, DateTime? effectiveDate, bool isAdminUser = false);

        Task<IEnumerable<EnumLeaveType>> GetLeaveTypesAsync(long employeeId);

        Task<bool> HasLeaveTypeAsync(long companyRunId, long employeeId, LeaveType leaveType);

        Task<IEnumerable<(long CompanyLeaveSetupId, string LeaveDescription)>> GetBucketsAsync(long employeeId, LeaveType leaveType, DateTime? effectiveDate = null);

        Task<IQueryable<CompanyLeaveReason>> GetReasonsAsync(long companyId, long employeeId, LeaveType leaveType, long? companyLeaveSetupId);

        Task<IList<DayOfWeek>> GetRosterInactiveWorkingDaysAsync(long companyId, long employeeId);

        Task<double> GetDefaultHoursPerDayAsync(long employeeId, DateTime? effectiveDate);

        Task<IList<DayOfWeek>> GetDefaultInactiveWorkingDaysAsync(long companyId, long employeeId);
    }
}