namespace PaySpace.Venuta.Modules.Payslips.UK
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Configuration;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.UK;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;

    [CountryService(CountryCode.GB)]
    public sealed class UkPayslipHeaderService : PayslipHeaderService, IUkPayslipHeaderService
    {
        private record TaxRegimeInfo(long CustomFormId, string FieldValue);

        private readonly IEmployeeService employeeService;
        private readonly IScopedCache scopedCache;
        private readonly ICustomFormService<EmployeeCustomForm> customFormService;
        private readonly ICustomFieldService customFieldService;

        private const long TaxRegimeFieldId = 2914;
        private const long TaxCodeFieldId = 2912;
        private const long Week1Month1FieldId = 2913;

        public UkPayslipHeaderService(
            IDbContextRepository<PayslipHeader> repository,
            IConfiguration configuration,
            IBureauConfigService bureauConfigService,
            ICompanyRunService companyRunService,
            IEmployeeService employeeService,
            IPayRateService payRateService,
            IScopedCache scopedCache,
            ICustomFormService<EmployeeCustomForm> customFormService,
            ICustomFieldService customFieldService) : base(
                repository,
                configuration,
                bureauConfigService,
                companyRunService,
                employeeService,
                payRateService,
                scopedCache)
        {
            this.employeeService = employeeService;
            this.scopedCache = scopedCache;
            this.customFormService = customFormService;
            this.customFieldService = customFieldService;
        }

        public override async Task CreateNewPayslipAsync(long employeeId, long runId, int occurrence, bool bypassCache = false)
        {
            if (!await this.PayslipHeaderExistAsync(employeeId, runId, bypassCache))
            {
                await this.AddAsync(new PayslipHeader
                {
                    CompanyRunId = runId,
                    EmployeeId = employeeId,
                    PayslipOccurence = occurrence,
                    HasBeenEdited = false
                });

                this.scopedCache.Remove($"EditPayslip:PayslipExist:Employee:{employeeId}:Run:{runId}");

                var companyId = await this.employeeService.GetCompanyIdAsync(employeeId);
                await this.UpdateTaxDetailCustomFormAsync(companyId, employeeId);
            }
        }

        public async Task UpdateTaxDetailCustomFormAsync(long companyId, long employeeId)
        {
            var terminationDate = await this.GetLatestTerminationDateAsync(employeeId);
            if (!terminationDate.HasValue)
            {
                return;
            }

            var taxCodeField = await this.customFieldService.GetCustomFieldAsync(companyId, "CustomForm", "TAXD_B", "TCODE");
            if (taxCodeField == null)
            {
                return;
            }

            var latestEffectiveTaxRegime = await this.GetLatestEffectiveTaxRegimeAsync(companyId, employeeId, taxCodeField.CustomFormCategoryId.GetValueOrDefault(), terminationDate.Value);
            if (latestEffectiveTaxRegime == null || latestEffectiveTaxRegime.FieldValue == null)
            {
                return;
            }

            var taxCodeValue = this.GetTaxCodeValue(latestEffectiveTaxRegime.FieldValue);
            if (taxCodeValue == null)
            {
                return;
            }

            var hasTerminationTaxDetailForm = this.HasTerminationTaxDetailForm(companyId, employeeId, taxCodeField.CustomFormCategoryId.GetValueOrDefault(), taxCodeValue);

            if (!hasTerminationTaxDetailForm)
            {
                await this.UpdateLatestTaxDetailFieldsAsync(latestEffectiveTaxRegime.CustomFormId, taxCodeValue, terminationDate.Value);
            }
        }

        private async Task<TaxRegimeInfo?> GetLatestEffectiveTaxRegimeAsync(long companyId, long employeeId, long categoryId, DateTime terminationDate)
        {
            return await this.customFormService
                .GetWithCustomForms(new Tenant(companyId, employeeId), categoryId, CustomFormLevel.Bureau)
                .Where(_ => _.EffectiveDate <= terminationDate)
                .OrderByDescending(_ => _.EffectiveDate)
                .Select(_ => new TaxRegimeInfo(
                    _.CustomFormId,
                    _.CustomFields.FirstOrDefault(field => field.CustomFieldId == TaxRegimeFieldId).FieldValue
                ))
                .FirstOrDefaultAsync();
        }

        private string GetTaxCodeValue(string fieldValue)
        {
            return fieldValue switch
            {
                "Wales (Cymru)" => "C0T",
                "England & Northern Ireland" => "0T",
                "Scotland" => "S0T",
                _ => null
            };
        }

        private async Task UpdateLatestTaxDetailFieldsAsync(long customFormId, string taxCodeValue, DateTime terminationDate)
        {
            var fieldValues = await this.customFormService.Query()
                .Include(_ => _.CustomFields)
                .Where(_ => _.CustomFormId == customFormId)
                .FirstOrDefaultAsync();

            var customForm = new EmployeeCustomForm
            {
                EffectiveDate = terminationDate.AddDays(1),
                CustomFields = this.MapCustomFields(taxCodeValue, fieldValues),
                BureauCustomFormCategory = fieldValues.BureauCustomFormCategory,
                BureauCustomFormCategoryId = fieldValues.BureauCustomFormCategoryId,
                Code = fieldValues.Code,
                CompanyCustomFormCategory = fieldValues.CompanyCustomFormCategory,
                CompanyCustomFormCategoryId = fieldValues.CompanyCustomFormCategoryId,
                Description = fieldValues.Description,
                EmployeeId = fieldValues.EmployeeId,
                RelatedPrimaryKey = fieldValues.RelatedPrimaryKey,
                ParentCustomFormId = fieldValues.ParentCustomFormId,
                Employee = fieldValues.Employee,
                InactiveDate = fieldValues.InactiveDate
            };

            this.Repository.Context.Add(customForm);
            await this.Repository.Context.SaveChangesAsync();
        }

        private Task<DateTime?> GetLatestTerminationDateAsync(long employeeId)
        {
            return this.Repository.Context.Set<EmployeeEmploymentStatus>()
                .Where(_ => _.EmployeeId == employeeId && _.EmploymentDate <= DateTime.Today)
                .OrderByDescending(_ => _.EmploymentDate)
                .Select(_ => _.TerminationDate)
                .FirstOrDefaultAsync();
        }

        private bool HasTerminationTaxDetailForm(long companyId, long employeeId, long categoryId, string taxCodeValue)
        {
            return this.customFormService
                .GetWithCustomForms(new Tenant(companyId, employeeId), categoryId, CustomFormLevel.Bureau)
                .Any(_ => _.CustomFields.Any(_ => _.CustomFieldId == TaxCodeFieldId && _.FieldValue == taxCodeValue));
        }

        private CustomFieldList<EmployeeCustomFormFieldValue> MapCustomFields(string taxCodeValue, EmployeeCustomForm fieldValues)
        {
            var customFields = new CustomFieldList<EmployeeCustomFormFieldValue>();

            foreach (var customField in fieldValues.CustomFields)
            {
                switch (customField.CustomFieldId)
                {
                    case TaxCodeFieldId:
                        customField.FieldValue = taxCodeValue;
                        break;
                    case Week1Month1FieldId:
                        customField.FieldValue = "true";
                        break;
                }

                customFields.Add(new EmployeeCustomFormFieldValue
                {
                    CustomFieldId = customField.CustomFieldId,
                    FieldValue = customField.FieldValue,
                    EmployeeCustomFormId = customField.EmployeeCustomFormId,
                    BureauCustomField = customField.BureauCustomField,
                    CompanyCustomField = customField.CompanyCustomField,
                    Code = customField.Code,
                    CustomFieldType = customField.CustomFieldType,
                    EffectiveDate = customField.EffectiveDate,
                    EmployeeCustomForm = customField.EmployeeCustomForm,
                    ShouldValidate = customField.ShouldValidate
                });
            }

            return customFields;
        }
    }
}