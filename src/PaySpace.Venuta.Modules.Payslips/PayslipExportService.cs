namespace PaySpace.Venuta.Modules.Payslips
{
    using System;
    using System.Collections.Generic;
    using System.Drawing;
    using System.IO;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using DevExpress.Spreadsheet;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Bureau;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Services.Tax;

    public class PayslipExportService : IPayslipExportService
    {
        private readonly ApplicationContext context;
        private readonly ICompanyService companyService;
        private readonly ICompanySettingService companySettingService;
        private readonly IEmployeeService employeeService;
        private readonly ITaxBreakdownService taxBreakdownService;
        private readonly ICompanyRunService companyRunService;
        private readonly ICountryTaxYearService countryTaxYearService;
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly IPayslipServiceFactory payslipServiceFactory;
        private readonly IStringLocalizer localizer;
        private readonly IMessageBus messageBus;

        public PayslipExportService(
            ApplicationContext context,
            ICompanyService companyService,
            ICompanySettingService companySettingService,
            IEmployeeService employeeService,
            IStringLocalizerFactory stringLocalizerFactory,
            ITaxBreakdownService taxBreakdownService,
            ICompanyRunService companyRunService,
            ICountryTaxYearService countryTaxYearService,
            IEmploymentStatusService employmentStatusService,
            IPayslipServiceFactory payslipServiceFactory,
            IMessageBus messageBus)
        {
            this.context = context;
            this.companyService = companyService;
            this.companySettingService = companySettingService;
            this.employeeService = employeeService;
            this.taxBreakdownService = taxBreakdownService;
            this.companyRunService = companyRunService;
            this.countryTaxYearService = countryTaxYearService;
            this.employmentStatusService = employmentStatusService;
            this.payslipServiceFactory = payslipServiceFactory;
            this.messageBus = messageBus;

            this.localizer = stringLocalizerFactory.Create(SystemAreas.Payslip.Edit, null);
        }

        public async Task<byte[]?> ExportPayslipAsync(PayslipExportRequest payslipExportRequest, CancellationToken cancellationToken)
        {
            var useCustomPayslip = await this.companySettingService.IsActiveAsync(payslipExportRequest.CompanyId, "CUSTPAYSLIP");
            if (!useCustomPayslip)
            {
                var reportPath = await this.GetPayslipReportPathAsync(payslipExportRequest.EmployeeId);

                var parameters = new Dictionary<string, object?>
                {
                    { "RunID", payslipExportRequest.CompanyRunId.ToString() },
                    { "EmployeeID", payslipExportRequest.EmployeeId.ToString() },
                    { "PositionID", "0" },
                    { "LevelID", "0" },
                    { "GroupID", "0" },
                    { "FrequencyID", payslipExportRequest.FrequencyId.ToString() },
                    { "fkBaseCurrencyID", payslipExportRequest.HomeCurrency ? payslipExportRequest.CurrencyId.ToString() : null },
                    { "fkCompanyGroupID", payslipExportRequest.CompanyGroupId.ToString() },
                    { "AltLanguage", payslipExportRequest.AltLanguage.ToString() },
                    { "EmpNumber", null }
                };

                var report = await this.messageBus.RequestAsync<ExportSSRSReportMessage, ReportResponse>(
                    new ExportSSRSReportMessage
                    {
                        Background = false,
                        NotificationId = ExportReportMessage.GetHash(parameters),
                        UserId = payslipExportRequest.UserId,
                        CompanyId = payslipExportRequest.CompanyId,
                        FormatId = 1,
                        ReportHeaderId = 0,
                        ReportPath = reportPath,
                        Parameters = parameters.Select(_ => new ExportReportParameter
                        {
                            Id = 0,
                            Name = _.Key,
                            Value = (string)_.Value,
                        }).ToArray()
                    },
                    cancellationToken);
                return report.Document.HasValue ? await report.Document.Value : null;
            }
            else
            {
                var employeeNumber = await this.employeeService.GetEmployeeNumberAsync(payslipExportRequest.EmployeeId);

                var parameters = new Dictionary<string, object>()
                    {
                        { "RunId", payslipExportRequest.CompanyRunId.ToString() },
                        { "EmployeeNumber", employeeNumber },
                        { "AltLang", payslipExportRequest.AltLanguage },
                        { "ViewInHomeCurrency", payslipExportRequest.HomeCurrency },
                        { "CompanyId", payslipExportRequest.CompanyId.ToString() },
                        { "FrequencyId", payslipExportRequest.FrequencyId.ToString() },
                        { "NetPay", "True" }
                };

                var report = await this.messageBus.RequestAsync<ExportCustomReportMessage, ReportResponse>(
                    new ExportCustomReportMessage
                    {
                        Background = false,
                        AccessToken = payslipExportRequest.AccessToken,
                        NotificationId = ExportReportMessage.GetHash(parameters),
                        UserId = payslipExportRequest.UserId,
                        CompanyId = payslipExportRequest.CompanyId,
                        ReportUrl = $"PayslipHeader.repx?CompanyId={payslipExportRequest.CompanyId}&IsEmployeeActive={payslipExportRequest.IsMssOrEss}",
                        Parameters = parameters,
                        Format = "pdf",
                        Culture = payslipExportRequest.Culture
                    },
                    cancellationToken);
                return report.Document.HasValue ? await report.Document.Value : null;
            }
        }

        public async Task<byte[]> GetDummyPdfAsync(CancellationToken cancellationToken)
        {
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            using var fileStream = assembly.GetManifestResourceStream("PaySpace.Venuta.Modules.Payslips.Resources.dummy_payslip.pdf");
            var dummyPdf = new byte[fileStream.Length];
            await fileStream.ReadAsync(dummyPdf, 0, dummyPdf.Length, cancellationToken);
            return dummyPdf;
        }

        public async Task<(string Name, byte[] Bytes)> ExportTaxBreakdown(ExportTaxBreakdownMessage message, ISecurityProfile profile, CancellationToken cancellationToken)
        {
            var taxCalculationBreakdown = await this.taxBreakdownService.GetTaxCalculationBreakdownAsync(message.EmployeeId, message.RunId);
            var companyName = await this.companyService.GetCompanyTradingNameAsync(message.CompanyId);
            var employee = await this.employeeService.FindByIdAsync(message.EmployeeId);
            var runSummary = await this.companyRunService.GetRunSummaryAsync(message.RunId);
            var country = await this.companyService.GetTaxCountryIdAsync(message.CompanyId);

            // Updated to cater for runs that overlap tax years, the end date could be in a new tax year
            var taxYear = await this.countryTaxYearService.GetCountryTaxYearsById(country)
                .FirstOrDefaultAsync(_ =>
                    runSummary.PeriodEndDate >= _.YearStartDate &&
                    runSummary.PeriodEndDate <= _.YearEndDate,
                    cancellationToken);

            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(message.CompanyId);
            var yearOfAssessment = taxYear.YearEndDate.ToString("yyyy");
            var employmentStatusId = await this.employmentStatusService.GetEmploymentStatusIdForRunAsync(message.EmployeeId, message.RunId);

            var employmentStatus = await this.employmentStatusService.GetEmploymentStatusById(message.EmployeeId, employmentStatusId)
                .Select(_ => new { _.TaxStatus, _.TerminationDate, _.TerminationReason.TerminationDescription, }).FirstOrDefaultAsync(cancellationToken);

            var (periodFrom, periodTo) = await this.GetPeriodFromAndPeriodToDatesAsync(message.EmployeeId, message.RunId, taxYear.TaxYearId, taxCountryId, employmentStatusId);

            var payslipService = this.payslipServiceFactory.GetPayslipService(profile.UserType);
            var payslip = await payslipService.FindByIdAsync(profile, message.PayslipId!.Value, false, false, true);
            var payslipDetail = new Payslip
            {
                CompanyName = companyName,
                EmployeeNumber = employee.EmployeeNumber,
                FirstName = employee.FirstName,
                LastName = employee.LastName,
                Age = employee.Age,
                RunDescription = payslip.PayslipHeader.CompanyRun.RunDescription,
                TaxStatus = employmentStatus.TaxStatus.TaxStatusDescription,
                FrequencyName = employee.PayRates.Select(_ => _.PayFrequency.ToString()).FirstOrDefault(),
                PeriodStartDate = periodFrom,
                PeriodEndDate = employmentStatus.TerminationDate.HasValue && employmentStatus.TerminationDate <= periodTo ? employmentStatus.TerminationDate.Value : periodTo,
                PayDate = payslip.PayslipHeader.CompanyRun.PayDate,
                YearOfAssessment = yearOfAssessment,
                TerminationDate = employmentStatus.TerminationDate <= periodTo ? employmentStatus.TerminationDate : null,
                TerminationDescription = employmentStatus.TerminationDate <= periodTo ? employmentStatus.TerminationDescription : null
            };

            var taxBreakdown = new TaxBreakdown
            {
                UserId = message.UserId,
                Payslip = payslipDetail,
                AnnualisedRegularEarningsCalculated = taxCalculationBreakdown.AnnualisedRegularEarningsCalculated,
                BonusAnnualizedEarningsIncludingCalculated = taxCalculationBreakdown.BonusAnnualizedEarningsIncludingCalculated,
                BonusLessAnnualEarnings = taxCalculationBreakdown.BonusLessAnnualEarnings,
                BonusLessAnnualEarningsCalculated = taxCalculationBreakdown.BonusLessAnnualEarningsCalculated,
                BonusLessTaxBracketCalculated = taxCalculationBreakdown.BonusLessTaxBracketCalculated,
                BonusMarginalRate = taxCalculationBreakdown.BonusMarginalRate,
                BonusMarginalRateCalculated = taxCalculationBreakdown.BonusMarginalRateCalculated,
                BonusMarginalRateResult = taxCalculationBreakdown.BonusMarginalRateResult,
                BonusTaxLowerLimit = taxCalculationBreakdown.BonusTaxLowerLimit,
                BonusTaxOnLowerLimitCalculated = taxCalculationBreakdown.BonusTaxOnLowerLimitCalculated,
                TaxBonus = taxCalculationBreakdown.TaxBonus,
                BonusTaxRebateCalculated = taxCalculationBreakdown.BonusTaxRebateCalculated,
                BonusTaxUpperLimit = taxCalculationBreakdown.BonusTaxUpperLimit,
                CurrentTaxCalculated = taxCalculationBreakdown.CurrentTaxCalculated,
                EmployeeId = taxCalculationBreakdown.EmployeeId,
                EntityKey = message.RunId,
                LessTaxBracketCalculated = taxCalculationBreakdown.LessTaxBracketCalculated,
                MarginalRate = taxCalculationBreakdown.MarginalRate,
                MarginalRateCalculated = taxCalculationBreakdown.MarginalRateCalculated,
                MarginalRateResult = taxCalculationBreakdown.MarginalRateResult,
                PeriodInTaxYear2Calculated = taxCalculationBreakdown.PeriodInTaxYear2Calculated,
                PeriodInTaxYearCalculated = taxCalculationBreakdown.PeriodInTaxYearCalculated,
                PeriodsInYear = taxCalculationBreakdown.PeriodsInYear,
                PeriodsWorked = taxCalculationBreakdown.PeriodsWorked,
                PeriodWorked2Calculated = taxCalculationBreakdown.PeriodWorked2Calculated,
                PeriodWorkedCalculated = taxCalculationBreakdown.PeriodWorkedCalculated,
                PreviousRunTax = taxCalculationBreakdown.PreviousRunTax,
                TaxLowerLimit = taxCalculationBreakdown.TaxLowerLimit,
                TaxOnBonus = taxCalculationBreakdown.TaxOnBonus,
                TaxOnLowerLimitCalculated = taxCalculationBreakdown.TaxOnLowerLimitCalculated,
                TaxOnRegular = taxCalculationBreakdown.TaxOnRegular,
                TaxOnRegularCalculated = taxCalculationBreakdown.TaxOnRegularCalculated,
                TaxRebate = taxCalculationBreakdown.TaxRebate,
                TaxRebateCalculated = taxCalculationBreakdown.TaxRebateCalculated,
                TaxUpperLimit = taxCalculationBreakdown.TaxUpperLimit,
                TotalYTDTax = taxCalculationBreakdown.TotalYTDTax,
                TotalYTDTaxCalculated = taxCalculationBreakdown.TotalYTDTaxCalculated,
                YtdBonusEarnings = taxCalculationBreakdown.YtdBonusEarnings,
                YtdMedicalAidTaxCredits = taxCalculationBreakdown.YtdMedicalAidTaxCredits,
                YtdNormalEarnings = taxCalculationBreakdown.YtdNormalEarnings,
            };

            return ($"Tax Calc Breakdown {taxBreakdown.Payslip.RunDescription}.pdf", await this.ExportTaxBreakdown(taxBreakdown, cancellationToken));
        }

        private async Task<string> GetPayslipReportPathAsync(long employeeId)
        {
            async Task<string> GetReportPathAsync(int? reportId)
            {
                if (reportId.HasValue)
                {
                    var reportPath = await this.context.Set<EnumReportPath>()
                        .Where(_ => _.ReportId == reportId)
                        .Select(_ => _.ReportPath)
                        .SingleOrDefaultAsync();

                    if (!string.IsNullOrEmpty(reportPath))
                    {
                        return reportPath;
                    }
                }

                return "/PayrollReports/EmployeePayslip";
            }

            // try and get the reportId from either the employee or company
            var employee = await this.context.Set<Data.Models.Employees.Employee>()
                .Where(_ => _.EmployeeId == employeeId)
                .Select(_ => new { ReportId = _.ReportId ?? _.Company.ReportId, _.CompanyId })
                .SingleOrDefaultAsync();
            if (employee?.ReportId == null)
            {
                // if we dont get it from the above it from the payslip reports
                var taxCountryId = await this.companyService.GetTaxCountryIdAsync(employee!.CompanyId);
                var payslipReport = await this.GetPayslipReportAsync(taxCountryId);

                return await GetReportPathAsync(payslipReport?.ReportId);
            }

            return await GetReportPathAsync(employee.ReportId);
        }

        private Task<EnumReportPath?> GetPayslipReportAsync(int countryId)
        {
            return (from erp in this.context.Set<EnumReportPath>()
                    join bcrp in this.context.Set<BureauCountryReportPath>() on erp.ReportId equals bcrp.ReportPathId
                    where bcrp.CountryId == countryId
                    group erp by new { erp.ReportId, erp.ReportName, erp.ReportPath }
                into g
                    select new EnumReportPath { ReportId = g.Key.ReportId, ReportName = g.Key.ReportName, ReportPath = g.Key.ReportPath }).SingleOrDefaultAsync();
        }

        private async Task<(DateTime periodFrom, DateTime periodTo)> GetPeriodFromAndPeriodToDatesAsync(long employeeId, long companyRunId, long taxYearId, int taxCountryId, long employmentStatusId)
        {
            var companyRunPeriod = await this.companyRunService.GetRunPeriodAsync(companyRunId);
            var countryTaxYear = await this.countryTaxYearService.GetCountryTaxYearAsync(taxYearId, taxCountryId);
            var (employmentDate, terminationDate) = await this.employmentStatusService.GetEmploymentPeriodByStatusIdAsync(employeeId, employmentStatusId);

            var periodFrom = countryTaxYear.YearStartDate;
            if (employmentDate > countryTaxYear.YearStartDate)
            {
                periodFrom = employmentDate;
            }

            var periodTo = companyRunPeriod.PeriodEndDate;
            if (terminationDate.HasValue && terminationDate <= companyRunPeriod.PeriodEndDate)
            {
                periodTo = terminationDate.Value;
            }

            return (periodFrom, periodTo);
        }

        private async Task<byte[]> ExportTaxBreakdown(TaxBreakdown taxBreakdown, CancellationToken cancellationToken)
        {
            using var workbook = new Workbook();
            workbook.Unit = DevExpress.Office.DocumentUnit.Point;

            try
            {
                workbook.BeginUpdate();

                this.CreateStyles(workbook);

                var activeWorksheet = workbook.Worksheets[0];
                activeWorksheet.PrintOptions.FitToPage = true;
                activeWorksheet.Name = taxBreakdown.Payslip.RunDescription.SanitizeExcelSheetName();
                activeWorksheet.Columns.AutoFit(1, 5);

                this.AddDataToWorkSheet(taxBreakdown, activeWorksheet);
            }
            finally
            {
                workbook.EndUpdate();
            }

            using var ms = new MemoryStream();
            await workbook.ExportToPdfAsync(ms, cancellationToken);
            return ms.ToArray();
        }

        private void AddDataToWorkSheet(TaxBreakdown context, Worksheet worksheet)
        {
            var rowIndex = 1;

            rowIndex = this.AddHeader(worksheet, context, rowIndex);
            rowIndex++;

            rowIndex = this.RegularTax(worksheet, context, rowIndex);
            rowIndex = this.BonusTax(worksheet, context, rowIndex);

            this.TotalTax(worksheet, context, rowIndex);
        }

        private int AddHeader(Worksheet worksheet, TaxBreakdown context, int rowIndex)
        {
            worksheet.Cells[$"A{rowIndex}"].ColumnWidth = 120;
            worksheet.Cells[$"B{rowIndex}"].ColumnWidth = 250;
            worksheet.Cells[$"C{rowIndex}"].ColumnWidth = 150;
            worksheet.Cells[$"D{rowIndex}"].ColumnWidth = 150;

            worksheet.Range[$"A{rowIndex}:D{rowIndex}"].Merge();
            this.AddNormalHeaderText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderTitle") + ":");

            rowIndex++;
            rowIndex++;
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderCompanyName") + ":");
            this.AddDetailText(worksheet.Cells[$"B{rowIndex}"], context.Payslip.CompanyName);
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], this.localizer.GetString("exportHeaderFrequencyName") + ":");
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.Payslip.FrequencyName);

            rowIndex++;
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderEmployeeNumber") + ":");
            this.AddDetailText(worksheet.Cells[$"B{rowIndex}"], context.Payslip.EmployeeNumber);
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], this.localizer.GetString("exportHeaderRunDescription") + ":");
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.Payslip.RunDescription);

            rowIndex++;
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderFirstName") + ":");
            this.AddDetailText(worksheet.Cells[$"B{rowIndex}"], context.Payslip.FirstName);
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], this.localizer.GetString("exportHeaderYearOfAssessment") + ":");
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.Payslip.YearOfAssessment);

            rowIndex++;
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderLastName") + ":");
            this.AddDetailText(worksheet.Cells[$"B{rowIndex}"], context.Payslip.LastName);
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], this.localizer.GetString("exportHeaderPeriodStartDate") + ":");
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.Payslip.PeriodStartDate.ToString("yyyy/MM/dd"));

            rowIndex++;
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderAge") + ":");
            this.AddDetailText(worksheet.Cells[$"B{rowIndex}"], context.Payslip.Age.ToString());
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], this.localizer.GetString("exportHeaderPeriodEndDate") + ":");
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.Payslip.PeriodEndDate.ToString("yyyy/MM/dd"));

            rowIndex++;
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderTaxStatus") + ":");
            this.AddDetailText(worksheet.Cells[$"B{rowIndex}"], context.Payslip.TaxStatus);

            if (context.Payslip.TerminationDate.HasValue)
            {
                this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], this.localizer.GetString("exportHeaderTerminationDate") + ":");
                this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.Payslip.TerminationDate.Value.ToString("yyyy/MM/dd"));
            }

            rowIndex++;
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("exportHeaderRunon") + ":");
            this.AddDetailText(worksheet.Cells[$"B{rowIndex}"], DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));

            if (context.Payslip.TerminationDate.HasValue)
            {
                this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], this.localizer.GetString("exportHeaderTerminationDescription") + ":");
                this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.Payslip.TerminationDescription);
            }

            return rowIndex;
        }

        private int RegularTax(Worksheet worksheet, TaxBreakdown context, int rowIndex)
        {
            rowIndex++;

            worksheet.Range[$"A{rowIndex}:D{rowIndex}"].Merge();
            this.AddHeaderText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblRegularTax"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTotalYTDRegularEarnings"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.YtdNormalEarnings.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblDivide") + "/" + this.localizer.GetString("lblPeriodsWorked"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.PeriodsWorked.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.PeriodWorkedCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblMultiply") + "/" + this.localizer.GetString("lblPeriodsTaxYear"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.PeriodsInYear.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.PeriodInTaxYearCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddAlterDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblAnnualisedRegularEarnings"));
            this.AddAlterDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddAlterDetailText(worksheet.Cells[$"D{rowIndex}"], context.AnnualisedRegularEarningsCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblLess") + "/" + this.localizer.GetString("lblTaxBracketUpperLimit"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.TaxUpperLimit.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.LessTaxBracketCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblMultiply") + "/" + this.localizer.GetString("lblMarginalRate"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.MarginalRateResult.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.MarginalRateCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblPlus") + "/" + this.localizer.GetString("lblTaxOnLowerLimit"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.TaxLowerLimit.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.TaxOnLowerLimitCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblLess") + "/" + this.localizer.GetString("lblTaxRebate"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.TaxRebate.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.TaxRebateCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblDivide") + "/" + this.localizer.GetString("lblPeriodsTaxYear"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.PeriodsInYear.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.PeriodInTaxYear2Calculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblMultiply") + "/" + this.localizer.GetString("lblPeriodsWorked"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.PeriodsWorked.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.PeriodWorked2Calculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddAlterDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTaxOnRegularEarnings"));
            this.AddAlterDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddAlterDetailText(worksheet.Cells[$"D{rowIndex}"], context.TaxOnRegularCalculated.ToString("N2"));

            return rowIndex;
        }

        private int BonusTax(Worksheet worksheet, TaxBreakdown context, int rowIndex)
        {
            rowIndex++;
            worksheet.Range[$"A{rowIndex}:D{rowIndex}"].Merge();
            this.AddHeaderText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblBonusTax"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTotalBonusEarnings"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.YtdBonusEarnings.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblAdd") + " " + this.localizer.GetString("lblToAnnualisedEarnings"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.AnnualisedRegularEarningsCalculated.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusAnnualizedEarningsIncludingCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddAlterDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblNonAnnualisedBonus"));
            this.AddAlterDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddAlterDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusAnnualizedEarningsIncludingCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblLess") + " " + this.localizer.GetString("lblTaxBracketUpperLimit"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.BonusTaxUpperLimit.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusLessTaxBracketCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblMultiply") + " " + this.localizer.GetString("lblMarginalRate"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.BonusMarginalRateResult.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusMarginalRateCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblPlus") + " " + this.localizer.GetString("lblTaxOnLowerLimit"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.BonusTaxLowerLimit.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusTaxOnLowerLimitCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblLess") + " " + this.localizer.GetString("lblTaxRebate"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.TaxRebate.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusTaxRebateCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddAlterDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblNonAnnualisedRaxRegularEarnings"));
            this.AddAlterDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddAlterDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusTaxRebateCalculated.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblLess") + " " + this.localizer.GetString("lblAnnualisedTaxOnRegularEarnings"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], context.BonusLessAnnualEarningsCalculated.ToString("N2"));
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.BonusLessAnnualEarnings.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddAlterDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTaxOnBonus"));
            this.AddAlterDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddAlterDetailText(worksheet.Cells[$"D{rowIndex}"], context.TaxOnBonus.ToString("N2"));

            return rowIndex;
        }

        private void TotalTax(Worksheet worksheet, TaxBreakdown context, int rowIndex)
        {
            rowIndex++;
            worksheet.Range[$"A{rowIndex}:D{rowIndex}"].Merge();
            this.AddHeaderText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTotalTax"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTotalYTDDeannualised"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.TotalYTDTax.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblYtdMedicalAidTaxCredits"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.YtdMedicalAidTaxCredits.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTaxPaidUpBeforeThisRun"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.PreviousRunTax.ToString("N2"));

            rowIndex++;
            worksheet.Range[$"A{rowIndex}:B{rowIndex}"].Merge();
            this.AddDetailText(worksheet.Cells[$"A{rowIndex}"], this.localizer.GetString("lblTaxThisMonth"));
            this.AddDetailText(worksheet.Cells[$"C{rowIndex}"], string.Empty);
            this.AddDetailText(worksheet.Cells[$"D{rowIndex}"], context.CurrentTaxCalculated.ToString("N2"));
        }

        private void AddHeaderText(Cell cell, string text)
        {
            cell.SetValue(text);
            cell.Style = cell.Worksheet.Workbook.Styles["thead-dark"];
        }

        private void AddNormalHeaderText(Cell cell, string text)
        {
            cell.SetValue(text);
            cell.Style = cell.Worksheet.Workbook.Styles["thead-normal"];
        }

        private void AddDetailText(Cell cell, string text)
        {
            cell.SetValue(text);
            cell.Style = cell.Worksheet.Workbook.Styles["tbody-normal"];
        }

        private void AddAlterDetailText(Cell cell, string text)
        {
            cell.SetValue(text);
            cell.Style = cell.Worksheet.Workbook.Styles["table-success"];
        }

        private void CreateStyles(Workbook workbook)
        {
            var alterDetailTextStyle = workbook.Styles.Add("table-success");
            alterDetailTextStyle.BeginUpdate();
            alterDetailTextStyle.Font.Color = Color.Black;
            alterDetailTextStyle.Fill.BackgroundColor = ColorTranslator.FromHtml("#8fd19e");
            alterDetailTextStyle.Font.Size = 11;
            alterDetailTextStyle.Font.FontStyle = SpreadsheetFontStyle.Bold;
            alterDetailTextStyle.EndUpdate();

            var detailTextStyle = workbook.Styles.Add("tbody-normal");
            detailTextStyle.BeginUpdate();
            detailTextStyle.Font.Color = Color.Black;
            detailTextStyle.Fill.BackgroundColor = Color.White;
            detailTextStyle.Font.Size = 11;
            detailTextStyle.Font.FontStyle = SpreadsheetFontStyle.Regular;
            detailTextStyle.EndUpdate();

            var headerTextStyle = workbook.Styles.Add("thead-dark");
            headerTextStyle.BeginUpdate();
            headerTextStyle.Fill.BackgroundColor = ColorTranslator.FromHtml("#666");
            headerTextStyle.Font.Color = Color.White;
            headerTextStyle.Font.FontStyle = SpreadsheetFontStyle.Bold;
            headerTextStyle.Font.Size = 12;
            headerTextStyle.EndUpdate();

            var theadNormalStyle = workbook.Styles.Add("thead-normal");
            theadNormalStyle.BeginUpdate();
            theadNormalStyle.Font.Color = Color.Black;
            theadNormalStyle.Fill.BackgroundColor = Color.White;
            theadNormalStyle.Font.Size = 13;
            theadNormalStyle.Font.FontStyle = SpreadsheetFontStyle.Bold;
            theadNormalStyle.EndUpdate();
        }

        private sealed class TaxBreakdown
        {
            public object? EntityKey { get; set; }

            public long EmployeeId { get; set; }

            public decimal YtdNormalEarnings { get; set; }

            public decimal PeriodsWorked { get; set; }

            public int PeriodsInYear { get; set; }

            public decimal TaxUpperLimit { get; set; }

            public decimal MarginalRate { get; set; }

            public decimal TaxLowerLimit { get; set; }

            public decimal TaxRebate { get; set; }

            public decimal TaxOnRegular { get; set; }

            public decimal YtdBonusEarnings { get; set; }

            public decimal BonusTaxUpperLimit { get; set; }

            public decimal BonusMarginalRate { get; set; }

            public decimal BonusTaxLowerLimit { get; set; }

            public decimal TaxOnBonus { get; set; }

            public decimal PreviousRunTax { get; set; }

            public decimal YtdMedicalAidTaxCredits { get; set; }

            public decimal PeriodWorkedCalculated { get; set; }

            public decimal PeriodInTaxYearCalculated { get; set; }

            public decimal AnnualisedRegularEarningsCalculated { get; set; }

            public decimal LessTaxBracketCalculated { get; set; }

            public decimal MarginalRateCalculated { get; set; }

            public decimal TaxOnLowerLimitCalculated { get; set; }

            public decimal TaxRebateCalculated { get; set; }

            public decimal PeriodInTaxYear2Calculated { get; set; }

            public decimal PeriodWorked2Calculated { get; set; }

            public decimal TaxOnRegularCalculated { get; set; }

            public decimal BonusAnnualizedEarningsIncludingCalculated { get; set; }

            public decimal BonusLessTaxBracketCalculated { get; set; }

            public decimal BonusMarginalRateCalculated { get; set; }

            public decimal BonusTaxOnLowerLimitCalculated { get; set; }

            public decimal BonusTaxRebateCalculated { get; set; }

            public decimal BonusLessAnnualEarningsCalculated { get; set; }

            public decimal TaxBonus { get; set; }

            public decimal BonusLessAnnualEarnings { get; set; }

            public decimal TotalYTDTax { get; set; }

            public decimal TotalYTDTaxCalculated { get; set; }

            public decimal MarginalRateResult { get; set; }

            public decimal BonusMarginalRateResult { get; set; }

            public decimal CurrentTaxCalculated { get; set; }

            public long UserId { get; set; }

            public Payslip Payslip { get; set; }
        }

        private sealed class Payslip
        {
            public string CompanyName { get; set; }

            public string EmployeeNumber { get; set; }

            public string FirstName { get; set; }

            public string LastName { get; set; }

            public int Age { get; set; }

            public string FrequencyName { get; set; }

            public string RunDescription { get; set; }

            public DateTime PeriodStartDate { get; set; }

            public DateTime PeriodEndDate { get; set; }

            public DateTime? PayDate { get; set; }

            public string TaxStatus { get; set; }

            public string YearOfAssessment { get; set; }

            public DateTime? TerminationDate { get; set; }

            public string TerminationDescription { get; set; }
        }
    }
}