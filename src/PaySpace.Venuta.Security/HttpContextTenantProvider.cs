namespace PaySpace.Venuta.Security
{
    using System;
    using System.Security.Claims;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models;

    public class HttpContextTenantProvider : ClaimsTenantProvider, ITenantProvider
    {
        private readonly IHttpContextAccessor contextAccessor;

        public HttpContextTenantProvider(IHttpContextAccessor contextAccessor)
        {
            this.contextAccessor = contextAccessor;
        }

        protected HttpContext HttpContext => this.contextAccessor.HttpContext;

        protected ClaimsPrincipal User => this.HttpContext?.User;

        public virtual long GetUserId()
        {
            return this.GetUserId(this.User);
        }

        public UserType GetUserType()
        {
            return Enum.Parse<UserType>(this.HttpContext.User.GetUserType());
        }

        public virtual long GetAgencyId()
        {
            return this.GetAgencyId(this.User);
        }

        public virtual long? GetCompanyGroupId()
        {
            return this.GetCompanyGroupId(this.User);
        }

        public virtual long? GetCompanyId()
        {
            return this.GetCompanyId(this.User);
        }

        public virtual long? GetEmployeeId()
        {
            return this.GetEmployeeId(this.User);
        }

        public virtual long? GetFrequencyId()
        {
            return this.GetFrequencyId(this.User);
        }

        public virtual string? GetTaxCountryCode()
        {
            // It is possible for the User to be null, because on the CustomLocalizerFactory.cs
            // We check the Users Culture. And in certain cases the User has not been set resulting in null value.
            if (this.User == null)
            {
                return null;
            }

            return this.GetTaxCountryCode(this.User);
        }

        public virtual int GetDecimalPlaces()
        {
            return this.GetDecimalPlaces(this.User);
        }

        public virtual bool CanEditHistoricalRecords()
        {
            return this.CanEditHistoricalRecords(this.User);
        }
    }
}
