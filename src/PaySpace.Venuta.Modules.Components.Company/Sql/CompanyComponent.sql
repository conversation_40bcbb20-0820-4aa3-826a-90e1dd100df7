SELECT
    [c].[pkComponentCompanyID],
    [c].[fkCompanyFrequencyID] AS [CompanyFrequencyId],
    COALESCE(CAST([bc].[fkPayslipAction] AS nvarchar(max)), N'') + N'-' + COALESCE([bc].[ComponentDescription], N'') + N'-' + COALESCE(CAST([bc].[ComponentOrderNumber] AS nvarchar(max)), N'') AS [ComponentBureau],
    [c].[fkComponentBureauID] AS [ComponentBureauId],
    [bc].[ComponentDescription] AS [ComponentName],
    [f].[FormulaDefinition] AS [Formula],
    [c].[AliasDescription] AS [Description],
    [c].[inPackage],
    [c].[ComponentCode],
    [c].[MinValue],
    [c].[MaxValue],
    [c].[ActiveFromMonth],
    [c].[ActiveToMonth],
    CAST([c].[AutoRecoveryType] AS int) as [AutoRecoveryType],
    [c].[MaxRatioRecovery],
    [cm].[AliasDescription] AS [MultiplyByComponentCompany],
    [c].[RecoverFigures],
    [c].[AddToEmployee],
    [c].[ProRata],
    [c].[DoNotConvertCurrency],
    [e].[CurrencyCode] AS [Currency],
    [c].[DoNotShowOnPayslip],
    [c].[LanguageAliasDescription],
    [t].[TaxCode] AS [OverRidingTaxCode],
    [c].[OnceOffValuesActSameAsPackage],
    [c].[ExclComponentRetrospective],
    [c].[isCostToCompany],
    [c].[ShowOnMockPayslipOnly],
    [c].[HidePayslipComments],
    [c].[RetroDoNotCalc],
    [c].[CalcRegardlessOfBasicPosted],
    [c].[OverRideDecimalPlaces] AS [OverrideDecimalPlaces],
    [c].[PayslipMsg] AS [PayslipMessage],
    [cr].[fkRunID] AS [RunId],
    ~[cr].[Active] AS [Inactive],
    [c].[SelectedRunsCalc] AS [RunsToCompleteTheComponentIn],
    [c].[fkIndicatorId] AS [IndicatorId],
    [ci].[IndicatorDescription]

FROM [ComponentCompany] AS [c]
INNER JOIN [ComponentBureau] AS [bc] ON [c].[fkComponentBureauID] = [bc].[pkComponentBureauID]
LEFT JOIN [EnumCurrencies] AS [e] ON [c].[fkCurrencyID] = [e].[pkCurrencyID]
LEFT JOIN [TaxCode] AS [t] ON [c].[OverRidingTaxCode] = [t].[pkTaxCodeID]
LEFT JOIN [FormulaHeader] AS [f] ON [bc].[fkFormulaID] = [f].[pkFormulaID]
LEFT JOIN [ComponentCompany] AS [cm] ON [c].[MultiplyByComponentCompanyId] = [cm].[pkComponentCompanyID]
LEFT JOIN [ComponentIndicators] AS [ci] on [c].[fkIndicatorId] = [ci].[pkIndicatorId]

OUTER APPLY (
    SELECT TOP 1
        [cv1].[fkRunId]
       ,[cv1].[Active]
    FROM ComponentValues AS [cv1]
    JOIN CompanyRuns [r] ON [cv1].[fkRunID] = [r].[pkRunID]
    WHERE
        [r].[PeriodEndDate] <= @periodEndDate
      AND
        [r].[fkCompanyFrequency] = @frequencyId
      AND
        [c].[pkComponentCompanyID] = [cv1].[fkComponentCompanyID]
    ORDER BY [r].PeriodEndDate, [r].[OrderNumber] DESC
) AS [cr]

WHERE [c].[fkCompanyFrequencyID] = @frequencyId