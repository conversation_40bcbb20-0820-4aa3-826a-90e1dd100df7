namespace PaySpace.Venuta.Services.Components
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.Data.SqlClient;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Formula;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions.Models;
    using PaySpace.Venuta.Modules.Components.Models;
    using PaySpace.Venuta.Services.Abstractions;

    public class CompanyComponentService : GenericService<ComponentCompany>, ICompanyComponentService
    {
        private readonly ApplicationContext context;
        private readonly IDapperRepository dapper;
        private readonly IDistributedCache distributedCache;
        private readonly IScopedCache scopedCache;
        private readonly ReadOnlyContext readOnlyContext;

        public CompanyComponentService(
            IDbContextRepository<ComponentCompany> repository,
            ApplicationContext context,
            IDapperRepository dapper,
            IDistributedCache distributedCache,
            IScopedCache scopedCache,
            ReadOnlyContext readOnlyContext)
            : base(repository)
        {
            this.context = context;
            this.dapper = dapper;
            this.distributedCache = distributedCache;
            this.scopedCache = scopedCache;
            this.readOnlyContext = readOnlyContext;
        }

        public Task<CompanyComponentResult> GetComponentResultAsync(long companyComponentId, long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"ComponentCompanyResult:{frequencyId}:{companyComponentId}",
                async () =>
                {
                    var result = await this.dapper.GetAsync<CompanyComponentResult>(
                        "component_company_search",
                        new
                        {
                            SearchType = "CompanyComponentID",
                            SearchValue = companyComponentId.ToString(),
                            FrequencyID = frequencyId
                        });

                    return result.FirstOrDefault();
                });
        }

        public async Task<List<ActiveComponentCompanyResult>> GetSpecialComponentsAsync(int specialComponentId, long frequencyId, long runId)
        {
            return await this.scopedCache.GetOrCreateAsync(
                $"SpecialComponentsAsync:{specialComponentId}:{frequencyId}:{runId}",
                async () =>
                {
                    var allActiveComponentCompanies = await this.GetAllActiveCompanyComponentsInternalAsync(frequencyId, runId, true);
                    return allActiveComponentCompanies
                        .Where(_ => _.SpecialComponentId == specialComponentId)
                        .OrderBy(_ => _.AliasDescription)
                        .ToList();
                });
        }

        public List<ActiveComponentCompanyResult> GetSpecialComponents(int specialComponentId, long frequencyId, long runId)
        {
            return this.scopedCache.GetOrCreate(
                $"SpecialComponents:{specialComponentId}:{frequencyId}:{runId}",
                () => this.GetAllActiveCompanyComponentsInternal(frequencyId, runId, true)
                    .Where(_ => _.SpecialComponentId == specialComponentId)
                    .OrderBy(_ => _.AliasDescription)
                    .ToList());
        }

        public async Task<ActiveComponentCompanyResult> GetRecurringCompanyComponentAsync(long componentCompanyId, long frequencyId, long runId)
        {
            return await this.scopedCache.GetOrCreateAsync(
                $"RecurringCompanyComponent:{componentCompanyId}:{frequencyId}:{runId}",
                async () =>
                {
                    var allActiveComponentCompanies = await this.GetAllActiveCompanyComponentsInternalAsync(frequencyId, runId, true);

                    return allActiveComponentCompanies.FirstOrDefault(_ => _.ComponentCompanyId == componentCompanyId);
                });
        }

        public IQueryable<CompanyComponentTemplate> GetRecurringTemplates(long frequencyId)
        {
            return this.readOnlyContext.Set<CompanyComponentTemplate>()
                .TagWith("CompanyComponentService:GetRecurringTemplates")
                .AsNoTracking()
                .Where(_ => _.CompanyFrequencyId == frequencyId);
        }

        public async Task<IList<ComponentCompanyChildrenInformationResult>> GetChildComponents(long componentCompanyId)
        {
            return await this.scopedCache.GetOrCreateAsync(
                $"CompanyComponent:ChildComponents:{componentCompanyId}",
                () => this.Repository.Set
                    .Where(_ => _.ComponentCompanyParentId == componentCompanyId)
                    .Select(_ => new ComponentCompanyChildrenInformationResult
                    {
                        ComponentCompanyId = _.ComponentId,
                        InPackage = _.InPackage
                    })
                    .ToListAsync());
        }

        public Task<bool?> PartOfPackageAsync(long componentCompanyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"PartOfPackage:{componentCompanyId}",
                () => this.Repository.Set
                    .Where(_ => _.ComponentId == componentCompanyId)
                    .Select(_ => _.InPackage)
                    .SingleOrDefaultAsync());
        }

        public Task<bool> IsCompanyComponentActive(long companyComponentId, long companyRunId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"IsCompanyComponentActive:{companyComponentId}:{companyRunId}",
                async () =>
                {
                    var closestRunId = await (from componentValues in this.Repository.Context.Set<ComponentValues>()
                                              from companyRun in this.context.Set<CompanyRun>().Where(_ => _.RunId == companyRunId)
                                              where componentValues.CompanyRun.PeriodEndDate <= companyRun.PeriodEndDate
                                                    && componentValues.ComponentCompanyId == companyComponentId
                                                    && componentValues.CompanyRun.CompanyFrequencyId == companyRun.CompanyFrequencyId
                                              orderby componentValues.CompanyRun.PeriodEndDate descending
                                              select componentValues.CompanyRun.RunId).FirstOrDefaultAsync();
                    if (closestRunId == default)
                    {
                        closestRunId = companyRunId;
                    }

                    return !await this.Repository.Context.Set<ComponentValues>().AnyAsync(_ => _.ComponentCompanyId == companyComponentId && _.CompanyRunId == closestRunId && _.Active == false);
                });
        }

        public IQueryable<ComponentCompany> GetNetpayDeductionNoteComponentsByFrequency(long companyFrequencyId)
        {
            return this.readOnlyContext.Set<ComponentCompany>()
                .TagWith("CompanyComponentService:GetNetpayDeductionNoteComponentsByFrequency")
                .AsNoTracking()
                .Where(_ => _.CompanyFrequencyId == companyFrequencyId)
                .Where(_ => _.ComponentBureau.PayslipAction == (int)PayslipAction.Deduction
                            || _.ComponentBureau.PayslipAction == (int)PayslipAction.Note
                            || (_.ComponentBureau.PayslipAction == (int)PayslipAction.Totals && _.ComponentBureau.SpecialComponentType.SpecialComponentCode == SpecialComponentCodes.NetPay))
                .OrderBy(_ => _.ComponentBureau.PayslipAction);
        }

        public IQueryable<ComponentCompany> GetComponentsByFormulaLineCode(long companyFrequency, string formulaLineCode)
        {
            return this.Repository.Set.AsNoTracking()
                .Where(_ => _.CompanyFrequencyId == companyFrequency
                            && _.ComponentBureau.FormulaHeader.FormulaLinkingTable.Any(flt => flt.FormulaLines.Any(fl => fl.FormulaTableLine.TableLineFieldCode == formulaLineCode)));
        }

        public Task<List<long>> GetComponentIdsBySpecialComponentAsync(long specialComponentId, long companyFrequencyId)
        {
            return this.Repository.Set
                .Where(_ => _.ComponentBureau.SpecialComponentTypeId == specialComponentId && _.CompanyFrequencyId == companyFrequencyId)
                .OrderBy(_ => _.ComponentBureau.ComponentCategory.CategoryOrderNumber)
                .ThenBy(_ => _.ComponentBureau.ComponentOrderNumber)
                .Select(_ => _.ComponentId)
                .ToListAsync();
        }

        public Task<string> GetComponentFieldCodeAsync(long componentCompanyId)
        {
            return this.distributedCache.GetOrCreateAsync(
                CacheKeys.ComponentFieldCode(componentCompanyId),
                () => (from cc in this.Repository.Context.Set<ComponentCompany>().AsNoTracking()
                       join cb in this.Repository.Context.Set<ComponentBureau>().AsNoTracking() on cc.ComponentBureauId equals cb.ComponentBureauId
                       join fh in this.Repository.Context.Set<FormulaHeader>().AsNoTracking() on cb.FormulaHeaderId equals fh.FormulaHeaderId
                       join flt in this.Repository.Context.Set<FormulaLinkingTable>().AsNoTracking() on fh.FormulaHeaderId equals flt.FormulaHeaderId
                       join fl in this.Repository.Context.Set<FormulaLine>().AsNoTracking() on flt.FormulaLinkingId equals fl.FormulaLinkingId
                       join ftl in this.Repository.Context.Set<FormulaTableLine>().AsNoTracking() on fl.TableLineId equals ftl.TableLineId
                       where cc.ComponentId == componentCompanyId
                       select ftl.TableLineFieldCode).FirstOrDefaultAsync());
        }

        public Task<FormulaTables> GetComponentFormulaTableAsync(long componentCompanyId)
        {
            return (from cc in this.Repository.Context.Set<ComponentCompany>().AsNoTracking()
                    join cb in this.Repository.Context.Set<ComponentBureau>().AsNoTracking() on cc.ComponentBureauId equals cb.ComponentBureauId
                    join fh in this.Repository.Context.Set<FormulaHeader>().AsNoTracking() on cb.FormulaHeaderId equals fh.FormulaHeaderId
                    join flt in this.Repository.Context.Set<FormulaLinkingTable>().AsNoTracking() on fh.FormulaHeaderId equals flt.FormulaHeaderId
                    join fl in this.Repository.Context.Set<FormulaLine>().AsNoTracking() on flt.FormulaLinkingId equals fl.FormulaLinkingId
                    join ftl in this.Repository.Context.Set<FormulaTableLine>().AsNoTracking() on fl.TableLineId equals ftl.TableLineId
                    where cc.ComponentId == componentCompanyId
                    select ftl.FormulaTableId).FirstOrDefaultAsync();
        }

        public IQueryable<ComponentCompany> GetByComponentsFieldCode(long frequencyId, string fieldCode)
        {
            return from cc in this.Repository.Context.Set<ComponentCompany>().AsNoTracking()
                   join cb in this.Repository.Context.Set<ComponentBureau>().AsNoTracking() on cc.ComponentBureauId equals cb.ComponentBureauId
                   join fh in this.Repository.Context.Set<FormulaHeader>().AsNoTracking() on cb.FormulaHeaderId equals fh.FormulaHeaderId
                   join flt in this.Repository.Context.Set<FormulaLinkingTable>().AsNoTracking() on fh.FormulaHeaderId equals flt.FormulaHeaderId
                   join fl in this.Repository.Context.Set<FormulaLine>().AsNoTracking() on flt.FormulaLinkingId equals fl.FormulaLinkingId
                   join ftl in this.Repository.Context.Set<FormulaTableLine>().AsNoTracking() on fl.TableLineId equals ftl.TableLineId
                   where cc.CompanyFrequencyId == frequencyId && ftl.TableLineFieldCode == fieldCode
                   select cc;
        }

        public Task<bool> HasSpecialComponent(long companyFrequencyId, string specialComponentCode)
        {
            return this.Repository.Set.AnyAsync(_ => _.ComponentBureau.SpecialComponentType.SpecialComponentCode == specialComponentCode && _.CompanyFrequencyId == companyFrequencyId);
        }

        public Task<string> GetComponentAliasDescriptionAsync(long companyComponentId)
        {
            return this.Repository.Set
                .Where(_ => _.ComponentId == companyComponentId)
                .Select(_ => _.AliasDescription)
                .FirstOrDefaultAsync();
        }

        public Task<long> GetCompanyIdAsync(long componentCompanyId)
        {
            return this.Repository.Set.Where(_ => _.ComponentId == componentCompanyId)
                .Select(_ => _.CompanyFrequency.Company.CompanyId)
                .SingleOrDefaultAsync();
        }

        public async Task<bool> HasUniqueComponentCodeOrDescriptionAsync(long frequencyId, long runId, long? companyComponentId)
        {
            return await this.scopedCache.GetOrCreateAsync(
                $"HasUniqueComponentCodeOrDescriptionAsync:{frequencyId}:{runId}:{companyComponentId}",
                async () =>
                {
                    var allComponents = await this.GetAllActiveCompanyComponentsAsync(frequencyId, runId);

                    var description = allComponents.First(_ => _.ComponentCompanyId == companyComponentId).AliasDescription.Trim();

                    var componentCompanies = allComponents.Where(_ => _.AliasDescription.Trim().Equals(description, StringComparison.OrdinalIgnoreCase)).ToList();

                    var hasCompanyComponentCode = componentCompanies.Any(_ => !string.IsNullOrEmpty(_.ComponentCode));

                    return hasCompanyComponentCode || (componentCompanies.Count == 1);
                });
        }

        public ComponentCompanyInfoResult GetComponentCompanyIdByDescriptionOrCode(long frequencyId, long runId, string componentCodeDescription)
        {
            return this.scopedCache.GetOrCreate(
                $"CompanyComponent:ComponentIdByDescription:{frequencyId}:{runId}:{componentCodeDescription}",
                () =>
                {
                    var components = this.GetActiveComponentCompanies(frequencyId, runId, componentCodeDescription, null, false);
                    // only selecting 1st one, validation must validate for duplicates
                    return components.FirstOrDefault();
                });
        }

        public Task<ComponentCompanyInfoResult> GetComponentCompanyIdByDescriptionAsync(long frequencyId, long runId, string componentCodeDescription)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyComponent:ComponentIdByDescription:{frequencyId}:{runId}:{componentCodeDescription}",
                async () =>
                {
                    var components = await this.GetActiveComponentCompaniesAsync(frequencyId, runId, componentCodeDescription, null, false);

                    // only selecting 1st one, validation must validate for duplicates
                    return components.FirstOrDefault();
                });
        }

        public async Task<long?> GetComponentCompanyIdByEmployeeComponentAsync(long? employeeComponentId)
        {
            // -1 = ComponentPercentageCosting
            if (employeeComponentId is null or (-1))
            {
                return null;
            }

            return await this.scopedCache.GetOrCreateAsync(
                $"CompanyComponent:ComponentIdByEmployeeComponent:{employeeComponentId}",
                () =>
                {
                    return this.Repository.Context.Set<ComponentEmployee>()
                        .Where(_ => _.ComponentId == employeeComponentId)
                        .Select(_ => _.ComponentCompanyId)
                        .FirstOrDefaultAsync();
                });
        }

        public PayslipAction GetPayslipAction(long companyComponentId)
        {
            return this.Repository.Set
                .Where(_ => _.ComponentId == companyComponentId)
                .Select(_ => (PayslipAction)_.ComponentBureau.PayslipAction)
                .SingleOrDefault();
        }

        public Task<PayslipAction> GetPayslipActionAsync(long companyComponentId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyComponent:PayslipAction:{companyComponentId}",
                () => this.Repository.Set
                    .Where(_ => _.ComponentId == companyComponentId)
                    .Select(_ => (PayslipAction)_.ComponentBureau.PayslipAction)
                    .SingleOrDefaultAsync());
        }

        public Task<bool> IsComponentReadonlyAsync(long componentCompanyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyComponent:{componentCompanyId}:ReadOnly",
                () => this.Repository.Set.AnyAsync(_ => _.ComponentId == componentCompanyId && _.ComponentBureau.DisplayReadOnlyEmployee == true));
        }

        public async Task<bool> IsComponentActiveForSpecifiedRunAsync(long componentCompanyId, long frequencyId, long runId)
        {
            return await this.scopedCache.GetOrCreateAsync(
                $"IsValidComponentForSpecifiedRun:{componentCompanyId}:{runId}",
                async () =>
                {
                    var activeComponents = await this.GetActiveComponentCompaniesAsync(frequencyId, runId, null, componentCompanyId, true);

                    return activeComponents.Any();
                });
        }

        public Task<bool> IsBasicSalarySpecialComponentAsync(long companyComponentId)
        {
            if (companyComponentId <= 0)
            {
                return Task.FromResult(false);
            }

            return this.distributedCache.GetOrCreateAsync(
                CacheKeys.IsBasSalSpecialComponent(companyComponentId),
                () =>
                {
                    return this.readOnlyContext.Set<ComponentCompany>()
                        .Where(_ => _.ComponentId == companyComponentId)
                        .Select(_ => _.ComponentBureau.SpecialComponentType.SpecialComponentCode == SpecialComponentCodes.BasicSalary)
                        .FirstOrDefaultAsync();
                });
        }

        public bool IsBasicSalarySpecialComponent(long companyComponentId)
        {
            if (companyComponentId <= 0)
            {
                return false;
            }

            return this.distributedCache.GetOrCreate(
                CacheKeys.IsBasSalSpecialComponent(companyComponentId),
                () =>
                {
                    return this.readOnlyContext.Set<ComponentCompany>()
                        .Where(_ => _.ComponentId == companyComponentId)
                        .Select(_ => _.ComponentBureau.SpecialComponentType.SpecialComponentCode == SpecialComponentCodes.BasicSalary)
                        .FirstOrDefault();
                });
        }

        // TODO: rework this when cacheing change is in master
        public bool IsLumpSumSpecialComponent(long companyComponentId)
        {
            return this.distributedCache.GetOrCreate(
                    $"Company:IsLumpSumSpecialComponent:{companyComponentId}",
                    () =>
                    {
                        return this.Repository.Set
                            .Where(_ => _.ComponentId == companyComponentId)
                            .Select(_ => _.ComponentBureau.SpecialComponentType.SpecialComponentCode == SpecialComponentCodes.LumpSum)
                            .FirstOrDefault();
                    });
        }

        public Task<bool> IsLeaveEncashmentComponentAsync(long? companyComponentId)
        {
            // will be null for ComponentPercentageCosting
            if (companyComponentId == null)
            {
                return Task.FromResult(false);
            }

            return this.distributedCache.GetOrCreateAsync(
                CacheKeys.IsLeaveEncashmentComponent(companyComponentId.Value),
                () =>
                {
                    return this.Repository.Set
                        .Where(_ => _.ComponentId == companyComponentId)
                        .Select(_ => _.ComponentBureau.SpecialComponentType.SpecialComponentCode == SpecialComponentCodes.LeaveEncashment)
                        .FirstOrDefaultAsync();
                });
        }

        public List<ActiveComponentCompanyResult> GetAllActiveCompanyComponents(long frequencyId, long runId)
        {
            return this.scopedCache.GetOrCreate(
                CacheKeys.AllActiveCompanyComponents(frequencyId, runId, false),
                () => this.GetAllActiveCompanyComponentsInternal(frequencyId, runId, false));
        }

        public Task<List<ActiveComponentCompanyResult>> GetAllActiveCompanyComponentsAsync(long frequencyId, long runId, bool skipRecurringDisplayCheck = false)
        {
            return this.scopedCache.GetOrCreateAsync(
                CacheKeys.AllActiveCompanyComponents(frequencyId, runId, skipRecurringDisplayCheck),
                () => this.GetAllActiveCompanyComponentsInternalAsync(frequencyId, runId, skipRecurringDisplayCheck));
        }

        public Task<List<CompanyComponentPayslipActionResult>> GetComponentsByPayslipActionAsync(
            long countryId,
            long companyFrequencyId,
            PayslipAction payslipAction,
            CompanyRunPeriodResult companyRunPeriod,
            bool includeInactive)
        {
            var parameters = new[]
            {
                new SqlParameter("@payslipActionId", payslipAction),
                new SqlParameter("@frequencyId", companyFrequencyId),
                new SqlParameter("@runStartDate", companyRunPeriod.PeriodStartDate),
                new SqlParameter("@runEndDate", companyRunPeriod.PeriodEndDate),
                new SqlParameter("@countryID", countryId),
                new SqlParameter("@includeInactive", includeInactive),
                new SqlParameter("@runId", companyRunPeriod.RunId)
            };

            return this.context.Set<CompanyComponentPayslipActionResult>()
                .FromSqlRaw("exec [component_company_payslip_action_search] @payslipActionId, @frequencyId, @runStartDate, @runEndDate, @countryID, @includeInactive, @runId", parameters)
                .ToListAsync();
        }

        public Task<(int? ComponentOrderNumber, int? CategoryOrderNumber, string specialComponentCode)> GetComponentCategoryAsync(long companyFrequencyId, long componentId)
        {
            return this.context.Set<ComponentCompany>()
                .Where(_ => _.CompanyFrequencyId == companyFrequencyId && _.ComponentId == componentId)
                .Select(_ => ValueTuple.Create(
                    _.ComponentBureau.ComponentOrderNumber,
                    _.ComponentBureau.ComponentCategory.CompanyComponentCategoryOverrides
                        .Where(x => x.CompanyFrequencyId == companyFrequencyId)
                        .Select(x => x.OrderNumber)
                        .FirstOrDefault()
                    ?? _.ComponentBureau.ComponentCategory.CategoryOrderNumber,
                    _.ComponentBureau.SpecialComponentType.SpecialComponentCode))
                .FirstOrDefaultAsync();
        }

        public Task<long?> GetParentComponentIdAsync(long companyComponentId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyComponent:ParentComponent:{companyComponentId}",
                () => this.Repository.Set
                    .Where(_ => _.ComponentId == companyComponentId)
                    .Select(_ => _.ComponentCompanyParentId)
                    .SingleOrDefaultAsync());
        }

        public Task<long> GetComponentIdBySpecialComponentCodeAsync(long companyFrequencyId, string specialComponentCode)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"Frequency:{companyFrequencyId}:NetPayCompanyComponent",
                () =>
                {
                    return this.Repository.Set
                        .Where(_ => _.ComponentBureau.SpecialComponentType.SpecialComponentCode == specialComponentCode && _.CompanyFrequencyId == companyFrequencyId)
                        .Select(_ => _.ComponentId)
                        .FirstOrDefaultAsync();
                });
        }

        public async Task<bool> HasUniqueComponentCodeAsync(long frequencyId, long companyComponentId, string componentCode)
        {
            return !await this.Repository.Set.AnyAsync(_ => _.CompanyFrequencyId == frequencyId && _.ComponentId != companyComponentId && _.ComponentCode.Trim() == componentCode);
        }

        public async Task<bool> HasUniqueDescriptionAsync(long frequencyId, long companyComponentId, string description)
        {
            return !await this.Repository.Set.AnyAsync(_ => _.CompanyFrequencyId == frequencyId && _.ComponentId != companyComponentId && _.AliasDescription.Trim() == description);
        }

        public Task<List<ComponentCompanyInfoResult>> GetCompanyGroupComponentsListAsync(long companyGroupId)
        {
            return this.readOnlyContext.Set<ComponentCompanyInfoResult>()
                .FromSqlRaw(
                    "nextgen_all_componentcompanygroup @CompanyGroupID",
                    new SqlParameter("@CompanyGroupID", companyGroupId))
                .TagWith("CompanyComponentService")
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<bool> DisallowDeleteEmployeeRecurringAsync(long employeeComponentId)
        {
            return await this.distributedCache.GetOrCreateAsync(
                CacheKeys.DisallowDeleteEmployeeRecurring(employeeComponentId),
                () => this.Repository.Context.Set<ComponentEmployee>()
                    .AnyAsync(_ => _.ComponentId == employeeComponentId
                                   && _.ComponentCompany.ComponentBureau.DisallowDeleteEmployeeRecurring == true));
        }

        private IEnumerable<ComponentCompanyInfoResult> GetActiveComponentCompanies(long frequencyId, long runId, string codeOrDescription, long? componentCompanyId, bool byDescriptionOnly)
        {
            if (string.IsNullOrEmpty(codeOrDescription) && componentCompanyId == null)
            {
                return Array.Empty<ComponentCompanyInfoResult>();
            }

            codeOrDescription = codeOrDescription?.Trim().ToLower();
            var descriptionCodeOrId = codeOrDescription ?? Convert.ToString(componentCompanyId);

            return this.distributedCache.GetOrCreate(
                CacheKeys.ActiveCompanyComponent(frequencyId, runId, byDescriptionOnly, descriptionCodeOrId),
                () =>
                {
                    if (componentCompanyId != null)
                    {
                        return this.GetAllActiveCompanyComponents(frequencyId, runId)
                            .Where(_ => _.ComponentCompanyId == componentCompanyId)
                            .Select(_ => new ComponentCompanyInfoResult
                            {
                                AliasDescription = _.AliasDescription,
                                ComponentCode = _.ComponentCode,
                                ComponentCompanyId = _.ComponentCompanyId,
                                SpecialComponentCode = _.SpecialComponentCode
                            })
                            .ToList();
                    }

                    return this.GetAllActiveCompanyComponents(frequencyId, runId)
                        .Where(_ => _.AliasDescription.ToLower().Trim() == codeOrDescription || (!byDescriptionOnly && _.ComponentCode?.ToLower().Trim() == codeOrDescription))
                        .Select(_ => new ComponentCompanyInfoResult
                        {
                            AliasDescription = _.AliasDescription,
                            ComponentCode = _.ComponentCode,
                            ComponentCompanyId = _.ComponentCompanyId,
                            SpecialComponentCode = _.SpecialComponentCode
                        })
                        .ToList();
                });
        }

        private async Task<IEnumerable<ComponentCompanyInfoResult>> GetActiveComponentCompaniesAsync(long frequencyId, long runId, string codeOrDescription, long? componentCompanyId, bool byDescriptionOnly)
        {
            if (string.IsNullOrEmpty(codeOrDescription) && componentCompanyId == null)
            {
                return Array.Empty<ComponentCompanyInfoResult>();
            }

            codeOrDescription = codeOrDescription?.Trim().ToLower();
            var descriptionCodeOrId = codeOrDescription ?? Convert.ToString(componentCompanyId);

            return await this.distributedCache.GetOrCreateAsync(
                CacheKeys.ActiveCompanyComponent(frequencyId, runId, byDescriptionOnly, descriptionCodeOrId),
                async () =>
                {
                    var activeComponents = await this.GetAllActiveCompanyComponentsAsync(frequencyId, runId);

                    if (componentCompanyId != null)
                    {
                        return activeComponents.Where(_ => _.ComponentCompanyId == componentCompanyId)
                            .Select(_ => new ComponentCompanyInfoResult
                            {
                                AliasDescription = _.AliasDescription,
                                ComponentCode = _.ComponentCode,
                                ComponentCompanyId = _.ComponentCompanyId
                            })
                            .ToList();
                    }

                    return activeComponents.Where(_ => _.AliasDescription.ToLower().Trim() == codeOrDescription || (!byDescriptionOnly && _.ComponentCode?.ToLower().Trim() == codeOrDescription))
                        .Select(_ => new ComponentCompanyInfoResult
                        {
                            AliasDescription = _.AliasDescription,
                            ComponentCode = _.ComponentCode,
                            ComponentCompanyId = _.ComponentCompanyId
                        })
                        .ToList();
                });
        }

        private List<ActiveComponentCompanyResult> GetAllActiveCompanyComponentsInternal(long frequencyId, long runId, bool skipRecurringDisplayCheck)
        {
            return this.distributedCache.GetOrCreate(
                CacheKeys.AllActiveCompanyComponents(frequencyId, runId, skipRecurringDisplayCheck),
                () => this.GetAllActiveOnceOffCompanyComponentsQuery(frequencyId, runId, skipRecurringDisplayCheck).ToList());
        }

        private Task<List<ActiveComponentCompanyResult>> GetAllActiveCompanyComponentsInternalAsync(long frequencyId, long runId, bool skipRecurringDisplayCheck)
        {
            return this.distributedCache.GetOrCreateAsync(
                CacheKeys.AllActiveCompanyComponents(frequencyId, runId, skipRecurringDisplayCheck),
                () => this.GetAllActiveOnceOffCompanyComponentsQuery(frequencyId, runId, skipRecurringDisplayCheck).ToListAsync());
        }

        private IQueryable<ActiveComponentCompanyResult> GetAllActiveOnceOffCompanyComponentsQuery(long frequencyId, long runId, bool skipRecurringDisplayCheck)
        {
            return this.readOnlyContext.Set<ActiveComponentCompanyResult>()
                .FromSqlRaw(
                    "nextgen_all_active_componentcompanies_get @frequencyId, @runId, @skipRecurringDisplayCheck",
                    new SqlParameter("@frequencyId", frequencyId),
                    new SqlParameter("@runId", runId),
                    new SqlParameter("@skipRecurringDisplayCheck", skipRecurringDisplayCheck))
                .TagWith("CompanyComponentService")
                .AsNoTracking();
        }
    }
}