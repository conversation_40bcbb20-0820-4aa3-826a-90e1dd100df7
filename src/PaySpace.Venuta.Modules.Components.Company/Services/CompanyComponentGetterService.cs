namespace PaySpace.Venuta.Modules.Components.Company.Services
{
    using System.Linq;

    using Microsoft.Data.SqlClient;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions.Results;
    using PaySpace.Venuta.Services.Company;

    public class CompanyComponentGetterService : ICompanyComponentGetterService
    {
        private readonly ApplicationContext context;
        private readonly ICompanyRunService companyRunService;

        public CompanyComponentGetterService(ApplicationContext context, ICompanyRunService companyRunService)
        {
            this.context = context;
            this.companyRunService = companyRunService;
        }

        public IQueryable<CompanyComponentGetterResult> GetCompanyComponentResults(long companyFrequencyId, long companyRunId)
        {
            var periodEndDate = this.companyRunService.GetPeriodEndDate(companyRunId);
            var query = ResourceHelper.GetEmbeddedContent("Sql.CompanyComponent.sql");
            var parameters = new[]
            {
                new SqlParameter("@frequencyId", companyFrequencyId),
                new SqlParameter("@periodEndDate", periodEndDate)
            };

            return this.context.Set<CompanyComponentGetterResult>()
                .FromSqlRaw(query, parameters);
        }
    }
}