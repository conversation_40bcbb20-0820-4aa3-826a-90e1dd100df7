namespace PaySpace.Venuta.Services.External
{
    using System;
    using System.Globalization;
    using System.Linq;
    using System.Net.Http;
    using System.Net.Http.Json;
    using System.Threading.Tasks;

    using Microsoft.Extensions.Options;

    public interface IWordConversionService
    {
        Task<string> TranslateAsync(string word, CultureInfo culture);
    }

    public class WordConversionService : IWordConversionService
    {
        private readonly IHttpClientFactory httpClientFactory;
        private readonly CognativeServicesSettings cognativeServicesSettings;

        public WordConversionService(IHttpClientFactory httpClientFactory, IOptions<CognativeServicesSettings> cognativeServicesSettings)
        {
            this.httpClientFactory = httpClientFactory;
            this.cognativeServicesSettings = cognativeServicesSettings.Value;
        }

        public async Task<string> TranslateAsync(string word, CultureInfo culture)
        {
            if (!string.IsNullOrEmpty(word) && culture.TwoLetterISOLanguageName != "en")
            {
                using (var client = this.httpClientFactory.CreateClient())
                {
                    var body = new object[] { new { Text = word } };
                    var requestUri = new Uri(this.cognativeServicesSettings.Endpoint + $"/translate?api-version=3.0&from=en&to={culture.TwoLetterISOLanguageName}");
                    client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Key", this.cognativeServicesSettings.AuthKey);
                    client.DefaultRequestHeaders.Add("Ocp-Apim-Subscription-Region", this.cognativeServicesSettings.Location);

                    var response = await client.PostAsJsonAsync(requestUri, body);
                    if (response.IsSuccessStatusCode)
                    {
                        var translation = await response.Content.ReadFromJsonAsync<WordConversionResult[]>();
                        return translation.First().Translations.FirstOrDefault()?.Text;
                    }
                }
            }

            return word;
        }

        private sealed record WordConversionResult
        {
            public Translations[] Translations { get; set; }
        }

        private sealed record Translations
        {
            public string Text { get; set; }

            public string To { get; set; }
        }
    }
}