namespace PaySpace.Venuta.Services.External
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Abstractions;

    public interface IEmailService
    {
        IEmailClient Client { get; }

        Task SendNewEngagementAsync(string email, string employeeNumber, string fullName);

        Task SendRegisterAsync(string url, string email);

        Task SendForgotPasswordAsync(string url, long userId);

        Task SendBulkUploadStatusAsync(long userId, string uploadStatus, EmailDetail emailDetails);

        Task SendAppraisalApprovalAsync(long userId, string fullName, string comments, bool accept, string commentLabel);

        Task SendAppraisalApprovalAsync(string email, string fullName, string comments, bool accept, string commentLabel);

        Task SendAppraisalFinishedAsync(long userId, string fullName);

        Task SendAppraisalApprovalRequiredAsync(long userId, string fullName);

        Task SendReviewAsync(long userId, string template, string owner, string comment, string commentLabel);

        Task SendApprovalEmailAsync(long userId, IDictionary<string, string> emailParameters, EmailDetail emailDetails);

        Task SendBankDetailsChangedAsync(long employeeId, EmailDetail detail);

        Task SendCloudRoomEmailAsync(long userId, string emailTemplate, EmailDetail detail, string label, string description, string url);

        Task SendDeleteEmployeeEmailAsync(string email, string employeeFullName, string employeeNumber, string companyName, string organisationUnit);

        Task SendESSAutoRegisterEmailAsync(Uri url, string email);

        Task SendWebhookErrorsEmailAsync(long agencyId, string email, EmailDetail emailDetail, DateTime date);

        Task SendUserRegisterEmailAsync(Uri url, string email);

        Task SendJobManagementEmailAsync(string template, string email, EmailDetail detail, EmailParameters parameters);
    }

    public class EmailService : IEmailService
    {
        public EmailService(IEmailClient client)
        {
            this.Client = client;
        }

        public IEmailClient Client { get; }

        public Task SendNewEngagementAsync(string email, string employeeNumber, string fullName)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("EmpNumber", employeeNumber);
            parameters.TryAdd("EmpFullName", fullName);

            return this.Client.EmailAsync(
                EmailTemplates.NewEngagement,
                email,
                CultureInfo.CurrentUICulture,
                parameters,
                null);
        }

        public Task SendRegisterAsync(string url, string email)
        {
            return this.Client.EmailAsync(
                EmailTemplates.Register,
                email,
                CultureInfo.CurrentUICulture,
                new EmailParameters { Url = new Uri(url) },
                null);
        }

        public Task SendForgotPasswordAsync(string url, long userId)
        {
            return this.Client.EmailAsync(
                EmailTemplates.Forgot,
                userId,
                new EmailParameters { Url = new Uri(url) },
                null);
        }

        public Task SendBulkUploadStatusAsync(long userId, string uploadStatus, EmailDetail emailDetails)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("Status", uploadStatus);

            return this.Client.EmailAsync(EmailTemplates.BulkUploadStatus, userId, parameters, emailDetails);
        }

        public Task SendAppraisalApprovalAsync(long userId, string fullName, string comments, bool accept, string commentLabel)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("EmpFullName", fullName);

            var detail = new EmailDetail
            {
                 new EmailDetailValue
                 {
                     Name = $"{commentLabel}:",
                     Value = comments
                 }
            };

            return this.Client.EmailAsync(
                accept ? EmailTemplates.AppraisalAccepted : EmailTemplates.AppraisalRejected,
                userId,
                parameters,
                detail);
        }

        public Task SendAppraisalApprovalAsync(string email, string fullName, string comments, bool accept, string commentLabel)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("EmpFullName", fullName);

            var detail = new EmailDetail
            {
                new EmailDetailValue
                {
                    Name = $"{commentLabel}:",
                    Value = comments
                }
            };

            return this.Client.EmailAsync(
                accept ? EmailTemplates.AppraisalAccepted : EmailTemplates.AppraisalRejected,
                email,
                CultureInfo.CurrentUICulture,
                parameters,
                detail);
        }

        public Task SendAppraisalFinishedAsync(long userId, string fullName)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("EmpFullName", fullName);

            return this.Client.EmailAsync(EmailTemplates.AppraisalFinished, userId, parameters, null);
        }

        public Task SendAppraisalApprovalRequiredAsync(long userId, string fullName)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("EmpFullName", fullName);

            return this.Client.EmailAsync(EmailTemplates.AppraisalApprovalRequired, userId, parameters, null);
        }

        public Task SendReviewAsync(long userId, string template, string owner, string comment, string commentLabel)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("Owner", owner);

            var detail = new EmailDetail();

            if (!string.IsNullOrWhiteSpace(comment))
            {
                detail.Add(new EmailDetailValue { Name = commentLabel, Value = comment });
            }

            return this.Client.EmailAsync(template, userId, parameters, detail);
        }

        public Task SendApprovalEmailAsync(long userId, IDictionary<string, string> emailParameters, EmailDetail emailDetails)
        {
            var parameters = new EmailParameters();
            parameters.TryAdd("Sender", emailParameters["Sender"]);
            parameters.TryAdd("ProcessType", emailParameters["ProcessType"]);
            parameters.TryAdd("Process", emailParameters["Process"]);

            return this.Client.EmailAsync(EmailTemplates.KpaReviewSubmission, userId, parameters, emailDetails);
        }

        public Task SendBankDetailsChangedAsync(long employeeId, EmailDetail detail)
        {
            return this.Client.EmailAsync(
                EmailTemplates.BankDetailUpdated,
                employeeId,
                CultureInfo.CurrentUICulture,
                null,
                detail);
        }

        public Task SendCloudRoomEmailAsync(long userId, string emailTemplate, EmailDetail detail, string label, string description, string url)
        {
            var parameters = new EmailParameters();
            if (!string.IsNullOrEmpty(url))
            {
                parameters.TryAdd("CloudRoomLabel", label);
                parameters.TryAdd("CloudRoomLink", description);
                parameters.TryAdd("CloudRoomUrl", url);
            }

            return this.Client.EmailAsync(
                emailTemplate,
                userId,
                parameters,
                detail);
        }

        public Task SendDeleteEmployeeEmailAsync(string email, string employeeFullName, string employeeNumber, string companyName, string organisationUnit)
        {
            var detail = new EmailDetail
             {
                new EmailDetailValue("Employee Name:",  employeeFullName),
                new EmailDetailValue("Employee Number:", employeeNumber),
                new EmailDetailValue("Company Name:", companyName),
                new EmailDetailValue("Organisation Unit", organisationUnit)
             };

            return this.Client.EmailAsync(
                EmailTemplates.EmployeeDeleted,
                email,
                CultureInfo.CurrentUICulture,
                null,
                detail);
        }

        public Task SendESSAutoRegisterEmailAsync(Uri url, string email)
        {
            return this.Client.EmailAsync(
                EmailTemplates.EmailAdded,
                email,
                CultureInfo.CurrentUICulture,
                new EmailParameters { Url = url },
                null);
        }

        public Task SendWebhookErrorsEmailAsync(long agencyId, string email, EmailDetail emailDetail, DateTime date)
        {
            var parameters = new EmailParameters();

            parameters.TryAdd("Subject", $"Webhook error summary for {date:d}");
            parameters.TryAdd("Text", $"Please see summary of webhook errors for {date:d}.");

            return this.Client.EmailAsync(EmailTemplates.WebhookErrors, email, agencyId, CultureInfo.CurrentUICulture, parameters, emailDetail);
        }

        public Task SendUserRegisterEmailAsync(Uri url, string email)
        {
            return this.Client.EmailAsync(
                EmailTemplates.UserAdded,
                email,
                CultureInfo.CurrentUICulture,
                new EmailParameters { Url = url },
                null);
        }

        public Task SendJobManagementEmailAsync(string template, string email, EmailDetail detail, EmailParameters parameters)
        {
            return this.Client.EmailAsync(
                template,
                email,
                CultureInfo.CurrentUICulture,
                parameters,
                detail);
        }
    }
}