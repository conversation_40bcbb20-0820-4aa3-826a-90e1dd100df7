namespace PaySpace.Venuta.Services.External
{
    public static class EmailTemplates
    {
        public const string Register = "SELFSERVICE";
        public const string Deregister = "DEREGISTER";
        public const string Forgot = "FORGOTTENPASS";
        public const string Otp = "OTP";

        public const string Payslip = "PAYSLIP";
        public const string PayslipScheduled = "PAYSLIP.SCHEDULED";

        public const string WorkflowSubmission = "Workflow.Submission";
        public const string WorkflowActioned = "Workflow.Actioned";
        public const string WorkflowDeleted = "Workflow.Deleted";
        public const string WorkflowApprover = "Workflow.Approver";

        public const string AppraisalApprovalRequired = "Appraisal.ApprovalRequired";
        public const string AppraisalFinished = "Appraisal.Finished";
        public const string AppraisalAccepted = "Appraisal.Accepted";
        public const string AppraisalRejected = "Appraisal.Rejected";

        public const string KpaReviewSubmission = "KpaReview.Submission";
        public const string KpaReviewAccepted = "KpaReview.Accepted";
        public const string KpaReviewRejected = "KpaReview.Rejected";
        public const string KpaReviewAcceptedWithoutComment = "KpaReview.AcceptedWithoutComment";
        public const string KpaReviewRejectedWithoutComment = "KpaReview.RejectedWithoutComment";

        public const string BulkUploadStatus = "BulkUploadStatus";
        public const string BankDetailUpdated = "Bankdetail.Updated";

        public const string CloudRoomItemCreated = "CloudRoom.Item.Created";
        public const string CloudRoomAssignedToUserChanged = "CloudRoom.AssignedToUserChanged";
        public const string CloudRoomStatusCompleted = "CloudRoom.Status.Completed";
        public const string CloudRoomCommentAdded = "CloudRoom.Comment.Added";

        public const string NewEngagement = "NewEngagement";

        public const string Invoice = "Invoice";
        public const string OutStatements = "OutStatements";
        public const string CutOffReminder = "CutOffReminder";

        public const string OnboardingNotification = "OnboardingNotification";
        public const string TerminationNotification = "TerminationNotification";

        public const string Report = "REPORT";

        public const string EmployeeDeleted = "EmployeeDeleted";

        public const string PayRateNotification = "PayRate.Notification";

        public const string EmailAdded = "EmailAdded";

        public const string XeroContact = "XeroContact";

        public const string LeaveExpiry = "Leave.Expiry";

        public const string WebhookErrors = "WebhookErrors";

        public const string UserAdded = "UserAdded";

        public const string JobCreated = "JobManagement.Created";

        public const string JobCopied = "JobManagement.Copied";

        public const string JobTransferred = "JobManagement.Transferred";

        public const string PensionLetterEmail = "Pension.Letter";

        public const string CompanyReset = "CompanyReset";

        public const string LeaveNotification = "Leave.Notification";
    }
}