namespace PaySpace.Venuta.Modules.Users.Services
{
    using System;
    using System.Buffers;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Http;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Options;

    using PaySpace.Cache;
    using PaySpace.Cache.Distributed;
    using PaySpace.Configuration;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Agency;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Security;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Modules.Users.Abstractions;
    using PaySpace.Venuta.Modules.Users.Abstractions.Models;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.External;

    public class UserProfileService : IUserProfileService
    {
        private readonly ApplicationContext context;
        private readonly ICompanyService companyService;
        private readonly ICompanySingleSignOnService companySingleSignOnService;
        private readonly IRedisService redisService;
        private readonly IEmailService emailService;
        private readonly IEnumService enumService;
        private readonly IdentitySettings identitySettings;
        private readonly IScopedCache scopedCache;
        private readonly IDapperRepository dapper;
        private readonly IAgencyService agencyService;
        private readonly IDistributedCache distributedCache;
        private readonly IUserUpdateService userUpdateService;
        private readonly IAuditPublisherService auditPublisherService;
        private readonly ITenantProvider tenantProvider;

        private readonly string region;

        public UserProfileService(
            ApplicationContext context,
            ICompanyService companyService,
            ICompanySingleSignOnService companySingleSignOnService,
            IRedisService redisService,
            IEmailService emailService,
            IEnumService enumService,
            IOptions<IdentitySettings> identitySettings,
            IScopedCache scopedCache,
            IDapperRepository dapper,
            IAgencyService agencyService,
            IDistributedCache distributedCache,
            IUserUpdateService userUpdateService,
            IAuditPublisherService auditPublisherService,
            ITenantProvider tenantProvider,
            IConfiguration configuration)
        {
            this.context = context;
            this.companyService = companyService;
            this.companySingleSignOnService = companySingleSignOnService;
            this.redisService = redisService;
            this.emailService = emailService;
            this.enumService = enumService;
            this.identitySettings = identitySettings.Value;
            this.scopedCache = scopedCache;
            this.dapper = dapper;
            this.agencyService = agencyService;
            this.distributedCache = distributedCache;
            this.userUpdateService = userUpdateService;
            this.auditPublisherService = auditPublisherService;
            this.tenantProvider = tenantProvider;

            this.region = configuration.GetRegion();
        }

        public Task<User> FindByIdWithNavigationAsync(long userId)
        {
            return this.context.Set<User>()
                .TagWithSource()
                .Include(_ => _.UserCompanyLinks)
                .Include(_ => _.SecurityGroupsUsers)
                .Include(_ => _.UserCompanyContactTypes)
                .Include(_ => _.UserAgencyLinks)
                .Include(_ => _.Employee)
                .Include(_ => _.UserCountryPermissions)
                .Where(_ => _.UserId == userId)
                .SingleOrDefaultAsync();
        }

        public IQueryable<User> GetEmployeeUsers(long companyId)
        {
            return this.GetUsers().ForRegion(this.region).Where(_ => _.Employee.CompanyId == companyId && _.UserType == UserType.Employee);
        }

        public IQueryable<User> GetCompanyUsers(UserType userType, long companyGroupId)
        {
            return this.GetUsers().ForRegion(this.region).Where(_ => _.CompanyGroupId == companyGroupId && _.UserType == userType);
        }

        public IQueryable<User> GetAgencyUsers(UserType userType, long agencyId)
        {
            return this.GetUsers().Where(_ => _.AgencyId == agencyId && _.UserType == userType);
        }

        public IQueryable<User> GetBureauUsers()
        {
            return this.GetUsers().Where(_ => _.UserType == UserType.Bureau);
        }

        public IQueryable<User> GetUsers(long companyGroupId, long companyId, long? agencyId, long? securityGroupUsers, long? userCompanyLinks, long? userAgencyLinks, int? userCompanyContactTypes)
        {
            var query = this.GetUsers();

            query = query.Where(u =>
                this.GetCompanyUsers(UserType.Company, companyGroupId)
                    .Any(cu => cu.UserId == u.UserId && cu.CompanyGroupId == companyGroupId) ||
                this.GetEmployeeUsers(companyId)
                    .Any(eu => eu.UserId == u.UserId && eu.Employee.CompanyId == companyId) ||
                (agencyId.HasValue && this.GetAgencyUsers(UserType.Agency, agencyId.Value)
                    .Any(au => au.UserId == u.UserId && au.AgencyId == agencyId))
            );

            if (securityGroupUsers.HasValue)
            {
                query = query.Where(u => u.SecurityGroupsUsers.Any(x => x.SecurityGroupId == securityGroupUsers.Value));
            }

            if (userCompanyLinks.HasValue)
            {
                query = query.Where(u => u.UserCompanyLinks.Any(x => x.CompanyFrequencyId == userCompanyLinks.Value));
            }

            if (userAgencyLinks.HasValue)
            {
                query = query.Where(u => u.UserAgencyLinks.Any(x => x.CompanyId == userAgencyLinks.Value));
            }

            if (userCompanyContactTypes.HasValue)
            {
                query = query.Where(u => u.UserCompanyContactTypes.Any(x => x.ContactTypeId == userCompanyContactTypes.Value));
            }

            return query;
        }

        public IQueryable<UserResult> GetUserProfiles(long companyGroupId, long companyId, long? agencyId, long? securityGroupUsers, long? userCompanyLinks, long? userAgencyLinks, int? userCompanyContactTypes)
        {
            var query = this.GetUsers(companyGroupId, companyId, agencyId, securityGroupUsers, userCompanyLinks, userAgencyLinks, userCompanyContactTypes);

            return query.Select(u => new UserResult
            {
                UserId = u.UserId,
                UserType = u.UserType,
                FirstName = u.FirstName,
                LastName = u.LastName,
                Email = u.Email,
                UserStatus = u.UserStatus,
                UserCompanyContactTypes = u.UserCompanyContactTypes
                    .Select(x => x.ContactTypeId)
                    .ToList(),
                LoginDate = u.CompanyLoginAudits.Max(x => x.LoginDate),
                CompanyId = u.Employee != null ? u.Employee.CompanyId : 0,
                AllocateContact = u.UserType == UserType.Employee && u.UserCompanyContactTypes.Count != 0,
                IsPowerBiUser = u.IsPowerBIUser,
                EmployeeNumber = u.Employee != null ? u.Employee.EmployeeNumber : null,
                IsTerminated = u.Employee.EmploymentStatuses.OrderByDescending(_ => _.EmploymentDate).FirstOrDefault(_ => _.EmploymentDate <= DateTime.Today).TerminationDate != null,
                BureauAdmin = u.BureauAdmin.HasValue && u.BureauAdmin.Value,
                CanEditHistoricalRecords = u.CanEditHistoricalRecords,
                AnalyticsUser = u.AnalyticsUser.HasValue && u.AnalyticsUser.Value,
                IsCloudRoomUser = u.IsCloudRoomUser.HasValue && u.IsCloudRoomUser.Value,
                IsAgencyTopLevelUser = u.IsAgencyTopLevelUser.HasValue && u.IsAgencyTopLevelUser.Value,
                IsOrgChartUser = u.IsOrgChartUser.HasValue && u.IsOrgChartUser.Value,
            });
        }

        public async Task<User> AddAsync(User entity, HostString tenant)
        {
            this.UserCleanUp(entity);

            await this.HandleCompanyUserEmployeeIdAsync(entity);
            await this.HashOrEncryptPasswordAsync(entity);

            var change = new ChangedEntity()
            {
                Entry = this.context.Entry(entity),
                State = EntityState.Added
            };

            var userCompanyLinks = entity.UserCompanyLinks?.ToList() ?? [];
            var securityGroupsUsers = entity.SecurityGroupsUsers?.ToList() ?? [];
            var userCompanyContactTypes = entity.UserCompanyContactTypes?.ToList() ?? [];
            var userAgencyLinks = entity.UserAgencyLinks?.ToList() ?? [];
            var userPasswordHistories = entity.UserPasswordHistories?.ToList() ?? [];
            var companyLoginAudits = entity.CompanyLoginAudits?.ToList() ?? [];
            var countryPermissions = entity.UserCountryPermissions?.ToList() ?? [];

            // Clear collections, we can't save this yet. the user object wil lbe saved via the function
            entity!.UserCompanyLinks?.Clear();
            entity.SecurityGroupsUsers?.Clear();
            entity.UserCompanyContactTypes?.Clear();
            entity.UserAgencyLinks?.Clear();
            entity.UserPasswordHistories?.Clear();
            entity.CompanyLoginAudits?.Clear();
            entity.UserCountryPermissions?.Clear();

            // We save the user object via the function, should not save here
            this.context.Entry(entity).State = EntityState.Detached;

            var modifiedByUserId = this.tenantProvider.GetUserId();

            // Create user via function so that it is created in all regions
            entity.UserId = await this.userUpdateService.AddOrUpdateAsync(entity.UserId, entity);
            if (entity.UserId == default)
            {
                throw new InvalidOperationException("Error Creating user");
            }

            await this.auditPublisherService.PublishAuditAsync([change], modifiedByUserId, DateTime.Now);

            await this.context.Entry(entity).ReloadAsync();

            // Re-add collections so that EF can detect the change and save the related data
            entity.UserCompanyLinks?.AddRange(userCompanyLinks);
            entity.SecurityGroupsUsers?.AddRange(securityGroupsUsers);
            entity.UserCompanyContactTypes?.AddRange(userCompanyContactTypes);
            entity.UserAgencyLinks?.AddRange(userAgencyLinks);
            entity.UserPasswordHistories?.AddRange(userPasswordHistories);
            entity.CompanyLoginAudits?.AddRange(companyLoginAudits);
            entity.UserCountryPermissions?.AddRange(countryPermissions);

            await this.context.SaveChangesAsync();

            await this.distributedCache.RemoveAsync(CacheKeys.UserBureauCountryPermissions(entity.UserId));

            if (entity.UserType != UserType.Bureau)
            {
                await this.SendRegistrationMailAsync(entity, tenant);
            }

            return entity;
        }

        public async Task<User> UpdateAsync(User entity)
        {
            ArgumentNullException.ThrowIfNull(entity);

            this.UserCleanUp(entity);

            var entry = this.context.Entry(entity);
            if (entity.UserType == UserType.Employee && entry.Property(_ => _.UserType).OriginalValue == UserType.Company)
            {
                entity.EmployeeId = await this.GetUserEmployeeIdAsync(entity.CompanyGroupId, entity.Email);
                entity.UserCompanyLinks = null;
                entity.SecurityGroupsUsers = null;
                entity.CompanyGroupId = null;
                entity.AllocateContact = entity.ReceiveNewsletters == true;
                entity.JobTitle = string.Empty;
                entity.IsBudgetUser = false;
                entity.IsCloudRoomUser = false;
                entity.IsOrgChartUser = false;
                entity.CanEditHistoricalRecords = false;
                entity.IsPowerBIUser = false;
                entity.CompanyGroupId = null;
            }

            if (entity.UserType == UserType.Employee && entry.Property(_ => _.UserType).OriginalValue == UserType.Agency)
            {
                var companyGroupId = this.companyService.GetCompanyGroupId(entity.CompanyId.Value);

                entity.EmployeeId = await this.GetUserEmployeeIdAsync(companyGroupId, entity.Email);
                entity.AgencyId = null;
                entity.IsAgencyTopLevelUser = null;
                entity.AnalyticsUser = null;
                entity.IsCloudRoomUser = null;
                entity.IsOrgChartUser = null;
                entity.UserAgencyLinks = null;
                entity.IsBudgetUser = false;
                entity.CanEditHistoricalRecords = false;
                entity.IsPowerBIUser = false;
            }

            var modifiedByUserId = this.tenantProvider.GetUserId();

            var change = new ChangedEntity()
            {
                Entry = entry,
                State = entry.State,
                Properties = this.auditPublisherService.GetModifiedProperties(entry)
            };

            _ = await this.userUpdateService.AddOrUpdateAsync(entity.UserId, entity);

            await this.auditPublisherService.PublishAuditAsync([change], modifiedByUserId, DateTime.Now);

            await entry.ReloadAsync();

            await this.context.SaveChangesAsync();

            await this.distributedCache.RemoveAsync(CacheKeys.UserBureauCountryPermissions(entity.UserId));

            return entity;
        }

        public Task<long?> GetUserEmployeeIdAsync(long? companyGroupId, string email)
        {
            return this.context.Set<Employee>()
                .AsNoTracking()
                .TagWithSource()
                .Where(_ => _.Company.GroupLinks.Any(_ => _.CompanyGroupId == companyGroupId))
                .Where(_ => _.Email == email)
                .Select(_ => (long?)_.EmployeeId)
                .SingleOrDefaultAsync();
        }

        public Task<IQueryable<SecurityGroup>> GetSecurityGroupsAsync(UserType userType, long? companyId = null)
        {
            return userType switch
            {
                UserType.Bureau => Task.FromResult(this.GetBureauSecurityGroups()),
                UserType.Agency => this.GetAgencySecurityGroupsAsync(companyId!.Value),
                _ => this.GetCompanySecurityGroupsAsync(companyId!.Value)
            };
        }

        public Task<List<ContactType>> GetUserContactTypesForCompanyGroupAsync(long companyGroupId)
        {
            return this.context.Set<User>()
                .ForRegion(this.region)
                .TagWithSource()
                .Where(_ => (_.CompanyGroupId == companyGroupId || _.Employee.Company.GroupLinks.Any(x => x.CompanyGroupId == companyGroupId)) && _.UserStatus == UserStatus.Active)
                .SelectMany(_ => _.UserCompanyContactTypes)
                .Select(_ => (ContactType)_.ContactTypeId)
                .Distinct()
                .ToListAsync();
        }

        public Task<List<ContactType>> GetUserContactTypesForAgencyAsync(long agencyId)
        {
            return this.context.Set<User>()
                .TagWithSource()
                .Where(_ => _.UserType == UserType.Agency && _.AgencyId == agencyId && _.UserStatus == UserStatus.Active)
                .SelectMany(_ => _.UserCompanyContactTypes)
                .Select(_ => (ContactType)_.ContactTypeId)
                .Distinct()
                .ToListAsync();
        }

        public Task<IList<GlobalUserSearchResult>> GetGlobalUsersAsync(GlobalUserSearchModel searchModel)
        {
            return this.dapper.GetAsync<GlobalUserSearchResult>(
                "global_employee_search",
                new
                {
                    FirstName = string.IsNullOrWhiteSpace(searchModel.FirstName) ? null : searchModel.FirstName,
                    LastName = string.IsNullOrWhiteSpace(searchModel.LastName) ? null : searchModel.LastName,
                    EmpNumber = string.IsNullOrWhiteSpace(searchModel.EmployeeNumber) ? null : searchModel.EmployeeNumber,
                    EmailAddress = string.IsNullOrWhiteSpace(searchModel.EmailAddress) ? null : searchModel.EmailAddress
                });
        }

        public async Task SetUserSecurityGroupsAsync(User user, long? companyId, bool hasSystemAccess, List<long> selectedSecurityGroupIds)
        {
            // `user.SecurityGroupsUsers` is tracked, so changes to the list will persist to the db when save changes is called.
            if (companyId > 0)
            {
                if (!hasSystemAccess)
                {
                    user.SecurityGroupsUsers.ForEach(_ => _.IsUserProfile = true);
                    user.SecurityGroupsUsers = null;
                    return;
                }
            }

            selectedSecurityGroupIds ??= new List<long>();

            switch (user.UserId)
            {
                // select default group if nothing is selected
                case > 0:
                    var groupsToRemove = user.SecurityGroupsUsers.Where(_ => !selectedSecurityGroupIds.Contains(_.SecurityGroupId)).ToList();

                    foreach (var securityGroup in groupsToRemove)
                    {
                        securityGroup.IsUserProfile = true;
                    }

                    user.SecurityGroupsUsers.RemoveRange(groupsToRemove);
                    selectedSecurityGroupIds = selectedSecurityGroupIds.Where(_ => !user.SecurityGroupsUsers.Select(x => x.SecurityGroupId).Contains(_)).ToList();
                    break;
                case <= 0 when user.UserType == UserType.Company && selectedSecurityGroupIds.Count == 0:
                    var securityGroupQuery = await this.GetSecurityGroupsAsync(UserType.Company, companyId);
                    var defaultGroupId = await securityGroupQuery
                        .Where(_ => _.CompanyId == companyId && _.IsDefault == true)
                        .Select(_ => _.SecurityGroupId)
                        .FirstOrDefaultAsync();
                    selectedSecurityGroupIds.Add(defaultGroupId);
                    break;
            }

            if (selectedSecurityGroupIds.Count > 0)
            {
                var securityGroupQuery = await this.GetSecurityGroupsAsync(user.UserType, companyId);
                var newSecurityGroups = await securityGroupQuery
                    .Where(_ => selectedSecurityGroupIds.Contains(_.SecurityGroupId))
                    .Select(_ => new SecurityGroupUser
                    {
                        SecurityGroupId = _.SecurityGroupId,
                        UserId = user.UserId,
                        IsUserProfile = true
                    }).ToListAsync();

                user.SecurityGroupsUsers.AddRange(newSecurityGroups);
            }
        }

        public async Task SetUserCompanyLinksAsync(User user, long companyId, long frequencyId, bool hasSystemAccess, List<long> selectedCompanyFrequencyIds)
        {
            // `user.UserCompanyLinks` is tracked, so changes to the list will persist to the db when save changes is called.
            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            if (!hasSystemAccess)
            {
                foreach (var companyLinks in user.UserCompanyLinks)
                {
                    companyLinks.IsUserProfile = true;
                }

                user.UserCompanyLinks = null;
                return;
            }

            selectedCompanyFrequencyIds ??= new List<long>();

            switch (user.UserId)
            {
                case <= 0 when selectedCompanyFrequencyIds.Count == 0:
                    selectedCompanyFrequencyIds.Add(frequencyId);
                    break;
                case > 0:
                    {
                        foreach (var userCompanyLink in user.UserCompanyLinks.Where(userCompanyLink => userCompanyLink.CompanyFrequencyId.HasValue))
                        {
                            userCompanyLink.Checked = selectedCompanyFrequencyIds.Contains(userCompanyLink.CompanyFrequencyId!.Value);
                            userCompanyLink.IsUserProfile = true;
                        }

                        selectedCompanyFrequencyIds = selectedCompanyFrequencyIds.Where(_ => !user.UserCompanyLinks.Select(x => x.CompanyFrequencyId).Contains(_)).ToList();
                        break;
                    }
            }

            if (selectedCompanyFrequencyIds.Count > 0)
            {
                var newUserCompanyLinks = await this.companyService.GetByGroup(companyGroupId)
                    .SelectMany(_ => _.CompanyRunFrequencies)
                    .Where(_ => !user.UserCompanyLinks.Select(x => x.CompanyFrequencyId).Contains(_.CompanyFrequencyId))
                    .Select(_ => new UserCompanyLink
                    {
                        UserId = user.UserId,
                        CompanyId = _.CompanyId,
                        CompanyFrequencyId = _.CompanyFrequencyId,
                        Checked = selectedCompanyFrequencyIds.Contains(_.CompanyFrequencyId),
                        IsUserProfile = true
                    }).ToListAsync();

                user.UserCompanyLinks.AddRange(newUserCompanyLinks);
            }
        }

        public async Task SetUserAgencyLinksAsync(User user, long companyId, List<long> selectedCompanyIds)
        {
            // `user.UserAgencyLinks` is tracked, so changes to the list will persist to the db when save changes is called.
            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            user.AgencyId ??= agencyId;

            selectedCompanyIds ??= new List<long>();
            switch (user.UserId)
            {
                case <= 0 when selectedCompanyIds.Count == 0:
                    selectedCompanyIds.Add(companyId);
                    break;
                case > 0:
                    {
                        var linksToRemove = user.UserAgencyLinks.Where(_ => !selectedCompanyIds.Contains(_.CompanyId)).ToList();
                        user.UserAgencyLinks.RemoveRange(linksToRemove);
                        selectedCompanyIds = selectedCompanyIds.Where(_ => !user.UserAgencyLinks.Select(x => x.CompanyId).Contains(_)).ToList();
                        break;
                    }
            }

            if (selectedCompanyIds.Count > 0)
            {
                var newAgencyLinks = await this.companyService.GetByAgency(agencyId)
                    .Where(_ => selectedCompanyIds.Contains(_.CompanyId))
                    .Select(_ => new UserAgencyLink
                    {
                        CompanyId = _.CompanyId,
                        UserId = user.UserId
                    }).ToListAsync();

                user.UserAgencyLinks.AddRange(newAgencyLinks);
            }
        }

        public async Task SetUserCompanyContactTypeAsync(User user, long companyId, bool allocateContact, List<int> selectedContactTypesIds)
        {
            if (!allocateContact && user.UserType == UserType.Employee && user.UserType != UserType.Company)
            {
                return;
            }

            // Check if contact types should be disabled for the agency. If contact types are disabled then remove the contact types from the user.
            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var disableContactType = user.UserType != UserType.Agency && await this.IsContactTypeDisabled(agencyId);

            // `user.UserCompanyContactTypes` is tracked, so changes to the list will persist to the db when save changes is called.
            selectedContactTypesIds = (selectedContactTypesIds == null || disableContactType)
                ? []
                : selectedContactTypesIds;

            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            if (user.UserId > 0)
            {
                var contactsToRemove = user.UserCompanyContactTypes.Where(_ => !selectedContactTypesIds.Contains(_.ContactTypeId)).ToList();
                foreach (var userCompanyContactType in contactsToRemove)
                {
                    userCompanyContactType.CompanyGroupId = companyGroupId;
                }

                user.UserCompanyContactTypes.RemoveRange(contactsToRemove);
                selectedContactTypesIds = selectedContactTypesIds.Where(_ => !user.UserCompanyContactTypes.Select(x => x.ContactTypeId).Contains(_)).ToList();
            }

            if (selectedContactTypesIds.Count > 0)
            {
                var newContactType = (await this.enumService.GetContactTypeAsync())
                    .Where(_ => selectedContactTypesIds.Contains(_.ContactTypeId))
                    .Select(_ => new UserContactType
                    {
                        UserId = user.UserId,
                        CompanyGroupId = companyGroupId,
                        ContactTypeId = _.ContactTypeId
                    }).ToList();

                user.UserCompanyContactTypes.AddRange(newContactType);
            }
        }

        public async Task ActivateUserAsync(long companyId, List<long> userIds)
        {
            var users = await this.context.Set<User>()
                .Where(_ => userIds.Contains(_.UserId))
                .TagWithSource()
                .ToListAsync();

            foreach (var user in users)
            {
                user.UserStatus = UserStatus.Active;

                if (user.UserType != UserType.Bureau)
                {
                    user.CompanyId = companyId;
                    user.IsUserActive = true;
                }
            }

            await this.context.SaveChangesAsync(); // here for auditing

            await Parallel.ForEachAsync(users, async (u, ct) => await this.userUpdateService.AddOrUpdateAsync(u.UserId, u));
        }

        public Task<bool> CheckUserEmailExistsAsync(string email, long userId)
        {
            email = email.Trim();
            return this.context.Set<User>().AnyAsync(_ => _.Email == email && _.UserId != userId);
        }

        public Task<bool> IsAgencyTopLevelUserAsync(long userId)
        {
            return this.redisService.GetOrCreateAsync(
                CacheKeys.UserAgencyTopLevel(userId),
                () => this.context.Set<User>()
                    .Where(_ => _.UserId == userId)
                    .Select(_ => _.IsAgencyTopLevelUser == true)
                    .SingleAsync());
        }

        public void SetBureauUserCountryPermission(User user, int[] countryIds, bool allow = true)
        {
            countryIds ??= [];
            user.UserCountryPermissions ??= new List<BureauUserCountryPermission>();

            var currentCountryIds = user.UserCountryPermissions.Select(c => c.CountryId).ToHashSet();

            foreach (var permission in user.UserCountryPermissions)
            {
                permission.Allow = countryIds.Contains(permission.CountryId);
            }

            var newCountryIds = countryIds.Except(currentCountryIds);
            user.UserCountryPermissions.AddRange(newCountryIds.Select(_ => new BureauUserCountryPermission
            {
                Allow = allow,
                CountryId = _,
                UserId = user.UserId
            }));
        }

        public async Task<bool> IsContactTypeDisabled(long agencyId)
        {
            var agencyType = await this.agencyService.GetAgencyTypeAsync(agencyId);
            return agencyType.HasValue && agencyType != AgencyType.DirectClient;
        }

        private IQueryable<User> GetUsers()
        {
            return this.context.Set<User>()
                .TagWithSource()
                .AsNoTracking();
        }

        private void UserCleanUp(User entity)
        {
            switch (entity.UserType)
            {
                case UserType.Employee:
                    entity.UserCompanyLinks = null;
                    entity.SecurityGroupsUsers = null;
                    break;
                case UserType.Bureau:
                    entity.UserCompanyLinks = null;
                    entity.UserAgencyLinks = null;
                    break;
                case UserType.Agency when entity.AgencyId == PaySpaceConstants.PaySpaceAgencyId:
                    {
                        // There should never be an agency user linked to the paySpace agency
                        throw new InvalidOperationException("Invalid frequency for user, please try again");
                    }
            }
        }

        private async Task SendRegistrationMailAsync(User entity, HostString tenant)
        {
            if (!string.IsNullOrEmpty(entity.Email))
            {
                var isSSO = entity.UserType == UserType.Company && !string.IsNullOrEmpty(await this.scopedCache.GetOrCreateAsync(
                    $"Company:{entity.CompanyId}:isSSO",
                    () => this.companySingleSignOnService.GetExternalIdentityProviderAsync(entity.CompanyId!.Value)));

                if (!isSSO)
                {
                    // build up string for url
                    var url = new Uri($"{this.identitySettings.Authority}/Register/UserRegister?tenant={tenant}");
                    await this.emailService.SendUserRegisterEmailAsync(url, entity.Email);
                }
            }
        }

        private async Task HashOrEncryptPasswordAsync(User entity)
        {
            var hashSetting = entity.UserType != UserType.Bureau && await this.companyService.GetHashSettingsAsync(entity.CompanyId.Value);
            var customPasswordHasher = new CustomPasswordHasher(ArrayPool<byte>.Create());
            var crypto = new Crypto(Crypto.Des);
            var password = entity.Password;

            entity.Password ??= Guid.NewGuid().ToString();

            var result = customPasswordHasher.HashPassword(entity.Password, string.Empty, false, hashSetting);

            entity.Password = result.HashedPassword;
            entity.Salt = result.Salt;

            entity.SecurityStamp = Guid.NewGuid().ToString();

            if (!string.IsNullOrEmpty(password))
            {
                entity.UserPasswordHistories = new List<UserPasswordHistory>
                {
                    new() {
                        UserPassword = crypto.Encrypt(password),
                        Salt = entity.Salt
                    }
                };

                entity.CompanyLoginAudits = new List<CompanyLoginAudit>
                {
                    new() {
                        LoginDate = DateTime.Now
                    }
                };
            }
        }

        private IQueryable<SecurityGroup> GetBureauSecurityGroups()
        {
            return this.context.Set<SecurityGroup>()
                .TagWithSource()
                .AsNoTracking()
                .Where(_ => _.BureauId == 1);
        }

        private async Task<IQueryable<SecurityGroup>> GetAgencySecurityGroupsAsync(long companyId)
        {
            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            return this.context.Set<SecurityGroup>()
                .TagWithSource()
                .AsNoTracking()
                .Where(_ => _.Company.AgencyId == agencyId);
        }

        private async Task<IQueryable<SecurityGroup>> GetCompanySecurityGroupsAsync(long companyId)
        {
            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            return this.context.Set<SecurityGroup>()
                .TagWithSource()
                .AsNoTracking()
                .Where(_ => _.Company.GroupLinks.Select(x => x.CompanyGroupId).FirstOrDefault() == companyGroupId);
        }

        private async Task HandleCompanyUserEmployeeIdAsync(User entity)
        {
            if (entity.UserType == UserType.Company)
            {
                var employeeId = await this.GetUserEmployeeIdAsync(entity.CompanyGroupId, entity.Email);
                if (employeeId.HasValue)
                {
                    var userProfileExists = await this.GetUsers().ForRegion(this.region).AnyAsync(_ => _.EmployeeId == employeeId);
                    if (!userProfileExists)
                    {
                        entity.EmployeeId = employeeId;
                    }
                }
            }
        }
    }
}