namespace PaySpace.Venuta.Reports.ViewModels
{
    using System.Collections.Generic;

    using DevExpress.XtraReports.Web.ReportDesigner;

    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.Services;

    public class ReportDesignerViewModel
    {
        public string AccessToken { get; set; }

        public string ReportId { get; set; }

        public ReportType? ReportType { get; set; }

        public long AgencyId { get; set; }

        public long CompanyId { get; set; }

        public long FrequencyId { get; set; }

        public int? ReportCategoryId { get; set; }

        public long? ReportSubCategoryId { get; set; }

        public string CompanyName { get; set; }

        public string TemplateDisplayName { get; set; }

        public string Description { get; set; }

        public bool CanSetDesignableStatus { get; set; }

        public string ReportPath { get; set; }

        public long CompanyGroupId { get; set; }

        public ReportContextLevel ReportContextLevel { get; set; }

        public bool ShowReportContextLevel { get; set; }

        public ReportSource ReportSource { get; set; }

        public bool IsCustomReport => ReportUtils.IsCustomReport(this.ReportId);

        public string ReportDirectory { get; set; }

        public bool ReportExists { get; set; }

        public bool IsReportResettable { get; set; }

        public ReportDesignerModel DesignerModel { get; set; }

        public string? Version { get; set; }

        public string RelatedEntityType { get; set; }

        public IEnumerable<string> RelatedEntities { get; set; }

        public string ExportSeparator { get; set; }

        public bool ExportQuoteStringsWithSeparators { get; set; }

        public bool IsEnabled { get; set; }

        public bool IsDesignable { get; set; }

        public bool CanDownloadReport { get; set; }

        public bool ShouldRefreshAfterSave { get; set; }

        public long? ReportListId { get; set; }

        public bool ShowReportHistory { get; set; }

        public bool CanSetEnableStatus { get; set; }
    }
}