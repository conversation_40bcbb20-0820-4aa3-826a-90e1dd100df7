namespace PaySpace.Venuta.Reports.ViewModels
{
    using DevExpress.XtraReports.Web.ParametersPanel;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Services.Reports.Enums;

    public class ReportViewerViewModel
    {
        public string AccessToken { get; set; }

        public string ReportId { get; set; }

        public ReportType? ReportType { get; set; }

        public long AgencyId { get; set; }

        public long CompanyId { get; set; }

        public long FrequencyId { get; set; }

        public HostString? Tenant { get; set; }

        public ReportViewType ReportViewType { get; set; }

        public string CompanyName { get; set; }

        public string TemplateDisplayName { get; set; }

        public string Description { get; set; }

        public string ReportDirectory { get; set; }

        public ReportParametersPanelModel ViewerModel { get; set; }
    }
}