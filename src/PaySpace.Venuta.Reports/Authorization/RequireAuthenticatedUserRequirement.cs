namespace PaySpace.Venuta.Reports.Authorization
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authorization;

    public class RequireAuthenticatedUserRequirement : AuthorizationHandler<RequireAuthenticatedUserRequirement>, IAuthorizationRequirement
    {
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, RequireAuthenticatedUserRequirement requirement)
        {
            if (context.User.Identities.All(i => i.IsAuthenticated))
            {
                context.Succeed(requirement);
            }
            else
            {
                context.Fail();
            }

            return Task.CompletedTask;
        }
    }
}