namespace PaySpace.Venuta.Reports.ViewModelBuilder
{
    using System.Globalization;
    using System.IO;
    using System.Threading;
    using System.Threading.Tasks;

    using DevExpress.AspNetCore.Reporting.WebDocumentViewer;
    using DevExpress.XtraReports.UI;
    using DevExpress.XtraReports.Web.WebDocumentViewer.Native;
    using DevExpress.XtraReports.Web.WebDocumentViewer.Native.Services;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Reports.ViewModels;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Extensions;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Reports.Services;

    using VenutaReportInfo = PaySpace.Venuta.Services.Reports.Models.ReportInfo;

    public interface IReportViewerViewModelBuilder
    {
        Task<ReportViewerViewModel> BuildViewerReportViewModelAsync(
            long companyId,
            long frequencyId,
            string reportId,
            long userId,
            bool includeDisabledReports,
            string accessToken,
            HostString? tenant,
            CancellationToken cancellationToken);
    }

    public class ReportViewerViewModelBuilder : IReportViewerViewModelBuilder
    {
        private readonly IParametersPanelModelGeneratorAsync documentViewerModelGenerator;
        private readonly IWebDocumentViewerUriProvider documentViewerUriProvider;
        private readonly ICompanyService companyService;
        private readonly IReportHeaderService reportHeaderService;
        private readonly IReportRetrievalService reportRetrievalService;

        public ReportViewerViewModelBuilder(
            IParametersPanelModelGeneratorAsync documentViewerModelGenerator,
            IWebDocumentViewerUriProvider documentViewerUriProvider,
            ICompanyService companyService,
            IReportHeaderService reportHeaderService,
            IReportRetrievalService reportRetrievalService)
        {
            this.documentViewerModelGenerator = documentViewerModelGenerator;
            this.documentViewerUriProvider = documentViewerUriProvider;
            this.companyService = companyService;
            this.reportHeaderService = reportHeaderService;
            this.reportRetrievalService = reportRetrievalService;
        }

        public async Task<ReportViewerViewModel> BuildViewerReportViewModelAsync(
            long companyId,
            long frequencyId,
            string reportId,
            long userId,
            bool includeDisabledReports,
            string accessToken,
            HostString? tenant,
            CancellationToken cancellationToken)
        {
            var companySummary = await this.companyService.GetCompanySummary(companyId);

            var reportPathIdentifier = ReportUtils.IsStandardReport(reportId)
                ? (await this.reportHeaderService.GetByIdAsync(long.Parse(reportId!))).ReportPath
                : reportId;

            var reportPath = ReportPath.CreateWithStandardParameters(reportPathIdentifier, companyId, companySummary.AgencyId, includeDisabledReports, frequencyId);
            var reportInfo = await this.reportRetrievalService.GetReportInfoForGenerationAsync(userId, reportPath, CultureInfo.CurrentCulture, accessToken, cancellationToken);

            return await this.GetReportViewerViewModel(companyId, frequencyId, reportId, accessToken, includeDisabledReports, reportInfo, companySummary, tenant);
        }

        private async Task<ReportViewerViewModel> GetReportViewerViewModel(
            long companyId,
            long frequencyId,
            string reportId,
            string accessToken,
            bool includeDisabledReports,
            VenutaReportInfo reportInfo,
            CompanySummary companySummary,
            HostString? tenant)
        {
            var agencyId = companySummary.AgencyId;
            var reportPath = reportInfo.ReportSource.ReportPath;
            var report = reportInfo.Report;
            var reportViewerModelInfo = GetReportViewerModelInfo(reportPath, report, agencyId, companyId, includeDisabledReports, frequencyId);

            var viewModel = new ReportViewerViewModel
            {
                ReportId = reportId,
                AccessToken = accessToken,
                AgencyId = agencyId,
                CompanyId = companyId,
                FrequencyId = frequencyId,
                CompanyName = companySummary.CompanyName,
                ReportDirectory = Path.GetDirectoryName(reportPath),
                ReportType = report.GetReportType(),
                ViewerModel = await this.documentViewerModelGenerator.GenerateAsync(reportViewerModelInfo),
                TemplateDisplayName = report.DisplayName,
                Tenant = tenant
            };
            viewModel.ViewerModel.HandlerUri = this.documentViewerUriProvider.GetAbsoluteUri(WebDocumentViewerController.DefaultUri);

            return viewModel;
        }

        private static ReportModelInfo GetReportViewerModelInfo(string fullReportPath, XtraReport report, long agencyId, long companyId, bool includeDisabledReports, long? frequencyId)
        {
            var reportPath = ReportPath.CreateWithStandardParameters(fullReportPath, companyId, agencyId, includeDisabledReports, frequencyId);
            return new ReportModelInfo(reportPath.OriginalString, report);
        }
    }
}