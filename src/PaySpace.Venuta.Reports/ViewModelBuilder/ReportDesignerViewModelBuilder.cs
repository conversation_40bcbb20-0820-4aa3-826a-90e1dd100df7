namespace PaySpace.Venuta.Reports.ViewModelBuilder
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using DevExpress.AspNetCore.Reporting.ReportDesigner;
    using DevExpress.AspNetCore.Reporting.WebDocumentViewer;
    using DevExpress.XtraPrinting;
    using DevExpress.XtraReports.Web.ReportDesigner;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Reports.ViewModels;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.Extensions;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Reports.Services;
    using PaySpace.Venuta.Storage;

    public interface IReportDesignerViewModelBuilder
    {
        Task<ReportDesignerViewModel> BuildDesignerViewModelAsync(
            string reportId,
            long companyId,
            long frequencyId,
            long userId,
            string userType,
            string version,
            bool includeDisabledReports,
            string accessToken,
            string? reportPath,
            IServiceProvider serviceProvider,
            CancellationToken cancellationToken);
    }

    public class ReportDesignerViewModelBuilder : IReportDesignerViewModelBuilder
    {
        private readonly ICompanyService companyService;
        private readonly IReportStorageService reportStorageService;
        private readonly IEnumService enumService;
        private readonly IAgencyService agencyService;
        private readonly IStringLocalizer reportLocalizer;
        private readonly IReportRetrievalService reportRetrievalService;

        public ReportDesignerViewModelBuilder(
            IStringLocalizerFactory stringLocalizerFactory,
            ICompanyService companyService,
            IReportStorageService reportStorageService,
            IEnumService enumService,
            IAgencyService agencyService,
            IReportRetrievalService reportRetrievalService)
        {
            this.companyService = companyService;
            this.reportStorageService = reportStorageService;
            this.enumService = enumService;
            this.agencyService = agencyService;
            this.reportRetrievalService = reportRetrievalService;

            this.reportLocalizer = stringLocalizerFactory.Create(SystemAreas.Report.Area, null);
        }

        public async Task<ReportDesignerViewModel> BuildDesignerViewModelAsync(
            string reportId,
            long companyId,
            long frequencyId,
            long userId,
            string userType,
            string version,
            bool includeDisabledReports,
            string accessToken,
            string reportPath,
            IServiceProvider serviceProvider,
            CancellationToken cancellationToken)
        {
            var companySummary = await this.companyService.GetCompanySummary(companyId);
            var agencyId = companySummary.AgencyId;
            var companyGroupId = companySummary.CompanyGroupId;
            var taxCountryId = companySummary.TaxCountryId;
            ReportPath reportPathWithStandardParameters;

            // When saving a report, the Report Path is no longer the full path, but rather only the ReportId as you could have changed the destination the report will live
            // for this case we need to go find out the new path that now the report lives at. The GetDirectoryName method returns the directory of the file, but if there isnt one its empty.
            if (!string.IsNullOrEmpty(reportPath))
            {
                reportPathWithStandardParameters = ReportPath.CreateWithStandardParameters(reportPath, companyId, agencyId, includeDisabledReports, frequencyId, version);
            }
            else
            {
                reportPathWithStandardParameters = ReportPath.CreateWithStandardParameters(reportId, companyId, agencyId, includeDisabledReports, frequencyId, version);
            }

            var reportInfo = await this.reportRetrievalService.GetReportInfoForDesignAsync(userId, reportPathWithStandardParameters, CultureInfo.CurrentCulture, accessToken ?? string.Empty, cancellationToken);
            var report = reportInfo.Report;
            var reportSource = reportInfo.ReportSource;
            reportPathWithStandardParameters = ReportPath.CreateWithStandardParameters(reportInfo.ReportSource.ReportPath, companyId, agencyId, includeDisabledReports, frequencyId, version);

            var isBureauUser = userType == UserTypeCodes.Bureau;
            var fullReportPath = reportPathWithStandardParameters.OriginalString;
            var isSystemReport = ReportUtils.IsSystemReport(reportPathWithStandardParameters.ReportId);

            var viewModel = new ReportDesignerViewModel
            {
                ReportId = reportPathWithStandardParameters.ReportId,
                AccessToken = accessToken,
                AgencyId = agencyId,
                CompanyId = companyId,
                FrequencyId = frequencyId,
                CompanyName = await this.companyService.GetCompanyNameAsync(companyId),
                TemplateDisplayName = string.Empty,
                CompanyGroupId = companyGroupId,
                ReportPath = fullReportPath,
                ReportSource = reportSource,
                ReportDirectory = Path.GetDirectoryName(reportSource.ReportPath),
                ReportType = report.GetReportType(),
                ReportExists = !reportSource.IsTempReport(),
                Version = version,
                CanSetDesignableStatus = isBureauUser && !isSystemReport,
                CanDownloadReport = isBureauUser && !ReportUtils.IsOldCustomPayslip(reportPathWithStandardParameters.ReportId),
                ShouldRefreshAfterSave = !isBureauUser,
                ReportContextLevel = reportSource.ToReportContextLevel(),
                ShowReportContextLevel = isBureauUser,
                ShowReportHistory = !reportSource.IsBureauLevelReport() || (reportSource.IsBureauLevelReport() && isBureauUser),
                CanSetEnableStatus = isSystemReport
            };

            if (!reportSource.IsTempReport())
            {
                viewModel.ReportType = report.GetReportType();

                if (viewModel.ReportType == ReportType.Extract)
                {
                    var reportDataSource = report.GetReportDataSource();

                    TextExportOptionsBase exportOptions = reportDataSource.ExportFormat == "txt"
                        ? report.ExportOptions.Text
                        : report.ExportOptions.Csv;
                    viewModel.ExportSeparator = exportOptions.Separator;
                    viewModel.ExportQuoteStringsWithSeparators = exportOptions.QuoteStringsWithSeparators;
                }

                var (relatedEntityType, relatedEntities) = await this.GetRelatedEntitiesAsync(
                    reportPath,
                    reportSource,
                    agencyId,
                    taxCountryId,
                    companyGroupId,
                    companyId,
                    cancellationToken);

                viewModel.RelatedEntityType = relatedEntityType;
                viewModel.RelatedEntities = relatedEntities.OrderBy(entityDescription => entityDescription);

                this.SetMetaDataValues(report.Extensions, viewModel);
            }
            else
            {
                viewModel.IsEnabled = ReportConstants.Metadata.DefaultValues.IsEnabled;
                viewModel.IsDesignable = !isBureauUser;
            }

            viewModel.TemplateDisplayName = report.DisplayName;

            var parentReportInfo = await this.reportRetrievalService.GetEnabledParentReportInfoAsync(reportSource, companyId, userId, cancellationToken);
            viewModel.IsReportResettable = parentReportInfo is not null;

            var modelGenerator = new ReportDesignerClientSideModelGenerator(serviceProvider);
            viewModel.DesignerModel = await modelGenerator.GetModelAsync(report, null, ReportDesignerController.DefaultUri, WebDocumentViewerController.DefaultUri, null);
            viewModel.DesignerModel.ReportUrl = fullReportPath; // Have to set the path, otherwise the save dialog is shown.
            viewModel.DesignerModel.WizardSettings.EnableSqlDataSource = isBureauUser;
            viewModel.DesignerModel.WizardSettings.EnableObjectDataSource = false;

            return viewModel;
        }

        private void SetMetaDataValues(IDictionary<string, string> metadataValues, ReportDesignerViewModel viewModel)
        {
            if (metadataValues.TryGetValue(ReportConstants.Metadata.TemplateDisplayName, out var templateDisplayName) && !string.IsNullOrWhiteSpace(templateDisplayName))
            {
                viewModel.TemplateDisplayName = metadataValues.ContainsKey(ReportConstants.Tags.IsTemplateDisplayNameEncoded)
                    ? templateDisplayName.ConvertFromBase64()
                    : templateDisplayName;
            }

            if (metadataValues.TryGetValue(ReportConstants.Metadata.ReportCategory, out var reportCategoryValue) && int.TryParse(reportCategoryValue, out var reportCategoryId))
            {
                viewModel.ReportCategoryId = reportCategoryId;
            }

            if (metadataValues.TryGetValue(ReportConstants.Metadata.ReportSubCategory, out var reportSubCategoryValue)
                && int.TryParse(reportSubCategoryValue, out var reportSubCategoryId))
            {
                viewModel.ReportSubCategoryId = reportSubCategoryId;
            }

            if (metadataValues.TryGetValue(ReportConstants.Metadata.Description, out var description) && !string.IsNullOrWhiteSpace(description))
            {
                viewModel.Description = metadataValues.ContainsKey(ReportConstants.Tags.IsDescriptionEncoded)
                    ? description.ConvertFromBase64()
                    : description;
            }

            if (metadataValues.TryGetValue(ReportConstants.Metadata.ReportListId, out var reportListId) && !string.IsNullOrWhiteSpace(reportListId))
            {
                viewModel.ReportListId = long.Parse(reportListId);
            }

            viewModel.IsEnabled = ReportUtils.IsEnabled(metadataValues);
            viewModel.IsDesignable = ReportUtils.IsDesignable(metadataValues);
        }

        private async Task<(string RelatedEntityType, IList<string> RelatedEntities)> GetRelatedEntitiesAsync(
            string reportPath,
            ReportSource reportSource,
            long agencyId,
            int taxCountryId,
            long companyGroupId,
            long companyId,
            CancellationToken cancellationToken)
        {
            var relatedEntityIds = await this.GetRelatedEntityIds(reportPath, cancellationToken);

            if (reportSource.IsAgencyLevelReport())
            {
                return await this.GetRelatedEntitiesForAgencyAsync(relatedEntityIds, agencyId);
            }

            if (reportSource.IsAgencyTaxCountryLevelReport())
            {
                return await this.GetRelatedEntitiesForTaxCountryAsync(relatedEntityIds, taxCountryId);
            }

            if (reportSource.IsCompanyGroupLevelReport())
            {
                return await this.GetRelatedEntitiesForCompanyGroupAsync(relatedEntityIds, companyGroupId);
            }

            if (reportSource.IsCompanyLevelReport())
            {
                return await this.GetRelatedEntitiesForCompanyAsync(relatedEntityIds, companyId);
            }

            return (string.Empty, Array.Empty<string>());
        }

        private async Task<List<string>> GetRelatedEntityIds(string reportPath, CancellationToken cancellationToken)
        {
            var relatedEntityIds = new List<string>();
            var reportTags = (await this.reportStorageService.GetTagsForReportAsync(reportPath)) ?? new Dictionary<string, string>();

            if (!reportTags.TryGetValue(ReportConstants.Tags.InstanceId, out var instanceId))
            {
                return relatedEntityIds;
            }

            var queryTags = new Dictionary<string, string>() { { ReportConstants.Tags.InstanceId, instanceId } };

            var instanceTags = await this.reportStorageService.GetTagsByQueryAsync(queryTags, new[] { ReportConstants.Tags.RelatedEntityId }, cancellationToken);
            relatedEntityIds = instanceTags.Select(_ => _.Value.FirstOrDefault(_ => _.Key == ReportConstants.Tags.RelatedEntityId).Value).ToList();

            return relatedEntityIds;
        }

        private async Task<(string RelatedEntityType, List<string> RelatedEntities)> GetRelatedEntitiesForAgencyAsync(List<string> relatedEntityIds, long agencyId)
        {
            var convertedAgencyId = agencyId.ToString();

            // Ensure that the AgencyId for the current context is always there.
            if (!relatedEntityIds.Any(_ => _ == convertedAgencyId))
            {
                relatedEntityIds.Add(convertedAgencyId);
            }

            var relatedEntities = new List<string>();
            foreach (var relatedAgencyId in relatedEntityIds)
            {
                if (long.TryParse(relatedAgencyId, out var parsedId))
                {
                    relatedEntities.Add(await this.agencyService.GetNameAsync(parsedId));
                }
            }

            return (this.reportLocalizer.GetString("lblBusinessPartners"), relatedEntities);
        }

        private async Task<(string RelatedEntityType, List<string> RelatedEntities)> GetRelatedEntitiesForTaxCountryAsync(List<string> relatedEntityIds, int taxCountryId)
        {
            var convertedTaxCountryId = taxCountryId.ToString();

            // Ensure that the TaxCountryId for the current context is always there.
            if (!relatedEntityIds.Any(_ => _ == convertedTaxCountryId))
            {
                relatedEntityIds.Add(convertedTaxCountryId);
            }

            var relatedEntities = new List<string>();
            var taxCountries = await this.enumService.GetTaxCountriesAsync();
            foreach (var relatedCountryId in relatedEntityIds)
            {
                if (int.TryParse(relatedCountryId, out var parsedId))
                {
                    var countryDescription = taxCountries
                        .Where(_ => _.CountryId == parsedId)
                        .Select(_ => _.CountryDescription)
                        .FirstOrDefault();

                    relatedEntities.Add(countryDescription);
                }
            }

            return (this.reportLocalizer.GetString("lblTaxCountries"), relatedEntities);
        }

        private async Task<(string RelatedEntityType, List<string> RelatedEntities)> GetRelatedEntitiesForCompanyGroupAsync(List<string> relatedEntityIds, long companyGroupId)
        {
            var convertedCompanyGroupId = companyGroupId.ToString();

            // Ensure that the CompanyGroupId for the current context is always there.
            if (!relatedEntityIds.Any(_ => _ == convertedCompanyGroupId))
            {
                relatedEntityIds.Add(convertedCompanyGroupId);
            }

            var relatedEntities = new List<string>();
            foreach (var relatedCompanyGroupId in relatedEntityIds)
            {
                if (long.TryParse(relatedCompanyGroupId, out var parsedId))
                {
                    relatedEntities.Add(await this.companyService.GetGroupDescriptionAsync(parsedId));
                }
            }

            return (this.reportLocalizer.GetString("lblCompanyGroups"), relatedEntities);
        }

        private async Task<(string RelatedEntityType, List<string> RelatedEntities)> GetRelatedEntitiesForCompanyAsync(List<string> relatedEntityIds, long companyId)
        {
            var convertedCompanyId = companyId.ToString();

            // Ensure that the CompanyId for the current context is always there.
            if (!relatedEntityIds.Any(_ => _ == convertedCompanyId))
            {
                relatedEntityIds.Add(convertedCompanyId);
            }

            var relatedEntities = new List<string>();
            foreach (var relatedCompanyId in relatedEntityIds)
            {
                if (long.TryParse(relatedCompanyId, out var parsedId))
                {
                    relatedEntities.Add(await this.companyService.GetCompanyTradingNameAsync(parsedId));
                }
            }

            return (this.reportLocalizer.GetString("lblCompanies"), relatedEntities);
        }
    }
}