namespace PaySpace.Venuta.Reports.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Globalization;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Mvc;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Data.Models.Reports;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Message;
    using PaySpace.Venuta.Messaging.Notifications;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Reports.ViewModels;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Storage;

    [DisplayName(SystemAreas.Report.Area)]
    public class ClassicController : Controller
    {
        private readonly IReportHeaderService reportHeaderService;
        private readonly IReportParameterService reportParameterService;
        private readonly IEnumService enumService;
        private readonly IMessageBus messageBus;
        private readonly IStringLocalizer localizer;
        private readonly IEnumerable<ReportParameterStrategyBase> strategies;
        private readonly ICompanyService companyService;
        private readonly ICompanyFrequencyService companyFrequencyService;
        private readonly IUserService userService;
        private readonly IStringLocalizer reportLocalizer;
        private readonly ICompanyRunService companyRunService;
        private readonly IReportsMessageService reportsMessageService;

        public ClassicController(
            IReportHeaderService reportHeaderService,
            IReportParameterService reportParameterService,
            IEnumService enumService,
            IMessageBus messageBus,
            IStringLocalizer<ReportHeader> localizer,
            IEnumerable<ReportParameterStrategyBase> strategies,
            ICompanyService companyService,
            ICompanyFrequencyService companyFrequencyService,
            IUserService userService,
            IStringLocalizerFactory stringLocalizerFactory,
            ICompanyRunService companyRunService,
            IReportsMessageService reportsMessageService)
        {
            this.reportHeaderService = reportHeaderService;
            this.reportParameterService = reportParameterService;
            this.enumService = enumService;
            this.messageBus = messageBus;
            this.localizer = localizer;
            this.strategies = strategies;
            this.companyService = companyService;
            this.companyFrequencyService = companyFrequencyService;
            this.userService = userService;
            this.companyRunService = companyRunService;

            this.reportLocalizer = stringLocalizerFactory.Create(SystemAreas.Report.Area, null);
            this.reportsMessageService = reportsMessageService;
        }

        public async Task<ActionResult> Detail(long companyId, long frequencyId, long reportHeaderId, string? tenant)
        {
            // If this is a DevExpress Report, then set the ReportContext, and redirect to the DevExpress Viewer.
            var reportHeader = await this.reportHeaderService.GetByIdAsync(reportHeaderId);
            if (reportHeader.ReportTypeId == ReportConstants.DevExpressReportType)
            {
                return this.RedirectToAction("Index", "Viewer", new
                {
                    access_token = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken),
                    companyId,
                    frequencyId,
                    reportId = reportHeaderId,
                    tenant
                });
            }

            return this.View(await this.BuildViewModelAsync(
                this.User.GetUserId(),
                companyId,
                frequencyId,
                reportHeaderId,
                tenant));
        }

        public async Task<object> GetParameters(DataSourceLoadOptions loadOptions, long companyId, long frequencyId, long reportHeaderId, string paramName, long? parentId = default)
        {
            var parameterDetails = await this.reportParameterService.GetReportParameterDataAsync(this.User.GetUserId(), companyId, frequencyId, reportHeaderId, paramName, parentId);
            return DataSourceLoader.Load(parameterDetails, loadOptions);
        }

        public Task<ReportParameterResult> GetHiddenParameter(long companyId, long frequencyId, string paramName)
        {
            return this.reportParameterService.GetDefaultParameterDataAsync(this.User.GetAgencyId(), companyId, frequencyId, paramName);
        }

        public object GetChildParameters(long parentId)
        {
            return this.reportHeaderService.GetChildren(parentId);
        }

        public async Task<object> GetFormats(DataSourceLoadOptions loadOptions)
        {
            var reportOutputTypes = await this.enumService.GetReportOutputTypesAsync();
            return DataSourceLoader.Load(reportOutputTypes, loadOptions);
        }

        [HttpPost]
        public async Task GenerateReport(long companyId, long frequencyId, int formatId, long reportHeaderId, List<ReportParameterDetailViewModel> reportParameters, string? tenant)
        {
            var reportHeader = await this.reportHeaderService.GetByIdAsync(reportHeaderId);
            var reportName = this.reportLocalizer.GetString(reportHeader.ReportNameKey);

            var exportReportParameters = reportParameters.Select(_ => new ExportReportParameter
            {
                Id = _.ReportParameterId,
                Name = _.ReportParameterName,
                Value = _.ReportParameterValue
            }).ToArray();

            var notificationHash = ExportReportMessage.GetHash(exportReportParameters);
            await this.reportsMessageService.PublishNotificationMessage(this.User.GetUserId(), notificationHash, reportName);

            if (EvaluateComponentVarianceDateRange(reportHeader, exportReportParameters))
            {
                await this.messageBus.PublishMessageAsync(new NotificationStatusMessage
                {
                    UserId = this.User.GetUserId(),
                    StatusMessage = new ErrorMessage
                    {
                        Id = notificationHash,
                        Description = this.localizer.GetString(SystemAreas.Report.Keys.DateRange)
                    }
                });
            }
            else
            {
                await this.messageBus.PublishMessageAsync(new ExportSSRSReportMessage
                {
                    NotificationId = notificationHash,
                    UserId = this.User.GetUserId(),
                    CompanyId = companyId,
                    Tenant = tenant,
                    FormatId = formatId,
                    ReportHeaderId = reportHeaderId,
                    Parameters = exportReportParameters,
                    Metadata = await this.GetMetadataAsync(companyId, frequencyId, reportParameters),
                    ReportPriority = ReportPriority.Low
                });
            }
        }

        private async Task<ReportParameterViewModel> BuildViewModelAsync(long userId, long companyId, long frequencyId, long reportHeaderId, string? tenant)
        {
            var headerParamLinks = this.reportHeaderService.GetReportHeaderParamLinksByReportId(reportHeaderId);

            var reportParameters = await headerParamLinks.Select(_ => new ReportParameterDetailViewModel
            {
                ReportParamHeaderLinkId = _.ReportHeaderParamLinkId,
                ReportParameterId = _.ReportParamId,
                ReportParameterName = _.ReportParameterDetail.ParamName,
                ReportParameterFriendlyName = this.localizer.GetString(_.ReportParameterDetail.ParamNameKey).Value,
                ReportParameterFriendlyDescription = this.localizer.GetString(_.ReportParameterDetail.ParamDescriptionKey).Value,
                ReportParamType = _.ReportParameterDetail.ParamType,
                StrategyTypeEnum = StrategyTypeEnum.Setting,
                IsRequired = _.IsRequired.GetValueOrDefault(),
                ReportHeaderId = reportHeaderId,
                CanShow = !_.IsHidden,
                CompanyId = companyId,
                FrequencyId = frequencyId
            }).ToListAsync();

            // load strategy overrides
            foreach (var reportParameter in reportParameters)
            {
                var strategy = this.strategies.GetStrategy(reportParameter.ReportParameterName);
                if (strategy != null)
                {
                    var strategyParameter = new StrategyParameter
                    {
                        UserId = userId,
                        CompanyId = companyId,
                        FrequencyId = frequencyId,
                        ReportHeaderId = reportHeaderId
                    };

                    var defaultDisplayValue = await strategy.GetDefaultDisplayValueAsync(strategyParameter);

                    if (defaultDisplayValue != null)
                    {
                        reportParameter.DefaultDisplayValue = defaultDisplayValue;
                    }

                    reportParameter.CanShow = await strategy.GetVisibleAsync(strategyParameter);
                    reportParameter.IsReadOnly = strategy.IsReadOnly;
                    reportParameter.Placeholder = strategy.Placeholder;
                    reportParameter.Searchable = strategy.Searchable;
                    reportParameter.GroupByCategory = strategy.GroupByCategory;
                    reportParameter.GroupByCategoryDescendingOrder = strategy.GroupByCategoryDescendingOrder;
                    reportParameter.StrategyTypeEnum = strategy.StrategyType;
                }

                // load additional viewmodel data.
                reportParameter.ValueFilter = this.reportParameterService.GetValueFilter(companyId, frequencyId, reportParameter.ReportParameterName);
            }

            return await this.GetParentViewModelAsync(companyId, frequencyId, userId, reportHeaderId, reportParameters, tenant);
        }

        private async Task<ReportParameterViewModel> GetParentViewModelAsync(long companyId, long frequencyId, long userId, long reportHeaderId, List<ReportParameterDetailViewModel> reportParameters, string? tenant)
        {
            var reportHeader = await this.reportHeaderService.GetByIdAsync(reportHeaderId);

            return new ReportParameterViewModel
            {
                ReportHeaderId = reportHeaderId,
                UserId = userId,
                CompanyId = companyId,
                FrequencyId = frequencyId,
                Tenant = tenant,
                ReportParameterDetails = reportParameters,
                SubmitBtnFriendlyText = this.localizer.GetString("lblReportSubmitBtnText"),
                DefaultOutputType = reportHeader.DefaultOutputType
            };
        }

        private async Task<Dictionary<string, string>> GetMetadataAsync(long companyId, long frequencyId, List<ReportParameterDetailViewModel> reportParameters)
        {
            var metadata = new Dictionary<string, string>();
            var userFullName = await this.userService.GetUserNameByUserIdAsync(this.User.GetUserId());
            var companyName = await this.companyService.GetCompanyNameAsync(companyId);
            var frequency = await this.companyFrequencyService.GetNameAsync(frequencyId);
            var runFor = await this.GetRunForAsync(reportParameters);

            metadata.Add("UserId", this.User.GetUserId().ToString());
            metadata.Add("UserName", userFullName.FullName.RemoveNonASCIICharacters());
            metadata.Add("CompanyName", companyName.RemoveNonASCIICharacters());
            metadata.Add("Frequency", frequency.RemoveNonASCIICharacters());
            metadata.Add("RunFor", runFor.RemoveNonASCIICharacters());

            return metadata;
        }

        private async Task<string> GetRunForAsync(List<ReportParameterDetailViewModel> reportParameters)
        {
            var runId = reportParameters.Where(_ => _.ReportParameterName == "RunID").Select(_ => _.ReportParameterValue).FirstOrDefault();
            var periodCode = reportParameters.Where(_ => _.ReportParameterName == "PeriodCode").Select(_ => _.ReportParameterValue).FirstOrDefault();
            var startDate = reportParameters.Where(_ => (_.ReportParameterName == "RunStartDate" || _.ReportParameterName == "StartDate") && _.ReportParameterValue != null)
                                            .Select(_ => _.ReportParameterValue)
                                            .FirstOrDefault();
            var endDate = reportParameters.Where(_ => (_.ReportParameterName == "RunEndDate" || _.ReportParameterName == "EndDate") && _.ReportParameterValue != null).Select(_ => _.ReportParameterValue).FirstOrDefault();
            var effectiveDate = reportParameters.Where(_ => _.ReportParameterName == "EffectiveDate").Select(_ => _.ReportParameterValue).FirstOrDefault();

            if (!string.IsNullOrEmpty(runId))
            {
                return await this.companyRunService.GetRunDescriptionAsync(long.Parse(runId));
            }
            else if (!string.IsNullOrEmpty(periodCode))
            {
                var periodMonth = CultureInfo.CurrentUICulture.DateTimeFormat.GetMonthName(int.Parse(periodCode.Substring(4)));
                var periodYear = periodCode.Substring(0, 4);
                return periodMonth + "-" + periodYear;
            }
            else if (!string.IsNullOrEmpty(startDate))
            {
                return startDate + "-" + endDate;
            }
            else if (!string.IsNullOrEmpty(effectiveDate))
            {
                return effectiveDate;
            }

            return string.Empty;
        }

        private static bool EvaluateComponentVarianceDateRange(ReportHeader reportHeader, IEnumerable<ExportReportParameter> reportParameters)
        {
            // 48 and 49 are Component Varience Reports
            if (reportHeader.ReportHeaderId is 48 or 49)
            {
                var startDate = default(DateTime);
                var endDate = default(DateTime);

                foreach (var parameter in reportParameters)
                {
                    // 132 is EndDate and 179 is RunEndDate, when one has a value the other will not which is why we need to do the null check.
                    // This is dependant for which select of run, month or YTD is being run for.
                    if (!string.IsNullOrEmpty(parameter.Value) && (parameter.Id is 132 or 179))
                    {
                        endDate = DateTime.Parse(parameter.Value, CultureInfo.InvariantCulture);
                    }

                    // 133 is StartDate and 178 is RunStartDate, when one has a value the other will not which is why we need to do the null check.
                    // This is dependant for which select of run, month or YTD is being run for.
                    if (!string.IsNullOrEmpty(parameter.Value) && (parameter.Id is 133 or 178))
                    {
                        startDate = DateTime.Parse(parameter.Value, CultureInfo.InvariantCulture);
                    }
                }

                if (endDate > startDate.AddMonths(13))
                {
                    return true;
                }
            }

            return false;
        }
    }
}