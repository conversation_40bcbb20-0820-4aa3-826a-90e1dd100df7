namespace PaySpace.Venuta.Reports.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Data.ResponseModel;
    using DevExtreme.AspNet.Mvc;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Reports.ViewModels;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Storage;

    public class DesignerApiController : ControllerBase
    {
        private readonly IReportVersionService reportVersionService;
        private readonly IUserService userService;

        public DesignerApiController(IReportVersionService reportVersionService, IUserService userService)
        {
            this.reportVersionService = reportVersionService;
            this.userService = userService;
        }

        [HttpGet]
        public async Task<object> GetVersions(DataSourceLoadOptions loadOptions, ReportPath reportUrl, string? reportId)
        {
            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
            var versions = this.reportVersionService.ListAllVersions(reportUrl.Path)
                .Select((version, index) => new
                {
                    text = DateTime.Parse(version.VersionId).ToString("yyyy-MM-dd HH:mm:ss"),
                    versionId = version.VersionId,
                    url = this.Url.Action("Index", "Designer", new { access_token = accessToken, companyId = reportUrl.CompanyId, frequencyId = reportUrl.FrequencyId, reportId, v = index == 0 ? string.Empty : version.VersionId })
                });

            return DataSourceLoader.Load(versions, loadOptions);
        }

        [HttpGet]
        public async Task<LoadResult> GetAudit(DataSourceLoadOptions loadOptions, ReportPath reportUrl)
        {
            var versions = this.reportVersionService.ListAllVersions(reportUrl.Path);

            var audit = new List<ReportAuditViewModel>();
            foreach (var version in versions)
            {
                var blobHasUserId = version.Metadata.TryGetValue("userId", out string userId);
                var userFullName = "Undefined";
                if (blobHasUserId && long.TryParse(userId, out var id) && id != 0)
                {
                    var user = await this.userService.GetUserNameByUserIdAsync(id);
                    userFullName = user.FullName;
                }

                var decodedMetadata = AzureAttachmentService.ConvertDictionaryValuesFromBase64(version.Metadata);
                var blobHasDescription = decodedMetadata.TryGetValue("changesmadedescription", out var decodedDescription);

                audit.Add(new ReportAuditViewModel
                {
                    User = userFullName,
                    Created = version.Properties.CreatedOn.Value.DateTime,
                    Description = blobHasDescription ? decodedDescription : string.Empty
                });
            }

            return DataSourceLoader.Load(audit, loadOptions);
        }
    }
}