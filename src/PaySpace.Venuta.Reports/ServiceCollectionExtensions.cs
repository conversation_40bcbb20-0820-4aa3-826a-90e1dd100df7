namespace PaySpace.Venuta.Reports
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using FluentValidation;

    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Options;

    using PaySpace.Venuta.Reports.Services;
    using PaySpace.Venuta.Reports.ViewModelBuilder;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Services.Reports.Extensions;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.Services;
    using PaySpace.Venuta.Services.Reports.Validation;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddReportApplicationServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IReportSavingService, ReportSavingService>();
            services.AddScoped<IReportParameterHelper, ReportParameterHelper>();
            services.AddScoped<IReportDefaultParameterService, ReportDefaultParameterService>();

            services.AddHttpClient("ApiClient", (sp, client) =>
            {
                var clientSettings = sp.GetRequiredService<IOptions<ClientSettings>>().Value;
                client.BaseAddress = new Uri(clientSettings.ApiUrl, "odata/v1.1/");
                client.Timeout = TimeSpan.FromMinutes(30);
            });

            services.AddHttpClient("ReportsClient", (sp, client) =>
            {
                client.BaseAddress = new Uri(configuration.GetReportApiUrl(), "odata/");
                client.Timeout = TimeSpan.FromMinutes(30);
            });

            return services;
        }

        public static IServiceCollection AddViewModelBuilders(this IServiceCollection services)
        {
            services.AddScoped<IReportViewerViewModelBuilder, ReportViewerViewModelBuilder>();
            services.AddScoped<IReportDesignerViewModelBuilder, ReportDesignerViewModelBuilder>();
            services.AddScoped<IDataSourceViewModelBuilder, DataSourceViewModelBuilder>();
            services.AddScoped<IDataSourceSettingsViewModelBuilder, DataSourceSettingsViewModelBuilder>();
            services.AddScoped<IValidator<ReportSaveContext>, ReportSaveContextValidator>();

            return services;
        }

        public static IServiceCollection AddReportValidators(this IServiceCollection services)
        {
            foreach (var type in GetValidatorTypes())
            {
                foreach (var interfaceType in type.GetInterfaces())
                {
                    services.AddTransient(interfaceType, type);
                }
            }

            return services;
        }

        private static IEnumerable<Type> GetValidatorTypes()
        {
            return typeof(ServiceCollectionExtensions).Assembly.GetTypes()
                .Union(typeof(Venuta.Services.Reports.ServiceCollectionExtensions).Assembly.GetTypes())
                .Where(t => !t.IsAbstract && t.IsPublic && typeof(IValidator).IsAssignableFrom(t) && !t.ContainsGenericParameters);
        }
    }
}