<?xml version="1.0" encoding="utf-8"?>
<Dashboard>
  <Title Visible="false" Text="Pacey Dashboard" IncludeMasterFilterState="false" />
  <DataSources>
    <ObjectDataSource Name="UserEntity" ComponentName="objectDataSource1">
      <CalculatedFields>
        <CalculatedField Name="Unblock Constant" Expression="iif ( [IsBlocked] And [AuthenticationAttempts] &gt;= 3, 'Unblock (Exceeded authentication attempts)', iif ( [IsBlocked] And [UnauthenticatedMessages] &gt; 10, 'Unblock (Exceeded max unauthenticated messages)', iif ( [IsBlocked] And [SentInactiveMessage], 'Unblock (User inactive)', iif ( [IsBlocked] And [SentUserNotFoundMessage], 'Unblock (User cell not found)', iif ( [IsBlocked] And [SentCompanyBillingModuleNotFoundMessage], 'Unblock (Company does not have billing module)', '')))))" />
        <CalculatedField Name="SanitisedNumber" Expression="Replace( [RowKey] ,'+', '%2B') " DataType="Auto" />
        <CalculatedField Name="UnblockQueryParams" Expression="?functionUrl + '/api/unblock?code=' + ?functionKey + '&amp;number=' + [SanitisedNumber]" DataType="Auto" />
        <CalculatedField Name="IsBlocked" Expression="[AuthenticationAttempts] &gt;= 3 || [SentCompanyBillingModuleNotFoundMessage]  || [SentInactiveMessage]  || [SentUserNotFoundMessage]  || [UnauthenticatedMessages]  &gt; 10" DataType="Boolean" />
        <CalculatedField Name="RegisteredBefore" Expression="[RegistrationDate] &lt; ?period" DataType="Auto" />
      </CalculatedFields>
    </ObjectDataSource>
    <ObjectDataSource Name="UserMonthlyStatistics" ComponentName="objectDataSource2" />
  </DataSources>
  <Parameters>
    <Parameter Name="companyId" Type="System.Int64" AllowNull="true" Visible="false" />
    <Parameter Name="period" Type="System.DateTime" AllowNull="true" Visible="false" />
    <Parameter Name="EmployeeSearch" Visible="false" />
    <Parameter Name="functionUrl" Visible="false" />
    <Parameter Name="functionKey" Visible="false" />
    <Parameter Name="accessToken" Visible="false" />
  </Parameters>
  <Items>
    <Grid ComponentName="gridDashboardItem1" Name="Registered Employees" ParentContainer="RegisteredUsers" DataSource="objectDataSource1" VisibleDataFilterString="iif(?EmployeeSearch = ?, True, Contains(?EmployeeSearch, [DataItem2]))">
      <InteractivityOptions IgnoreMasterFilters="true" />
      <DataItems>
        <Dimension DataMember="Number" DefaultId="DataItem2" />
        <Dimension DataMember="RegistrationDate" DateTimeGroupInterval="DateHourMinute" DefaultId="DataItem3" />
        <Measure DataMember="IsBlocked" SummaryType="Min" DefaultId="DataItem12" />
        <Measure DataMember="AuthenticationAttempts" DefaultId="DataItem4" />
        <Measure DataMember="UnauthenticatedMessages" DefaultId="DataItem5" />
        <Measure DataMember="SentCompanyBillingModuleNotFoundMessage" SummaryType="Count" DefaultId="DataItem6" />
        <Measure DataMember="SentInactiveMessage" SummaryType="Count" DefaultId="DataItem7" />
        <Measure DataMember="SentUserNotFoundMessage" SummaryType="Count" DefaultId="DataItem8" />
        <Dimension DataMember="Unblock Constant" DefaultId="DataItem0" />
        <Measure DataMember="UnblockQueryParams" SummaryType="Min" DefaultId="DataItem1" />
        <Dimension DataMember="ConversationsCount" DefaultId="DataItem9" />
        <Dimension DataMember="FullName" DefaultId="DataItem10" />
        <Dimension DataMember="EmployeeNumber" DefaultId="DataItem11" />
      </DataItems>
      <HiddenMeasures>
        <Measure DefaultId="DataItem12" />
        <Measure DefaultId="DataItem4" />
        <Measure DefaultId="DataItem5" />
        <Measure DefaultId="DataItem6" />
        <Measure DefaultId="DataItem7" />
        <Measure DefaultId="DataItem8" />
      </HiddenMeasures>
      <FormatRules>
        <GridItemFormatRule Name="Format Rule 1" ApplyToRow="true" DataItemApplyTo="DataItem2">
          <FormatConditionExpression Expression="[DataItem12] = True">
            <AppearanceSettings AppearanceType="PaleRed" />
          </FormatConditionExpression>
        </GridItemFormatRule>
      </FormatRules>
      <GridColumns>
        <GridDimensionColumn Weight="27.998878923766817">
          <Dimension DefaultId="DataItem2" />
        </GridDimensionColumn>
        <GridDimensionColumn Name="Registration date" Weight="95.09529147982063">
          <Dimension DefaultId="DataItem3" />
        </GridDimensionColumn>
        <GridDimensionColumn Name="Employee Full Name" Weight="100.09529147982063">
          <Dimension DefaultId="DataItem10" />
        </GridDimensionColumn>
        <GridDimensionColumn Name="Employee Number" Weight="100.09529147982063">
          <Dimension DefaultId="DataItem11" />
        </GridDimensionColumn>
        <GridDimensionColumn Name="Conversation count">
          <Dimension DefaultId="DataItem9" />
        </GridDimensionColumn>
        <GridHyperlinkColumn Name="Unblock" Weight="101.90582959641256" UriPattern="{0}">
          <DisplayValue DefaultId="DataItem0" />
          <UriAttribute DefaultId="DataItem1" />
        </GridHyperlinkColumn>
      </GridColumns>
      <GridOptions EnableBandedRows="true" ColumnWidthMode="Manual" />
    </Grid>
    <Chart ComponentName="chartDashboardItem1" Name="Chart 1" ShowCaption="false" ParentContainer="dashboardTabPage2" DataSource="objectDataSource1" VisibleDataFilterString="iif(?period = ?, True, [DataItem2] Between(?period, #2022-04-06#))">
      <InteractivityOptions IgnoreMasterFilters="true" />
      <DataItems>
        <Measure DataMember="Number" SummaryType="Count" DefaultId="DataItem0">
          <Calculation>
            <RunningTotal />
          </Calculation>
          <WindowDefinition>
            <ChartWindowDefinition />
          </WindowDefinition>
        </Measure>
        <Dimension DataMember="RegistrationDate" DateTimeGroupInterval="DayMonthYear" DefaultId="DataItem1" />
        <Dimension DataMember="RegistrationDate" DateTimeGroupInterval="None" DefaultId="DataItem2" />
      </DataItems>
      <HiddenDimensions>
        <Dimension DefaultId="DataItem2" />
      </HiddenDimensions>
      <Arguments>
        <Argument DefaultId="DataItem1" />
      </Arguments>
      <Panes>
        <Pane Name="Pane 1">
          <AxisY Title="Number of registrations" />
          <Series>
            <Simple Name="Number of registrations" SeriesType="Line">
              <Value DefaultId="DataItem0" />
            </Simple>
          </Series>
        </Pane>
      </Panes>
      <ChartLegend Visible="false" />
      <AxisX TitleVisible="true" Title="Registration date" />
    </Chart>
    <Chart ComponentName="chartDashboardItem2" Name="Menu item clicks" ShowCaption="false" ParentContainer="dashboardTabPage1" DataSource="objectDataSource2">
      <InteractivityOptions IgnoreMasterFilters="true" />
      <DataItems>
        <Measure DataMember="ViewPayslipsClickCount" DefaultId="DataItem0" />
        <Measure DataMember="ApplyForLeaveClickCount" DefaultId="DataItem1" />
        <Measure DataMember="ApproveInboxItemClickCount" DefaultId="DataItem2" />
        <Measure DataMember="SubmitClaimClickCounts" DefaultId="DataItem3" />
        <Measure DataMember="ViewLeaveBalanceClickCounts" DefaultId="DataItem4" />
        <Measure DataMember="ViewPersonalDetailsClickCount" DefaultId="DataItem5" />
        <Measure DataMember="ViewTaxCertificatesClickCount" DefaultId="DataItem6" />
      </DataItems>
      <Panes>
        <Pane Name="Pane 1">
          <AxisY Title="Click count" />
          <Series>
            <Simple Name="View payslips">
              <Value DefaultId="DataItem0" />
              <PointLabelOptions ShowForZeroValues="true" />
            </Simple>
            <Simple Name="Apply for leave">
              <Value DefaultId="DataItem1" />
            </Simple>
            <Simple Name="Approve inbox items">
              <Value DefaultId="DataItem2" />
            </Simple>
            <Simple Name="Submit claim">
              <Value DefaultId="DataItem3" />
            </Simple>
            <Simple Name="View leave balances">
              <Value DefaultId="DataItem4" />
            </Simple>
            <Simple Name="View personal details">
              <Value DefaultId="DataItem5" />
            </Simple>
            <Simple Name="View tax certificates">
              <Value DefaultId="DataItem6" />
            </Simple>
          </Series>
        </Pane>
      </Panes>
      <ChartLegend InsidePosition="TopLeftHorizontal" OutsidePosition="BottomCenterHorizontal" />
      <AxisX Visible="false" TitleVisible="true" Title="Menu items" VisiblePointsCount="13" />
    </Chart>
    <Card ComponentName="cardDashboardItem2" Name="" ShowCaption="false" DataSource="objectDataSource1">
      <InteractivityOptions IgnoreMasterFilters="true" />
      <DataItems>
        <Measure DataMember="RowKey" SummaryType="Count" DefaultId="DataItem0">
          <NumericFormat FormatType="Number" Precision="0" Unit="Ones" IncludeGroupSeparator="true" />
        </Measure>
      </DataItems>
      <Card Name="Registered employees">
        <ActualValue DefaultId="DataItem0" />
        <AbsoluteVariationNumericFormat />
        <PercentVariationNumericFormat />
        <PercentOfTargetNumericFormat />
        <LayoutTemplate MinWidth="100" Type="Lightweight">
          <MainValue Visible="true" ValueType="ActualValue" DimensionIndex="0" />
          <SubValue Visible="true" ValueType="Title" DimensionIndex="0" />
          <BottomValue Visible="true" ValueType="Subtitle" DimensionIndex="0" />
          <DeltaIndicator Visible="false" />
          <Sparkline Visible="false" />
        </LayoutTemplate>
      </Card>
    </Card>
    <Card ComponentName="cardDashboardItem3" Name="Cards 3" ShowCaption="false" DataSource="objectDataSource2">
      <InteractivityOptions IgnoreMasterFilters="true" />
      <DataItems>
        <Measure DataMember="RowKey" SummaryType="Count" DefaultId="DataItem0" />
      </DataItems>
      <Card Name="Active employees in selected month">
        <ActualValue DefaultId="DataItem0" />
        <AbsoluteVariationNumericFormat />
        <PercentVariationNumericFormat />
        <PercentOfTargetNumericFormat />
        <LayoutTemplate MinWidth="100" Type="Lightweight">
          <MainValue Visible="true" ValueType="ActualValue" DimensionIndex="0" />
          <SubValue Visible="true" ValueType="Title" DimensionIndex="0" />
          <BottomValue Visible="true" ValueType="Subtitle" DimensionIndex="0" />
          <DeltaIndicator Visible="false" />
          <Sparkline Visible="false" />
        </LayoutTemplate>
      </Card>
    </Card>
    <DateFilter ComponentName="monthFilter" Name="Calendar" DataSource="objectDataSource1" FilterType="Exact" DatePickerLocation="Near">
      <DataItems>
        <Dimension DataMember="RegistrationDate" Name="Filter by month..." DateTimeGroupInterval="MonthYear" DefaultId="DataItem0" />
      </DataItems>
      <Dimension DefaultId="DataItem0" />
    </DateFilter>
    <TabContainer ComponentName="tabContainer" Name="Tab Container 1">
      <Pages>
        <Page ComponentName="dashboardTabPage1" Name="Menu option usage" ShowItemAsTabPage="false">
          <InteractivityOptions IgnoreMasterFilters="true" />
        </Page>
        <Page ComponentName="dashboardTabPage2" Name="Employee registrations per month" ShowItemAsTabPage="false">
          <InteractivityOptions IgnoreMasterFilters="true" IsMasterFilter="false" />
        </Page>
        <Page ComponentName="RegisteredUsers" Name="Registered users">
          <InteractivityOptions IgnoreMasterFilters="true" IsMasterFilter="false" />
        </Page>
      </Pages>
    </TabContainer>
    <ComboBox ComponentName="comboBoxEmployeeSearch" Name="Search for a cell number" ParentContainer="RegisteredUsers" DataSource="objectDataSource1" EnableSearch="true" ComboBoxType="Checked">
      <InteractivityOptions IgnoreMasterFilters="false" />
      <DataItems>
        <Dimension DataMember="RowKey" DefaultId="DataItem0" />
        <Measure DataMember="Number" SummaryType="Count" DefaultId="DataItem1" />
      </DataItems>
      <HiddenMeasures>
        <Measure DefaultId="DataItem1" />
      </HiddenMeasures>
      <FilterDimensions>
        <Dimension DefaultId="DataItem0" />
      </FilterDimensions>
    </ComboBox>
  </Items>
  <LayoutOptions>
    <Width Value="1200" />
    <Height Mode="Fixed" Value="550" />
  </LayoutOptions>
  <LayoutTree>
    <LayoutGroup>
      <LayoutGroup Orientation="Vertical">
        <LayoutGroup Weight="0.****************">
          <LayoutItem DashboardItem="monthFilter" Weight="1.1704699027494394" />
          <LayoutItem DashboardItem="cardDashboardItem2" Weight="1.0611943240639015" />
          <LayoutItem DashboardItem="cardDashboardItem3" Weight="1.1272101519254483" />
        </LayoutGroup>
        <LayoutTabContainer DashboardItem="tabContainer" Weight="1.****************">
          <LayoutTabPage DashboardItem="dashboardTabPage1">
            <LayoutItem DashboardItem="chartDashboardItem2" />
          </LayoutTabPage>
          <LayoutTabPage Orientation="Vertical" DashboardItem="dashboardTabPage2">
            <LayoutGroup Weight="0.598495458599321">
              <LayoutItem DashboardItem="chartDashboardItem1" />
            </LayoutGroup>
          </LayoutTabPage>
          <LayoutTabPage Orientation="Vertical" DashboardItem="RegisteredUsers">
            <LayoutGroup>
              <LayoutItem DashboardItem="comboBoxEmployeeSearch" Weight="0.5075034106412005" />
              <LayoutItem DashboardItem="gridDashboardItem1" Weight="1.****************" />
            </LayoutGroup>
          </LayoutTabPage>
        </LayoutTabContainer>
      </LayoutGroup>
    </LayoutGroup>
  </LayoutTree>
</Dashboard>