{"ConnectionStrings": {"DefaultConnection": "Data Source=devops.payspace.com;Initial Catalog=PaySpaceDevelopment;Integrated Security=True;TrustServerCertificate=True;"}, "Identity": {"Authority": "https://uat-identity.payspace.com/", "ClientId": "nextgen", "ClientSecret": "f7442f2a-bd04-4bf1-8b31-f1f1f2182e02"}, "ElasticSearch": {"Urls": ["http://localhost:9200"]}, "RedisSettings": {"Instance": "NextGen_Dev:", "DefaultConnection": "localhost:6379"}, "AzureConnections": {"ServiceBusConnection": "", "StorageConnection": "DefaultEndpointsProtocol=https;AccountName=websiteattachmentsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "CosmosConnection": "AccountEndpoint=https://localhost:8081/;AccountKey=****************************************************************************************;"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}