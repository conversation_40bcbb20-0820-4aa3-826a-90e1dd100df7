@using DevExpress.Drawing
@using PaySpace.Venuta.Services.Reports

@model ReportDesignerViewModel
@{
    ViewBag.Title = Model.TemplateDisplayName;
}

<link href="~/css/reports-viewer.min.css" rel="stylesheet" asp-append-version="true" />
<link href="~/css/reports-designer.min.css" rel="stylesheet" asp-append-version="true" />
<link href="~/css/designer.css" rel="stylesheet" asp-append-version="true" />

@if (Model.ReportType == ReportType.Extract)
{
    /* Hide the export options when extract*/
    <style>
        div.dx-editor:has(div#dxrd-accordion-trigger-Export_Options) {
            display: none;
        }
    </style>
}

<style>
    /*Remove when federated DS feature goes to production*/
    #btn-add-dataSource {
        display: none;
    }
</style>
<script>
    DevExpress.localization.loadMessages({
        "@CultureInfo.CurrentCulture.Parent.Name": {
            "reports-unsavedchanges": "@Localizer["lblReportSaveChanges"]",
            "reports-validation-company": "@Localizer["lblCompanyRequired"]"
        }
    });

    Designer = {
        Settings: {
            OriginalReportContextLevel: @Json.Serialize(Model.ReportContextLevel),
            ReportType: @Json.Serialize(Model.ReportType),
            AgencyId: @Model.AgencyId,
            CompanyGroupId: @Model.CompanyGroupId,
            CompanyId: @Model.CompanyId,
            FrequencyId: @Model.FrequencyId,
            ReportDirectory: @Json.Serialize(Model.ReportDirectory),
            ReportId: @Json.Serialize(Model.ReportId),
            ReportListId: @Json.Serialize(Model.ReportListId),
            IsCustomReport: @Json.Serialize(Model.IsCustomReport),
            CurrentReportPath: @Json.Serialize(Model.ReportPath),
            ResetReportUri: "@Html.Raw(Url.Action("Reset", "Designer", new { Model.CompanyId, Model.FrequencyId, Model.ReportId, Model.ReportContextLevel, access_token = Model.AccessToken }))",
            DeleteReportUri: "@Html.Raw(Url.Action("Delete", "Designer", new { Model.CompanyId, Model.FrequencyId, Model.ReportId, Model.ReportContextLevel, access_token = Model.AccessToken }))",
            EditDataSourceUri: "@Html.Raw(Url.Action("Edit", "DataSource", new { Model.CompanyId, Model.FrequencyId, Model.ReportId, access_token = Model.AccessToken }))",
            ReportDesignerUri: '@Html.Raw(Url.Action("Index", "Designer", new { Model.CompanyId, Model.FrequencyId, access_token = Model.AccessToken }))',
            DownloadReportUri: '@Html.Raw(Url.Action("DownloadReport", "Designer", new { reportPath = Model.ReportSource.ReportPath, access_token = Model.AccessToken }))',
            FontRepository: @Json.Serialize(DXFontRepository.Instance.GetFonts().Select(font => font.Name)),
            IsReportResettable: @Json.Serialize(Model.IsReportResettable),
            CanDownloadReport: @Json.Serialize(Model.CanDownloadReport),
            ShouldRefreshAfterSave: @Json.Serialize(Model.ShouldRefreshAfterSave)
        },
        DefaultHeaders: {
            Authorization: "Bearer @Model.AccessToken",
            Agencyid: @Model.AgencyId,
            Companyid: @Model.CompanyId,
            Frequencyid: @Model.FrequencyId,
            Dir: @Json.Serialize(Model.ReportDirectory)
        }
    };
</script>

<script src="~/bundles/reports-viewer.min.js" asp-append-version="true"></script>
<script src="~/bundles/reports-designer.min.js" asp-append-version="true"></script>
<script src="~/js/designer.js" asp-append-version="true"></script>

<script type="text/html" id="dxrd-svg-toolbox-payspace_venuta_services_reports_xrcompanylogopicturebox">
    <svg version="1.1" height="0.75em" viewBox="0 0 448 512">
        <path class="dxd-icon-fill" d="M128 148v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12zm140 12h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm-128 96h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm128 0h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm-76 84v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm76 12h40c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12zm180 124v36H0v-36c0-6.6 5.4-12 12-12h19.5V24c0-13.3 10.7-24 24-24h337c13.3 0 24 10.7 24 24v440H436c6.6 0 12 5.4 12 12zM79.5 463H192v-67c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v67h112.5V49L80 48l-.5 415z"/>
    </svg>
</script>

<script type="text/html" id="dxrd-svg-toolbox-payspace_venuta_services_reports_xragencylogopicturebox">
    <svg version="1.1" viewBox="0 0 122 113" style="enable-background:new 0 0 122 113" xml:space="preserve">
        <style type="text/css">.st0 { fill-rule: evenodd; clip-rule: evenodd;}</style>
        <path class="st0 dxd-icon-fill" d="M0,100.07h14.72V1.57c0-0.86,0.71-1.57,1.57-1.57h49.86c0.86,0,1.57,0.71,1.57,1.57V38.5h44.12 c0.86,0,1.57,0.71,1.57,1.57v59.99h9.47v12.99H0V100.07L0,100.07z M27.32,14.82h10.2c0.31,0,0.57,0.26,0.57,0.57v12.36 c0,0.31-0.26,0.57-0.57,0.57h-10.2c-0.31,0-0.57-0.26-0.57-0.57V15.39C26.75,15.08,27.01,14.82,27.32,14.82L27.32,14.82z M44.6,76.3h10.2c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57H44.6c-0.31,0-0.57-0.26-0.57-0.57V76.87 C44.03,76.55,44.29,76.3,44.6,76.3L44.6,76.3z M27.32,76.3h10.2c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57 h-10.2c-0.31,0-0.57-0.26-0.57-0.57V76.87C26.75,76.55,27.01,76.3,27.32,76.3L27.32,76.3z M44.6,55.8h10.2 c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57H44.6c-0.31,0-0.57-0.26-0.57-0.57V56.38 C44.03,56.06,44.29,55.8,44.6,55.8L44.6,55.8z M27.32,55.8h10.2c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57 h-10.2c-0.31,0-0.57-0.26-0.57-0.57V56.38C26.75,56.06,27.01,55.8,27.32,55.8L27.32,55.8z M44.6,35.31h10.2 c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57H44.6c-0.31,0-0.57-0.26-0.57-0.57V35.88 C44.03,35.57,44.29,35.31,44.6,35.31L44.6,35.31z M27.32,35.31h10.2c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57 h-10.2c-0.31,0-0.57-0.26-0.57-0.57V35.88C26.75,35.57,27.01,35.31,27.32,35.31L27.32,35.31z M44.6,14.82h10.2 c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57H44.6c-0.31,0-0.57-0.26-0.57-0.57V15.39 C44.03,15.08,44.29,14.82,44.6,14.82L44.6,14.82z M23.17,7.32h35.92c0.62,0,1.13,0.61,1.13,1.35v85.87c0,0.74-0.51,1.35-1.13,1.35 H23.17c-0.62,0-1.13-0.61-1.13-1.35V8.67C22.04,7.93,22.55,7.32,23.17,7.32L23.17,7.32z M72.61,53.43h10.2 c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57h-10.2c-0.31,0-0.57-0.26-0.57-0.57V54 C72.04,53.69,72.3,53.43,72.61,53.43L72.61,53.43z M89.89,76.3h10.2c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57 h-10.2c-0.31,0-0.57-0.26-0.57-0.57V76.87C89.32,76.55,89.58,76.3,89.89,76.3L89.89,76.3z M72.61,76.3h10.2 c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57h-10.2c-0.31,0-0.57-0.26-0.57-0.57V76.87 C72.04,76.55,72.3,76.3,72.61,76.3L72.61,76.3z M89.89,53.43h10.2c0.31,0,0.57,0.26,0.57,0.57v12.36c0,0.31-0.26,0.57-0.57,0.57 h-10.2c-0.31,0-0.57-0.26-0.57-0.57V54C89.32,53.69,89.58,53.43,89.89,53.43L89.89,53.43z M68.86,45.82h35.92 c0.62,0,1.13,0.61,1.13,1.35v47.37c0,0.74-0.51,1.35-1.13,1.35H68.86c-0.62,0-1.13-0.61-1.13-1.35V47.17 C67.73,46.43,68.24,45.82,68.86,45.82L68.86,45.82z"/>
    </svg>
</script>

@await Html.PartialAsync("_NavBar")

<script>
    Designer.App.SetHeaders();

    function CustomizeLocalization(_, e) {
        const language = '@CultureInfo.CurrentCulture.Parent.Name';
        if (language != "en") {
            e.LoadMessages($.get('@Url.Content("/localization/dx-analytics-core." + CultureInfo.CurrentCulture.Parent.Name + ".json")'));
            e.LoadMessages($.get('@Url.Content("/localization/dx-reporting." + CultureInfo.CurrentCulture.Parent.Name + ".json")'));
        }
    }

    function CustomizeToolbox(s, e) {
        function customizeToolbox(e, shortTypeName, fullTypeName, inheritClass, displayName) {
            const controlsFactory = e.ControlsFactory;
            controlsFactory.registerControl(
                shortTypeName,
                controlsFactory.inheritControl(inheritClass, {
                    defaultVal: {
                        "@@ControlType": fullTypeName
                    },
                    displayName
                }));
        }

        customizeToolbox(e, '@typeof(XRAgencyLogoPictureBox).FullName', '@typeof(XRAgencyLogoPictureBox).AssemblyQualifiedName', "XRPictureBox", "Agency Logo");
        customizeToolbox(e, '@typeof(XRCompanyLogoPictureBox).FullName', '@typeof(XRCompanyLogoPictureBox).AssemblyQualifiedName', "XRPictureBox", "Company Logo");
    }
</script>

<modal id="report-audit-modal" data-clear-on-close="true">
    <modal-header>
        <modal-title title="@Localizer.GetString("lblReportAuditHeader")" />
        <modal-close-button />
    </modal-header>
    <modal-body />
</modal>

@await Html.PartialAsync("_SaveForm")

@{
    var designer = Html.DevExpress().ReportDesigner("ReportDesigner")
        .Height("calc(100vh - 67px")
        .ClientSideModelSettings(x => x.IncludeLocalization = false)
        .ClientSideEvents(x =>
        {
            x.CustomizeLocalization("CustomizeLocalization");
            x.CustomizeMenuActions("Designer.App.CustomizeMenuActions");
            x.BeforeRender("Designer.App.BeforeRender");
            x.ReportSaved("Designer.App.OnReportSaved");
            x.TabChanged("Designer.App.OnReportTabChanged");
            x.CustomizeToolbox("CustomizeToolbox");
        })
        .Bind(Model.DesignerModel);
}

@designer.RenderHtml()

@section scripts {
    @designer.RenderScripts()
}