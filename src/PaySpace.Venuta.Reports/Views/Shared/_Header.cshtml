<environment names="Production">
    <!--Application Insights-->
    @Html.Raw(JavaScriptSnippet.FullScript)
    <script>
        if (window.appInsights) {
            appInsights.setAuthenticatedUserContext("@User.GetUserId()");
            appInsights.trackPageView();
        }
    </script>
</environment>

<link href="~/bundles/site.min.css" rel="stylesheet" asp-append-version="true" />
<script src="~/bundles/site.min.js" asp-append-version="true"></script>

<auth-session access-token="@await Context.GetTokenAsync(OpenIdConnectParameterNames.AccessToken)" />

<script>
    DevExpress.localization.locale("@CultureInfo.CurrentUICulture.Name");
</script>