@model ReportViewerViewModel
@{
	ViewBag.Title = Model.TemplateDisplayName;
}

<link href="~/css/reports-viewer.min.css" rel="stylesheet" asp-append-version="true" />
<script src="~/bundles/reports-viewer.min.js" asp-append-version="true"></script>

@section scripts {
	<style>
        html, body {
            height: 100%;
        }

        .dxrd-preview-parameter-actions-container {
            padding: 0.25rem;
        }

        .dxrd-preview.dxrd-designer-wrapper .dxrd-preview-parameters-wrapper .dx-fieldset {
            margin: 0;
        }

		.dx-designer .dxrv-daterange-editor .dx-state-readonly.dx-widget {
			border: none;
		}

        .dx-designer .dxrv-daterange-editor .dx-editor-outlined .dx-texteditor-input {
            color: rgba(0,0,0,.87);
        }

		body {
			background-color: transparent;
		}
		.dx-designer .dxrv-daterange-editor-popup-wrapper .dxrv-daterange-editor-popup-container .dxrv-daterange-editor-list{
			display:none;
		}
		.dx-designer .dx-designer-viewport .dxrv-daterange-editor-popup-wrapper .dx-popup-normal .dx-popup-content .dx-scrollable-content .dxrv-daterange-editor-popup-container{
			width: auto;
			margin: 20px;
		}
        .dx-designer-viewport .dxd-back-accented{
            background-color: rgba(0, 0, 0, 0.12);
        }
	</style>
}
@await Html.PartialAsync("_Parameters")
<script>
	DevExpress.Analytics.Utils.ajaxSetup.ajaxSettings = {
		headers: {
			"Authorization": "Bearer @Model.AccessToken"
		}
	};

	function BeforeRender(s, e) {
        e.parametersModel.showButtons = true;
        e.parametersModel.buttons.push({
            text: 'Submit',
            onClick: async (args) => {
                if (args.validationGroup) {
                    const validationResult = args.validationGroup.validate();
                    if (validationResult.isValid) {
                        const parameters = e.parametersModel.serializeParameters();

                        const runOrMonth = parameters.find(_ => _.Key === "RunOrMonth");
                        if (runOrMonth) {
                            const paramResetName = runOrMonth.Value === "PeriodCode" ? "RunId" : "PeriodCode";
                            const paramToReset = parameters.find(_ => _.Key === paramResetName);
                            if (paramToReset) {
                                paramToReset.Value = null;
                            }
                        }

                        const format = parameters.find(_ => _.Key === "Format");
                        return $.post({
                            url: "@Url.Action("ExportReport", "Viewer")",
                            contentType: "application/json",
                            data: JSON.stringify({
                                Tenant: "@Model.Tenant",
                                ReportUrl: "@Html.Raw(Model.ViewerModel.ReportInfo.ReportUrl)",
                                ReportName: "@Html.Raw(Model.TemplateDisplayName)",
                                Format: format.Value,
                                Parameters: parameters
                            })
                        });
                    }
                }
            }
        });

		$(window).on('beforeunload', function () {
			s.Close();
		});
	}

	function CustomizeLocalization(s, e) {
		const language = '@CultureInfo.CurrentCulture.Parent.Name';
		if (language != "en") {
			e.LoadMessages($.get('@Url.Content("/localization/dx-analytics-core." + CultureInfo.CurrentCulture.Parent.Name + ".json")'));
			e.LoadMessages($.get('@Url.Content("/localization/dx-reporting." + CultureInfo.CurrentCulture.Parent.Name + ".json")'));
		}
	}

	function CustomizeParameterEditors(_, e) {
		const tags = e.parameter.tag?.split(",") ?? [];

		if (e.info.editor.header === "dx-text") {
			// This is needed for parameters callback. Otherwise the value only commits on blur; requiring clicking on submit twice.
			e.info.editor.extendedOptions = $.extend({}, e.info.editor.extendedOptions || {}, {
				valueChangeEvent: "input"
			});
		}

		if (tags.includes("Required") && e.parameter.visible === true) {
			e.info.validationRules = [{ type: 'required' }];
		}

		if ((e.info.editor.header === "dxrd-multivalue" || e.info.editor.header === "dx-selectbox") && e.parameter.hasLookUpValues) {
			e.info.editor = $.extend({}, e.info.editor);
			e.info.editor.extendedOptions = $.extend(e.info.editor.extendedOptions || {}, { showDropDownButton: true });
		}

		if (tags.includes("Grouped") || tags.includes("GroupByCompany")) {
			e.info.editor = $.extend({}, e.info.editor);
			e.info.editor.extendedOptions = $.extend({}, e.info.editor.extendedOptions || {}, {
				grouped: true,
			});
		}

		if (e.parameter.name === "RunId") {
			e.info.editor = $.extend({}, e.info.editor);
			e.info.editor.extendedOptions = $.extend({}, e.info.editor.extendedOptions || {}, {
				groupTemplate: function groupTemplate(data) {
					return data.items[0].Month;
				}
			});
		}

		if (e.parameter.name === "OrgUnitId") {
			e.info.editor = { header: "OrgUnits" };
		}

		if (e.parameter.name === "PeriodCode") {
			e.info.validationRules = [{ type: 'required' }];
		}

		if (e.parameter.name === "RunId") {
			e.info.validationRules = [{ type: 'required' }];
		}

		if (e.parameter.name === "PositionIds") {
			e.info.editor = { header: "Positions" };
		}
	}

	function CustomizeParameterLookUpSource(s, e) {
		const tags = e.parameter.tag?.split(",") ?? [];

		if (e.parameter.name === "RunId" && tags.includes("Grouped")) {
			GroupParameter(e, "PeriodCode");
		}

		if (tags.includes("GroupByCompany")) {
			GroupParameter(e, "CompanyName");
		}
	}

	function ParametersInitialized(s, e) {
		const selectedCompanyIds = ko.observable();

		if (@((int)Model.ReportViewType) == @((int)ReportViewType.CompanyGroup)) {
			const companyIdsParam = e.ActualParametersInfo.find(_ => _.parameterDescriptor.name == "CompanyIds");
			if (companyIdsParam) {
				if (companyIdsParam.value) {
					selectedCompanyIds(companyIdsParam.value.join(','));
				}

				companyIdsParam.events.on("propertyChanged", args => {
					if (args.propertyName === 'value') {
						selectedCompanyIds(args.newValue?.join(','));
					}
				});
			}
		} else {
			const companyIdParam = e.ActualParametersInfo.find(_ => _.parameterDescriptor.name == "CompanyId");
			if (companyIdParam) {
				if (companyIdParam.value) {
					selectedCompanyIds(companyIdParam.value);
				}

				companyIdParam.events.on("propertyChanged", args => {
					if (args.propertyName === 'value') {
						selectedCompanyIds(args.newValue);
					}
				});
			}
		}

		const orgUnitIdParam = e.ParametersModel['OrgUnitId'];
		if (orgUnitIdParam) {
			const reqs = ko.observableArray([]);

			selectedCompanyIds.subscribe(() => {
				orgUnitIdParam.value = [];
			});

			orgUnitIdParam.isLoading = ko.computed(() => reqs().length > 0);
			orgUnitIdParam.dropDownButtonTemplate = ko.computed(() => {
				if (orgUnitIdParam.isLoading()) {
					return $('<div />').dxLoadIndicator({ visible: true, elementAttr: { class: "dx-dropdowneditor-button" }, height: "100%" });
				}
				return "dropDownButton";
			});
			orgUnitIdParam.dataSource = ko.computed(() => {
				const innerStore = DevExpress.data.AspNet.createStore({
					key: "organizationGroupId",
					loadUrl: '@Url.Action("GetOrgUnits", "Viewer")',
					loadParams: {
						companyIds: selectedCompanyIds()
					},
					onBeforeSend(operation, ajaxSettings) {
						ajaxSettings.method = "POST";
						ajaxSettings.headers = {
							"X-Http-Method-Override": "GET"
						};
					}
				});

				return new DevExpress.data.DataSource({
					store: new DevExpress.data.CustomStore({
						key: innerStore.key(),
						load(loadOptions) {
							reqs.push(loadOptions);
							return innerStore.load(loadOptions).then(res => {
								reqs.pop();
								return res;
							});
						},
						byKey(key) {
							reqs.push(key);
							return innerStore.byKey(key).then(res => {
								reqs.pop();
								return res;
							});
						}
					})
				});
			});
		}

		const positionsParam = e.ParametersModel['PositionIds'];
		if (positionsParam) {
			selectedCompanyIds.subscribe(() => {
				positionsParam.value = [];
			});

			positionsParam.dataSource = ko.computed(() => {
				return new DevExpress.data.DataSource({
					store: DevExpress.data.AspNet.createStore({
						key: "value",
						loadUrl: '@Url.Action("GetPositions", "Viewer")',
						loadParams: {
							companyIds: selectedCompanyIds()
						},
						onBeforeSend(operation, ajaxSettings) {
							ajaxSettings.method = "POST";
							ajaxSettings.headers = {
								"X-Http-Method-Override": "GET"
							};
						}
					})
				})
			});
		}

		const frequencyIdParam = e.ActualParametersInfo.find(_ => _.parameterDescriptor.name == "FrequencyId");
		const runIdParam = e.ActualParametersInfo.find(_ => _.parameterDescriptor.name == "RunId");

		if (frequencyIdParam) {
			frequencyIdParam.events.on("propertyChanged", args => {
				if (args.propertyName === 'value') {
					if (runIdParam) {
						runIdParam.value = '';
					}
				}
			});
		}

		if (runIdParam) {
			const values = runIdParam.lookUpValues;
			if (values.length > 0) {
				runIdParam.value = values[0].value;
			}
		}

		const periodCodeParam = e.ActualParametersInfo.find(_ => _.parameterDescriptor.name == "PeriodCode");
		if (periodCodeParam) {
			const values = periodCodeParam.lookUpValues;
			if (values.length > 0) {
				periodCodeParam.value = values[0].value;
			}
		}
	}

	function GroupParameter(lookUpSource, groupName) {
		if (lookUpSource.dataSource == null && Array.isArray(lookUpSource.items)) {
			lookUpSource.items.forEach(dataItem => {
				// replace tab character before parsing to prevent parsing errors
				const itemDisplayValue = JSON.parse(dataItem.displayValue.replace(/\t/g, ''));
				Object.assign(dataItem, itemDisplayValue);
			});

			lookUpSource.dataSource = new DevExpress.data.DataSource({
				store: new DevExpress.data.ArrayStore({
					data: lookUpSource.items,
					key: "value"
				}),
				group: { selector: groupName, desc: true }
			});
		}
	}

	function ParametersReset() {
		window.location.reload();
	}
</script>

@(Html.DevExpress().ReportParametersPanel().Height("100%").ClientSideEvents(x =>
{
    x.ParametersReset("ParametersReset");
    x.CustomizeLocalization("CustomizeLocalization");
    x.CustomizeParameterEditors("CustomizeParameterEditors");
    x.CustomizeParameterLookUpSource("CustomizeParameterLookUpSource");
    x.ParametersInitialized("ParametersInitialized");
    x.BeforeRender("BeforeRender");
}).Bind(Model.ViewerModel))