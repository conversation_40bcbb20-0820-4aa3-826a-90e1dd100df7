@using PaySpace.Venuta.Services.Reports.Enums

@inject IStringLocalizerFactory StringLocalizerFactory
@model ReportParameterViewModel
@{
	var notificationsLocalizer = StringLocalizerFactory.Create(SystemAreas.Notification.Area, null);
	var reportsLocalizer = StringLocalizerFactory.Create(SystemAreas.Report.Area, null);
}

<style>
	body, html {
		background-color: transparent;
		height: 100%;
	}

	.dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-label {
		width: 40%;
		white-space: normal;
	}

    /* Fixes a Firefox specific bug - we need to force the width to be 100% of the parent container */
    .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-label .dx-field-item-label-content{
        width: 100% !important;
    }

	.dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content, .dx-layout-manager .dx-label-h-align.dx-flex-layout .dx-field-item-content-wrapper {
		flex: 1 0 0%;
	}
</style>

<script>
	ReportParameters = {
		Settings: {
			BearerToken: 'Bearer @await Context.GetTokenAsync("access_token")',
			GetHiddenParameterUrl: '@Url.Action("GetHiddenParameter", "Classic")',
			GetChildParameterUrl: '@Url.Action("GetChildParameters", "Classic")',
            GenerateReportUrl: '@Html.Raw(Url.Action("GenerateReport", "Classic", new { Model.CompanyId, Model.FrequencyId, Model.ReportHeaderId, Model.Tenant }))',
			ErrorMessage: '@notificationsLocalizer.GetString(SystemAreas.Notification.Keys.GenericFailure)'
		}
	}
</script>
<script src="~/js/report-parameters.js" asp-append-version="true"></script>

<form id="reportForm" asp-action="GenerateReport" class="h-100">
	<div class="d-flex flex-column h-100">
		<div class="flex-grow-1 overflow-auto">
			<div class="card border-0 mb-3">
				<div class="card-body pb-2">
					@await Html.PartialAsync("Controls/_FormatControl")
				</div>
			</div>
			<div class="card border-0 mb-3">
				<div class="card-body">
					@{
						var settings = Model.ReportParameterDetails.Where(_ => _.StrategyTypeEnum == StrategyTypeEnum.Setting);
						if (settings.Any())
						{
							<h6 class="card-title">@reportsLocalizer.GetString(SystemAreas.Report.Keys.Settings)</h6>
							foreach (var setting in settings)
							{
								Model.ReportParameterDetail = setting;
								@await Html.PartialAsync("Parameter")
								Model.ParameterIndex++;
							}
						}
					}
				</div>
			</div>
			<div class="card border-0 mb-3">
				<div class="card-body">
					@{
						var filters = Model.ReportParameterDetails.Where(_ => _.StrategyTypeEnum == StrategyTypeEnum.Filter);
						if (filters.Any())
						{
							<h6 class="card-title">@reportsLocalizer.GetString(SystemAreas.Report.Keys.Filters)</h6>
							foreach (var filter in filters)
							{
								Model.ReportParameterDetail = filter;
								@await Html.PartialAsync("Parameter")
								Model.ParameterIndex++;
							}
						}

						var additionals = Model.ReportParameterDetails.Where(_ => _.StrategyTypeEnum == StrategyTypeEnum.Additional);
						if (additionals.Any())
						{
							<h6>@reportsLocalizer.GetString(SystemAreas.Report.Keys.Additional)</h6>
							foreach (var additional in additionals)
							{
								Model.ReportParameterDetail = additional;
								@await Html.PartialAsync("Parameter")
								Model.ParameterIndex++;
							}
						}
					}
				</div>
			</div>
		</div>
		<div class="text-end mt-2 pb-1">
			@(Html.DevExtreme().Button()
						.ID("btn-submit")
						.ValidationGroup("ReportParameterValidationGroup")
						.Text(Model.SubmitBtnFriendlyText)
						.OnClick("ReportParameters.App.OnSubmitClick"))
		</div>
	</div>

</form>
