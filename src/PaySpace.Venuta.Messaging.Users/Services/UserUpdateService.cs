namespace PaySpace.Venuta.Messaging.Users.Services
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;

    using MapsterMapper;

    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Venuta.Messages;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Users.Models;

    public interface IUserUpdateService
    {
        Task<long> AddOrUpdateAsync(long userId, object user, string region = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Only call this method when an employee is deleted. This will ensure that the correct user is removed from all regions
        /// Make sure that the userId belonged to the employee that is in the same region
        /// </summary>
        /// <param name="userId">User id that belongs to the employee</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<bool> DeleteAsync(long userId, CancellationToken cancellationToken = default);
    }

    public class UserUpdateService : IUserUpdateService
    {
        private readonly IMessageBus bus;
        private readonly IMapper mapper;
        private readonly IConfiguration configuration;

        private static readonly HostRegion DefaultRegion = HostRegion.SAN;

        public UserUpdateService(IServiceProvider serviceProvider, IMapper mapper, IConfiguration configuration)
        {
            this.mapper = mapper;
            this.configuration = configuration;

            this.bus = serviceProvider.GetKeyedService<IMessageBus>(DefaultRegion);
        }

        public async Task<long> AddOrUpdateAsync(long userId, object user, string region = null, CancellationToken cancellationToken = default)
        {
            var userDto = this.mapper.Map<UserDto>(user);
            region ??= this.configuration["AppConfiguration:Region"] ?? DefaultRegion.ToString().ToLower();

            var updateRequest = new UserUpdateRequestMessage
            {
                User = userDto,
                UpdateType = userId == default ? UserUpdateType.Add : UserUpdateType.Update,
                Region = region
            };

            var result = await this.bus.RequestAsync<UserUpdateRequestMessage, UserUpdatedMessage>(updateRequest, cancellationToken);
            if (!result!.Success)
            {
                throw new InvalidOperationException("Error updating user");
            }

            return result!.UserId;
        }

        // Only call this method when an employee is deleted. This will ensure that the correct user is removed from all regions
        // Make sure that the userId belonged to the employee that is in the same region
        public async Task<bool> DeleteAsync(long userId, CancellationToken cancellationToken = default)
        {
            var region = this.configuration["AppConfiguration:Region"] ?? DefaultRegion.ToString().ToLower();

            var updateRequest = new UserUpdateRequestMessage
            {
                UserId = userId,
                UpdateType = UserUpdateType.Delete,
                Region = region
            };

            var result = await this.bus.RequestAsync<UserUpdateRequestMessage, UserUpdatedMessage>(updateRequest, cancellationToken);

            return result!.Success;
        }
    }
}