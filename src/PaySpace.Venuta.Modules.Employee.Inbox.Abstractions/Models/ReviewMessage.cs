namespace PaySpace.Venuta.Data.Models.Employees.Inbox
{
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Security.Claims;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Routing;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Infrastructure;

    public class ReviewMessage : UserMessage
    {
        private readonly DbContext context;

        public ReviewMessage(DbContext context)
            : base(context)
        {
            this.context = context;
        }

        [NotMapped]
        public string Icon { get; } = "fas fa-chart-line";

        [Column("fkPrimaryKeyID")]
        public long ReviewId { get; set; }

        public virtual EmployeeReviewHeader Review { get; set; }

        public override string NavigateUrl
        {
            get
            {
                var httpContextAccessor = this.context.GetService<IHttpContextAccessor>();

                var companyId = httpContextAccessor.HttpContext.User.GetCompanyId(throwOnEmpty: true);
                var employeeId = httpContextAccessor.HttpContext.User.GetEmployeeId(throwOnEmpty: true);
                var frequencyId = httpContextAccessor.HttpContext.User.GetFrequencyId(throwOnEmpty: true);

                var linkGenerator = this.context.GetService<LinkGenerator>();
                return linkGenerator.GetPathByAction(httpContextAccessor.HttpContext, "Index", "EmployeeEvaluations", new { area = "Employees", companyId, employeeId, frequencyId, EmployeeReviewHeaderId = this.ReviewId });
            }
        }

        [NotMapped]
        public override string SubjectDescription
        {
            get
            {
                return this.Review?.EmployeeReviewTemplate?.CompanyReviewProcess == null
                    ? string.Empty
                    : this.Review.EmployeeReviewTemplate.CompanyReviewProcess.Title;
            }
        }
    }
}