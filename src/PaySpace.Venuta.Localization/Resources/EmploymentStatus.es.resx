<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="0011" xml:space="preserve">
    <value>EL Motivo de la Baja es un campo obligatorio al seleccionar Baja.</value>
  </data>
  <data name="0019" xml:space="preserve">
    <value>La fecha de contratación debe ser posterior a la fecha de ingreso al grupo.</value>
  </data>
  <data name="0021" xml:space="preserve">
    <value>La fecha de ingreso al grupo no puede ser anterior a ningún registro existente.</value>
  </data>
  <data name="0049" xml:space="preserve">
    <value>Se requiere el número de referencia fiscal.</value>
  </data>
  <data name="0051" xml:space="preserve">
    <value>El país de emisión del pasaporte debe estar completo</value>
  </data>
  <data name="CompanyRunId" xml:space="preserve">
    <value>Ciclo del cálculo de nómina</value>
  </data>
  <data name="EmploymentDate" xml:space="preserve">
    <value>Fecha de Alta</value>
  </data>
  <data name="errGroupJoinDate" xml:space="preserve">
    <value>La fecha de ingreso al grupo no puede ser posterior a la fecha de vigencia del empleado</value>
  </data>
  <data name="GroupJoinDate" xml:space="preserve">
    <value>Fecha de adhesión al grupo</value>
  </data>
  <data name="IdentityType" xml:space="preserve">
    <value>Tipo de identificación</value>
  </data>
  <data name="IdentityTypeId" xml:space="preserve">
    <value>Tipo de Identificación</value>
  </data>
  <data name="IdNumber" xml:space="preserve">
    <value>Número de Identidad</value>
  </data>
  <data name="lblCompanyRun" xml:space="preserve">
    <value>Ciclo del cálculo de nómina</value>
  </data>
  <data name="lblEmploymentStatusDeleted" xml:space="preserve">
    <value>El Perfil Fiscal se ha borrado con éxito</value>
  </data>
  <data name="lblEmploymentStatusSaved" xml:space="preserve">
    <value>El Perfil Fiscal se ha guardado con éxito</value>
  </data>
  <data name="lblFullName" xml:space="preserve">
    <value>Nombre Completo</value>
  </data>
  <data name="lblIdNumber" xml:space="preserve">
    <value>Número de Identidad</value>
  </data>
  <data name="lblInvalidPassportNumber" xml:space="preserve">
    <value>El número de pasaporte debe tener al menos {0} caracteres de longitud.</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Perfil Fiscal</value>
  </data>
  <data name="lblPassportExpiryBeforeIssueDate" xml:space="preserve">
    <value>La caducidad del pasaporte no puede ser anterior a la fecha de emisión del pasaporte.</value>
  </data>
  <data name="lblPassportLengthError" xml:space="preserve">
    <value>El número debe tener al menos 7 caracteres</value>
  </data>
  <data name="lblTaxStatusRequired" xml:space="preserve">
    <value>El País FIscal es obligatorio.</value>
  </data>
  <data name="lblTermination" xml:space="preserve">
    <value>Dar de baja empleado</value>
  </data>
  <data name="NatureOfPerson" xml:space="preserve">
    <value>Naturaleza de la persona</value>
  </data>
  <data name="NatureOfPersonId" xml:space="preserve">
    <value>Naturaleza de la Persona</value>
  </data>
  <data name="NotReEmployable" xml:space="preserve">
    <value>No re-contratable</value>
  </data>
  <data name="PassportCountryId" xml:space="preserve">
    <value>País de emisión del pasaporte</value>
  </data>
  <data name="PassportExpiry" xml:space="preserve">
    <value>Caducidad del Pasaporte</value>
  </data>
  <data name="PassportIssued" xml:space="preserve">
    <value>Fecha de expedición del pasaporte</value>
  </data>
  <data name="PassportNumber" xml:space="preserve">
    <value>Número de Pasaporte</value>
  </data>
  <data name="ReferenceNumber" xml:space="preserve">
    <value>Número de Referencia</value>
  </data>
  <data name="TaxReferenceNumber" xml:space="preserve">
    <value>Número de referencia fiscal</value>
  </data>
  <data name="TaxStatus" xml:space="preserve">
    <value>Estatus Fiscal</value>
  </data>
  <data name="TaxStatusId" xml:space="preserve">
    <value>País Fiscal</value>
  </data>
  <data name="TerminationReasonId" xml:space="preserve">
    <value>Motivo de la Baja</value>
  </data>
</root>