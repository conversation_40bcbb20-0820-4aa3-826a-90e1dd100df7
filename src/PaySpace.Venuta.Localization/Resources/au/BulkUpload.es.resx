<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BulkUploadHistory" xml:space="preserve">
    <value>Historial de Cargas Masivas</value>
  </data>
  <data name="Dependants" xml:space="preserve">
    <value>Dependientes</value>
  </data>
  <data name="DuplicateEmployeeNumbers" xml:space="preserve">
    <value>Duplicar número de Empleado '{0}' en fila {1}</value>
  </data>
  <data name="InvalidEmployee" xml:space="preserve">
    <value>Número de Empleado no válido '{0}'.</value>
  </data>
  <data name="InvalidEmployeeNumber" xml:space="preserve">
    <value>Número de Empleado no válido '{0}'</value>
  </data>
  <data name="lblArea" xml:space="preserve">
    <value>Área</value>
  </data>
  <data name="lblEmployeeEtiTakeOnWarning5" xml:space="preserve">
    <value>La fecha de contratación es un campo obligatorio si el empleado tiene múltiples registros fiscales. Esta fecha se utilizará para asignar la información inicial a un registro fiscal específico.</value>
  </data>
  <data name="lblFormCaption" xml:space="preserve">
    <value>Datos</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Cargas Masivas</value>
  </data>
  <data name="lblPayslipRun" xml:space="preserve">
    <value>CIclo de la Nómina</value>
  </data>
  <data name="SheetName_Employee" xml:space="preserve">
    <value>Perfil Básico</value>
  </data>
  <data name="SheetName_EmployeePayRate" xml:space="preserve">
    <value>Datos Tarifa Salarial</value>
  </data>
  <data name="SheetName_EmployeeSkill" xml:space="preserve">
    <value>Habilidades del Empleado</value>
  </data>
  <data name="SheetName_EmployeeTakeOn" xml:space="preserve">
    <value>Acumulado del Año hasta la Fecha</value>
  </data>
</root>