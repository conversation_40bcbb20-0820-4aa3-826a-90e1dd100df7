<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="FullName" xml:space="preserve">
    <value>Nombre Completo</value>
  </data>
  <data name="grpAdditional" xml:space="preserve">
    <value>Datos adicionales</value>
  </data>
  <data name="grpPayrateDetails" xml:space="preserve">
    <value>Datos de la tarifa de pago actual</value>
  </data>
  <data name="HoursPerSemiMonth" xml:space="preserve">
    <value>Horas por ciclo</value>
  </data>
  <data name="IsAnnual" xml:space="preserve">
    <value>Es Anual</value>
  </data>
  <data name="lblAddSuccessful" xml:space="preserve">
    <value>Nueva tarifa de pago guardada correctamente</value>
  </data>
  <data name="lblAdvisedAmountIncrease" xml:space="preserve">
    <value>Capturar la cuantía del paquete</value>
  </data>
  <data name="lblAnnualPackage" xml:space="preserve">
    <value>Paquete anual</value>
  </data>
  <data name="lblAutoPay" xml:space="preserve">
    <value>Pago automático del paquete salarial</value>
  </data>
  <data name="lblCategory" xml:space="preserve">
    <value>Categoría</value>
  </data>
  <data name="lblComments" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="lblDailyRate" xml:space="preserve">
    <value>Tarifa diaria</value>
  </data>
  <data name="lblDaysPerPeriod" xml:space="preserve">
    <value>Días por periodo</value>
  </data>
  <data name="lblDelete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="lblDeleteSuccessful" xml:space="preserve">
    <value>La cuantía se ha borrado satisfactoriamente</value>
  </data>
  <data name="lblDetail" xml:space="preserve">
    <value>Datos del pago actual</value>
  </data>
  <data name="lblDuplicateRecord" xml:space="preserve">
    <value>Todos los registros deben tener una fecha de vigencia única</value>
  </data>
  <data name="lblEdit" xml:space="preserve">
    <value>Editar/Ver</value>
  </data>
  <data name="lblEditSuccessful" xml:space="preserve">
    <value>Los cambios se han guardado satisfactoriamente</value>
  </data>
  <data name="lblEffectiveDate" xml:space="preserve">
    <value>Fecha de Alta</value>
  </data>
  <data name="lblEffectiveDateCannotBeBeforeGroupJoinDate" xml:space="preserve">
    <value>La fecha de ingreso al grupo no coincide</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Número de Empleado</value>
  </data>
  <data name="lblFrequency" xml:space="preserve">
    <value>Frecuencia de pago</value>
  </data>
  <data name="lblFriday" xml:space="preserve">
    <value>Viernes</value>
  </data>
  <data name="lblHasClosedRun" xml:space="preserve">
    <value>Los datos creados en antiguos periodos no se pueden editar</value>
  </data>
  <data name="lblHistory" xml:space="preserve">
    <value>Historial</value>
  </data>
  <data name="lblHourlyRate" xml:space="preserve">
    <value>Tarifa por Hora</value>
  </data>
  <data name="lblHoursPerDay" xml:space="preserve">
    <value>Horas al día</value>
  </data>
  <data name="lblHoursPerDayInvalid" xml:space="preserve">
    <value>Las horas por día no pueden ser 0 o menos y no pueden ser más de 24</value>
  </data>
  <data name="lblHoursPerFortnight" xml:space="preserve">
    <value>Horas por quincena</value>
  </data>
  <data name="lblHoursPerMonth" xml:space="preserve">
    <value>Horas al mes</value>
  </data>
  <data name="lblHoursPerWeek" xml:space="preserve">
    <value>Horas a la semana</value>
  </data>
  <data name="lblIncreasePercent" xml:space="preserve">
    <value>Aumento en base a la tarifa anterior</value>
  </data>
  <data name="lblMonday" xml:space="preserve">
    <value>Lunes</value>
  </data>
  <data name="lblMonthlyPackage" xml:space="preserve">
    <value>Paquete mensual</value>
  </data>
  <data name="lblMonthlyRate" xml:space="preserve">
    <value>Tarifa mensual</value>
  </data>
  <data name="lblNew" xml:space="preserve">
    <value>Nuevo</value>
  </data>
  <data name="lblNoWorkDaysSelected" xml:space="preserve">
    <value>Por favor, selecciona como mínimo un día de la semana</value>
  </data>
  <data name="lblPackage" xml:space="preserve">
    <value>Paquete salarial</value>
  </data>
  <data name="lblPackage2" xml:space="preserve">
    <value>Segundo paquete salarial</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Datos Tarifa Salarial</value>
  </data>
  <data name="lblPercentageIncrease" xml:space="preserve">
    <value>Incremento en (%) del paquete anterior</value>
  </data>
  <data name="lblPreviousPackage" xml:space="preserve">
    <value>Paquete Previo</value>
  </data>
  <data name="lblProvinceOfEmployment" xml:space="preserve">
    <value>Provincia de Empleo</value>
  </data>
  <data name="lblReason" xml:space="preserve">
    <value>Motivo del aumento</value>
  </data>
  <data name="lblRetro" xml:space="preserve">
    <value>Fecha de vigencia del cálculo retrospectivo</value>
  </data>
  <data name="lblSaturday" xml:space="preserve">
    <value>Sábado</value>
  </data>
  <data name="lblSunday" xml:space="preserve">
    <value>Domingo</value>
  </data>
  <data name="lblThirteenCheque" xml:space="preserve">
    <value>Décimotercera Paga</value>
  </data>
  <data name="lblThursday" xml:space="preserve">
    <value>Jueves</value>
  </data>
  <data name="lblTuesday" xml:space="preserve">
    <value>Martes</value>
  </data>
  <data name="lblWednesday" xml:space="preserve">
    <value>Miércoles</value>
  </data>
  <data name="lblWeeklyRate" xml:space="preserve">
    <value>Tarifa Semanal</value>
  </data>
  <data name="lblWeeksPerMonth" xml:space="preserve">
    <value>Semanas al mes</value>
  </data>
  <data name="lblWorkReasonRequired" xml:space="preserve">
    <value>Se requiere el motivo del aumento</value>
  </data>
  <data name="SemiMonthlyRate" xml:space="preserve">
    <value>Tarifa semimensual</value>
  </data>
  <data name="tabAddtional" xml:space="preserve">
    <value>Adicional</value>
  </data>
  <data name="tabPayrateDetails" xml:space="preserve">
    <value>Datos</value>
  </data>
</root>