<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accrual" xml:space="preserve">
    <value>Acumulado</value>
  </data>
  <data name="ddlRunMonth" xml:space="preserve">
    <value>Mes</value>
  </data>
  <data name="DueToExpire" xml:space="preserve">
    <value>Fecha de Expiración</value>
  </data>
  <data name="EffectiveDate" xml:space="preserve">
    <value>Fehca Efectiva</value>
  </data>
  <data name="HeaderTextFamRes" xml:space="preserve">
    <value>Permiso de responsabilidad familiar</value>
  </data>
  <data name="HeaderTextStudy" xml:space="preserve">
    <value>Permiso de Estudio</value>
  </data>
  <data name="lblDays" xml:space="preserve">
    <value>días</value>
  </data>
  <data name="lblDaysPerPeriod" xml:space="preserve">
    <value>Días por Periodo</value>
  </data>
  <data name="lblDescription" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="lblHours" xml:space="preserve">
    <value>horas</value>
  </data>
  <data name="lblHoursPerPeriod" xml:space="preserve">
    <value>Horas por Periodo</value>
  </data>
  <data name="lblLeaveBalance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="lblLeaveDays" xml:space="preserve">
    <value>Días de Permiso</value>
  </data>
  <data name="lblLeaveHours" xml:space="preserve">
    <value>Horas de Permiso</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="lblRun" xml:space="preserve">
    <value>Cálculo</value>
  </data>
  <data name="lblStartDate" xml:space="preserve">
    <value>Fecha de Inicio</value>
  </data>
  <data name="lblTotal" xml:space="preserve">
    <value>Media de la Línea</value>
  </data>
  <data name="lblTotalAmount" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="LeaveBalance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="MaxBalance" xml:space="preserve">
    <value>Saldo máximo</value>
  </data>
  <data name="SchemeName" xml:space="preserve">
    <value>Nombre del Esquema</value>
  </data>
  <data name="YearsService" xml:space="preserve">
    <value>Antigüedad</value>
  </data>
</root>