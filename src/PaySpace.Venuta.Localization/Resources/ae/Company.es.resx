<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Address" xml:space="preserve">
    <value>Dirección</value>
  </data>
  <data name="AdvancedPositionManagement" xml:space="preserve">
    <value>¿Quieres utilizar el Módulo de Estructura Jerárquica Avanzada? (si no, se utilizará la versión simplificada)</value>
  </data>
  <data name="AgencyId" xml:space="preserve">
    <value>Linked Business Partner</value>
  </data>
  <data name="BusinessPartnerSettings" xml:space="preserve">
    <value>Configuraciones del Socio de Negocios</value>
  </data>
  <data name="CalculationMethod" xml:space="preserve">
    <value>Método de Cálculo</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ciudad</value>
  </data>
  <data name="CityOrTown" xml:space="preserve">
    <value>Ciudad</value>
  </data>
  <data name="CityOrTownRequired" xml:space="preserve">
    <value>Se requiere insertar la Ciudad</value>
  </data>
  <data name="CloudAnalytics" xml:space="preserve">
    <value>¿Añadir esta empresa (+ las otras compañías del grupo) a Cloud Analytics?</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Código</value>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Comentarios Inactivos</value>
  </data>
  <data name="CommissionEarnerId" xml:space="preserve">
    <value>Sales Owner</value>
  </data>
  <data name="CompanyAlternateContactNo" xml:space="preserve">
    <value>Número de contacto alternativo</value>
  </data>
  <data name="CompanyBackground" xml:space="preserve">
    <value>Fondo de Pantalla de la Empresa</value>
  </data>
  <data name="CompanyCode" xml:space="preserve">
    <value>Código de Empresa</value>
  </data>
  <data name="CompanyContactFaxNo" xml:space="preserve">
    <value>Número de Fax</value>
  </data>
  <data name="CompanyContactNo" xml:space="preserve">
    <value>Número de Teléfono</value>
  </data>
  <data name="CompanyContactPerson" xml:space="preserve">
    <value>Persona de contacto en la Empresa</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Datos de la Empresa</value>
  </data>
  <data name="CompanyEmailPayslips" xml:space="preserve">
    <value>¿Se enviará por email la nomina a todos los empleados?</value>
  </data>
  <data name="CompanyGroupHelperText" xml:space="preserve">
    <value>Si se deja en blanco, esto se establecerá por defecto como el Nombre de la Empresa</value>
  </data>
  <data name="CompanyGroupName" xml:space="preserve">
    <value>Nombre del Grupo Empresarial</value>
  </data>
  <data name="CompanyGroupNameLength" xml:space="preserve">
    <value>El nombre de grupo de empresas no puede exceder 128 carácteres</value>
  </data>
  <data name="CompanyId" xml:space="preserve">
    <value>ID de Empresa</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Logo de la Empresa</value>
  </data>
  <data name="CompanyMobileNumber" xml:space="preserve">
    <value>Teléfono Móvil</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Nombre de la Empresa</value>
  </data>
  <data name="CompanyNameRequired" xml:space="preserve">
    <value>Se requiere insertar el Nombre de la Empresa</value>
  </data>
  <data name="CompanyPaymentModuleRequired" xml:space="preserve">
    <value>Se requiere elegir la Edición</value>
  </data>
  <data name="CompanyRegistrationNo" xml:space="preserve">
    <value>Numero de Registro de la Empresa</value>
  </data>
  <data name="CompanyRegistrationNumberRequired" xml:space="preserve">
    <value>Se requiere insertar el Número de Registro de la Empresa</value>
  </data>
  <data name="CompanyThemes" xml:space="preserve">
    <value>Temas de la Empresa</value>
  </data>
  <data name="CompanyTradingName" xml:space="preserve">
    <value>Nombre Comercial</value>
  </data>
  <data name="CompanyVatNo" xml:space="preserve">
    <value>Número de Identificación Fiscal (NIF)</value>
  </data>
  <data name="ComplexName" xml:space="preserve">
    <value>Complejo / Urbanización</value>
  </data>
  <data name="ContactDetails" xml:space="preserve">
    <value>Datos de Contacto</value>
  </data>
  <data name="DefaultPayslip" xml:space="preserve">
    <value>Nómina Predeterminada</value>
  </data>
  <data name="DisableCalculations" xml:space="preserve">
    <value>Desactivar los cálculos durante el cuadre de nómina para agilizar el proceso</value>
  </data>
  <data name="DoNotCalcCompany" xml:space="preserve">
    <value>Desactivar los cálculos durante el cuadre de nómina para agilizar el proceso</value>
  </data>
  <data name="DuplicateGroupName" xml:space="preserve">
    <value>Nombre de Grupo Empresarial duplicado</value>
  </data>
  <data name="DuplicateRegistrationNumber" xml:space="preserve">
    <value>Este Numero de Registro ya existe</value>
  </data>
  <data name="Edition" xml:space="preserve">
    <value>Edición</value>
  </data>
  <data name="EmailDomainVerification" xml:space="preserve">
    <value>Dirección de email para verificación del dominio</value>
  </data>
  <data name="EmailPayslip" xml:space="preserve">
    <value>¿Se enviará por email la nomina a todos los empleados?</value>
  </data>
  <data name="EmployeeNumberGenerationLength" xml:space="preserve">
    <value>El número de empleado auto generado no puede exceder de 10</value>
  </data>
  <data name="EnableLockout" xml:space="preserve">
    <value>Permitir bloqueo de todo el sistema</value>
  </data>
  <data name="errAddressCodeMaxLength" xml:space="preserve">
    <value>El código no puede exceder de 10 caracteres</value>
  </data>
  <data name="FeesPaymentMethodId" xml:space="preserve">
    <value>¿Como quieres pagar tus tarifas mensuales?</value>
  </data>
  <data name="FinancialYearEndMonth" xml:space="preserve">
    <value>Último Mes del Año Fiscal</value>
  </data>
  <data name="FinancialYrEndMonth" xml:space="preserve">
    <value>Último Mes del Año Fiscal</value>
  </data>
  <data name="GeneralDetails" xml:space="preserve">
    <value>Datos Generales</value>
  </data>
  <data name="GeneratedLength" xml:space="preserve">
    <value>Núm. de caracteres del núm. de empleado al usar la generación automática</value>
  </data>
  <data name="GenerateEmployeeNumbers" xml:space="preserve">
    <value>Generar Números de Empleado</value>
  </data>
  <data name="GenerateEmpNo" xml:space="preserve">
    <value>Generar Números de Empleados</value>
  </data>
  <data name="HierarchyStructureModule" xml:space="preserve">
    <value>¿Quieres utilizar el Módulo de Estructura Jerárquica Avanzada? (si no, se utilizará la versión simplificada)</value>
  </data>
  <data name="InactiveComments" xml:space="preserve">
    <value>Notas Inactivas</value>
  </data>
  <data name="InactiveCompany" xml:space="preserve">
    <value>Inactive Company</value>
  </data>
  <data name="IncludeAnalytics" xml:space="preserve">
    <value>¿Añadir esta empresa (+ las otras compañías del grupo) a Cloud Analytics?</value>
  </data>
  <data name="InvalidGroupName" xml:space="preserve">
    <value>Nombre de grupo de empresas inválido</value>
  </data>
  <data name="IsPartOfGroup" xml:space="preserve">
    <value>¿La empresa es parte de un grupo empresarial?</value>
  </data>
  <data name="KeepEmployeeNumbers" xml:space="preserve">
    <value>Mantener números de empleados existentes al transferir</value>
  </data>
  <data name="lblAddressPostal1" xml:space="preserve">
    <value>Dirección Postal Linea 1</value>
  </data>
  <data name="lblAddressStreetType" xml:space="preserve">
    <value>Tipo de Vía</value>
  </data>
  <data name="lblBlock" xml:space="preserve">
    <value>Bloque</value>
  </data>
  <data name="lblCityTown" xml:space="preserve">
    <value>Cuidad</value>
  </data>
  <data name="lblCode" xml:space="preserve">
    <value>Código</value>
  </data>
  <data name="lblCompanyTaxReferenceNumber" xml:space="preserve">
    <value>Número de Identificación Fiscal (NIF)</value>
  </data>
  <data name="lblComplexName" xml:space="preserve">
    <value>Complejo / Urbanización</value>
  </data>
  <data name="lblCountry" xml:space="preserve">
    <value>País</value>
  </data>
  <data name="lblDoor" xml:space="preserve">
    <value>Puerta</value>
  </data>
  <data name="lblEntrance" xml:space="preserve">
    <value>Entrada</value>
  </data>
  <data name="lblFloor" xml:space="preserve">
    <value>Piso</value>
  </data>
  <data name="lblIsCareofAddress" xml:space="preserve">
    <value>¿Es la dirección postal una dirección a cargo de otra persona?</value>
  </data>
  <data name="lblMunicipality" xml:space="preserve">
    <value>Municipio</value>
  </data>
  <data name="lblNavbarColor" xml:space="preserve">
    <value>Color de la Barra de Navegación</value>
  </data>
  <data name="lblPrimaryColor" xml:space="preserve">
    <value>Color Primario</value>
  </data>
  <data name="lblProvince" xml:space="preserve">
    <value>Provincia</value>
  </data>
  <data name="lblSameAsPostal" xml:space="preserve">
    <value>Selecciona esta casilla si la dirección postal es la misma que la física</value>
  </data>
  <data name="lblSignature" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="lblStaircase" xml:space="preserve">
    <value>Escalera</value>
  </data>
  <data name="lblStreetName" xml:space="preserve">
    <value>Calle</value>
  </data>
  <data name="lblStreetNum" xml:space="preserve">
    <value>Número</value>
  </data>
  <data name="lblSuburb" xml:space="preserve">
    <value>Suburbio o Distrito</value>
  </data>
  <data name="lblUnitNumber" xml:space="preserve">
    <value>Número</value>
  </data>
  <data name="MfaOption" xml:space="preserve">
    <value>MfaOption</value>
  </data>
  <data name="MonthlyFeeMethod" xml:space="preserve">
    <value>¿Como quieres pagar tus tarifas mensuales?</value>
  </data>
  <data name="OutsourceCustomer" xml:space="preserve">
    <value>Outsource Customer</value>
  </data>
  <data name="PasswordExpiryPeriod" xml:space="preserve">
    <value>Periodo de Caducidad de la Contraseña</value>
  </data>
  <data name="PasswordExpiryPeriodOverride" xml:space="preserve">
    <value>Reemplazar Periodo de Caducidad de la Contraseña</value>
  </data>
  <data name="PasswordExpiryPeriodRequired" xml:space="preserve">
    <value>Se requiere insertar un periodo de caducidad de la contraseña</value>
  </data>
  <data name="PaymentModuleId" xml:space="preserve">
    <value>Edición</value>
  </data>
  <data name="Physical" xml:space="preserve">
    <value>Dirección Física</value>
  </data>
  <data name="Prefix" xml:space="preserve">
    <value>Prefijo</value>
  </data>
  <data name="PrioritySupport" xml:space="preserve">
    <value>Soporte Prioritario</value>
  </data>
  <data name="SalesOwner" xml:space="preserve">
    <value>Sales Owner</value>
  </data>
  <data name="SignatureOverride" xml:space="preserve">
    <value>Firma Personalizada</value>
  </data>
  <data name="SignatureOverrideLength" xml:space="preserve">
    <value>La Firma no puede exceder de 50 caracteres</value>
  </data>
  <data name="StatutoryFields" xml:space="preserve">
    <value>Datos Legales de la Empresa</value>
  </data>
  <data name="StreetName" xml:space="preserve">
    <value>Calle</value>
  </data>
  <data name="StreetNameLength" xml:space="preserve">
    <value>El nombre de la calle no puede exceder de 50 caracteres</value>
  </data>
  <data name="StreetNumber" xml:space="preserve">
    <value>Número</value>
  </data>
  <data name="StreetNumberLength" xml:space="preserve">
    <value>El número de la calle no puede exceder 8 dígitos</value>
  </data>
  <data name="SuburbOrDistrict" xml:space="preserve">
    <value>Suburbio o Distrito</value>
  </data>
  <data name="TaxAuthorityRequired" xml:space="preserve">
    <value>Se requiere insertar la País Fiscal</value>
  </data>
  <data name="TaxCountry" xml:space="preserve">
    <value>Autoridad Fiscal</value>
  </data>
  <data name="TaxCountryId" xml:space="preserve">
    <value>País Fiscal</value>
  </data>
  <data name="TestDemoCompany" xml:space="preserve">
    <value>Test/Demo Company</value>
  </data>
  <data name="UnitNumber" xml:space="preserve">
    <value>Número</value>
  </data>
  <data name="UnitNumberLength" xml:space="preserve">
    <value>El número no puede exceder de 8 caracteres</value>
  </data>
  <data name="UrlOverride" xml:space="preserve">
    <value>URL Personalizada</value>
  </data>
</root>