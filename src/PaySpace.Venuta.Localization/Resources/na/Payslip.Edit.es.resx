<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnSave" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="exportHeaderRunDescription" xml:space="preserve">
    <value>Ciclo de la Nómina</value>
  </data>
  <data name="lblAction" xml:space="preserve">
    <value>Acciones</value>
  </data>
  <data name="lblAdd" xml:space="preserve">
    <value>Añadir</value>
  </data>
  <data name="lblDescription" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="lblEmployeeNumber" xml:space="preserve">
    <value>Número de Empleado</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Número de Empleado</value>
  </data>
  <data name="lblHourlyRate" xml:space="preserve">
    <value>Tarifa por Hora</value>
  </data>
  <data name="lblHours" xml:space="preserve">
    <value>Horas</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Editar Nómina</value>
  </data>
  <data name="lblPayslipComments" xml:space="preserve">
    <value>Notas de la Nómina</value>
  </data>
  <data name="lblPayslipErrors" xml:space="preserve">
    <value>Errores de cálculo</value>
  </data>
  <data name="lblPeriodEndDate" xml:space="preserve">
    <value>Fin del Periodo</value>
  </data>
  <data name="lblPeriodStartDate" xml:space="preserve">
    <value>Inicio del Periodo</value>
  </data>
  <data name="lblRunDescription" xml:space="preserve">
    <value>Descripción del periodo de cálculo</value>
  </data>
  <data name="lblTotalNetPay" xml:space="preserve">
    <value>Pago Neto Total</value>
  </data>
  <data name="PartOfPackage" xml:space="preserve">
    <value>Parte del paquete salarial</value>
  </data>
</root>