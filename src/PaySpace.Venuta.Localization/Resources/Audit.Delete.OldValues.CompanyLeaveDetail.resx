<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AccrualComponentCodeHours" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="AccrualOptionId" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="AccrualPeriodValue" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="AfterDays" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ApplyEmployeeDefined" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ApplyGradeBands" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ApplyServiceLength" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="AttachmentMandatory" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="BucketRules" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="CarryOverDays" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="CompanyLeaveSetupType" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ConsecutiveDays" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="DisplayBalanceESS" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="DoNotCalculateBceaValue" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="DoNotForceAttachment" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="DoNotShowOnPaySlip" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="DropOffMonth" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="EffectiveDate" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="EffectiveDateForfeit" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="EncashComponentId" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ForceAttachmentOnSecond" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ForfeitCompanyLeaveSetupId" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ForfeitPeriod" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="IncludePendingApps" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="IncludePH" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="LeaveDescription" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="LeaveForfeitPeriod" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="LiabilityComponentId" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="MaxBalance" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="NegativeLeaveAmount" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="OffDays" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ProrateAccrual" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ReflectInHours" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ShowOnPaySlip" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="StopDate" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ValueLessThan" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
  <data name="ValueMoreThan" xml:space="preserve">
    <value>Delete : {0}</value>
  </data>
</root>