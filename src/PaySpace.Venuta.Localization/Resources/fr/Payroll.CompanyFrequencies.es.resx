<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Day_0" xml:space="preserve">
    <value>Domingo</value>
  </data>
  <data name="Day_12" xml:space="preserve">
    <value>Siguiente - Viernes</value>
  </data>
  <data name="Day_2" xml:space="preserve">
    <value>Martes</value>
  </data>
  <data name="Day_3" xml:space="preserve">
    <value>MIércoles</value>
  </data>
  <data name="Day_4" xml:space="preserve">
    <value>Jueves</value>
  </data>
  <data name="Day_5" xml:space="preserve">
    <value>Viernes</value>
  </data>
  <data name="Day_6" xml:space="preserve">
    <value>Sábado</value>
  </data>
  <data name="Day_7" xml:space="preserve">
    <value>Domingo</value>
  </data>
  <data name="errFrequencyNameRequired" xml:space="preserve">
    <value>Se requiere un Nombre de la Frecuencia</value>
  </data>
  <data name="FrequencyName" xml:space="preserve">
    <value>Nombre de la Frecuencia</value>
  </data>
  <data name="lblAddFrequency" xml:space="preserve">
    <value>Añadir frecuencia</value>
  </data>
  <data name="lblComplex" xml:space="preserve">
    <value>Complejo / Urbanización</value>
  </data>
  <data name="lblFrequency" xml:space="preserve">
    <value>Frecuencia</value>
  </data>
  <data name="lblFrequencyName" xml:space="preserve">
    <value>Nombre de la Frecuencia</value>
  </data>
  <data name="lblFrequencyTab_1" xml:space="preserve">
    <value>Frecuencia semanal</value>
  </data>
  <data name="lblFrequencyTab_2" xml:space="preserve">
    <value>Frecuencia mensual</value>
  </data>
  <data name="lblFrequencyTab_3" xml:space="preserve">
    <value>Frecuencia quincenal</value>
  </data>
  <data name="lblInactiveFromDate" xml:space="preserve">
    <value>Fecha de Baja</value>
  </data>
  <data name="lblPayFrequency" xml:space="preserve">
    <value>Frecuencia de pago</value>
  </data>
  <data name="lblRunDescription" xml:space="preserve">
    <value>Descripción del periodo de cálculo</value>
  </data>
  <data name="lblUnitNumber" xml:space="preserve">
    <value>Unidad</value>
  </data>
  <data name="lblWorkingDaysFriday" xml:space="preserve">
    <value>Días laborables por defecto - viernes</value>
  </data>
  <data name="lblWorkingDaysSaturday" xml:space="preserve">
    <value>Días laborables por defecto - sábado</value>
  </data>
  <data name="lblWorkingDaysSunday" xml:space="preserve">
    <value>Días laborables por defecto - domingo</value>
  </data>
  <data name="lblWorkingDaysTuesday" xml:space="preserve">
    <value>Días laborables por defecto - Martes</value>
  </data>
  <data name="RunFrequency" xml:space="preserve">
    <value>Frecuencia</value>
  </data>
</root>