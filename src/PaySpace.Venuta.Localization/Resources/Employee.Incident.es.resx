<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="errFacilitatorLength" xml:space="preserve">
    <value>El mediador no puede ser superior a 100 caracteres.</value>
  </data>
  <data name="lblAccused" xml:space="preserve">
    <value>Acusado</value>
  </data>
  <data name="lblAdditional" xml:space="preserve">
    <value>Adicional</value>
  </data>
  <data name="lblAppealHeader" xml:space="preserve">
    <value>Apelación</value>
  </data>
  <data name="lblAppealOutcome" xml:space="preserve">
    <value>Resultado de la Apelación</value>
  </data>
  <data name="lblAppealReason" xml:space="preserve">
    <value>Motivo de la Apelación</value>
  </data>
  <data name="lblAppealReasonRequiredError" xml:space="preserve">
    <value>Se requiere el Motivo de la Apelación</value>
  </data>
  <data name="lblArbitrationDate" xml:space="preserve">
    <value>Fecha del Arbitraje</value>
  </data>
  <data name="lblArbitrationHeader" xml:space="preserve">
    <value>Arbitraje</value>
  </data>
  <data name="lblAttachments" xml:space="preserve">
    <value>Datos Adjuntos</value>
  </data>
  <data name="lblAwardInFavour" xml:space="preserve">
    <value>Decisión a favor de</value>
  </data>
  <data name="lblCaseRefNumber" xml:space="preserve">
    <value>Número de referencia del caso</value>
  </data>
  <data name="lblChairperson" xml:space="preserve">
    <value>Presidente</value>
  </data>
  <data name="lblCommissioner" xml:space="preserve">
    <value>Miembro de la comisión</value>
  </data>
  <data name="lblCompanyRepresentatives" xml:space="preserve">
    <value>Representantes de la empresa</value>
  </data>
  <data name="lblCompanyWitnesses" xml:space="preserve">
    <value>Testigos</value>
  </data>
  <data name="lblConciliationHeader" xml:space="preserve">
    <value>Conciliación</value>
  </data>
  <data name="lblConditions" xml:space="preserve">
    <value>Condiciones</value>
  </data>
  <data name="lblCourtHeader" xml:space="preserve">
    <value>Corte</value>
  </data>
  <data name="lblDiscussionHeader" xml:space="preserve">
    <value>Discusión</value>
  </data>
  <data name="lblEmployeeWitnesses" xml:space="preserve">
    <value>Testigo del Empleado</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Número de Empleado</value>
  </data>
  <data name="lblFacilitator" xml:space="preserve">
    <value>Mediador</value>
  </data>
  <data name="lblIncidentDate" xml:space="preserve">
    <value>Fecha de la Incidencia</value>
  </data>
  <data name="lblIncidentId" xml:space="preserve">
    <value>ID del Incidente</value>
  </data>
  <data name="lblIncidentNotes" xml:space="preserve">
    <value>Notas de la Incidencia</value>
  </data>
  <data name="lblIncidentType" xml:space="preserve">
    <value>Tipo de Incidente</value>
  </data>
  <data name="lblLinkItem" xml:space="preserve">
    <value>Enlazar Objeto</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Gestión de Incidencias</value>
  </data>
  <data name="lblPreparingAttachments" xml:space="preserve">
    <value>Preparando tus Datos Adjuntos</value>
  </data>
</root>