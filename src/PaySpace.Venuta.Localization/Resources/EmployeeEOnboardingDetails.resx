<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="EmployeeNumber" xml:space="preserve">
    <value>Employee number</value>
  </data>
  <data name="EOnboardingStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="errEmailExists" xml:space="preserve">
    <value>The Email already exists</value>
  </data>
  <data name="errEmailRequired" xml:space="preserve">
    <value>The Email is required</value>
  </data>
  <data name="errEmployeeNumberExists" xml:space="preserve">
    <value>The Employee number already exists</value>
  </data>
  <data name="errEmployeeNumberRequired" xml:space="preserve">
    <value>The Employee number is required</value>
  </data>
  <data name="errEmploymentDateRequired" xml:space="preserve">
    <value>The Employment date is required</value>
  </data>
  <data name="errFirstnameRequired" xml:space="preserve">
    <value>The First name is required</value>
  </data>
  <data name="errInvalidEmail" xml:space="preserve">
    <value>The email is invalid</value>
  </data>
  <data name="errInvalidEmployeeNumber" xml:space="preserve">
    <value>The Employee number is invalid</value>
  </data>
  <data name="errLastnameRequired" xml:space="preserve">
    <value>The Last name is required</value>
  </data>
  <data name="lblActivate" xml:space="preserve">
    <value>Activate</value>
  </data>
  <data name="lblComplete" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="lblConfirmationTitle" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="lblCreateOnboardingTemplate" xml:space="preserve">
    <value>On this page, you'll also be able to customize the default E-Onboarding Template by editing sections, adding fields or create custom groupings to suit your onboarding process.</value>
  </data>
  <data name="lblDfbActivate" xml:space="preserve">
    <value>To access the E-Onboarding screen and start loading new hire records, you first need to activate the E-Onboarding Template on the Dynamic Form Builder screen under the Config menu.</value>
  </data>
  <data name="lblEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="lblEmployeeNumberGenerated" xml:space="preserve">
    <value>Will be generated</value>
  </data>
  <data name="lblEmployeeNumberNotRequiredWhenGenerateEmpNo" xml:space="preserve">
    <value>Uploading new employee not allowed while "Generate Employee Numbers" enabled</value>
  </data>
  <data name="lblEmploymentDate" xml:space="preserve">
    <value>Employment date</value>
  </data>
  <data name="lblFirstname" xml:space="preserve">
    <value>First name</value>
  </data>
  <data name="lblLastname" xml:space="preserve">
    <value>Last name</value>
  </data>
  <data name="lblNewHires" xml:space="preserve">
    <value>New Hires</value>
  </data>
  <data name="lblOnboardingConfirmation" xml:space="preserve">
    <value>The New Hire record has been saved. You can choose to send the Onboarding Welcome email now, which includes a link for the new hire to complete their onboarding form. Alternatively, you may send the email later when you're ready to begin the onboarding process.</value>
  </data>
  <data name="lblOnboardingExperience" xml:space="preserve">
    <value>Activating this template ensures everything is set up correctly to guide new hires through a smooth onboarding experience.</value>
  </data>
  <data name="lblOrganizationGroup" xml:space="preserve">
    <value>Organization unit</value>
  </data>
  <data name="lblOrganizationPosition" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>E-Onboarding</value>
  </data>
  <data name="lblPhoneNumber" xml:space="preserve">
    <value>Cellphone number</value>
  </data>
  <data name="lblSendLater" xml:space="preserve">
    <value>Send later</value>
  </data>
  <data name="lblSendNow" xml:space="preserve">
    <value>Send now</value>
  </data>
  <data name="Select &quot;Activate&quot; to go to this page now." xml:space="preserve">
    <value>Select "Activate" to go to this page now.</value>
  </data>
</root>