<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="lblAttachmentClassificationDesc" xml:space="preserve">
    <value>Indica qué tipo de anexos se han cargado para cada empleado</value>
  </data>
  <data name="lblEmployeeDependantsListingName" xml:space="preserve">
    <value>Lista de Dependientes</value>
  </data>
  <data name="lblEmployeeNumber" xml:space="preserve">
    <value>Número de Empleado</value>
  </data>
  <data name="lblEndDateParamNameKey" xml:space="preserve">
    <value>Fecha Fin</value>
  </data>
  <data name="lblFormatSelection" xml:space="preserve">
    <value>Formato</value>
  </data>
  <data name="lblIncludeTerminatedParamKey" xml:space="preserve">
    <value>Incluir Terminaciones</value>
  </data>
  <data name="lblLeaveTypeParamNameKey" xml:space="preserve">
    <value>Tipo</value>
  </data>
  <data name="lblLeg_Burundi_Monthly_Tax_Declaration_FormDesc" xml:space="preserve">
    <value>El formulario de declaración mensual del Impuesto sobre el Valor Añadido (IVA), IRE, retenciones en la fuente y varias deducciones a tanto alzado. Solo la sección del encabezado en la página 1, la página 4 y el campo 4 en la página 6 devolverán datos de nómina. El informe debe descargarse en Adobe Acrobat.</value>
  </data>
  <data name="lblLeg_Spain_Average_HeadcountDesc" xml:space="preserve">
    <value>El reporte "Plantilla media de trabajadores por Empresa"  es un reporte operacional y tiene como objetivo mostrar  la plantilla media de trabajadores de la empresa en periodo determinado de tiempo.</value>
  </data>
  <data name="lblLeg_Spain_Average_HeadcountName" xml:space="preserve">
    <value>Plantilla media de trabajadores por Centro de Trabajo</value>
  </data>
  <data name="lblLeg_Spain_IRPF_ReconDesc" xml:space="preserve">
    <value>El informe "Cuadre de Cuotas" garantiza el cumplimiento de los requisitos legales de retención cuando un empleado es dado de baja. Compara las retenciones estimadas con los montos realmente retenidos. Si se detecta un déficit (una diferencia negativa), se debe aplicar un tipo impositivo más alto para cumplir con la retención mínima legal basada en la compensación total del empleado.</value>
  </data>
  <data name="lblLeg_Spain_IRPF_ReconName" xml:space="preserve">
    <value>Cuadre de Cuotas</value>
  </data>
  <data name="lblLeg_Spain_Tax_Form_111_ReportDesc" xml:space="preserve">
    <value>El Modelo 111 en España es utilizado por empresas y autónomos para declarar y pagar retenciones sobre los ingresos obtenidos por empleados, autónomos y profesionales. Cubre retenciones de salarios, servicios profesionales y otros pagos determinados. Presentado trimestral o mensualmente, garantiza que los impuestos retenidos de los pagos se informen y transfieran a la Agencia Tributaria española (Agencia Tributaria).</value>
  </data>
  <data name="lblLeg_Spain_Tax_Form_111_ReportName" xml:space="preserve">
    <value>Modelo 111 España</value>
  </data>
  <data name="lblOrgUnitIDParamNameKey" xml:space="preserve">
    <value>Unidades Organizacionales</value>
  </data>
  <data name="lblOrgUnitParamNameKey" xml:space="preserve">
    <value>Unidades Organizacionales</value>
  </data>
  <data name="lblOrgUnitsParamNameKey" xml:space="preserve">
    <value>Unidades Organizacionales</value>
  </data>
  <data name="lblPeriodEndDate" xml:space="preserve">
    <value>Fin Periodo</value>
  </data>
  <data name="lblPeriodStartDate" xml:space="preserve">
    <value>Inicio Periodo</value>
  </data>
  <data name="lblSpain_Average_Headcount_ReportDesc" xml:space="preserve">
    <value>El reporte "Plantilla media de trabajadores por Empresa"  es un reporte operacional y tiene como objetivo mostrar  la plantilla media de trabajadores de la empresa en periodo determinado de tiempo.</value>
  </data>
  <data name="lblSpain_Average_Headcount_ReportName" xml:space="preserve">
    <value>Plantilla media de trabajadores por Centro de Trabajo</value>
  </data>
  <data name="lblSpain_Leaves_ReportDesc" xml:space="preserve">
    <value>El Informe de Ausencias proporciona una lista completa de todas las ausencias de los empleados registradas en la plataforma. Incluye detalles clave como la fecha de inicio, fecha de fin, tipo de ausencia y duración total. Este informe es especialmente útil para identificar bajas médicas que superen los 30 días, ya que RRHH necesita determinar qué casos requieren complementos salariales.</value>
  </data>
  <data name="lblSpain_Leaves_ReportName" xml:space="preserve">
    <value>Listado de absentismos</value>
  </data>
  <data name="lblStartDateParamNameKey" xml:space="preserve">
    <value>Fecha de Inicio</value>
  </data>
  <data name="lblTaxIDParamNameKey" xml:space="preserve">
    <value>N.I.F del Empleado</value>
  </data>
  <data name="lblWorkplaceParamNameKey" xml:space="preserve">
    <value>Centro de Trabajo</value>
  </data>
</root>