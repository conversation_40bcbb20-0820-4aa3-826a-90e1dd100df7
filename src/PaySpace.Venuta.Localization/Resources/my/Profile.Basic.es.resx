<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="0011" xml:space="preserve">
    <value>Se requiere indicar la provincia</value>
  </data>
  <data name="0021" xml:space="preserve">
    <value>Provincia errónea para el país inidcado</value>
  </data>
  <data name="0023" xml:space="preserve">
    <value>Número de trabajo no válido. Solo se permiten números.</value>
  </data>
  <data name="0048" xml:space="preserve">
    <value>Se requiere un número de contacto</value>
  </data>
  <data name="0056" xml:space="preserve">
    <value>Las iniciales no pueden contener caracteres especiales</value>
  </data>
  <data name="AddNewEmployeePageHeader" xml:space="preserve">
    <value>Añadir Nuevo Empleado</value>
  </data>
  <data name="BlockMaxLengthError" xml:space="preserve">
    <value>Bloque no puede exceder {0} caracteres</value>
  </data>
  <data name="DoorMaxLengthError" xml:space="preserve">
    <value>Puerta no puede exceder de {0} caracteres</value>
  </data>
  <data name="EffectiveDate" xml:space="preserve">
    <value>Efectivo desde</value>
  </data>
  <data name="EntranceMaxLengthError" xml:space="preserve">
    <value>La Entrada no puede exceder de {0} caracteres</value>
  </data>
  <data name="errAddressStreetTypeRequired" xml:space="preserve">
    <value>Se requiere Tipo de Vía</value>
  </data>
  <data name="errEmploymentDate" xml:space="preserve">
    <value>La fecha de vigencia no puede ser anterior a la fecha de contratación del empleado.</value>
  </data>
  <data name="lblBirthdate" xml:space="preserve">
    <value>Fecha de Nacimiento</value>
  </data>
  <data name="lblBlock" xml:space="preserve">
    <value>Bloque</value>
  </data>
  <data name="lblCellNumber" xml:space="preserve">
    <value>Teléfono Móvil</value>
  </data>
  <data name="lblCitiz" xml:space="preserve">
    <value>Ciudadanía</value>
  </data>
  <data name="lblContactDetHeader" xml:space="preserve">
    <value>Datos de Contacto</value>
  </data>
  <data name="lblDetailsHeader" xml:space="preserve">
    <value>Datos</value>
  </data>
  <data name="lblDisabledT" xml:space="preserve">
    <value>Tipo de Discapacidad</value>
  </data>
  <data name="lblDoor" xml:space="preserve">
    <value>Puerta</value>
  </data>
  <data name="lblEmergContactDetHeader" xml:space="preserve">
    <value>Datos de Contacto de Emergencia</value>
  </data>
  <data name="lblEmergContAddress" xml:space="preserve">
    <value>Dirección del Contacto de Emergencia</value>
  </data>
  <data name="lblEmergContName" xml:space="preserve">
    <value>Nombre del Contacto de Emergencia</value>
  </data>
  <data name="lblEmergContNumber" xml:space="preserve">
    <value>Número de Contacto de Emergencia</value>
  </data>
  <data name="lblEmployeeNumberChangeNotAllowed" xml:space="preserve">
    <value>Cambio del Número de Empleado no permitido</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Número de Empleado</value>
  </data>
  <data name="lblEntrance" xml:space="preserve">
    <value>Entrada</value>
  </data>
  <data name="lblEtiExempt" xml:space="preserve">
    <value>No cualifica para incentivos fiscales de empleo</value>
  </data>
  <data name="lblFirstName" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="lblFloor" xml:space="preserve">
    <value>Piso</value>
  </data>
  <data name="lblForeignNat" xml:space="preserve">
    <value>Residente Extrangero</value>
  </data>
  <data name="lblFormGroupCaption" xml:space="preserve">
    <value>Datos</value>
  </data>
  <data name="lblGender" xml:space="preserve">
    <value>Género</value>
  </data>
  <data name="lblHomeNumber" xml:space="preserve">
    <value>Teléfono Fijo</value>
  </data>
  <data name="lblInitial" xml:space="preserve">
    <value>Iniciales</value>
  </data>
  <data name="lblInvalidHomeNumber" xml:space="preserve">
    <value>Número de teléfono inválido</value>
  </data>
  <data name="lblIsCareofAddress" xml:space="preserve">
    <value>¿Es la dirección postal una dirección a cargo de otra persona?</value>
  </data>
  <data name="lblIsEmpRetired" xml:space="preserve">
    <value>¿Este empleado está jubilado?</value>
  </data>
  <data name="lblLanguage" xml:space="preserve">
    <value>Idioma</value>
  </data>
  <data name="lblLastName" xml:space="preserve">
    <value>Primer Apellido</value>
  </data>
  <data name="lblMaidenName" xml:space="preserve">
    <value>Apellido de Soltera</value>
  </data>
  <data name="lblMaritalStat" xml:space="preserve">
    <value>Estado civil</value>
  </data>
  <data name="lblMiddleName" xml:space="preserve">
    <value>Nombre Intermedio</value>
  </data>
  <data name="lblNationality" xml:space="preserve">
    <value>Nacionalidad</value>
  </data>
  <data name="lblNewEmployeeHistory" xml:space="preserve">
    <value>Perfil Básico</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Perfil Básico</value>
  </data>
  <data name="lblPostalAddress" xml:space="preserve">
    <value>Dirección Postal</value>
  </data>
  <data name="lblPrefName" xml:space="preserve">
    <value>Nombre de Pila</value>
  </data>
  <data name="lblRace" xml:space="preserve">
    <value>Raza</value>
  </data>
  <data name="lblSameAsPostal" xml:space="preserve">
    <value>¿La dirección postal es la misma que la dirección física?</value>
  </data>
  <data name="lblSdlexemption" xml:space="preserve">
    <value>Razón de exención de SDL</value>
  </data>
  <data name="lblStaircase" xml:space="preserve">
    <value>Escalera</value>
  </data>
  <data name="lblStandardIndustryCodeHeader" xml:space="preserve">
    <value>Grupo de Códigos Estándar de la Indústria</value>
  </data>
  <data name="lblSubStandardIndustryCodeId" xml:space="preserve">
    <value>Código Estándar de la Indústria</value>
  </data>
  <data name="lblTitle" xml:space="preserve">
    <value>Título</value>
  </data>
  <data name="lblUifexemption" xml:space="preserve">
    <value>Razón de exención de UIF</value>
  </data>
  <data name="lblWorkEx" xml:space="preserve">
    <value>Extensión</value>
  </data>
  <data name="lblWorkNumber" xml:space="preserve">
    <value>Teléfono del Trabajo</value>
  </data>
  <data name="StaircaseMaxLengthError" xml:space="preserve">
    <value>Escalera no puede exceder {0} caracteres</value>
  </data>
  <data name="tabAddresses" xml:space="preserve">
    <value>Dirección</value>
  </data>
  <data name="tabContactDetails" xml:space="preserve">
    <value>Contacto</value>
  </data>
  <data name="tabEmployeeDetails" xml:space="preserve">
    <value>Datos del Empleado</value>
  </data>
  <data name="tabExemptionsAndOther" xml:space="preserve">
    <value>Exempciones y Otros</value>
  </data>
  <data name="tabPersonal" xml:space="preserve">
    <value>Personal</value>
  </data>
  <data name="txtimage" xml:space="preserve">
    <value>Foto de Empleado</value>
  </data>
</root>