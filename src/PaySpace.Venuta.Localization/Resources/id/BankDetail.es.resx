<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="0001" xml:space="preserve">
    <value>Número de Sucursal no válido</value>
  </data>
  <data name="0002" xml:space="preserve">
    <value>El número de cuenta no pasó la validación del dígito de control.</value>
  </data>
  <data name="0003" xml:space="preserve">
    <value>Tipo de cuenta no válido.</value>
  </data>
  <data name="0007" xml:space="preserve">
    <value>El número de cuenta ya está asignado a un empleado</value>
  </data>
  <data name="0011" xml:space="preserve">
    <value>Se requiere un Método de Pago</value>
  </data>
  <data name="0012" xml:space="preserve">
    <value>Se requiere el tipo de cuenta.</value>
  </data>
  <data name="0013" xml:space="preserve">
    <value>Se requiere el Nombre del Banco</value>
  </data>
  <data name="0024" xml:space="preserve">
    <value>Se requiere el nombre del titular de la cuenta bancaria</value>
  </data>
  <data name="0026" xml:space="preserve">
    <value>Se requiere el titular de la cuenta bancaria</value>
  </data>
  <data name="0041" xml:space="preserve">
    <value>Se requiere el titular de la cuenta bancaria</value>
  </data>
  <data name="0042" xml:space="preserve">
    <value>Se requiere el nombre del titular de la cuenta bancaria</value>
  </data>
  <data name="0043" xml:space="preserve">
    <value>Se requiere el tipo de cuenta.</value>
  </data>
  <data name="0044" xml:space="preserve">
    <value>Se requiere el Nombre del Banco</value>
  </data>
  <data name="0045" xml:space="preserve">
    <value>Se requiere el Número de sucursal</value>
  </data>
  <data name="0046" xml:space="preserve">
    <value>Se requiere el número de cuenta.</value>
  </data>
  <data name="AccountType" xml:space="preserve">
    <value>Tipo de Cuenta</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Cantidad</value>
  </data>
  <data name="BankAccountNo" xml:space="preserve">
    <value>Número de cuenta</value>
  </data>
  <data name="BankAccountOwner" xml:space="preserve">
    <value>Titular de la cuenta</value>
  </data>
  <data name="BankAccountOwnerName" xml:space="preserve">
    <value>Nombre del titular de la cuenta bancaria</value>
  </data>
  <data name="BankBranchNo" xml:space="preserve">
    <value>Número de Sucursal</value>
  </data>
  <data name="BankName" xml:space="preserve">
    <value>Nombre del Banco</value>
  </data>
  <data name="btnNewBankDetail" xml:space="preserve">
    <value>Nueva Cuenta Bancaria</value>
  </data>
  <data name="btnSave" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="lblAccountType" xml:space="preserve">
    <value>Tipo de Cuenta</value>
  </data>
  <data name="lblAdditionalAccount" xml:space="preserve">
    <value>Cuenta Adicional</value>
  </data>
  <data name="lblAmount" xml:space="preserve">
    <value>Cantidad</value>
  </data>
  <data name="lblBankAccountComments" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="lblBankAccountNo" xml:space="preserve">
    <value>Número de cuenta</value>
  </data>
  <data name="lblBankAccountOwner" xml:space="preserve">
    <value>Titular de la cuenta</value>
  </data>
  <data name="lblBankAccountOwnerName" xml:space="preserve">
    <value>Nombre del titular de la cuenta bancaria</value>
  </data>
  <data name="lblBankAccountRoutingCode" xml:space="preserve">
    <value>Cód. Ruta Bancaria</value>
  </data>
  <data name="lblBankAccountSwiftCode" xml:space="preserve">
    <value>Código SWIFT</value>
  </data>
  <data name="lblBankBranchNo" xml:space="preserve">
    <value>Número de Sucursal</value>
  </data>
  <data name="lblBankName" xml:space="preserve">
    <value>Nombre del Banco</value>
  </data>
  <data name="lblBankReference" xml:space="preserve">
    <value>Beneficiario</value>
  </data>
  <data name="lblDetailsHeader" xml:space="preserve">
    <value>Datos</value>
  </data>
  <data name="lblEmpNumber" xml:space="preserve">
    <value>Número de Empleado</value>
  </data>
  <data name="lblFullName" xml:space="preserve">
    <value>Nombre Completo</value>
  </data>
  <data name="lblNoRecordsFound" xml:space="preserve">
    <value>No se han encontrado datos</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Datos Bancarios</value>
  </data>
  <data name="lblPaymentMethod" xml:space="preserve">
    <value>Método de Pago</value>
  </data>
  <data name="lblPercentage" xml:space="preserve">
    <value>Porcentaje</value>
  </data>
  <data name="lblSaved" xml:space="preserve">
    <value>Los cambios en los datos bancarios se han guardado correctamente.</value>
  </data>
  <data name="lblSplitByHeader" xml:space="preserve">
    <value>Dividir por</value>
  </data>
  <data name="lblSuburbs" xml:space="preserve">
    <value>Buscar código de la sucursal por zona</value>
  </data>
  <data name="lblSuspended" xml:space="preserve">
    <value>Suspender la cuenta</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>Método de Pago</value>
  </data>
  <data name="Reference" xml:space="preserve">
    <value>Referencia</value>
  </data>
  <data name="RoutingCode" xml:space="preserve">
    <value>Cód. Ruta Bancaria</value>
  </data>
  <data name="SplitType" xml:space="preserve">
    <value>Dividir por</value>
  </data>
  <data name="Suburb" xml:space="preserve">
    <value>Buscar código de la sucursal por zona</value>
  </data>
  <data name="Suburbs" xml:space="preserve">
    <value>Buscar código de la sucursal por zona</value>
  </data>
  <data name="Suspended" xml:space="preserve">
    <value>Suspender la cuenta</value>
  </data>
  <data name="SwiftCode" xml:space="preserve">
    <value>Código SWIFT</value>
  </data>
</root>