<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="2NDPAYRATE" xml:space="preserve">
    <value>Mostrar un segundo campo de tarifa de pago en la pantalla de detalles de la tarifa de pago del empleado</value>
  </data>
  <data name="ADDDATE1" xml:space="preserve">
    <value>Mostrar una segunda fecha adicional en la pantalla del perfil fiscal del empleado con el siguiente nombre</value>
  </data>
  <data name="ADDTAXDATE" xml:space="preserve">
    <value>Mostrar una fecha adicional en la pantalla del perfil fiscal del empleado con el siguiente nombre</value>
  </data>
  <data name="ADDTAXREFNO" xml:space="preserve">
    <value>Perfil Fiscal del Empleado: Número Fiscal de Referencia</value>
  </data>
  <data name="ALERTEMPCUT" xml:space="preserve">
    <value>Alertar al empleado si se ha realizado un cambio en la nómina entre la fecha límite y la fecha de pago</value>
  </data>
  <data name="AUTOESS" xml:space="preserve">
    <value>Enviar automáticamente el enlace de registro en ESS a los empleados al cargar un nuevo empleado/dirección de correo electrónico por primera vez</value>
  </data>
  <data name="BANKDETEMAIL" xml:space="preserve">
    <value>No enviar notificación por correo electrónico al empleado cuando se actualicen los datos bancarios</value>
  </data>
  <data name="CONSOL" xml:space="preserve">
    <value>Incluir todas las frecuencias en el archivo consolidado de impuestos de fin de año</value>
  </data>
  <data name="CUSTPAYSLIP" xml:space="preserve">
    <value>Usar el informe de nómina personalizado como predeterminado en las nóminas de los empleados</value>
  </data>
  <data name="EARCLOSE" xml:space="preserve">
    <value>Cerrar las ejecuciones tantos días antes de la fecha de pago</value>
  </data>
  <data name="EMPDELNOT" xml:space="preserve">
    <value>Notificar al destinatario del correo electrónico cuando un empleado sea eliminado</value>
  </data>
  <data name="ENROLLTRAINLINK" xml:space="preserve">
    <value>Deshabilitar el enlace "inscribirse ahora" en el calendario de horarios de cursos de formación a nivel de empleado</value>
  </data>
  <data name="FREQEMP" xml:space="preserve">
    <value>Mostrar la columna de selector de frecuencia de la empresa en la plantilla de carga masiva para agregar nuevos empleados</value>
  </data>
  <data name="General.Settings.Payroll" xml:space="preserve">
    <value>Ajustes de Nómina</value>
  </data>
  <data name="General.Settings.Statutory" xml:space="preserve">
    <value>Configuraciones legales</value>
  </data>
  <data name="HIDEPAYSLIPEMAIL" xml:space="preserve">
    <value>Ocultar la opción de enviar nóminas por correo electrónico en la pantalla de nóminas para esta empresa</value>
  </data>
  <data name="IDNUMCHECK" xml:space="preserve">
    <value>Verificar si hay un número de identificación duplicado al contratar a un nuevo empleado</value>
  </data>
  <data name="INBOXREM" xml:space="preserve">
    <value>Enviar correos de recordatorio para los elementos de la bandeja de entrada después de este número de días</value>
  </data>
  <data name="MaritalStatus" xml:space="preserve">
    <value>Perfil Básico del Empleado: Estado Civil</value>
  </data>
  <data name="MEDREF" xml:space="preserve">
    <value>Recurrente del Empleado: Número de referencia de la ayuda médica</value>
  </data>
  <data name="MIBFACBL" xml:space="preserve">
    <value>Si no eres miembro de una federación de empleadores, ¿qué cantidad pagas?</value>
  </data>
  <data name="MIBFACOMPNO" xml:space="preserve">
    <value>Número de empresa MIBFA para la extracción del archivo MIBFA</value>
  </data>
  <data name="NAPSAEMPLYRNO" xml:space="preserve">
    <value>El número de cuenta del empleador que se incluirá en el informe de NAPSA</value>
  </data>
  <data name="PAYRATECUR" xml:space="preserve">
    <value>Tarifa del Empleado: Divisa de la Tarifa</value>
  </data>
  <data name="PayRateReason" xml:space="preserve">
    <value>Tarifa del Empleado: Razón del Incremento</value>
  </data>
  <data name="PAYSLIPACCESS" xml:space="preserve">
    <value>Habilitar a los empleados para acceder a sus nóminas en el período entre la fecha límite y la fecha de pago</value>
  </data>
  <data name="SALCLOSE" xml:space="preserve">
    <value>Si se utiliza la función de transferencia electrónica de fondos (EFT), cerrar las ejecuciones tantos días antes de la fecha de pago</value>
  </data>
  <data name="SEARCH" xml:space="preserve">
    <value>Habilitar la búsqueda de empleados de esta empresa desde cualquier empresa dentro del grupo</value>
  </data>
  <data name="SHOWNOTES" xml:space="preserve">
    <value>Mostrar los componentes de la sección de notas en la nómina del empleado</value>
  </data>
  <data name="SHOWYTD" xml:space="preserve">
    <value>Mostrar el valor acumulado del año independientemente del valor actual</value>
  </data>
  <data name="UIFSelf" xml:space="preserve">
    <value>Enviar el archivo de declaración UIF a la dirección de correo electrónico UIF registrada en la pantalla de información básica de la empresa en lugar del Departamento de Trabajo</value>
  </data>
</root>