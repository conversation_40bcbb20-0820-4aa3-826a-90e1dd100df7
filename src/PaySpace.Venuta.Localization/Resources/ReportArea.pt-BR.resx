<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnNo" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="btnYes" xml:space="preserve">
    <value>Atualizar</value>
  </data>
  <data name="DownloadReady" xml:space="preserve">
    <value>Relatório pronto</value>
  </data>
  <data name="ExtractFile" xml:space="preserve">
    <value>Construir extração</value>
  </data>
  <data name="lbl_DisabilityDesc" xml:space="preserve">
    <value>Fornece uma lista das contribuições de invalidez dos funcionários para um período escolhido.</value>
  </data>
  <data name="lbl_DisabilityName" xml:space="preserve">
    <value>Relatório de Incapacidade</value>
  </data>
  <data name="lbl_GroupLifeDesc" xml:space="preserve">
    <value>Fornece uma lista das contribuições dos funcionários da Group Life para um período escolhido.</value>
  </data>
  <data name="lbl_GroupLifeName" xml:space="preserve">
    <value>Relatório de Vida em Grupo</value>
  </data>
  <data name="lbl_IncomeProtectionDesc" xml:space="preserve">
    <value>Fornece uma lista das contribuições de Proteção de Renda dos funcionários para um período escolhido.</value>
  </data>
  <data name="lbl_IncomeProtectionName" xml:space="preserve">
    <value>Relatório de Proteção de Renda</value>
  </data>
  <data name="lbl_PensionDesc" xml:space="preserve">
    <value>Fornece uma lista das contribuições de pensão/previdência dos funcionários para um período escolhido.</value>
  </data>
  <data name="lbl_PensionName" xml:space="preserve">
    <value>Relatório de Pensão e Previdência</value>
  </data>
  <data name="lbl_TradeUnionDesc" xml:space="preserve">
    <value>Fornece uma lista das contribuições sindicais do funcionário para um período ou execução escolhida.</value>
  </data>
  <data name="lbl_TradeUnionName" xml:space="preserve">
    <value>Relatório Sindical</value>
  </data>
  <data name="lblAccountNumber" xml:space="preserve">
    <value>Número da Conta</value>
  </data>
  <data name="lblAccrual" xml:space="preserve">
    <value>Acréscimo</value>
  </data>
  <data name="lblActualPeriodOrApprovedMonth" xml:space="preserve">
    <value>Renderizar relatório por</value>
  </data>
  <data name="lblActualPeriodSelect" xml:space="preserve">
    <value>Período efectivo</value>
  </data>
  <data name="lblAdditional" xml:space="preserve">
    <value>Adicional</value>
  </data>
  <data name="lblAgencyReport" xml:space="preserve">
    <value>Nível Business Partner</value>
  </data>
  <data name="lblAgencyTaxCountryReport" xml:space="preserve">
    <value>Nível de país de imposto de agência</value>
  </data>
  <data name="lblAllActions" xml:space="preserve">
    <value>Todas as ações</value>
  </data>
  <data name="lblAllCompanies" xml:space="preserve">
    <value>Todas as empresas</value>
  </data>
  <data name="lblAllComponents" xml:space="preserve">
    <value>Todas as rubricas</value>
  </data>
  <data name="lblAllCurrencies" xml:space="preserve">
    <value>Todas as moedas</value>
  </data>
  <data name="lblAllEmployees" xml:space="preserve">
    <value>Todos os colaboradores</value>
  </data>
  <data name="lblAllFrequencies" xml:space="preserve">
    <value>Todas as frequências</value>
  </data>
  <data name="lblAllLeaveTypes" xml:space="preserve">
    <value>Todos os tipos de licença</value>
  </data>
  <data name="lblAllLevels" xml:space="preserve">
    <value>Todos os níveis</value>
  </data>
  <data name="lblAllPayslipActions" xml:space="preserve">
    <value>Todos os tipos de pagamento</value>
  </data>
  <data name="lblAllPositions" xml:space="preserve">
    <value>Todas as funções</value>
  </data>
  <data name="lblAllRegions" xml:space="preserve">
    <value>Todos os estabelecimentos</value>
  </data>
  <data name="lblAllReportTabName" xml:space="preserve">
    <value>Construir relatório</value>
  </data>
  <data name="lblAllUnits" xml:space="preserve">
    <value>Todas as unidades</value>
  </data>
  <data name="lblAltLang" xml:space="preserve">
    <value>Nomes de componentes alternativos</value>
  </data>
  <data name="lblAltLanguageParamNameKey" xml:space="preserve">
    <value>Você deseja visualizar os nomes dos componentes em um idioma alternativo?</value>
  </data>
  <data name="lblAmount" xml:space="preserve">
    <value>Quantidade</value>
  </data>
  <data name="lblAngola_Simplificado_IRTDesc" xml:space="preserve">
    <value>Relatório eletrônico mensal simplificado de impostos. Baixe o arquivo em Excel para importar para a plataforma online.</value>
  </data>
  <data name="lblAngola_Simplificado_IRTName" xml:space="preserve">
    <value>TRI Simplificada - Mapa de Remuneração</value>
  </data>
  <data name="lblApplicableEntities" xml:space="preserve">
    <value>Salvo no seguinte</value>
  </data>
  <data name="lblApprovedMonthSelect" xml:space="preserve">
    <value>Mês aprovado</value>
  </data>
  <data name="lblArchiveIDParamNameKey" xml:space="preserve">
    <value>Selecionar orçamento arquivado</value>
  </data>
  <data name="lblArchiveParamNameKey" xml:space="preserve">
    <value>Selecionar orçamento arquivado</value>
  </data>
  <data name="lblAttachmentClassificationDesc" xml:space="preserve">
    <value>Indica que tipo de anexos foram carregados para cada funcionário.</value>
  </data>
  <data name="lblAttachmentClassificationName" xml:space="preserve">
    <value>Classificação de Anexos</value>
  </data>
  <data name="lblBalance" xml:space="preserve">
    <value>Equilíbrio</value>
  </data>
  <data name="lblBalancesDesc" xml:space="preserve">
    <value>Fornece uma lista dos saldos de férias de todos os funcionários para um período escolhido.</value>
  </data>
  <data name="lblBalancesName" xml:space="preserve">
    <value>Saldo de férias</value>
  </data>
  <data name="lblBalancingToolFormatSelect" xml:space="preserve">
    <value>Formato da ferramenta de balanceamento</value>
  </data>
  <data name="lblBankName" xml:space="preserve">
    <value>Nome do banco</value>
  </data>
  <data name="lblBirthDate" xml:space="preserve">
    <value>Data de Nascimento</value>
  </data>
  <data name="lblBRA_NH54_1_NetPayDesc" xml:space="preserve">
    <value>Relação de Líquidos - Analítica</value>
  </data>
  <data name="lblBRA_NH54_1_NetPayName" xml:space="preserve">
    <value>Relação de Líquidos - Analítica</value>
  </data>
  <data name="lblBranchCode" xml:space="preserve">
    <value>Código da Filial</value>
  </data>
  <data name="lblBrazil_Component_Variance_ReportDesc" xml:space="preserve">
    <value>Fornece os eventos/rubricas por funcionário e por processo de folha de pagamento referente ao mês selecionado</value>
  </data>
  <data name="lblBrazil_Component_Variance_ReportName" xml:space="preserve">
    <value>Folha de Pagamento por Processo em XLS</value>
  </data>
  <data name="lblBudgetReportDesc" xml:space="preserve">
    <value>Veja um período de orçamento único por vez. (instantâneo, arquivado ou dados atuais) Nenhuma comparação é feita neste relatório</value>
  </data>
  <data name="lblBudgetReportName" xml:space="preserve">
    <value>Relatório de orçamento</value>
  </data>
  <data name="lblBureauReport" xml:space="preserve">
    <value>Nível de escritório</value>
  </data>
  <data name="lblBureauTaxCountryReport" xml:space="preserve">
    <value>Nível de país de imposto do departamento</value>
  </data>
  <data name="lblBureauTaxId" xml:space="preserve">
    <value>Ano Fiscal</value>
  </data>
  <data name="lblBurkinaFasoDetailedFSPDesc" xml:space="preserve">
    <value>Um relatório detalhado que indica a base de contribuição, bem como a contribuição devida para o Fundo de Apoio Patriótico (FSP).</value>
  </data>
  <data name="lblBurkinaFasoDetailedFSPName" xml:space="preserve">
    <value>Declaração de dedução obrigatória dos salários dos funcionários públicos e trabalhadores do setor privado em anexo</value>
  </data>
  <data name="lblBurkinaFasoSummaryFSPDesc" xml:space="preserve">
    <value>Um relatório resumido da contribuição do FSP do mês.</value>
  </data>
  <data name="lblBurkinaFasoSummaryFSPName" xml:space="preserve">
    <value>Declaração obrigatória de dedução dos salários dos funcionários públicos e trabalhadores do setor privado</value>
  </data>
  <data name="lblBusinessPartner" xml:space="preserve">
    <value>Parceiro de negócios</value>
  </data>
  <data name="lblBusinessPartners" xml:space="preserve">
    <value>Parceiros de negócios</value>
  </data>
  <data name="lblCantOverwriteDefault" xml:space="preserve">
    <value>Não é possível substituir o nível de relatório padrão</value>
  </data>
  <data name="lblChangeDescriptionRequired" xml:space="preserve">
    <value>Descrição das alterações efetuadas</value>
  </data>
  <data name="lblCollapseAllBtn" xml:space="preserve">
    <value>Colapso</value>
  </data>
  <data name="lblCollectionIDFundParamKey" xml:space="preserve">
    <value>Categoria de dados</value>
  </data>
  <data name="lblCompanies" xml:space="preserve">
    <value>Empresas</value>
  </data>
  <data name="lblCompany" xml:space="preserve">
    <value>Companhia</value>
  </data>
  <data name="lblCompanyCarReportDesc" xml:space="preserve">
    <value>Fornece uma listagem dos números de carros da empresa por mês ou por frequência para um empregado ou unidade organizacional ou para todas as unidades num determinado nível.</value>
  </data>
  <data name="lblCompanyCarReportName" xml:space="preserve">
    <value>Relatório de Carro da Empresa</value>
  </data>
  <data name="lblCompanyContributions" xml:space="preserve">
    <value>Contribuições da empresa</value>
  </data>
  <data name="lblCompanyEffectiveDate" xml:space="preserve">
    <value>Data de vigência da empresa</value>
  </data>
  <data name="lblCompanyFrequencyName" xml:space="preserve">
    <value>Empresa - Frequência</value>
  </data>
  <data name="lblCompanyGroup" xml:space="preserve">
    <value>Grupo de empresas</value>
  </data>
  <data name="lblCompanyGroupIDParamNameKey" xml:space="preserve">
    <value>ID do grupo da empresa</value>
  </data>
  <data name="lblCompanyGroupReport" xml:space="preserve">
    <value>Nível do grupo de empresas</value>
  </data>
  <data name="lblCompanyGroups" xml:space="preserve">
    <value>Grupos de empresas</value>
  </data>
  <data name="lblCompanyLeaveDetailsIDParamNameKey" xml:space="preserve">
    <value>Esquema de licença</value>
  </data>
  <data name="lblCompanyName" xml:space="preserve">
    <value>Nome da Empresa</value>
  </data>
  <data name="lblCompanyRegNo" xml:space="preserve">
    <value>Nº de registro da empresa</value>
  </data>
  <data name="lblCompanyReport" xml:space="preserve">
    <value>Nível da empresa</value>
  </data>
  <data name="lblCompanyRequired" xml:space="preserve">
    <value>É necessária uma empresa</value>
  </data>
  <data name="lblComponent" xml:space="preserve">
    <value>Rubricas</value>
  </data>
  <data name="lblComponentCode" xml:space="preserve">
    <value>Componentes</value>
  </data>
  <data name="lblComponentCodes" xml:space="preserve">
    <value>Lista de componentes</value>
  </data>
  <data name="lblComponentCodesParamNameKey" xml:space="preserve">
    <value>Lista de componentes</value>
  </data>
  <data name="lblComponentIDParamNameKey" xml:space="preserve">
    <value>Componentes</value>
  </data>
  <data name="lblComponentPostedUnitsDesc" xml:space="preserve">
    <value>Semelhante ao relatório de resumo do componente, no entanto, este relatório fornece detalhes específicos sobre unidades / horas / dias lançados para cada componente</value>
  </data>
  <data name="lblComponentPostedUnitsName" xml:space="preserve">
    <value>Unidades Postadas do Componente</value>
  </data>
  <data name="lblComponentReportDesc" xml:space="preserve">
    <value>Este relatório fornece uma lista de números de componentes por mês ou por execução para um funcionário ou qualquer unidade organizacional ou todas as unidades em um nível específico.</value>
  </data>
  <data name="lblComponentReportName" xml:space="preserve">
    <value>Relatório de Componentes</value>
  </data>
  <data name="lblComponentTotalsParamNameKey" xml:space="preserve">
    <value>Mostrar totais por componente?</value>
  </data>
  <data name="lblComponentVarianceReport-GoingAcrossDesc" xml:space="preserve">
    <value>Fornece uma comparação, mês a mês ou execução para executar uma lista vertical de todos os números de componentes para um período selecionado por funcionário.</value>
  </data>
  <data name="lblComponentVarianceReport-GoingAcrossName" xml:space="preserve">
    <value>Relatório de variação de componentes - indo além</value>
  </data>
  <data name="lblComponentVarianceReportDesc" xml:space="preserve">
    <value>Fornece uma listagem comparativa, mês a mês ou executada lado a lado, de todos os números de componentes para um período selecionado por funcionário e inclui uma coluna de diferença.</value>
  </data>
  <data name="lblComponentVarianceReportName" xml:space="preserve">
    <value>Relatório de variação de componentes</value>
  </data>
  <data name="lblComponentVarianceTotalsReportDesc" xml:space="preserve">
    <value>Fornece uma listagem comparativa, mês a mês ou execução a execução, lado a lado, de todos os números de componentes de um período selecionado.</value>
  </data>
  <data name="lblComponentVarianceTotalsReportName" xml:space="preserve">
    <value>Relatório totais de variação de componente</value>
  </data>
  <data name="lblConfirm" xml:space="preserve">
    <value>Confirmar</value>
  </data>
  <data name="lblConfirmReportOverride" xml:space="preserve">
    <value>Substituir relatório</value>
  </data>
  <data name="lblConfirmReportOverrideMessage" xml:space="preserve">
    <value>Já existe um relatório com o mesmo nome de arquivo, você deseja substituí-lo?</value>
  </data>
  <data name="lblConsolidatedDynamicEmployeeDetailsDesc" xml:space="preserve">
    <value>Fornece uma lista de vários campos predefinidos do funcionários que podem ser selecionados para todas as empresas do grupo.</value>
  </data>
  <data name="lblConsolidatedDynamicEmployeeDetailsName" xml:space="preserve">
    <value>Informações Dinâmicas Consolidadas por Funcionário.</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportDesc" xml:space="preserve">
    <value>Fornece valores mensais de todos as rubricas dos empregados para as empresas selecionadas dentro de um grupo</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportName" xml:space="preserve">
    <value>Relatório consolidado de reconciliação da folha de pagamento</value>
  </data>
  <data name="lblConsolidatedPayrollReconciliationReportNameBra" xml:space="preserve">
    <value>Relatório Consolidado de Reconciliação da Folha de Pagamento por Estabelecimento</value>
  </data>
  <data name="lblCostCentre" xml:space="preserve">
    <value>Centro de Custo</value>
  </data>
  <data name="lblCostCentreDescription" xml:space="preserve">
    <value>Descrição do Centro de Custo</value>
  </data>
  <data name="lblCostingPayrollReconcilliationReportDesc" xml:space="preserve">
    <value>Fornece um relatório de reconciliação da folha de pagamento com um detalhamento de cada centro de custo por funcionário.</value>
  </data>
  <data name="lblCostingPayrollReconcilliationReportName" xml:space="preserve">
    <value>Relatório de reconciliação da folha de pagamento de cálculo de custos</value>
  </data>
  <data name="lblCountries" xml:space="preserve">
    <value>Países</value>
  </data>
  <data name="lblCreated" xml:space="preserve">
    <value>Criado</value>
  </data>
  <data name="lblCsvExportQuoteStringsWithSeparators" xml:space="preserve">
    <value>Strings de aspas com separadores?</value>
  </data>
  <data name="lblCsvExportSeparator" xml:space="preserve">
    <value>Separador</value>
  </data>
  <data name="lblCsvExportSkipEmptyColumns" xml:space="preserve">
    <value>Ignorar colunas vazias</value>
  </data>
  <data name="lblCsvExportSkipEmptyRows" xml:space="preserve">
    <value>Ignorar linhas vazias</value>
  </data>
  <data name="lblCurrency" xml:space="preserve">
    <value>Moeda</value>
  </data>
  <data name="lblCurrencyIDParamNameKey" xml:space="preserve">
    <value>Extrair a configuração de arquivos bancários em moedas específicas</value>
  </data>
  <data name="lblCurrent" xml:space="preserve">
    <value>Atual</value>
  </data>
  <data name="lblCurrentOrMtdOrYtd" xml:space="preserve">
    <value>Execução/mês</value>
  </data>
  <data name="lblCurrentReport" xml:space="preserve">
    <value>Relatório atual</value>
  </data>
  <data name="lblCustomFormsReportDesc" xml:space="preserve">
    <value>Recupere detalhes históricos dinâmicos de funcionários em uma data específica para uma categoria de dados específica.</value>
  </data>
  <data name="lblCustomFormsReportName" xml:space="preserve">
    <value>Relatório de formulários personalizados</value>
  </data>
  <data name="lblCustomizableReport" xml:space="preserve">
    <value>Relatório personalizável</value>
  </data>
  <data name="lblCustomPayslips" xml:space="preserve">
    <value>Recibos de vencimento personalizados</value>
  </data>
  <data name="lblCustomReport" xml:space="preserve">
    <value>Relatório personalizado</value>
  </data>
  <data name="lblCustomReportNameExists" xml:space="preserve">
    <value>O nome do relatório já existe para a categoria especificada.</value>
  </data>
  <data name="lblCustomReportTabName" xml:space="preserve">
    <value>Costume</value>
  </data>
  <data name="lblDateComplete" xml:space="preserve">
    <value>Data de conclusão</value>
  </data>
  <data name="lblDateRange" xml:space="preserve">
    <value>Este relatório não pode ser executado por um período superior a 13 meses.  Ajuste o intervalo de datas de acordo</value>
  </data>
  <data name="lblDeductions" xml:space="preserve">
    <value>Deduções</value>
  </data>
  <data name="lblDefault" xml:space="preserve">
    <value>Inadimplência</value>
  </data>
  <data name="lblDefaultCurrencyParamNameKey" xml:space="preserve">
    <value>Extrair a configuração de arquivos bancários em moedas específicas</value>
  </data>
  <data name="lblDefaultFormatSelect" xml:space="preserve">
    <value>Formatado</value>
  </data>
  <data name="lblDeleteConfirmation" xml:space="preserve">
    <value>Tem certeza de que deseja excluir este relatório?</value>
  </data>
  <data name="lblDeleteReport" xml:space="preserve">
    <value>Excluir relatório</value>
  </data>
  <data name="lblDeletionFailed" xml:space="preserve">
    <value>Ocorreu um erro inesperado ao tentar excluir o relatório, tente novamente mais tarde.</value>
  </data>
  <data name="lblDeletionSuccessful" xml:space="preserve">
    <value>O relatório foi excluído com sucesso.</value>
  </data>
  <data name="lblDescription" xml:space="preserve">
    <value>Descrição</value>
  </data>
  <data name="lblDescriptionRequired" xml:space="preserve">
    <value>Descrição</value>
  </data>
  <data name="lblDisplayOrientationParamNameKey" xml:space="preserve">
    <value>Orientação</value>
  </data>
  <data name="lblDivisionParamNameKey" xml:space="preserve">
    <value>Divisão</value>
  </data>
  <data name="lblDoNotHaveEmailAddresses" xml:space="preserve">
    <value>Excluir emps com endereços de e-mail</value>
  </data>
  <data name="lblDontGroupByTotals" xml:space="preserve">
    <value>Sem totais</value>
  </data>
  <data name="lblDT0107aDesc" xml:space="preserve">
    <value>Este é o cronograma mensal de dedução fiscal do empregador para PAYE</value>
  </data>
  <data name="lblDT0107aName" xml:space="preserve">
    <value>DT0107a</value>
  </data>
  <data name="lblDynamicEmployeeDetailsDesc" xml:space="preserve">
    <value>Fornece uma lista de vários campos predefinidos de funcionários que podem ser selecionados por um usuário.</value>
  </data>
  <data name="lblDynamicEmployeeDetailsName" xml:space="preserve">
    <value>Detalhes dinâmicos do funcionário</value>
  </data>
  <data name="lblEditableReport" xml:space="preserve">
    <value>Editável</value>
  </data>
  <data name="lblEditReport" xml:space="preserve">
    <value>Editar relatório</value>
  </data>
  <data name="lblEffectiveDate" xml:space="preserve">
    <value>Data Efetiva</value>
  </data>
  <data name="lblEffectiveDateParamKey" xml:space="preserve">
    <value>Data de vigência</value>
  </data>
  <data name="lbLeg_Canada_Advice_of_DebitName" xml:space="preserve">
    <value>Aviso de débito (Executar)</value>
  </data>
  <data name="lblEmailReport" xml:space="preserve">
    <value>Relatório por e-mail?</value>
  </data>
  <data name="lblEMP201BreakDownDesc" xml:space="preserve">
    <value>Fornece uma lista dos números de funcionários que compõem os números do relatório EMP201.</value>
  </data>
  <data name="lblEMP201BreakDownName" xml:space="preserve">
    <value>EMP201 Detalhamento</value>
  </data>
  <data name="lblEMP201ViewDesc" xml:space="preserve">
    <value>Fornece relatório EMP201 para a empresa mensalmente.</value>
  </data>
  <data name="lblEMP201ViewName" xml:space="preserve">
    <value>EMP201</value>
  </data>
  <data name="lblEmplNo" xml:space="preserve">
    <value>EmplNo</value>
  </data>
  <data name="lblEmplNumber" xml:space="preserve">
    <value>Empl. Número</value>
  </data>
  <data name="lblEmployeeActionTypeHistoryDesc" xml:space="preserve">
    <value>Fornece uma lista de tipos de ação para funcionários entre o intervalo de datas selecionadas.</value>
  </data>
  <data name="lblEmployeeActionTypeHistoryName" xml:space="preserve">
    <value>Histórico de Tipo de Ação</value>
  </data>
  <data name="lblEmployeeDependantsListingDesc" xml:space="preserve">
    <value>Fornece uma lista de dependentes com os seus dados básicos.</value>
  </data>
  <data name="lblEmployeeDependantsListingName" xml:space="preserve">
    <value>Listagem de Dependentes</value>
  </data>
  <data name="lblEmployeeFinancialHousePaymentsDesc" xml:space="preserve">
    <value>Fornece um relatório de pagamentos por banco.</value>
  </data>
  <data name="lblEmployeeFinancialHousePaymentsName" xml:space="preserve">
    <value>Relatório de Pagamentos de Empregado por Banco</value>
  </data>
  <data name="lblEmployeeListingDesc" xml:space="preserve">
    <value>Fornece uma lista dos funcionários.</value>
  </data>
  <data name="lblEmployeeListingName" xml:space="preserve">
    <value>Lista de funcionários</value>
  </data>
  <data name="lblEmployeeNumber" xml:space="preserve">
    <value>Matrícula</value>
  </data>
  <data name="lblEmployeePayslipDesc" xml:space="preserve">
    <value>Fornece uma lista dos recibos de vencimento dos funcionários para uma execução escolhida.</value>
  </data>
  <data name="lblEmployeePayslipName" xml:space="preserve">
    <value>Recibos de vencimento</value>
  </data>
  <data name="lblEmployees" xml:space="preserve">
    <value>Empregados</value>
  </data>
  <data name="lblEmployeeSuspensionDesc" xml:space="preserve">
    <value>Fornece uma lista de todos os funcionários afastados na data selecionada (exceto férias).</value>
  </data>
  <data name="lblEmployeeSuspensionName" xml:space="preserve">
    <value>Afastamentos do Funcionário</value>
  </data>
  <data name="lblEmpNoParamNameKey" xml:space="preserve">
    <value>Número do funcionário</value>
  </data>
  <data name="lblEmpStatus" xml:space="preserve">
    <value>Situação</value>
  </data>
  <data name="lblEndDateParamNameKey" xml:space="preserve">
    <value>Data final</value>
  </data>
  <data name="lblEngagementDate" xml:space="preserve">
    <value>Data de Noivado</value>
  </data>
  <data name="lblETIUtilisedParamNameKey" xml:space="preserve">
    <value>Valor utilizado da ETI</value>
  </data>
  <data name="lblExclDefSortParamNameKey" xml:space="preserve">
    <value>Excluir unidade organizacional, ponto de pagamento e classificação do projeto (classifique apenas na opção selecionada acima)</value>
  </data>
  <data name="lblExclOrgUnitGroupingParamNameKey" xml:space="preserve">
    <value>Excluir agrupamento de unidades organizacionais?</value>
  </data>
  <data name="lblExcludeEESParamNameKey" xml:space="preserve">
    <value>Incluir apenas funcionários que NÃO possuem endereços de email?</value>
  </data>
  <data name="lblExcludePaid" xml:space="preserve">
    <value>Excluir recibos de vencimento pagos</value>
  </data>
  <data name="lblExecuteReport" xml:space="preserve">
    <value>Executar relatório</value>
  </data>
  <data name="lblExpandAllBtn" xml:space="preserve">
    <value>Expandir</value>
  </data>
  <data name="lblFavouriteReportTabName" xml:space="preserve">
    <value>Favoritos</value>
  </data>
  <data name="lblFilledCountTypeParamNameKey" xml:space="preserve">
    <value>Contagem cheia como</value>
  </data>
  <data name="lblFilledCurrentCount" xml:space="preserve">
    <value>Preencher atual</value>
  </data>
  <data name="lblFilledEndOfPeriodCount" xml:space="preserve">
    <value>Preencher final do período</value>
  </data>
  <data name="lblFilters" xml:space="preserve">
    <value>Aplicar filtros</value>
  </data>
  <data name="lblFirstName" xml:space="preserve">
    <value>Nome próprio</value>
  </data>
  <data name="lblfkFilterCurrencyIDParamNameKey" xml:space="preserve">
    <value>Moeda</value>
  </data>
  <data name="lblfkForeingCurrencyIDParamNameKey" xml:space="preserve">
    <value>Moeda</value>
  </data>
  <data name="lblfkReportIDParamNameKey" xml:space="preserve">
    <value>Visualizar apenas os funcionários que estão anexados a esse holerite</value>
  </data>
  <data name="lblForeingCurrencyParamNameKey" xml:space="preserve">
    <value>Moeda</value>
  </data>
  <data name="lblFormatPerLine" xml:space="preserve">
    <value>Por linha</value>
  </data>
  <data name="lblFormatPivotHorizontally" xml:space="preserve">
    <value>Girar horizontalmente</value>
  </data>
  <data name="lblFormatSelection" xml:space="preserve">
    <value>Formato</value>
  </data>
  <data name="lblFormatSelectionNotSupported" xml:space="preserve">
    <value>Esta opção não é suportada</value>
  </data>
  <data name="lblFreqeuncyParamKey" xml:space="preserve">
    <value>Empresa - lista de frequências</value>
  </data>
  <data name="lblFrequency" xml:space="preserve">
    <value>Frequência</value>
  </data>
  <data name="lblFrequencyId" xml:space="preserve">
    <value>Frequência</value>
  </data>
  <data name="lblFrequencyIdConditionalParamNameKey" xml:space="preserve">
    <value>Executar relatório apenas para a frequência atual</value>
  </data>
  <data name="lblFrequencyIds" xml:space="preserve">
    <value>Frequência</value>
  </data>
  <data name="lblFringeBenefits" xml:space="preserve">
    <value>Benefícios adicionais</value>
  </data>
  <data name="lblFromDateNameKey" xml:space="preserve">
    <value>Incluir rescisões a partir desta data</value>
  </data>
  <data name="lblGarnisheeReportDesc" xml:space="preserve">
    <value>Fornece uma lista dos funcionários que a Garnishee pede para um período escolhido.</value>
  </data>
  <data name="lblGarnisheeReportName" xml:space="preserve">
    <value>Guarnição</value>
  </data>
  <data name="lblGeneralLedgerReportDesc" xml:space="preserve">
    <value>Fornece uma lista GL das entradas para uma execução ou período escolhido.</value>
  </data>
  <data name="lblGeneralLedgerReportName" xml:space="preserve">
    <value>Relatório do arquivo contábil</value>
  </data>
  <data name="lblGenerationCompleted" xml:space="preserve">
    <value>Relatório gerado</value>
  </data>
  <data name="lblGenerationFailed" xml:space="preserve">
    <value>Ocorreu um erro inesperado ao gerar seu relatório, tente novamente mais tarde.</value>
  </data>
  <data name="lblGenericTaxCertificateDesc" xml:space="preserve">
    <value>Gere informações de folha de pagamento acumuladas no ano para um ano fiscal específico. Certifique-se de ter configurado os parâmetros visitando a tela Configuração do certificado fiscal (Configuração &gt; Configurações básicas &gt; Configurações legislativas &gt; Configuração do certificado fiscal).</value>
  </data>
  <data name="lblGenericTaxCertificateName" xml:space="preserve">
    <value>Certidão Fiscal Genérica</value>
  </data>
  <data name="lblGhana_DT0107_Monthly_PAYEDesc" xml:space="preserve">
    <value>Retorno mensal de deduções PYE.</value>
  </data>
  <data name="lblGhana_DT0107_Monthly_PAYEName" xml:space="preserve">
    <value>DT0107</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier1Desc" xml:space="preserve">
    <value>Este relatório indica o valor das contribuições mensais feitas para o esquema SSNIT Tier 1. O relatório deve ser extraído em um Excel (formato xlsx).</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier1Name" xml:space="preserve">
    <value>Relatório de Contribuição SSNIT Nível 1</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier2Desc" xml:space="preserve">
    <value>Esta é uma declaração mensal que pode ser usada para reconciliar os valores relatados para o esquema SSNIT Tier 2 e pode ser fornecida aos curadores relevantes. O relatório deve ser extraído em um Excel (formato xlsx).</value>
  </data>
  <data name="lblGhana_SSNIT_Contribution_Tier2Name" xml:space="preserve">
    <value>Relatório de Contribuição SSNIT Nível 2</value>
  </data>
  <data name="lblGoToDesigner" xml:space="preserve">
    <value>Desenhista</value>
  </data>
  <data name="lblGoToViewer" xml:space="preserve">
    <value>Visualizador</value>
  </data>
  <data name="lblGrade" xml:space="preserve">
    <value>Grau</value>
  </data>
  <data name="lblGrandTotalsfor" xml:space="preserve">
    <value>Totais gerais para</value>
  </data>
  <data name="lblGroupAllByOrgUnit" xml:space="preserve">
    <value>Todo</value>
  </data>
  <data name="lblGroupByComponent" xml:space="preserve">
    <value>Componente</value>
  </data>
  <data name="lblGroupByGLNumber" xml:space="preserve">
    <value>Número Gl</value>
  </data>
  <data name="lblHeadcountComparisonDesc" xml:space="preserve">
    <value>Planejar o período orçamentário futuro comparando o orçamento aprovado anterior (arquivado) com o atual como está e as alterações planejadas / previstas (novo período)</value>
  </data>
  <data name="lblHeadcountComparisonName" xml:space="preserve">
    <value>Comparação de funcionários</value>
  </data>
  <data name="lblHideDetailParamNameKey" xml:space="preserve">
    <value>Ocultar detalhes?</value>
  </data>
  <data name="lblhideRateParamNameKey" xml:space="preserve">
    <value>Ocultar avaliador</value>
  </data>
  <data name="lblHistory" xml:space="preserve">
    <value>Histórico</value>
  </data>
  <data name="lblHistoryParamNameKey" xml:space="preserve">
    <value>Incluir histórico</value>
  </data>
  <data name="lblHourlyRate" xml:space="preserve">
    <value>Taxa horária</value>
  </data>
  <data name="lblIDNumber" xml:space="preserve">
    <value>Número de identificação</value>
  </data>
  <data name="lblImage" xml:space="preserve">
    <value>imagem</value>
  </data>
  <data name="lblIncludeEngTerm_InThisPeriodParamNameKey" xml:space="preserve">
    <value>Incluir novos compromissos e rescisões capturados neste período</value>
  </data>
  <data name="lblIncludeTerminatedParamKey" xml:space="preserve">
    <value>Incluir rescisões</value>
  </data>
  <data name="lblIncludeZeroNetPayParamNameKey" xml:space="preserve">
    <value>Incluir holerites de pagamento líquido zero?</value>
  </data>
  <data name="lblIncome" xml:space="preserve">
    <value>Rendimento</value>
  </data>
  <data name="lblIndia_IT_Savings_Report_For_Previous_EmploymentDesc" xml:space="preserve">
    <value>Este relatório é usado para capturar os detalhes da renda do emprego anterior, se registrados para um funcionário em qualquer exercício financeiro selecionado</value>
  </data>
  <data name="lblIndia_IT_Savings_Report_For_Previous_EmploymentName" xml:space="preserve">
    <value>Relatório de economia de TI para empregos anteriores</value>
  </data>
  <data name="lblIndia_P29_IT_Savings_ReportsDesc" xml:space="preserve">
    <value>Este relatório fornece informações sobre a declaração de TI real aplicável ao(s) funcionário(s).</value>
  </data>
  <data name="lblIndia_P29_IT_Savings_ReportsName" xml:space="preserve">
    <value>Relatório de Poupança de Imposto de Renda</value>
  </data>
  <data name="lblIndia_P3_GPDesc" xml:space="preserve">
    <value>Este relatório fornece detalhes sobre as contribuições mensais de funcionários e empregadores para a ESIC (Employee's State Insurance Corporation), conforme aplicável.</value>
  </data>
  <data name="lblIndia_P3_GPName" xml:space="preserve">
    <value>Declaração Mensal ESI</value>
  </data>
  <data name="lblIndia_P38_IT_Savings_Other_Report_Monthly_RentDesc" xml:space="preserve">
    <value>Fornece o aluguel mensal pago pelos funcionários durante o ano fiscal.</value>
  </data>
  <data name="lblIndia_P38_IT_Savings_Other_Report_Monthly_RentName" xml:space="preserve">
    <value>Relatório de Poupanças de TI - Renda Mensal</value>
  </data>
  <data name="lblIndia_P39_IT_Savings_Others_Report_Other_IncomeDesc" xml:space="preserve">
    <value>Fornece os detalhes da renda recebida pelo funcionário devido a Outras Receitas, conforme atualizado na declaração de TI e a renda da propriedade da casa (seja lucro/perda) incorrida a um funcionário</value>
  </data>
  <data name="lblIndia_P39_IT_Savings_Others_Report_Other_IncomeName" xml:space="preserve">
    <value>Relatório de Outros Poupanças de TI - Outras Receitas</value>
  </data>
  <data name="lblIndia_P6_Hold_Release_ReportDesc" xml:space="preserve">
    <value>Este relatório fornece dados do(s) funcionário(s) para o(s) qual(is) o salário foi suspenso por motivos como fuga, período de aviso prévio ou informações vitais ausentes para liberação de salário, etc.</value>
  </data>
  <data name="lblIndia_P6_Hold_Release_ReportName" xml:space="preserve">
    <value>Relatório de Salário de Retenção e Liberação</value>
  </data>
  <data name="lblIsTotalsParamNameKey" xml:space="preserve">
    <value>Relatório de execução</value>
  </data>
  <data name="lblIsTotalsPerEmp" xml:space="preserve">
    <value>Totais por empregado</value>
  </data>
  <data name="lblIsTotalsPerOrgUnit" xml:space="preserve">
    <value>Totais por unidade organizacional</value>
  </data>
  <data name="lblLastName" xml:space="preserve">
    <value>Apelido</value>
  </data>
  <data name="lblLeave" xml:space="preserve">
    <value>Deixar</value>
  </data>
  <data name="lblLeaveTypeIDParamNameKey" xml:space="preserve">
    <value>Tipo de férias</value>
  </data>
  <data name="lblLeg_Angola_IRTDesc" xml:space="preserve">
    <value>Tabela de tributos a acompanhar a Declaração Anual Modelo 2 (Grupo A). O arquivo deve ser exportado para o Excel para importação no portal do empregador da autoridade fiscal.</value>
  </data>
  <data name="lblLeg_Angola_IRTName" xml:space="preserve">
    <value>TRI Anual - Modelo 2 (Grupo A)</value>
  </data>
  <data name="lblLeg_Benin_Monthly_IRPP_TSDesc" xml:space="preserve">
    <value>Um relatório resumido que reflete o valor mensal de ITS pago por um mês.</value>
  </data>
  <data name="lblLeg_Benin_Monthly_IRPP_TSName" xml:space="preserve">
    <value>Declaração IRPP - TS</value>
  </data>
  <data name="lblLeg_Benin_Monthly_VPSDesc" xml:space="preserve">
    <value>Uma declaração resumida das contribuições do VPS.</value>
  </data>
  <data name="lblLeg_Benin_Monthly_VPSName" xml:space="preserve">
    <value>Formulário de Declaração Mensal para VPS</value>
  </data>
  <data name="lblLeg_Botswana_ITW7ADesc" xml:space="preserve">
    <value>O retorno mensal de remessa PAYE. Este é o arquivo de envio eletrônico mensal do BURS em formato .csv para o mês selecionado.</value>
  </data>
  <data name="lblLeg_Botswana_ITW7AName" xml:space="preserve">
    <value>Arquivo de envio eletrônico ITW7A (válido a partir de janeiro de 2024)</value>
  </data>
  <data name="lblLeg_BRA_Analytical_13th_Salary_ProvisionDesc" xml:space="preserve">
    <value>Relatório Analítico de Provisão de 13o Salário</value>
  </data>
  <data name="lblLeg_BRA_Analytical_13th_Salary_ProvisionName" xml:space="preserve">
    <value>Relatório Analítico de Provisão de 13o Salário</value>
  </data>
  <data name="lblLeg_BRA_NH18_Leave_Pre_WarningDesc" xml:space="preserve">
    <value>Aviso Prévio de Férias Report</value>
  </data>
  <data name="lblLeg_BRA_NH18_Leave_Pre_WarningName" xml:space="preserve">
    <value>Aviso Prévio de Férias Report</value>
  </data>
  <data name="lblLeg_BRA_NH19_Vacation_ScheduleDesc" xml:space="preserve">
    <value>Programação de Férias para a competência selecionada</value>
  </data>
  <data name="lblLeg_BRA_NH19_Vacation_ScheduleName" xml:space="preserve">
    <value>Programação de Férias</value>
  </data>
  <data name="lblLeg_BRA_NH20_Vacation_PayslipDesc" xml:space="preserve">
    <value>Aviso e Recibo de Férias</value>
  </data>
  <data name="lblLeg_BRA_NH20_Vacation_PayslipName" xml:space="preserve">
    <value>Aviso e Recibo de Férias</value>
  </data>
  <data name="lblLeg_BRA_NH21_Unpaid_LeaveDesc" xml:space="preserve">
    <value>Demonstrativo de Falta no Período - Férias</value>
  </data>
  <data name="lblLeg_BRA_NH21_Unpaid_LeaveName" xml:space="preserve">
    <value>Demonstrativo de Falta no Período - Férias</value>
  </data>
  <data name="lblLeg_BRA_NH21Desc" xml:space="preserve">
    <value>Demonstrativo de Falta no Período - Férias OLD</value>
  </data>
  <data name="lblLeg_BRA_NH21Name" xml:space="preserve">
    <value>Demonstrativo de Falta no Período - Férias OLD</value>
  </data>
  <data name="lblLeg_BRA_NH50_Payroll_RegisterDesc" xml:space="preserve">
    <value>Folha de Pagamento - Analítico</value>
  </data>
  <data name="lblLeg_BRA_NH50_Payroll_RegisterName" xml:space="preserve">
    <value>Folha de Pagamento - Analítico</value>
  </data>
  <data name="lblLeg_BRA_NH53_Net_Salary_AlimonyDesc" xml:space="preserve">
    <value>Relação de Líquidos - Analítica - Pensionistas</value>
  </data>
  <data name="lblLeg_BRA_NH53_Net_Salary_AlimonyName" xml:space="preserve">
    <value>Relação de Líquidos - Analítica - Pensionistas</value>
  </data>
  <data name="lblLeg_BRA_NH55_0_Net_PayDesc" xml:space="preserve">
    <value>Este relatório demonstra empregados sem folha de pagamento e/ou com Liquido Zero</value>
  </data>
  <data name="lblLeg_BRA_NH55_0_Net_PayName" xml:space="preserve">
    <value>Empregados com o Líquido Zero</value>
  </data>
  <data name="lblLeg_BRA_NH62_Provision_VacationDesc" xml:space="preserve">
    <value>Demonstrativo de provisões Folha de Pagamento Analitico</value>
  </data>
  <data name="lblLeg_BRA_NH62_Provision_VacationName" xml:space="preserve">
    <value>Demonstrativo de provisões Folha de Pagamento Analitico</value>
  </data>
  <data name="lblLeg_Bra_NH622_ReportDesc" xml:space="preserve">
    <value>O relatório exibe, por localidade, estabelecimento e empresa, os valores alocados à provisão de férias para o período selecionado.</value>
  </data>
  <data name="lblLeg_Bra_NH622_ReportName" xml:space="preserve">
    <value>Relatório de Resumo de Provisão de Férias</value>
  </data>
  <data name="lblLeg_BRA_NT36_1_TQRCTDesc" xml:space="preserve">
    <value>Termo de Quitação de Rescisão do Contrato de Trabalho</value>
  </data>
  <data name="lblLeg_BRA_NT36_1_TQRCTName" xml:space="preserve">
    <value>TQRCT</value>
  </data>
  <data name="lblLeg_BRA_NT36_3_Auxilary_Termination_PayslipDesc" xml:space="preserve">
    <value>Relatório Analítico de Rescisão</value>
  </data>
  <data name="lblLeg_BRA_NT36_3_Auxilary_Termination_PayslipName" xml:space="preserve">
    <value>Relatório Analítico de Rescisão</value>
  </data>
  <data name="lblLeg_BRA_NT58_DARFDesc" xml:space="preserve">
    <value>Documento de Arrecadação de Receitas Federais - DARF (IRFF)</value>
  </data>
  <data name="lblLeg_BRA_NT58_DARFName" xml:space="preserve">
    <value>DARF (IRFF)</value>
  </data>
  <data name="lblLeg_BRA_NT58A_DARFDesc" xml:space="preserve">
    <value>Documento de Arrecadação de Receitas Federais - DARF (IRFF) por Empresa</value>
  </data>
  <data name="lblLeg_BRA_NT58A_DARFName" xml:space="preserve">
    <value>DARF Consolidado por Empresa</value>
  </data>
  <data name="lblLeg_BRA_NT60_Tax_DetailDesc" xml:space="preserve">
    <value>Analítico de Imposto de Renda</value>
  </data>
  <data name="lblLeg_BRA_NT60_Tax_DetailName" xml:space="preserve">
    <value>Analítico de Imposto de Renda</value>
  </data>
  <data name="lblLeg_BRA_NT61_Tax_SummaryDesc" xml:space="preserve">
    <value>Sintético de Imposto de Renda</value>
  </data>
  <data name="lblLeg_BRA_NT61_Tax_SummaryName" xml:space="preserve">
    <value>Sintético de Imposto de Renda</value>
  </data>
  <data name="lblLeg_Brazil_NH22_1_Variable_AmountDesc" xml:space="preserve">
    <value>Demonstra a lista de rubricas utilizadas para o cálculo das médias dos valores para o intervalo de datas selecionadas.</value>
  </data>
  <data name="lblLeg_Brazil_NH22_1_Variable_AmountName" xml:space="preserve">
    <value>Demonstrativo de Bases - Valores</value>
  </data>
  <data name="lblLeg_Brazil_NH22_BalancesDesc" xml:space="preserve">
    <value>Demonstra a lista de rubricas utilizadas para o cálculo das médias de unidades para o intervalo de datas selecionadas</value>
  </data>
  <data name="lblLeg_Brazil_NH22_BalancesName" xml:space="preserve">
    <value>Demonstrativo de Bases - Unidades</value>
  </data>
  <data name="lblLeg_Brazil_NH62_4_13th_SalaryDesc" xml:space="preserve">
    <value>Resumo de Provisão de 13o Salário</value>
  </data>
  <data name="lblLeg_Brazil_NH62_4_13th_SalaryName" xml:space="preserve">
    <value>Resumo de Provisão de 13o Salário</value>
  </data>
  <data name="lblLeg_Brazil_NH7_Employee_Registration_FormDesc" xml:space="preserve">
    <value>Ficha de Registro de Empregado</value>
  </data>
  <data name="lblLeg_Brazil_NH7_Employee_Registration_FormName" xml:space="preserve">
    <value>Ficha de Registro de Empregado</value>
  </data>
  <data name="lblLeg_Brazil_NT36_2_THRCTDesc" xml:space="preserve">
    <value>Termo de Homologação de Rescisão do Contrato de Trabalho</value>
  </data>
  <data name="lblLeg_Brazil_NT36_2_THRCTName" xml:space="preserve">
    <value>THRCT</value>
  </data>
  <data name="lblLeg_Brazil_NT38_1_Company_Dismissing_EmpDesc" xml:space="preserve">
    <value>Comunicação Aviso Prévio Trabalhado</value>
  </data>
  <data name="lblLeg_Brazil_NT38_1_Company_Dismissing_EmpName" xml:space="preserve">
    <value>Comunicação Aviso Prévio Trabalhado</value>
  </data>
  <data name="lblLeg_Brazil_NT38Desc" xml:space="preserve">
    <value>Aviso Prévio Indenizado</value>
  </data>
  <data name="lblLeg_Brazil_NT38Name" xml:space="preserve">
    <value>Aviso Prévio Indenizado</value>
  </data>
  <data name="lblLeg_Burkina_Faso_Summary_DeclarationDesc" xml:space="preserve">
    <value>O relatório de declaração resumida dos funcionários é um relatório abrangente da Previdência Social, que categoriza os diferentes tipos de funcionários e indica as várias contribuições. Se a empresa tiver pelo menos 20 funcionários, o relatório e o pagamento devem ser apresentados trimestralmente, se houver mais de 20 funcionários, o relatório deve ser enviado mensalmente.</value>
  </data>
  <data name="lblLeg_Burkina_Faso_Summary_DeclarationName" xml:space="preserve">
    <value>Declaração sumária dos trabalhadores</value>
  </data>
  <data name="lblLeg_Burundi_Monthly_Tax_Declaration_FormDesc" xml:space="preserve">
    <value>O formulário de declaração mensal de Imposto sobre Valor Agregado (IVA), IRE, deduções na fonte e várias deduções fixas. Somente a seção de cabeçalho na página 1, página 4 e campo 4 na página 6 retornará dados da folha de pagamento. O relatório deve ser baixado no Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Burundi_Monthly_Tax_Declaration_FormName" xml:space="preserve">
    <value>Formulário de Declaração de Imposto Mensal</value>
  </data>
  <data name="lblLeg_Cam_27ADesc" xml:space="preserve">
    <value>O relatório anual do DIPE - guia C1-NOTE27A. Reportagem sobre IRPP, Credit Foncier du Cameroun (CFC), Imposto de Desenvolvimento Local e Radiodifusão Televisão Camaronesa (CRTV). Os empregadores são obrigados a apresentar o DIPE Anual como parte da Declaração Estatística e Fiscal (DSF). Este relatório será utilizado para copiar e colar informações na declaração anual da Dêclaration Stastiuque et de Fiscale (DSF) no separador C1-NOTE27A. O relatório deve ser baixado no Excel (xlsx).</value>
  </data>
  <data name="lblLeg_Cam_27AName" xml:space="preserve">
    <value>DIPE Anual</value>
  </data>
  <data name="lblLeg_Canada_Advice_of_Debit_MonthlyDesc" xml:space="preserve">
    <value>Um relatório que detalha os detalhes da Remessa a serem pagos às autoridades competentes por mês.</value>
  </data>
  <data name="lblLeg_Canada_Advice_of_Debit_MonthlyName" xml:space="preserve">
    <value>Aviso de débito</value>
  </data>
  <data name="lblLeg_Canada_Advice_of_DebitDesc" xml:space="preserve">
    <value>Um relatório que detalha os detalhes da Remessa a serem pagos às autoridades competentes.</value>
  </data>
  <data name="lblLeg_Canada_T4_ER_CopyDesc" xml:space="preserve">
    <value>O relatório T4 Slip é usado para relatar com precisão a renda e os impostos à Agência de Receita do Canadá (CRA). Esta versão do relatório imprime uma cópia do Comprovante T4, juntamente com um Resumo T4.</value>
  </data>
  <data name="lblLeg_Canada_T4_ER_CopyName" xml:space="preserve">
    <value>Recibo T4 - Cópia do Empregador</value>
  </data>
  <data name="lblLeg_Canada_Workers_Compensation_SummaryDesc" xml:space="preserve">
    <value>Resumo da Compensação dos Trabalhadores</value>
  </data>
  <data name="lblLeg_Canada_Workers_Compensation_SummaryName" xml:space="preserve">
    <value>Resumo da Compensação dos Trabalhadores</value>
  </data>
  <data name="lblLeg_Canada_Workers_CompensationDesc" xml:space="preserve">
    <value>O relatório lista todos os funcionários que têm uma taxa de agência de trabalhadores atribuída e o valor calculado para as contribuições da agência.</value>
  </data>
  <data name="lblLeg_Canada_Workers_CompensationName" xml:space="preserve">
    <value>Compensação dos Trabalhadores</value>
  </data>
  <data name="lblLeg_CNSS_BNTS_ElectroniqueDesc" xml:space="preserve">
    <value>Arquivo eletrônico CNSS mensal e trimestral.</value>
  </data>
  <data name="lblLeg_CNSS_BNTS_ElectroniqueName" xml:space="preserve">
    <value>CNSS BNTS Eletrônico</value>
  </data>
  <data name="lblLeg_Congo_CNSS_DetailDesc" xml:space="preserve">
    <value>Este é um relatório detalhado que indica as várias contribuições sociais.</value>
  </data>
  <data name="lblLeg_Congo_CNSS_DetailName" xml:space="preserve">
    <value>Declaração mensal ou trimestral de vencimentos e contribuições</value>
  </data>
  <data name="lblLeg_Congo_Details_Tax_DeclarationDesc" xml:space="preserve">
    <value>Um relatório detalhado dos impostos (PIT, TUS, TOL e CAMU) deduzidos por funcionário. Este relatório pode ser utilizado em conjunto com o relatório «Bordereau Général de Versement».</value>
  </data>
  <data name="lblLeg_Congo_Details_Tax_DeclarationName" xml:space="preserve">
    <value>Detalhes da declaração de imposto</value>
  </data>
  <data name="lblLeg_Congo_Mon_CAMUDesc" xml:space="preserve">
    <value>Um relatório detalhado que indica a contribuição mensal dos funcionários para a CAMU.</value>
  </data>
  <data name="lblLeg_Congo_Mon_CAMUName" xml:space="preserve">
    <value>Declaração Mensal da CAMU</value>
  </data>
  <data name="lblLeg_DRC_Annual_Employee_Income_TaxDesc" xml:space="preserve">
    <value>Declaração anual de remunerações e impostos dos colaboradores.</value>
  </data>
  <data name="lblLeg_DRC_Annual_Employee_Income_TaxName" xml:space="preserve">
    <value>Certificado de imposto anual do funcionário</value>
  </data>
  <data name="lblLeg_DRC_Declaration_MensuelleDesc" xml:space="preserve">
    <value>Declaração consolidada mensal de impostos sobre trabalhadores, contribuições para a Segurança Social e Contribuições Patronais</value>
  </data>
  <data name="lblLeg_DRC_Declaration_MensuelleName" xml:space="preserve">
    <value>Declaração Mensal de Impostos, Contribuições para a Segurança Social e Contribuições Patronais</value>
  </data>
  <data name="lblLeg_DRC_Declaration_Mesuelle_ImpotDesc" xml:space="preserve">
    <value>Um relatório mensal indicando o imposto e o imposto excepcional sobre a remuneração, indicando também o pagamento do imposto nas diferentes províncias e categorias de emprego.</value>
  </data>
  <data name="lblLeg_DRC_Declaration_Mesuelle_ImpotName" xml:space="preserve">
    <value>Declaração Mensal de Imposto Profissional e Excepcional sobre Remunerações</value>
  </data>
  <data name="lblLeg_DRC_DMFP_SummaryDesc" xml:space="preserve">
    <value>Este arquivo fornece informações relativas às contribuições do CNSS para completar o relatório denominado Déclaration Mensuelle de La Feuille de Paie (DMFP) através do portal eletrônico.</value>
  </data>
  <data name="lblLeg_DRC_DMFP_SummaryName" xml:space="preserve">
    <value>Declarações Mensais de Folha de Pagamento (DMFP) - O modelo de folha de pagamento</value>
  </data>
  <data name="lblLeg_DRC_DMFPDesc" xml:space="preserve">
    <value>Trata-se de um arquivo que fornece as informações financeiras relativas à base de contribuição do CNSS para completar o relatório denominado Déclaration Mensuelle de La Feuille de Paie (DMFP) através do portal eletrônico.</value>
  </data>
  <data name="lblLeg_DRC_DMFPName" xml:space="preserve">
    <value>Declarações mensais de folha de pagamento (DMFP) - O modelo de detalhe do recibo de pagamento</value>
  </data>
  <data name="lblLeg_DRC_Fiche_IntercalaireName" xml:space="preserve">
    <value>Relatório Anual Suplementar</value>
  </data>
  <data name="lblLeg_Egypt_Monthly_Tax_ReturnDesc" xml:space="preserve">
    <value>Este relatório deve ser enviado eletronicamente à Autoridade Tributária Egípcia para declarar as informações mensais da folha de pagamento que afetam os impostos.</value>
  </data>
  <data name="lblLeg_Egypt_Monthly_Tax_ReturnName" xml:space="preserve">
    <value>Formulário de declaração de imposto mensal nº. (2)</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_1Desc" xml:space="preserve">
    <value>Este relatório deve ser apresentado à Autoridade Nacional de Seguro Social para registrar um novo funcionário.</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_1Name" xml:space="preserve">
    <value>Formulário de Segurança Social nº. (1)</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_2Desc" xml:space="preserve">
    <value>Este relatório deve ser apresentado à Autoridade Nacional de Seguro Social para relatar quaisquer alterações.</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_2Name" xml:space="preserve">
    <value>Formulário de Segurança Social nº. (2)</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_6Desc" xml:space="preserve">
    <value>Este relatório deve ser apresentado à Autoridade Nacional de Seguro Social quando um funcionário é demitido.</value>
  </data>
  <data name="lblLeg_Egypt_SocSec_Form_6Name" xml:space="preserve">
    <value>Formulário de Segurança Social nº. (6)</value>
  </data>
  <data name="lblLeg_Eswatini_PAYE_ReconDesc" xml:space="preserve">
    <value>O relatório é usado para reconciliar a remuneração dos funcionários, deduções permitidas, impostos deduzidos e outras informações relevantes para um mês específico.</value>
  </data>
  <data name="lblLeg_Eswatini_PAYE_ReconName" xml:space="preserve">
    <value>Entrada mensal de reconciliação PAYE</value>
  </data>
  <data name="lblLeg_Eswatini_Tax_CertificateDesc" xml:space="preserve">
    <value>Certificado Fiscal Anual emitido aos colaboradores.</value>
  </data>
  <data name="lblLeg_Eswatini_Tax_CertificateName" xml:space="preserve">
    <value>PAYE05 Certificado de Imposto de Empregado</value>
  </data>
  <data name="lblLeg_GAB_CNSSDesc" xml:space="preserve">
    <value>Retorno trimestral de salários para CNSS. A Déclaration trimestrielle des salaires (DTS) retorna na primeira guia e a Avis de declaration employeur (AVIS) retorna na segunda guia. Este relatório deve ser extraído em um formato Excel (xlsx). Se a 'Data de início da empresa' ou 'Data de vigência da empresa' não for igual à 'Data de criação da empresa' na tela Informações básicas da empresa, use as datas de substituição nos parâmetros do relatório.</value>
  </data>
  <data name="lblLeg_GAB_CNSSName" xml:space="preserve">
    <value>Déclaration trimestrielle des salaires CNSS</value>
  </data>
  <data name="lblLeg_Gabon_ID19Desc" xml:space="preserve">
    <value>Formulário de justificativa para salários, vencimentos, pensões e anuidades vitalícias ID19.</value>
  </data>
  <data name="lblLeg_Gabon_ID19Name" xml:space="preserve">
    <value>Certificado de fim de ano do imposto do funcionário</value>
  </data>
  <data name="lblLeg_Gabon_ID21_Salaries_PaidDesc" xml:space="preserve">
    <value>Um relatório anual detalhado indicando a remuneração auferida e os vários impostos deduzidos. Este relatório deve ser extraído em formato Adobe (pdf).</value>
  </data>
  <data name="lblLeg_Gabon_ID21_Salaries_PaidName" xml:space="preserve">
    <value>Relatório detalhado dos salários pagos aos funcionários ID21</value>
  </data>
  <data name="lblLeg_Gabon_ID22_Summary_StatementDesc" xml:space="preserve">
    <value>Um relatório anual de síntese que indica a remuneração auferida e os vários impostos deduzidos. O relatório retorna os totais de página do relatório ID21. Este relatório deve ser extraído em formato Adobe (pdf).</value>
  </data>
  <data name="lblLeg_Gabon_ID22_Summary_StatementName" xml:space="preserve">
    <value>Declaração de resumo ID22</value>
  </data>
  <data name="lblLeg_Gabon_Payroll_ID20Desc" xml:space="preserve">
    <value>Um relatório resumido indicando o número total de funcionários e a remuneração paga no ano fiscal.</value>
  </data>
  <data name="lblLeg_Gabon_Payroll_ID20Name" xml:space="preserve">
    <value>Demonstrativo do cálculo das folhas de pagamento ID20</value>
  </data>
  <data name="lblLeg_Gambia_NPF3_FormDesc" xml:space="preserve">
    <value>Calendário mensal das contribuições do Fundo Nacional de Previdência.</value>
  </data>
  <data name="lblLeg_Gambia_NPF3_FormName" xml:space="preserve">
    <value>Formulário de Aconselhamento de Remessa da Corporação de Financiamento da Previdência Social e Habitação (NPF3)</value>
  </data>
  <data name="lblLeg_India_Annexure_1Desc" xml:space="preserve">
    <value>Este relatório fornece os valores mensais de TDS para funcionários de um trimestre selecionado.</value>
  </data>
  <data name="lblLeg_India_Annexure_1Name" xml:space="preserve">
    <value>Anexo 1 Relatório</value>
  </data>
  <data name="lblLeg_India_Arrear_PFDesc" xml:space="preserve">
    <value>O formato de arquivo de texto necessário para carregar o PF no portal do PF quando os atrasos são aplicáveis.</value>
  </data>
  <data name="lblLeg_India_Arrear_PFName" xml:space="preserve">
    <value>Formato ECR PF traseiro</value>
  </data>
  <data name="lblLeg_India_Bank_Transfer_StatementDesc" xml:space="preserve">
    <value>Este relatório fornece informações sobre o salário mensal creditado na conta bancária do funcionário.</value>
  </data>
  <data name="lblLeg_India_Bank_Transfer_StatementName" xml:space="preserve">
    <value>Extrato de transferência bancária</value>
  </data>
  <data name="lblLeg_India_Form_16Desc" xml:space="preserve">
    <value>Carta de apresentação para o certificado do Formulário 16</value>
  </data>
  <data name="lblLeg_India_Form_16Name" xml:space="preserve">
    <value>Carta de apresentação do formulário 16</value>
  </data>
  <data name="lblLeg_India_Form_24QDesc_" xml:space="preserve">
    <value>O Formulário 24Q é um formulário de declaração TDS (Imposto Deduzido na Fonte) usado na Índia para relatar TDS sobre pagamentos de salários. É usado especificamente pelos empregadores para relatar as deduções de TDS feitas dos salários pagos aos funcionários. Este formulário deve ser preenchido trimestralmente.  Frequência de arquivamento: O formulário deve ser preenchido trimestralmente. Cada trimestre corresponde a um trimestre do exercício: 1º trimestre: abril a junho 2º trimestre: julho a setembro 3º trimestre: outubro a dezembro 4º trimestre: janeiro a março</value>
  </data>
  <data name="lblLeg_India_Form_24QName_" xml:space="preserve">
    <value>Formulário 24Q</value>
  </data>
  <data name="lblLeg_India_Form_CDesc" xml:space="preserve">
    <value>É um registro consolidado de bônus pagos mantido pelas empresas.</value>
  </data>
  <data name="lblLeg_India_Form_CName" xml:space="preserve">
    <value>Formulário C</value>
  </data>
  <data name="lblLeg_India_Form_Ddescr" xml:space="preserve">
    <value>Este é um registro consolidado do bônus estendido aos funcionários no exercício financeiro.</value>
  </data>
  <data name="lblLeg_India_Form_Dname" xml:space="preserve">
    <value>Formulário D</value>
  </data>
  <data name="lblLeg_India_Form12BDesc" xml:space="preserve">
    <value>O Formulário 12B é usado para fornecer detalhes sobre a renda e as deduções fiscais de um funcionário que mudou de emprego durante o ano financeiro. Ele é usado para fins de imposto de renda para garantir que a renda obtida de vários empregadores seja devidamente contabilizada ao apresentar declarações de imposto de renda.</value>
  </data>
  <data name="lblLeg_India_Form12BName" xml:space="preserve">
    <value>Formulário 12B</value>
  </data>
  <data name="lblLeg_India_Income_Tax_Monthly_StatementDesc" xml:space="preserve">
    <value>Fornece detalhes de detalhes de tributação para qualquer mês selecionado para todos os funcionários/funcionários selecionados.</value>
  </data>
  <data name="lblLeg_India_Income_Tax_Monthly_StatementName" xml:space="preserve">
    <value>Relatório de Declaração Mensal do Imposto de Renda</value>
  </data>
  <data name="lblLeg_India_LWF_Monthly_StatementDesc" xml:space="preserve">
    <value>Este relatório fornece contribuições de funcionários e empregadores para o fundo de bem-estar do trabalho.</value>
  </data>
  <data name="lblLeg_India_LWF_Monthly_StatementName" xml:space="preserve">
    <value>Declaração Mensal da FLM</value>
  </data>
  <data name="lblLeg_India_P16_Settlement_PayslipDesc" xml:space="preserve">
    <value>Fornece um resumo detalhado das informações de liquidação/reassentamento dos funcionários que deixam a empresa.</value>
  </data>
  <data name="lblLeg_India_P16_Settlement_PayslipName" xml:space="preserve">
    <value>Liquidação Holerite</value>
  </data>
  <data name="lblLeg_India_P27Desc" xml:space="preserve">
    <value>Relatório do Anexo II</value>
  </data>
  <data name="lblLeg_India_P27Name" xml:space="preserve">
    <value>Relatório do Anexo II</value>
  </data>
  <data name="lblLeg_India_P30Desc" xml:space="preserve">
    <value>Fornece informações sobre a declaração de TI real aplicável ao Capítulo VIA.</value>
  </data>
  <data name="lblLeg_India_P30Name" xml:space="preserve">
    <value>Relatório de economia de TI para deduções do Capítulo VI</value>
  </data>
  <data name="lblLeg_India_P33_Income_Tax_WorksheetDesc" xml:space="preserve">
    <value>Este relatório fornece detalhes de salário, dados de declaração de TI para o mês e ano selecionado em um formato consolidado para todos os funcionários / selecionados</value>
  </data>
  <data name="lblLeg_India_P33_Income_Tax_WorksheetName" xml:space="preserve">
    <value>Planilha de Imposto de Renda</value>
  </data>
  <data name="lblLeg_India_P41_Income_Tax_ConsolidatedDesc" xml:space="preserve">
    <value>Este relatório fornece detalhes de salário, dados de declaração de TI para o exercício financeiro selecionado em um formato consolidado para todos os funcionários / selecionados.</value>
  </data>
  <data name="lblLeg_India_P41_Income_Tax_ConsolidatedName" xml:space="preserve">
    <value>Relatório Consolidado do Imposto de Renda</value>
  </data>
  <data name="lblLeg_India_PF_ArrearsDesc" xml:space="preserve">
    <value>Fornece detalhes dos atrasos do fundo de previdência processados para o(s) funcionário(s), se houver.</value>
  </data>
  <data name="lblLeg_India_PF_ArrearsName" xml:space="preserve">
    <value>Relatório de atraso da PF</value>
  </data>
  <data name="lblLeg_India_PF_MonthlyDesc" xml:space="preserve">
    <value>Este relatório fornece detalhes do salário e da contribuição do fundo de previdência do empregado e do empregador para o mês.</value>
  </data>
  <data name="lblLeg_India_PF_MonthlyName" xml:space="preserve">
    <value>Relatório de Declaração Mensal da PF</value>
  </data>
  <data name="lblLeg_India_PFDesc" xml:space="preserve">
    <value>O formato de arquivo de texto necessário para fazer o upload para o portal PF.</value>
  </data>
  <data name="lblLeg_India_PFName" xml:space="preserve">
    <value>Formato PF ECR</value>
  </data>
  <data name="lblLeg_India_Proof_Of_Investment_DeclarationDesc" xml:space="preserve">
    <value>Fornece detalhes de todos os investimentos declarados, enviados e aprovados (status) para um funcionário para o ano financeiro.</value>
  </data>
  <data name="lblLeg_India_Proof_Of_Investment_DeclarationName" xml:space="preserve">
    <value>Comprovante de Declaração de Investimento</value>
  </data>
  <data name="lblLeg_India_PT_StatementDesc" xml:space="preserve">
    <value>Este relatório fornece detalhes do imposto profissional deduzido para todos os funcionários. É gerado por estado (ou todos os estados).</value>
  </data>
  <data name="lblLeg_India_PT_StatementName" xml:space="preserve">
    <value>Declaração mensal de imposto profissional</value>
  </data>
  <data name="lblLeg_India_SettlementDesc" xml:space="preserve">
    <value>Este relatório fornece detalhes de separação de funcionários e valor estendido durante a liquidação total e final (FnF).</value>
  </data>
  <data name="lblLeg_India_SettlementName" xml:space="preserve">
    <value>Relatório de liquidação</value>
  </data>
  <data name="lblLeg_IVOR_FDFP_MonthlyDesc" xml:space="preserve">
    <value>Relatório mensal de impostos de treinamento do empregador.</value>
  </data>
  <data name="lblLeg_IVOR_FDFP_MonthlyName" xml:space="preserve">
    <value>Taxa de aprendizagem e taxa adicional para educação continuada (FDFP)</value>
  </data>
  <data name="lblLeg_IVOR_IVCDesc" xml:space="preserve">
    <value>Declaração mensal que indica os impostos mensais pagos às autoridades fiscais. O relatório deve ser baixado no Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_IVOR_IVCName" xml:space="preserve">
    <value>Declaração mensal de impostos</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Electronic_CNPS_MonthlyDesc" xml:space="preserve">
    <value>Um arquivo eletrônico mensal para importar contribuições previdenciárias.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_Electronic_CNPS_MonthlyName" xml:space="preserve">
    <value>Relatório Eletrônico Mensal do CNPS</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITS_FDFPDesc" xml:space="preserve">
    <value>Relatório resumido dos impostos sobre empregados e empregadores (ITS, CE &amp; CN), incluindo o imposto de aprendizagem e o imposto de treinamento.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITS_FDFPName" xml:space="preserve">
    <value>Declaração de imposto sobre vencimentos, salários e contribuições conexas</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSDesc" xml:space="preserve">
    <value>Declaração mensal pormenorizada que indique a discriminação mensal da remuneração bruta, da situação familiar e do imposto devido. Este relatório será usado para copiar e colar na macro EDI. Copie as colunas C para R, T, Y, AA para AB e cole-as no modelo. As colunas S, U a X, Z serão calculadas pelo modelo. As colunas S, U a X, Z ainda são retornadas no relatório PaySpace para fins de reconciliação.</value>
  </data>
  <data name="lblLeg_Ivory_Coast_ITSName" xml:space="preserve">
    <value>Declaração Individual da Remuneração Mensal Bruta dos Trabalhadores</value>
  </data>
  <data name="lblLeg_Kenya_SHIFCDesc" xml:space="preserve">
    <value>Este relatório é apresentado mensalmente através do site da Autoridade de Saúde Social e contém informações sobre as contribuições dos funcionários para o Fundo de Seguro Social de Saúde.</value>
  </data>
  <data name="lblLeg_Kenya_SHIFCName" xml:space="preserve">
    <value>Relatório de Contribuições SHIF</value>
  </data>
  <data name="lblLeg_Lesotho_FBT_ReturnDesc" xml:space="preserve">
    <value>Relatório trimestral refletindo todos os benefícios concedidos no âmbito do regime FBT.</value>
  </data>
  <data name="lblLeg_Lesotho_FBT_ReturnName" xml:space="preserve">
    <value>Declaração de imposto sobre benefícios adicionais (FBT)</value>
  </data>
  <data name="lblLeg_Lesotho_Monthly_PAYEDesc" xml:space="preserve">
    <value>Cronograma mensal de PAYE para efiling (baixe o relatório em Excel).</value>
  </data>
  <data name="lblLeg_Lesotho_Monthly_PAYEName" xml:space="preserve">
    <value>Horários do PAYE</value>
  </data>
  <data name="lblLeg_Mad_MGA_USD_Bordereau_IRSA_ReportDesc" xml:space="preserve">
    <value>Relatório mensal de imposto sobre salários e rendimentos similares.</value>
  </data>
  <data name="lblLeg_Mad_MGA_USD_Bordereau_IRSA_ReportName" xml:space="preserve">
    <value>Relatório mensal do Imposto sobre Salários e Rendimentos Similares (IRSA)</value>
  </data>
  <data name="lblLeg_Mada_Etat_Nominatif_IRSA_ReportDesc" xml:space="preserve">
    <value>Declaração de vencimentos, salários e rendimentos equiparados.</value>
  </data>
  <data name="lblLeg_Mada_Etat_Nominatif_IRSA_ReportName" xml:space="preserve">
    <value>Relatório de Declaração de Vencimentos, Vencimentos e Rendimentos Equiparados (IRSA)</value>
  </data>
  <data name="lblLeg_Mada_Health_Org_ReportDesc" xml:space="preserve">
    <value>Fornece os relatórios necessários para as contribuições da Organização de Saúde. Recomenda-se que o relatório seja exportado no formato MS Excel.</value>
  </data>
  <data name="lblLeg_Mada_Health_Org_ReportName" xml:space="preserve">
    <value>Contribuição da Organização de Saúde</value>
  </data>
  <data name="lblLeg_Mada_Mon_eHetra_ReportDesc" xml:space="preserve">
    <value>Este cronograma contém informações mensais do IRSA dos funcionários. Baixe o relatório em Excel e copie-o para o arquivo de macro que pode ser enviado através do portal eHetra.</value>
  </data>
  <data name="lblLeg_Mada_Mon_eHetra_ReportName" xml:space="preserve">
    <value>Programação mensal da eHetra IRSA</value>
  </data>
  <data name="lblLeg_Malaysia_ASNBDesc" xml:space="preserve">
    <value>Deduções do Amanah Saham Nasional Berhad (ASNB)</value>
  </data>
  <data name="lblLeg_Malaysia_ASNBName" xml:space="preserve">
    <value>ANSB (Amanah Saham Nasional Berhad)</value>
  </data>
  <data name="lblLeg_Malaysia_CP21Desc" xml:space="preserve">
    <value>O formulário CP21 na Malásia refere-se a uma "Notificação de Mudança de Emprego" emitida pelo Inland Revenue Board da Malásia (Lembaga Hasil Dalam Negeri, ou LHDN). Este formulário é usado principalmente pelos empregadores para informar a LHDN quando o status de emprego de um funcionário muda, principalmente nos casos em que um funcionário deixa de trabalhar para a empresa, como devido a rescisão, demissão ou aposentadoria.</value>
  </data>
  <data name="lblLeg_Malaysia_CP21Name" xml:space="preserve">
    <value>CP21</value>
  </data>
  <data name="lblLeg_Malaysia_CP22ADesc" xml:space="preserve">
    <value>CP22A é outro formulário enviado pelos empregadores para notificar a LHDN sobre a rescisão do contrato de trabalho, aposentadoria ou migração permanente para o exterior de seus funcionários.</value>
  </data>
  <data name="lblLeg_Malaysia_CP22AName" xml:space="preserve">
    <value>CP22 A</value>
  </data>
  <data name="lblLeg_Malaysia_CP22Desc" xml:space="preserve">
    <value>O formulário CP22 é usado no contexto da folha de pagamento e da administração tributária para notificar o Inland Revenue Board (LHDN) sobre um novo funcionário ou alterações relacionadas a um funcionário existente.</value>
  </data>
  <data name="lblLeg_Malaysia_CP22Name" xml:space="preserve">
    <value>CP22</value>
  </data>
  <data name="lblLeg_Malaysia_CP39Desc" xml:space="preserve">
    <value>CP39 é uma declaração de dedução fiscal mensal (PCB) que precisa ser enviada à LHDN Malásia todos os meses por todos os empregadores, a partir de 01 de setembro de 2019 O IRB Malásia não aceita mais um formulário CP39 manual, o empregador é obrigado a enviar por meio de e-PCB, e-Data-PCB ou e-CP39.</value>
  </data>
  <data name="lblLeg_Malaysia_CP39Name" xml:space="preserve">
    <value>CP39</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_FormDesc" xml:space="preserve">
    <value>O Formulário CP8D é o relatório do funcionário CP8D nos formatos PDF e EXCEL</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_FormName" xml:space="preserve">
    <value>Formulário CP8D</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_txtDesc" xml:space="preserve">
    <value>O arquivo TXT CP8D Prefil e Praisi são recursos relacionados ao Relatório do Empregador CP8D na Malásia.</value>
  </data>
  <data name="lblLeg_Malaysia_CP8D_txtName" xml:space="preserve">
    <value>Arquivo CP8D Prefil / Praisi TXT</value>
  </data>
  <data name="lblLeg_Malaysia_EIS_Text_FileDesc" xml:space="preserve">
    <value>O Sistema de Seguro de Emprego (EIS) é um esquema de ajuda financeira para funcionários que perderam seus empregos até encontrarem um novo emprego. As contribuições são relatadas mensalmente por meio do Arquivo de Texto Mensal do EIS.</value>
  </data>
  <data name="lblLeg_Malaysia_EIS_Text_FileName" xml:space="preserve">
    <value>Arquivo de texto mensal EIS</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_Form_A_Text_FileDesc" xml:space="preserve">
    <value>EPF significa Fundo de Previdência dos Funcionários e também conhecido como Kumpulan Wang Simpanan Pekerja. As empresas são obrigadas a contribuir com o EPF em nome de seus funcionários e a remeter o valor da contribuição ao KWSP antes do dia 15 do mês seguinte</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_Form_A_Text_FileName" xml:space="preserve">
    <value>Formulário EPF A Arquivo de Texto Mensal</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_FORM_ADesc" xml:space="preserve">
    <value>Fornece os detalhes mensais das contribuições do EPF feitas pelo empregado e pelo empregador.</value>
  </data>
  <data name="lblLeg_Malaysia_EPF_FORM_AName" xml:space="preserve">
    <value>Borang A (EPF FORMA Manual Form) Relatório Mensal</value>
  </data>
  <data name="lblLeg_Malaysia_Form_EDesc" xml:space="preserve">
    <value>O Formulário E é um documento essencial para os empregadores na Malásia relatarem anualmente os salários, subsídios e deduções fiscais de todos os seus funcionários ao Inland Revenue Board (LHDN). Ele garante a declaração adequada e a conformidade com os regulamentos de imposto de renda do país.</value>
  </data>
  <data name="lblLeg_Malaysia_Form_EName" xml:space="preserve">
    <value>FORMULÁRIO E</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ListingDesc" xml:space="preserve">
    <value>O Relatório de Listagem do HRDF serve a vários propósitos relacionados ao gerenciamento e conformidade das contribuições do HRDF. Ele exibe a taxa total considerada para o cálculo do HRDF e a contribuição do HRDF de um funcionário para o mês</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ListingName" xml:space="preserve">
    <value>Relatório de listagem HRDF</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ReportDesc" xml:space="preserve">
    <value>O HRDF é uma agência do Ministério de Recursos Humanos da Malásia, criada para incentivar e apoiar o desenvolvimento da força de trabalho da Malásia por meio de iniciativas de treinamento e qualificação. O Relatório HRDF normalmente inclui: Demonstrações Financeiras: Detalhes sobre o desempenho financeiro do fundo, incluindo receitas, despesas e reservas. Iniciativas de treinamento: Visão geral dos programas de treinamento financiados pelo HRDF durante o ano.</value>
  </data>
  <data name="lblLeg_Malaysia_HRDF_ReportName" xml:space="preserve">
    <value>Relatório HRDF</value>
  </data>
  <data name="lblLeg_Malaysia_Lampiran_1Desc" xml:space="preserve">
    <value>O relatório Lampiran 1 (Formulário Manual EIS) permite gerar um relatório daqueles que contribuíram para o EIS</value>
  </data>
  <data name="lblLeg_Malaysia_Lampiran_1Name" xml:space="preserve">
    <value>Lampiran 1 (Formulário Manual EIS)</value>
  </data>
  <data name="lblLeg_Malaysia_LHDN_ReportDesc" xml:space="preserve">
    <value>O arquivo de texto MTD (Dedução Fiscal Mensal) é um arquivo digital usado para enviar informações de Dedução Fiscal Mensal (MTD) ao Inland Revenue Board of Malaysia (LHDN). Esse formato de arquivo ajuda a simplificar o processo de declaração e pagamento de deduções fiscais sobre os salários dos funcionários.</value>
  </data>
  <data name="lblLeg_Malaysia_LHDN_ReportName" xml:space="preserve">
    <value>Relatório LHDN (arquivo de texto MTD)</value>
  </data>
  <data name="lblLeg_Malaysia_SOCSO_8ADesc" xml:space="preserve">
    <value>O SOCSO BORANG 8A fornece informações sobre as contribuições mensais do SOCSO feitas para todos os funcionários. O formulário SOCSO BORANG 8A para execuções completas da folha de pagamento deve ser enviado à Perkeso on-line ou na agência/balcão até o dia 15 do mês seguinte à contribuição.</value>
  </data>
  <data name="lblLeg_Malaysia_SOCSO_8AName" xml:space="preserve">
    <value>SOCSO BORANG 8A</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_KedahDesc" xml:space="preserve">
    <value>O relatório Zakat Kedah normalmente se refere a um relatório periódico emitido pela agência de coleta e distribuição Zakat no estado de Kedah, Malásia.</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_KedahName" xml:space="preserve">
    <value>Zakat Kedah</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_PahangDesc" xml:space="preserve">
    <value>Um formulário de Zakat é usado por indivíduos ou organizações para calcular sistematicamente sua responsabilidade de Zakat com base nos princípios islâmicos. Ajuda a documentar os ativos, deduções e o valor calculado do Zakat, garantindo o cumprimento das obrigações religiosas. Apenas para a região selecionada.</value>
  </data>
  <data name="lblLeg_Malaysia_Zakat_PahangName" xml:space="preserve">
    <value>Zakat Pahang, Perlis &amp; Negeri Sembilan</value>
  </data>
  <data name="lblLeg_Mali_Declaration_Monthly_INPSDesc" xml:space="preserve">
    <value>Relatório detalhado indicando as várias contribuições para o INPS. Para garantir relatórios corretos, todos os funcionários devem estar vinculados a uma 'Categoria de Funcionário'.</value>
  </data>
  <data name="lblLeg_Mali_Declaration_Monthly_INPSName" xml:space="preserve">
    <value>Declaração nominativa de pagamento de contribuições</value>
  </data>
  <data name="lblLeg_Mali_Monthly_Tax_Declaration_Form_ReportDesc" xml:space="preserve">
    <value>Uma declaração mensal ilustrando as várias contribuições devidas à DGI. As submissões e relatórios devem ser enviados até o dia 15 do mês seguinte à dedução.</value>
  </data>
  <data name="lblLeg_Mali_Monthly_Tax_Declaration_Form_ReportName" xml:space="preserve">
    <value>Formulário de Declaração de Imposto Mensal</value>
  </data>
  <data name="lblLeg_Mauritania_Ann_DeclarationDesc" xml:space="preserve">
    <value>A declaração anual de salários é um documento utilizado para informar as contribuições para a segurança social e os impostos sobre salários e vencimentos. Esta declaração faz parte dos requisitos para a Declaração Geral de Impostos.</value>
  </data>
  <data name="lblLeg_Mauritania_Ann_DeclarationName" xml:space="preserve">
    <value>D31. Declaração anual de salário (DAS) do DADS Declaração geral de imposto</value>
  </data>
  <data name="lblLeg_Mauritania_DAS_AnnualDesc" xml:space="preserve">
    <value>Declaração anual de vencimentos utilizada para apresentar as contribuições para a segurança social e o imposto sobre os salários.</value>
  </data>
  <data name="lblLeg_Mauritania_DAS_AnnualName" xml:space="preserve">
    <value>Declaração anual de vencimento (DAS)</value>
  </data>
  <data name="lblLeg_Mauritania_Declaration_MenDesc" xml:space="preserve">
    <value>Declaração mensal utilizada para apresentar o imposto sobre os salários.  Este relatório deve ser extraído em formato PDF.</value>
  </data>
  <data name="lblLeg_Mauritania_Declaration_MenName" xml:space="preserve">
    <value>Relatório mensal PAYE</value>
  </data>
  <data name="lblLeg_Mon_Togo_CNSS_DeclarationDesc" xml:space="preserve">
    <value>O formulário de declaração de contribuição social permite que o empregador faça declarações mensais ao CNSS.</value>
  </data>
  <data name="lblLeg_Mon_Togo_CNSS_DeclarationName" xml:space="preserve">
    <value>Declaração de Remunerações e Contribuições (DRC)</value>
  </data>
  <data name="lblLeg_Mon_Togo_Tax_reportDesc" xml:space="preserve">
    <value>Declaração mensal de impostos sobre a folha de pagamento e imposto sobre funcionários</value>
  </data>
  <data name="lblLeg_Mon_Togo_Tax_reportName" xml:space="preserve">
    <value>Declaração de Pagamento de Imposto sobre a Folha de Pagamento e Deduções Fiscais</value>
  </data>
  <data name="lblLeg_MOZ_INSS_EDIDesc" xml:space="preserve">
    <value>Ficheiro eletrónico de submissão da declaração mensal de contribuições para a Segurança Social carregado via SISSMO (formato txt).</value>
  </data>
  <data name="lblLeg_MOZ_INSS_EDIName" xml:space="preserve">
    <value>Arquivo de Upload de Submissão Eletrônica do INSS (formato txt)</value>
  </data>
  <data name="lblLeg_MOZ_INSSDesc" xml:space="preserve">
    <value>Arquivo eletrônico de submissão da declaração mensal de contribuições para a Segurança Social carregado via SISSMO (formato xlsx).</value>
  </data>
  <data name="lblLeg_MOZ_INSSName" xml:space="preserve">
    <value>Arquivo de Upload de Submissão Eletrônica do INSS (formato xlsx)</value>
  </data>
  <data name="lblLeg_MOZ_M19_ReturnDesc" xml:space="preserve">
    <value>Declaração de pagamento mensal de IRPS.</value>
  </data>
  <data name="lblLeg_MOZ_M19_ReturnName" xml:space="preserve">
    <value>Retorno de IRPS M/19</value>
  </data>
  <data name="lblLeg_Niger_Ann_ITSDesc" xml:space="preserve">
    <value>Este é um relatório resumido anual sobre os impostos calculados sobre salários, salários, rendas semelhantes e anuidades vitalícias. Este relatório deve ser baixado no Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Niger_Ann_ITSName" xml:space="preserve">
    <value>Declaração Anual Sumária de Deduções Fiscais</value>
  </data>
  <data name="lblLeg_Niger_Mon_ITSDesc" xml:space="preserve">
    <value>Um relatório mensal sobre os impostos calculados sobre salários, salários, rendas semelhantes e anuidades vitalícias. Este relatório deve ser baixado no Adobe Acrobat (pdf).</value>
  </data>
  <data name="lblLeg_Niger_Mon_ITSName" xml:space="preserve">
    <value>Declaração Mensal de Deduções Fiscais</value>
  </data>
  <data name="lblLeg_Nigeria_NHFDesc" xml:space="preserve">
    <value>Cronograma mensal para as contribuições do Fundo Nacional de Habitação da Nigéria.</value>
  </data>
  <data name="lblLeg_Nigeria_NHFName" xml:space="preserve">
    <value>Relatório NHF</value>
  </data>
  <data name="lblLeg_Qua_Togo_CNSSDesc" xml:space="preserve">
    <value>A partir do ano fiscal de 2024, este relatório Nextgen não será mais aprimorado.</value>
  </data>
  <data name="lblLeg_Qua_Togo_CNSSName" xml:space="preserve">
    <value>Declaração Nominativa de Remuneração</value>
  </data>
  <data name="lblLeg_Senegal_Annual_TaxDesc" xml:space="preserve">
    <value>Declaração anual informando os impostos (IR, TRIMF, CFCE) cobrados sobre os salários pagos a todos os funcionários.</value>
  </data>
  <data name="lblLeg_Senegal_Annual_TaxName" xml:space="preserve">
    <value>Mapa anual resumido dos salários</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Desc" xml:space="preserve">
    <value>Este relatório é executado anualmente para identificar as variações de contribuição do CPF.</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Name" xml:space="preserve">
    <value>Listagem de Ajuste de CPF</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Payment_Advice_SummaryDesc" xml:space="preserve">
    <value>Este é um relatório mensal que fornece um resumo detalhado dos itens a pagar ao CPF e detalhes de apoio dos funcionários.</value>
  </data>
  <data name="lblLeg_Singapore_CPF_Payment_Advice_SummaryName" xml:space="preserve">
    <value>Resumo do Aviso de Pagamento do CPF</value>
  </data>
  <data name="lblLeg_Singapore_CPF_TextDesc" xml:space="preserve">
    <value>Este é um arquivo mensal a ser gerado para upload no Portal do CPF como envio do Empregador do Total de Contribuições do CPF e das Contribuições do SHG.</value>
  </data>
  <data name="lblLeg_Singapore_CPF_TextName" xml:space="preserve">
    <value>Arquivo CPF EZPay (FTP)</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8ADesc" xml:space="preserve">
    <value>Este relatório é gerado anualmente para relatar a remuneração dos funcionários para o ano encerrado em 31 de dezembro de 2024</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8AName" xml:space="preserve">
    <value>FORMULÁRIO APÊNDICE 8A</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8BDesc" xml:space="preserve">
    <value>Este relatório é gerado anualmente para relatar a remuneração dos funcionários para o ano encerrado em 31 de dezembro &lt;yyyy&gt;</value>
  </data>
  <data name="lblLeg_Singapore_Form_Appendix8BName" xml:space="preserve">
    <value>FORMULÁRIO APÊNDICE 8B</value>
  </data>
  <data name="lblLeg_Singapore_Form_IR8ADesc" xml:space="preserve">
    <value>Este relatório é gerado anualmente para relatar a remuneração dos funcionários para o ano encerrado em 31 de dezembro de 2024</value>
  </data>
  <data name="lblLeg_Singapore_Form_IR8AName" xml:space="preserve">
    <value>FORMULÁRIO IR8A</value>
  </data>
  <data name="lblLeg_Singapore_IR8SDesc" xml:space="preserve">
    <value>Este relatório é gerado anualmente para relatar a remuneração dos funcionários para o ano encerrado em 31 de dezembro &lt;yyyy&gt;</value>
  </data>
  <data name="lblLeg_Singapore_IR8SName" xml:space="preserve">
    <value>FORMULÁRIO IR8S</value>
  </data>
  <data name="lblLeg_Singapore_IRAS_ValidationDesc" xml:space="preserve">
    <value>Este é um relatório de validação interna listando todos os funcionários relatados em IR8A, Apêndice 8A, Apêndice 8B, IR8S com totais resumidos relevantes para cada formulário</value>
  </data>
  <data name="lblLeg_Singapore_IRAS_ValidationName" xml:space="preserve">
    <value>Relatório de validação do IRAS</value>
  </data>
  <data name="lblLeg_Singapore_ReconDesc" xml:space="preserve">
    <value>Este relatório é usado como uma referência interna para validar os valores relatados ao IRAS.</value>
  </data>
  <data name="lblLeg_Singapore_ReconName" xml:space="preserve">
    <value>Relatório de reconciliação do IRAS</value>
  </data>
  <data name="lblLeg_South_Africa_UI_2_7Desc" xml:space="preserve">
    <value>Formulário emitido para funcionários que ainda estão empregados, mas incapazes de trabalhar devido a licença maternidade, doença, licença por adoção, licença parental, licença parental por comissionamento ou redução do tempo de trabalho.</value>
  </data>
  <data name="lblLeg_South_Africa_UI_2_7Name" xml:space="preserve">
    <value>Interface do usuário-2.7</value>
  </data>
  <data name="lblLeg_Tanzania_SDL_Return_FormDesc" xml:space="preserve">
    <value>Relatório eletrônico SDL mensal usado na planilha macro para envio online.</value>
  </data>
  <data name="lblLeg_Tanzania_SDL_Return_FormName" xml:space="preserve">
    <value>Formulário de Devolução SDL ITX.215.03.E</value>
  </data>
  <data name="lblLeg_Togo_Anuual_Tax_DeclarationDesc" xml:space="preserve">
    <value>Declaração anual utilizada para apresentar impostos sobre salários, salários, pensões e anuidades vitalícias.</value>
  </data>
  <data name="lblLeg_Togo_Anuual_Tax_DeclarationName" xml:space="preserve">
    <value>Declaração Anual de Impostos</value>
  </data>
  <data name="lblLeg_Tunisia_Annual_CAVISDesc" xml:space="preserve">
    <value>O relatório é usado para declarar o salário anual de contribuição do CAVIS em formato de texto na plataforma online eletronicamente.</value>
  </data>
  <data name="lblLeg_Tunisia_Annual_CAVISName" xml:space="preserve">
    <value>Relatório Anual de Declaração CAVIS (formato txt)</value>
  </data>
  <data name="lblLeg_UAE_DIFC_Employee_Workplace_SavingsDesc" xml:space="preserve">
    <value>Este relatório preenche os dados dos funcionários e seus valores de contribuição DEWS. O arquivo pode ser usado para inscrição inicial e de forma contínua, de acordo com a frequência de seus processos internos de folha de pagamento (semanal, mensal, etc.). O arquivo é carregado no formato CSV para o portal DEWS.</value>
  </data>
  <data name="lblLeg_UAE_DIFC_Employee_Workplace_SavingsName" xml:space="preserve">
    <value>Plano de poupança para funcionários do DIFC dos Emirados Árabes Unidos (DEWS) Carregar arquivo</value>
  </data>
  <data name="lblLeg_UGA_LST_MonthlyDesc" xml:space="preserve">
    <value>Um retorno mensal genérico de LST. Esta declaração deve ser preenchida e enviada às autoridades governamentais locais para o imposto sobre serviços locais (LST).</value>
  </data>
  <data name="lblLeg_UGA_LST_MonthlyName" xml:space="preserve">
    <value>Retorno do Imposto sobre Serviços Locais (LST)</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule3Desc" xml:space="preserve">
    <value>Anexo 3 do DT-2008 PAYE Monthly Return. Este relatório será usado para copiar e colar no modelo de Retorno Mensal DT-2008 PAYE na guia Anexo 3. Copie as colunas A a G, I, J, M e O e cole-as no modelo. As colunas H, K, L, N, P e Q serão calculadas pelo modelo. As colunas H, K, L, N, P e Q ainda são retornadas no relatório PaySpace para fins de reconciliação.</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule3Name" xml:space="preserve">
    <value>Cronograma de Retorno Mensal PAYE 3</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule4Desc" xml:space="preserve">
    <value>Anexo 4 do DT-2008 PAYE Monthly Return. Este relatório será usado para copiar e colar no modelo de Retorno Mensal DT-2008 PAYE na guia Anexo 4. Copie as colunas A a G, J e L e cole-as no modelo. As colunas H, I, K, M e N serão calculadas pelo modelo. As colunas H, I, K, M e N ainda são retornadas no relatório PaySpace para fins de reconciliação.</value>
  </data>
  <data name="lblLeg_UGA_PAYE_Return_Schedule4Name" xml:space="preserve">
    <value>Cronograma de Devolução Mensal PAYE 4</value>
  </data>
  <data name="lblLeg_UGA_PAYE_ReturnDesc" xml:space="preserve">
    <value>Anexo 1 do DT-2008 PAYE Monthly Return. Este relatório será usado para copiar e colar no modelo de Retorno Mensal DT-2008 PAYE na guia Anexo 1. Copie as colunas A a O, Q, S, T e V e cole-as no modelo. As colunas P, R e U serão calculadas pelo modelo. As colunas P e R ainda são retornadas no relatório PaySpace para fins de reconciliação. O relatório deve ser baixado em excel (xlsx).</value>
  </data>
  <data name="lblLeg_UGA_PAYE_ReturnName" xml:space="preserve">
    <value>Cronograma de Retorno Mensal PAYE 1</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_MonthlyDesc" xml:space="preserve">
    <value>Declaração mensal do NSSF para Uganda, que pode ser carregada no portal do NSSF.</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_MonthlyName" xml:space="preserve">
    <value>Calendário de Declaração Mensal do Fundo Nacional de Segurança Social</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_SpecialDesc" xml:space="preserve">
    <value>Declaração mensal do NSSF para Uganda para Contribuições Especiais, que pode ser carregada no portal do NSSF.</value>
  </data>
  <data name="lblLeg_Uganda_NSSF_SpecialName" xml:space="preserve">
    <value>Declaração Mensal NSSF - Contribuições Especiais</value>
  </data>
  <data name="lblLeg_UK_P45_Termination_CertificateDesc" xml:space="preserve">
    <value>Certidão emitida para funcionários demitidos</value>
  </data>
  <data name="lblLeg_UK_P45_Termination_CertificateName" xml:space="preserve">
    <value>P45 Certificado de Rescisão de Funcionário</value>
  </data>
  <data name="lblLeg_Zambia_WCF_ElectronicDesc" xml:space="preserve">
    <value>Declaração anual para envio de um arquivo CSV por meio do site dos Conselhos de Controle do Fundo de Compensação dos Trabalhadores.</value>
  </data>
  <data name="lblLeg_Zambia_WCF_ElectronicName" xml:space="preserve">
    <value>Arquivo eletrônico WCF da Zâmbia</value>
  </data>
  <data name="lblLeg_Zim_Form_P2_PAYEDesc" xml:space="preserve">
    <value>Retorno mensal para a remessa de PAYE.</value>
  </data>
  <data name="lblLeg_Zim_Form_P2_PAYEName" xml:space="preserve">
    <value>Formulário P2</value>
  </data>
  <data name="lblLeg_Zim_ManPower_ReturnDesc" xml:space="preserve">
    <value>Relatório mensal para declarar contribuições para o Fundo de Desenvolvimento de Mão de Obra do Zimbábue.</value>
  </data>
  <data name="lblLeg_Zim_ManPower_ReturnName" xml:space="preserve">
    <value>Formulário de Declaração do Fundo de Desenvolvimento de Mão de Obra do Zimbábue</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4ADesc" xml:space="preserve">
    <value>O formulário P4A é um aviso de remessa mensal para pagamento à Autoridade Nacional de Segurança Social.</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4AName" xml:space="preserve">
    <value>Formulário P4A NSSA</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4Desc" xml:space="preserve">
    <value>Os empregadores são instruídos a visitar o portal e baixar o modelo P4 para edição. Uma vez baixado, os empregadores devem usar este relatório NSSA P4 Excel para editar as colunas E e K a N.</value>
  </data>
  <data name="lblLeg_Zim_NSSA_P4Name" xml:space="preserve">
    <value>NSSA P4</value>
  </data>
  <data name="lblLeg_Zim_PAYE_Return_USDDesc" xml:space="preserve">
    <value>Retorno mensal de PAYE para envio ao ZIMRA por meio da plataforma online, TaRMS.</value>
  </data>
  <data name="lblLeg_Zim_PAYE_Return_USDName" xml:space="preserve">
    <value>PAYE Retorno</value>
  </data>
  <data name="lblLeg_Zim_PAYE_ReturnDesc" xml:space="preserve">
    <value>Retorno mensal de PAYE para envio ao ZIMRA por meio da plataforma online, TaRMS. Para que esse relatório retorne os valores corretamente, a tela Taxas de câmbio deve ser configurada.</value>
  </data>
  <data name="lblLeg_Zim_PAYE_ReturnName" xml:space="preserve">
    <value>PAYE Retorno</value>
  </data>
  <data name="lblLeg_Zim_PAYE_TemplateDesc" xml:space="preserve">
    <value>Este modelo é usado para fazer upload dos ganhos dos funcionários para o portal TaRMs do Zimbábue para envio do PAYE. Para que este relatório retorne em uma moeda diferente, a tela Taxas de câmbio deve ser configurada.</value>
  </data>
  <data name="lblLeg_Zim_PAYE_TemplateName" xml:space="preserve">
    <value>Modelo PAYE - Upload de ganhos de funcionários</value>
  </data>
  <data name="lblLeg_Zim_SDL_DeclarationDesc" xml:space="preserve">
    <value>Relatório trimestral para declarar a Taxa de Desenvolvimento de Padrões.</value>
  </data>
  <data name="lblLeg_Zim_SDL_DeclarationName" xml:space="preserve">
    <value>Relatório de Taxa de Desenvolvimento de Padrões</value>
  </data>
  <data name="lblLegRSASalaryScheduleDesc" xml:space="preserve">
    <value>Direcionado a colaboradores que possuem solicitações ao ministério do trabalho</value>
  </data>
  <data name="lblLegRSASalaryScheduleName" xml:space="preserve">
    <value>Tabela Salarial</value>
  </data>
  <data name="lblLegRSASalaryScheduleNote" xml:space="preserve">
    <value>Este relatório precisará ser preenchido pelo usuário. Devido à natureza do relatório, os empregadores são obrigados a preencher a remuneração UIF do empregado recebida durante os períodos de pagamento, conforme solicitado pelo 'Departamento do Trabalho'.</value>
  </data>
  <data name="lblLevel" xml:space="preserve">
    <value>Nível Organizacional</value>
  </data>
  <data name="lblLevelParamNameKey" xml:space="preserve">
    <value>Grupo por nível de organização</value>
  </data>
  <data name="lblLoansReportDesc" xml:space="preserve">
    <value>Fornece uma lista de descontos de empréstimos de empregado por processo.</value>
  </data>
  <data name="lblLoansReportName" xml:space="preserve">
    <value>Relatório de Empréstimos</value>
  </data>
  <data name="lblMalaysia_SOCSO_txt_fileDesc" xml:space="preserve">
    <value>A contribuição da SOCSO (Organização da Previdência Social) refere-se às contribuições obrigatórias feitas por empregadores e empregados na Malásia para financiar os benefícios da previdência social fornecidos pela SOCSO. Essas contribuições visam principalmente fornecer assistência financeira e benefícios aos funcionários e seus dependentes em caso de lesões, deficiências, doenças ou morte relacionadas ao trabalho.</value>
  </data>
  <data name="lblMalaysia_SOCSO_txt_fileName" xml:space="preserve">
    <value>Arquivo de texto mensal SOCSO</value>
  </data>
  <data name="lblMedicalReportDesc" xml:space="preserve">
    <value>Fornece uma listagem das contribuições médicas de empregado para um período/competência.</value>
  </data>
  <data name="lblMedicalReportName" xml:space="preserve">
    <value>Relatório de Assistência Médica</value>
  </data>
  <data name="lblMonthConditionalParamNameKey" xml:space="preserve">
    <value>Período de rescisão ou suspensão</value>
  </data>
  <data name="lblMonthSelect" xml:space="preserve">
    <value>Mês</value>
  </data>
  <data name="lblMTDOrCurrentParamNameKey" xml:space="preserve">
    <value>Execução/mês</value>
  </data>
  <data name="lblMultipleActions" xml:space="preserve">
    <value>Múltiplas Ações</value>
  </data>
  <data name="lblMultipleCompanies" xml:space="preserve">
    <value>Várias empresas</value>
  </data>
  <data name="lblMultipleUnits" xml:space="preserve">
    <value>Unidades múltiplas</value>
  </data>
  <data name="lblNetPay" xml:space="preserve">
    <value>Incluir recibos de vencimento líquidos zero</value>
  </data>
  <data name="lblNetPaymentListingName" xml:space="preserve">
    <value>Listagem de pagamento líquido</value>
  </data>
  <data name="lblNewEngagementsandTerminationsDesc" xml:space="preserve">
    <value>Fornece uma lista de novos compromissos e funcionários demitidos por um período escolhido.</value>
  </data>
  <data name="lblNewEngagementsandTerminationsName" xml:space="preserve">
    <value>Novos compromissos e rescisões</value>
  </data>
  <data name="lblNig_Ann_Alt_LIRSDesc" xml:space="preserve">
    <value>Este é o formulário de declaração anual PAYE que os empregadores enviam às autoridades fiscais. Este relatório não é específico do estado e pode ser usado por empregadores para estados que não possuem um modelo de formulário H1 específico.</value>
  </data>
  <data name="lblNig_Ann_Alt_LIRSName" xml:space="preserve">
    <value>Relatório anual alternativo H1 (todos os estados)</value>
  </data>
  <data name="lblNig_Ann_LIRS_PAYEDesc" xml:space="preserve">
    <value>Arquivo de relatório fiscal anual para envio ao portal E-Tax do Internal Revenue Service (LIRS) do estado de Lagos. O arquivo deve ser exportado como um arquivo CSV para importação no portal E-Tax.</value>
  </data>
  <data name="lblNig_Ann_LIRS_PAYEName" xml:space="preserve">
    <value>Imposto Anual LIRS</value>
  </data>
  <data name="lblNIG_ITF_Form_5_Annual_ReturnDesc" xml:space="preserve">
    <value>Declaração anual a apresentar ao Fundo de Formação Industrial até 1 de abril, com base no exercício fiscal anterior.</value>
  </data>
  <data name="lblNIG_ITF_Form_5_Annual_ReturnName" xml:space="preserve">
    <value>Formulário ITF 5A</value>
  </data>
  <data name="lblNIG_OGIRS_PAYEDesc" xml:space="preserve">
    <value>Arquivo de relatório fiscal mensal para envio ao portal E-Tax do Serviço de Receita Interna do Estado de Ogun (OGIRS). O arquivo deve ser exportado para o Excel para importação no portal E-Tax.</value>
  </data>
  <data name="lblNIG_OGIRS_PAYEName" xml:space="preserve">
    <value>Imposto Mensal OGIRS</value>
  </data>
  <data name="lblNIG_Pension_Fund_NewDesc" xml:space="preserve">
    <value>Relatório mensal de declaração de contribuições para o Regime Nacional de Pensões através do portal online NIBSS. O arquivo deve ser exportado para o Excel. A primeira planilha exibirá o total geral de todos os funcionários e as planilhas separadas exibirão os subtotais por Código PFA.</value>
  </data>
  <data name="lblNIG_Pension_Fund_NewName" xml:space="preserve">
    <value>Relatório Genérico de Fundo de Pensão</value>
  </data>
  <data name="lblNigeria_ABUJA_FCT_IRS_PAYEDesc" xml:space="preserve">
    <value>Declaração fiscal mensal para submissão à FCT-IRS. O arquivo deve ser exportado para o Excel para os E-Services.</value>
  </data>
  <data name="lblNigeria_ABUJA_FCT_IRS_PAYEName" xml:space="preserve">
    <value>Cronograma Mensal de PAYE FCT-IRS</value>
  </data>
  <data name="lblNigeria_Annual_OGIRS_PAYEDesc" xml:space="preserve">
    <value>Arquivo de relatório anual de impostos para envio ao portal E-Tax do Serviço de Receita Interna do Estado de Ogun (OGIRS). O arquivo deve ser exportado para o Excel para Importação no portal E-Tax.</value>
  </data>
  <data name="lblNigeria_Annual_OGIRS_PAYEName" xml:space="preserve">
    <value>OGIRS Imposto Anual</value>
  </data>
  <data name="lblNigeria_FCT-IRS Form_H1_AnnDesc" xml:space="preserve">
    <value>Relatório anual de impostos para submissão à FCT-IRS. O arquivo deve ser exportado para o Excel para os E-Services.</value>
  </data>
  <data name="lblNigeria_FCT-IRS Form_H1_AnnName" xml:space="preserve">
    <value>Formulário FCT-IRS - H1- Declaração e Certidão Anual do Empregador</value>
  </data>
  <data name="lblNigeria_LIRS_PAYEDesc" xml:space="preserve">
    <value>Arquivo de relatório fiscal mensal para envio ao portal E-Tax do Internal Revenue Service (LIRS) do Estado de Lagos. O arquivo deve ser exportado como um arquivo CSV para importação no portal E-Tax.</value>
  </data>
  <data name="lblNigeria_LIRS_PAYEName" xml:space="preserve">
    <value>Imposto Mensal LIRS</value>
  </data>
  <data name="lblNoGLNumberParamNameKey" xml:space="preserve">
    <value>Mostrar apenas componentes que possuem um código GL definido?</value>
  </data>
  <data name="lblNonFormattedParamNameKey" xml:space="preserve">
    <value>Marque aqui se você deseja visualizar este relatório sem formatar</value>
  </data>
  <data name="lblNonFormattedSelect" xml:space="preserve">
    <value>Não formatado</value>
  </data>
  <data name="lblNoReport" xml:space="preserve">
    <value>Nenhum relatório encontrado</value>
  </data>
  <data name="lblNotes" xml:space="preserve">
    <value>Anotações</value>
  </data>
  <data name="lblNoTrainingParamNameKey" xml:space="preserve">
    <value>Exibir apenas os funcionários que não participaram de nenhum curso de treinamento no período acima?</value>
  </data>
  <data name="lblNT35_Unemployment_Insurance_FileDesc" xml:space="preserve">
    <value>O relatório exibe as informações e os valores relacionados às rescisões de funcionários em um período específico.</value>
  </data>
  <data name="lblNT35_Unemployment_Insurance_FileName" xml:space="preserve">
    <value>Relatório de Seguro Desemprego</value>
  </data>
  <data name="lblOrdinaryDays" xml:space="preserve">
    <value>Dias Comuns</value>
  </data>
  <data name="lblOrdinaryHours" xml:space="preserve">
    <value>Horário Ordinário</value>
  </data>
  <data name="lblOrgUnitId" xml:space="preserve">
    <value>Unidade Organizacional</value>
  </data>
  <data name="lblOrgUnitIDParamNameKey" xml:space="preserve">
    <value>Unidades organizacionais</value>
  </data>
  <data name="lblOrgUnitIds" xml:space="preserve">
    <value>Unidade Organizacional</value>
  </data>
  <data name="lblOrgUnitParamNameKey" xml:space="preserve">
    <value>Unidades organizacionais</value>
  </data>
  <data name="lblOrgUnitsParamNameKey" xml:space="preserve">
    <value>Unidades organizacionais</value>
  </data>
  <data name="lblOrgUnitTotalsfor" xml:space="preserve">
    <value>Totais de Unidade Organizacional</value>
  </data>
  <data name="lblotalsTypeParamNameKey" xml:space="preserve">
    <value>Gerar relatório com</value>
  </data>
  <data name="lblPackageStructureBreakDown" xml:space="preserve">
    <value>Detalhamento da estrutura do pacote</value>
  </data>
  <data name="lblPageHeader" xml:space="preserve">
    <value>Relatórios</value>
  </data>
  <data name="lblPaid" xml:space="preserve">
    <value>Pago?</value>
  </data>
  <data name="lblPaidParamNameKey" xml:space="preserve">
    <value>Excluir holerites para funcionários marcados como pagos? Isso se aplica à execução selecionada acima.</value>
  </data>
  <data name="lblParentReport" xml:space="preserve">
    <value>Relatório pai</value>
  </data>
  <data name="lblPaymentDate" xml:space="preserve">
    <value>Data de pagamento</value>
  </data>
  <data name="lblPaymentDay" xml:space="preserve">
    <value>Dia de Pagamento</value>
  </data>
  <data name="lblPayMethodParamNameKey" xml:space="preserve">
    <value>Filtrar funcionários pagos via</value>
  </data>
  <data name="lblPayPoint" xml:space="preserve">
    <value>Ponto de pagamento</value>
  </data>
  <data name="lblPayRate" xml:space="preserve">
    <value>Taxa de pagamento</value>
  </data>
  <data name="lblPayRatesReportDesc" xml:space="preserve">
    <value>Fornece uma lista do pacote de funcionários em ordem histórica ou apenas a mais recente.</value>
  </data>
  <data name="lblPayRatesReportName" xml:space="preserve">
    <value>Relatório de taxas de pagamento</value>
  </data>
  <data name="lblPayrollConsolidatedReconciliationReportName" xml:space="preserve">
    <value>Reconciliação consolidada da folha de pagamento</value>
  </data>
  <data name="lblPayrollReconciliationReportDesc" xml:space="preserve">
    <value>Fornece números atuais ou MTD de todos os componentes dos funcionários nos holerites. Normalmente usado para fins de reconciliação.</value>
  </data>
  <data name="lblPayrollReconciliationReportName" xml:space="preserve">
    <value>Relatório de reconciliação da folha de pagamento</value>
  </data>
  <data name="lblPayRollRegDesc" xml:space="preserve">
    <value>Fornece uma visão compacta dos contracheques dos funcionários com vários contracheques por página.</value>
  </data>
  <data name="lblPayRollRegName" xml:space="preserve">
    <value>Relatório de registro da folha de pagamento</value>
  </data>
  <data name="lblPayslipAction" xml:space="preserve">
    <value>Tipo de Rubrica</value>
  </data>
  <data name="lblPayslipActionParamNameKey" xml:space="preserve">
    <value>Ações de holerite</value>
  </data>
  <data name="lblPayslipActions" xml:space="preserve">
    <value>Ações de Holerite</value>
  </data>
  <data name="lblPayslipsDesc" xml:space="preserve">
    <value>Fornece uma lista dos holerites dos funcionários para uma execução escolhida.</value>
  </data>
  <data name="lblPayslipSettingsParamNameKey" xml:space="preserve">
    <value>Configurações</value>
  </data>
  <data name="lblPayslipsName" xml:space="preserve">
    <value>Holerites</value>
  </data>
  <data name="lblPeriodCode" xml:space="preserve">
    <value>Competência</value>
  </data>
  <data name="lblPeriodCodeParamNameKey" xml:space="preserve">
    <value>Mês</value>
  </data>
  <data name="lblPeriodEndDate" xml:space="preserve">
    <value>Data de Término do Período</value>
  </data>
  <data name="lblPeriodStartDate" xml:space="preserve">
    <value>Data de início do período</value>
  </data>
  <data name="lblPersonal" xml:space="preserve">
    <value>Pessoal</value>
  </data>
  <data name="lblPhysAddress" xml:space="preserve">
    <value>Phys. Adicionar</value>
  </data>
  <data name="lblPosition" xml:space="preserve">
    <value>Posição</value>
  </data>
  <data name="lblPositionId" xml:space="preserve">
    <value>Posição</value>
  </data>
  <data name="lblPositionIds" xml:space="preserve">
    <value>Posições</value>
  </data>
  <data name="lblPositionParamNameKey" xml:space="preserve">
    <value>Posição</value>
  </data>
  <data name="lblpositionsParamNameKey" xml:space="preserve">
    <value>Posição</value>
  </data>
  <data name="lblPrefName" xml:space="preserve">
    <value>Pref. Nome</value>
  </data>
  <data name="lblPreviewParameters" xml:space="preserve">
    <value>Parâmetros de Visualização</value>
  </data>
  <data name="lblPrintOrgUnit" xml:space="preserve">
    <value>Imprimir unidade organizacional</value>
  </data>
  <data name="lblProcessIDParamNameKey" xml:space="preserve">
    <value>Processo</value>
  </data>
  <data name="lblProject" xml:space="preserve">
    <value>Local/Tomador</value>
  </data>
  <data name="lblProjectDescription" xml:space="preserve">
    <value>Descrição do Local ou Tomador de Serviços</value>
  </data>
  <data name="lblProjectId" xml:space="preserve">
    <value>Projeto</value>
  </data>
  <data name="lblProjectIds" xml:space="preserve">
    <value>Projetos</value>
  </data>
  <data name="lblProjectParamNameKey" xml:space="preserve">
    <value>Projeto</value>
  </data>
  <data name="lblProjectTotalsfor" xml:space="preserve">
    <value>Totais par a Local ou Tomador de Serviços</value>
  </data>
  <data name="lblQty" xml:space="preserve">
    <value>Qtd</value>
  </data>
  <data name="lblRate" xml:space="preserve">
    <value>Taxa</value>
  </data>
  <data name="lblRegion" xml:space="preserve">
    <value>Estabelecimento</value>
  </data>
  <data name="lblRegionCompanyGroup" xml:space="preserve">
    <value>Estabelecimento</value>
  </data>
  <data name="lblRegionId" xml:space="preserve">
    <value>Estabelecimento</value>
  </data>
  <data name="lblRegionIds" xml:space="preserve">
    <value>Regiões</value>
  </data>
  <data name="lblRegionParamNameKey" xml:space="preserve">
    <value>Região / localização</value>
  </data>
  <data name="lblReport" xml:space="preserve">
    <value>Relatórios</value>
  </data>
  <data name="lblReportAudit" xml:space="preserve">
    <value>ReportAudit</value>
  </data>
  <data name="lblReportAuditHeader" xml:space="preserve">
    <value>Trilha de auditoria de relatório</value>
  </data>
  <data name="lblReportCategory" xml:space="preserve">
    <value>Categoria</value>
  </data>
  <data name="lblReportCategoryRequired" xml:space="preserve">
    <value>Categoria</value>
  </data>
  <data name="lblReportDescription" xml:space="preserve">
    <value>Descrição</value>
  </data>
  <data name="lblReportDisableConfirmation" xml:space="preserve">
    <value>Tem certeza de que deseja desativar este relatório?</value>
  </data>
  <data name="lblReportDisabled" xml:space="preserve">
    <value>Desactivado</value>
  </data>
  <data name="lblReportDisabledHasDependenies" xml:space="preserve">
    <value>O relatório tem dependências e foi marcado como desabilitado.</value>
  </data>
  <data name="lblReportEditable" xml:space="preserve">
    <value>Editável</value>
  </data>
  <data name="lblReportEdited" xml:space="preserve">
    <value>Edição</value>
  </data>
  <data name="lblReportEnable" xml:space="preserve">
    <value>Habilitado</value>
  </data>
  <data name="lblReportEnableConfirmation" xml:space="preserve">
    <value>Tem certeza de que deseja habilitar este relatório?</value>
  </data>
  <data name="lblReportEnabled" xml:space="preserve">
    <value>Habilitar</value>
  </data>
  <data name="lblReportHistoryHeader" xml:space="preserve">
    <value>Meus relatórios dos últimos 8 dias</value>
  </data>
  <data name="lblReportLevel" xml:space="preserve">
    <value>Nível</value>
  </data>
  <data name="lblReportList" xml:space="preserve">
    <value>Lista de relatórios clássicos</value>
  </data>
  <data name="lblReportName" xml:space="preserve">
    <value>Nome</value>
  </data>
  <data name="lblReportNameRequired" xml:space="preserve">
    <value>Insira o nome do relatório.</value>
  </data>
  <data name="lblReportNotExists" xml:space="preserve">
    <value>O relatório não existe</value>
  </data>
  <data name="lblReportPageTitle" xml:space="preserve">
    <value>Relatórios</value>
  </data>
  <data name="lblReportParamTitle" xml:space="preserve">
    <value>Parâmetros do relatório</value>
  </data>
  <data name="lblReportPath" xml:space="preserve">
    <value>Formato do conteúdo do relatório</value>
  </data>
  <data name="lblReportPeriodEnding" xml:space="preserve">
    <value>Fim de Período de Apuração</value>
  </data>
  <data name="lblReportSaveChanges" xml:space="preserve">
    <value>Salve as alterações do relatório primeiro.</value>
  </data>
  <data name="lblReportsNoDataText" xml:space="preserve">
    <value>Nenhum relatório foi disponibilizado para esta categoria.</value>
  </data>
  <data name="lblReportSubCategory" xml:space="preserve">
    <value>Subcategoria</value>
  </data>
  <data name="lblReportSubmitBtnText" xml:space="preserve">
    <value>Enviar</value>
  </data>
  <data name="lblReportType" xml:space="preserve">
    <value>Tipo</value>
  </data>
  <data name="lblReportTypeActual" xml:space="preserve">
    <value>Atual</value>
  </data>
  <data name="lblReportTypeArchive" xml:space="preserve">
    <value>Arquivado</value>
  </data>
  <data name="lblReportTypeParamNameKey" xml:space="preserve">
    <value>Período orçamentário</value>
  </data>
  <data name="lblReportTypeSnapShot" xml:space="preserve">
    <value>Anterior</value>
  </data>
  <data name="lblReportViewType" xml:space="preserve">
    <value>Tipo de vista</value>
  </data>
  <data name="lblResetMessage" xml:space="preserve">
    <value>Tem certeza de que deseja redefinir?</value>
  </data>
  <data name="lblResetSuccessful" xml:space="preserve">
    <value>O relatório foi redefinido com sucesso</value>
  </data>
  <data name="lblrpt_sp_MOZ_M20_HDesc" xml:space="preserve">
    <value>Declaração anual de informações contábeis e fiscais.</value>
  </data>
  <data name="lblrpt_sp_MOZ_M20_HName" xml:space="preserve">
    <value>M/20 H</value>
  </data>
  <data name="lblRptDateRange" xml:space="preserve">
    <value>Intervalo de datas</value>
  </data>
  <data name="lblRun" xml:space="preserve">
    <value>Folha</value>
  </data>
  <data name="lblRunAndPeriod" xml:space="preserve">
    <value>Execução/Ponto</value>
  </data>
  <data name="lblRunId" xml:space="preserve">
    <value>Folha</value>
  </data>
  <data name="lblRunIdParamNameKey" xml:space="preserve">
    <value>Correr</value>
  </data>
  <data name="lblRunOn" xml:space="preserve">
    <value>Gerado em</value>
  </data>
  <data name="lblRunOrMonthOrYtd" xml:space="preserve">
    <value>Execução/mês/acumulado no ano</value>
  </data>
  <data name="lblRunSelect" xml:space="preserve">
    <value>Execução</value>
  </data>
  <data name="lblSA_UI_19Desc" xml:space="preserve">
    <value>Formulário de declaração UIF emitido para funcionários na rescisão do serviço (Novos compromissos e alterações são declarados automaticamente mensalmente).</value>
  </data>
  <data name="lblSA_UI_19Name" xml:space="preserve">
    <value>Interface do usuário 19</value>
  </data>
  <data name="lblSaveDescription" xml:space="preserve">
    <value>Insira uma descrição de quais alterações foram feitas.</value>
  </data>
  <data name="lblSaveMessage" xml:space="preserve">
    <value>Tem certeza de que deseja economizar?</value>
  </data>
  <data name="lblSavingsReportDesc" xml:space="preserve">
    <value>Fornece uma listagem dos descontos de empregado por processo.</value>
  </data>
  <data name="lblSavingsReportName" xml:space="preserve">
    <value>Relatório de Poupanças</value>
  </data>
  <data name="lblScoresHistoryDesc" xml:space="preserve">
    <value>Retorna todos os detalhes da classificação e a pontuação total do processo selecionado</value>
  </data>
  <data name="lblScoresHistoryName" xml:space="preserve">
    <value>Histórico de pontuações</value>
  </data>
  <data name="lblSearchPlaceHolderText" xml:space="preserve">
    <value>Pesquisar</value>
  </data>
  <data name="lblSecurityReportName" xml:space="preserve">
    <value>Relatório de segurança</value>
  </data>
  <data name="lblSelect" xml:space="preserve">
    <value>Selecionar</value>
  </data>
  <data name="lblSelectExtract" xml:space="preserve">
    <value>Selecione Extrair</value>
  </data>
  <data name="lblSettings" xml:space="preserve">
    <value>Opções de seleção</value>
  </data>
  <data name="lblShowCDiffParamNameKey" xml:space="preserve">
    <value>Mostrar apenas diferenças</value>
  </data>
  <data name="lblShowColumnsParamKey" xml:space="preserve">
    <value>Campos de relatório</value>
  </data>
  <data name="lblShowDifferenceDiff" xml:space="preserve">
    <value>Diferença (disponível apenas para execuções mensais)</value>
  </data>
  <data name="lblShowExtraColParamNameKey" xml:space="preserve">
    <value>Campos de relatório</value>
  </data>
  <data name="lblShowOpenPeriodsOnly" xml:space="preserve">
    <value>Exibir apenas períodos com dias remanescentes</value>
  </data>
  <data name="lblShowSumDifferenceSum" xml:space="preserve">
    <value>Soma</value>
  </data>
  <data name="lblShowSumDiffParamNameKey" xml:space="preserve">
    <value>Soma/Diferença</value>
  </data>
  <data name="lblShowTotalsPerNameKey" xml:space="preserve">
    <value>Mostrar totais por</value>
  </data>
  <data name="lblSnapshotIDParamNameKey" xml:space="preserve">
    <value>Selecionar orçamento instantâneo</value>
  </data>
  <data name="lblSnapshotParamNameKey" xml:space="preserve">
    <value>Selecionar orçamento instantâneo</value>
  </data>
  <data name="lblSortEmpLastName" xml:space="preserve">
    <value>Sobrenome do funcionário</value>
  </data>
  <data name="lblSortEmpName" xml:space="preserve">
    <value>Nome do Empregado</value>
  </data>
  <data name="lblSortEmpNumber" xml:space="preserve">
    <value>Número do Funcionário</value>
  </data>
  <data name="lblSortOrder" xml:space="preserve">
    <value>Ordem de classificação</value>
  </data>
  <data name="lblSortOrderParamNameKey" xml:space="preserve">
    <value>Ordem de classificação</value>
  </data>
  <data name="lblStandardReportTabName" xml:space="preserve">
    <value>Padrão</value>
  </data>
  <data name="lblStartDateParamNameKey" xml:space="preserve">
    <value>Data de início</value>
  </data>
  <data name="lblStatistics_SA_BreakdownDesc" xml:space="preserve">
    <value>Estatísticas SA Detalhamento</value>
  </data>
  <data name="lblStatistics_SA_BreakdownName" xml:space="preserve">
    <value>Estatísticas SA Detalhamento</value>
  </data>
  <data name="lblTaxCode" xml:space="preserve">
    <value>Código Tributário</value>
  </data>
  <data name="lblTaxCodeName" xml:space="preserve">
    <value>Código Interno da Rubrica</value>
  </data>
  <data name="lblTaxCountries" xml:space="preserve">
    <value>Países fiscais</value>
  </data>
  <data name="lblTaxRefNumber" xml:space="preserve">
    <value>Número de Referência Fiscal</value>
  </data>
  <data name="lblTaxStatus" xml:space="preserve">
    <value>Situação fiscal</value>
  </data>
  <data name="lblTemplateDisplayName" xml:space="preserve">
    <value>Insira um nome de exibição do modelo.</value>
  </data>
  <data name="lblTemplateDisplayNameInvalidFormat" xml:space="preserve">
    <value>Caracteres especiais não permitidos no nome de exibição do modelo</value>
  </data>
  <data name="lblTemplateDisplayNameRequired" xml:space="preserve">
    <value>Nome</value>
  </data>
  <data name="lblTerminatedFromDateParamKey" xml:space="preserve">
    <value>Incluir rescisões a partir desta data</value>
  </data>
  <data name="lblTerminatedParamNameKey" xml:space="preserve">
    <value>Incluir rescisões</value>
  </data>
  <data name="lblTerminationDate" xml:space="preserve">
    <value>Data de rescisão</value>
  </data>
  <data name="lblToDateNameKey" xml:space="preserve">
    <value>Incluir até esta data</value>
  </data>
  <data name="lblTotal" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="lblTotalCreditorParamKey" xml:space="preserve">
    <value>Mostrar totais por credor</value>
  </data>
  <data name="lblTotalEarnings" xml:space="preserve">
    <value>Ganhos totais</value>
  </data>
  <data name="lblTotalsFundParamDescKey" xml:space="preserve">
    <value>mostrar totais por fundo?</value>
  </data>
  <data name="lblTotalsFundParamNameKey" xml:space="preserve">
    <value>Mostrar totais por fundo?</value>
  </data>
  <data name="lblTotalsParamNameKey" xml:space="preserve">
    <value>Mostrar totais por esquema?</value>
  </data>
  <data name="lblTotalsTypeParamNameKey" xml:space="preserve">
    <value>Mostrar totais por tipo de licença?</value>
  </data>
  <data name="lblTrainingReportDesc" xml:space="preserve">
    <value>Fornece uma lista de funcionários que participaram ou não de cursos de treinamento por um período escolhido.</value>
  </data>
  <data name="lblTrainingReportName" xml:space="preserve">
    <value>Relatório de treinamento</value>
  </data>
  <data name="lblTransactionsReportDesc" xml:space="preserve">
    <value>Fornece uma lista de todas as transações de licença por um período especificado.</value>
  </data>
  <data name="lblTransactionsReportName" xml:space="preserve">
    <value>Relatório de transações</value>
  </data>
  <data name="lblTunisia_Annual_Individual_WHTDesc" xml:space="preserve">
    <value>Certificado anual de retenção na fonte do empregado</value>
  </data>
  <data name="lblTunisia_Annual_Individual_WHTName" xml:space="preserve">
    <value>Certificado de Imposto Retido na Fonte do Empregado</value>
  </data>
  <data name="lblTunisiaEmployerAnnualTaxReturnReportDesc" xml:space="preserve">
    <value>O relatório é usado para preencher o Anexo 1 da declaração anual do empregador</value>
  </data>
  <data name="lblTunisiaEmployerAnnualTaxReturnReportName" xml:space="preserve">
    <value>Declaração Anual de Imposto de Renda do Empregador - Anexo 1</value>
  </data>
  <data name="lblTypeFigures" xml:space="preserve">
    <value>Figuras</value>
  </data>
  <data name="lblTypeHeadCount" xml:space="preserve">
    <value>Contagem de cabeças</value>
  </data>
  <data name="lblUI19ReportDesc" xml:space="preserve">
    <value>Formulário de declaração UIF emitido para funcionários na rescisão do serviço (Novos compromissos e alterações são declarados automaticamente eletronicamente mensalmente).</value>
  </data>
  <data name="lblUI19ReportName" xml:space="preserve">
    <value>Interface do usuário 19</value>
  </data>
  <data name="lblUIFNumber" xml:space="preserve">
    <value>Número UIF</value>
  </data>
  <data name="lblUIFSalaryScheduleDesc" xml:space="preserve">
    <value>A Tabela Salarial pode ser solicitada pela UIF para verificar ou atualizar as informações do histórico de um funcionário.</value>
  </data>
  <data name="lblUIFSalaryScheduleName" xml:space="preserve">
    <value>Tabela Salarial UIF</value>
  </data>
  <data name="lblUK_Ann_P60Desc" xml:space="preserve">
    <value>Dê um certificado fiscal P60 a todos os funcionários ainda empregados no último dia do ano fiscal (5 de abril). O P60 é um resumo anual das informações da folha de pagamento necessárias para apresentar uma declaração de imposto.</value>
  </data>
  <data name="lblUK_Ann_P60Name" xml:space="preserve">
    <value>P60 Certificado de Imposto de Empregado</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_DetailDesc" xml:space="preserve">
    <value>Este relatório exibe os valores totais usados para calcular a Taxa de Aprendizagem por mês para o empregador selecionado.</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_DetailName" xml:space="preserve">
    <value>Detalhe mensal da taxa de aprendizagem</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_QuarterlyDesc" xml:space="preserve">
    <value>Este relatório exibe os valores totais usados para calcular a Taxa de Aprendizagem por trimestre para o empregador selecionado.</value>
  </data>
  <data name="lblUK_Apprenticeship_Levy_QuarterlyName" xml:space="preserve">
    <value>Detalhe trimestral da taxa de aprendizagem</value>
  </data>
  <data name="lblUK_P11DDesc" xml:space="preserve">
    <value>Relatório anual de benefícios em espécie para apresentação ao HM Revenue and Customs (HMRC)</value>
  </data>
  <data name="lblUK_P11DName" xml:space="preserve">
    <value>P11D - versão antiga</value>
  </data>
  <data name="lblUK_P30_QuarterlyDesc" xml:space="preserve">
    <value>Um resumo do passivo total do HMRC devido no trimestre selecionado. Imprima o P30 no final do trimestre após enviar o FPS e/ou EPS.</value>
  </data>
  <data name="lblUK_P30_QuarterlyName" xml:space="preserve">
    <value>P30 Resumo Trimestral do Empregador</value>
  </data>
  <data name="lblUK_P30Desc" xml:space="preserve">
    <value>Um resumo do passivo total do HMRC devido para o mês selecionado. Imprima o P30 no final do mês após enviar o FPS e/ou EPS.</value>
  </data>
  <data name="lblUK_P30Name" xml:space="preserve">
    <value>P30 Resumo Mensal do Empregador</value>
  </data>
  <data name="lblUK_P32_MonthlyDesc" xml:space="preserve">
    <value>O P32 Monthly Employer Payment Detail é usado pelos empregadores para relatar os detalhes de suas atividades de folha de pagamento ao HMRC mensalmente. Normalmente inclui informações sobre deduções de imposto de renda de funcionários (PAYE), contribuições para o Seguro Nacional (NICs) e outros detalhes relevantes da folha de pagamento para o mês.</value>
  </data>
  <data name="lblUK_P32_MonthlyName" xml:space="preserve">
    <value>P32 Detalhe do pagamento mensal do empregador</value>
  </data>
  <data name="lblUK_P32_QuarterlyDesc" xml:space="preserve">
    <value>O P32 é o Relatório de Detalhes de Pagamento do Empregador. É um resumo total dos valores que você pagou ao HMRC a cada mês. Isso inclui todos os PAYE, deduções de empréstimos estudantis e contribuições para o Seguro Nacional.</value>
  </data>
  <data name="lblUK_P32_QuarterlyName" xml:space="preserve">
    <value>P32 Detalhes trimestrais do pagamento do empregador</value>
  </data>
  <data name="lblUnit" xml:space="preserve">
    <value>Unidade</value>
  </data>
  <data name="lblUploadReport" xml:space="preserve">
    <value>Enviar Relatório</value>
  </data>
  <data name="lblUploadReportHeader" xml:space="preserve">
    <value>Carregar relatório</value>
  </data>
  <data name="lblUser" xml:space="preserve">
    <value>Utilizador</value>
  </data>
  <data name="lblUsername" xml:space="preserve">
    <value>Utilizador</value>
  </data>
  <data name="lblValueRequired" xml:space="preserve">
    <value>O valor é necessário</value>
  </data>
  <data name="lblVersions" xml:space="preserve">
    <value>Versões</value>
  </data>
  <data name="lblViewDiffCurrencyParamNameKey" xml:space="preserve">
    <value>Ver em moeda diferente</value>
  </data>
  <data name="lblViewEmpHomeCurrencyParamNameKey" xml:space="preserve">
    <value>Veja este relatório com base nos funcionários vinculados a uma moeda específica</value>
  </data>
  <data name="lblViewInHomeCurrency" xml:space="preserve">
    <value>Moeda local</value>
  </data>
  <data name="lblViewInHomeCurrencyCheckBoxNameKey" xml:space="preserve">
    <value>Ver na moeda local</value>
  </data>
  <data name="lblYTDAmount" xml:space="preserve">
    <value>Valor acumulado no ano</value>
  </data>
  <data name="lblYTDParamNameKey" xml:space="preserve">
    <value>Desenhe este relatório para o YTD para uma execução específica</value>
  </data>
  <data name="lblYtdSelect" xml:space="preserve">
    <value>Acumulado no ano</value>
  </data>
  <data name="Leg_Malaysia_EA_FormDesc" xml:space="preserve">
    <value>O "Formulário EA" é um documento importante usado para fins fiscais. É uma declaração anual de impostos fornecida pelos empregadores a seus funcionários, detalhando os ganhos, subsídios, deduções e contribuições fiscais do funcionário para o ano.</value>
  </data>
  <data name="Leg_Malaysia_EA_FormName" xml:space="preserve">
    <value>Formulário EA</value>
  </data>
  <data name="Leg_Malaysia_PCB2Desc" xml:space="preserve">
    <value>PCB2 (Potongan Cukai Berjadual 2) refere-se ao Formulário de Dedução Fiscal Programada 2 na Malásia. Este formulário é usado para a dedução do imposto mensal (potongan cukai bulanan) do salário de um funcionário. O PCB2 se aplica especificamente a funcionários cuja renda está sujeita a deduções fiscais programadas com base nas leis de imposto de renda da Malásia.</value>
  </data>
  <data name="Leg_Malaysia_PCB2Name" xml:space="preserve">
    <value>PCB2</value>
  </data>
  <data name="Leg_Malaysia_Tabung_HajiDesc" xml:space="preserve">
    <value>O formulário Tabung Haji refere-se a um formulário relacionado às contribuições em folha de pagamento feitas ao Lembaga Tabung Haji. Este formulário é usado pelos empregadores para facilitar a dedução automática das contribuições dos funcionários para suas contas Tabung Haji mensalmente.</value>
  </data>
  <data name="Leg_Malaysia_Tabung_HajiName" xml:space="preserve">
    <value>Tabung Haji</value>
  </data>
  <data name="Leg_Malaysia_Zakat_KL_ReportDesc" xml:space="preserve">
    <value>Um formulário de Zakat é usado por indivíduos ou organizações para calcular sistematicamente sua responsabilidade de Zakat com base nos princípios islâmicos. Ajuda a documentar os ativos, deduções e o valor calculado do Zakat, garantindo o cumprimento das obrigações religiosas. Apenas na região de Kuala Lumpur Zakat.</value>
  </data>
  <data name="Leg_Malaysia_Zakat_KL_ReportName" xml:space="preserve">
    <value>Zakat</value>
  </data>
  <data name="Leg_Malaysia_Zakat_SelagnorDesc" xml:space="preserve">
    <value>Este documento é uma forma de pagamento para o Esquema Berkat do Selangor Zakat Board (MAIS). Inclui detalhes como nome e endereço do empregador, número de funcionários e o valor total do pagamento a ser feito ao MAIS antes do dia 10 de cada mês. O formulário fornece instruções para preenchê-lo corretamente, como organizar os nomes dos funcionários em ordem alfabética e efetuar o pagamento verificando os detalhes de pagamento ao MAIS.</value>
  </data>
  <data name="Leg_Malaysia_Zakat_SelagnorName" xml:space="preserve">
    <value>Zakat Selangor</value>
  </data>
  <data name="Leg_Tanzania_Electronic_Monthly_PAYEDesc" xml:space="preserve">
    <value>Arquivo de importação mensal PAYE usado para importar para a planilha macro, para envio online.</value>
  </data>
  <data name="Leg_Tanzania_Electronic_Monthly_PAYEName" xml:space="preserve">
    <value>Modelo Eletrônico para PAYE Mensal</value>
  </data>
  <data name="msgResetFail" xml:space="preserve">
    <value>Os relatórios do nível país não podem ser resetados</value>
  </data>
  <data name="PreparingReport" xml:space="preserve">
    <value>Preparando o relatório</value>
  </data>
  <data name="ReportDesigner" xml:space="preserve">
    <value>Construtor de relatórios</value>
  </data>
  <data name="reportDetails" xml:space="preserve">
    <value>Detalhes do relatório</value>
  </data>
  <data name="ReportHistory" xml:space="preserve">
    <value>Histórico de relatórios</value>
  </data>
  <data name="ReportUploadCancelText" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="ReportUploadDetails" xml:space="preserve">
    <value>Detalhes do relatório</value>
  </data>
  <data name="ReportUploadHeading" xml:space="preserve">
    <value>Carregar relatório</value>
  </data>
  <data name="ReportUploadReportType" xml:space="preserve">
    <value>Tipo de relatório</value>
  </data>
  <data name="ReportUploadSubheading" xml:space="preserve">
    <value>Arquivo do relatório</value>
  </data>
  <data name="ReportUploadText" xml:space="preserve">
    <value>Carregar arquivo</value>
  </data>
</root>