<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="104" xml:space="preserve">
    <value>Crear una nómina de prueba</value>
  </data>
  <data name="107" xml:space="preserve">
    <value>Posición</value>
  </data>
  <data name="108" xml:space="preserve">
    <value>Detalles de la Posición</value>
  </data>
  <data name="110" xml:space="preserve">
    <value>Datos Bancarios</value>
  </data>
  <data name="115" xml:space="preserve">
    <value>Administración de Ausencias</value>
  </data>
  <data name="116" xml:space="preserve">
    <value>Configuración del Esquema de Ausencias</value>
  </data>
  <data name="117" xml:space="preserve">
    <value>Parámetros del Esquema de Ausencias</value>
  </data>
  <data name="118" xml:space="preserve">
    <value>Ajustes de Ausencias</value>
  </data>
  <data name="119" xml:space="preserve">
    <value>Gestión de Ausencias</value>
  </data>
  <data name="12" xml:space="preserve">
    <value>Menú</value>
  </data>
  <data name="120" xml:space="preserve">
    <value>Solicitud de Permiso</value>
  </data>
  <data name="123" xml:space="preserve">
    <value>Configuración de Ausencias</value>
  </data>
  <data name="129" xml:space="preserve">
    <value>Reportes</value>
  </data>
  <data name="141" xml:space="preserve">
    <value>Información Básica</value>
  </data>
  <data name="145" xml:space="preserve">
    <value>Dar de Baja / Reincorporar o Pausar Empleo</value>
  </data>
  <data name="146" xml:space="preserve">
    <value>Dar de Baja / Reincorporar</value>
  </data>
  <data name="15" xml:space="preserve">
    <value>Creditores</value>
  </data>
  <data name="151" xml:space="preserve">
    <value>Configuración del Cálculo Promedio de Ingresos por Permiso</value>
  </data>
  <data name="153" xml:space="preserve">
    <value>Balance de Ausencias</value>
  </data>
  <data name="166" xml:space="preserve">
    <value>Datos Bancarios</value>
  </data>
  <data name="173" xml:space="preserve">
    <value>Capacitación / Habilidades / Cualificaciones</value>
  </data>
  <data name="177" xml:space="preserve">
    <value>Habilidades</value>
  </data>
  <data name="178" xml:space="preserve">
    <value>Habilidades</value>
  </data>
  <data name="18" xml:space="preserve">
    <value>Empleado</value>
  </data>
  <data name="184" xml:space="preserve">
    <value>Costeo</value>
  </data>
  <data name="185" xml:space="preserve">
    <value>Costeo de Proyecto</value>
  </data>
  <data name="192" xml:space="preserve">
    <value>Calendario de Permisos</value>
  </data>
  <data name="197" xml:space="preserve">
    <value>Resumen de Permisos de Ausencia</value>
  </data>
  <data name="198" xml:space="preserve">
    <value>Administración de la Evaluación de Desempeño</value>
  </data>
  <data name="2" xml:space="preserve">
    <value>Empresa</value>
  </data>
  <data name="204" xml:space="preserve">
    <value>Evaluaciones de Desempeño</value>
  </data>
  <data name="206" xml:space="preserve">
    <value>Diario de Desempeño</value>
  </data>
  <data name="211" xml:space="preserve">
    <value>Calendario</value>
  </data>
  <data name="212" xml:space="preserve">
    <value>Resumen de Permisos de Ausencia</value>
  </data>
  <data name="217" xml:space="preserve">
    <value>Datos Adjuntos</value>
  </data>
  <data name="22" xml:space="preserve">
    <value>Comp. Salariales Recurrentes</value>
  </data>
  <data name="221" xml:space="preserve">
    <value>Habilidades / Capacitación / Cualificaciones</value>
  </data>
  <data name="224" xml:space="preserve">
    <value>Transferir</value>
  </data>
  <data name="23" xml:space="preserve">
    <value>Datos Tarifa Salarial</value>
  </data>
  <data name="24" xml:space="preserve">
    <value>Perfil Básico</value>
  </data>
  <data name="256" xml:space="preserve">
    <value>Histórico de Transferencias</value>
  </data>
  <data name="258" xml:space="preserve">
    <value>Registro de Activos del Empleado</value>
  </data>
  <data name="271" xml:space="preserve">
    <value>Notas / Recordatorios</value>
  </data>
  <data name="288" xml:space="preserve">
    <value>Tarifas del Proyecto</value>
  </data>
  <data name="290" xml:space="preserve">
    <value>Bandeja de Entrada</value>
  </data>
  <data name="293" xml:space="preserve">
    <value>Visión del Gerente</value>
  </data>
  <data name="4" xml:space="preserve">
    <value>Añadir Nueva Empresa</value>
  </data>
  <data name="40306" xml:space="preserve">
    <value>Gestión de Incidencias</value>
  </data>
  <data name="50376" xml:space="preserve">
    <value>Lista de Dependientes</value>
  </data>
  <data name="50381" xml:space="preserve">
    <value>Reporte de Incidencias</value>
  </data>
  <data name="60416" xml:space="preserve">
    <value>Reclamaciones</value>
  </data>
  <data name="60440" xml:space="preserve">
    <value>Definición de los Procesos de Nómina</value>
  </data>
  <data name="60497" xml:space="preserve">
    <value>Declaración del Impuesto de Desarrollo Estándar</value>
  </data>
  <data name="60508" xml:space="preserve">
    <value>Suspensión del Empleado</value>
  </data>
  <data name="60526" xml:space="preserve">
    <value>Reportes</value>
  </data>
  <data name="60584" xml:space="preserve">
    <value>Costeo Recurrente</value>
  </data>
  <data name="60687" xml:space="preserve">
    <value>Solicitud de Modificación</value>
  </data>
  <data name="67" xml:space="preserve">
    <value>Añadir Nuevo Empleado</value>
  </data>
  <data name="71105" xml:space="preserve">
    <value>Ajustes de Ausencias</value>
  </data>
  <data name="71108" xml:space="preserve">
    <value>Ajustes de Gestión del Desempeño</value>
  </data>
  <data name="71393" xml:space="preserve">
    <value>Suspensión</value>
  </data>
  <data name="88" xml:space="preserve">
    <value>Dependientes</value>
  </data>
  <data name="89" xml:space="preserve">
    <value>Perfil Fiscal</value>
  </data>
  <data name="92" xml:space="preserve">
    <value>Suspensión</value>
  </data>
  <data name="96" xml:space="preserve">
    <value>Acumulado del Año hasta la Fecha</value>
  </data>
  <data name="addnewemployee" xml:space="preserve">
    <value>Añadir Nuevo Empleado</value>
  </data>
  <data name="BasicProfileInfo" xml:space="preserve">
    <value>Perfil Básico</value>
  </data>
  <data name="bulkupload" xml:space="preserve">
    <value>Cargas Masivas</value>
  </data>
  <data name="editpayslip" xml:space="preserve">
    <value>Editar Nómina</value>
  </data>
  <data name="lblUpdateEmployee" xml:space="preserve">
    <value>Actualizar Datos del Empleado</value>
  </data>
  <data name="Leave" xml:space="preserve">
    <value>Ausencias</value>
  </data>
  <data name="MenuBulkUpload" xml:space="preserve">
    <value>Menú de Cargas Masivas</value>
  </data>
  <data name="MenuEmployee" xml:space="preserve">
    <value>Empleado</value>
  </data>
  <data name="MenuOnOffBoarding" xml:space="preserve">
    <value>Altas / Bajas</value>
  </data>
  <data name="MenuOther" xml:space="preserve">
    <value>Otros</value>
  </data>
  <data name="MenuPayrollCycle" xml:space="preserve">
    <value>Ciclo de Nómina</value>
  </data>
  <data name="MenuReport" xml:space="preserve">
    <value>Reportes</value>
  </data>
  <data name="miscelleneous" xml:space="preserve">
    <value>Otros</value>
  </data>
  <data name="onBehalfOf" xml:space="preserve">
    <value>Actuar En Nombre De</value>
  </data>
  <data name="ONOffBoarding" xml:space="preserve">
    <value>Altas / Bajas</value>
  </data>
  <data name="other" xml:space="preserve">
    <value>Otros</value>
  </data>
  <data name="payrollprocessing" xml:space="preserve">
    <value>Procesamiento de Nómina</value>
  </data>
  <data name="payrollresults" xml:space="preserve">
    <value>Resultados de Nómina</value>
  </data>
  <data name="perfmanagement" xml:space="preserve">
    <value>Gestión del Desempeño</value>
  </data>
  <data name="Performance" xml:space="preserve">
    <value>Desempeño</value>
  </data>
  <data name="performancehistory" xml:space="preserve">
    <value>Histórico de Desempeño</value>
  </data>
  <data name="PositionOrg" xml:space="preserve">
    <value>Posición</value>
  </data>
  <data name="reportsexports" xml:space="preserve">
    <value>Reportes</value>
  </data>
  <data name="skill" xml:space="preserve">
    <value>Habilidades</value>
  </data>
  <data name="Terminate" xml:space="preserve">
    <value>Dar de Baja / Reincorporar</value>
  </data>
</root>