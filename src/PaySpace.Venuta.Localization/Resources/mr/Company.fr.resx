<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Address" xml:space="preserve">
    <value>Adresse de l'entreprise</value>
  </data>
  <data name="AgencyId" xml:space="preserve">
    <value>Partenaire commercial associé</value>
  </data>
  <data name="AgencyNameOverride" xml:space="preserve">
    <value>Remplacer la signature</value>
  </data>
  <data name="AgencyUrlOverride" xml:space="preserve">
    <value>Remplacer l'URL</value>
  </data>
  <data name="CityOrTown" xml:space="preserve">
    <value>Municipalité</value>
  </data>
  <data name="CloudAnalytics" xml:space="preserve">
    <value>Ajouter cette société (+ toutes les autres sociétés du groupe) à Cloud Analytics ?</value>
  </data>
  <data name="CompanyAddress" xml:space="preserve">
    <value>Adresse de l'entreprise</value>
  </data>
  <data name="CompanyContactFaxNo" xml:space="preserve">
    <value>Numéro de fax</value>
  </data>
  <data name="CompanyContactNo" xml:space="preserve">
    <value>Numéro de téléphone fixe</value>
  </data>
  <data name="CompanyContactPerson" xml:space="preserve">
    <value>Contact entreprise</value>
  </data>
  <data name="CompanyDetails" xml:space="preserve">
    <value>Société</value>
  </data>
  <data name="CompanyEmailPayslips" xml:space="preserve">
    <value>Envoyer automatiquement une notification lors de la mise à disposition d'une fiche de paie</value>
  </data>
  <data name="CompanyGroupName" xml:space="preserve">
    <value>Nom du groupe</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Logo</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Nom de l'entreprise</value>
  </data>
  <data name="CompanyThemes" xml:space="preserve">
    <value>Personnalisation</value>
  </data>
  <data name="CompanyTradingName" xml:space="preserve">
    <value>Raison sociale</value>
  </data>
  <data name="ComplexName" xml:space="preserve">
    <value>Résidence</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="DisableCalculations" xml:space="preserve">
    <value>Désactiver les calculs lors de l'équilibrage pour accélérer le processus</value>
  </data>
  <data name="Edition" xml:space="preserve">
    <value>Édition</value>
  </data>
  <data name="EmailCompanyId" xml:space="preserve">
    <value>Matricule</value>
  </data>
  <data name="EnableLockout" xml:space="preserve">
    <value>Activer le verrouillage du système</value>
  </data>
  <data name="EnablePasswordHashing" xml:space="preserve">
    <value>Activer le hachage de mot de passe</value>
  </data>
  <data name="GeneralDetails" xml:space="preserve">
    <value>Général</value>
  </data>
  <data name="GenerateEmployeeNumbers" xml:space="preserve">
    <value>Générer automatiquement les matricules</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Entreprise inactive</value>
  </data>
  <data name="InactiveCompany" xml:space="preserve">
    <value>Entreprise inactive</value>
  </data>
  <data name="lblCityTown" xml:space="preserve">
    <value>Municipalité</value>
  </data>
  <data name="lblCode" xml:space="preserve">
    <value>Code Postal</value>
  </data>
  <data name="lblComplexName" xml:space="preserve">
    <value>Résidence</value>
  </data>
  <data name="lblCountry" xml:space="preserve">
    <value>Pays</value>
  </data>
  <data name="lblDetails" xml:space="preserve">
    <value>Détails</value>
  </data>
  <data name="lblDistributionService" xml:space="preserve">
    <value>Service de distribution</value>
  </data>
  <data name="lblNavbarColor" xml:space="preserve">
    <value>Couleur de la barre de navigation</value>
  </data>
  <data name="lblPrimaryColor" xml:space="preserve">
    <value>Couleur principale</value>
  </data>
  <data name="lblStreetName" xml:space="preserve">
    <value>Type et nom de la voie</value>
  </data>
  <data name="lblStreetNum" xml:space="preserve">
    <value>Numéro</value>
  </data>
  <data name="LoginPage" xml:space="preserve">
    <value>Fond d'écran de connexion</value>
  </data>
  <data name="MfaCompanyLevel" xml:space="preserve">
    <value>Option MFA</value>
  </data>
  <data name="MfaOption" xml:space="preserve">
    <value>Option MFA</value>
  </data>
  <data name="NavigationBarColour" xml:space="preserve">
    <value>Couleur de la barre de navigation</value>
  </data>
  <data name="OutsourceCustomer" xml:space="preserve">
    <value>Client externalisé</value>
  </data>
  <data name="PasswordExpiryPeriod" xml:space="preserve">
    <value>Expiration du mot de passe</value>
  </data>
  <data name="PasswordExpiryPeriodOverride" xml:space="preserve">
    <value>Expiration du mot de passe</value>
  </data>
  <data name="SalesOwner" xml:space="preserve">
    <value>Responsable commercial</value>
  </data>
  <data name="SignatureOverride" xml:space="preserve">
    <value>Remplacer la signature</value>
  </data>
  <data name="StatutoryFields" xml:space="preserve">
    <value>Informations juridiques</value>
  </data>
  <data name="StreetName" xml:space="preserve">
    <value>Type et nom de la voie</value>
  </data>
  <data name="StreetNumber" xml:space="preserve">
    <value>Numéro</value>
  </data>
  <data name="TaxAuthorityRequired" xml:space="preserve">
    <value>Autorité fiscale</value>
  </data>
  <data name="TaxCountryId" xml:space="preserve">
    <value>Autorité fiscale</value>
  </data>
  <data name="TestDemoCompany" xml:space="preserve">
    <value>Entreprise test/démonstration</value>
  </data>
  <data name="UrlOverride" xml:space="preserve">
    <value>Remplacer l'URL</value>
  </data>
</root>