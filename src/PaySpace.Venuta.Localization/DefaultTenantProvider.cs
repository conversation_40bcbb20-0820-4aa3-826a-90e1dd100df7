namespace PaySpace.Venuta.Localization
{
    using System;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models;

    public class DefaultTenantProvider : ITenantProvider
    {
        public long GetUserId() => throw new NotImplementedException();

        public UserType GetUserType() => throw new NotImplementedException();

        public long GetAgencyId() => throw new NotImplementedException();

        public long? GetCompanyGroupId() => null;

        public long? GetCompanyId() => null;

        public long? GetEmployeeId() => null;

        public long? GetFrequencyId() => null;

        public string? GetTaxCountryCode() => null;

        public int GetDecimalPlaces() => 2;

        public bool CanEditHistoricalRecords() => false;
    }
}
