namespace PaySpace.Venuta.Abstractions
{
    public static class BulkUploadCategory
    {
        public const string BasicInformation = "lblBasicInformation";
        public const string PayrollProcessing = "lblPayrollProcessing";
        public const string Leave = "lblLeaveCategory";
        public const string Other = "lblOtherCategory";
        public const string OnOffBoarding = "lblOnOffBoarding";
        public const string Performance = "lblPerformance";
        public const string PayrollConfig = "lblPayrollConfig";
        public const string Skills = "lblSkills";
        public const string Costing = "lblCosting";
        public const string Upskilling = "lblUpskilling";
        public const string OrganizationStructure = "lblOrganizationStructure";
        public const string CustomScreens = "lblCustomScreensAndFields";
        public const string Pacey = "lblPacey";
        public const string DropdownManagement = "lblDropDownManagement";
        public const string WorkforcePlanning = "lblWorkforcePlanning";
        public const string LeaveAdministration = "lblLeaveAdministration";
        public const string CountryAdministration = "lblCountryAdministration";
    }
}