namespace PaySpace.Venuta.Abstractions
{
    using PaySpace.Venuta.Data.Models;

    public interface ITenantProvider
    {
        long GetUserId();

        UserType GetUserType();

        long GetAgencyId();

        long? GetCompanyGroupId();

        long? GetCompanyId();

        long? GetEmployeeId();

        long? GetFrequencyId();

        string? GetTaxCountryCode();

        int GetDecimalPlaces();

        bool CanEditHistoricalRecords();
    }
}
