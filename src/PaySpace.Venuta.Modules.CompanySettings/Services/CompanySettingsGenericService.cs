namespace PaySpace.Venuta.Modules.CompanySettings.Services
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Calculation.Cache.Common.Enums;
    using PaySpace.Calculation.Cache.Common.Helpers;
    using PaySpace.Calculation.Cache.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;

    public class CompanySettingsGenericService : ICompanySettingsGenericService
    {
        private readonly ApplicationContext context;
        private readonly ICalculationCacheWebApiClient calcCacheWebApiClient;
        private readonly ICompanyService companyService;
        private readonly ICompanyPaymentModuleService companyPaymentModuleService;
        private readonly IDistributedCache distributedCache;

        private sealed record EditionRestrictionOverride(string SettingCode, bool ShowLite, bool ShowPremier, bool ShowMaster);

        private static readonly Dictionary<string, IList<EditionRestrictionOverride>> CountryEditionRestrictionOverride =
            new()
            {
                {
                    "BF", new List<EditionRestrictionOverride>
                    {
                        new("PAYPNT", true, true, true),
                        new("CAT", true, true, true)
                    }
                }
            };

        public CompanySettingsGenericService(
            ApplicationContext context,
            ICalculationCacheWebApiClient calcCacheWebApiClient,
            ICompanyService companyService,
            ICompanyPaymentModuleService companyPaymentModuleService,
            IDistributedCache distributedCache)
        {
            this.context = context;
            this.calcCacheWebApiClient = calcCacheWebApiClient;
            this.companyService = companyService;
            this.companyPaymentModuleService = companyPaymentModuleService;
            this.distributedCache = distributedCache;
        }

        public async Task<IQueryable<CompanySettingsModel>> GetAllCompanySettingsByScreenAsync(long companyId, string screen)
        {
            var paymentModule = await this.companyPaymentModuleService.GetPaymentModuleAsync(companyId);
            var countryCode = await this.companyService.GetTaxCountryCodeAsync(companyId);

            var companySettings = from ecst in this.FilterByPaymentModule(this.context.Set<EnumCompanySettingType>().AsNoTracking(), paymentModule, countryCode)
                join cs2 in this.context.Set<CompanySetting>() on new { ecst.SettingTypeId, CompanyId = companyId } equals new { cs2.SettingTypeId, cs2.CompanyId } into settings
                from cs in settings.DefaultIfEmpty()
                where ecst.Screen == screen
                orderby ecst.OrderNumber
                select new CompanySettingsModel
                {
                    SettingId = cs.SettingId,
                    CompanyId = cs.CompanyId,
                    SettingTypeId = ecst.SettingTypeId,
                    SettingTypeCode = ecst.SettingTypeCode,
                    SettingValue = cs.SettingValue,
                    SettingIndicator = cs.SettingIndicator,
                    InputType = ecst.InputType,
                    Options = GetCountryOptions(ecst.Options, countryCode),
                    Screen = ecst.Screen,
                    SettingGroup = ecst.CompanySettingGroup.SettingGroupCode,
                    OrderNumber = ecst.OrderNumber,
                    CountryHidden = ecst.CountryHidden,
                    CountryVisible = ecst.CountryVisible
                };

            // Below will filter settings based on if a country requires the setting or not
            // CountryHidden - Settings that have to be hidden for specific countries
            // CountryVisible - Settings that have to be displayed for specific countries
            return companySettings.Where(
                _ => (_.CountryHidden.Length == 0 && _.CountryVisible.Length == 0)
                     || ((_.CountryHidden.Length > 0) && (!_.CountryHidden.Contains(countryCode)))
                     || ((_.CountryVisible.Length > 0) && _.CountryVisible.Contains(countryCode))
            );
        }

        public IQueryable<CompanySetting> GetExistingCompanySettingsByScreen(long companyId, string screen)
        {
            return this.context.Set<CompanySetting>()
                .Include(_ => _.SettingType)
                .Where(setting => setting.CompanyId == companyId && setting.SettingType.Screen == screen);
        }

        public Task<string> GetCompanySettingDescriptionAsync(long settingTypeId)
        {
            return this.context.Set<EnumCompanySettingType>()
                .AsNoTracking()
                .Where(_ => _.SettingTypeId == settingTypeId)
                .Select(_ => _.SettingTypeDescription)
                .FirstOrDefaultAsync();
        }

        public async Task<List<CompanySetting>> ProcessCompanySettingsAsync(List<CompanySettingsModel> values, string screenName, long companyId)
        {
            var companySettingsToUpdate = this.GetExistingCompanySettingsByScreen(companyId, screenName).ToList();
            var allCompanySettings = (await this.GetAllCompanySettingsByScreenAsync(companyId, screenName))
                .Where(_ => _.SettingId == null);

            foreach (var setting in values)
            {
                var existingSetting = companySettingsToUpdate.Find(_ => _.SettingType?.SettingTypeCode == setting.SettingTypeCode);

                var newSetting = (existingSetting is null)
                    ? await allCompanySettings.FirstOrDefaultAsync(_ => _.SettingTypeCode == setting.SettingTypeCode)
                    : null;

                var isCheckBoxOrSwitch = (existingSetting is null)
                    ? newSetting!.InputType is "toggle" or "checkbox"
                    : existingSetting.SettingType.InputType is "toggle" or "checkbox";

                // Do not update settings which the user did not add/update values for.
                if (setting.IsReadonly || !this.ShouldUpdateSetting(existingSetting, newSetting, setting.SettingValue, setting.SettingIndicator, isCheckBoxOrSwitch))
                {
                    continue;
                }

                // Setting value is null when cleared on form.
                var settingValue = setting.SettingValue ?? string.Empty;

                // Edit existing setting, if setting does not exist, then create it.
                if (existingSetting is not null)
                {
                    existingSetting.SettingIndicator = isCheckBoxOrSwitch
                        ? setting.SettingIndicator ?? false
                        : !string.IsNullOrWhiteSpace(settingValue);

                    existingSetting.SettingValue = isCheckBoxOrSwitch ? string.Empty : settingValue!.Trim();
                }
                else
                {
                    companySettingsToUpdate.Add(
                        new CompanySetting
                        {
                            CompanyId = companyId,
                            SettingTypeId = newSetting.SettingTypeId,
                            SettingIndicator = isCheckBoxOrSwitch
                                ? setting.SettingIndicator ?? false
                                : !string.IsNullOrWhiteSpace(settingValue),
                            SettingValue = isCheckBoxOrSwitch ? string.Empty : settingValue!.Trim()
                        });
                }
            }

            return companySettingsToUpdate;
        }

        public async Task SaveAsync(List<CompanySetting> companySettings, long companyId)
        {
            this.context.Set<CompanySetting>().UpdateRange(companySettings.Where(_ => _.SettingId > 0));
            this.context.Set<CompanySetting>().AddRange(companySettings.Where(_ => _.SettingId == 0));

            await this.context.SaveChangesAsync();

            // Ensure both NextGen and Calc setting-cache is cleared
            var cacheLevelKey = CacheKeyHelper.CreateCacheLevelKey(companyId, KeyLevelEnum.Company);
            await this.calcCacheWebApiClient.RemoveByKeyLevelAsync(cacheLevelKey);
            await this.distributedCache.RemoveAsync(CacheKeys.CompanySettings(companyId));
            await this.distributedCache.RemoveAsync(IdentityCacheKeys.CompanySettings(companyId));
        }

        public Task<bool> IsOnNewTaxCalcTemplateSetAsync(int taxCountryId)
        {
            // Only display a specific setting if this property is true
            return this.context.Set<EnumTaxCountry>().TagWithSource()
                .AnyAsync(
                    _ => _.CountryId == taxCountryId &&
                         _.OnNewTaxCalcTemplate == true);
        }

        public Task<EnumCompanySettingType> GetEnumCompanySettingTypeAsync(int settingTypeId)
        {
            return this.context.Set<EnumCompanySettingType>()
                .AsNoTracking().FirstOrDefaultAsync(c => c.SettingTypeId == settingTypeId);
        }

        public async Task AddSettingAsync(long companyId, string settingCode, bool settingIndicator, string settingValue = "")
        {
            var settingTypeId = await this.context.Set<EnumCompanySettingType>().TagWithSource()
                .Where(_ => _.SettingTypeCode == settingCode)
                .Select(_ => _.SettingTypeId)
                .SingleOrDefaultAsync();

            if (settingTypeId != default)
            {
                await this.context.Set<CompanySetting>().AddAsync(
                    new CompanySetting
                    {
                        CompanyId = companyId,
                        SettingTypeId = settingTypeId,
                        SettingIndicator = settingIndicator,
                        SettingValue = settingValue
                    });

                await this.context.SaveChangesAsync();
            }
        }

        public virtual List<CompanySettingsModel> SetDefaultCompanySettings(List<CompanySettingsModel> settings, long companyId, bool isNewCompany = false)
        {
            foreach (var setting in settings.Where(_ => !_.SettingIndicator.GetValueOrDefault()))
            {
                if (setting.SettingTypeCode is CompanyCalculationSettingConstants.ProrateLookupCode or
                        CompanyCalculationSettingConstants.WorkingDaysOption ||
                    (setting.SettingTypeCode is CompanyCalculationSettingConstants.CalendarDaysOption && !isNewCompany))
                {
                    setting.CompanyId = companyId;
                    setting.SettingIndicator = true;
                    setting.SettingValue = "2";
                }
            }

            return settings;
        }

        public async Task AddDefaultSettingsAsync(long companyId)
        {
            // Add default calculation settings
            var calculationSettings = await this.GetAllCompanySettingsByScreenAsync(companyId, CompanySettingConstants.CompanyCalculationSettingsController);

            var defaultCalculationSettings = this.SetDefaultCompanySettings(await calculationSettings.ToListAsync(), companyId, isNewCompany: true);

            var calculationSettingsToUpdate = await this.ProcessCompanySettingsAsync(defaultCalculationSettings, CompanySettingConstants.CompanyCalculationSettingsController, companyId);

            await this.SaveAsync(calculationSettingsToUpdate, companyId);
        }

        private IQueryable<EnumCompanySettingType> FilterByPaymentModule(IQueryable<EnumCompanySettingType> query, PaymentModule paymentModule, string countryCode)
        {
            switch (paymentModule)
            {
                case PaymentModule.Lite:
                case PaymentModule.NewLite:
                    var liteOverrides = CountryEditionRestrictionOverride
                        .SingleOrDefault(_ => _.Key == countryCode).Value?
                        .Where(_ => _.ShowLite)
                        .Select(_ => _.SettingCode);
                    return query.Where(setting => setting.ShowLite || (liteOverrides != null && liteOverrides.Contains(setting.SettingTypeCode)));

                case PaymentModule.Premier:
                case PaymentModule.NewPremier:
                    var premierOverrides = CountryEditionRestrictionOverride
                        .SingleOrDefault(_ => _.Key == countryCode).Value?
                        .Where(_ => _.ShowPremier)
                        .Select(_ => _.SettingCode);
                    return query.Where(setting => setting.ShowPremier || (premierOverrides != null && premierOverrides.Contains(setting.SettingTypeCode)));

                case PaymentModule.Master:
                case PaymentModule.NewMaster:
                    var masterOverrides = CountryEditionRestrictionOverride
                        .SingleOrDefault(_ => _.Key == countryCode).Value?
                        .Where(_ => _.ShowLite)
                        .Select(_ => _.SettingCode);
                    return query.Where(setting => setting.ShowMaster || (masterOverrides != null && masterOverrides.Contains(setting.SettingTypeCode)));

                default:
                    return query;
            }
        }

        private bool ShouldUpdateSetting(CompanySetting existingSetting, CompanySettingsModel newSetting, string settingValue, bool? settingIndicator, bool isCheckBoxOrSwitch)
        {
            if (existingSetting is null)
            {
                return (!isCheckBoxOrSwitch && !string.IsNullOrEmpty(settingValue)) ||
                       (!isCheckBoxOrSwitch && newSetting.SettingValue != settingValue) ||
                       (isCheckBoxOrSwitch && newSetting.SettingIndicator != settingIndicator);
            }

            return (isCheckBoxOrSwitch && existingSetting.SettingIndicator != settingIndicator) ||
                   (!isCheckBoxOrSwitch && existingSetting.SettingValue != settingValue);
        }

        private static List<CompanySettingOption> GetCountryOptions(List<CompanySettingOption> options, string countryCode) =>
            options switch
            {
                { Count: > 0 } => options
                    .Where(option => option.IncludeCountries.Contains("All") || option.IncludeCountries.Contains(countryCode))
                    .Where(option => !option.ExcludeCountries.Contains(countryCode))
                    .ToList(),
                _ => []
            };
    }
}