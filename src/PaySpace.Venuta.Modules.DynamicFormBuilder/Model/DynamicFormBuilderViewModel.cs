namespace PaySpace.Venuta.Modules.DynamicFormBuilder.Model
{
    public class DynamicFormBuilderViewModel
    {
        public string DynamicFormId { get; set; } = null!;

        public string ModuleType { get; set; } = null!;

        public string TaxCountryDescription { get; set; }

        public int TaxCountryId { get; set; }

        public string FormName { get; set; } = null!;

        public bool GenerateEmployeeNumber { get; set; } = false;

        public string BureauAddNewEmployeeDynamicFormId { get; set; }

        public bool IsActive { get; set; }
    }
}