namespace PaySpace.Venuta.Modules.Components.Bureau.Services
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Formula;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Components.Abstractions;

    public class BureauComponentService : IBureauComponentService
    {
        private readonly ApplicationContext context;
        private readonly IDistributedCache distributedCache;

        public BureauComponentService(ApplicationContext context, IDistributedCache distributedCache)
        {
            this.context = context;
            this.distributedCache = distributedCache;
        }

        public IQueryable<ComponentBureau> GetComponentAsync(long componentCompanyId)
        {
            return from cmc in this.context.Set<ComponentCompany>().AsNoTracking()
                   join cmb in this.context.Set<ComponentBureau>().AsNoTracking() on cmc.ComponentBureauId equals cmb.ComponentBureauId
                   where cmc.ComponentId == componentCompanyId
                   select cmb;
        }

        public IQueryable<ComponentBureau> GetComponentAsync(long categoryId, long countryId)
        {
            return this.context.Set<ComponentBureau>().AsNoTracking()
                .TagWithSource()
                .Where(_ => _.CountryId == countryId && _.ComponentCategoryId == categoryId);
        }

        public IQueryable<ComponentBureau> GetComponentsByCountryId(long countryId)
        {
            return this.context.Set<ComponentBureau>().AsNoTracking()
                .TagWithSource()
                .Where(_ => _.CountryId == countryId);
        }

        public IQueryable<ComponentBureau> GetCalcStatutoryComponentsByCountryId(long countryId)
        {
            return this.GetComponentsByCountryId(countryId)
                .TagWithSource()
                .Where(_ => _.IsStatutoryInCalc == true);
        }

        public Task<List<ComponentBureau>> GetStatutoryComponentsNotInFrequencyAsync(long countryId, long companyFrequencyId)
        {
            return this.context.Set<ComponentBureau>().AsNoTracking()
                .TagWithSource()
                .Include(_ => _.FormulaHeader)
                .ThenInclude(_ => _.FormulaLinkingTable)
                .ThenInclude(_ => _.FormulaLines)
                .ThenInclude(_ => _.CalculationElement)
                .Where(bc => bc.CountryId == countryId)
                .Where(bc => bc.Statutory == true || bc.GiveToCompanyByDefault == 1)
                .Where(bc => !this.context.Set<ComponentCompany>().Any(cc => cc.CompanyFrequencyId == companyFrequencyId && cc.ComponentBureauId == bc.ComponentBureauId))
                .ToListAsync();
        }

        public async Task<List<BureauChildComponents>> GetChildComponentsAsync(List<long> bureauComponentIds)
        {
            var children = await (from lt in this.context.Set<ComponentBureauLinkingTable>()
                                  where bureauComponentIds.Contains(lt.ComponentBureauId)
                                  select new { lt.ComponentBureauId, childId = lt.LinkingComponentId }).ToListAsync();

            var childIds = children.Select(_ => _.childId).ToList();

            var childComponents = await this.context.Set<ComponentBureau>().AsNoTracking()
                .TagWithSource()
                .Include(_ => _.FormulaHeader)
                .ThenInclude(_ => _.FormulaLinkingTable)
                .ThenInclude(_ => _.FormulaLines)
                .ThenInclude(_ => _.CalculationElement)
                .Where(_ => childIds.Contains(_.ComponentBureauId))
                .ToListAsync();

            return children.Select(child => new BureauChildComponents(child.ComponentBureauId, childComponents.Where(_ => _.ComponentBureauId == child.childId).ToList())).ToList();
        }

        public Task<bool> HasDependenciesAsync(long tableBuilderCategoryId)
        {
            return this.context.Set<ComponentBureau>()
                .TagWithSource()
                .AnyAsync(_ => _.TableBuilderCategoryId == tableBuilderCategoryId);
        }

        public IQueryable<ComponentBureau> GetAllowanceComponentsByCountryId(long countryId)
        {
            /*
               Component type is 'Allowance'
               AND
               Component order is before Basic Pay
               AND
               It must have exactly 3 formula lines with 'Amount', 'Days' and 'Hours' value types
               AND
               Once Off within the Formula line is TRUE
            */

            // Define the subquery for components that are of type "BasSal"
            var basicPayCompNumberQuery
                  = from subC in this.context.Set<ComponentCategory>()
                    join subCb in this.context.Set<ComponentBureau>() on subC.ComponentCategoryId equals subCb.ComponentCategoryId
                    join sct in this.context.Set<EnumSpecialComponentType>() on subCb.SpecialComponentTypeId equals sct.SpecialComponentId
                    where sct.SpecialComponentCode == "BasSal" && subCb.CountryId == countryId
                    select new { subC.CategoryOrderNumber, subCb.ComponentOrderNumber };

            // Component formula must have 3 lines (Amount, Days, Hours), all with OnceOff = true and CalculationElement = VariableDefinition
            var threeLineFormulasQuery
                  = from flt in this.context.Set<FormulaLinkingTable>()
                    join fl in this.context.Set<FormulaLine>() on flt.FormulaLinkingId equals fl.FormulaLinkingId
                    where this.context.Set<FormulaLine>().Any(_ => fl.FormulaLinkingId == _.FormulaLinkingId
                            && _.CalculationElementId == (int)CalculationElement.VariableDefinition
                            && _.ValueTypeId == (int)ValueType.Amount
                            && _.OnceOff == true)
                        && this.context.Set<FormulaLine>().Any(_ => fl.FormulaLinkingId == _.FormulaLinkingId
                            && _.CalculationElementId == (int)CalculationElement.VariableDefinition
                            && _.ValueTypeId == (int)ValueType.Days
                            && _.OnceOff == true)
                        && this.context.Set<FormulaLine>().Any(_ => fl.FormulaLinkingId == _.FormulaLinkingId
                            && _.CalculationElementId == (int)CalculationElement.VariableDefinition
                            && _.ValueTypeId == (int)ValueType.Hours
                            && _.OnceOff == true)
                    group flt by flt.FormulaHeaderId into g
                    where g.Count() == 3
                    select new { FormulaHeaderId = g.Key };

            // Main query for allowance components
            var query = from cb in this.context.Set<ComponentBureau>()
                        join c in this.context.Set<ComponentCategory>() on cb.ComponentCategoryId equals c.ComponentCategoryId
                        join fh in this.context.Set<FormulaHeader>() on cb.FormulaHeaderId equals fh.FormulaHeaderId
                        where cb.CountryId == countryId
                              && cb.PayslipAction == (int)PayslipAction.Allowance
                              && basicPayCompNumberQuery.Any(_ => c.CategoryOrderNumber > _.CategoryOrderNumber || (c.CategoryOrderNumber == _.CategoryOrderNumber && cb.ComponentOrderNumber > _.ComponentOrderNumber))
                              && threeLineFormulasQuery.Select(_ => _.FormulaHeaderId).Contains(fh.FormulaHeaderId)
                        select cb;

            return query.AsNoTracking();
        }

        public Task<bool> IsStatutoryOrAutomaticComponentAsync(long componentCompanyId)
        {
            // If 'DisplayReadOnlyComponent' is false, Classic determines the component as neither automatic nor statutory
            return this.distributedCache.GetOrCreateAsync(
                CacheKeys.IsStatutoryOrAutomaticComponent(componentCompanyId),
                () => this.context.Set<ComponentCompany>().AsNoTracking()
                .TagWithSource()
                .AnyAsync(_ => _.ComponentId == componentCompanyId && _.ComponentBureau.DisplayReadOnlyComponent.Value));
        }
    }
}