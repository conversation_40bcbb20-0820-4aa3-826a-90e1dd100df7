namespace PaySpace.Venuta.Infrastructure
{
    public static class RegexCodes
    {
        public static class CommonCodes
        {
            public const string Alphanumeric = "^[a-zA-Z0-9]";
        }

        public static class EmploymentStatus
        {
            public static class IdNumber
            {
                public const string Default = "^[0-9]*$";
                public const string MY = @"^\d{12}$";
                public const string IE = @"^\d{7}[A-Za-z]{1,2}$";
                public const string ZA = @"^(18[0-9]{2}|19[0-9]{2}|20[0-9]{2})/\d{6}/(06|07|08|09|10|11|20|21|22|23|24|25|26|30|31)$";
            }

            public static class TaxReferenceNumber
            {
                public const string ZA = "^([0-9]{10,10})$";
                public const string MY = @"^[A-Z]{2}\d{9,11}$";
                public const string IN = "^[A-Z]{3}P[A-Z][0-9]{4}[A-Z]$";
                public const string ES = @"^(?<province>\d{2})(?<personal>\d{7,8})(?<control>\d{2})$";
            }
        }
    }
}