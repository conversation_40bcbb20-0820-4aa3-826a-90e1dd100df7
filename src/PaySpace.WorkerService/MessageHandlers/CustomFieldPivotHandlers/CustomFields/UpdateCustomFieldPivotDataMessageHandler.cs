namespace PaySpace.WorkerService.MessageHandlers.CustomFieldPivotHandlers.CustomFields
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using MassTransit;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Messages;
    using PaySpace.Venuta.Services.Abstractions.Models.CustomFields;

    public class UpdateCustomFieldPivotDataMessageHandler : MessageHandlerBase<AuditLogMessage>
    {
        public override string SubscriptionFilter => "(Exists(IsCustomFieldEntity) AND IsCustomFieldEntity = True) AND ((Exists(IsRetry) AND IsRetry = False) OR (NOT EXISTS(IsRetry)))";

        public override string SubscriptionFilterName => "CustomFieldPivotDataChangesFilter";

        public override string SubscriptionName => "update_custom_field_pivot_data";

        public override async Task Consume(ConsumeContext<AuditLogMessage> context)
        {
            var message = context.Message;
            var entity = message.Entity;

            var isCustomFormEntity = entity.EntityType == typeof(EmployeeCustomForm) || entity.EntityType == typeof(CompanyCustomForm);
            var isTableBuilderEntity = entity.EntityType == typeof(EmployeeTableBuilder) || entity.EntityType == typeof(TableBuilder);
            if (isCustomFormEntity || isTableBuilderEntity)
            {
                return;
            }

            var customFieldUpdatedOnEntity = typeof(ICustomFieldEntity).IsAssignableFrom(entity.EntityType);
            if (!customFieldUpdatedOnEntity)
            {
                return;
            }

            var companyId = await GetCompanyIdAsync(entity, context);

            // Unable to find associated company
            if (companyId is null or 0)
            {
                return;
            }

            var actionType = GetActionType(message.ActionType);
            var updatedCustomFields = entity.ModifiedProperties?
                .Where(_ => _.IsCustomField)
                .Select(_ => new UpdatedCustomField
                {
                    OldValue = _.OldValue,
                    NewValue = _.NewValue,
                    CustomFieldId = _.CustomFieldId,
                    CustomFieldType = _.CustomFieldType,
                    EffectiveDate = _.EffectiveDate
                })
                .ToArray();

            if (actionType == ActionType.Edit && updatedCustomFields?.Length == 0)
            {
                return;
            }

            var primaryKeyProp = entity.EntityType.GetProperties().Single(_ => Attribute.IsDefined(_, typeof(KeyAttribute)));
            var primaryKeyValue = Convert.ToInt64(primaryKeyProp.GetValue(entity.Entity)!);

            await context.Publish(
                new CustomFieldDataChangedMessage
                {
                    CompanyId = companyId.Value,
                    EntityType = entity.EntityType,
                    ActionType = actionType,
                    EntityPrimaryKeyValue = primaryKeyValue,
                    UserId = message.UserId,
                    UpdatedCustomFields = updatedCustomFields
                });
        }

        protected override Task<bool> ProcessMessageAsync(AuditLogMessage message, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        private static async Task<long?> GetCompanyIdAsync(AuditEntity entity, ConsumeContext<AuditLogMessage> context)
        {
            if (entity.EntityProperties.TryGetValue("CompanyId", out var companyIdProp))
            {
                return (long)companyIdProp;
            }

            if (entity.EntityProperties.TryGetValue("EmployeeId", out var employeeIdProp))
            {
                var employeeService = context.GetServiceOrCreateInstance<IEmployeeService>();
                return await employeeService.GetCompanyIdAsync((long)employeeIdProp);
            }

            if (entity.EntityProperties.TryGetValue("HistoryEntityDetailId", out var historyEntityDetailIdProp))
            {
                var historyEntityDetailId = (long)historyEntityDetailIdProp;
                var applicationContext = context.GetServiceOrCreateInstance<ApplicationContext>();
                if (entity.EntityProperties.ContainsKey("RegionId"))
                {
                    return await applicationContext.Set<OrganizationRegionHistory>()
                        .AsNoTracking()
                        .Include(_ => _.Region)
                        .Where(_ => _.HistoryEntityDetailId == historyEntityDetailId)
                        .Select(_ => _.Region.CompanyId)
                        .FirstAsync();
                }

                return await applicationContext.Set<CompanyRosterHistory>()
                    .AsNoTracking()
                    .Include(_ => _.Roster)
                    .Where(_ => _.HistoryEntityDetailId == historyEntityDetailId)
                    .Select(_ => _.Roster.CompanyId)
                    .FirstAsync();
            }

            return null;
        }

        private static ActionType GetActionType(AuditActionType actionType)
        {
            return actionType switch
            {
                AuditActionType.New => ActionType.New,
                AuditActionType.Edit => ActionType.Edit,
                AuditActionType.Delete => ActionType.Delete,
                _ => throw new ArgumentOutOfRangeException(nameof(actionType), actionType, null)
            };
        }
    }
}