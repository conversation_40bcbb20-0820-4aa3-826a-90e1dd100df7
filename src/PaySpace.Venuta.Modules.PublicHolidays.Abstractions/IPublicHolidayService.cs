namespace PaySpace.Venuta.Modules.PublicHolidays.Abstractions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions.Models;

    public interface IPublicHolidayService : IGenericService<PublicHoliday>
    {
        IQueryable<PublicHoliday> GetPublicHolidays(string taxCountryCode);

        Task<IList<DateTime>> GetPublicHolidaysAsync(PublicHolidayRequest model);

        Task<int> GetPublicHolidayTotalAsync(PublicHolidayTotalRequest model);

        Task<bool> IsPublicHolidayAsync(IsPublicHolidayRequest model);

        Task<int?> GetProvinceIdAsync(long employeeId);

        Task<long?> GetPublicHolidayCategoryAsync(long? companySchemeId, long employeeId);
    }

    // Common properties present in each record
    public sealed record PublicHolidayInfo(
        long CompanyId,
        int? ProvinceId,
        long? PublicHolidayCategoryId,
        string? BRMunicipalityCode,
        string? BRProvinceCode);

    public sealed record PublicHolidayRequest(PublicHolidayInfo Info, int TaxCountryId);

    public sealed record PublicHolidayTotalRequest(PublicHolidayInfo Info, int CountryId, DateTime EndDate, DateTime StartDate);

    public sealed record IsPublicHolidayRequest(PublicHolidayInfo Info, DateTime Date, int TaxCountryId);
}