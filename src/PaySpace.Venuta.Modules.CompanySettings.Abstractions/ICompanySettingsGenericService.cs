namespace PaySpace.Venuta.Modules.CompanySettings.Abstractions
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions.Models;

    public interface ICompanySettingsGenericService
    {
        Task<IQueryable<CompanySettingsModel>> GetAllCompanySettingsByScreenAsync(long companyId, string screen);

        Task SaveAsync(List<CompanySetting> companySettings, long companyId);

        Task<string> GetCompanySettingDescriptionAsync(long settingTypeId);

        Task<List<CompanySetting>> ProcessCompanySettingsAsync(List<CompanySettingsModel> values, string screenName, long companyId);

        IQueryable<CompanySetting> GetExistingCompanySettingsByScreen(long companyId, string screen);

        Task<bool> IsOnNewTaxCalcTemplateSetAsync(int taxCountryId);

        Task AddSettingAsync(long companyId, string settingCode, bool settingIndicator, string settingValue = "");

        List<CompanySettingsModel> SetDefaultCompanySettings(List<CompanySettingsModel> settings, long companyId, bool isNewCompany = false);

        Task AddDefaultSettingsAsync(long companyId);

        Task<EnumCompanySettingType> GetEnumCompanySettingTypeAsync(int settingTypeId);
    }
}