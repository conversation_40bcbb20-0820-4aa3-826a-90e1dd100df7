namespace PaySpace.Venuta.Data.Models.Dto.Mappers.DropdownManagement
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;
    using PaySpace.Venuta.Data.Models.Organization;

    public class OrganizationPayPointProfile : Profile
    {
        public OrganizationPayPointProfile()
        {
            this.CreateMap<OrganizationPayPointDto, OrganizationPayPoint>()
                .ForMember(_ => _.CompanyId, opts => opts.MapFrom<CompanyIdConverter>());

            this.CreateMap<OrganizationPayPoint, OrganizationPayPointDto>();
        }
    }
}
