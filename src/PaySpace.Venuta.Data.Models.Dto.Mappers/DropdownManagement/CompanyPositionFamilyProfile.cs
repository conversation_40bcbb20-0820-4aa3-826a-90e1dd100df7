namespace PaySpace.Venuta.Data.Models.Dto.Mappers.DropdownManagement
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;

    public class CompanyPositionFamilyProfile : Profile
    {
        public CompanyPositionFamilyProfile()
        {
            this.CreateMap<CompanyPositionFamilyDto, CompanyPositionFamily>()
                .ForMember(_ => _.CompanyId, opts => opts.MapFrom<CompanyIdConverter>());

            this.CreateMap<CompanyPositionFamily, CompanyPositionFamilyDto>();
        }
    }
}
