namespace PaySpace.Venuta.Data.Models.Dto.Mappers.DropdownManagement
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;
    using PaySpace.Venuta.Data.Models.Organization;

    public class CompanyPositionFunctionAreaProfile : Profile
    {
        public CompanyPositionFunctionAreaProfile()
        {
            this.CreateMap<CompanyPositionFunctionAreaDto, CompanyPositionFunctionArea>()
                .ForMember(_ => _.CompanyId, opts => opts.MapFrom<CompanyIdConverter>());

            this.CreateMap<CompanyPositionFunctionArea, CompanyPositionFunctionAreaDto>();
        }
    }
}
