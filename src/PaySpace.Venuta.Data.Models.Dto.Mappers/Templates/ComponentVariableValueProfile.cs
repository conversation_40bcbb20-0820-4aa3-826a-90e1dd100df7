namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Templates
{
    using AutoMapper;
    using AutoMapper.EquivalencyExpression;

    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;

    public class ComponentVariableValueProfile : Profile
    {
        public ComponentVariableValueProfile()
        {
            // General
            this.CreateMap<ComponentVariableValueDto, ComponentVariableValue>()
                .ForMember(dest => dest.ComponentVariableValueId, opt => opt.Ignore())
                .ForMember(dest => dest.ComponentVariableId, opt => opt.LookupFrom(src => src.ComponentVariableCode));

            this.CreateMap<ComponentVariableValue, ComponentVariableValueDto>()
                .ForMember(dest => dest.ComponentVariableCode, opt => opt.MapFrom(src => src.ComponentVariable.Code));

            this.CreateMap<ComponentVariableValueDto, ComponentVariableValueDto>()
                .ForMember(dest => dest.ComponentVariableValueId, opt => opt.Ignore())
                .EqualityComparison((src, dst) => src.ComponentVariableValueId == dst.ComponentVariableValueId);

            // Agency
            this.CreateMap<AgencyComponentVariableValue, ComponentVariableValueDto>()
                .ForMember(dest => dest.ComponentVariableCode, opt => opt.MapFrom(src => src.ComponentVariable.Code));

            this.CreateMap<AgencyComponentVariableValue, ComponentVariableValue>()
                .ForMember(dest => dest.ComponentVariable, opt => opt.Ignore());

            this.CreateMap<ComponentVariableValueDto, AgencyComponentVariableValue>()
                .ForMember(dest => dest.ComponentVariableId, opt => opt.LookupFrom(src => src.ComponentVariableCode))
                .ForMember(dest => dest.TemplateConfigurationId, opt => opt.MapFrom(src => src.TemplateConfigurationId))
                .ForMember(dest => dest.AgencyId, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.EmployeeId, opt => opt.Ignore());

            // Company
            this.CreateMap<CompanyComponentVariableValue, ComponentVariableValueDto>()
                .ForMember(dest => dest.ComponentVariableCode, opt => opt.MapFrom(src => src.ComponentVariable.Code));

            this.CreateMap<CompanyComponentVariableValue, ComponentVariableValue>()
                .ForMember(dest => dest.ComponentVariable, opt => opt.Ignore());

            this.CreateMap<ComponentVariableValueDto, CompanyComponentVariableValue>()
                .ForMember(dest => dest.ComponentVariableId, opt => opt.LookupFrom(src => src.ComponentVariableCode))
                .ForMember(dest => dest.TemplateConfigurationId, opt => opt.MapFrom(src => src.TemplateConfigurationId))
                .ForMember(dest => dest.AgencyId, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.EmployeeId, opt => opt.Ignore());

            // Employee
            this.CreateMap<EmployeeComponentVariableValue, ComponentVariableValueDto>()
                .ForMember(dest => dest.ComponentVariableCode, opt => opt.MapFrom(src => src.ComponentVariable.Code));

            this.CreateMap<EmployeeComponentVariableValue, ComponentVariableValue>()
                .ForMember(dest => dest.ComponentVariable, opt => opt.Ignore());

            this.CreateMap<ComponentVariableValueDto, EmployeeComponentVariableValue>()
                .ForMember(dest => dest.ComponentVariableId, opt => opt.LookupFrom(src => src.ComponentVariableCode))
                .ForMember(dest => dest.TemplateConfigurationId, opt => opt.MapFrom(src => src.TemplateConfigurationId))
                .ForMember(dest => dest.AgencyId, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.EmployeeId, opt => opt.Ignore());
        }
    }
}