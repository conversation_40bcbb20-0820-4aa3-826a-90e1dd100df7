namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Company
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Dto.Company;

    public class ComponentCompanyProfile : Profile
    {
        public ComponentCompanyProfile()
        {
            this.CreateMap<ComponentCompany, ComponentCompanyDto>()
                .ForMember(dest => dest.CompanyComponentId, opt => opt.MapFrom(src => src.ComponentId))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.AliasDescription))
                .ForMember(dest => dest.ComponentCode, opt => opt.MapFrom(src => src.ComponentCode));

            this.CreateMap<ComponentCompanyDto, ComponentCompany>()
                .ForMember(dest => dest.AliasDescription, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.Description) ? src.Description.Trim() : src.Description))
                .ForMember(dest => dest.ComponentCode, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.ComponentCode) ? src.ComponentCode.Trim() : src.ComponentCode))
                .ForAllOtherMembers(opt => opt.Ignore());
        }
    }
}