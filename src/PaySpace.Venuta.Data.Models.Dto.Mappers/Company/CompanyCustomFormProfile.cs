namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.Mappers.Converters;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Dto.Mapping.Converters;
    using PaySpace.Venuta.Data.Models.Enums;

    public class CompanyCustomFormProfile : CustomFieldProfile<CompanyCustomFormFieldValue>
    {
        public CompanyCustomFormProfile()
        {
            this.CreateMap<CompanyCustomForm, CompanyCustomFormDto>()
                .ForMember(_ => _.CustomFormCategoryCode, opts =>
                {
                    opts.MapFrom(_ => _.BureauCustomFormCategoryId > 0
                        ? _.BureauCustomFormCategory.Code + "_B"
                        : _.CompanyCustomFormCategory.Code + "_C");
                })

                .ForMember(_ => _.ProjectCode, opts =>
                {
                    opts.MapFrom(_ => _.BureauCustomFormCategoryId > 0
                        ? (_.BureauCustomFormCategory.CustomFormScreenType == CustomFormScreenTypes.CompanyProject ? _.CostingProjectActivity.ProjectActivityCode : null)
                        : (_.CompanyCustomFormCategory.CustomFormScreenType == CustomFormScreenTypes.CompanyProject ? _.CostingProjectActivity.ProjectActivityCode : null));
                });

            this.CreateMap<CompanyCustomFormDto, CompanyCustomForm>()
                .ForMember(_ => _.BureauCustomFormCategoryId, opts =>
                {
                    opts.SetMappingOrder(1);
                    opts.Condition(_ => _.CustomFormCategoryCode.EndsWith("_B"));
                    opts.LookupFrom(_ => _.CustomFormCategoryCode);
                })
                .ForMember(_ => _.CompanyCustomFormCategoryId, opts =>
                {
                    opts.SetMappingOrder(2);
                    opts.Condition(_ => _.CustomFormCategoryCode.EndsWith("_C"));
                    opts.LookupFrom(_ => _.CustomFormCategoryCode);
                })
                .ForMember(_ => _.BureauCustomFormCategory, opts => opts.Ignore())
                .ForMember(_ => _.CompanyCustomFormCategory, opts => opts.Ignore())
                .ForMember(_ => _.CompanyId, opts => opts.MapFrom<CompanyIdConverter>())

                // TODO Add Custom Converter for when this is exposed to other screens
                .ForMember(_ => _.RelatedPrimaryKey, _ => _.LookupFrom(_ => _.ProjectCode))
                .ForMember(_ => _.CostingProjectActivity, _ => _.Ignore());

            this.CreateMap<CompanyCustomFormDto, CompanyCustomFormDto>()
                .ForMember(_ => _.BureauCustomFormCategoryId, opts => opts.LookupFrom(_ => _.CustomFormCategoryCode))
                .ForMember(_ => _.CompanyCustomFormCategoryId, opts => opts.LookupFrom(_ => _.CustomFormCategoryCode))
                .ForMember(_ => _.CustomFields, opts =>
                {
                    opts.SetMappingOrder(99);
                    opts.MapFrom<CustomFormFieldValueConverter<CompanyCustomFormDto, CompanyCustomFormDto>>();
                });

            this.CreateMap<CompanyCustomForm, CustomFieldEditorOption>();
        }
    }
}