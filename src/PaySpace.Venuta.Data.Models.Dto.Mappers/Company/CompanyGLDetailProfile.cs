namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Company
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Dto.Company;

    public class CompanyGLDetailProfile : Profile
    {
        public CompanyGLDetailProfile()
        {
            this.CreateMap<CompanyGLDetail, CompanyGLDetailDto>()
                .ForMember(dest => dest.ComponentCompany, opt => opt.MapFrom(src => src.ComponentCompany.AliasDescription))
                .ForMember(dest => dest.TaxCode, opt => opt.MapFrom(src => src.ComponentCompany.OverridingTaxCodeId.HasValue
                    ? src.ComponentCompany.OverridingTaxCode.Code : src.ComponentCompany.ComponentBureau.TaxCode.Code))
                .ForMember(dest => dest.ComponentCode, opt => opt.MapFrom(src => src.ComponentCompany.ComponentCode))
                .ForMember(dest => dest.PayslipAction, opt => opt.MapFrom(src => src.ComponentCompany.ComponentBureau.EnumPayslipAction.ActionDescription))
                .ForMember(dest => dest.GeneralLedgerHeader, opt => opt.MapFrom(src => src.CompanyGL.HeaderName));

            this.CreateMap<CompanyGLDetailDto, CompanyGLDetail>()
                .ForMember(dest => dest.ComponentCompany, opt => opt.MapFrom(src => src));

            this.CreateMap<CompanyGLDetailDto, ComponentCompany>()
                .ForMember(dest => dest.ComponentCode, opt => opt.MapFrom(src => src.ComponentCode))
                .ForAllOtherMembers(dest => dest.Ignore());
        }
    }
}
