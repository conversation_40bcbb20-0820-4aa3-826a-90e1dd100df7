namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Converters
{
    using System;
    using System.Linq;

    using AutoMapper;

    using Microsoft.Data.SqlClient;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Exceptions;

    public class EmployeeLeaveBucketValueConverter<TDtoEntity>
        : IValueResolver<TDtoEntity, EmployeeLeaveAdjustment, long?>
        where TDtoEntity : IEmployeeLeaveAdjustmentDtoEntity, new()
    {
        private readonly IStringLocalizer<EmployeeLeaveAdjustment> localizer;
        private readonly ApplicationContext applicationContext;

        public EmployeeLeaveBucketValueConverter(IStringLocalizer<EmployeeLeaveAdjustment> localizer, ApplicationContext applicationContext)
        {
            this.localizer = localizer;
            this.applicationContext = applicationContext;
        }

        public long? Resolve(TDtoEntity source, EmployeeLeaveAdjustment destination, long? destMember, ResolutionContext context)
        {
            if (string.IsNullOrWhiteSpace(source.LeaveBucket))
            {
                throw new EmptyLeaveBucketException(this.localizer);
            }

            if (source.LeaveType != LeaveType.Special && source.LeaveBucket.Equals("leave-priority", StringComparison.InvariantCultureIgnoreCase))
            {
                return -2;
            }
            else if (source.LeaveType == LeaveType.Special && source.LeaveBucket.Equals("leave-priority", StringComparison.InvariantCultureIgnoreCase))
            {
                throw new InvalidBucketException(this.localizer);
            }

            var buckets = this.applicationContext.Set<EmployeeLeaveBucketResult>()
                .FromSqlRaw(
                    $"exec [nextgen_get_employee_company_leave_setup_id] @EmployeeId, @LeaveTypeId, @LeaveBucket",
                    new SqlParameter("@EmployeeId", source.EmployeeId),
                    new SqlParameter("@LeaveTypeId", (int)source.LeaveType),
                    new SqlParameter("@LeaveBucket", source.LeaveBucket))
                .AsEnumerable();

            var id = buckets.Select(_ => _.CompanyLeaveSetupId).SingleOrDefault();
            if (id == 0)
            {
                throw new InvalidBucketException(this.localizer);
            }

            return id;
        }
    }
}
