namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;

    public class EmployeeLumpSumProfile : Profile
    {
        public EmployeeLumpSumProfile()
        {
            this.CreateMap<EmployeeLumpSumDto, EmployeeLumpSumResult>()
                .ForMember(dest => dest.EmployeeLumpSumId, opt => opt.MapFrom(src => src.EmployeeLumpSumId))
                .ForMember(dest => dest.PayslipId, opt => opt.MapFrom(src => src.PayslipId))
                .ForMember(dest => dest.EmployeeId, opt => opt.MapFrom(src => src.EmployeeId))
                .ForMember(dest => dest.DirectiveAmount, opt => opt.MapFrom(src => src.DirectiveAmount))
                .ForMember(dest => dest.DirectiveNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DirectiveNumber) ? src.DirectiveNumber.Trim() : string.Empty))
                .ForMember(dest => dest.DirectiveTax, opt => opt.MapFrom(src => src.DirectiveTax))
                .ForMember(dest => dest.DirectiveIssuedDate, opt => opt.MapFrom(src => src.DirectiveIssuedDate))
                .ForMember(dest => dest.ReferenceNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.ReferenceNumber) ? src.ReferenceNumber.Trim() : src.ReferenceNumber))
                .ForMember(dest => dest.TaxCode, opt => opt.MapFrom(src => src.TaxCode))
                .ForMember(dest => dest.TaxFreeDirectiveAmount, opt => opt.MapFrom(src => src.TaxFreeDirectiveAmount))
                .ForAllOtherMembers(dest => dest.Ignore());

            this.CreateMap<EmployeeLumpSum, EmployeeLumpSumResult>()
                .ForMember(dest => dest.PayslipId, opt => opt.MapFrom(src => src.PayslipId))
                .ForMember(dest => dest.EmployeeId, opt => opt.MapFrom(src => src.EmployeeId))
                .ForMember(dest => dest.DirectiveAmount, opt => opt.MapFrom(src => src.DirectiveAmount))
                .ForMember(dest => dest.DirectiveNumber, opt => opt.MapFrom(src => src.DirectiveNumber))
                .ForMember(dest => dest.DirectiveTax, opt => opt.MapFrom(src => src.DirectiveTax))
                .ForMember(dest => dest.DirectiveIssuedDate, opt => opt.MapFrom(src => src.DirectiveIssuedDate))
                .ForMember(dest => dest.ReferenceNumber, opt => opt.MapFrom(src => src.ReferenceNumber))
                .ForMember(dest => dest.TaxCode, opt => opt.MapFrom(src => src.TaxCode))
                .ForMember(dest => dest.TaxFreeDirectiveAmount, opt => opt.MapFrom(src => src.TaxFreeDirectiveAmount))
                .ForAllOtherMembers(dest => dest.Ignore());

            this.CreateMap<EmployeeLumpSumDto, EmployeeLumpSum>()
                .ForMember(dest => dest.TaxCode, opt => opt.LookupFrom(src => src.TaxCode))
                .ForMember(dest => dest.DirectiveNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DirectiveNumber) ? src.DirectiveNumber.Trim() : string.Empty))
                .ForMember(dest => dest.ReferenceNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.ReferenceNumber) ? src.ReferenceNumber.Trim() : src.ReferenceNumber))
                .ForMember(dest => dest.TaxFreeDirectiveAmount, opt => opt.MapFrom(src => src.TaxFreeDirectiveAmount));

            this.CreateMap<EmployeeLumpSum, EmployeeLumpSumDto>()
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.Employee.EmployeeNumber))
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.Employee.FullName))
                .ForMember(dest => dest.TaxCode, opt => opt.MapFrom(src => src.ComponentCompany.OverridingTaxCodeId.HasValue
                    ? src.ComponentCompany.OverridingTaxCode.Code + " - " + src.ComponentCompany.AliasDescription
                    : src.ComponentCompany.ComponentBureau.TaxCode.Code + " - " + src.ComponentCompany.AliasDescription))
                .ForMember(dest => dest.TaxFreeDirectiveAmount, opt => opt.MapFrom(src => src.TaxFreeDirectiveAmount));

            this.CreateMap<EmployeeLumpSumDto, EmployeeLumpSumDto>()
                .ForMember(dest => dest.PayslipId, opt => opt.MapFrom(src => src.PayslipId))
                .ForMember(dest => dest.EmployeeId, opt => opt.MapFrom(src => src.EmployeeId))
                .ForMember(dest => dest.DirectiveAmount, opt => opt.MapFrom(src => src.DirectiveAmount))
                .ForMember(dest => dest.DirectiveNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DirectiveNumber) ? src.DirectiveNumber.Trim() : string.Empty))
                .ForMember(dest => dest.DirectiveTax, opt => opt.MapFrom(src => src.DirectiveTax))
                .ForMember(dest => dest.ReferenceNumber, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.ReferenceNumber) ? src.ReferenceNumber.Trim() : src.ReferenceNumber))
                .ForMember(dest => dest.TaxCode, opt => opt.MapFrom(src => src.TaxCode))
                .ForMember(dest => dest.TaxFreeDirectiveAmount, opt => opt.MapFrom(src => src.TaxFreeDirectiveAmount))
                .ForAllOtherMembers(dest => dest.Ignore());
        }
    }
}