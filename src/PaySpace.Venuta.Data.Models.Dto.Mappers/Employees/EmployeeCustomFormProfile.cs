namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Mappers.Converters;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;

    public class EmployeeCustomFormProfile : CustomFieldProfile<EmployeeCustomFormFieldValue>
    {
        public EmployeeCustomFormProfile()
        {
            this.CreateMap<EmployeeCustomForm, EmployeeCustomFormDto>()
                .ForMember(_ => _.EmployeeNumber, opts => opts.MapFrom(_ => _.Employee.EmployeeNumber))
                .ForMember(_ => _.CompanyRun, opts => opts.MapFrom(src => src.CompanyRun == null
                    ? null
                    : src.CompanyRun.CompanyFrequency.RunFrequencyId == (int)PayslipFrequency.Monthly
                        ? src.CompanyRun.RunType == RunType.Main ? src.CompanyRun.PeriodCode : src.CompanyRun.PeriodCode + "-" + src.CompanyRun.OrderNumber
                        : src.CompanyRun.PeriodCode + "-" + src.CompanyRun.PeriodEndDate.Month + "-" + src.CompanyRun.PeriodEndDate.Day + "-" + src.CompanyRun.OrderNumber))
                .ForMember(_ => _.CustomFormCategoryCode, opts =>
                {
                    opts.MapFrom(_ => _.BureauCustomFormCategoryId > 0
                        ? _.BureauCustomFormCategory.Code + "_B"
                        : _.CompanyCustomFormCategory.Code + "_C");
                });

            this.CreateMap<EmployeeCustomFormDto, EmployeeCustomForm>()
                .ForMember(_ => _.BureauCustomFormCategoryId, opts =>
                {
                    opts.SetMappingOrder(1);
                    opts.Condition(_ => _.CustomFormCategoryCode.EndsWith("_B"));
                    opts.LookupFrom(_ => _.CustomFormCategoryCode);
                })
                .ForMember(_ => _.CompanyCustomFormCategoryId, opts =>
                {
                    opts.SetMappingOrder(2);
                    opts.Condition(_ => _.CustomFormCategoryCode.EndsWith("_C"));
                    opts.LookupFrom(_ => _.CustomFormCategoryCode);
                })
                .ForMember(_ => _.BureauCustomFormCategory, opts => opts.Ignore())
                .ForMember(_ => _.CompanyCustomFormCategory, opts => opts.Ignore())
                .ForMember(_ => _.CompanyRun, opts => opts.Ignore())
                .ForMember(_ => _.CompanyRunId, opts => opts.LookupFrom(_ => _.CompanyRun));

            this.CreateMap<EmployeeCustomFormDto, EmployeeCustomFormDto>()
                .ForMember(_ => _.BureauCustomFormCategoryId, opts => opts.LookupFrom(_ => _.CustomFormCategoryCode))
                .ForMember(_ => _.CompanyCustomFormCategoryId, opts => opts.LookupFrom(_ => _.CustomFormCategoryCode))
                .ForMember(_ => _.CustomFields, opts =>
                {
                    opts.SetMappingOrder(99);
                    opts.MapFrom<CustomFormFieldValueConverter<EmployeeCustomFormDto, EmployeeCustomFormDto>>();
                });
        }
    }
}