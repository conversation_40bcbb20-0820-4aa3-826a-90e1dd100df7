namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Modules.RPN.Abstractions.Models;

    public class EmployeeRpnProfile : Profile
    {
        public EmployeeRpnProfile()
        {
            this.CreateMap<EmployeeRpn, EmployeeRpnDto>()
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.RpnFirstName + " " + src.RpnFamilyName))
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.Employee.EmployeeNumber))
                .ForMember(dest => dest.TaxYear, opt => opt.MapFrom(src => src.TaxYear.YearStartDate.ToString("d") + " - " + src.TaxYear.YearEndDate.ToString("d")))
                .ForMember(dest => dest.IncomeTaxCalculationBasisRpn, opt => opt.MapFrom(src => src.IncomeTaxCalculationBasis.IncomeTaxCalculationOption));

            this.CreateMap<EmployeeRpnDto, EmployeeRpn>()
                .ForMember(dest => dest.LptToBeDeducted, opt => opt.MapFrom(src => src.LptToBeDeducted ?? 0.00m))
                .ForMember(dest => dest.YearlyRate1CutOff, opt => opt.MapFrom(src => src.YearlyRate1CutOff ?? 0.00m))
                .ForMember(dest => dest.YearlyUscRate1CutOff, opt => opt.MapFrom(src => src.YearlyUscRate1CutOff ?? 0.00m))
                .ForMember(dest => dest.YearlyUscRate2CutOff, opt => opt.MapFrom(src => src.YearlyUscRate2CutOff ?? 0.00m))
                .ForMember(dest => dest.YearlyUscRate3CutOff, opt => opt.MapFrom(src => src.YearlyUscRate3CutOff ?? 0.00m))
                .ForMember(dest => dest.YearlyUscRate4CutOff, opt => opt.MapFrom(src => src.YearlyUscRate4CutOff ?? 0.00m))
                .ForMember(dest => dest.TaxYear, opt => opt.Ignore())
                .ForMember(dest => dest.IncomeTaxCalculationBasisId, opt => opt.LookupFrom(src => src.IncomeTaxCalculationBasisRpn));
        }
    }
}
