namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using AutoMapper;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Enums;
    using System;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;

    public class EmployeeAppendix8AProfile : Profile
    {
        public EmployeeAppendix8AProfile()
        {
            this.CreateMap<EmployeeAppendix8A, EmployeeAppendix8ADto>()
                .ForMember(dest => dest.YearEndAccommodationType, opt => opt.MapFrom(src => src.YearEndAccommodationType.YearEndAccommodationTypeCode))
                .ForMember(dest => dest.FurnitureFittingOption, opt => opt.MapFrom(src => src.FurnitureFittingOption.FurnitureFittingOptionCode))
                .ForMember(dest => dest.TaxYear, opt => opt.MapFrom(src => src.TaxYear.YearStartDate.Year.ToString()))
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.Employee.EmployeeNumber));

            this.CreateMap<EmployeeAppendix8ADto, EmployeeAppendix8A>()
                .ForMember(dest => dest.TaxYearId, opt => opt.LookupFrom(src => src.TaxYear))
                .ForMember(dest => dest.TaxYear, opt => opt.Ignore())
                .ForMember(dest => dest.YearEndAccommodationTypeId, opt => opt.LookupFrom(src => src.YearEndAccommodationType))
                .ForMember(dest => dest.YearEndAccommodationType, opt => opt.Ignore())
                .ForMember(dest => dest.FurnitureFittingOptionID, opt => opt.LookupFrom(src => src.FurnitureFittingOption))
                .ForMember(dest => dest.FurnitureFittingOption, opt => opt.Ignore())
                .ForMember(dest => dest.Employee, opt => opt.Ignore());
        }
    }
}