namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using System;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;
    using PaySpace.Venuta.Infrastructure;

    public class EmployeePositionProfile : CustomFieldProfile<EmployeePositionCustomFieldValue>
    {
        public EmployeePositionProfile()
        {
            var includeCompanyNameInDirectlyReportsTo = false;
            Version? version = null;

            this.CreateMap<EmployeePositionDto, EmployeePosition>()
                .ForMember(dest => dest.TradeUnionId, opt => opt.LookupFrom(src => src.TradeUnion))
                .ForMember(dest => dest.TradeUnion, opt => opt.Ignore())

                .ForMember(dest => dest.OrganizationPositionId, opt => opt.OrganizationPositionLookup())
                .ForMember(dest => dest.OrganizationPosition, opt => opt.Ignore())

                .ForMember(dest => dest.GradeId, opt => opt.LookupFrom(src => src.Grade))
                .ForMember(dest => dest.Grade, opt => opt.Ignore())

                .ForMember(dest => dest.OrganizationRegionId, opt => opt.LookupFrom(src => src.OrganizationRegion))
                .ForMember(dest => dest.OrganizationRegion, opt => opt.Ignore())

                .ForMember(dest => dest.DirectlyReportsPositionOverrideId, opt => opt.LookupFrom(src => src.DirectlyReportsPositionOverride))
                .ForMember(dest => dest.DirectlyReportsPositionOverride, opt => opt.Ignore())

                .ForMember(dest => dest.OrganizationGroupId, opt => opt.LookupFrom(src => src.OrganizationGroup))
                .ForMember(dest => dest.OrganizationGroup, opt => opt.Ignore())

                .ForMember(dest => dest.PayPointId, opt => opt.LookupFrom(src => src.PayPoint))
                .ForMember(dest => dest.PayPoint, opt => opt.Ignore())

                .ForMember(dest => dest.EmploymentCategoryId, opt => opt.LookupFrom(src => src.EmploymentCategory))
                .ForMember(dest => dest.EmploymentCategory, opt => opt.Ignore())

                .ForMember(dest => dest.WorkflowRoleId, opt => opt.LookupFrom(src => src.WorkflowRole))
                .ForMember(dest => dest.WorkflowRole, opt => opt.Ignore())

                .ForMember(dest => dest.RosterId, opt => opt.LookupFrom(src => src.Roster))
                .ForMember(dest => dest.Roster, opt => opt.Ignore())

                .ForMember(dest => dest.JobId, opt => opt.LookupFrom(src => src.Job))
                .ForMember(dest => dest.Job, opt => opt.Ignore())

                .ForMember(dest => dest.EmploymentSubCategoryId, opt => opt.LookupFrom(src => src.EmploymentSubCategory))
                .ForMember(dest => dest.EmploymentSubCategory, opt => opt.Ignore())

                .ForMember(dest => dest.AdministratorId, opt => opt.LookupFrom(src => src.AdministratorEmployeeNumber))
                .ForMember(dest => dest.Administrator, opt => opt.Ignore())

                .ForMember(dest => dest.GlName, opt => opt.LookupFrom(src => src.GeneralLedger))
                .ForMember(dest => dest.DirectlyReportsEmployee, opt => opt.Ignore())

                .ForMember(dest => dest.DirectlyReportsEmployeeId, opt => opt.LookupFrom(src => src.DirectlyReportsEmployeeNumber))
                .ForMember(dest => dest.DirectlyReportsEmployee, opt => opt.Ignore())

                .ForMember(dest => dest.PositionTypeId, opt => opt.LookupFrom(src => src.PositionType))
                .ForMember(dest => dest.PositionType, opt => opt.Ignore())

                .ForMember(dest => dest.OrganizationGroups, opt => opt.Ignore())

                .ForMember(dest => dest.CustomTradeUnionId, opt => opt.LookupFrom(src => src.CustomTradeUnion))
                .ForMember(dest => dest.TableBuilder, opt => opt.Ignore())

                .ForMember(dest => dest.JobMode, opt => opt.MapFrom(src => src.KeepJob ? JobMode.KeepJob : JobMode.OrgUnit));

            this.CreateMap<EmployeePosition, EmployeePositionDto>()
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.Employee.EmployeeNumber))
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.Employee.FullName))
                .ForMember(dest => dest.OrganizationPosition, opt => opt.MapFrom(src => src.OrganizationPosition.Description))
                .ForMember(dest => dest.OrganizationPositionId, opt => opt.MapFrom(src => src.OrganizationPositionId))
                .ForMember(
                    dest => dest.OrganizationPositionWithCode,
                    opt => opt.MapFrom(
                        src => version >= PaySpaceApiVersions.V1_1
                            ? src.OrganizationPosition.Description
                              + src.OrganizationPosition.OrganizationPositionDetails.Where(pd => pd.EffectiveDate <= src.EffectiveDate)
                                  .OrderByDescending(pd => pd.EffectiveDate)
                                  .Select(pd => !string.IsNullOrEmpty(pd.PositionCode) ? " - " + pd.PositionCode : string.Empty)
                                  .FirstOrDefault()
                            : null))
                .ForMember(
                    dest => dest.OccupationalLevel,
                    opt => opt.MapFrom(
                        src => src.OrganizationPosition.OrganizationPositionDetails
                            .OrderByDescending(_ => _.EffectiveDate)
                            .Where(_ => _.EffectiveDate <= src.EffectiveDate)
                            .Select(_ => _.OccupationalLevel.LevelDescription)
                            .FirstOrDefault()))
                .ForMember(
                    dest => dest.Grade,
                    opt => opt.MapFrom(
                        src => src.GradeId == -1
                            ? src.OrganizationPosition.OrganizationPositionDetails
                                .OrderByDescending(_ => _.EffectiveDate)
                                .Where(_ => _.EffectiveDate <= src.EffectiveDate)
                                .Select(_ => _.Grade.Code)
                                .FirstOrDefault()
                            : src.Grade.Code))
                .ForMember(
                    dest => dest.DirectlyReportsPosition,
                    opt => opt.MapFrom(
                        src => src.OrganizationPosition.OrganizationPositionDetails
                            .OrderByDescending(_ => _.EffectiveDate)
                            .Where(_ => _.EffectiveDate <= src.EffectiveDate)
                            .Select(_ => _.DirectlyReportsPosition.Description)
                            .FirstOrDefault()))
                .ForMember(dest => dest.DirectlyReportsPositionOverride, opt => opt.MapFrom(src => src.DirectlyReportsPositionOverride.Description))
                .ForMember(dest => dest.OrganizationGroup, opt => opt.MapFrom(src => src.OrganizationGroup.UploadCode))
                .ForMember(dest => dest.OrganizationGroupDescription, opt => opt.MapFrom(src => src.OrganizationGroup.Description))
                .ForMember(
                    dest => dest.OrganizationGroups,
                    opt =>
                    {
                        opt.MapFrom(src => src.OrganizationGroups.OrderBy(_ => _.LevelId).Select(_ => _.OrganizationGroup.Description));
                        opt.ExplicitExpansion();
                    })
                .ForMember(
                    dest => dest.OrganizationGroups_1_1,
                    opt =>
                    {
                        opt.MapFrom(src => src.OrganizationGroups.OrderBy(_ => _.LevelId).Select(_ => _.OrganizationGroup));
                        opt.ExplicitExpansion();
                    })
                .ForMember(dest => dest.OrganizationRegion, opt => opt.MapFrom(src => src.OrganizationRegion.Description))
                .ForMember(dest => dest.PayPoint, opt => opt.MapFrom(src => src.PayPoint.Description))
                .ForMember(dest => dest.DirectlyReportsEmployee, opt => opt.MapFrom(src => src.DirectlyReportsEmployee.FullName))
                .ForMember(
                    dest => dest.DirectlyReportsEmployeeNumber,
                    opt => opt.MapFrom(
                        src => includeCompanyNameInDirectlyReportsTo
                            ? src.DirectlyReportsEmployee.EmployeeNumber + " (" + src.DirectlyReportsEmployee.Company.CompanyName + ")"
                            : src.DirectlyReportsEmployee.EmployeeNumber))
                .ForMember(dest => dest.EmploymentCategory, opt => opt.MapFrom(src => src.EmploymentCategory.CategoryCode))
                .ForMember(dest => dest.Administrator, opt => opt.MapFrom(src => src.Administrator.FullName))
                .ForMember(dest => dest.AdministratorEmployeeNumber, opt => opt.MapFrom(src => src.Administrator.EmployeeNumber))
                .ForMember(dest => dest.WorkflowRole, opt => opt.MapFrom(src => src.WorkflowRole.Description))
                .ForMember(dest => dest.TradeUnion, opt => opt.MapFrom(src => src.TradeUnion.Description))
                .ForMember(dest => dest.EmploymentSubCategory, opt => opt.MapFrom(src => src.EmploymentSubCategory.SubCategoryCode))
                .ForMember(dest => dest.Roster, opt => opt.MapFrom(src => src.Roster.RosterName))
                .ForMember(dest => dest.PositionType, opt => opt.MapFrom(src => src.PositionType.PositionTypeDesc))
                .ForMember(dest => dest.GeneralLedger, opt => opt.MapFrom(src => src.GlName))
                .ForMember(dest => dest.Job, opt => opt.MapFrom(src => src.Job.JobNumber))
                .ForMember(dest => dest.CustomTradeUnion, opt => opt.MapFrom(src => src.TableBuilder.Name));

            this.CreateMap<EmployeePositionResult, EmployeePositionDto>()
                .ForMember(dest => dest.OrganizationPosition, opt => opt.MapFrom(src => src.OrganizationPosition))
                .ForMember(dest => dest.OrganizationPositionWithCode, opt => opt.MapFrom(src => src.OrganizationPositionWithCode))
                .ForMember(dest => dest.DirectlyReportsEmployeeNumber,
                           opt => opt.MapFrom(
                               src => includeCompanyNameInDirectlyReportsTo
                                   ? src.DirectlyReportsEmployeeNumberWithCompanyName
                                   : src.DirectlyReportsEmployeeNumber))
                .ForMember(dest => dest.OrganizationGroups, opt => opt.MapFrom(src => src.OrganizationGroups.Select(_ => _.Description)))
                .ForMember(dest => dest.OrganizationGroups_1_1, opt => opt.Ignore());

            this.CreateMap<EmployeePositionCustomFieldValue, EmployeePositionCustomFieldValue>()
                .ForMember(dest => dest.EmployeePositionId, opt => opt.Ignore())
                .IncludeBase<CustomFieldValueBase, CustomFieldValueBase>();

            this.CreateMap<EmployeePosition, EmployeePosition>()
                .ForMember(dest => dest.Job, opt => opt.Ignore());
        }
    }

    public class PositionRequestDefaultProfile : CustomFieldProfile<EmployeePositionCustomFieldValue>
    {
        public PositionRequestDefaultProfile()
        {
            var includeCompanyNameInDirectlyReportsTo = false;
            this.CreateMap<EmployeePositionResult, EmployeePositionDto>()
                .ForMember(dest => dest.OrganizationPosition, opt => opt.MapFrom(src => src.OrganizationPosition))
                .ForMember(dest => dest.OrganizationPositionWithCode, opt => opt.MapFrom(src => src.OrganizationPositionWithCode))
                .ForMember(
                    dest => dest.DirectlyReportsEmployeeNumber,
                    opt => opt.MapFrom(
                        src => includeCompanyNameInDirectlyReportsTo
                            ? src.DirectlyReportsEmployeeNumberWithCompanyName
                            : src.DirectlyReportsEmployeeNumber))
                .ForMember(
                    dest => dest.OrganizationGroups,
                    opt =>
                    {
                        opt.MapFrom(src => src.OrganizationGroups.OrderBy(_ => _.LevelId).Select(_ => _.Description));
                    })
                .ForMember(dest => dest.OrganizationGroups_1_1, opt => opt.Ignore());
        }
    }

    public class PositionRequestNoExpandProfile : CustomFieldProfile<EmployeePositionCustomFieldValue>
    {
        public PositionRequestNoExpandProfile()
        {
            var includeCompanyNameInDirectlyReportsTo = false;
            this.CreateMap<EmployeePositionResult, EmployeePositionDto>()
                .ForMember(dest => dest.OrganizationPosition, opt => opt.MapFrom(src => src.OrganizationPosition))
                .ForMember(dest => dest.OrganizationPositionWithCode, opt => opt.MapFrom(src => src.OrganizationPositionWithCode))
                .ForMember(dest => dest.DirectlyReportsEmployeeNumber, opt => opt.MapFrom(src => includeCompanyNameInDirectlyReportsTo
                                                                                              ? src.DirectlyReportsEmployeeNumberWithCompanyName
                                                                                              : src.DirectlyReportsEmployeeNumber))
                .ForMember(dest => dest.OrganizationGroups, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationGroups_1_1, opt => opt.Ignore());

            this.CreateMap<EmployeePositionGroupResult, PositionOrganizationUnit>()
                .ForMember(dest => dest.Address, opt => opt.Ignore());
        }
    }

    public class PositionRequestExpandProfile : CustomFieldProfile<EmployeePositionCustomFieldValue>
    {
        public PositionRequestExpandProfile()
        {
            var includeCompanyNameInDirectlyReportsTo = false;
            this.CreateMap<EmployeePositionResult, EmployeePositionDto>()
                .ForMember(dest => dest.OrganizationPosition, opt => opt.MapFrom(src => src.OrganizationPosition))
                .ForMember(dest => dest.OrganizationPositionWithCode, opt => opt.MapFrom(src => src.OrganizationPositionWithCode))
                .ForMember(dest => dest.DirectlyReportsEmployeeNumber, opt => opt.MapFrom(src => includeCompanyNameInDirectlyReportsTo
                                                                                              ? src.DirectlyReportsEmployeeNumberWithCompanyName
                                                                                              : src.DirectlyReportsEmployeeNumber))
                .ForMember(dest => dest.OrganizationGroups, opt => opt.Ignore())
                .ForMember(dest => dest.OrganizationGroups_1_1, opt =>
                {
                    opt.MapFrom(src => src.OrganizationGroups.OrderBy(_ => _.LevelId));
                });

            this.CreateMap<EmployeePositionGroupResult, PositionOrganizationUnit>();
        }
    }
}