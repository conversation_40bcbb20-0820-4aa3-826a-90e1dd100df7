namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using System;
    using System.Linq;

    using AutoMapper;

    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Mappers.Converters;
    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Employees;

    public class EmployeeReviewDefaultRatersProfile : Profile
    {
        public EmployeeReviewDefaultRatersProfile()
        {

            this.CreateMap<EmployeeReviewDefRaters, EmployeeReviewDefaultRatersDto>()
                .ForMember(dest => dest.EmployeeId, opt => opt.MapFrom(src => src.ReviewDefaults.Employee.EmployeeId))
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.ReviewDefaults.Employee.EmployeeNumber))
                .ForMember(dest => dest.FullName, opt => opt.MapFrom(src => src.ReviewDefaults.Employee.FullName))
                .ForMember(dest => dest.DirectReportsTo, opt => opt.MapFrom(src => src.ReviewDefaults.Employee.EmployeePositions
                    .Where(_ => _.EffectiveDate.Date <= DateTime.Today)
                    .OrderByDescending(_ => _.EffectiveDate)
                    .Select(_ => _.DirectlyReportsEmployee.FullName)
                    .FirstOrDefault()))
                .ForMember(dest => dest.ProcessType, opt => opt.MapFrom(src => src.ReviewDefaults.ProcessType.ProcessTypeDescription))
                .ForMember(dest => dest.DefaultTemplate, opt => opt.MapFrom(src => src.ReviewDefaults.Template.Description))
                .ForMember(dest => dest.RaterType, opt => opt.MapFrom(src => src.RaterType.RaterCode))
                .ForMember(dest => dest.InheritRaterFromPosition, opt => opt.Ignore())
                .ForMember(dest => dest.RaterEmpNumber, opt => opt.MapFrom(src => src.Rater.EmployeeNumber))
                .ForMember(dest => dest.RaterName, opt => opt.MapFrom(src => src.Rater.FullName))
                .ForMember(dest => dest.IncludeScore, opt => opt.MapFrom(src => src.IncludeScore))
                .ForMember(dest => dest.Weighting, opt => opt.MapFrom(src => src.Weighting))
                .ForMember(dest => dest.ShowWeighting, opt => opt.MapFrom(src => src.ShowWeightings))
                .ForMember(dest => dest.AccessJournal, opt => opt.MapFrom(src => src.AllowJournalAccess));

            this.CreateMap<EmployeeReviewDefaultRatersDto, EmployeeReviewDefRaters>()
                .ForMember(dest => dest.ProcessTypeId, opt =>
                {
                    opt.SetMappingOrder(1);
                    opt.LookupFrom(src => src.ProcessType);
                })
                .ForMember(dest => dest.TemplateId, opt =>
                {
                    opt.SetMappingOrder(2);
                    opt.LookupFrom(src => src.DefaultTemplate);
                })

                .ForMember(dest => dest.ReviewDefaultsId, opt =>
                {
                    opt.SetMappingOrder(3);
                    opt.MapFrom<EmployeeReviewDefaultRatersConverter>();
                })
                .ForMember(dest => dest.DefRaterId, opt => opt.MapFrom(src => src.DefRaterId))
                .ForMember(dest => dest.RaterId, opt => opt.LookupFrom(src => src.RaterEmpNumber))
                .ForMember(dest => dest.Rater, opt => opt.Ignore())
                .ForMember(dest => dest.RaterTypeId, opt => opt.LookupFrom(src => src.RaterType))
                .ForMember(dest => dest.RaterType, opt => opt.Ignore())
                .ForMember(dest => dest.IncludeScore, opt => opt.MapFrom(src => src.IncludeScore))
                .ForMember(dest => dest.Weighting, opt => opt.MapFrom(src => src.Weighting))
                .ForMember(dest => dest.ShowWeightings, opt => opt.MapFrom(src => src.ShowWeighting))
                .ForMember(dest => dest.AllowJournalAccess, opt => opt.MapFrom(src => src.AccessJournal))
                .ForMember(dest => dest.InheritRaterFromPosition, opt => opt.MapFrom(src => src.InheritRaterFromPosition))
                .ForMember(dest => dest.TemplateName, opt => opt.MapFrom(src => src.DefaultTemplate))
                .ForMember(dest => dest.ProcessTypeName, opt => opt.MapFrom(src => src.ProcessType));

            this.CreateMap<EmployeeReviewDefaultRatersDto, EmployeeReviewDefaults>()
                .ForMember(dest => dest.TemplateId, opt => opt.LookupFrom(src => src.DefaultTemplate))
                .ForMember(dest => dest.ProcessTypeId, opt => opt.LookupFrom(src => src.ProcessType))
                .ForMember(dest => dest.ProcessType, opt => opt.Ignore())
                .ForAllOtherMembers(opt => opt.UseDestinationValue());

        }
    }
}
