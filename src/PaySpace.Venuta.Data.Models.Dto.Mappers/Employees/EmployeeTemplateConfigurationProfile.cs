namespace PaySpace.Venuta.Data.Models.Dto.Mappers.Employees
{
    using AutoMapper;
    using AutoMapper.EquivalencyExpression;

    using PaySpace.Venuta.Data.Models.Dto.Mapping;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;

    public class EmployeeTemplateConfigurationProfile : Profile
    {
        public EmployeeTemplateConfigurationProfile()
        {
            this.CreateMap<TemplateConfiguration, EmployeeTemplateConfigurationDto>()
                .ForMember(dest => dest.ComponentVariableValues, opt => opt.MapFrom(src => src.EmployeeComponentVariableValues))
                .ForMember(dest => dest.EmployeeNumber, opt => opt.MapFrom(src => src.Employee.EmployeeNumber))
                .ForMember(dest => dest.TemplateCode, opt => opt.MapFrom(src => src.Template.Code));

            this.CreateMap<EmployeeTemplateConfigurationDto, TemplateConfiguration>()
                .ForMember(dest => dest.ComponentVariableValues, opt => opt.Ignore())
                .ForMember(dest => dest.EmployeeComponentVariableValues, opt => opt.Ignore())
                .ForMember(dest => dest.TemplateId, opt => opt.LookupFrom(src => src.TemplateCode));
        }
    }
}