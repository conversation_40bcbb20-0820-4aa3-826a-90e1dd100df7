namespace PaySpace.Venuta.Excel.Employees.UploadMutators
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.RPN.Abstractions.Models;
    using PaySpace.Venuta.Storage;

    public class EmployeeRpnUploadMutator : BulkUploadMutator<EmployeeRpnDto, EmployeeRpn, long>
    {
        private readonly IMapper mapper;

        public EmployeeRpnUploadMutator(
            IMapper mapper,
            IEntityValidator<EmployeeRpnDto, EmployeeRpn> entityValidator,
            IAttachmentStorageService attachmentService,
            ApplicationContext applicationContext)
            : base(mapper, applicationContext, attachmentService, entityValidator)
        {
            this.mapper = mapper;
        }

        protected override Task<EmployeeRpnDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set.Where(_ => _.EmployeeRpnId == key && _.Employee.CompanyId == context.CompanyId)
                .ProjectTo<EmployeeRpnDto>(this.mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }

        protected override Task<EmployeeRpn?> FindAsync(MutatorContext context, long key)
        {
            return this.Set.FirstOrDefaultAsync(_ => _.EmployeeRpnId == key && _.Employee.CompanyId == context.CompanyId);
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeRpn entity)
        {
            throw new InvalidOperationException();
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, EmployeeRpn entity)
        {
            throw new InvalidOperationException();
        }
    }
}