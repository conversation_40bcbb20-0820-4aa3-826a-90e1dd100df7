namespace PaySpace.Venuta.Excel.Employees.UploadMutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;

    using MassTransit.Serialization;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Employees.SG;
    using PaySpace.Venuta.Storage;

    public class EmployeeAppendix8AUploadMutator : BulkUploadMutator<EmployeeAppendix8ADto, EmployeeAppendix8A, long>
    {
        private readonly ISingaporeEmployeeAppendix8AService singaporeEmployeeAppendix8AService;
        private readonly IStringLocalizer<EmployeeAppendix8A> localizer;

        public EmployeeAppendix8AUploadMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IAttachmentStorageService attachmentService,
            IEntityValidator<EmployeeAppendix8ADto, EmployeeAppendix8A> entityValidator,
            ISingaporeEmployeeAppendix8AService singaporeEmployeeAppendix8AService,
            IStringLocalizer<EmployeeAppendix8A> localizer)
            : base(mapper, applicationContext, attachmentService, entityValidator)
        {
            this.singaporeEmployeeAppendix8AService = singaporeEmployeeAppendix8AService;
            this.localizer = localizer;
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, EmployeeAppendix8A entity)
        {
            return this.singaporeEmployeeAppendix8AService.AddAsync(entity);
        }

        protected override Task UpdateAsync(MutatorContext context, long key, EmployeeAppendix8A entity)
        {
            return this.singaporeEmployeeAppendix8AService.UpdateAsync(entity);
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeAppendix8A entity)
        {
            return this.singaporeEmployeeAppendix8AService.DeleteAsync(entity);
        }

        protected override async Task<EntityValidationResult> ValidateAsync(
            MutatorContext context,
            EmployeeAppendix8ADto dto,
            EmployeeAppendix8A entity,
            string ruleSet,
            CancellationToken cancellationToken = default)
        {
            var validationResult = await base.ValidateAsync(context, dto, entity, ruleSet, cancellationToken);

            if (context.TryGetValue<IList<EmployeeAppendix8ADto>>("RelatedRows", out var relatedRows))
            {
                var count = await this.singaporeEmployeeAppendix8AService.GetRecordCountForAnEmployeeForATaxYearAsync(entity.TaxYearId, entity.EmployeeId);
                var rowCount = relatedRows.Count(_ => dto.TaxYear.Equals(_.TaxYear));
                if ((rowCount + count) > SingaporeConstants.YearEndReporting.Appendix8AMaxRecordsPerTaxYear && dto.EmployeeAppendix8AId == default)
                {
                    validationResult.Errors.Add(new ErrorCodeValidationResult(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.Max11RecordsPerYearError)));
                }

                var hotelAccommodationCount = relatedRows.Count(_ => _.YearEndAccommodationType == YearEndAccommadationType.HotelAccomodation.ToString());
                if (hotelAccommodationCount > 1 && dto.EmployeeAppendix8AId == default)
                {
                    validationResult.Errors.Add(new ErrorCodeValidationResult(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.HotelAccommodationCountErrorSheet)));
                }

                var residenceProvidedByEmployerCount = relatedRows.Count(_ => _.YearEndAccommodationType == YearEndAccommadationType.ResidenceProvidedByEmployer.ToString());
                if (residenceProvidedByEmployerCount > 10 && dto.EmployeeAppendix8AId == default)
                {
                    validationResult.Errors.Add(new ErrorCodeValidationResult(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.ResidenceProvidedByEmployerCountErrorSheet)));
                }
            }

            return new EntityValidationResult(!validationResult.Errors.Any(), validationResult.Errors);
        }
    }
}
