namespace PaySpace.Venuta.Excel.Employees.UploadMutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Employees.SG;
    using PaySpace.Venuta.Storage;

    public class EmployeeAppendix8BUploadMutator : BulkUploadMutator<EmployeeAppendix8BDto, EmployeeAppendix8B, long>
    {
        private readonly ISingaporeEmployeeAppendix8BService singaporeEmployeeAppendix8BService;
        private readonly IStringLocalizer<EmployeeAppendix8B> localizer;

        public EmployeeAppendix8BUploadMutator(
            IMapper mapper, ApplicationContext applicationContext,
            IAttachmentStorageService attachmentService,
            IEntityValidator<EmployeeAppendix8BDto, EmployeeAppendix8B> entityValidator,
            ISingaporeEmployeeAppendix8BService singaporeEmployeeAppendix8BService,
            IStringLocalizer<EmployeeAppendix8B> localizer)
            : base(mapper, applicationContext, attachmentService, entityValidator)
        {
            this.singaporeEmployeeAppendix8BService = singaporeEmployeeAppendix8BService;
            this.localizer = localizer;
        }

        protected override Task UpdateAsync(MutatorContext context, long key, EmployeeAppendix8B entity)
        {
            return this.singaporeEmployeeAppendix8BService.UpdateAsync(entity);
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, EmployeeAppendix8B entity)
        {
            return this.singaporeEmployeeAppendix8BService.AddAsync(entity);
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeAppendix8B entity)
        {
            return this.singaporeEmployeeAppendix8BService.DeleteAsync(entity);
        }

        protected override async Task<EntityValidationResult> ValidateAsync(
            MutatorContext context,
            EmployeeAppendix8BDto dto,
            EmployeeAppendix8B entity,
            string ruleSet,
            CancellationToken cancellationToken = default)
        {
            var validationResult = await base.ValidateAsync(context, dto, entity, ruleSet, cancellationToken);

            if (context.TryGetValue<IList<EmployeeAppendix8BDto>>("RelatedRows", out var relatedRows))
            {
                var count = await this.singaporeEmployeeAppendix8BService.GetRecordCountAsync(entity.TaxYearId, entity.EmployeeId);
                var rowCount = relatedRows.Count(_ => dto.TaxYear.Equals(_.TaxYear));
                if ((rowCount + count) > 30)
                {
                    validationResult.Errors.Add(new ErrorCodeValidationResult(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.TotalRecordCountExceeds30)));
                }
            }

            return new EntityValidationResult(!validationResult.Errors.Any(), validationResult.Errors);
        }
    }
}
