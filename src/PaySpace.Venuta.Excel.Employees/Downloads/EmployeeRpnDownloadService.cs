namespace PaySpace.Venuta.Excel.Employees.Downloads
{
    using System.Collections.Generic;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Excel.Services;
    using PaySpace.Venuta.Modules.RPN.Abstractions.Models;

    public class EmployeeRpnDownloadService : ExcelDownloadService<EmployeeRpnDto, EmployeeRpn>
    {

        public EmployeeRpnDownloadService(
            IExcelMetadataBuilder<EmployeeRpnDto, EmployeeRpn> metadataBuilder,
            IStringLocalizerFactory localizerFactory)
            : base(metadataBuilder, localizerFactory)
        {
        }

        protected override bool HideActionColumn(IList<EmployeeRpnDto> data)
        {
            return true;
        }
    }
}