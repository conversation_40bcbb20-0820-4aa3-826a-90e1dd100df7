namespace PaySpace.Venuta.Excel.Employees.Mutators
{
    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Storage;

    public class EmploymentContractEntityMutator : EntityMutator<EmployeeEmploymentContractDto, EmployeeEmploymentContract, long>
    {
        public EmploymentContractEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<EmployeeEmploymentContractDto, EmployeeEmploymentContract> entityValidator,
            IAttachmentStorageService attachmentService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
        }
    }
}