namespace PaySpace.Venuta.Excel.Employees.Mutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.RPN.Abstractions;
    using PaySpace.Venuta.Modules.RPN.Abstractions.Models;
    using PaySpace.Venuta.Storage;

    internal sealed class EmployeeRpnEntityMutator : EntityMutator<EmployeeRpnDto, EmployeeRpn, long>
    {
        private readonly IMapper mapper;

        public EmployeeRpnEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<EmployeeRpnDto, EmployeeRpn> entityValidator,
            IAttachmentStorageService attachmentService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.mapper = mapper;
        }

        protected override Task<EmployeeRpn?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
                .FirstOrDefaultAsync(_ => _.Employee.CompanyId == context.CompanyId && _.EmployeeRpnId == key);
        }

        protected override Task<EmployeeRpnDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set.Where(_ => _.Employee.CompanyId == context.CompanyId && _.EmployeeRpnId == key)
                .ProjectTo<EmployeeRpnDto>(this.mapper.ConfigurationProvider)
                .SingleOrDefaultAsync();
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, EmployeeRpn entity)
        {
            throw new NotImplementedException();
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeRpn entity)
        {
            throw new NotImplementedException();
        }
    }
}
