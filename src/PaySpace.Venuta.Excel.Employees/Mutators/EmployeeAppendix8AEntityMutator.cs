namespace PaySpace.Venuta.Excel.Employees.Mutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Services.Employees.SG;
    using PaySpace.Venuta.Storage;

    public class EmployeeAppendix8AEntityMutator : EntityMutator<EmployeeAppendix8ADto, EmployeeAppendix8A, long>
    {
        private readonly ISingaporeEmployeeAppendix8AService singaporeEmployeeAppendix8A;

        public EmployeeAppendix8AEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<EmployeeAppendix8ADto, EmployeeAppendix8A> entityValidator,
            IAttachmentStorageService attachmentService,
            ISingaporeEmployeeAppendix8AService singaporeEmployeeAppendix8A)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.singaporeEmployeeAppendix8A = singaporeEmployeeAppendix8A;
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, EmployeeAppendix8A entity)
        {
            return this.singaporeEmployeeAppendix8A.AddAsync(entity);
        }

        protected override Task UpdateAsync(MutatorContext context, long key, EmployeeAppendix8A entity)
        {
            return this.singaporeEmployeeAppendix8A.UpdateAsync(entity);
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, EmployeeAppendix8A entity)
        {
            return this.singaporeEmployeeAppendix8A.DeleteAsync(entity);
        }
    }
}
