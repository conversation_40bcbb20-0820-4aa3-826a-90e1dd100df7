namespace PaySpace.Venuta.Excel.Employees.Mutators
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Services;
    using PaySpace.Venuta.Storage;

    internal class EmployeeTemplateConfigurationEntityMutator : EntityMutator<EmployeeTemplateConfigurationDto, TemplateConfiguration, long>
    {
        private readonly IMapper mapper;
        private readonly IComponentVariableValueService componentVariableValueService;

        public EmployeeTemplateConfigurationEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<EmployeeTemplateConfigurationDto, TemplateConfiguration> entityValidator,
            IAttachmentStorageService attachmentService,
            IComponentVariableValueService componentVariableValueService) : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.mapper = mapper;
            this.componentVariableValueService = componentVariableValueService;
        }

        protected override async Task AfterMapAsync(MutatorContext context, EmployeeTemplateConfigurationDto dto, TemplateConfiguration entity)
        {
            var level = TemplateConfigurationLevel.Employee;
            var templateConfigurationId = entity.TemplateConfigurationId;

            // On create, we need use the inherited company level component variable values
            // The template configuration id will need to be determined via the selected template
            if (entity.TemplateConfigurationId == default)
            {
                level = TemplateConfigurationLevel.Company;
                templateConfigurationId = await this.componentVariableValueService.GetEmployeeTemplateConfigurationIdAsync(entity.TemplateId);
            }

            var originalValues = await this.componentVariableValueService
                .GetByTemplateConfigurationId(templateConfigurationId, level)
                .ToListAsync();

            if (level == TemplateConfigurationLevel.Company)
            {
                foreach (var value in originalValues)
                {
                    // Since we are on employee level, we need to set the override to false again
                    value.IsOverride = false;
                }
            }

            var dtoValues = dto.ComponentVariableValues;
            var entityValues = entity.ComponentVariableValues;
            var ComponentVariableValueIds = dtoValues.Where(_ => _.ComponentVariableValueId > 0)
                                  .Select(_ => _.ComponentVariableValueId)
                                  .ToHashSet();

            this.AddNewValues(dtoValues, entityValues, dto.EmployeeId);
            this.UpdateOrOverrideValues(dtoValues, originalValues, entityValues, dto.EmployeeId);
            this.RemoveDeletedValues(entityValues, ComponentVariableValueIds);

            await base.AfterMapAsync(context, dto, entity);
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, TemplateConfiguration entity)
        {
            // Set the pk to 0 to ensure a new entity is created and map the EmployeeId
            entity.TemplateConfigurationId = 0;
            entity.EmployeeId = employeeId;
            return base.AddAsync(context, employeeId, entity);
        }

        protected override Task<TemplateConfiguration?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
                .Include(_ => _.ComponentVariableValues)
                .SingleOrDefaultAsync(_ => _.TemplateConfigurationId == key);
        }

        protected override Task<EmployeeTemplateConfigurationDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set
                .ProjectTo<EmployeeTemplateConfigurationDto>(this.mapper.ConfigurationProvider)
                .SingleOrDefaultAsync(_ => _.TemplateConfigurationId == key);
        }

        private void AddNewValues(List<ComponentVariableValueDto> dtoValues, List<ComponentVariableValue> entityValues, long employeeId)
        {
            foreach (var dto in dtoValues.Where(_ => _.ComponentVariableValueId == 0))
            {
                dto.IsOverride = false;
                dto.EmployeeId = employeeId;
                entityValues.Add(this.mapper.Map<ComponentVariableValue>(dto));
            }
        }

        private void UpdateOrOverrideValues(List<ComponentVariableValueDto> dtoValues,
            List<ComponentVariableValue> originalValues,
            List<ComponentVariableValue> entityValues,
            long employeeId)
        {
            foreach (var dto in dtoValues.Where(_ => _.ComponentVariableValueId > 0))
            {
                var original = originalValues.FirstOrDefault(_ => _.ComponentVariableValueId == dto.ComponentVariableValueId);
                if (original == null)
                {
                    continue;
                }

                if (dto.IsOverride && !original.IsOverride)
                {
                    var overrideValue = this.CreateOverride(dto, original, employeeId);
                    entityValues.Add(overrideValue);
                }
                else
                {
                    var existing = entityValues.FirstOrDefault(x => x.ComponentVariableValueId == dto.ComponentVariableValueId);
                    if (existing?.EmployeeId != null && existing.Value != dto.Value)
                    {
                        existing.Value = dto.Value;
                    }
                }
            }
        }

        private ComponentVariableValue CreateOverride(ComponentVariableValueDto dto, ComponentVariableValue original, long employeeId)
        {
            dto.ComponentVariableValueId = 0;

            var newValue = this.mapper.Map<ComponentVariableValue>(dto);

            newValue.ComponentVariableId = original.ComponentVariableId;
            newValue.AgencyId = null;
            newValue.CompanyId = null;
            newValue.EmployeeId = employeeId;
            newValue.IsOverride = true;

            return newValue;
        }

        private void RemoveDeletedValues(List<ComponentVariableValue> entityValues, HashSet<long> dtoIds)
        {
            entityValues.RemoveAll(_ => _.EmployeeId.HasValue && _.ComponentVariableValueId > 0 &&
                !dtoIds.Contains(_.ComponentVariableValueId));
        }
    }
}