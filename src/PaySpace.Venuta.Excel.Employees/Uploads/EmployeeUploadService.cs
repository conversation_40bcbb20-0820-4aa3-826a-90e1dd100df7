namespace PaySpace.Venuta.Excel.Employees.Uploads
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Readers;
    using PaySpace.Venuta.Excel.Services;
    using PaySpace.Venuta.Excel.Services.Uploads;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;

    public interface IEmployeeUploadService
    {
        Task ValidateExcelDataForUpdateAsync(IList<ExcelRowItem<EmployeeDto, long>> rowData, ExcelUploadContext excelContext, ExcelMetadata excelMetadata);
    }

    internal sealed class EmployeeUploadService : FileUploadService<EmployeeDto, Employee, long>, IEmployeeUploadService
    {
        private readonly ICompanyService companyService;

        public EmployeeUploadService(
            ApplicationContext applicationContext,
            IExcelFileReader<EmployeeDto, long> excelFileReader,
            IExcelMessageService messageService,
            IExcelMetadataBuilder<EmployeeDto, Employee> metadataBuilder,
            IStringLocalizerFactory localizerFactory,
            IUploadEntityMutator<EmployeeDto, Employee, long> entityMutator,
            IMapper mapper,
            ICompanyService companyService)
            : base(applicationContext, excelFileReader, messageService, metadataBuilder, localizerFactory, entityMutator, mapper)
        {
            this.companyService = companyService;
        }

        public override Task ValidateExcelDataAsync(IList<ExcelRowItem<EmployeeDto, long>> rowData, ExcelUploadContext excelContext, ExcelMetadata excelMetadata)
        {
            this.CheckDuplicateEmployeeNumbers(rowData);
            this.CheckForEmptyEmployeeNumbers(rowData, excelMetadata);
            this.CheckDuplicateEmails(rowData);

            return this.CheckNewEmployeeErrorsAsync(rowData, excelContext.Profile.CompanyId);
        }

        public Task ValidateExcelDataForUpdateAsync(IList<ExcelRowItem<EmployeeDto, long>> rowData, ExcelUploadContext excelContext, ExcelMetadata excelMetadata)
        {
            this.CheckDuplicateEmployeeNumbers(rowData);
            this.CheckForEmptyEmployeeNumbers(rowData, excelMetadata);
            this.CheckDuplicateEmails(rowData);

            return Task.CompletedTask;
        }

        protected override void AddValidationInfo(ExcelRowItem<EmployeeDto, long> rowItem, Employee dbEntity)
        {
            if (dbEntity != null)
            {
                rowItem.ValidationInfo.Add(PaySpaceConstants.EmployeeCitizenshipId, dbEntity.CitizenshipId);
                rowItem.ValidationInfo.Add(PaySpaceConstants.EmployeeGenderId, dbEntity.GenderId);
                rowItem.ValidationInfo.Add(PaySpaceConstants.CompanyFrequencyId, dbEntity.CompanyFrequencyId);
                rowItem.ValidationInfo.Add(PaySpaceConstants.EmployeeBirthday, dbEntity.Birthday);
            }
        }

        protected override ExcelError GetError(ExcelRowItem<EmployeeDto, long> rowItem, ErrorCodeValidationResult errorResult)
        {
            if (errorResult.MemberNames.Any(_ => _.StartsWith("Address")))
            {
                var addressFieldIndex = errorResult.MemberNames.First().Split('.')[0];
                if (rowItem.ValuesToUpdate.RelatedRowNumbers.TryGetValue(addressFieldIndex, out var rowNumber))
                {
                    return new ExcelError(errorResult.ErrorMessage, nameof(AddressDto).TrimDto(), rowNumber);
                }

                return new ExcelError(errorResult.ErrorMessage);
            }

            return base.GetError(rowItem, errorResult);
        }

        private void CheckForEmptyEmployeeNumbers(IList<ExcelRowItem<EmployeeDto, long>> rowData, ExcelMetadata excelMetadata)
        {
            var employeeNumberProperty = excelMetadata.Properties.First(_ => _.Name == nameof(IEmployeeNumberEntity.EmployeeNumber));
            var message = this.StringLocalizer.GetString(ExcelConstants.Localization.Error.PropertyRequired);

            foreach (var rowItem in rowData.Where(_ => _.Success && string.IsNullOrEmpty(_.GetEmployeeNumber())))
            {
                rowItem.Errors.Add(this.GetError(string.Format(message, employeeNumberProperty.DisplayName)));
            }
        }

        private async Task CheckNewEmployeeErrorsAsync(IList<ExcelRowItem<EmployeeDto, long>> rowData, long companyId)
        {
            if (await this.companyService.ShouldGenerateEmployeeNumber(companyId))
            {
                // ValuesToUpdate will be null if it is a address row error
                foreach (var rowItem in rowData.Where(_ => _.Key == default && _.ValuesToUpdate != null))
                {
                    rowItem.Errors.Add(new ExcelError(this.StringLocalizer.GetString(ExcelConstants.Localization.Error.NewEmployeeNotAllowed).ToString()));
                }
            }
        }

        private void CheckDuplicateEmployeeNumbers(IList<ExcelRowItem<EmployeeDto, long>> rowData)
        {
            var duplicateRows = rowData.Where(_ => _.Success)
                .GroupBy(_ => _.GetEmployeeNumber())
                .Where(_ => _.Count() > 1)
                .Select(_ => new { EmployeeNumber = _.Key, Items = _, Rows = _.Select(en => en.RowNumber) });

            foreach (var duplicateRow in duplicateRows)
            {
                foreach (var rowItem in duplicateRow.Items)
                {
                    rowItem.Errors.Add(new ExcelError($"Duplicate Employee Number '{duplicateRow.EmployeeNumber}' on rows {string.Join(", ", duplicateRow.Rows)}"));
                    rowItem.UploadStatus = BulkStatusType.Duplicates;
                }
            }
        }

        private void CheckDuplicateEmails(IList<ExcelRowItem<EmployeeDto, long>> rowData)
        {
            var duplicatedEmails = rowData
                .Select(_ => new
                {
                    Row = _,
                    Email = _.ValuesToUpdate.GetFieldValue<string>(nameof(EmployeeDto.Email))
                })
                .Where(_ => !string.IsNullOrWhiteSpace(_.Email))
                .GroupBy(_ => _.Email)
                .Where(_ => _.Count() > 1);

            foreach (var duplicatedEmail in duplicatedEmails)
            {
                var duplicatedRowNumbers = duplicatedEmail.Select(_ => _.Row.RowNumber).ToList();
                foreach (var rowItem in duplicatedEmail)
                {
                    rowItem.Row.Errors.Add(new ExcelError($"Duplicate Email '{duplicatedEmail.Key}' on rows {string.Join(", ", duplicatedRowNumbers)}"));
                    rowItem.Row.UploadStatus = BulkStatusType.Duplicates;
                }
            }
        }
    }
}