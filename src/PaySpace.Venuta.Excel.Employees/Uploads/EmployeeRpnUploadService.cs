namespace PaySpace.Venuta.Excel.Employees.Uploads
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Excel.Abstractions.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Readers;
    using PaySpace.Venuta.Excel.Services;
    using PaySpace.Venuta.Excel.Services.Uploads;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.RPN.Abstractions.Models;

    internal sealed class EmployeeRpnUploadService : FileUploadService<EmployeeRpnDto, EmployeeRpn, long>
    {
        private readonly IStringLocalizer localizer;

        public EmployeeRpnUploadService(
            ApplicationContext applicationContext,
            IExcelFileReader<EmployeeRpnDto, long> excelFileReader,
            IExcelMessageService messageService,
            IExcelMetadataBuilder<EmployeeRpnDto, EmployeeRpn> metadataBuilder,
            IStringLocalizerFactory localizerFactory,
            IUploadEntityMutator<EmployeeRpnDto, EmployeeRpn, long> entityMutator,
            IMapper mapper)
            : base(applicationContext, excelFileReader, messageService, metadataBuilder, localizerFactory, entityMutator, mapper)
        {
            this.localizer = localizerFactory.Create(SystemAreas.EmployeeRpn.Area, null!);
        }

        public override Task ValidateExcelDataAsync(IList<ExcelRowItem<EmployeeRpnDto, long>> rowData, ExcelUploadContext excelContext, ExcelMetadata excelMetadata)
        {
            this.CheckValuesHaveValidKey(rowData);

            return base.ValidateExcelDataAsync(rowData, excelContext, excelMetadata);
        }

        private static long GetEmployeeRpnId(ExcelRowItem<EmployeeRpnDto, long> rowItem)
        {
            return rowItem.ValuesToUpdate.GetFieldValue<long>(nameof(EmployeeRpnDto.EmployeeRpnId));
        }

        private void CheckValuesHaveValidKey(IList<ExcelRowItem<EmployeeRpnDto, long>> rowData)
        {
            var rowsWithoutKey = rowData.Where(_ => _.Success)
                .Where(_ => GetEmployeeRpnId(_) == default);

            foreach (var rowItem in rowsWithoutKey)
            {
                rowItem.Errors.Add(new ExcelError(this.localizer.GetString(SystemAreas.EmployeeRpn.Keys.BulkUploadOnlySupportsUpdate)));
                rowItem.UploadStatus = BulkStatusType.InvalidValue;
            }
        }
    }
}