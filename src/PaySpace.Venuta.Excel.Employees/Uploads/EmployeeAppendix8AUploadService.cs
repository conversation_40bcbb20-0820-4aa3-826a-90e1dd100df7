namespace PaySpace.Venuta.Excel.Employees.Uploads
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Readers;
    using PaySpace.Venuta.Excel.Services;
    using PaySpace.Venuta.Excel.Services.Uploads;

    public class EmployeeAppendix8AUploadService : FileUploadService<EmployeeAppendix8ADto, EmployeeAppendix8A, long>
    {
        public EmployeeAppendix8AUploadService(
            ApplicationContext applicationContext,
            IExcelFileReader<EmployeeAppendix8ADto, long> excelFileReader,
            IExcelMessageService messageService,
            IExcelMetadataBuilder<EmployeeAppendix8ADto, EmployeeAppendix8A> metadataBuilder,
            IStringLocalizerFactory localizerFactory,
            IUploadEntityMutator<EmployeeAppendix8ADto, EmployeeAppendix8A, long> entityMutator,
            IMapper mapper)
            : base(applicationContext, excelFileReader, messageService, metadataBuilder, localizerFactory, entityMutator, mapper)
        {
        }

        protected override MutatorContext GetMutatorContext(ExcelUploadContext excelContext, ExcelRowItem<EmployeeAppendix8ADto, long> currentRow, IList<ExcelRowItem<EmployeeAppendix8ADto, long>> rows)
        {
            var context = base.GetMutatorContext(excelContext, currentRow, rows);
            var employeeNumber = currentRow.GetEmployeeNumber();

            var hasRelatedRows = rows
                .Where(_ => employeeNumber.Equals(_.GetEmployeeNumber(), StringComparison.InvariantCultureIgnoreCase))
                .Any(_ => _ != currentRow);

            if (hasRelatedRows)
            {
                var relatedRows = rows
                    .Where(_ => employeeNumber.Equals(_.GetEmployeeNumber(), StringComparison.InvariantCultureIgnoreCase))
                    .Select(
                        _ => new EmployeeAppendix8ADto
                        {
                            TaxYear = _.ValuesToUpdate.GetFieldValue<string>(nameof(EmployeeAppendix8ADto.TaxYear)),
                            YearEndAccommodationType = _.ValuesToUpdate.GetFieldValue<string>(nameof(EmployeeAppendix8ADto.YearEndAccommodationType))
                        })
                    .ToList();

                context.Add("RelatedRows", relatedRows);
            }

            return context;
        }
    }
}