namespace PaySpace.Venuta.Excel.Employees.Uploads
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Readers;
    using PaySpace.Venuta.Excel.Services;
    using PaySpace.Venuta.Excel.Services.Uploads;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;

    // TODO: combine with EmployeeUploadService
    internal sealed class EmployeeHistoryUploadService : FileUploadService<EmployeeHistoryDto, Employee, long>
    {
        private readonly ICompanyService companyService;

        public EmployeeHistoryUploadService(
            ApplicationContext applicationContext,
            IExcelFileReader<EmployeeHistoryDto, long> excelFileReader,
            IExcelMessageService messageService,
            IExcelMetadataBuilder<EmployeeHistoryDto, Employee> metadataBuilder,
            IStringLocalizerFactory localizerFactory,
            IUploadEntityMutator<EmployeeHistoryDto, Employee, long> entityMutator,
            IMapper mapper,
            ICompanyService companyService)
            : base(applicationContext, excelFileReader, messageService, metadataBuilder, localizerFactory, entityMutator, mapper)
        {
            this.companyService = companyService;
        }

        public override Task ValidateExcelDataAsync(IList<ExcelRowItem<EmployeeHistoryDto, long>> rowData, ExcelUploadContext excelContext, ExcelMetadata excelMetadata)
        {
            this.CheckDuplicateEmployeeNumbers(rowData);
            this.CheckForEmptyEmployeeNumbers(rowData, excelMetadata);
            this.CheckDuplicateEmails(rowData);

            return this.CheckNewEmployeeErrorsAsync(rowData, excelContext.Profile.CompanyId);
        }

        protected override string GetEntityName()
        {
            return nameof(EmployeeDto).TrimDto();
        }

        protected override void AddValidationInfo(ExcelRowItem<EmployeeHistoryDto, long> rowItem, Employee dbEntity)
        {
            if (dbEntity != null)
            {
                rowItem.ValidationInfo.Add(PaySpaceConstants.EmployeeCitizenshipId, dbEntity.CitizenshipId);
                rowItem.ValidationInfo.Add(PaySpaceConstants.EmployeeGenderId, dbEntity.GenderId);
                rowItem.ValidationInfo.Add(PaySpaceConstants.CompanyFrequencyId, dbEntity.CompanyFrequencyId);
                rowItem.ValidationInfo.Add(PaySpaceConstants.EmployeeBirthday, dbEntity.Birthday);
                rowItem.ValidationInfo.Add(PaySpaceConstants.EmployeeEffectiveDate, dbEntity.EffectiveDate);
            }
        }

        protected override ExcelError GetError(ExcelRowItem<EmployeeHistoryDto, long> rowItem, ErrorCodeValidationResult errorResult)
        {
            if (errorResult.MemberNames.Any(_ => _.StartsWith("Address")))
            {
                var addressFieldIndex = errorResult.MemberNames.First().Split('.')[0];

                if (rowItem.ValuesToUpdate.RelatedRowNumbers.TryGetValue(addressFieldIndex, out var rowNumber))
                {
                    return new ExcelError(errorResult.ErrorMessage, nameof(AddressDto).TrimEnd(), rowNumber);
                }

                return new ExcelError(errorResult.ErrorMessage);
            }

            return base.GetError(rowItem, errorResult);
        }

        private void CheckForEmptyEmployeeNumbers(IList<ExcelRowItem<EmployeeHistoryDto, long>> rowData, ExcelMetadata excelMetadata)
        {
            var employeeNumberProperty = excelMetadata.Properties.First(_ => _.Name == nameof(IEmployeeNumberEntity.EmployeeNumber));
            var message = this.StringLocalizer.GetString(ExcelConstants.Localization.Error.PropertyRequired);

            foreach (var rowItem in rowData.Where(_ => _.Success && string.IsNullOrEmpty(_.GetEmployeeNumber())))
            {
                rowItem.Errors.Add(this.GetError(string.Format(message, employeeNumberProperty.DisplayName)));
            }
        }

        private async Task CheckNewEmployeeErrorsAsync(IList<ExcelRowItem<EmployeeHistoryDto, long>> rowData, long companyId)
        {
            if (await this.companyService.ShouldGenerateEmployeeNumber(companyId))
            {
                // ValuesToUpdate will be null if it is an address row error
                foreach (var rowItem in rowData.Where(_ => _.ValuesToUpdate != null
                                                           && (_.GetFieldValue<long?>(nameof(IEmployeeNumberEntity.EmployeeId)) == null
                                                           || _.GetFieldValue<long?>(nameof(IEmployeeNumberEntity.EmployeeId)) == 0)))
                {
                    rowItem.Errors.Add(new ExcelError(this.StringLocalizer.GetString(ExcelConstants.Localization.Error.NewEmployeeNotAllowed).ToString()));
                }
            }
        }

        private void CheckDuplicateEmployeeNumbers(IList<ExcelRowItem<EmployeeHistoryDto, long>> rowData)
        {
            var duplicateRows = rowData.Where(_ => _.Success)
                .GroupBy(_ => _.GetEmployeeNumber())
                .Where(_ => _.Count() > 1)
                .Select(_ => new { EmployeeNumber = _.Key, Items = _, Rows = _.Select(en => en.RowNumber) });

            foreach (var duplicateRow in duplicateRows)
            {
                foreach (var rowItem in duplicateRow.Items)
                {
                    rowItem.Errors.Add(new ExcelError($"Duplicate Employee Number '{duplicateRow.EmployeeNumber}' on rows {string.Join(", ", duplicateRow.Rows)}"));
                    rowItem.UploadStatus = BulkStatusType.Duplicates;
                }
            }
        }

        private void CheckDuplicateEmails(IList<ExcelRowItem<EmployeeHistoryDto, long>> rowData)
        {
            var duplicatedEmails = rowData
                .Select(_ => new
                {
                    Row = _,
                    Email = _.ValuesToUpdate.GetFieldValue<string>(nameof(EmployeeDto.Email))
                })
                .Where(_ => !string.IsNullOrWhiteSpace(_.Email))
                .GroupBy(_ => _.Email)
                .Where(_ => _.Count() > 1);

            foreach (var duplicatedEmail in duplicatedEmails)
            {
                var duplicatedRowNumbers = duplicatedEmail.Select(_ => _.Row.RowNumber);
                foreach (var rowItem in duplicatedEmail)
                {
                    rowItem.Row.Errors.Add(new ExcelError($"Duplicate Email '{duplicatedEmail.Key}' on rows {string.Join(", ", duplicatedRowNumbers)}"));
                    rowItem.Row.UploadStatus = BulkStatusType.Duplicates;
                }
            }
        }
    }
}