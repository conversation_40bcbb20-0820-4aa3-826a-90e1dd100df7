namespace PaySpace.Venuta.Excel.Employees.Uploads.Readers
{
    using System;

    using Microsoft.Extensions.Localization;

    using OfficeOpenXml;

    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Excel.Abstractions.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Exceptions;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Helpers;
    using PaySpace.Venuta.Excel.Readers;
    using PaySpace.Venuta.Services.Abstractions;

    public class EmployeeRpnFileReader : EmployeeExcelFileReader<EmployeeRpnDto, long>
    {
        public EmployeeRpnFileReader(
            IStringLocalizerFactory localizerFactory,
            IEmployeeService employeeService)
            : base(localizerFactory, employeeService)
        {
        }

        protected override MutatorActionType GetActionColumnValue(ExcelWorksheet workSheet, int rowNumber, ExcelMetadata excelMetadata, ExcelRowItem<EmployeeRpnDto, long> rowData)
        {
            var value = "Overwrite";
            if (workSheet.TryGetCell(rowNumber, this.GetColumnIndex(workSheet, excelMetadata.ActionColumnName), out var cell))
            {
                value = cell.GetValue<string>();
            }

            if (Enum.TryParse<BulkCaptureAction>(value, true, out var action) && action is BulkCaptureAction.Overwrite)
            {
                return MutatorActionType.Update;
            }

            throw new InvalidBulkActionException(value);
        }
    }
}