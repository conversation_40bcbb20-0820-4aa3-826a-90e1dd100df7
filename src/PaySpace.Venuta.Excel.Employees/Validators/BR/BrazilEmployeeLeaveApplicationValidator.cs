namespace PaySpace.Venuta.Excel.Employees.Validators.BR
{
    using System;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Maddalena;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    [CountryService(CountryCode.BR)]
    internal sealed class BrazilEmployeeLeaveApplicationValidator : EmployeeLeaveApplicationValidator
    {
        private readonly IStringLocalizer<EmployeeLeaveAdjustment> localizer;
        private readonly IBrazilEmployeeLeaveBalanceService brazilEmployeeLeaveBalanceService;
        private readonly IEmployeeLeaveService employeeLeaveService;
        private readonly IEmployeeLeaveValueService employeeLeaveValueService;
        private readonly IEmployeeLeaveValidationService employeeLeaveValidationService;
        private readonly ITenantProvider tenantProvider;

        public BrazilEmployeeLeaveApplicationValidator(
            IStringLocalizer<EmployeeLeaveAdjustment> localizer,
            IStringLocalizer<EmployeeLeaveApplicationDto> dtoLocalizer,
            ApplicationContext applicationContext,
            IBrazilEmployeeLeaveBalanceService brazilEmployeeLeaveBalanceService,
            IEmployeeLeaveSettingService employeeLeaveSettingService,
            IEmployeeLeaveService employeeLeaveService,
            IEmployeeLeaveValueService employeeLeaveValueService,
            ICompanyService companyService,
            ICompanySettingService companySettingService,
            ICompanyLeaveService companyLeaveService,
            ICompanyRunService companyRunService,
            IAuthorizationService authorizationService,
            IModelMetadataProvider modelMetadataProvider,
            IObjectModelValidator objectModelValidator,
            IServiceProvider serviceProvider,
            IScopedCache scopedCache,
            IEmployeeLeaveValidationService employeeLeaveValidationService,
            ITenantProvider tenantProvider,
            IFileManagerStorageService fileManagerStorageService)
            : base(
                localizer,
                dtoLocalizer,
                applicationContext,
                employeeLeaveSettingService,
                employeeLeaveService,
                companyService,
                companySettingService,
                companyLeaveService,
                companyRunService,
                authorizationService,
                modelMetadataProvider,
                objectModelValidator,
                serviceProvider,
                scopedCache,
                employeeLeaveValidationService,
                tenantProvider,
                fileManagerStorageService)
        {
            this.localizer = localizer;
            this.brazilEmployeeLeaveBalanceService = brazilEmployeeLeaveBalanceService;
            this.employeeLeaveService = employeeLeaveService;
            this.employeeLeaveValueService = employeeLeaveValueService;
            this.employeeLeaveValidationService = employeeLeaveValidationService;
            this.tenantProvider = tenantProvider;
        }

        protected override void GeneralRules()
        {
            base.GeneralRules();

            this.RuleFor(_ => _.ThirteenCheque)
                .NotEqual(true)
                .WhenAsync(this.ThirteenthChequeNotAllowedAsync)
                .WithMessage(ErrorCodes.Leave.ThirteenthChequeNotAllowed);

            this.RuleFor(_ => _.SellVacationDays)
                .NotEqual(true)
                .When(_ => _.LeaveType != LeaveType.Annual)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Leave.InvalidLeaveDaysToSell));

            this.When(_ => _.LeaveType == LeaveType.Annual,
                () =>
                {
                    this.RuleFor(_ => _)
                        .MustAsync(this.ValidateParcelsAsync)
                        .When(_ => !_.SkipValidation)
                        .WithMessage("{ValidationMessage}");

                    this.RuleFor(_ => _.SellVacationDays)
                        .NotEqual(true)
                        .WhenAsync(this.DisallowSellingVacationDaysAsync)
                        .WithMessage(this.Localizer.GetString(ErrorCodes.Leave.CannotSellDays));
                });

            this.RuleFor(_ => _.SellVacationDays)
                .Must((m, c) => m.LeaveTransactionType != LeaveEntryType.Cancellation)
                .WithMessage(ErrorCodes.Leave.InvalidLeaveEntryTypeToSell)
                .When(_ => _.SellVacationDays);

            this.RuleFor(_ => _)
                .MustAsync(this.IsValidStartDateAsync)
                .When(_ => !_.SkipValidation)
                .WithMessage("{ValidationMessage}");

            this.RuleFor(_ => _)
                .MustAsync(this.HaveNoSuspensionOverlapAsync)
                .WithMessage(this.Localizer.GetString("lblHasSuspensionOverlap"));

            this.RuleFor(_ => _)
                .MustAsync(this.HaveNoTerminationOverlapAsync)
                .WithMessage(this.Localizer.GetString("lblHasTerminationOverlap"));
        }

        private async Task<bool> ThirteenthChequeNotAllowedAsync(EmployeeLeaveApplicationDto model, CancellationToken cancellationToken)
        {
            if (model.LeaveType != LeaveType.Annual)
            {
                return true;
            }

            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            return !await this.employeeLeaveService.ShowThirteenthChequeAsync(model.EmployeeId, companyId, model.RunId);
        }

        private async Task<bool> DisallowSellingVacationDaysAsync(EmployeeLeaveApplicationDto dto, ValidationContext<EmployeeLeaveApplicationDto> context, CancellationToken token)
        {
            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            var settings = this.GetLeaveApplicationSettings(context);

            return !await this.brazilEmployeeLeaveBalanceService.AllowVacationDaysSellAsync(
                companyId,
                dto.EmployeeId,
                dto.RunId,
                dto.CompanyLeaveSetupId,
                settings.LeaveSettings.EmployeeHistoricConcession);
        }

        private async Task<bool> HaveNoSuspensionOverlapAsync(EmployeeLeaveApplicationDto model, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            return !await this.employeeLeaveValidationService.HasSuspensionOverlapAsync(
                new Tenant(companyId, model.EmployeeId),
                model.LeaveStartDate,
                model.LeaveEndDate,
                cancellationToken);
        }

        private async Task<bool> HaveNoTerminationOverlapAsync(EmployeeLeaveApplicationDto model, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            return !await this.employeeLeaveValidationService.HasTerminationOverlapAsync(
                new Tenant(companyId, model.EmployeeId),
                model.LeaveStartDate,
                model.LeaveEndDate);
        }

        private async Task<bool> ValidateParcelsAsync(EmployeeLeaveApplicationDto dto, EmployeeLeaveApplicationDto property, ValidationContext<EmployeeLeaveApplicationDto> context, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            var settings = this.GetLeaveApplicationSettings(context);
            var dbEntity = (EmployeeLeaveAdjustment)context.RootContextData["Entity"];

            var result = await this.employeeLeaveValidationService
                .ValidateLeaveParcelsAsync(
                    dbEntity,
                    companyId,
                    settings.Balance.Days,
                    settings.LeaveSettings.EmployeeHistoricConcession?.ConcessionYearEndDate,
                    cancellationToken);
            if (!result.valid)
            {
                context.MessageFormatter.AppendArgument("ValidationMessage", result.message);
            }

            return result.valid;
        }

        private async Task<bool> IsValidStartDateAsync(
            EmployeeLeaveApplicationDto viewModel,
            EmployeeLeaveApplicationDto property,
            ValidationContext<EmployeeLeaveApplicationDto> context,
            CancellationToken cancellation = default)
        {
            var settings = this.GetLeaveApplicationSettings(context);

            if (settings.LeaveSettings.OffDays is null or 0)
            {
                return true;
            }

            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            var disabledStartDates = (await this.employeeLeaveValueService.GetDisabledStartDatesAsync(
                viewModel.LeaveType,
                viewModel.CompanyLeaveSetupId,
                viewModel.LeaveStartDate,
                companyId,
                viewModel.EmployeeId,
                settings.LeaveSettings.OffDays)).ToList();
            var disabledStartDays = await this.employeeLeaveValueService.GetDisabledStartDaysAsync(companyId, viewModel.EmployeeId, settings.LeaveSettings.OffDays);

            if (disabledStartDates.Contains(viewModel.LeaveStartDate!.Value) || disabledStartDays.Contains(viewModel.LeaveStartDate!.Value.DayOfWeek))
            {
                context.MessageFormatter.AppendArgument("ValidationMessage", this.localizer.GetString(ErrorCodes.Leave.StartDateInvalid, settings.LeaveSettings.OffDays));
                return false;
            }

            return true;
        }
    }
}