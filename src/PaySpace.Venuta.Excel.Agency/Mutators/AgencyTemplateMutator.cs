namespace PaySpace.Venuta.Excel.Agency.Mutators
{
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Storage;

    internal sealed class AgencyTemplateMutator : EntityMutator<AgencyTemplateDto, Template, long>
    {
        private readonly ICompanyService companyService;

        public AgencyTemplateMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<AgencyTemplateDto, Template> entityValidator,
            IAttachmentStorageService attachmentService,
            ICompanyService companyService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.companyService = companyService;
        }

        public override async Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, AgencyTemplateDto Dto)> AddEntity(MutatorContext context, AgencyTemplateDto dto)
        {
            dto.AgencyId = await this.companyService.GetAgencyIdAsync(context.CompanyId);
            return await base.AddEntity(context, dto);
        }
    }
}