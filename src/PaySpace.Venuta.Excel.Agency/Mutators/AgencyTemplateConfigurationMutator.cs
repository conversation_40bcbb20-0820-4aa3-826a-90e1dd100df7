namespace PaySpace.Venuta.Excel.Agency.Mutators
{
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Storage;

    internal sealed class AgencyTemplateConfigurationEntityMutator : EntityMutator<AgencyTemplateConfigurationDto, TemplateConfiguration, long>
    {
        private readonly IMapper mapper;
        private readonly ICompanyService companyService;

        public AgencyTemplateConfigurationEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<AgencyTemplateConfigurationDto, TemplateConfiguration> entityValidator,
            IAttachmentStorageService attachmentService,
            ICompanyService companyService) : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.mapper = mapper;
            this.companyService = companyService;
        }

        public override async Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, AgencyTemplateConfigurationDto Dto)> AddEntity(MutatorContext context, AgencyTemplateConfigurationDto dto)
        {
            // Map the AgencyId
            dto.AgencyId = await this.companyService.GetAgencyIdAsync(context.CompanyId);
            return await base.AddEntity(context, dto);
        }

        protected override async Task AfterMapAsync(MutatorContext context, AgencyTemplateConfigurationDto dto, TemplateConfiguration entity)
        {
            var agencyId = await this.companyService.GetAgencyIdAsync(context.CompanyId);

            var dtoValues = dto.ComponentVariableValues;
            var entityValues = entity.ComponentVariableValues;
            var dtoIds = dtoValues
                .Where(_ => _.ComponentVariableValueId > 0)
                .Select(_ => _.ComponentVariableValueId)
                .ToHashSet();

            this.AddNewValues(dtoValues, entityValues, agencyId);
            UpdateValues(dtoValues, entityValues);
            RemoveDeletedValues(agencyId, entityValues, dtoIds);

            await base.AfterMapAsync(context, dto, entity);
        }

        protected override Task<EntityValidationResult> ValidateAndDelete(MutatorContext context, AgencyTemplateConfigurationDto dto, TemplateConfiguration entity, long key)
        {
            if (entity.AgencyId.HasValue)
            {
                entity.AllowDelete = true;
            }

            return base.ValidateAndDelete(context, dto, entity, key);
        }

        protected override Task<TemplateConfiguration?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
                .Include(_ => _.ComponentVariableValues)
                .SingleOrDefaultAsync(_ => _.TemplateConfigurationId == key);
        }

        protected override Task<AgencyTemplateConfigurationDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set
                .ProjectTo<AgencyTemplateConfigurationDto>(this.mapper.ConfigurationProvider)
                .SingleOrDefaultAsync(_ => _.TemplateConfigurationId == key);
        }

        private void AddNewValues(List<ComponentVariableValueDto> dtoValues, List<ComponentVariableValue> entityValues, long agencyId)
        {
            foreach (var dto in dtoValues.Where(_ => _.ComponentVariableValueId == 0))
            {
                dto.AgencyId = agencyId;
                entityValues.Add(this.mapper.Map<ComponentVariableValue>(dto));
            }
        }

        private static void UpdateValues(List<ComponentVariableValueDto> dtoValues, List<ComponentVariableValue> entityValues)
        {
            foreach (var dto in dtoValues.Where(_ => _.ComponentVariableValueId > 0))
            {
                var existing = entityValues.FirstOrDefault(_ => _.ComponentVariableValueId == dto.ComponentVariableValueId);
                if (existing != null)
                {
                    existing.Value = dto.Value;
                }
            }
        }

        private static void RemoveDeletedValues(long agencyId, List<ComponentVariableValue> entityValues, HashSet<long> dtoIds)
        {
            entityValues.RemoveAll(_ => _.AgencyId == agencyId && _.ComponentVariableValueId > 0 &&
                !dtoIds.Contains(_.ComponentVariableValueId));
        }
    }
}