namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    using System;
    using System.Collections.Generic;

    using PaySpace.Venuta.Modules.Leave.Abstractions;

    public class BrazilLeaveViewModel : LeaveViewModel
    {
        public BrazilLeaveViewModel()
        {
        }

        public BrazilLeaveViewModel(EmployeeLeaveSettings settings)
            : base(settings)
        {
            this.IsBrazilLeave = true;
        }

        public bool ShowThirteenthCheque { get; set; }

        public bool ShowSellOption { get; set; }

        public double? TotalDaysSell { get; set; }

        public string HistoricalConcession { get; set; }

        public IEnumerable<DateTime> DisabledStartDates { get; set; }

        public IEnumerable<DayOfWeek> DisabledStartDays { get; set; }
    }
}
