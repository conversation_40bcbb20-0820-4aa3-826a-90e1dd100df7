namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Workflow;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Leave.Abstractions;

    [DisplayName(SystemAreas.Leave.Application)]
    public class LeaveViewModel
    {
        private readonly EmployeeLeaveSettings settings;

        public LeaveViewModel()
        {
        }

        public LeaveViewModel(EmployeeLeaveSettings settings)
        {
            this.settings = settings;
        }

        public long CompanyId { get; set; }

        public long EmployeeId { get; set; }

        public long FrequencyId { get; set; }

        public long CompanySchemeId { get; set; }

        [Required]
        [DropDownList]
        [Display(Name = "lblLeaveType")]
        public int? SelectedLeaveType { get; set; }

        public IWorkflow Workflow { get; set; }

        public WorkflowItem WorkflowItem { get; set; }

        public EmployeeLeaveAdjustment LeaveAdjustment { get; set; }

        public IEnumerable<DateTime> Waiting { get; set; }

        public IEnumerable<DateTime> Approved { get; set; }

        public IEnumerable<DateTime> Holidays { get; set; }

        public IEnumerable<DateTime> Disabled { get; set; }

        public double Future { get; set; }

        public LeaveBalance Balance { get; set; }

        public bool ShowBalance { get; set; }

        public bool ShowBuckets => this.settings?.ShowBuckets == true;

        public bool ShowReasons => this.settings?.ShowReasons == true;

        public bool ReadOnly => this.settings?.ReadOnly == true;

        public bool HideEntry => this.LeaveAdjustment?.StartDate == null || this.LeaveAdjustment?.EndDate == null;

        public bool HideDays => this.settings?.AllowHours == true && this.LeaveAdjustment?.StartDate == this.LeaveAdjustment?.EndDate;

        public bool AllowHours => this.settings?.AllowHours == true;

        public bool AllowTimeOfDay => this.settings?.AllowTimeOfDay == true;

        public bool ShowHours { get; set; }

        public bool OverrideLeaveDays => this.settings?.OverrideLeaveDays == true;

        public IEnumerable<DayOfWeek> InactiveWorkingDays => this.settings?.InactiveWorkingDays ?? Enumerable.Empty<DayOfWeek>();

        public bool ReflectInHours => this.settings?.ReflectInHours == true;

        public bool IsAdminUser { get; set; }

        public double PayRateHours => this.settings?.PayRateHours ?? 8;

        public DateTime? ConcessionEndDate => this.settings?.EmployeeHistoricConcession?.ConcessionYearEndDate;

        public bool AllowEdit { get; set; }

        public DateTime? MinStartDate { get; set; }

        public DateTime? MinEndDate { get; set; }

        public DateTime? MaxEndDate { get; set; }

        public double? MinDays { get; set; }

        public double? MaxDays { get; set; }

        public double MinHours { get; set; }

        public double? MaxHours { get; set; }

        public double? PartialDayTotal { get; set; }

        public int? ConsecutiveDays { get; set; }

        public bool IsLeaveAttachmentRequired => this.settings?.IsLeaveAttachmentRequired == true;

        public bool IsApply { get; set; }

        public bool IsBrazilLeave { get; set; }

        public bool HasValidLeaveType => this.LeaveAdjustment.LeaveType > 0;
    }

    public class LeaveBalance
    {
        public static LeaveBalance Empty { get; } = new LeaveBalance();

        public bool ReflectInHours { get; set; }

        public double HoursPerDay { get; set; }

        public double Days
        {
            get
            {
                if (this.ReflectInHours)
                {
                    return this.TimeSpan.TotalHours / this.HoursPerDay;
                }

                return this.TimeSpan.TotalDays;
            }
        }

        public TimeSpan TimeSpan { get; set; }
    }
}