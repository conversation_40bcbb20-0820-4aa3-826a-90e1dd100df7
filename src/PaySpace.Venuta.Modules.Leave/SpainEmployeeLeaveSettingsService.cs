namespace PaySpace.Venuta.Modules.Leave
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.ES)]
    public class SpainEmployeeLeaveSettingsService : EmployeeLeaveSettingService
    {
        private readonly ICustomFieldService customFieldService;
        private readonly IEmployeeProfileService employeeProfileService;
        private readonly ApplicationContext context;

        public SpainEmployeeLeaveSettingsService(
            ApplicationContext context,
            IStringLocalizerFactory stringLocalizerFactory,
            ICompanySettingService companySettingService,
            ICompanyLeaveService companyLeaveService,
            ICompanyRosterService companyRosterService,
            IEmployeeLeaveService employeeLeaveService,
            IPayRateService payRateService,
            IEnumService enumService,
            IScopedCache scopedCache,
            IPublicHolidayService publicHolidayService,
            ICustomFieldService customFieldService,
            IEmployeeProfileService employeeProfileService,
            ICompanyPaymentModuleService companyPaymentModuleService,
            IEmployeeLeaveSetupService employeeLeaveSetupService,
            ICompanyRunService companyRunService)
            : base(
                context,
                stringLocalizerFactory,
                companySettingService,
                companyLeaveService,
                companyRosterService,
                employeeLeaveService,
                payRateService,
                enumService,
                scopedCache,
                publicHolidayService,
                companyPaymentModuleService,
                employeeLeaveSetupService,
                companyRunService)
        {
            this.context = context;
            this.customFieldService = customFieldService;
            this.employeeProfileService = employeeProfileService;
        }

        public override async Task<string> GetMunicipalityCodeAsync(long companyId, long employeeId)
        {
            var bankHolidayOption = await this.GetEmployeeCustomFieldValueAsync(
                companyId,
                employeeId,
                SpainConstants.PublicHolidayOptionFieldCode);

            if (bankHolidayOption == SpainConstants.FollowWorkLocationValue)
            {
                return await this.GetMunicipalityCodeAsync(employeeId);
            }

            return await this.employeeProfileService.GetEmployeeRegionCustomFieldCodeAsync(
                companyId,
                employeeId,
                SpainConstants.MunicipalityFieldCode);
        }

        private async Task<string> GetEmployeeCustomFieldValueAsync(long companyId, long employeeId, string fieldCode)
        {
            var customField = await this.customFieldService.GetCustomFieldAsync(companyId, "Employee", null, fieldCode);

            return await this.context.Set<EmployeeCustomFieldValue>()
                .TagWithSource()
                .Where(cf => cf.EmployeeId == employeeId && cf.CustomFieldId == customField.CustomFieldId)
                .OrderByDescending(cf => cf.EffectiveDate)
                .Select(cf => cf.Code)
                .FirstOrDefaultAsync();
        }

        private Task<string> GetMunicipalityCodeAsync(long employeeId)
        {
            return this.context.Set<Address>()
                .TagWithSource()
                .Where(a => a.EmployeeId == employeeId && a.AddressType == AddressType.Physical)
                .Select(a => a.Municipality.FieldCode)
                .FirstOrDefaultAsync();
        }
    }
}