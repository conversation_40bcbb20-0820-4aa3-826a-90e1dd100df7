namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyEmploymentCategory
    {
        [Key]
        [Column("pkEmploymentCategoryID")]
        public long CategoryId { get; set; }

        [Column("EmploymentCatCode")]
        public string CategoryCode { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Column("EmploymentCategoryDesc")]
        public string CategoryDescription { get; set; }

        public override string ToString()
        {
            return this.CategoryDescription;
        }
    }
}
