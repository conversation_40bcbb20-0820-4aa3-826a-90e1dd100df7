namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("CompanyAttachmentClassification")]
    public class CompanyAttachmentClassification
    {
        [Key]
        [Column("pkCompanyAttachmentClassificationID")]
        public long ClassificationId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Column("AttachmentClassification")]
        public string Classification { get; set; }

        public string ExcludeSecurityRoles { get; set; }

        public override string ToString()
        {
            return this.Classification;
        }
    }
}
