namespace PaySpace.Venuta.Data.Models.Company
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyGl
    {
        [Key]
        [Column("pkCompanyGLID")]
        public long CompanyGlId { get; set; }

        [Column("fkCompanyFrequencyId")]
        public long CompanyFrequencyId { get; set; }

        [DataType(DataType.Date)]
        public DateTime EffectiveDate { get; set; }

        [Column("GLHeaderName")]
        public string HeaderName { get; set; }

        public override string ToString()
        {
            return this.HeaderName;
        }
    }
}
