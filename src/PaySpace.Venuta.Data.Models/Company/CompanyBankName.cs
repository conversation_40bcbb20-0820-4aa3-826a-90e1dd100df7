namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("CompanyBankNames")]
    public class CompanyBankName
    {
        private string defaultBranchCode;

        [Key]
        [Column("pkCompanyBankNamesID")]
        public long CompanyBankNameId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Column("CompanyBankName")]
        public string BankName { get; set; }

        public string DefaultBranchCode
        {
            get => this.defaultBranchCode ?? string.Empty;
            set => this.defaultBranchCode = value;
        }
    }
}