namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyLegalBody
    {
        [Key]
        [Column("pkCompanyLegalBodyID")]
        public long CompanyLegalBodyId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        public string LegalBodyCode { get; set; }

        [Column("LegalBody")]
        public string LegalBodyDescription { get; set; }

        public override string ToString()
        {
            return this.LegalBodyDescription;
        }
    }
}
