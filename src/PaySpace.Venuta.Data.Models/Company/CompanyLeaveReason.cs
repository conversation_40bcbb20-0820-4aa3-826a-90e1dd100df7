namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    [Table("CompanyLeaveReasons")]
    [DisplayName(SystemAreas.Leave.Application)]
    public class CompanyLeaveReason
    {
        [Key]
        [Column("pkLeaveReasonID")]
        public long LeaveReasonId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Column("fkCompanyLeaveID")]
        public long? CompanyLeaveSetupId { get; set; }

        public virtual CompanyLeaveSetup? CompanyLeaveSetup { get; set; }

        [Column("fkLeaveTypeID")]
        public int LeaveTypeId { get; set; }

        public EnumLeaveType LeaveType { get; set; }

        [Required]
        public string LeaveReason { get; set; }

        public override string ToString()
        {
            return this.LeaveReason;
        }
    }
}
