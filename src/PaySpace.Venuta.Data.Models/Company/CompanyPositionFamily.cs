namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyPositionFamily
    {
        [Key]
        [Column("pkFamilyID")]
        public long FamilyId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Column("FamilyCode")]
        public string Code { get; set; }

        [Column("FamilyDescription")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}
