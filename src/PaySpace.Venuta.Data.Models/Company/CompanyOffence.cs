namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyOffence
    {
        [Key]
        [Column("pkCompanyOffenceID")]
        public long CompanyOffenceId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        public string OffenceCode { get; set; }

        [Column("Offence")]
        public string OffenceDescription { get; set; }

        public override string ToString()
        {
            return this.OffenceDescription;
        }
    }
}
