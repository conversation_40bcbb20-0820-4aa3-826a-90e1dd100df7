namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("CompanyManagerRequestTypeAttachments")]
    public class CompanyManagerRequestTypeAttachment
    {
        [Key]
        [Column("pkRequestTypeAttachmentID")]
        public long RequestTypeAttachmentId { get; set; }

        [Column("fkRequestTypeID")]
        public long RequestTypeId { get; set; }

        [Column("fkCompanyAttachmentID")]
        public long CompanyAttachmentId { get; set; }
    }
}
