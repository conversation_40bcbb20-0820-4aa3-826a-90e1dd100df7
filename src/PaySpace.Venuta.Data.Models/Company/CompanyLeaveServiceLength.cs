namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyLeaveServiceLength
    {
        [Key]
        [Column("pkServiceLengthID")]
        public long ServiceLengthId { get; set; }

        [Column("fkCompanyLeaveDetailsID")]
        public long CompanyLeaveDetailId { get; set; }

        public CompanyLeaveDetail CompanyLeaveDetail { get; set; }

        public string ServiceDescription { get; set; }

        public int StartYear { get; set; }

        public int EndYear { get; set; }

        public decimal Accrual { get; set; }

        [Column("fkAccrualPeriodID")]
        public int AccrualPeriodId { get; set; }

        [Column("fkLeaveAccrualValueID")]
        public LeaveAccrualValue LeaveAccrualValueId { get; set; }

        public decimal AccrualPeriodValue { get; set; }
    }
}