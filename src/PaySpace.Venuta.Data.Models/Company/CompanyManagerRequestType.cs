namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.ChangeRequest.Area)]
    public class CompanyManagerRequestType
    {
        [Key]
        [Column("pkRequestTypeID")]
        public long RequestTypeId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Required]
        [Display(Name = "lblRequestType")]
        public string RequestTypeName { get; set; }

        [Column("fkCompanyAttachmentID")]
        public long? CompanyAttachmentId { get; set; }

        [Column("isAttachmentReq")]
        public bool? IsAttachmentReq { get; set; }

        public string RequestTypeDescription { get; set; }

        public string ExcludeSecurityRoles { get; set; }
    }
}
