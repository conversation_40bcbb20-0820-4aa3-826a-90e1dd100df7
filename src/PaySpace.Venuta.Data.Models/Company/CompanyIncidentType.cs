namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyIncidentType
    {
        [Key]
        [Column("pkCompanyIncidentTypeID")]
        public long CompanyIncidentTypeId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        public string IncidentType { get; set; }

        public override string ToString()
        {
            return this.IncidentType;
        }
    }
}
