namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("CompanyLeaveBucketMappingDetails")]
    public class CompanyLeaveBucketMappingDetail
    {
        [Key]
        [Column("pkLveMappingDetID")]
        public long LeaveMappingDetailId { get; set; }

        [Column("fkLveMappingID")]
        public long LeaveMappingId { get; set; }

        public virtual CompanyLeaveBucketMapping LeaveMapping { get; set; }

        [Column("fkFromCompanyLeaveID")]
        public long FromCompanyLeaveSetupId { get; set; }

        [Column("fkToCompanyLeaveID")]
        public long ToCompanyLeaveSetupId { get; set; }
    }
}
