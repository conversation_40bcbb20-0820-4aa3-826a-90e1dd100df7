namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyIncreaseReason
    {
        [Key]
        [Column("pkIncreaseReasonID")]
        public long ReasonId { get; set; }

        [Column("IncreaseReasonCode")]
        public string ReasonCode { get; set; }

        [Column("fkCompanyId")]
        public long CompanyId { get; set; }

        [Column("IncreaseReason")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}
