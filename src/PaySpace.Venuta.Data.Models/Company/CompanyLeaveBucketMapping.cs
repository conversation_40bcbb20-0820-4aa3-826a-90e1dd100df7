namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyLeaveBucketMapping
    {
        [Key]
        [Column("pkLveMappingID")]
        public long LeaveMappingId { get; set; }

        [Column("fkFromSchemeID")]
        public long FromSchemeId { get; set; }

        [Column("fkToSchemeID")]
        public long ToSchemeId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }
    }
}