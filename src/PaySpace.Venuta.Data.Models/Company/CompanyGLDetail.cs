namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Data.Models.Components;

    [Table("CompanyGLDetails")]
    public class CompanyGLDetail : ICompanyAuditEntity
    {
        [Key]
        [Column("pkCompanyGLDetailsID")]
        public long CompanyGLDetailId { get; set; }

        [Column("fkCompanyGLID")]
        public long CompanyGLId { get; set; }

        [ForeignKey(nameof(CompanyGLId))]
        public virtual CompanyGl CompanyGL { get; set; }

        [Column("fkCompanyComponentID")]
        public long CompanyComponentId { get; set; }

        [ForeignKey(nameof(CompanyComponentId))]
        public virtual ComponentCompany ComponentCompany { get; set; }

        public string GLAccountNumber { get; set; }

        public string GLContraAccountNumber { get; set; }

        public string GLAccountNumberOnly { get; set; }

        public string GLContraAccountNumberOnly { get; set; }

        [NotMapped]
        public long CompanyId { get; set; }
    }
}