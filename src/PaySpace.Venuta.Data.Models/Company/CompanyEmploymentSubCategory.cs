namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyEmploymentSubCategory
    {
        [Key]
        [Column("pkEmploymentSubCategoryID")]
        public long SubCategoryId { get; set; }

        [Column("EmploymentSubCatCode")]
        public string SubCategoryCode { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        [Column("EmploymentSubCategoryDesc")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}
