namespace PaySpace.Venuta.Data.Models.Company
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    // Employee Bank Detail Indicator, created in company configurations.
    // NB! This is not an enum
    [Table("CompanyEnumEDIndicator")]
    public class CompanyEbdIndicator
    {
        [Key]
        [Column("pkEBDIndicatorID")]
        public long EbdIndicatorId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        public string EbdIndicator { get; set; }

        public override string ToString()
        {
            return this.EbdIndicator;
        }
    }
}