namespace PaySpace.Venuta.Data.Models.Agency
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Data.Models.Bureau;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Infrastructure;

    public enum AgencyType
    {
        NotSet = -1,
        AuditLarge = 1,
        Aggregator = 3,
        DirectClient = 4,
        AuditSmall = 5,
        Reseller = 6
    }

    [DisplayName(SystemAreas.BusinessPartner.Area)]
    public class Agency
    {
        private int? passwordExpiryPeriod;
        private bool? hashEncryptPassword;
        private bool? balancingTool;
        private bool? agencyInvoice;
        private bool? agencyDiscount;
        private bool? disallowCompanyBankDetailChanges;
        private int? paymentTerms;
        private int? billingDay;
        private bool? allowManualPayment;
        private bool? showManualPaymentOnInvoice;
        private bool? priority;
        private bool? applyCrossCompanyCount;
        private bool? applyCrossGroupCount;
        private bool? showPasswordField;

        [Key]
        [Column("pkAgencyID")]
        public long AgencyId { get; set; }

        [Column("fkAgencyId")]
        public long? LinkingAgencyId { get; set; }

        public virtual AgencyBankDetail AgencyBankDetail { get; set; }

        public virtual Address Address { get; set; }

        public virtual CompanyTheme CompanyTheme { get; set; }

        [Required]
        [Display(Name = "lblAgencyName")]
        public string AgencyName { get; set; }

        [Column("APayURL")]
        [Display(Name = "lblAPayURL")]
        public string APayUrl { get; set; }

        [IgnoreAudit]
        public string ATheme { get; set; }

        [Display(Name = "lblPasswordExpiryPeriod")]
        public int? PasswordExpiryPeriod
        {
            get => this.passwordExpiryPeriod ?? 0;
            set => this.passwordExpiryPeriod = value;
        }

        [Display(Name = "lblEnableUserAgencySubInvoiceDetails")]
        public bool AgencySubInvoiceDetails { get; set; }

        [Display(Name = "lblShowsPasswordField")]
        public bool? ShowPasswordField
        {
            get => this.showPasswordField ?? false;
            set => this.showPasswordField = value;
        }

        [Display(Name = "lblHashEncryptPassword")]
        public bool? HashEncryptPassword
        {
            get => this.hashEncryptPassword ?? false;
            set => this.hashEncryptPassword = value;
        }

        [Display(Name = "lblBalancingTool")]
        public bool? BalancingTool
        {
            get => this.balancingTool ?? false;
            set => this.balancingTool = value;
        }

        [Display(Name = "lblAgencyContactPersonFirstName")]
        public string AgencyContactPersonFirstName { get; set; }

        [Display(Name = "lblAgencyContactPersonLastName")]
        public string AgencyContactPersonLastName { get; set; }

        [Display(Name = "lblAgencyContactTelephone")]
        public string AgencyContactTelephone { get; set; } = string.Empty;

        [Display(Name = "lblAgencyContactEmail")]
        public string AgencyContactEmail { get; set; }

        [Column("AgencyVATNumber")]
        [Display(Name = "lblAgencyVatNumber")]
        public string? AgencyVatNumber { get; set; }

        [Display(Name = "lblAgencyInvoice")]
        public bool? AgencyInvoice
        {
            get => this.agencyInvoice ?? false;
            set => this.agencyInvoice = value;
        }

        [Display(Name = "lblAPayName")]
        public string? APayName { get; set; }

        [Display(Name = "lblAPayContactNo")]
        public string? APayContactNo { get; set; }

        [Display(Name = "lblAPayContactEmail")]
        public string? APayContactEmail { get; set; }

        public byte[] APayLogo { get; set; }

        [Display(Name = "lblAgencyDiscount")]
        public bool? AgencyDiscount
        {
            get => this.agencyDiscount ?? false;
            set => this.agencyDiscount = value;
        }

        [Column("DisallowCompBankDetChanges")]
        [Display(Name = "lblDisallowCompanyBankDetailChanges")]
        public bool? DisallowCompanyBankDetailChanges
        {
            get => this.disallowCompanyBankDetailChanges ?? false;
            set => this.disallowCompanyBankDetailChanges = value;
        }

        [Display(Name = "lblBillingDay")]
        public int? BillingDay
        {
            get => this.billingDay ?? 7;
            set => this.billingDay = value;
        }

        [Display(Name = "lblAgencyDiscountPercent")]
        public decimal? AgencyDiscountPercent { get; set; }

        [Column("fkBillingSourceID")]
        [Display(Name = "lblBillingSourceId")]
        public int? BureauBillingCountryId { get; set; }

        public virtual BureauBillingCountry BureauBillingCountry { get; set; }

        [Display(Name = "lblAllowManualPayment")]
        public bool? AllowManualPayment
        {
            get => this.allowManualPayment ?? false;
            set => this.allowManualPayment = value;
        }

        [Column("ShowManualPaymentOnOnvoice")]
        [Display(Name = "lblShowManualPaymentOnInvoice")]
        public bool? ShowManualPaymentOnInvoice
        {
            get => this.showManualPaymentOnInvoice ?? false;
            set => this.showManualPaymentOnInvoice = value;
        }

        [Column("fkAgencyTypeID")]
        [Display(Name = "lblAgencyType")]
        public int? AgencyTypeId { get; set; }

        public virtual EnumAgencyType AgencyType { get; set; }

        [Column("PrioritySupport")]
        [Display(Name = "lblPriority")]
        public bool? Priority
        {
            get => this.priority ?? false;
            set => this.priority = value;
        }

        [Display(Name = "lblAgencyNewDiscount")]
        public decimal? AgencyNewDiscount { get; set; }

        [Display(Name = "lblApplyCrossCompanyCount")]
        public bool? ApplyCrossCompanyCount
        {
            get => this.applyCrossCompanyCount ?? false;
            set => this.applyCrossCompanyCount = value;
        }

        [Display(Name = "lblApplyCrossGroupCount")]
        public bool? ApplyCrossGroupCount
        {
            get => this.applyCrossGroupCount ?? false;
            set => this.applyCrossGroupCount = value;
        }

        [Display(Name = "lblPaymentTerms")]
        public int? PaymentTerms
        {
            get => this.paymentTerms ?? 8;
            set => this.paymentTerms = value;
        }

        [Display(Name = "lblPaymentTermsExtDate")]
        public DateTime? PaymentTermsExtDate { get; set; }

        [Column("fkMFAID")]
        [Display(Name = "lblMfaAgencyLevel")]
        public int? MfaOptionId { get; set; }

        public virtual EnumMfaOption MfaOption { get; set; }

        [Column("LoginURL")]
        [Display(Name = "lblLoginUrl")]
        public string? LoginUrl { get; set; }

        [Column("LoginName")]
        [Display(Name = "lblLoginUrlDisplay")]
        public string? LoginUrlDisplay { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "lblDeactivationDate")]
        public DateTime? DeactivationDate { get; set; }

        [Display(Name = "lblComment")]
        public string? Comment { get; set; }

        public string? Region { get; set; }

        public virtual ICollection<User> Users { get; set; }

        public virtual ICollection<Company> Companies { get; set; }

        public override string ToString()
        {
            return this.AgencyName;
        }
    }
}