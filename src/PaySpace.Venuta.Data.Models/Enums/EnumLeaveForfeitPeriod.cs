namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public enum LeaveForfeitPeriod
    {
        None = -1, // classic use case
        Year = 1,
        Month = 2,
        Day = 3
    }

    // Enum in CompanyLeaveDetail
    [Table("EnumLeaveForfietPeriod")]
    public class EnumLeaveForfeitPeriod
    {
        [Key]
        [Column("pkForfeitPeriodID")]
        public int ForfeitPeriodId { get; set; }

        [Column("ForfeitPeriodId")]
        public string ForfeitPeriodCode { get; set; }

        public string ForfeitPeriodDescription { get; set; }

        public static implicit operator LeaveForfeitPeriod(EnumLeaveForfeitPeriod d)
        {
            return (LeaveForfeitPeriod)d.ForfeitPeriodId;
        }

        public override string ToString()
        {
            return this.ForfeitPeriodDescription;
        }
    }
}