namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public enum LeaveAccrualPeriod
    {
        None = -1, // classic use case
        Month = 2,
        Year = 3,
        Days = 4,
        Hours = 5
    }

    public class EnumLeaveAccrualPeriod
    {
        [Key]
        [Column("pkAccrualPeriodID")]
        public int AccrualPeriodId { get; set; }

        public string AccrualPeriodCode { get; set; }

        public string AccrualPeriodDescription { get; set; }

        public override string ToString()
        {
            return this.AccrualPeriodDescription;
        }
    }
}