namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public enum LeaveAccrualValue
    {
        None = -1, // classic use case
        Day = 1,
        Hours = 2
    }

    public class EnumLeaveAccrualValue
    {
        [Key]
        [Column("pkLeaveAccrualValueID")]
        public int AccrualValueId { get; set; }

        public string AccrualValueCode { get; set; }

        public string AccrualValue { get; set; }

        public override string ToString()
        {
            return this.AccrualValue;
        }
    }
}