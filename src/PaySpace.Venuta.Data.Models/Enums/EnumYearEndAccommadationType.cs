namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public enum YearEndAccommadationType
    {
        ResidenceProvidedByEmployer = 1,
        HotelAccomodation = 2
    }

    [Table("EnumYearEndAccommadationType")]
    public class EnumYearEndAccommadationType
    {
        [Key]
        [Column("pkEnumYearEndAccommadationTypeID")]
        public int YearEndAccommodationTypeId { get; set; }

        public string YearEndAccommodationTypeCode { get; set; }

        public string YearEndAccommodationTypeDescription { get; set; }

        public override string ToString()
        {
            return this.YearEndAccommodationTypeDescription;
        }
    }
}
