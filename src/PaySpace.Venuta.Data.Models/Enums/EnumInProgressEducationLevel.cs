namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class EnumInProgressEducationLevel
    {
        [Key]
        [Column("pkInProgressEducationLevelID")]
        public int InProgressEducationLevelId { get; set; }

        [Column("InProgressEducationLevelCode")]
        public string Code { get; set; }

        [Column("InProgressEducationLevelDescription")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}