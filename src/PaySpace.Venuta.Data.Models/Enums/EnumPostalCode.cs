namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("EnumPostalCode")]
    public class EnumPostalCode : EnumCountryBase
    {
        [Key]
        [Column("pkPostalCodeID")]
        public long PostalCodeId { get; set; }

        public string Code { get; set; }

        public string Description { get; set; }
    }
}
