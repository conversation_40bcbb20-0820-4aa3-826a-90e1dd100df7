namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public enum OccupationCode
    {
        [Description("Personnel in exclusive office work")]
        A = 1,
        [Description("Trade representatives")]
        B = 2,
        [Description("Trades personnel in installations and repairs in buildings, construction sites and construction work in general")]
        D = 3,
        [Description("Drivers of motor vehicles transporting goods with a payload capacity exceeding 3.5t")]
        F = 4,
        [Description("Cleaning personnel in general. Cleaning of buildings and all types of establishments")]
        G = 5,
        [Description("Security guards, guards, sworn guards and security personnel")]
        H = 6,
        [Description("Second group of contribution to the special regime of the sea")]
        V = 7,
        [Description("Third group of contribution to the special regime of the sea")]
        W = 8,
        [Description("Loading and unloading, stowage and unstowage")]
        X = 9,
        [Description("Usual work inside mines")]
        Y = 10
    }

    [Table("EnumOccupationCodes")]
    public class EnumOccupationCode : EnumCountryBase
    {
        [Key]
        [Column("pkOccupationCodeID")]
        public int OccupationCodeId { get; set; }

        public string Code { get; set; }

        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}
