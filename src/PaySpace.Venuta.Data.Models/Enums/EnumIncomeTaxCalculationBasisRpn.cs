namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class EnumIncomeTaxCalculationBasisRpn
    {
        [Key]
        [Column("pkIncomeTaxCalculationId")]
        public int IncomeTaxCalculationId { get; set; }

        public string IncomeTaxCalculationOption { get; set; }

        public string IncomeTaxDescription { get; set; }
    }
}
