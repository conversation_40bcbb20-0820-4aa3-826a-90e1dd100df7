namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class EnumNoticePeriodType
    {
        [Key]
        [Column("pkNoticePeriodTypeID")]
        public int NoticePeriodTypeId { get; set; }

        [Column("NoticePeriodTypeCode")]
        public string Code { get; set; }

        [Column("NoticePeriodTypeDescription")]
        public string Description { get; set; }
    }
}