namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class EnumContractType
    {
        [Key]
        [Column("pkContractTypeID")]
        public int ContractTypeId { get; set; }

        [Column("ContractTypeCode")]
        public string Code { get; set; }

        [Column("ContractTypeDescription")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}