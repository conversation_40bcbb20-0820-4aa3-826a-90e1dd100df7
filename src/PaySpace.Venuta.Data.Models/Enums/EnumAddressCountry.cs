namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public enum AddressCountry
    {
        UnitedKingdom = 7,
        Ireland = 43,
        Australia = 62,
        Brazil = 78,
        Canada = 84,
        India = 137,
        Spain = 222,
        Singapore = 264,
        Malaysia = 164
    }

    public class EnumAddressCountry
    {
        [Key]
        [Column("pkAddressCountryID")]
        public int AddressCountryId { get; set; }

        [Required]
        public string AddressCountryCode { get; set; }

        [Required]
        public string AddressCountryDescription { get; set; }

        public virtual ICollection<EnumProvince> Provinces { get; set; }

        public string Iso3DigitCode { get; set; }

        public override string ToString()
        {
            return this.AddressCountryDescription;
        }
    }
}