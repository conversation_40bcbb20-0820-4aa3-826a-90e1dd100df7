namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class EnumContractTypeGroup
    {
        [Key]
        [Column("pkContractTypeGroupID")]
        public int ContractTypeGroupId { get; set; }

        [Column("ContractTypeGroupCode")]
        public string Code { get; set; }

        [Column("ContractTypeGroupDescription")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}