namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class EnumHighestEducationLevel
    {
        [Key]
        [Column("pkHighestEducationLevelID")]
        public int HighestEducationLevelId { get; set; }

        [Column("HighestEducationLevelCode")]
        public string Code { get; set; }

        [Column("HighestEducationLevelDescription")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}