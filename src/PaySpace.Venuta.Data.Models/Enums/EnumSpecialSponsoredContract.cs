namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class EnumSpecialSponsoredContract
    {
        [Key]
        [Column("pkSpecialSponsoredContractID")]
        public int SpecialSponsoredContractId { get; set; }

        [Column("SpecialSponsoredContractCode")]
        public string Code { get; set; }

        [Column("SpecialSponsoredContractDescription")]
        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}