namespace PaySpace.Venuta.Data.Models.Enums
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public enum LeaveAccrualOption
    {
        Upfront = 1,
        PerMonth = 2
    }

    public class EnumLeaveAccrualOption
    {
        [Key]
        [Column("pkAccrualOptionID")]
        public int AccrualOptionId { get; set; }

        public string AccrualOptionCode { get; set; }

        public string AccrualOptionDescription { get; set; }

        public override string ToString()
        {
            return this.AccrualOptionDescription;
        }
    }
}