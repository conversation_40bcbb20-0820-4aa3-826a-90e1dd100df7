namespace PaySpace.Venuta.Data.Models.Enums
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    public class EnumEOnboardingStatus
    {
        [Key]
        [Column("pkEOnboardingStatusID")]
        public int EOnboardingStatusId { get; set; }

        public string Description { get; set; }

        public override string ToString()
        {
            return this.Description;
        }
    }
}
