namespace PaySpace.Venuta.Data.Models.Enums
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    public enum FurnitureFittingOption
    {
        F = 1,
        P = 2
    }

    [Table("EnumFurnitureFittingOption")]
    public class EnumFurnitureFittingOption
    {
        [Key]
        [Column("pkFurnitureFittingOptionID")]
        public int FurnitureFittingOptionId { get; set; }

        public string FurnitureFittingOptionCode { get; set; }

        public string FurnitureFittingOptionDescription { get; set; }
    }
}
