namespace PaySpace.Venuta.Data.Models.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;

    public class EmployeeAppendix8A
    {
        [Key]
        [Column("pkEmployeeAppendix8AId")]
        public long EmployeeAppendix8AId { get; set; }

        [Column("fkEmpID")]
        public long EmployeeId { get; set; }
        public virtual Employee Employee { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        public virtual Company Company { get; set; }

        [Column("fkTaxYearID")]
        public int TaxYearId { get; set; }
        public virtual CountryTaxYear TaxYear { get; set; }

        public DateTime? OccupationPeriodStartDate { get; set; }

        public DateTime? OccupationPeriodEndDate { get; set; }

        public int? NumberOfDays { get; set; } // Calculated, but stored

        [StringLength(30)]
        public string AddressLine1 { get; set; }

        [StringLength(30)]
        public string AddressLine2 { get; set; }

        [StringLength(30)]
        public string AddressLine3 { get; set; }

        public int? NumberOfEmployeesSharing { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? AnnualValuePremises { get; set; }

        [Column("fkFurnitureFittingOptionID")]
        public int? FurnitureFittingOptionID { get; set; }

        public EnumFurnitureFittingOption FurnitureFittingOption { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? ValueFurnitureFitting { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? RentPaidToLandlord { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? TaxableValuePlaceOfResidence { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? RentPaidByEmployee { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? TotalTaxableValuePlaceOfResidence { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? UtilitiesCosts { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? DriverCosts { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? ServantCosts { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? TaxableValueUtilitiesHousekeeping { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? CostHotelAccommodation { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? HotelAmountPaidByEmployee { get; set; }

        [Column(TypeName = "decimal(9, 2)")]
        public decimal? TaxableValueHotelAccommodation { get; set; }

        [Column(TypeName = "decimal(11, 2)")]
        public decimal? TotalAccommodationBenefit { get; set; }
    }
}
