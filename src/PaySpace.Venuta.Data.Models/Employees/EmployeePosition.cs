namespace PaySpace.Venuta.Data.Models.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using Maddalena;

    using Newtonsoft.Json;

    using PaySpace.Data.Models.Converters;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Data.Models.Workflow;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    public enum JobMode
    {
        KeepJob = 1,
        SearchAll = 2,
        OrgUnit = 3
    }

    [Table("EmployeePosition")]
    [DisplayName(SystemAreas.Profile.Positions)]
    public class EmployeePosition : CustomFieldEntity<EmployeePositionCustomFieldValue>, ICompanyRunValidationEntity, IEmployeeAuditEntity
    {
        private long? gradeId;
        private long? directlyReportsEmployeeId;
        private long? employmentCategoryId;
        private long? organizationGroupId;

        [Key]
        [Column("pkPosEmpID")]
        public long EmployeePositionId { get; set; }

        [Column("fkEmpID")]
        public long EmployeeId { get; set; }

        public Employee Employee { get; set; }

        [Required]
        [Column("fkPositionID")]
        [Display(Name = "lblOrganizationPosition")]
        public long OrganizationPositionId { get; set; }

        [Display(Name = "lblOrganizationPosition")]
        public OrganizationPosition OrganizationPosition { get; set; }

        [Column("fkGradeID")]
        [Display(Name = "lblGrade")]
        public long? GradeId
        {
            get => this.gradeId ?? -1;
            set => this.gradeId = value ?? -1;
        }

        public OrganizationGrade? Grade { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        [Display(Name = "lblEffectiveDate")]
        public DateTime EffectiveDate { get; set; }

        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        [Display(Name = "lblInactiveFrom")]
        public DateTime? InactiveFrom { get; set; }

        [Column("DirectlyReportsID")]
        [Display(Name = "lblOverridingReportsToPosition")]
        public long? DirectlyReportsPositionOverrideId { get; set; }

        public OrganizationPosition? DirectlyReportsPositionOverride { get; set; }

        [Column("fkOrgGroupID")]
        [Display(Name = "lblOrganizationGroup")]
        public long? OrganizationGroupId
        {
            get => this.organizationGroupId ?? -1;
            set => this.organizationGroupId = value ?? -1;
        }

        [Display(Name = "lblOrganizationGroup")]
        public OrganizationGroup? OrganizationGroup { get; set; }

        [Column("fkRegionID")]
        [Display(Name = "lblOrganizationRegion")]
        public long? OrganizationRegionId { get; set; }

        public OrganizationRegion? OrganizationRegion { get; set; }

        [Column("fkPayPointID")]
        [Display(Name = "lblPayPoint")]
        public long? PayPointId { get; set; }

        public OrganizationPayPoint? PayPoint { get; set; }

        [Column("fkDirectlyReportsEmpID")]
        [Display(Name = "lblDirectlyReportsEmployee")]
        public long? DirectlyReportsEmployeeId
        {
            get => this.directlyReportsEmployeeId;
            set => this.directlyReportsEmployeeId = value ?? -1;
        }

        [ForeignKey("DirectlyReportsEmployeeId")]
        [Display(Name = "lblDirectlyReportsEmployee")]
        public Employee? DirectlyReportsEmployee { get; set; }

        [Column("fkEmploymentCategoryID")]
        [Display(Name = "lblCategory")]
        public long? EmploymentCategoryId
        {
            get => this.employmentCategoryId;
            set => this.employmentCategoryId = value ?? -1;
        }

        public CompanyEmploymentCategory? EmploymentCategory { get; set; }

        [Column("fkEmploymentSubCategoryID")]
        [Display(Name = "lblSubCategory")]
        public long? EmploymentSubCategoryId { get; set; }

        public CompanyEmploymentSubCategory? EmploymentSubCategory { get; set; }

        [Column("fkPositionTypeID")]
        [Display(Name = "lblPositionType")]
        public int? PositionTypeId { get; set; }

        public EnumPositionType? PositionType { get; set; }

        [Column("fkAdministratorID")]
        [Display(Name = "lblAdministrator")]
        public long? AdministratorId { get; set; }

        [ForeignKey("AdministratorId")]
        public Employee? Administrator { get; set; }

        [Column("fkWFRoleID")]
        [Display(Name = "lblWorkflow")]
        public long? WorkflowRoleId { get; set; }

        public CompanyWorkflowRole? WorkflowRole { get; set; }

        [Display(Name = "lblGl")]
        public string GlName { get; set; }

        [Column("fkTradeUnionID")]
        [Display(Name = "lblTradeUnion")]
        public int? TradeUnionId { get; set; }

        public EnumTradeUnion? TradeUnion { get; set; }

        [Display(Name = "lblIsPromotion")]
        public bool? IsPromotion { get; set; }

        [Column("fkRosterID")]
        [Display(Name = "lblRoster")]
        public long? RosterId { get; set; }

        public CompanyRoster? Roster { get; set; }

        [Column("fkJobID")]
        [Display(Name = "lblJob")]
        public long? JobId { get; set; }

        public CompanyJobManagement? Job { get; set; }

        [Display(Name = "lblComments")]
        public string Comments { get; set; }

        [Display(Name = "lblAltPositionName")]
        public string AltPositionName { get; set; }

        public DateTime? DateAdded { get; set; }

        [Display(Name = "lblPositionEffectiveDate")]
        public DateTime? PositionEffectiveDate { get; set; }

        public DateTime? DateCreated { get; set; }

        [Column("fkCreationRunId")]
        public long? CreationRunId { get; set; }

        public CompanyRun? CreationRun { get; set; }

        [Column("fkCustomTradeUnionID")]
        [Display(Name = "CustomTradeUnion")]
        public long? CustomTradeUnionId { get; set; }

        [ForeignKey(nameof(CustomTradeUnionId))]
        public TableBuilder? TableBuilder { get; set; }

        [NotMapped]
        public long? DefaultGradeId { get; set; }

        [NotMapped]
        public long? DefaultOrganizationPositionId { get; set; }

        [NotMapped]
        public JobMode JobMode { get; set; }

        [NotMapped]
        public bool FromJobManagement { get; set; }

        [NotMapped]
        public bool IsTemporaryManager { get; set; }

        [NotMapped]
        [CountryVisible(CountryCode.CA)]
        [Display(Name = "lblWCBClassCode")]
        public string WcbClassCode { get; set; }

        [NotMapped]
        [CountryVisible(CountryCode.CA)]
        [Display(Name = "lblWCBClassName")]
        public string WcbClassName { get; set; }

        public ICollection<EmployeePositionLevel> OrganizationGroups { get; set; }
    }
}