namespace PaySpace.Venuta.Data.Models.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using Maddalena;

    using Newtonsoft.Json;

    using PaySpace.Data.Models.Converters;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [Table("Employee")]
    [DisplayName(SystemAreas.Profile.Basic)]
    public class Employee : CustomFieldEntity<EmployeeCustomFieldValue>, IEmployeeName, IEmployeeAuditEntity
    {
        private string homeNumber;
        private string workExtension;
        private string cellNumber;
        private string empMiddleName;
        private string workNumber;
        private int? languageId;
        private string email;
        private int? disabledTypeId;
        private int? citizenshipId;

        [Key]
        [Column("pkEmpID")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long EmployeeId { get; set; }

        [Column("fkCompanyID")]
        public long CompanyId { get; set; }

        public Company Company { get; set; }

        [Display(Name = "lblEmpNumber")]
        [Column("EmpNumber")]
        public string EmployeeNumber { get; set; }

        [CountryNotRequired(CountryCode.CA, CountryCode.IE, CountryCode.MY, CountryCode.IN)]
        [Display(Name = "lblTitle")]
        [Column("fkTitleID")]
        public int? TitleId { get; set; }

        public EnumTitle Title { get; set; }

        [Required]
        [Display(Name = "lblFirstName")]
        [Column("EmpFirstName")]
        public string FirstName { get; set; }

        [Required]
        [Display(Name = "lblLastName")]
        [Column("EmpLastName")]
        public string LastName { get; set; }

        [Display(Name = "lblMiddleName")]
        public string EmpMiddleName
        {
            get => this.empMiddleName ?? string.Empty;
            set => this.empMiddleName = value;
        }

        [Display(Name = "lblPrefName")]
        [Column("EmpPreferredName")]
        [CountryRequired(CountryCode.MY, CountryCode.IN)]
        public string PreferredName { get; set; }

        [StringLength(5)]
        [Display(Name = "lblInitial")]
        [CountryNotRequired(CountryCode.CA, CountryCode.IE, CountryCode.MY, CountryCode.IN, CountryCode.AU)]
        public string EmpInitials { get; set; }

        [IgnoreAudit]
        [Display(Name = "lblName")]
        public string FullName { get; set; }

        [Display(Name = "lblName")]
        public string FullNameWithEmployeeNumber => $"{this.FullName} ({this.EmployeeNumber})";

        public string Initials => $"{this.FirstName[0]}{this.LastName[0]}".ToUpper();

        [Display(Name = "lblHomeNumber")]
        [Column("EmpHomeNo")]
        public string HomeNumber
        {
            get => this.homeNumber ?? string.Empty;
            set => this.homeNumber = value;
        }

        [Display(Name = "lblWorkNumber")]
        [Column("EmpWorkNo")]
        public string WorkNumber
        {
            get => this.workNumber ?? string.Empty;
            set => this.workNumber = value;
        }

        [Display(Name = "lblCellNumber")]
        [Column("EmpCell")]
        public string CellNumber
        {
            get => this.cellNumber ?? string.Empty;
            set => this.cellNumber = value;
        }

        [StringLength(6)]
        [Display(Name = "lblWorkEx")]
        [Column("EmpWorkExt")]
        public string WorkExtension
        {
            get => this.workExtension ?? string.Empty;
            set => this.workExtension = value;
        }

        //[EmailAddress]
        [Display(Name = "lblEmailAdd")]
        [Column("EmpEmail")]
        public string Email
        {
            get => this.email ?? string.Empty;
            set => this.email = value;
        }

        [Column("fkLanguageID")]
        [Display(Name = "lblLanguage")]
        [CountryRequired(CountryCode.IE)]
        public int? LanguageId
        {
            get => this.languageId ?? -1;
            set => this.languageId = value;
        }

        public EnumLanguage Language { get; set; }

        [CountryNotRequired(CountryCode.IE)]
        [Display(Name = "lblGender")]
        [Column("fkGenderID")]
        public int? GenderId { get; set; }

        public EnumGender Gender { get; set; }

        [Display(Name = "lblRace")]
        [Column("fkRaceID")]
        public int? RaceId { get; set; }

        public EnumRace Race { get; set; }

        [CountryNotRequired(CountryCode.IE)]
        [Display(Name = "lblNationality")]
        [Column("fkNationality")]
        public int? NationalityId { get; set; }

        public EnumCountry Nationality { get; set; }

        [Display(Name = "lblCitiz")]
        [Column("fkCitizenship")]
        public int? CitizenshipId
        {
            get => this.citizenshipId ?? -1;
            set => this.citizenshipId = value;
        }

        public EnumCountry Citizenship { get; set; }

        public bool? Disabled { get; set; }

        [Display(Name = "lblDisabledT")]
        [Column("DisabledType")]
        public int? DisabledTypeId
        {
            get => this.disabledTypeId ?? 0;
            set => this.disabledTypeId = value;
        }

        public EnumDisabledType DisabledType { get; set; }

        [Column("fkEthnicGroup")]
        public int? EthnicGroupId { get; set; }

        public EnumEthnicGroup EthnicGroup { get; set; }

        [DropDownList]
        [Display(Name = "lblUifexemption")]
        [Column("UIFExemption")]
        public int? UifExemptionId { get; set; }

        public EnumUIFExemption UifExemption { get; set; }

        [DropDownList]
        [Display(Name = "lblSdlexemption")]
        [Column("SDLExemption")]
        public int? SdlExemptionId { get; set; }

        public EnumSDLExemption SdlExemption { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        [Display(Name = "lblBirthdate")]
        [Column("EmpBirthDate")]
        public DateTime Birthday { get; set; }

        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        public DateTime? EmpGroupDate { get; set; }

        public DateTime? EmpDateCreated { get; set; }

        public string EmpTaxRefNumber { get; set; }

        public long? EmpFrequency { get; set; }

        [Column("fkRSCRegion")]
        public int? RscRegion { get; set; }

        [ScaffoldColumn(false)]
        public Guid? GuidIdentifier { get; set; }

        [Display(Name = "lblForeignNat")]
        public bool? ForeignNational { get; set; }

        [ScaffoldColumn(false)]
        public bool? IsCompanyRecurringTemplate { get; set; }

        public bool? ApplicantIndicator { get; set; }

        [Display(Name = "lblMaritalStat")]
        [Column("fkMaritalStatusID")]
        public int? MaritalStatusId { get; set; }

        public EnumMaritalStatus MaritalStatus { get; set; }

        [StringLength(50)]
        [Display(Name = "lblMaidenName")]
        public string EmpMaidenName { get; set; }

        [Display(Name = "lblReportId")]
        [Column("fkReportID")]
        public int? ReportId { get; set; }

        public EnumReportPath? Report { get; set; }

        public string CustomFieldValue { get; set; }

        public string CustomFieldValue2 { get; set; }

        public bool? IsMockEmployee { get; set; }

        [Display(Name = "lblEtiExempt")]
        [Column("ETIExempt")]
        public bool? EtiExempt { get; set; }

        [DropDownList]
        [Display(Name = "lblSubStandardIndustryCodeId")]
        [Column("fkSubStandardIndustryCodeID")]
        public int? SubStandardIndustryCodeId { get; set; } = 0;

        [ForeignKey(nameof(SubStandardIndustryCodeId))]
        public EnumStandardIndustryCodeSub StandardIndustryCodeSub { get; set; }

        [ScaffoldColumn(false)]
        [Column("fkUserCreatedByID")]
        public long? UserCreatedById { get; set; }

        [Display(Name = "lblEmergContName")]
        [Column("EmergContName")]
        public string EmergencyContactName { get; set; }

        [Display(Name = "lblEmergContNumber")]
        [Column("EmergContNumber")]
        public string EmergencyContactNumber { get; set; }

        [Display(Name = "lblEmergContAddress")]
        [Column("EmergContAddress")]
        public string EmergencyContactAddress { get; set; }

        [Display(Name = "lblIsEmpRetired")]
        public bool? IsRetired { get; set; }

        public long? CompanyFrequencyId { get; set; }

        [IgnoreAudit]
        [Column("fkEmployeeHistoryId")]
        public long? EmployeeHistoryId { get; set; }

        [NotMapped]
        public DateTime? EffectiveDate { get; set; }

        public CompanyRunFrequency? CompanyFrequency { get; set; }

        public IList<Address> Address { get; set; }

        public EmployeeWithPosition EmployeeWithPosition { get; set; }

        public EmployeeTransferHistory EmployeeTransferHistory { get; set; }

        public ICollection<EmployeeProject> EmployeeProjects { get; set; }

        public ICollection<EmployeeEmploymentStatus> EmploymentStatuses { get; set; } = [];

        public ICollection<EmployeePosition> EmployeePositions { get; set; }

        public ICollection<EmployeeBankHeader> BankHeaders { get; set; }

        public ICollection<EmployeePayRate> PayRates { get; set; }

        public ICollection<PayslipHeader> Payslips { get; set; }

        public ICollection<EmployeeLeaveSetup> EmployeeLeaveSetups { get; set; }

        public ICollection<EmployeeSuspension> EmployeeSuspensions { get; set; }

        public ICollection<EmployeeHistory> EmployeeHistory { get; set; } = [];

        [NotMapped]
        public int Age
        {
            get
            {
                var today = DateTime.Today;
                var age = today.Year - this.Birthday.Year;

                if (today.DayOfYear < this.Birthday.DayOfYear)
                {
                    age -= 1;
                }

                return age;
            }
        }

        // This property is temporary, do not tie ANY new logic to it, because it WILL be removed.
        // It is needed now by the DFB to prevent unnecessary employee delete audit records from being created.
        // It will be removed once the DFB can wrap all the INSERT/UPDATE logic in to one transaction instead of across multiple.
        [NotMapped]
        public bool DfbBypassDeleteAudit { get; set; }

        public override string ToString()
        {
            return $"{this.EmployeeId} - {this.FullName}";
        }
    }
}