namespace PaySpace.Venuta.Data.Models.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using Newtonsoft.Json;

    using PaySpace.Data.Models.Converters;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.EmployeeLumpSum.Area)]
    public class EmployeeLumpSum : IAuditEntity
    {
        [Key]
        [Column("pkEmpLumpSumID")]
        public long EmployeeLumpSumId { get; set; }

        [Column("fkTaxYearID")]
        public long TaxYearId { get; set; }

        [Column("fkEmpID")]
        public long? EmployeeId { get; set; }

        public virtual Employee Employee { get; set; }

        [Column("fkPayslipID")]
        public long PayslipId { get; set; }

        [ForeignKey(nameof(PayslipId))]
        public virtual PayslipHeader PayslipHeader { get; set; }

        public string DirectiveNumber { get; set; }

        [IgnoreAudit]
        public decimal DirectiveAmount { get; set; }

        [IgnoreAudit]
        public decimal? TaxFreeDirectiveAmount { get; set; }

        [IgnoreAudit]
        public decimal DirectiveTax { get; set; }

        [Column("fkComponentCompanyIDAmount")]
        public long ComponentCompanyIdAmount { get; set; }

        [ForeignKey(nameof(ComponentCompanyIdAmount))]
        public virtual ComponentCompany ComponentCompany { get; set; }

        [Column("fkComponentCompanyIDTax")]
        public long CompanyComponentIdTax { get; set; }

        [IgnoreAudit]
        [Column("3696Amount")]
        public decimal? Amount3696 { get; set; }

        [IgnoreAudit]
        [Column("3697Amount")]
        public decimal? Amount3697 { get; set; }

        [IgnoreAudit]
        [Column("3698Amount")]
        public decimal? Amount3698 { get; set; }

        [IgnoreAudit]
        [Column("3699Amount")]
        public decimal? Amount3699 { get; set; }

        [IgnoreAudit]
        [Column("4102Amount")]
        public decimal? Amount4102 { get; set; }

        [IgnoreAudit]
        [Column("4103Amount")]
        public decimal? Amount4103 { get; set; }

        public string ReferenceNumber { get; set; }

        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        public DateTime? DirectiveIssuedDate { get; set; }

        // Adding this property is to simplify the update method on the EmployeeLumpSumApiController
        [NotMapped]
        public string? TaxCode { get; set; }

        [NotMapped]
        public bool ShouldAuditEntity { get; set; } = true;
    }
}