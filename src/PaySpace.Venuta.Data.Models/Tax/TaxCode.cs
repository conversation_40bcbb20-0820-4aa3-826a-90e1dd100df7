namespace PaySpace.Venuta.Data.Models.Tax
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.TaxCode.Area)]
    public class TaxCode : IBureauAuditEntity
    {
        [Key]
        [Column("pkTaxCodeID")]
        public int TaxCodeId { get; set; }

        [Column("fkTaxCountryID")]
        public int TaxCountryId { get; set; }

        [Required]
        [Column("TaxCode")]
        public string Code { get; set; }

        [Required]
        public string TaxCodeDescription { get; set; }

        [Column("fkTaxCodeType")]
        public int TaxCodeTypeId { get; set; }

        [ForeignKey(nameof(TaxCodeTypeId))]
        public virtual EnumTaxType TaxType { get; set; }

        public string AlternateTaxCode1 { get; set; }

        public string AlternateTaxCode2 { get; set; }

        public bool? Inactive { get; set; }

        public bool? ValidTaxCode { get; set; }

        public bool? DoNotZeroForGrossUps { get; set; }

        public bool? DoNotShowOnOverride { get; set; }

        public bool IsTaxabilityOverrideEnabled { get; set; }

        public ICollection<TaxCodeIRP5Action> TaxCodeIRP5Actions { get; set; } = [];

        public ICollection<TaxCodeTaxabilityOverride> TaxabilityOverrides { get; set; } = [];

        [NotMapped]
        public int CountryId { get; set; }

        public override string ToString()
        {
            return this.TaxCodeDescription;
        }
    }
}
