namespace PaySpace.Venuta.Data.Models.Validation.Company
{
    using System.Text;

    using FluentValidation;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Infrastructure;

    public class CompanyGLInterAccountValidator : AbstractValidator<CompanyGLInterAccount>
    {
        private readonly IStringLocalizer localizer;

        private const int InterAccountLength = 100;
        private const int AccountNumberLength = 100;
        private const int ContraAccountNumberLength = 100;

        public CompanyGLInterAccountValidator(IStringLocalizerFactory localizerFactory)
        {
            this.localizer = localizerFactory.Create(typeof(CompanyGLInterAccount));

            this.RuleSet(RuleSetNames.CreateAndUpdate, this.CreateRules);
        }

        private void CreateRules()
        {
            this.RuleFor(_ => _.InterGLAccount)
                .MaximumLength(InterAccountLength)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.InterAccountLength));

            this.RuleFor(_ => _.GLAccountNo)
                .MaximumLength(AccountNumberLength)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.AccountNumberLength));

            this.RuleFor(_ => _.GLContraAccountNo)
                .MaximumLength(ContraAccountNumberLength)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.ContraAccountNumberLength));
        }
    }
}
