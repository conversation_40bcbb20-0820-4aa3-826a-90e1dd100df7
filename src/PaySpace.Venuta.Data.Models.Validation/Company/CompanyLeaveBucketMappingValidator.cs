namespace PaySpace.Venuta.Data.Models.Validation.Company
{
    using System.Linq;
    using System.Linq.Dynamic.Core;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Infrastructure;

    public class CompanyLeaveBucketMappingValidator : AbstractValidator<CompanyLeaveBucketMapping>
    {
        private readonly ApplicationContext context;
        private readonly IStringLocalizer<CompanyLeaveBucketMapping> localizer;

        public CompanyLeaveBucketMappingValidator(ApplicationContext context, IStringLocalizer<CompanyLeaveBucketMapping> localizer)
        {
            this.context = context;
            this.localizer = localizer;

            this.RuleSet(RuleSetNames.Create, this.CreateRules);
            this.RuleSet(RuleSetNames.Update, this.UpdateRules);
        }

        private void SharedRules()
        {
            this.RuleFor(_ => _.FromCompanyLeaveSchemeId)
                .GreaterThan(default(long))
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyLeaveBucketMapping.SelectDifferentScheme));

            this.RuleFor(_ => _.ToCompanyLeaveSchemeId)
               .GreaterThan(default(long))
               .WithMessage(this.localizer.GetString(ErrorCodes.CompanyLeaveBucketMapping.SelectDifferentScheme));
        }

        private void UpdateRules()
        {
            this.SharedRules();

            this.RuleFor(_ => _)
                .MustAsync(this.CheckIfDetailsMappingExistsAsync)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyLeaveBucketMapping.MappingAlreadyExists));
        }

        private void CreateRules()
        {
            this.SharedRules();

            this.RuleFor(_ => _)
                .MustAsync(this.CheckIfMappingExistsAsync)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyLeaveBucketMapping.MappingAlreadyExists));
        }

        private async Task<bool> CheckIfMappingExistsAsync(CompanyLeaveBucketMapping model, CancellationToken cancellationToken)
        {
            return !await this.context.Set<CompanyLeaveBucketMapping>().TagWithSource()
                .Where(_ => _.CompanyId == model.CompanyId
                && _.FromCompanyLeaveSchemeId == model.FromCompanyLeaveSchemeId
                && _.ToCompanyLeaveSchemeId == model.ToCompanyLeaveSchemeId)
                .AnyAsync(cancellationToken);
        }

        private async Task<bool> CheckIfDetailsMappingExistsAsync(CompanyLeaveBucketMapping model, CancellationToken cancellationToken)
        {
            var mappingDetails = model.CompanyLeaveBucketMappingDetails.Where(x => x.LeaveMappingDetailId == 0).ToList();

            if (mappingDetails.Count == 0)
            {
                return true;
            }

            var existingDetails = await this.context.Set<CompanyLeaveBucketMappingDetail>().TagWithSource()
                .Where(_ => _.LeaveMappingId == model.LeaveMappingId).ToListAsync(cancellationToken);

            if (existingDetails.Count != 0)
            {
                return !existingDetails.Any(item => mappingDetails.Any(x => x.FromCompanyLeaveSetupId == item.FromCompanyLeaveSetupId && x.ToCompanyLeaveSetupId == item.ToCompanyLeaveSetupId));
            }

            return true;
        }
    }
}
