namespace PaySpace.Venuta.Data.Models.Validation.Company
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;

    public class EmployeeEOnboardingDetailsValidator : AbstractValidator<EmployeeEOnboardingDetails>
    {
        private readonly ICompanyService companyService;
        private readonly ITenantProvider tenantProvider;
        private readonly IEmployeeService employeeService;
        private readonly IEmployeeEOnboardingDetailsService eOnboardingDetailsService;
        private readonly IStringLocalizer localizer;
        private readonly IUserRegionService userRegionService;

        public EmployeeEOnboardingDetailsValidator(
            ICompanyService companyService,
            ITenantProvider tenantProvider,
            IEmployeeService employeeService,
            IStringLocalizer<EmployeeEOnboardingDetails> localizer,
            IEmployeeEOnboardingDetailsService eOnboardingDetailsService,
            IUserRegionService userRegionService)
        {
            this.localizer = localizer;
            this.companyService = companyService;
            this.tenantProvider = tenantProvider;
            this.employeeService = employeeService;
            this.userRegionService = userRegionService;
            this.eOnboardingDetailsService = eOnboardingDetailsService;

            this.RuleSet(RuleSetNames.Create, this.CreateRules);
            this.RuleSet(RuleSetNames.Update, this.GeneralRules);
        }

        private void CreateRules()
        {
            this.GeneralRules();

            // Primary validation: Block manual employee numbers when auto-generation is enabled
            this.RuleFor(_ => _.EmployeeNumber)
                .MustAsync(this.ValidateEmployeeNumberNotProvidedWhenAutoGenerateAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.EmployeeNumberNotRequiredWhenGenerateEmpNo));

            // Secondary validation: Validate employee number when manual entry is allowed
            this.RuleFor(_ => _.EmployeeNumber)
                .NotEmpty()
                .WhenAsync(this.ShouldNotGenerateEmployeeNumberAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.EmployeeNumberRequired));

            this.RuleFor(_ => _.EmployeeNumber)
                .Must(this.ValidateEmployeeNumberLength)
                .WhenAsync(this.ShouldNotGenerateEmployeeNumberAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.InvalidEmployeeNumber));

            this.RuleFor(_ => _.EmployeeNumber)
                .MustAsync(this.ValidateEmployeeNumberUniquenessAsync)
                .WhenAsync(this.ShouldNotGenerateEmployeeNumberAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.EmployeeNumberExists));
        }

        private void GeneralRules()
        {
            this.RuleFor(_ => _.Firstname)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.FirstnameRequired));

            this.RuleFor(_ => _.Lastname)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.LastnameRequired));

            this.RuleFor(_ => _.EmploymentDate)
                .NotNull()
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.EmploymentDateRequired));

            this.RuleFor(_ => _.Email)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.EmailRequired))
                .EmailAddress()
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.InvalidEmail));

            this.RuleFor(_ => _.Email)
                .MustAsync(this.CheckEmailDoesNotExistsAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.EOnboarding.Keys.EmailExists));
        }

        private async Task<bool> CheckEmailDoesNotExistsAsync(EmployeeEOnboardingDetails model, string email, CancellationToken token = default)
        {
            var isNotDuplicate = !await this.eOnboardingDetailsService.CheckEmailExistsAsync(email, model.EmployeeDetailsId);

            if (isNotDuplicate)
            {
                var existsinRegion = await this.userRegionService.DoesEmailExistAsync(email);
                return !existsinRegion;
            }

            return isNotDuplicate;
        }

        private async Task<bool> ValidateEmployeeNumberNotProvidedWhenAutoGenerateAsync(EmployeeEOnboardingDetails model, string employeeNumber, CancellationToken token = default)
        {
            var shouldGenerateEmpNumber = await this.companyService.ShouldGenerateEmployeeNumberAsync(model.CompanyId);

            // If auto-generation is enabled, employee number should not be provided
            if (shouldGenerateEmpNumber && !string.IsNullOrWhiteSpace(employeeNumber))
            {
                return false;
            }

            return true;
        }

        private async Task<bool> ShouldNotGenerateEmployeeNumberAsync(EmployeeEOnboardingDetails model, CancellationToken token = default)
        {
            return !await this.companyService.ShouldGenerateEmployeeNumberAsync(model.CompanyId);
        }

        private bool ValidateEmployeeNumberLength(EmployeeEOnboardingDetails model, string employeeNumber)
        {
            if (string.IsNullOrEmpty(employeeNumber))
            {
                return true;
            }

            var employeeNumberMaxLength = this.IsTaxCountryBrazil() ? 30 : 20;
            return employeeNumber.Length <= employeeNumberMaxLength;
        }

        private async Task<bool> ValidateEmployeeNumberUniquenessAsync(EmployeeEOnboardingDetails model, string employeeNumber, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(employeeNumber))
            {
                return true;
            }

            if (model.EmployeeDetailsId == default)
            {
                if (await this.employeeService.CheckEmployeeNumberExistsAsync(model.CompanyId, employeeNumber))
                {
                    return false;
                }
            }
            else
            {
                if (await this.employeeService.CheckEmployeeNumberExistsAsync(model.CompanyId, model.EmployeeDetailsId, employeeNumber))
                {
                    return false;
                }
            }

            return true;
        }

        private bool IsTaxCountryBrazil()
        {
            return this.tenantProvider.GetTaxCountryCode() == CountryCode.BR.ToString();
        }
    }
}
