namespace PaySpace.Venuta.Data.Models.Validation.Company
{
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Data.Models.Validation.Extensions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;

    public class OrganizationPositionDetailValidator : AbstractValidator<OrganizationPositionDetail>
    {
        private readonly IStringLocalizer localizer;
        private readonly IOrganizationPositionService organizationPositionService;
        private readonly IEmployeePositionService employeePositionService;
        private readonly ICompanyJobManagementService companyJobManagementService;
        private readonly ICompanyPaymentModuleService companyPaymentModuleService;
        private readonly ITenantProvider tenantProvider;
        private readonly ICompanyService companyService;
        private readonly IScopedCache scopedCache;
        private readonly ApplicationContext context;

        public OrganizationPositionDetailValidator(
            IStringLocalizer<OrganizationPositionDetail> localizer,
            IOrganizationPositionService organizationPositionService,
            IEmployeePositionService employeePositionService,
            ICompanyJobManagementService companyJobManagementService,
            ICompanyPaymentModuleService companyPaymentModuleService,
            ITenantProvider tenantProvider,
            ICompanyService companyService,
            IScopedCache scopedCache,
            ApplicationContext context)
        {
            this.localizer = localizer;
            this.organizationPositionService = organizationPositionService;
            this.employeePositionService = employeePositionService;
            this.companyJobManagementService = companyJobManagementService;
            this.companyPaymentModuleService = companyPaymentModuleService;
            this.tenantProvider = tenantProvider;
            this.companyService = companyService;
            this.scopedCache = scopedCache;

            this.RuleSet(RuleSetNames.Create, this.CreateRules);
            this.RuleSet(RuleSetNames.Update, this.UpdateRules);
            this.RuleSet(RuleSetNames.Delete, this.DeleteRules);
            this.context = context;
        }

        private void CreateRules()
        {
            this.GeneralRules();

            this.When((_, context) => this.IsCopy(context), () =>
            {
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateDuplicatesPositionDetailAsync)
                    .WhenAsync(this.HasDescriptionChangedAsync)
                    .WithMessage(this.localizer.GetString("lblDuplicatePositions"));

                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateDuplicatesCopyPositionDetailsAsync)
                    .WithMessage(this.localizer.GetString("lblDuplicatePositionDetails"));
            })
            .Otherwise(() =>
            {
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateDuplicatesPositionDetailAsync)
                    .WithMessage(this.localizer.GetString("lblDuplicatePositions"));

                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateDuplicatesPositionDetailsAsync)
                    .WithMessage(this.localizer.GetString("lblDuplicatePositionDetails"));
            });
        }

        private void UpdateRules()
        {
            this.GeneralRules();

            this.RuleFor(_ => _)
                  .MustAsync(this.ValidateDuplicatesPositionDetailAsync)
                  .WithMessage(this.localizer.GetString("lblDuplicatePositions"))
                  .When(x => this.IsFieldModified(x.OrganizationPosition, nameof(x.OrganizationPosition.Description)));

            this.RuleFor(_ => _)
                .MustAsync(this.ValidateDuplicatesPositionDetailsAsync)
                .WithMessage(this.localizer.GetString("lblDuplicatePositionDetails"))
                .When(_ => this.IsFieldModified(_.OrganizationPosition, nameof(_.OrganizationPosition.Description)) ||
                           this.IsFieldModified(_, nameof(_.PositionCode)) ||
                           this.IsFieldModified(_, nameof(_.EffectiveDate)));
        }

        private void GeneralRules()
        {
            this.RuleFor(_ => _)
               .Must(this.ValidDate)
               .When(_ => _.InactiveDate.HasValue)
               .WithMessage(this.localizer.GetString("lblInvalidInactiveDate"));

            this.RuleFor(_ => _.GradeId)
                .GreaterThan(0)
                .NotEmpty()
                .WhenAsync(this.GradeIsRequired)
                .WithMessage(this.localizer.GetString("lblGradeRequired"));

            this.RuleFor(_ => _.OrganizationPosition.Description)
                .Length(0, 80)
                .WithMessage(this.localizer.GetString("lblDescriptionLength"));

            this.RuleFor(_ => _.PositionPurpose)
                .Length(0, 1000)
                .WithMessage(this.localizer.GetString("lblPositionPurposeLength"));

            this.RuleFor(_ => _.SpecialFeatures)
                .Length(0, 1000)
                .WithMessage(this.localizer.GetString("lblSpecialFeaturesLength"));

            this.RuleFor(_ => _.SalarySurveyCode1)
                .Length(0, 50)
                .WithMessage(this.localizer.GetString("lblSalarySurvey1CodeLength"));

            this.RuleFor(_ => _.SalarySurveyCode2)
                .Length(0, 50)
                .WithMessage(this.localizer.GetString("lblSalarySurvey2CodeLength"));
        }

        private async Task<bool> GradeIsRequired(OrganizationPositionDetail model, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            var companyIsMasterOrPremier = await this.companyPaymentModuleService.IsMasterOrPremierAsync(companyId);
            var companyHasAdvancedPositionManagement = await this.HasAdvancedPositionManagementAsync(companyId);

            return companyIsMasterOrPremier && companyHasAdvancedPositionManagement;
        }

        private Task<bool> HasAdvancedPositionManagementAsync(long companyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"OrganizationPositionDetail:HasAdvancedPositionManagementAsync:{companyId}",
                () => this.companyService.HasAdvancedPositionManagementAsync(companyId));
        }

        private void DeleteRules()
        {
            this.RuleFor(_ => _)
               .MustAsync(this.NotLinkedToEmployeeAsync)
               .WhenAsync(this.ShouldValidateForLinkedAsync)
               .WithMessage(this.localizer.GetString("lblLinkedEmployee"));

            this.RuleFor(_ => _)
                .MustAsync(this.NotLinkedToJobAsync)
                .WhenAsync(this.ShouldValidateForLinkedAsync)
                .WithMessage(this.localizer.GetString("lblLinkedJob")); // This record cannot be deleted as it has jobs linked to it.
        }

        private bool ValidDate(OrganizationPositionDetail organizationPositionDetail)
        {
            return organizationPositionDetail.EffectiveDate < organizationPositionDetail.InactiveDate;
        }

        private async Task<bool> NotLinkedToEmployeeAsync(OrganizationPositionDetail organizationPositionDetail, CancellationToken cancellationToken)
        {
            return !await this.employeePositionService.LinkedToOrganizationPositionAsync(organizationPositionDetail.OrganizationPositionId);
        }

        private async Task<bool> NotLinkedToJobAsync(OrganizationPositionDetail organizationPositionDetail, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId();
            return !await this.companyJobManagementService.GetByCompanyId(companyId!.Value)
                .AnyAsync(_ => _.PositionId == organizationPositionDetail.OrganizationPositionId, cancellationToken);
        }

        private async Task<bool> ValidateDuplicatesPositionDetailAsync(OrganizationPositionDetail entity, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId();
            return !await this.organizationPositionService.GetPositionsByCompanyId(companyId!.Value)
                .AnyAsync(_ => _.OrganizationPositionId != entity.OrganizationPositionId &&
                               _.Description == entity.OrganizationPosition.Description, cancellationToken);
        }

        private async Task<bool> ValidateDuplicatesPositionDetailsAsync(OrganizationPositionDetail entity, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId();
            var positionCode = entity.PositionCode?.Trim();
            return !await this.organizationPositionService.GetPositionDetailsByCompanyId(companyId!.Value)
                .AnyAsync(_ => _.PositionDetailId != entity.PositionDetailId &&
                               _.PositionCode.Trim() == positionCode &&
                               _.OrganizationPosition.Description == entity.OrganizationPosition.Description &&
                               _.EffectiveDate == entity.EffectiveDate, cancellationToken);
        }

        private async Task<bool> ValidateDuplicatesCopyPositionDetailsAsync(OrganizationPositionDetail entity, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId();
            var positionCode = entity.PositionCode?.Trim();
            return !await this.organizationPositionService.GetPositionDetailsByCompanyId(companyId!.Value)
                .AnyAsync(_ => _.OrganizationPositionId == entity.OrganizationPositionId &&
                               _.PositionCode.Trim() == positionCode &&
                               _.OrganizationPosition.Description == entity.OrganizationPosition.Description &&
                               _.EffectiveDate == entity.EffectiveDate, cancellationToken);
        }

        private async Task<bool> ShouldValidateForLinkedAsync(OrganizationPositionDetail entity, ValidationContext<OrganizationPositionDetail> context, CancellationToken cancellationToken)
        {
            if (context.GetValueFromValidationInfo<bool>("DeleteAll"))
            {
                return true;
            }

            return await this.scopedCache.GetOrCreateAsync(
                $"OrganizationPositionDetail:ShouldValidateForLinkedAsync:{entity.PositionDetailId}",
                () => this.ShouldValidateForLinkedInternalAsync(entity, cancellationToken));
        }

        private async Task<bool> ShouldValidateForLinkedInternalAsync(OrganizationPositionDetail entity, CancellationToken cancellationToken)
        {
            // Linked employee and jobs are linked to a position and not position detail.
            // Allow for deletion if not the last position detail, otherwise check for linked entities.
            var companyId = this.tenantProvider.GetCompanyId();
            return !await this.organizationPositionService.GetPositionDetailsByCompanyId(companyId!.Value)
                .AnyAsync(_ => _.OrganizationPositionId == entity.OrganizationPositionId &&
                               _.PositionDetailId != entity.PositionDetailId, cancellationToken);
        }

        private async Task<bool> HasDescriptionChangedAsync(OrganizationPositionDetail entity, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId();
            return await this.organizationPositionService.GetPositionsByCompanyId(companyId!.Value)
                .AnyAsync(_ => _.OrganizationPositionId == entity.OrganizationPositionId &&
                               _.Description != entity.OrganizationPosition.Description, cancellationToken);
        }

        private bool IsCopy(ValidationContext<OrganizationPositionDetail> context)
        {
            return context.GetValueFromValidationInfo<bool>("IsCopy");
        }

        private bool IsFieldModified(OrganizationPosition entity, string propertyName)
        {
            return this.context.IsFieldModified(entity, propertyName);
        }

        private bool IsFieldModified(OrganizationPositionDetail entity, string propertyName)
        {
            return this.context.IsFieldModified(entity, propertyName);
        }
    }
}