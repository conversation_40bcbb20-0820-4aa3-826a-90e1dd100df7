namespace PaySpace.Venuta.Data.Models.Validation.Company
{
    using System.Linq.Dynamic.Core;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Modules.GeneralLedger.Abstractions;
    using PaySpace.Venuta.Services.Company;

    public class CompanyGlValidator : AbstractValidator<CompanyGl>
    {
        private readonly IStringLocalizer localizer;
        private readonly ICompanyGlService companyGlService;
        private readonly ITenantProvider tenantProvider;

        private const int HeaderNameMaxLength = 50;

        public CompanyGlValidator(
            IStringLocalizerFactory localizerFactory,
            ICompanyGlService companyGlService,
            ICompanyGLDetailService companyGLDetailService,
            ITenantProvider tenantProvider)
        {
            this.companyGlService = companyGlService;

            this.localizer = localizerFactory.Create(typeof(CompanyGl));

            this.RuleSet(RuleSetNames.CreateAndUpdate, () =>
            {

                this.RuleFor(_ => _.EffectiveDate)
                    .Cascade(CascadeMode.Stop)
                    .NotEmpty()
                    .WithMessage(this.localizer.GetString("errEffectiveDateRequired"));

                this.RuleFor(_ => _.HeaderName)
                    .Cascade(CascadeMode.Stop)
                    .NotEmpty()
                        .WithMessage(this.localizer.GetString("errHeaderNameRequired"))
                    .MaximumLength(HeaderNameMaxLength)
                        .WithMessage(this.localizer.GetString("errHeaderNameExceedsCharLimit"));

                this.RuleFor(_ => _)
                    .MustAsync(this.IsHeaderNameUniqueAsync)
                    .WithMessage(this.localizer.GetString("errHeaderNameAlreadyUsed"));

                // Set validator for the nested GlDetails
                this.RuleForEach(_ => _.CompanyGLDetails)
                    .SetValidator(new CompanyGLDetailValidator(companyGLDetailService, localizerFactory));

                // Set validator for the nested CompanyGlExtra
                this.RuleForEach(_ => _.CompanyGLExtras)
                    .SetValidator(new CompanyGLExtraValidator(localizerFactory));

                // Set validator for the nested CompanyGlInterAccounts 
                this.RuleForEach(_ => _.CompanyGLInterAccounts)
                    .SetValidator(new CompanyGLInterAccountValidator(localizerFactory));
            });

            this.RuleSet(RuleSetNames.Delete, this.DeleteRules);

            this.tenantProvider = tenantProvider;
        }

        private async Task<bool> IsHeaderNameUniqueAsync(CompanyGl model, CancellationToken cancellationToken)
        {
            var frequencyId = this.tenantProvider.GetFrequencyId();

            var duplicateExists = await this.companyGlService
                .CompanyGlExistsAsync(
                    model.HeaderName,
                    frequencyId!.Value,
                    model.CompanyGlId,
                    cancellationToken);

            return !duplicateExists;
        }

        private void DeleteRules()
        {
            this.RuleFor(_ => _)
                .MustAsync(async (companyGL, cancellationToken) =>
                {
                    var companyId = this.tenantProvider.GetCompanyId()!.Value;
                    return !await this.companyGlService.HasDependenciesAsync(
                        companyGL.HeaderName,
                        companyId,
                        cancellationToken);
                })
                .WithMessage(this.localizer.GetString("errCompanyGlHasDependencies"));
        }
    }
}
