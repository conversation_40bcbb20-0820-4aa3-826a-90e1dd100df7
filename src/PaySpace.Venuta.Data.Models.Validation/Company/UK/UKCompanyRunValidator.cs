namespace PaySpace.Venuta.Data.Models.Validation.Company.UK
{
    using System;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Components;

    [CountryService(CountryCode.GB)]
    internal partial class UKCompanyRunValidator : CompanyRunValidator
    {
        private readonly ICompanyRunService companyRunService;

        public UKCompanyRunValidator(
            IStringLocalizer<CompanyRun> localizer,
            ApplicationContext context,
            ITenantProvider tenantProvider,
            IComponentValueService componentValueService,
            ICompanyService companyService,
            ICompanyRunService companyRunService,
            ICalculationWebApiClient calculationApiClient,
            ICompanyFrequencyService companyFrequencyService)
            : base(
                  localizer,
                  context,
                  tenantProvider,
                  componentValueService,
                  companyService,
                  companyRunService,
                  calculationApiClient,
                  companyFrequencyService)
        {
            this.companyRunService = companyRunService;
        }

        protected override void GeneralRules()
        {
            base.GeneralRules();

            this.RuleFor(_ => _.PayDate)
                .MustAsync(this.ValidateNoPublicHolidayAsync)
                .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyRun.PayDateOnBankHoliday));

            this.When((_, context) => !this.IsBureau(context), () =>
            {
                // Previous open runs need to be closed before this run can be closed
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateHasOpenRunsBeforeCurrentAsync)
                    .When(_ => _.Status == RunStatus.Closed)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyRun.CloseRunsBeforeCurrent));

                // Interim run order cannot change
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateCanChangeInterimRunOrder)
                    .When(_ => _.Status == RunStatus.Closed)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyRun.InterimRunOrderCannotBeChanged));

                //Invalid pay date and run order number combination. Please check run sequences for this period
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidatePayDateAndRunOrderNumberAsync)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyRun.InvalidPayDateRunOrder));

                //Invalid pay date and run order number combination. Please check run sequences for this period
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateHasSameOrderInFrequency)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyRun.InvalidPayDateRunOrder));
            });
        }

        protected override void CreateRules()
        {
            //Pay date cannot be before {calendarStart} or after {calendarEnd}
            this.RuleFor(_ => _.PayDate)
                .MustAsync(this.ValidateNewRunPayDateInCalendarRangeAsync)
                .When(_ => _.RunType == RunType.Interim)
                .WithMessage("{ValidationMessage}");

            base.CreateRules();
        }

        protected override void UpdateRules()
        {
            //Pay date cannot be before {calendarStart} or after {calendarEnd}
            this.RuleFor(_ => _.PayDate)
                .MustAsync(this.ValidateExistingRunPayDateInCalendarRangeAsync)
                .WithMessage("{ValidationMessage}");

            this.When((_, context) => !this.IsBureau(context), () =>
            {
                //Invalid run order number combination. Please check run sequences for this period
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateFutureRunsOrderAndPeriodStartDateAsync)
                    .When(_ => _.Status == RunStatus.Future)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyRun.InvalidRunOrderCheckSequence));

                //Invalid run order number combination. The future run cannot be before an open run
                this.RuleFor(_ => _)
                    .MustAsync(this.ValidateFutureRunsPeriodStartDateAsync)
                    .When(_ => _.Status == RunStatus.Future)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.CompanyRun.InvalidRunOrderFutureBeforeOpen));
            });

            base.UpdateRules();
        }

        //Ensure that the paydate for a UK entity does not fall outside of the allocated pay period        
        private async Task<bool> ValidateNewRunPayDateInCalendarRangeAsync(CompanyRun entity, DateTime? payDate, ValidationContext<CompanyRun> context, CancellationToken cancellationToken)
        {
            var mainRunId = await this.companyRunService.GetCompanyRuns(entity.CompanyFrequencyId, entity.PeriodCode)
                .Where(_ => _.RunType == RunType.Main && _.PeriodStartDate == entity.PeriodStartDate && _.PeriodEndDate == entity.PeriodEndDate)
                .Select(_ => _.RunId)
                .FirstOrDefaultAsync(cancellationToken);

            if (mainRunId == default)
            {
                return true;
            }

            return await this.IsPaydateInCalendarRange(payDate, context, mainRunId);
        }

        //Ensure that the paydate for a UK entity does not fall outside of the allocated pay period        
        private Task<bool> ValidateExistingRunPayDateInCalendarRangeAsync(CompanyRun entity, DateTime? payDate, ValidationContext<CompanyRun> context, CancellationToken cancellationToken)
        {
            return this.IsPaydateInCalendarRange(payDate, context, entity.RunId);
        }

        private async Task<bool> IsPaydateInCalendarRange(DateTime? payDate, ValidationContext<CompanyRun> context, long runId)
        {
            var payCalendar = await this.companyRunService.GetPayCalendarStartAndEndDateAsync(runId);

            if (!payCalendar.StartDate.HasValue || !payCalendar.EndDate.HasValue)
            {
                return true;
            }

            if (payDate >= payCalendar.StartDate && payDate <= payCalendar.EndDate)
            {
                return true;
            }

            context.MessageFormatter
                .AppendArgument(
                    "ValidationMessage",
                    this.Localizer.GetString(ErrorCodes.CompanyRun.PayDateOutsideAllocatedPeriodFormatted, payCalendar.StartDate.Value.ToShortDateString(), payCalendar.EndDate.Value.ToShortDateString()));

            return false;
        }

        private async Task<bool> ValidateHasOpenRunsBeforeCurrentAsync(CompanyRun entity, CancellationToken cancellationToken)
        {
            return !(await this.companyRunService.GetAllCompanyRunsAsync(entity.CompanyFrequencyId))
                    .Any(_ => _.Status == RunStatus.Open && (_.PeriodStartDate < entity.PeriodStartDate
                             || (_.PeriodStartDate == entity.PeriodStartDate && _.OrderNumber < entity.OrderNumber)));
        }

        private async Task<bool> ValidateCanChangeInterimRunOrder(CompanyRun entity, CancellationToken cancellationToken)
        {
            return !(await this.companyRunService.GetAllGroupedCompanyRunsPerPeriodAsync(entity.CompanyFrequencyId, entity.PeriodStartDate, entity.PeriodEndDate, false))
                    .Any(_ => _.RunId == entity.RunId
                            && _.RunType == RunType.Interim
                            && _.OrderNumber != entity.OrderNumber);
        }

        private async Task<bool> ValidateFutureRunsOrderAndPeriodStartDateAsync(CompanyRun entity, CancellationToken cancellationToken)
        {
            return !(await this.companyRunService.GetAllCompanyRunsAsync(entity.CompanyFrequencyId))
                    .Any(_ => _.Status == RunStatus.Open
                        && _.OrderNumber > entity.OrderNumber
                        && _.PeriodStartDate >= entity.PeriodStartDate);
        }

        private async Task<bool> ValidatePayDateAndRunOrderNumberAsync(CompanyRun entity, CancellationToken cancellationToken)
        {
            return !(await this.companyRunService.GetAllGroupedCompanyRunsPerPeriodAsync(entity.CompanyFrequencyId, entity.PeriodStartDate, entity.PeriodEndDate, false))
                    .Any(_ => (_.OrderNumber > entity.OrderNumber && _.PayDate < entity.PayDate)
                           || (_.OrderNumber < entity.OrderNumber
                                && (_.PayDate > entity.PayDate
                                    || (entity.Status == RunStatus.Closed && (_.Status == RunStatus.Open || _.Status == RunStatus.Future)))));
        }

        private async Task<bool> ValidateHasSameOrderInFrequency(CompanyRun entity, CancellationToken cancellationToken)
        {
            var valid = true;
            var allPeriodRuns = (await this.companyRunService.GetAllGroupedCompanyRunsPerPeriodAsync(entity.CompanyFrequencyId, entity.PeriodStartDate, entity.PeriodEndDate, false))
                .Where(_ => _.OrderNumber == entity.OrderNumber && _.RunId != entity.RunId);

            if (allPeriodRuns.Any())
            {
                var treatAsNew = false;
                var previousRun = await this.companyRunService.GetCompanyRuns(entity.CompanyFrequencyId)
                    .FirstOrDefaultAsync(_ => _.RunId == entity.RunId, cancellationToken);

                if (previousRun != null)
                {
                    treatAsNew = !(previousRun.OrderNumber < entity.OrderNumber);
                }
                else
                {
                    treatAsNew = true;
                }

                if (treatAsNew)
                {
                    valid = !allPeriodRuns.Any(_ => _.PayDate < entity.PayDate);
                }
                else
                {
                    valid = !allPeriodRuns.Any(_ => _.PayDate > entity.PayDate);
                }
            }

            return valid;
        }

        private async Task<bool> ValidateFutureRunsPeriodStartDateAsync(CompanyRun entity, CancellationToken cancellationToken)
        {
            return !(await this.companyRunService.GetAllCompanyRunsAsync(entity.CompanyFrequencyId))
                .Any(_ => _.Status == RunStatus.Open && _.PeriodStartDate > entity.PeriodStartDate);
        }
    }
}