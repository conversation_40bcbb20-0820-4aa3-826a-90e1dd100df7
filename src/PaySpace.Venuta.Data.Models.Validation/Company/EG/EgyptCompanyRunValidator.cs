namespace PaySpace.Venuta.Data.Models.Validation.Company.EG
{
    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Components;

    [CountryService(CountryCode.EG)]
    internal partial class EgyptCompanyRunValidator(
        IStringLocalizer<CompanyRun> localizer,
        ApplicationContext context,
        ITenantProvider tenantProvider,
        IComponentValueService componentValueService,
        ICompanyService companyService,
        ICompanyRunService companyRunService,
        ICalculationWebApiClient calculationApiClient,
        ICompanyFrequencyService companyFrequencyService)
        : CompanyRunValidator(
            localizer,
            context,
            tenantProvider,
            componentValueService,
            companyService,
            companyRunService,
            calculationApiClient,
            companyFrequencyService)
    {
        protected override bool IsSundayPayDayDisallowed => false;
    }
}
