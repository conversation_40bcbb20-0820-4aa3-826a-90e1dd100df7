namespace PaySpace.Venuta.Data.Models.Validation.Company
{
    using System.Text;

    using FluentValidation;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Infrastructure;

    public class CompanyGLExtraValidator : AbstractValidator<CompanyGLExtra>
    {
        private readonly IStringLocalizer localizer;

        private const int ComponentDescriptionLength = 100;
        private const int GLAccountNoLength = 100;
        private const int GLContraAccountNoLength = 50;

        public CompanyGLExtraValidator(IStringLocalizerFactory localizerFactory)
        {
            this.localizer = localizerFactory.Create(typeof(CompanyGLExtra));

            this.RuleSet(RuleSetNames.CreateAndUpdate, this.CreateRules);
        }

        private void CreateRules()
        {
            this.RuleFor(_ => _.CompanyFrequencyId)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.CompanyFrequencyIdRequired));

            this.RuleFor(_ => _.EffectiveDate)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.EffectiveDateRequired));

            this.RuleFor(_ => _.ComponentDescription)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.ComponentDescriptionRequired));

            this.RuleFor(_ => _.ComponentDescription)
                .MaximumLength(ComponentDescriptionLength)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.ComponentDescriptionLength));

            this.RuleFor(_ => _.GLAmount)
                .GreaterThanOrEqualTo(0)
                .When(_ => _.GLAmount != default)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.AmountGreaterThanZero));

            this.RuleFor(_ => _.GLAccountNo)
                .MaximumLength(GLAccountNoLength)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.AccountNumberLength));

            this.RuleFor(_ => _.GLContraAccountNo)
                .MaximumLength(GLContraAccountNoLength)
                .WithMessage(this.localizer.GetString(ErrorCodes.CompanyGL.ContraAccountNumberLength));
        }
    }
}
