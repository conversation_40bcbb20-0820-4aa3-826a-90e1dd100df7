namespace PaySpace.Venuta.Data.Models.Validation.Employees.SG
{
    using FluentValidation;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.SG)]
    public class SingaporeEmployeeValidator : EmployeeValidator
    {
        public SingaporeEmployeeValidator(
            ApplicationContext applicationContext,
            IStringLocalizer<EmployeeValidator> employeeLocalizer,
            IStringLocalizer<AddressValidator> addressLocalizer,
            IStringLocalizer<EmployeeEmploymentStatus> statusLocalizer,
            IStringLocalizer<EmployeePayRate> payRateLocalizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICompanyService companyService,
            ICompanySettingService companySettingService,
            IEmployeeService employeeService,
            IEmployeeProfileService employeeProfileService,
            IEmployeeAddressService employeeAddressService,
            IEmploymentStatusService employmentStatusService,
            IUserService userService,
            IEmployeeHistoryService employeeHistoryService,
            ICompanyFrequencyService frequencyService,
            ICountryServiceFactory countryServiceFactory,
            IGenderService genderService,
            IUserRegionService userRegionService)
            : base(
                applicationContext,
                employeeLocalizer,
                addressLocalizer,
                statusLocalizer,
                payRateLocalizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                companyService,
                companySettingService,
                employeeService,
                employeeProfileService,
                employeeAddressService,
                employmentStatusService,
                userService,
                employeeHistoryService,
                frequencyService,
                countryServiceFactory,
                genderService,
                userRegionService)
        {
        }

        protected override void AddPreferredNameRules()
        {
            this.RuleFor(_ => _.PreferredName)
                .NotEmpty()
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.FullNameRequired));

            this.RuleFor(_ => _.PreferredName)
                .MaximumLength(150)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.FullNameNameMaxLength, 150));
        }

        protected override void AddDuplicateAddressTypesRules()
        {
            this.RuleFor(_ => _.Address)
                .Must(NoDuplicateAddressTypes)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.DuplicateAddressTypes));
        }
    }
}