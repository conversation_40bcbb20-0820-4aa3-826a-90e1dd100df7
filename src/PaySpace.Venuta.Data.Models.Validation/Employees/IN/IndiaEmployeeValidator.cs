namespace PaySpace.Venuta.Data.Models.Validation.Employees.IN
{
    using System;

    using FluentValidation;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.IN)]
    public class IndiaEmployeeValidator : EmployeeValidator
    {
        private const double MinAgeAllowed = 365.25 * 14;

        public IndiaEmployeeValidator(
            ApplicationContext applicationContext,
            IStringLocalizer<EmployeeValidator> employeeLocalizer,
            IStringLocalizer<AddressValidator> addressLocalizer,
            IStringLocalizer<EmployeeEmploymentStatus> statusLocalizer,
            IStringLocalizer<EmployeePayRate> payRateLocalizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICompanyService companyService,
            ICompanySettingService companySettingService,
            IEmployeeService employeeService,
            IEmployeeProfileService employeeProfileService,
            IEmployeeAddressService employeeAddressService,
            IEmploymentStatusService employmentStatusService,
            IUserService userService,
            IEmployeeHistoryService employeeHistoryService,
            ICompanyFrequencyService frequencyService,
            ICountryServiceFactory countryServiceFactory,
            IGenderService genderService,
            IUserRegionService userRegionService)
            : base(
                applicationContext,
                employeeLocalizer,
                addressLocalizer,
                statusLocalizer,
                payRateLocalizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                companyService,
                companySettingService,
                employeeService,
                employeeProfileService,
                employeeAddressService,
                employmentStatusService,
                userService,
                employeeHistoryService,
                frequencyService,
                countryServiceFactory,
                genderService,
                userRegionService)
        {
        }

        protected override void GeneralRules()
        {
            base.GeneralRules();
            this.AddMinimumAge();
        }

        protected override void AddPreferredNameRules()
        {
            this.RuleFor(_ => _.PreferredName)
                .NotEmpty()
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.FullNameRequired))
                .MaximumLength(150)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.FullNameNameMaxLength, 150));
        }

        protected override void ValidationInitials()
        {
            // Initials are not required for India
        }

        private void AddMinimumAge()
        {
            // Add validation for Birth Date to ensure the employee is at least 14 years old
            this.RuleFor(_ => _.Birthday)
                .Must(birthDate => (DateTime.Now - birthDate).TotalDays >= MinAgeAllowed)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.InvalidMinimumAge));
        }
    }
}