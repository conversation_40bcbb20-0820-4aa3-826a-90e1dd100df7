namespace PaySpace.Venuta.Data.Models.Validation.Employees.IN
{
    using System.Text.RegularExpressions;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.IN)]
    public partial class IndiaEmploymentStatusValidator : EmploymentStatusValidator
    {
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly ITenantProvider tenantProvider;

        public IndiaEmploymentStatusValidator(
            IStringLocalizer<EmployeeEmploymentStatus> Localizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICountryTaxYearService countryTaxYearService,
            ICompanyService companyService,
            ICompanyComponentService companyComponentService,
            ICompanySettingService companySettingService,
            ICompanyRunService companyRunService,
            ICompanyJobManagementService companyJobManagementService,
            IEmployeeService employeeService,
            IEmployeeTotalsHeaderService employeeTotalsHeaderService,
            IEmployeePositionService employeePositionService,
            IEmploymentStatusService employmentStatusService,
            IPayRateService payRateService,
            ApplicationContext applicationContext,
            IEmployeeHistoryService employeeHistoryService,
            IEmployeeSuspensionService employeeSuspensionService,
            IEmployeeLeaveService employeeLeaveService,
            ICompanyFrequencyService companyFrequencyService,
            IBureauConfigService bureauConfigService,
            ICountryServiceFactory countryServiceFactory,
            IModelMetadataProvider modelMetadataProvider,
            IGenderService genderService) : base(
                Localizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                countryTaxYearService,
                companyService,
                companyComponentService,
                companySettingService,
                companyRunService,
                companyJobManagementService,
                employeeService,
                employeeTotalsHeaderService,
                employeePositionService,
                employmentStatusService,
                payRateService,
                applicationContext,
                employeeHistoryService,
                employeeSuspensionService,
                employeeLeaveService,
                companyFrequencyService,
                bureauConfigService,
                countryServiceFactory,
                modelMetadataProvider,
                genderService)
        {
            this.employmentStatusService = employmentStatusService;
            this.tenantProvider = tenantProvider;

            this.RuleSet(RuleSetNames.CreateAndUpdate, () =>
            {
                this.RuleFor(_ => _.IdNumber)
                    .Length(12)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidIndiaIdLength))
                    .Matches("^[0-9]*$")
                    .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidAadharNumber));

                this.RuleFor(_ => _.TaxReferenceNumber)
                    .Length(10)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidTaxReferenceNumberLength))
                    .Must(this.ValidateTaxReferenceNumber)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidTaxReferenceNumber));

                this.RuleFor(_ => _.TaxReferenceNumber)
                    .NotEmpty()
                    .When(this.TaxReferenceNumberRequired)
                    .WithMessage(this.Localizer.GetString(SystemAreas.EmploymentStatus.LocaleStrings.TaxReferenceNumberRequired))
                    .WithErrorCode(ErrorCodes.EmploymentStatus.TaxReferenceNumberRequired);

                this.RuleFor(_ => _.TaxReferenceNumber)
                    .MustAsync(this.IsTaxReferenceNumberUniqueAsync)
                    .WithMessage("{ValidationMessage}");
            });
        }

        private bool ValidateTaxReferenceNumber(string taxReferenceNumber)
        {
            // Regex pattern: any 3 letters, followed by letter 'P' then any 4 digits lastly any letter
            return TAX_REF_REGEX().IsMatch(taxReferenceNumber);
        }

        private bool TaxReferenceNumberRequired(EmployeeEmploymentStatus model)
        {
            return !string.IsNullOrWhiteSpace(model.TaxReferenceNumber);
        }

        [GeneratedRegex(@"^[A-Z]{3}P[A-Z][0-9]{4}[A-Z]$", RegexOptions.Compiled)]
        private static partial Regex TAX_REF_REGEX();

        private async Task<bool> IsTaxReferenceNumberUniqueAsync(
            EmployeeEmploymentStatus model,
            string taxReferenceNumber,
            ValidationContext<EmployeeEmploymentStatus> context,
            CancellationToken cancellationToken)
        {
            // Retrieve the employee number associated with the tax reference number, excluding the current employee
            long? excludeId = model.EmploymentStatusId == 0
                ? null
                : model.EmploymentStatusId;

            var companyId = model.CompanyId == 0
                ? this.tenantProvider.GetCompanyId()!.Value
                : model.CompanyId;

            var existingEmployeeNumber = await this.employmentStatusService
                .GetEmployeeNumberByReferenceNumberAsync(
                    companyId,
                    taxReferenceNumber,
                    excludeId,
                    cancellationToken);

            if (string.IsNullOrWhiteSpace(existingEmployeeNumber))
            {
                return true; // No existing employee number found, validation passes
            }

            // If there is a tax ref number, show error message including the existing employee number
            context.MessageFormatter
                .AppendArgument("ValidationMessage",
                    this.Localizer.GetString(
                        SystemAreas.EmploymentStatus.LocaleStrings.TaxReferenceNumberAssigned,
                        existingEmployeeNumber));

            return false;
        }
    }
}
