namespace PaySpace.Venuta.Data.Models.Validation.Employees.CA
{
    using System.ComponentModel;

    using FluentValidation;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.CA)]
    [DisplayName(SystemAreas.EmploymentStatus.Area)]
    public class CanadaEmploymentStatusValidator : EmploymentStatusValidator
    {
        public CanadaEmploymentStatusValidator(
            IStringLocalizer<EmployeeEmploymentStatus> localizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICountryTaxYearService countryTaxYearService,
            ICompanyService companyService,
            ICompanyComponentService companyComponentService,
            ICompanySettingService companySettingService,
            ICompanyRunService companyRunService,
            ICompanyJobManagementService companyJobManagementService,
            IEmployeeService employeeService,
            IEmployeeTotalsHeaderService employeeTotalsHeaderService,
            IEmployeePositionService employeePositionService,
            IEmploymentStatusService employmentStatusService,
            IPayRateService payRateService,
            ApplicationContext applicationContext,
            IEmployeeHistoryService employeeHistoryService,
            IEmployeeSuspensionService employeeSuspensionService,
            IEmployeeLeaveService employeeLeaveService,
            ICompanyFrequencyService companyFrequencyService,
            IBureauConfigService bureauConfigService,
            ICountryServiceFactory countryServiceFactory,
            IModelMetadataProvider modelMetadataProvider,
            IGenderService genderService)
            : base(
                localizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                countryTaxYearService,
                companyService,
                companyComponentService,
                companySettingService,
                companyRunService,
                companyJobManagementService,
                employeeService,
                employeeTotalsHeaderService,
                employeePositionService,
                employmentStatusService,
                payRateService,
                applicationContext,
                employeeHistoryService,
                employeeSuspensionService,
                employeeLeaveService,
                companyFrequencyService,
                bureauConfigService,
                countryServiceFactory,
                modelMetadataProvider,
                genderService)
        {
            this.RuleSet(RuleSetNames.CreateAndUpdate, this.IdentificationRules);
        }

        private void IdentificationRules()
        {
            this.RuleFor(_ => _.IdNumber)
                .NotEmpty()
                .WithMessage(this.Localizer.GetString(SystemAreas.EmploymentStatus.LocaleStrings.SocialSecurityNumberRequired));

            this.RuleFor(_ => _.IdNumberExpiryDate)
                .NotEmpty()
                .When(this.IdNumberExpiryDateRequired)
                .WithMessage(this.Localizer.GetString(SystemAreas.EmploymentStatus.LocaleStrings.IdNumberExpiryDateRequired))
                .WithErrorCode(ErrorCodes.EmploymentStatus.IdNumberExpiryDateRequired);
        }

        private bool IdNumberExpiryDateRequired(EmployeeEmploymentStatus model)
        {
            return !string.IsNullOrWhiteSpace(model.IdNumber) && model.IdNumber.StartsWith(PaySpaceConstants.CanadaTempSinPrefix);
        }
    }
}