namespace PaySpace.Venuta.Data.Models.Validation.Employees.AU
{
    using System;

    using FluentValidation;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.AU)]
    public class AustraliaEmployeeValidator : EmployeeValidator
    {
        private const int MinNameLength = 1;
        private const int MaxNameLength = 40;
        private const int MinAgeAllowed = 10;
        private const int MaxAgeAllowed = 120;

        public AustraliaEmployeeValidator(
            ApplicationContext applicationContext,
            IStringLocalizer<EmployeeValidator> employeeLocalizer,
            IStringLocalizer<AddressValidator> addressLocalizer,
            IStringLocalizer<EmployeeEmploymentStatus> statusLocalizer,
            IStringLocalizer<EmployeePayRate> payRateLocalizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICompanyService companyService,
            ICompanySettingService companySettingService,
            IEmployeeService employeeService,
            IEmployeeProfileService employeeProfileService,
            IEmployeeAddressService employeeAddressService,
            IEmploymentStatusService employmentStatusService,
            IUserService userService,
            IEmployeeHistoryService employeeHistoryService,
            ICompanyFrequencyService frequencyService,
            ICountryServiceFactory countryServiceFactory,
            IGenderService genderService,
            IUserRegionService userRegionService) : base(
                applicationContext,
                employeeLocalizer,
                addressLocalizer,
                statusLocalizer,
                payRateLocalizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                companyService,
                companySettingService,
                employeeService,
                employeeProfileService,
                employeeAddressService,
                employmentStatusService,
                userService,
                employeeHistoryService,
                frequencyService,
                countryServiceFactory,
                genderService,
                userRegionService)
        {
        }

        protected override void GeneralRules()
        {
            base.GeneralRules();
            this.AddMinMaxLengthNameRule();
            this.DateOfBirthValidation();
        }

        private void AddMinMaxLengthNameRule()
        {
            this.RuleFor(_ => _.FirstName)
                .NotEmpty()
                .Must(this.IsNameLengthValid)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.InvalidNamesLengthError, MinNameLength, MaxNameLength));

            this.When(_ => !string.IsNullOrEmpty(_.EmpMiddleName), () =>
            {
                this.RuleFor(_ => _.EmpMiddleName)
                .MaximumLength(MaxNameLength)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.InvalidNamesLengthError, 0, MaxNameLength));
            });

            this.RuleFor(_ => _.LastName)
                .NotEmpty()
                .Must(this.IsNameLengthValid)
                .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.InvalidNamesLengthError, MinNameLength, MaxNameLength));
        }

        private bool IsNameLengthValid(string name) =>
            name != null &&
            name.Length >= MinNameLength &&
            name.Length <= MaxNameLength;

        private void DateOfBirthValidation()
        {
            var today = DateTime.Today;

            this.RuleFor(e => e.Birthday)
                .Cascade(CascadeMode.Stop)
                // Enforce minimum and maximum age restrictions
                .Must(dob => dob.AddYears(MinAgeAllowed) <= today)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.MinimumAgeError))
                .Must(dob => dob.AddYears(MaxAgeAllowed) >= today)
                    .WithMessage(this.Localizer.GetString(ErrorCodes.Employee.MaximumAgeError));
        }

        protected override void ValidationInitials()
        {
            // Initials are not required for Australia
        }
    }
}