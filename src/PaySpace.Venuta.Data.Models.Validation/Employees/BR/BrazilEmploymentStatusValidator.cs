namespace PaySpace.Venuta.Data.Models.Validation.Employees.BR
{
    using System.ComponentModel;

    using DocumentFormat.OpenXml.Vml.Office;
    using DocumentFormat.OpenXml.Wordprocessing;

    using FluentValidation;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Validation.ValidationHelpers;

    using static PaySpace.Venuta.Infrastructure.ErrorCodes;
    using static PaySpace.Venuta.Infrastructure.SystemAreas;

    [CountryService(CountryCode.BR)]
    [DisplayName(SystemAreas.EmploymentStatus.Area)]
    public class BrazilEmploymentStatusValidator : EmploymentStatusValidator
    {
        public BrazilEmploymentStatusValidator(
            IStringLocalizer<EmployeeEmploymentStatus> localizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICountryTaxYearService countryTaxYearService,
            ICompanyService companyService,
            ICompanyComponentService companyComponentService,
            ICompanySettingService companySettingService,
            ICompanyRunService companyRunService,
            ICompanyJobManagementService companyJobManagementService,
            IEmployeeService employeeService,
            IEmployeeTotalsHeaderService employeeTotalsHeaderService,
            IEmployeePositionService employeePositionService,
            IEmploymentStatusService employmentStatusService,
            IPayRateService payRateService,
            ApplicationContext applicationContext,
            IEmployeeHistoryService employeeHistoryService,
            IEmployeeSuspensionService employeeSuspensionService,
            IEmployeeLeaveService employeeLeaveService,
            ICompanyFrequencyService companyFrequencyService,
            IBureauConfigService bureauConfigService,
            ICountryServiceFactory countryServiceFactory,
            IModelMetadataProvider modelMetadataProvider,
            IGenderService genderService)
            : base(
                localizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                countryTaxYearService,
                companyService,
                companyComponentService,
                companySettingService,
                companyRunService,
                companyJobManagementService,
                employeeService,
                employeeTotalsHeaderService,
                employeePositionService,
                employmentStatusService,
                payRateService,
                applicationContext,
                employeeHistoryService,
                employeeSuspensionService,
                employeeLeaveService,
                companyFrequencyService,
                bureauConfigService,
                countryServiceFactory,
                modelMetadataProvider,
                genderService)
        {
        }

        protected override void ValidateTaxReferenceNumber()
        {
            this.RuleFor(r => r.TaxReferenceNumber)
                .Length(14)
                .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidTaxReferenceNumberLength));

            this.RuleFor(r => r.TaxReferenceNumber)
                .Must(BrazilValidationHelper.ValidateBrazilCpfNumber)
                .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidTaxReferenceNumber));
        }
    }
}