namespace PaySpace.Venuta.Data.Models.Validation.Employees.BR
{
    using System.ComponentModel;

    using FluentValidation;

    using Maddalena;

    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Validation.ValidationHelpers;

    [CountryService(CountryCode.BR)]
    [DisplayName(SystemAreas.BankDetail.Area)]
    public class BrazilEmployeeBankDetailValidator : EmployeeBankDetailValidator
    {
        public BrazilEmployeeBankDetailValidator(
            IStringLocalizer<EmployeeBankDetail> localizer,
            IHostEnvironment env,
            IEmployeeBankDetailService employeeBank<PERSON><PERSON>ilSer<PERSON>,
            IEmployeeBankHeaderService employeeBankHeaderService,
            ApplicationContext context,
            ITenantProvider tenantProvider,
            ICompanyService companyService)
            : base(
                  localizer,
                  env,
                  employeeBankDetailService,
                  employeeBankHeaderService,
                  context,
                  tenantProvider,
                  companyService)
        {
            this.RuleSet(RuleSetNames.Create, this.CreateRules);
            this.RuleSet(RuleSetNames.Update, this.UpdateRules);
            this.RuleSet(RuleSetNames.Delete, this.DeleteRules);
        }

        protected override void FieldChecks()
        {
            base.FieldChecks();

            this.RuleFor(_ => _.AgencyCheckDigit)
                .Matches("^[a-zA-Z0-9]")
                .When(_ => !string.IsNullOrEmpty(_.AccountCheckDigit))
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.AgencyCheckDigitMustBeSingleAlphaNumericCharacter));

            this.RuleFor(_ => _.AccountCheckDigit)
                .Matches("^[a-zA-Z0-9]")
                .When(_ => !string.IsNullOrEmpty(_.AccountCheckDigit))
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.AccountCheckDigitDigitMustBeSingleAlphaNumericCharacter));

            this.RuleFor(_ => _.CpfNumber)
                .Must(BrazilValidationHelper.ValidateBrazilCpfNumber)
                .When(_ => !string.IsNullOrEmpty(_.CpfNumber))
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.InvalidCpfNumber));

            this.RuleFor(_ => _.CnpjNumber)
                .Must(BrazilValidationHelper.ValidateBrazilCnpjNumber)
                .When(_ => !string.IsNullOrEmpty(_.CnpjNumber))
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.InvalidCnpjNumber));

            this.RuleFor(_ => _.CellphoneNumber)
                .Must(this.CheckTelephoneNumber)
                .When(_ => !string.IsNullOrEmpty(_.CellphoneNumber))
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.InvalidCellNumber));

            this.RuleFor(_ => _.EmailAddress)
                .Must(ValidationHelper.IsValidEmail)
                .When(_ => !string.IsNullOrEmpty(_.EmailAddress))
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.InvalidContactEmail));

            this.RuleFor(_ => _.RandomKey)
                .MaximumLength(50)
                .When(_ => !string.IsNullOrEmpty(_.RandomKey))
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.InvalidRandomKeyLength));
        }

        protected override void EftRequired()
        {
            base.EftRequired();

            this.RuleFor(_ => _.AgencyCheckDigit)
                .NotEmpty()
                .When(_ => _.EmployeeBankHeader.PaymentMethod == PaymentMethod.EFT)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.AgencyCheckDigitRequired));

            this.RuleFor(_ => _.AccountCheckDigit)
                .NotEmpty()
                .When(_ => _.EmployeeBankHeader.PaymentMethod == PaymentMethod.EFT)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.AccountCheckDigitDigitRequired));

            this.RuleFor(_ => _.BankingKey)
                .NotEmpty()
                .When(_ => _.EmployeeBankHeader.PaymentMethod == PaymentMethod.PIX)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.BankingKeyTypeRequired));

            this.RuleFor(_ => _.CpfNumber)
                .NotEmpty()
                .When(_ => _.BankingKey == BankingKeyType.CPF)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.CpfNumberRequired));

            this.RuleFor(_ => _.CnpjNumber)
                .NotEmpty()
                .When(_ => _.BankingKey == BankingKeyType.CNPJ)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.CnpjNumberRequired));

            this.RuleFor(_ => _.CellphoneNumber)
                .NotEmpty()
                .When(_ => _.BankingKey == BankingKeyType.Cellphone)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.CellphoneNumberRequired));

            this.RuleFor(_ => _.EmailAddress)
                .NotEmpty()
                .When(_ => _.BankingKey == BankingKeyType.Email)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.EmailAddressRequired));

            this.RuleFor(_ => _.RandomKey)
                .NotEmpty()
                .When(_ => _.BankingKey == BankingKeyType.Random)
                .WithMessage(this.Localizer.GetString(ErrorCodes.BankDetails.RandomKeyRequired));
        }

        private bool CheckTelephoneNumber(string workNumber)
        {
            return string.IsNullOrEmpty(workNumber) || ValidationHelper.IsTelephoneNumber(workNumber);
        }
    }
}