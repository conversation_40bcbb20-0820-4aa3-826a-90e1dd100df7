namespace PaySpace.Venuta.Data.Models.Validation.Employees.MY
{
    using System.ComponentModel;
    using System.Threading.Tasks;
    using System.Threading;

    using FluentValidation;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using System.Text.RegularExpressions;

    [CountryService(CountryCode.MY)]
    [DisplayName(SystemAreas.EmploymentStatus.Area)]
    public partial class MalaysiaEmploymentStatusValidator : EmploymentStatusValidator
    {
        private readonly IEmploymentStatusService employmentStatusService;

        [GeneratedRegex(@"^\d{12}$")]
        private static partial Regex IdNumberRegex();

        public MalaysiaEmploymentStatusValidator(
            IStringLocalizer<EmployeeEmploymentStatus> localizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICountryTaxYearService countryTaxYearService,
            ICompanyService companyService,
            ICompanyComponentService companyComponentService,
            ICompanySettingService companySettingService,
            ICompanyRunService companyRunService,
            ICompanyJobManagementService companyJobManagementService,
            IEmployeeService employeeService,
            IEmployeeTotalsHeaderService employeeTotalsHeaderService,
            IEmployeePositionService employeePositionService,
            IEmploymentStatusService employmentStatusService,
            IPayRateService payRateService,
            ApplicationContext applicationContext,
            IEmployeeHistoryService employeeHistoryService,
            IEmployeeSuspensionService employeeSuspensionService,
            IEmployeeLeaveService employeeLeaveService,
            ICompanyFrequencyService companyFrequencyService,
            IBureauConfigService bureauConfigService,
            ICountryServiceFactory countryServiceFactory,
            IModelMetadataProvider modelMetadataProvider,
            IGenderService genderService)
            : base(
                localizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                countryTaxYearService,
                companyService,
                companyComponentService,
                companySettingService,
                companyRunService,
                companyJobManagementService,
                employeeService,
                employeeTotalsHeaderService,
                employeePositionService,
                employmentStatusService,
                payRateService,
                applicationContext,
                employeeHistoryService,
                employeeSuspensionService,
                employeeLeaveService,
                companyFrequencyService,
                bureauConfigService,
                countryServiceFactory,
                modelMetadataProvider,
                genderService)
        {
            this.employmentStatusService = employmentStatusService;

            this.RuleSet(RuleSetNames.CreateAndUpdate, this.GeneralRules);
        }

        protected override void ValidateTaxReferenceNumber()
        {
            this.RuleFor(_ => _.TaxReferenceNumber)
                .Must(ValidationHelper.IsValidMalaysiaPcbNumber)
                .When(_ => !string.IsNullOrWhiteSpace(_.TaxReferenceNumber))
                .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidTaxReferenceNumber));
        }

        private void GeneralRules()
        {
            this.RuleFor(_ => _.IdNumber)
                .Must(_ => IdNumberRegex().IsMatch(_ ?? ""))
                .WhenAsync(this.NatureOfPersonCodeWithIdAsync)
                .WithMessage(_ => this.Localizer.GetString(ErrorCodes.EmploymentStatus.InvalidNumericValues, 12, this.Localizer.GetString($"lbl{nameof(_.IdNumber)}")));
        }

        private async Task<bool> NatureOfPersonCodeWithIdAsync(EmployeeEmploymentStatus status, CancellationToken cancellationToken = default)
        {
            var natureOfPersonCode = await this.employmentStatusService.GetNatureOfPersonCodeByIdAsync(status.NatureOfPersonId);
            return natureOfPersonCode != this.NoIDTaxCode;
        }
    }
}