namespace PaySpace.Venuta.Data.Models.Validation.Employees
{
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;

    public class EmployeeReviewDefaultRatersValidator : AbstractValidator<EmployeeReviewDefRaters>
    {
        private readonly IStringLocalizer<EmployeeReviewDefRaters> localizer;

        private readonly IEmployeePositionHierarchyService employeePositionHierarchyService;
        private readonly IDbContextRepository<EmployeeReviewDefRaters> repository;

        public EmployeeReviewDefaultRatersValidator(
            IStringLocalizer<EmployeeReviewDefRaters> localizer,
            IEmployeePositionHierarchyService employeePositionHierarchyService,
            IDbContextRepository<EmployeeReviewDefRaters> repository)
        {
            this.localizer = localizer;
            this.employeePositionHierarchyService = employeePositionHierarchyService;
            this.repository = repository;

            this.RuleSet(RuleSetNames.CreateAndUpdate, () =>
            {
                this.RuleFor(_ => _.DefRaterId)
                    .GreaterThan(-1)
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.DefRaterIdGreaterThanZero));

                this.RuleFor(_ => _.ProcessTypeId)
                    .NotEmpty()
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.ProcessTypeRequired));

                this.RuleFor(_ => _.ProcessTypeId)
                    .MustAsync(this.UniqueTemplatePerProcessTypeAsync)
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.ProcessTypeMoreThanOneDefaultTemplate));

                this.RuleFor(_ => _.RaterTypeId)
                    .NotEmpty()
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterTypeRequired));

                this.RuleFor(_ => _.ReviewDefaults.TemplateId)
                    .NotEmpty()
                    .When(_ => _.ReviewDefaults != null)
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.TemplateIdRequired));

                this.RuleFor(_ => _.ReviewDefaults.EmployeeId)
                    .NotEmpty()
                    .When(_ => _.ReviewDefaults != null)
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.EmployeeIdRequired));

                this.RuleFor(_ => _.RaterId)
                     .Must((model, raterId) => this.ValidateRaterId(model, raterId))
                     .WithMessage(this.GetRaterIdValidationMessage);

                this.RuleFor(_ => _.Weighting)
                    .NotNull()
                    .InclusiveBetween(0, 100)
                    .When(_ => _.IncludeScore)
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.WeightingValidationError));

                this.RuleFor(_ => _.RaterTypeId)
                    .MustAsync(this.UniqueRaterTypeAsync)
                    .When(_ => _.RaterTypeId is (int)RaterTypes.DirectReport or (int)RaterTypes.Manager or (int)RaterTypes.Self)
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterTypeAlreadyLinked));

                this.RuleFor(_ => _.RaterId)
                    .MustAsync(this.UniqueRaterEmployeeAsync)
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterIdAlreadyLinked));

                // Inherit Rater from Position Validation
                this.RuleFor(_ => _.ReviewDefaults.EmployeeId)
                    .MustAsync(this.ValidateInheritRaterFromPositionAsync)
                    .When(_ => _.InheritRaterFromPosition &&
                    (_.RaterTypeId == (int)RaterTypes.DirectReport || _.RaterTypeId == (int)RaterTypes.Manager))
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.MoreThanOneRaterSelectedInheritRaterFromPosition));

                //Position Matching Validation
                this.RuleFor(_ => _.RaterId)
                    .MustAsync(this.ValidatePositionMatchingAsync)
                    .When(_ => _.InheritRaterFromPosition &&
                    (_.RaterTypeId == (int)RaterTypes.DirectReport || _.RaterTypeId == (int)RaterTypes.Manager))
                    .WithMessage(this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberDoesNotMatchRaterTypeAndInheritSelection));

            });

        }

        private async Task<bool> ValidateInheritRaterFromPositionAsync(EmployeeReviewDefRaters model, long employeeId, CancellationToken cancellationToken)
        {
            //only validate if InheritRaterFromPosition is true and rater type is DirectReport or Manager
            if (!model.InheritRaterFromPosition ||
                (model.RaterTypeId != (int)RaterTypes.DirectReport && model.RaterTypeId != (int)RaterTypes.Manager))
            {
                return true;
            }

            var templateId = model.ReviewDefaults?.TemplateId ?? 0;

            // Check if theres another rater with InheritRaterFromPosition - true
            var existsInheritRater = await this.repository.Set
                .AnyAsync(_ => _.ReviewDefaults.EmployeeId == employeeId &&
                              _.ProcessTypeId == model.ProcessTypeId &&
                              _.ReviewDefaults.TemplateId == templateId &&
                              (_.RaterTypeId == (int)RaterTypes.DirectReport || _.RaterTypeId == (int)RaterTypes.Manager) &&
                              _.DefRaterId != model.DefRaterId, cancellationToken);

            return !existsInheritRater;
        }

        private bool ValidateRaterId(EmployeeReviewDefRaters model, long raterId)
        {
            return model.RaterTypeId switch
            {
                (int)RaterTypes.DirectReport when model.InheritRaterFromPosition =>
                    raterId >= 0,

                (int)RaterTypes.DirectReport when !model.InheritRaterFromPosition =>
                    raterId > 0,

                (int)RaterTypes.Manager when model.InheritRaterFromPosition =>
                    raterId >= 0,

                (int)RaterTypes.Manager when !model.InheritRaterFromPosition =>
                    raterId > 0,

                (int)RaterTypes.Self =>
                    raterId == 0 || raterId == model.ReviewDefaults?.EmployeeId,

                (int)RaterTypes.Peer => raterId > 0,
                (int)RaterTypes.Other => raterId > 0,

                _ => false
            };

        }

        private async Task<bool> UniqueRaterTypeAsync(EmployeeReviewDefRaters model, int raterTypeId, CancellationToken cancellationToken)
        {
            var exists = await this.repository.Set
                .Where(_ => _.ReviewDefaultsId == model.ReviewDefaultsId &&
                           _.ProcessTypeId == model.ProcessTypeId &&
                           _.RaterTypeId == raterTypeId &&
                           _.DefRaterId != model.DefRaterId)
                .AnyAsync(cancellationToken);
            return !exists;
        }

        private async Task<bool> UniqueRaterEmployeeAsync(EmployeeReviewDefRaters model, long raterId, CancellationToken cancellationToken)
        {
            var templateId = model.ReviewDefaults?.TemplateId ?? 0;
            var employeeId = model.ReviewDefaults?.EmployeeId ?? 0;

            var exists = await this.repository.Set
                .AnyAsync(_ => _.ReviewDefaults.EmployeeId == employeeId &&
                              _.ProcessTypeId == model.ProcessTypeId &&
                              _.ReviewDefaults.TemplateId == templateId &&
                              _.RaterId == raterId &&
                              _.DefRaterId != model.DefRaterId, cancellationToken);

            return !exists;
        }

        private async Task<bool> UniqueTemplatePerProcessTypeAsync(EmployeeReviewDefRaters model, long processTypeId, CancellationToken cancellationToken)
        {
            var currentTemplateId = model.ReviewDefaults?.TemplateId ?? 0;
            var employeeId = model.ReviewDefaults?.EmployeeId ?? 0;

            // Check if this employee already has this process type with another template
            var exists = await this.repository.Set
                .AnyAsync(_ => _.ReviewDefaults.EmployeeId == employeeId &&
                              _.ProcessTypeId == processTypeId &&
                              _.ReviewDefaults.Template.StatusId == (int)TemplateStatus.Active &&
                              _.ReviewDefaults.TemplateId != currentTemplateId, cancellationToken);

            return !exists;
        }

        private async Task<bool> ValidatePositionMatchingAsync(EmployeeReviewDefRaters model, long raterId, CancellationToken cancellationToken)
        {
            // validate only when InheritRaterFromPosition is true and we have a raterId specified
            if (!model.InheritRaterFromPosition || raterId <= 0)
            {
                return true;
            }

            return model.RaterTypeId switch
            {
                (int)RaterTypes.Manager => await this.ValidateManagerRaterAsync(model.ReviewDefaults.EmployeeId, raterId),
                (int)RaterTypes.DirectReport => await this.ValidateManagerRaterAsync(model.ReviewDefaults.EmployeeId, raterId),
                (int)RaterTypes.Self => model.ReviewDefaults.EmployeeId == raterId, // self rater must be the employee 
                (int)RaterTypes.Peer => true,
                (int)RaterTypes.Other => true,
                _ => false
            };
        }

        private async Task<bool> ValidateManagerRaterAsync(long employeeId, long raterId)
        {
            var directManagerId = await this.employeePositionHierarchyService.GetDirectManagerEmployeeId(employeeId);
            return directManagerId == raterId;
        }

        private string GetRaterIdValidationMessage(EmployeeReviewDefRaters model)
        {
            return model.RaterTypeId switch
            {
                (int)RaterTypes.DirectReport when model.InheritRaterFromPosition =>
                    this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberDoesNotMatchRaterTypeAndInheritSelection),

                (int)RaterTypes.DirectReport when !model.InheritRaterFromPosition =>
                    this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberRequired),

                (int)RaterTypes.Manager when model.InheritRaterFromPosition =>
                    this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberDoesNotMatchRaterTypeAndInheritSelection),

                (int)RaterTypes.Manager when !model.InheritRaterFromPosition =>
                    this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberRequired),

                (int)RaterTypes.Self =>
                    this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberDoesNotMatchRaterTypeSelection),

                (int)RaterTypes.Peer =>
                    this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberRequired),

                (int)RaterTypes.Other =>
                    this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.RaterEmpNumberRequired),

                _ => this.localizer.GetString(ErrorCodes.EmployeeReviewDefaultRaters.InvalidRaterTypeSelection)

            };
        }
    }
}
