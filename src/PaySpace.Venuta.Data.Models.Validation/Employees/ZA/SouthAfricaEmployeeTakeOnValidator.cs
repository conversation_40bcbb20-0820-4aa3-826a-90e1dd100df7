namespace PaySpace.Venuta.Data.Models.Validation.Employees.ZA
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Extensions;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeTakeOns.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.ZA)]
    public class SouthAfricaEmployeeTakeOnValidator : EmployeeTakeOnValidator
    {
        private static readonly string[] CompanyCarTaxCodes = new[]
        {
            ComponentTaxCodeConstants.TravelAllowance,
            ComponentTaxCodeConstants.CompanyCarBenefit,
            ComponentTaxCodeConstants.CompanyCarRentalBenefit,
            ComponentTaxCodeConstants.CarNote
        };

        private static readonly TaxCodeResult[] BargainingCouncilTaxCodes = new TaxCodeResult[]
        {
            new() { TaxCode = ComponentTaxCodeConstants.BargainingCouncilEmployerContribution, RequiredTaxCode = ComponentTaxCodeConstants.BargainingCouncilEmployerContributionFringeBenefit },
            new() { TaxCode = ComponentTaxCodeConstants.BargainingCouncilEmployerContributionFringeBenefit, RequiredTaxCode = ComponentTaxCodeConstants.BargainingCouncilEmployerContribution }
        };

        private static readonly TaxCodeResult[] RetirementTaxCodes = new TaxCodeResult[]
        {
            new() {
                TaxCode = ComponentTaxCodeConstants.PensionEmployerContribution,
                RequiredTaxCode = ComponentTaxCodeConstants.PensionFringeBenefit,
                RequiredForeginTaxCode = ComponentTaxCodeConstants.PensionFringeBenefitForeign },
            new() {
                TaxCode = ComponentTaxCodeConstants.PensionFringeBenefit,
                RequiredTaxCode = ComponentTaxCodeConstants.PensionEmployerContribution,
                RequiredForeginTaxCode = ComponentTaxCodeConstants.PensionFringeBenefitForeign },
            new() {
                TaxCode = ComponentTaxCodeConstants.ProvidentContribution,
                RequiredTaxCode = ComponentTaxCodeConstants.ProvidentFringeBenefit,
                RequiredForeginTaxCode = ComponentTaxCodeConstants.ProvidentFringeBenefitForeign },
            new() {
                TaxCode = ComponentTaxCodeConstants.ProvidentFringeBenefit,
                RequiredTaxCode = ComponentTaxCodeConstants.ProvidentContribution,
                RequiredForeginTaxCode = ComponentTaxCodeConstants.ProvidentFringeBenefitForeign },
            new() {
                TaxCode = ComponentTaxCodeConstants.RetirementAnnuityDeduction,
                RequiredTaxCode = ComponentTaxCodeConstants.RetirementAnnuityEmployer,
                RequiredForeginTaxCode = ComponentTaxCodeConstants.RetirementAnnuityEmployerForeign }
        };

        private static readonly MedicalAidTaxCodeResult[] MedicalAidTaxCodes = new MedicalAidTaxCodeResult[]
        {
            new() {
                TaxCode = ComponentTaxCodeConstants.MedicalAidFringeBenefit,
                RequiredTaxCode = ComponentTaxCodeConstants.MedicalAidDeemedDeductions,
                AdditonalRequiredTaxCode = ComponentTaxCodeConstants.MedicalAidEmployerContribution,
                RequiredSpecialComponentId = null },
            new() {
                TaxCode = ComponentTaxCodeConstants.MedicalExpensesTaxCredit,
                RequiredTaxCode = ComponentTaxCodeConstants.MedicalAidDeemedDeductions,
                AdditonalRequiredTaxCode = null,
                RequiredSpecialComponentId = ComponentTaxCodeConstants.SpecialComponentIdMedicalExpensesTaxCredit },
            new() {
                TaxCode = ComponentTaxCodeConstants.AdditionalMedicalExpensesTaxCredit,
                RequiredTaxCode = ComponentTaxCodeConstants.MedicalExpensesTaxCredit,
                AdditonalRequiredTaxCode = null,
                RequiredSpecialComponentId = ComponentTaxCodeConstants.SpecialComponentIdAdditionalMedicalExpensesTaxCredit }
        };

        private readonly IStringLocalizer localizer;
        private readonly IEmployeeTakeOnService employeeTakeOnService;
        private readonly IEmployeeService employeeService;
        private readonly ITenantProvider tenantProvider;
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly IEmployeeComponentService employeeComponentService;
        private readonly ICompanyComponentService componentCompanyService;

        public SouthAfricaEmployeeTakeOnValidator(
            IStringLocalizer<EmployeeTakeOn> localizer,
            IEmployeeTakeOnService employeeTakeOnService,
            IEmployeeService employeeService,
            ITenantProvider tenantProvider,
            IEmploymentStatusService employmentStatusService,
            IEmployeeComponentService employeeComponentService,
            ICompanyComponentService componentCompanyService)
            : base(localizer, employeeTakeOnService, employeeService, employmentStatusService)

        {
            this.localizer = localizer;
            this.employeeTakeOnService = employeeTakeOnService;
            this.employeeService = employeeService;
            this.tenantProvider = tenantProvider;
            this.employmentStatusService = employmentStatusService;
            this.employeeComponentService = employeeComponentService;
            this.componentCompanyService = componentCompanyService;
        }

        protected override void TaxCodeRules()
        {
            this.RuleFor(_ => _)
                .CustomAsync(this.ValidateTaxCodesAsync);
        }

        private async Task<IList<EmployeeTakeOnResult>> GetTakeOnResultsWithUpdatesAsync(IQueryable<EmployeeTakeOnResult> takeOnResultsQuery, ValidationContext<EmployeeTakeOn> context)
        {
            var takeOnResults = await takeOnResultsQuery.ToListAsync();

            // apply any updates to the results from database so that we validate against the current values
            var updatedComponentValues = context.GetTakeOnComponentUpdatedValues();
            if (updatedComponentValues != null && updatedComponentValues.Any())
            {
                foreach (var updatedComponentValue in updatedComponentValues)
                {
                    var takeOnResult = takeOnResults.FirstOrDefault(_ => _.ComponentCompanyId == updatedComponentValue.Key);
                    if (takeOnResult != null)
                    {
                        takeOnResult.ComponentValue = updatedComponentValue.Value;
                    }
                }
            }

            return takeOnResults;
        }

        private async Task<bool> ValidateTaxCodesAsync(EmployeeTakeOn employeeTakeOn, ValidationContext<EmployeeTakeOn> context, CancellationToken cancellationToken)
        {
            var companyId = this.tenantProvider.GetCompanyId();
            var frequencyId = this.tenantProvider.GetFrequencyId();

            if (companyId == null || frequencyId == null)
            {
                return true;
            }

            var (takeOnResults, currentTakeOnResult) = await this.GetTakeOnResultsAsync(employeeTakeOn, context, companyId, frequencyId);
            if (takeOnResults == null || currentTakeOnResult == null)
            {
                return true;
            }

            var errorMessages = this.ValidateCompanyCar(currentTakeOnResult, takeOnResults)
                .Union(await this.ValidateRetirementTaxCodesAsync(currentTakeOnResult, takeOnResults))
                .Union(await this.ValidateBargainingCouncilAsync(currentTakeOnResult, takeOnResults))
                .Union(this.ValidateMedicalFringeBenefit(currentTakeOnResult, takeOnResults))
                .Union(this.ValidateMedicalTaxCredit(currentTakeOnResult, takeOnResults))
                .Union(this.ValidateAdditionalMedicalTaxCredit(currentTakeOnResult, takeOnResults));

            foreach (var errorMessage in errorMessages)
            {
                context.AddFailure(errorMessage);
            }

            return !errorMessages.Any();
        }

        private async Task<(IList<EmployeeTakeOnResult>? TakeOnResults, EmployeeTakeOnResult? CurrentTakeOnResult)> GetTakeOnResultsAsync(
            EmployeeTakeOn employeeTakeOn,
            ValidationContext<EmployeeTakeOn> context,
            long? companyId,
            long? frequencyId)
        {
            var currentCompanyComponentId = context.GetTakeOnCompanyComponentId();
            if (currentCompanyComponentId.GetValueOrDefault() == 0)
            {
                // if component company id is not available try to get from persistance
                currentCompanyComponentId = this.employeeComponentService.GetComponentsByEmployeeId(employeeTakeOn.EmployeeId)
                    .Where(_ => _.ComponentId == employeeTakeOn.ComponentEmployeeId)
                    .Select(_ => _.ComponentCompanyId)
                    .FirstOrDefault();
                if (currentCompanyComponentId.GetValueOrDefault() == 0)
                {
                    return (null, null);
                }
            }

            var taxCodes = CompanyCarTaxCodes
                .Union(BargainingCouncilTaxCodes.Select(_ => _.TaxCode))
                .Union(BargainingCouncilTaxCodes.Select(_ => _.RequiredTaxCode))
                .Union(RetirementTaxCodes.Select(_ => _.TaxCode))
                .Union(RetirementTaxCodes.Select(_ => _.RequiredTaxCode))
                .Union(MedicalAidTaxCodes.Select(_ => _.TaxCode))
                .Union(MedicalAidTaxCodes.Select(_ => _.RequiredTaxCode))
                .Union(MedicalAidTaxCodes.Where(_ => _.AdditonalRequiredTaxCode != null).Select(_ => _.AdditonalRequiredTaxCode));

            var specialComponentCodes = MedicalAidTaxCodes.Where(_ => _.RequiredSpecialComponentId != null).Select(_ => _.RequiredSpecialComponentId);

            var takeOnRun = await this.employeeTakeOnService.GetLatestEmployeeTakeOnRunForComponentsByFrequencyAsync(frequencyId!.Value);
            var takeOnResultsQuery = this.employeeTakeOnService.GetEmployeeTakeOnResults(companyId!.Value, frequencyId.Value, takeOnRun.RunId, takeOnRun.PeriodStartDate, takeOnRun.PeriodEndDate)
               .Where(_ => _.EmployeeId == employeeTakeOn.EmployeeId)
               .Where(_ => _.ComponentCompanyId == currentCompanyComponentId || taxCodes.Contains(_.TaxCode) || specialComponentCodes.Contains(_.SpecialComponentType));
            var takeOnResults = await this.GetTakeOnResultsWithUpdatesAsync(takeOnResultsQuery, context);

            var currentTakeOnResult = takeOnResults.FirstOrDefault(_ => _.ComponentCompanyId == currentCompanyComponentId);
            if (currentTakeOnResult == null)
            {
                return (null, null);
            }
            else
            {
                // just update to make sure we are using the latest updated value
                currentTakeOnResult.ComponentValue = employeeTakeOn.ComponentValue;
            }

            return (takeOnResults, currentTakeOnResult);
        }

        private List<string> ValidateCompanyCar(EmployeeTakeOnResult currentTakeOnResult, IList<EmployeeTakeOnResult> takeOnResults)
        {
            var errorMessages = new List<string>();
            if (!CompanyCarTaxCodes.Contains(currentTakeOnResult.TaxCode))
            {
                return errorMessages;
            }

            if (currentTakeOnResult.TaxCode != ComponentTaxCodeConstants.CarNote && currentTakeOnResult.ComponentValue > 0)
            {
                // if a primary tax code has been updated, we must ensure the car note is also captured
                var carNoteValue = takeOnResults.FirstOrDefault(_ => _.TaxCode.Equals(ComponentTaxCodeConstants.CarNote, StringComparison.OrdinalIgnoreCase))?.ComponentValue ?? 0;
                if (carNoteValue == 0)
                {
                    errorMessages.Add(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.CarNoteRequired).Value);
                    return errorMessages;
                }
            }
            else if (currentTakeOnResult.TaxCode == ComponentTaxCodeConstants.CarNote && currentTakeOnResult.ComponentValue == 0)
            {
                // if a car note has been updated to zero and any primary tax code still has values then the update to 0 should not be allowed
                var primaryTaxCodes = CompanyCarTaxCodes.Where(_ => !_.Equals(ComponentTaxCodeConstants.CarNote, StringComparison.OrdinalIgnoreCase));
                if (takeOnResults.Where(_ => primaryTaxCodes.Contains(_.TaxCode)).Any(_ => _.ComponentValue > 0))
                {
                    errorMessages.Add(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.CarNoteRequired).Value);
                    return errorMessages;
                }
            }

            return errorMessages;
        }

        private async Task<IList<string>> ValidateRetirementTaxCodesAsync(EmployeeTakeOnResult currentTakeOnResult, IList<EmployeeTakeOnResult> takeOnResults)
        {
            var errorMessages = new List<string>();
            if (await this.employeeService.IsRetiredAsync(currentTakeOnResult.EmployeeId))
            {
                return errorMessages;
            }

            var applicableTaxCodes = RetirementTaxCodes.Where(_ => _.TaxCode == currentTakeOnResult.TaxCode || _.RequiredTaxCode == currentTakeOnResult.TaxCode);
            if (!applicableTaxCodes.Any())
            {
                return errorMessages;
            }

            var isForeignEmployee = await this.employmentStatusService.GetEmploymentStatusesByEmployeeId(currentTakeOnResult.EmployeeId)
                .OrderByDescending(_ => _.EmploymentDate)
                .Take(1)
                .AnyAsync(_ => _.TaxStatusId == (int)RSATaxStatus.ForeignEmployment);

            var currentCompanyComponentChildrenIds = (await this.componentCompanyService.GetChildComponents(currentTakeOnResult.ComponentCompanyId))
                .Select(c => c.ComponentCompanyId)
                .ToList();

            var parentComponentId = await this.componentCompanyService.GetParentComponentIdAsync(currentTakeOnResult.ComponentCompanyId);

            if (currentTakeOnResult.ComponentValue > 0)
            {
                // check if the updated tax code has required tax codes and if the required tax codes were also captured
                var requiredTaxes = applicableTaxCodes.Where(_ => _.TaxCode == currentTakeOnResult.TaxCode);
                foreach (var requiredTax in requiredTaxes)
                {
                    var childMatch = currentCompanyComponentChildrenIds.Count > 0
                        ? takeOnResults.FirstOrDefault(_ => _.TaxCode == requiredTax.RequiredTaxCode && currentCompanyComponentChildrenIds.Contains(_.ComponentCompanyId))
                        : null;

                    var parentMatch = parentComponentId.HasValue
                        ? takeOnResults.FirstOrDefault(_ => _.TaxCode == requiredTax.RequiredTaxCode && _.ComponentCompanyId == parentComponentId)
                        : null;

                    var defaultTaxCodeMatch = takeOnResults.FirstOrDefault(_ => _.TaxCode == requiredTax.RequiredTaxCode);

                    // First check for child matches, then parent matches, else default to just tax code matches
                    var requiredTaxCodeValue = childMatch?.ComponentValue ?? parentMatch?.ComponentValue ?? defaultTaxCodeMatch?.ComponentValue ?? 0;

                    if (requiredTaxCodeValue == 0)
                    {
                        // if its a foreign employee, we still validate on the same tax code but display the foreign tax code
                        var requiredDisplayTaxCode = isForeignEmployee ? requiredTax.RequiredForeginTaxCode : requiredTax.RequiredTaxCode;
                        errorMessages.Add(string.Format(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeRequired).Value, currentTakeOnResult.TaxCode, requiredDisplayTaxCode));
                    }
                }
            }
            else if (currentTakeOnResult.ComponentValue == 0 && takeOnResults.Where(_ => _.TaxCode == currentTakeOnResult.TaxCode).All(_ => _.ComponentValue == 0))
            {
                // There could be multiple records with the same tax code, and all of them must have a value of zero to show an error
                // validate other way around, if updated tax code is a required tax code it must not be updated to 0 when the primary tax code has a value
                var primaryTaxes = applicableTaxCodes.Where(_ => _.RequiredTaxCode == currentTakeOnResult.TaxCode);
                foreach (var primaryTax in primaryTaxes)
                {
                    var requiredTaxCodeValue = currentCompanyComponentChildrenIds.Count > 0 ?
                        takeOnResults.FirstOrDefault(_ => _.TaxCode == primaryTax.TaxCode && currentCompanyComponentChildrenIds.Contains(_.ComponentCompanyId))?.ComponentValue ?? 0
                        : parentComponentId.HasValue ? takeOnResults.FirstOrDefault(_ => _.TaxCode == primaryTax.TaxCode && _.ComponentCompanyId == parentComponentId)?.ComponentValue ?? 0
                            : takeOnResults.FirstOrDefault(_ => _.TaxCode == primaryTax.TaxCode)?.ComponentValue ?? 0;

                    if (requiredTaxCodeValue > 0)
                    {
                        // if its a foreign employee, we still validate on the same tax code but display the foreign tax code
                        var requiredDisplayTaxCode = isForeignEmployee ? primaryTax.RequiredForeginTaxCode : primaryTax.RequiredTaxCode;
                        errorMessages.Add(string.Format(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeRequired).Value, primaryTax.TaxCode, requiredDisplayTaxCode));
                    }
                }
            }

            return errorMessages;
        }

        private async Task<IList<string>> ValidateBargainingCouncilAsync(EmployeeTakeOnResult currentTakeOnResult, IList<EmployeeTakeOnResult> takeOnResults)
        {
            var errorMessages = new List<string>();

            var applicableTaxCodes = BargainingCouncilTaxCodes.Where(_ => _.TaxCode == currentTakeOnResult.TaxCode || _.RequiredTaxCode == currentTakeOnResult.TaxCode);
            if (!applicableTaxCodes.Any())
            {
                return errorMessages;
            }

            var currentCompanyComponentChildrenIds = (await this.componentCompanyService.GetChildComponents(currentTakeOnResult.ComponentCompanyId))
                .Select(_ => _.ComponentCompanyId)
                .ToList();

            var parentComponentId = await this.componentCompanyService.GetParentComponentIdAsync(currentTakeOnResult.ComponentCompanyId);

            if (currentTakeOnResult.ComponentValue > 0)
            {
                // check if the updated tax code has required related tax codes and if the related tax codes were also captured with valid values
                var taxCode = applicableTaxCodes.FirstOrDefault(_ => _.TaxCode == currentTakeOnResult.TaxCode);
                if (taxCode != null)
                {
                    return this.HasNoBargainingCouncilValues(takeOnResults, taxCode, currentCompanyComponentChildrenIds, parentComponentId);
                }
            }
            else if (currentTakeOnResult.ComponentValue == 0)
            {
                // validate other way around, updated tax code must not be updated to 0 when the required related tax code has a value
                var requiredTaxCode = applicableTaxCodes.FirstOrDefault(_ => _.RequiredTaxCode == currentTakeOnResult.TaxCode);
                if (requiredTaxCode != null)
                {
                    return this.HasBargainingCouncilValues(takeOnResults, requiredTaxCode, currentCompanyComponentChildrenIds, parentComponentId);
                }
            }

            return errorMessages;
        }

        private List<string> HasNoBargainingCouncilValues(IList<EmployeeTakeOnResult> takeOnResults, TaxCodeResult taxCode, List<long> currentCompanyComponentChildrenIds, long? parentComponentId)
        {
            var errorMessages = new List<string>();

            var parentComponentValue = parentComponentId.HasValue
                    ? takeOnResults.FirstOrDefault(_ => _.TaxCode == taxCode.RequiredTaxCode && _.ComponentCompanyId == parentComponentId)?.ComponentValue ?? 0
                    : takeOnResults.FirstOrDefault(_ => _.TaxCode == taxCode.RequiredTaxCode)?.ComponentValue ?? 0;

            var requiredTaxCodeValue = currentCompanyComponentChildrenIds.Count > 0
                ? takeOnResults.FirstOrDefault(_ => _.TaxCode == taxCode.RequiredTaxCode && currentCompanyComponentChildrenIds.Contains(_.ComponentCompanyId))?.ComponentValue ?? 0
                : parentComponentValue;

            if (requiredTaxCodeValue == 0)
            {
                if (taxCode.RequiredTaxCode == ComponentTaxCodeConstants.BargainingCouncilEmployerContribution)
                {
                    errorMessages.Add(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.BargainingEmployerContributionRequired).Value);
                    return errorMessages;
                }

                errorMessages.Add(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.BargainingFringeBenefitRequired).Value);
                return errorMessages;
            }

            return errorMessages;
        }

        private List<string> HasBargainingCouncilValues(IList<EmployeeTakeOnResult> takeOnResults, TaxCodeResult requiredTaxCode, List<long> currentCompanyComponentChildrenIds, long? parentComponentId)
        {
            var errorMessages = new List<string>();

            var parentComponentValue = parentComponentId.HasValue
                        ? takeOnResults.FirstOrDefault(_ => _.TaxCode == requiredTaxCode.TaxCode && _.ComponentCompanyId == parentComponentId)?.ComponentValue ?? 0
                        : takeOnResults.FirstOrDefault(_ => _.TaxCode == requiredTaxCode.TaxCode)?.ComponentValue ?? 0;

            var requiredTaxCodeValue = currentCompanyComponentChildrenIds.Count > 0
                    ? takeOnResults.FirstOrDefault(_ => _.TaxCode == requiredTaxCode.TaxCode && currentCompanyComponentChildrenIds.Contains(_.ComponentCompanyId))?.ComponentValue ?? 0
                    : parentComponentValue;

            if (requiredTaxCodeValue > 0)
            {
                if (requiredTaxCode.TaxCode == ComponentTaxCodeConstants.BargainingCouncilEmployerContribution)
                {
                    errorMessages.Add(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.BargainingFringeBenefitRequired).Value);
                    return errorMessages;
                }

                errorMessages.Add(this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.BargainingEmployerContributionRequired).Value);
                return errorMessages;
            }

            return errorMessages;
        }

        private List<string> ValidateMedicalFringeBenefit(EmployeeTakeOnResult currentTakeOnResult, IList<EmployeeTakeOnResult> takeOnResults)
        {
            var errorMessages = new List<string>();

            var applicableTaxCodes = MedicalAidTaxCodes.Where(_ => _.TaxCode == currentTakeOnResult.TaxCode
            || _.RequiredTaxCode == currentTakeOnResult.TaxCode
            || _.AdditonalRequiredTaxCode == currentTakeOnResult.TaxCode
            || _.RequiredSpecialComponentId == currentTakeOnResult.SpecialComponentType);

            if (!applicableTaxCodes.Any())
            {
                return errorMessages;
            }

            var taxCodeComponent = takeOnResults.Select(_ => new
            {
                _.TaxCode,
                _.AliasDescription,
                _.ComponentValue,
                _.SpecialComponentType
            }).FirstOrDefault(_ => _.TaxCode == ComponentTaxCodeConstants.MedicalAidFringeBenefit);

            var taxCodeValue = taxCodeComponent?.ComponentValue ?? 0;
            var deemedAndPriorMedicalsHaveNoValue = takeOnResults.Where(_ => _.TaxCode == ComponentTaxCodeConstants.MedicalAidDeemedDeductions).All(_ => _.ComponentValue == 0);
            var additionalRequiredTaxCodeValue = takeOnResults.FirstOrDefault(_ => _.TaxCode == ComponentTaxCodeConstants.MedicalAidEmployerContribution)?.ComponentValue ?? 0;

            if (taxCodeValue > 0)
            {
                if (deemedAndPriorMedicalsHaveNoValue && additionalRequiredTaxCodeValue == 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeAndAdditionalTaxCodeRequired).Value,
                        taxCodeComponent!.TaxCode,
                        ComponentTaxCodeConstants.MedicalAidDeemedDeductions,
                        ComponentTaxCodeConstants.MedicalAidEmployerContribution));
                }

                if (!deemedAndPriorMedicalsHaveNoValue && additionalRequiredTaxCodeValue == 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeBothEnteredOneRequired).Value,
                        taxCodeComponent!.TaxCode,
                        ComponentTaxCodeConstants.MedicalAidDeemedDeductions,
                        ComponentTaxCodeConstants.MedicalAidEmployerContribution));
                }

                if (deemedAndPriorMedicalsHaveNoValue && additionalRequiredTaxCodeValue > 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeBothEnteredOneRequired).Value,
                        taxCodeComponent!.TaxCode,
                        ComponentTaxCodeConstants.MedicalAidEmployerContribution,
                        ComponentTaxCodeConstants.MedicalAidDeemedDeductions));
                }
            }

            return errorMessages;
        }

        private List<string> ValidateMedicalTaxCredit(EmployeeTakeOnResult currentTakeOnResult, IList<EmployeeTakeOnResult> takeOnResults)
        {
            var errorMessages = new List<string>();

            var applicableTaxCodes = MedicalAidTaxCodes.Where(_ => _.TaxCode == currentTakeOnResult.TaxCode
            || _.RequiredTaxCode == currentTakeOnResult.TaxCode
            || _.AdditonalRequiredTaxCode == currentTakeOnResult.TaxCode
            || _.RequiredSpecialComponentId == currentTakeOnResult.SpecialComponentType);

            if (!applicableTaxCodes.Any())
            {
                return errorMessages;
            }

            var taxCodeComponent = takeOnResults.Select(_ => new
            {
                _.TaxCode,
                _.AliasDescription,
                _.ComponentValue,
                _.SpecialComponentType
            }).FirstOrDefault(_ => _.TaxCode == ComponentTaxCodeConstants.MedicalExpensesTaxCredit);

            var taxCodeValue = taxCodeComponent?.ComponentValue ?? 0;
            var deemedAndPriorMedicalsHaveNoValue = takeOnResults.Where(_ => _.TaxCode == ComponentTaxCodeConstants.MedicalAidDeemedDeductions).All(_ => _.ComponentValue == 0);
            var requiredSpecialComponent = takeOnResults.Select(_ => new
            {
                _.AliasDescription,
                _.ComponentValue,
                _.SpecialComponentType
            }).FirstOrDefault(_ => _.SpecialComponentType == ComponentTaxCodeConstants.SpecialComponentIdMedicalExpensesTaxCredit);

            var requiredSpecialComponentValue = requiredSpecialComponent?.ComponentValue ?? 0;

            if (taxCodeValue > 0)
            {
                if (deemedAndPriorMedicalsHaveNoValue && requiredSpecialComponentValue == 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeAndSpecialComponentRequired).Value,
                        taxCodeComponent!.TaxCode,
                        ComponentTaxCodeConstants.MedicalAidDeemedDeductions,
                        requiredSpecialComponent?.AliasDescription));
                }

                if (!deemedAndPriorMedicalsHaveNoValue && requiredSpecialComponentValue == 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.SpecialComponentRequired).Value,
                        taxCodeComponent!.TaxCode,
                        requiredSpecialComponent?.AliasDescription));
                }

                if (deemedAndPriorMedicalsHaveNoValue && requiredSpecialComponentValue > 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeRequired).Value,
                        taxCodeComponent!.TaxCode,
                        ComponentTaxCodeConstants.MedicalAidDeemedDeductions));
                }
            }

            return errorMessages;
        }

        private List<string> ValidateAdditionalMedicalTaxCredit(EmployeeTakeOnResult currentTakeOnResult, IList<EmployeeTakeOnResult> takeOnResults)
        {
            var errorMessages = new List<string>();

            var applicableTaxCodes = MedicalAidTaxCodes.Where(_ => _.TaxCode == currentTakeOnResult.TaxCode
            || _.RequiredTaxCode == currentTakeOnResult.TaxCode
            || _.AdditonalRequiredTaxCode == currentTakeOnResult.TaxCode
            || _.RequiredSpecialComponentId == currentTakeOnResult.SpecialComponentType);

            if (!applicableTaxCodes.Any())
            {
                return errorMessages;
            }

            var taxCodeComponent = takeOnResults.Select(_ => new
            {
                _.TaxCode,
                _.AliasDescription,
                _.ComponentValue,
                _.SpecialComponentType
            }).FirstOrDefault(_ => _.TaxCode == ComponentTaxCodeConstants.AdditionalMedicalExpensesTaxCredit);

            var taxCodeValue = taxCodeComponent?.ComponentValue ?? 0;
            var requiredTaxCodeValue = takeOnResults.FirstOrDefault(_ => _.TaxCode == ComponentTaxCodeConstants.MedicalExpensesTaxCredit)?.ComponentValue ?? 0;
            var requiredSpecialComponent = takeOnResults.Select(_ => new
            {
                _.AliasDescription,
                _.ComponentValue,
                _.SpecialComponentType
            }).FirstOrDefault(_ => _.SpecialComponentType == ComponentTaxCodeConstants.SpecialComponentIdAdditionalMedicalExpensesTaxCredit);

            var requiredSpecialComponentValue = requiredSpecialComponent?.ComponentValue ?? 0;

            if (taxCodeValue > 0)
            {
                if (requiredTaxCodeValue == 0 && requiredSpecialComponentValue == 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeAndSpecialComponentRequired).Value,
                        taxCodeComponent!.TaxCode,
                        ComponentTaxCodeConstants.MedicalExpensesTaxCredit,
                        requiredSpecialComponent?.AliasDescription));
                }

                if (requiredTaxCodeValue > 0 && requiredSpecialComponentValue == 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.SpecialComponentRequired).Value,
                        taxCodeComponent!.TaxCode,
                        requiredSpecialComponent?.AliasDescription));
                }

                if (requiredTaxCodeValue == 0 && requiredSpecialComponentValue > 0)
                {
                    errorMessages.Add(string.Format(
                        this.localizer.GetString(SystemAreas.YearToDateTakeOn.Keys.TaxCodeRequired).Value,
                        taxCodeComponent!.TaxCode,
                        ComponentTaxCodeConstants.MedicalExpensesTaxCredit));
                }
            }

            return errorMessages;
        }
    }

    public class TaxCodeResult
    {
        public string? TaxCode { get; set; }

        // if TaxCode is captured then this Tax Code is also required
        public string? RequiredTaxCode { get; set; }

        // if employee is foregn and RequiredTaxCode is not captured, we must show this TAXCODE in the validation message
        public string? RequiredForeginTaxCode { get; set; }
    }

    public class MedicalAidTaxCodeResult : TaxCodeResult
    {
        // if TaxCode is captured then this Tax Code is also required
        public string? AdditonalRequiredTaxCode { get; set; }

        public int? RequiredSpecialComponentId { get; set; }
    }
}