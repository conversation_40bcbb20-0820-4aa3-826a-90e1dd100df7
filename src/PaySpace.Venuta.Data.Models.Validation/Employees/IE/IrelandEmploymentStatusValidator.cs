namespace PaySpace.Venuta.Data.Models.Validation.Employees.IE
{
    using System.ComponentModel;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.IE)]
    [DisplayName(SystemAreas.EmploymentStatus.Area)]
    public class IrelandEmploymentStatusValidator : EmploymentStatusValidator
    {
        private readonly IEmploymentStatusService employmentStatusService;

        private const string IrelandPpsnPattern = "^\\d{7}[A-Za-z]{1,2}$";

        public IrelandEmploymentStatusValidator(
            IStringLocalizer<EmployeeEmploymentStatus> Localizer,
            IStringLocalizerFactory stringLocalizerFactory,
            ICustomFieldService customFieldService,
            ITenantProvider tenantProvider,
            ICountryTaxYearService countryTaxYearService,
            ICompanyService companyService,
            ICompanyComponentService companyComponentService,
            ICompanySettingService companySettingService,
            ICompanyRunService companyRunService,
            ICompanyJobManagementService companyJobManagementService,
            IEmployeeService employeeService,
            IEmployeeTotalsHeaderService employeeTotalsHeaderService,
            IEmployeePositionService employeePositionService,
            IEmploymentStatusService employmentStatusService,
            IPayRateService payRateService,
            ApplicationContext applicationContext,
            IEmployeeHistoryService employeeHistoryService,
            IEmployeeSuspensionService employeeSuspensionService,
            IEmployeeLeaveService employeeLeaveService,
            ICompanyFrequencyService companyFrequencyService,
            IBureauConfigService bureauConfigService,
            ICountryServiceFactory countryServiceFactory,
            IModelMetadataProvider modelMetadataProvider,
            IGenderService genderService)
            : base(
                Localizer,
                stringLocalizerFactory,
                customFieldService,
                tenantProvider,
                countryTaxYearService,
                companyService,
                companyComponentService,
                companySettingService,
                companyRunService,
                companyJobManagementService,
                employeeService,
                employeeTotalsHeaderService,
                employeePositionService,
                employmentStatusService,
                payRateService,
                applicationContext,
                employeeHistoryService,
                employeeSuspensionService,
                employeeLeaveService,
                companyFrequencyService,
                bureauConfigService,
                countryServiceFactory,
                modelMetadataProvider,
                genderService)
        {
            this.employmentStatusService = employmentStatusService;

            this.RuleSet(RuleSetNames.CreateAndUpdate, this.ValidateIdNumberAndEmploymentIdentifier);
        }

        public override void IdNumberFieldRules()
        {
            this.RuleFor(r => r.IdNumber)
                .Matches(IrelandPpsnPattern)
                .WhenAsync(this.IsCorrectIdentityTypeAsync)
                .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.IdNumberInvalid));
        }

        protected override async Task<bool> CheckIdentityTypeAgainstNatureOfPerson(EmployeeEmploymentStatus status, int? identityTypeId, CancellationToken cancellationToken = default)
        {
            if (identityTypeId.HasValue && identityTypeId != -1)
            {
                var natureOfPersonCode = await this.employmentStatusService.GetNatureOfPersonCodeByIdAsync(status.NatureOfPersonId);
                var identityCode = await this.employmentStatusService.GetIdentityTypeCodeByIdAsync(status.IdentityTypeId!.Value);

                return natureOfPersonCode switch
                {
                    "A" => identityCode == "ID",
                    _ => identityCode != "ID"
                };
            }

            return false;
        }

        protected override async Task<bool> DoNotCheckIdentityType(EmployeeEmploymentStatus status, CancellationToken cancellationToken = default)
        {
            return await this.employmentStatusService.GetNatureOfPersonCodeByIdAsync(status.NatureOfPersonId) == "B";
        }

        protected override async Task<bool> CheckIdentityNumber(EmployeeEmploymentStatus status, CancellationToken cancellationToken = default)
        {
            return !await this.DoNotCheckIdentityType(status, cancellationToken);
        }

        private void ValidateIdNumberAndEmploymentIdentifier()
        {
            this.ValidateEmploymentIdentifier();

            this.RuleFor(_ => _.IdNumber)
                .MustAsync(this.IsIdNumberUniqueAsync)
                .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.IdNumberPPSDuplicate));

            this.RuleFor(_ => _.EmploymentIdentifier)
                .MustAsync(this.AreIdNumberAndIdentifierUniqueAsync)
                .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.IdNumberAndIdentifierNotUnique));
        }

        private async Task<bool> IsCorrectIdentityTypeAsync(EmployeeEmploymentStatus model, CancellationToken cancellationToken)
        {
            var identityCode = await this.employmentStatusService.GetIdentityTypeCodeByIdAsync(model.IdentityTypeId!.Value);

            return identityCode == "ID";
        }

        private void ValidateEmploymentIdentifier()
        {
            // Validate EmploymentIdentifier is not empty only for nature of person "A"
            this.RuleFor(_ => _.EmploymentIdentifier)
                .NotEmpty()
                .WhenAsync(async (model, cancellationToken) =>
                {
                    var natureOfPersonCode = await this.employmentStatusService.GetNatureOfPersonCodeByIdAsync(model.NatureOfPersonId);
                    return natureOfPersonCode == "A";
                })
                .DependentRules(() =>
                {
                    this.RuleFor(_ => _.EmploymentIdentifier)
                        .MaximumLength(100)
                        .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.EmploymentIdentifierMaxLength));
                })
                .WithMessage(this.Localizer.GetString(ErrorCodes.EmploymentStatus.EmploymentIdentifierRequired));
        }

        private async Task<bool> IsIdNumberUniqueAsync(EmployeeEmploymentStatus status, string idNumber, CancellationToken cancellationToken)
        {
            return !await this.employmentStatusService.IsIdNumberUniqueAsync(
                status.EmployeeId,
                status.IdNumber);
        }

        private async Task<bool> AreIdNumberAndIdentifierUniqueAsync(EmployeeEmploymentStatus status, string? employmentIdentifier, CancellationToken cancellationToken)
        {
            return !await this.employmentStatusService.AreIdNumberAndIdentifierUniqueAsync(
                status.EmployeeId,
                status.IdNumber,
                status.EmploymentIdentifier,
                status.NatureOfPersonId);
        }
    }
}