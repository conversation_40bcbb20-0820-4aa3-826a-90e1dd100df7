namespace PaySpace.Venuta.Data.Models.Validation.Employees
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Mapster;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees.SG;
    using Microsoft.Extensions.Localization;
    using PaySpace.Venuta.Infrastructure;
    using System.ComponentModel.DataAnnotations;
    using MassTransit.Internals;
    using System.Reflection;
    using Microsoft.Azure.Documents.SystemFunctions;
    using Microsoft.AspNetCore.Mvc.ModelBinding;

    public class EmployeeAppendix8AValidator : AbstractValidator<EmployeeAppendix8A>
    {
        private readonly ISingaporeEmployeeAppendix8AService appendix8AService;
        private readonly IEmployeeService employeeService;
        private readonly IStringLocalizer localizer;
        private readonly IModelMetadataProvider modelMetadataProvider;

        public EmployeeAppendix8AValidator(
            ISingaporeEmployeeAppendix8AService appendix8AService,
            IEmployeeService employeeService,
            IStringLocalizer<EmployeeAppendix8A> localizer,
            IModelMetadataProvider modelMetadataProvider)
        {
            this.appendix8AService = appendix8AService;
            this.employeeService = employeeService;
            this.localizer = localizer;
            this.modelMetadataProvider = modelMetadataProvider;

            this.RuleSet(RuleSetNames.CreateAndUpdate, this.CreateAndUpdateRules);
            this.RuleSet(RuleSetNames.Delete, this.DeleteRules);
        }

        private void CreateAndUpdateRules()
        {
            // Address fields length
            this.RuleFor(_ => _.AddressLine1)
                .MaximumLength(30)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AddressLine1LengthError));

            this.RuleFor(_ => _.AddressLine2)
                .MaximumLength(30)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AddressLine2LengthError));

            this.RuleFor(_ => _.AddressLine3)
                .MaximumLength(30)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AddressLine3LengthError));

            // Occupation dates should be within tax year if provided, but NOT required per spec
            this.RuleFor(_ => _.OccupationPeriodStartDate)
                .MustAsync((model, date, context, cancellationToken) => this.BeWithinBasisYearAsync(model, date))
                .When(_ => _.OccupationPeriodStartDate.HasValue && _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.OccupationPeriodStartDateBasisYearError));

            this.RuleFor(_ => _.OccupationPeriodEndDate)
                .MustAsync((model, date, context, cancellationToken) => this.BeWithinBasisYearAsync(model, date))
                .When(_ => _.OccupationPeriodEndDate.HasValue && _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.OccupationPeriodEndDateBasisYearError))
                .Must((model, endDate) =>
                    !model.OccupationPeriodStartDate.HasValue || !endDate.HasValue ||
                    endDate.Value >= model.OccupationPeriodStartDate.Value)
                .When(_ => _.OccupationPeriodEndDate.HasValue && _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.OccupationPeriodEndDateBeforeStartError));

            this.RuleFor(_ => _.NumberOfEmployeesSharing)
                .Must(num => !num.HasValue || (num.Value >= 0 && num.Value <= 99))
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.NumberOfEmployeesSharingError));

            this.RuleFor(_ => _.AnnualValuePremises)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AnnualValuePremisesError));

            // Furniture & Fitting Option - only required if Annual Value is present AND residence accommodation
            this.RuleFor(_ => _.FurnitureFittingOptionID)
                .NotEmpty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer &&
                          _.AnnualValuePremises.HasValue && _.AnnualValuePremises.Value > 0)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.FurnitureFittingOptionRequiredError));

            // Value of Furniture & Fitting - should be calculated (residence accommodation only)
            this.RuleFor(_ => _.ValueFurnitureFitting)
                .Must((model, val) =>
                {
                    if (!model.AnnualValuePremises.HasValue || model.FurnitureFittingOption == null)
                    {
                        return true;
                    }
                    decimal? expected = null;
                    if (model.FurnitureFittingOption.FurnitureFittingOptionCode == "P")
                    {
                        expected = 0.4m * model.AnnualValuePremises.Value;
                    }
                    else if (model.FurnitureFittingOption.FurnitureFittingOptionCode == "F")
                    {
                        expected = 0.5m * model.AnnualValuePremises.Value;
                    }

                    return !expected.HasValue || (val.HasValue && Math.Abs(val.Value - expected.Value) < 0.01m);
                })
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.ValueFurnitureFittingMismatchError));

            // Conditional validation: Only apply these rules for Residence accommodation type
            // Reverse mutual exclusivity: Cannot have Annual Value when Rent is populated
            this.RuleFor(_ => _.RentPaidToLandlord)
                .Must((model, rentPaid) =>
                    !rentPaid.HasValue || rentPaid.Value == 0 ||
                    !model.AnnualValuePremises.HasValue || model.AnnualValuePremises.Value == 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AccommodationMutualExclusivityRentError));

            this.RuleFor(_ => _.AnnualValuePremises)
                .Must((model, annualValue) =>
                    !annualValue.HasValue || annualValue.Value == 0 ||
                    !model.RentPaidToLandlord.HasValue || model.RentPaidToLandlord.Value == 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AccommodationMutualExclusivityAnnualValueError));

            // Rent paid to landlord including rental of Furniture & Fittings - required if AV is null (only for Residence type)
            this.RuleFor(_ => _.RentPaidToLandlord)
                .NotNull()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer &&
                          (!_.AnnualValuePremises.HasValue || _.AnnualValuePremises.Value == 0))
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.RentPaidToLandlordRequiredError));

            this.RuleFor(_ => _.TaxableValuePlaceOfResidence)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.TaxableValuePlaceOfResidenceError));

            this.RuleFor(_ => _.RentPaidByEmployee)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.RentPaidByEmployeeError));

            this.RuleFor(_ => _.TotalTaxableValuePlaceOfResidence)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.TotalTaxableValuePlaceOfResidenceError));

            this.RuleFor(_ => _.UtilitiesCosts)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.UtilitiesCostsError));
            this.RuleFor(_ => _.DriverCosts)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.DriverCostsError));

            this.RuleFor(_ => _.ServantCosts)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.ServantCostsError));

            // Hotel accommodation specific validations
            this.RuleFor(_ => _.CostHotelAccommodation)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.CostHotelAccommodationError));

            this.RuleFor(_ => _.HotelAmountPaidByEmployee)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.HotelAmountPaidByEmployeeError));

            this.RuleFor(_ => _.TaxableValueHotelAccommodation)
                .Must(_ => !_.HasValue || _.Value >= 0)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.TaxableValueHotelAccommodationError));

            this.RuleFor(_ => _.YearEndAccommodationTypeId)
                .NotEmpty()
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.AccommodationTypeRequiredError));

            // Separate validation for each accommodation type with specific messages
            this.RuleFor(_ => _)
                .MustAsync(this.ValidateResidenceProvidedByEmployerAccommodationCountAsync)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.ResidenceProvidedByEmployerCountError));

            this.RuleFor(_ => _)
                .MustAsync(this.ValidateHotelAccommodationCountAsync)
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.HotelAccommodationCountError));

            this.RuleFor(_ => _)
                .MustAsync(this.NotExceed11RecordsAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.Max11RecordsPerYearError));

            this.RuleFor(_ => _.TaxYearId)
                .MustAsync(this.IsAbleToEditOrCreateForTaxYearAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.Only5TaxYearsAllowedError));

            this.RuleFor(_ => _.AddressLine1)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.AddressLine1)));

            this.RuleFor(_ => _.AddressLine2)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.AddressLine2)));

            this.RuleFor(_ => _.AddressLine3)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.AddressLine3)));

            this.RuleFor(_ => _.OccupationPeriodStartDate)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.OccupationPeriodStartDate)));

            this.RuleFor(_ => _.OccupationPeriodEndDate)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.OccupationPeriodEndDate)));

            this.RuleFor(_ => _.NumberOfEmployeesSharing)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.NumberOfEmployeesSharing)));

            this.RuleFor(_ => _.AnnualValuePremises)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.AnnualValuePremises)));

            this.RuleFor(_ => _.FurnitureFittingOptionID)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.FurnitureFittingOptionID)));

            this.RuleFor(_ => _.RentPaidToLandlord)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.RentPaidToLandlord)));

            this.RuleFor(_ => _.UtilitiesCosts)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.UtilitiesCosts)));

            this.RuleFor(_ => _.DriverCosts)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.DriverCosts)));

            this.RuleFor(_ => _.ServantCosts)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
                .WithMessage(_ => this.GetResidenceValidationMessage(nameof(_.ServantCosts)));

            this.RuleFor(_ => _.CostHotelAccommodation)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(_ => this.GetHotelValidationMessage(nameof(_.CostHotelAccommodation)));

            this.RuleFor(_ => _.HotelAmountPaidByEmployee)
                .Empty()
                .When(_ => _.YearEndAccommodationTypeId == (int)YearEndAccommadationType.ResidenceProvidedByEmployer)
                .WithMessage(_ => this.GetHotelValidationMessage(nameof(_.HotelAmountPaidByEmployee)));
        }

        private void DeleteRules()
        {
            this.RuleFor(_ => _)
                .MustAsync(this.IsWithin5TaxYearsAsync)
                .WithMessage(this.localizer.GetString(SystemAreas.YearEndReporting.Keys.Only5TaxYearsAllowedError));
        }

        private async Task<bool> BeWithinBasisYearAsync(EmployeeAppendix8A model, DateTime? date)
        {
            if (!date.HasValue)
            {
                return true;
            }

            return await this.appendix8AService.WithinTaxYearAsync(model, date.Value);
        }

        private async Task<bool> IsAbleToEditOrCreateForTaxYearAsync(EmployeeAppendix8A model, int basisYearId, CancellationToken token)
        {
            var taxCountryId = await this.employeeService.GetTaxCountryIdAsync(model.EmployeeId);
            return await this.appendix8AService.CanEditOrAddRecordForTaxYearAsync(taxCountryId, basisYearId);
        }

        private async Task<bool> IsWithin5TaxYearsAsync(EmployeeAppendix8A model, CancellationToken cancellationToken)
        {
            var taxCountryId = await this.employeeService.GetTaxCountryIdAsync(model.EmployeeId);
            return await this.appendix8AService.CanEditOrAddRecordForTaxYearAsync(taxCountryId, model.TaxYearId);
        }

        private async Task<bool> ValidateResidenceProvidedByEmployerAccommodationCountAsync(EmployeeAppendix8A model, CancellationToken cancellationToken)
        {
            var existingCount = await this.appendix8AService.GetRecordCountByYearEndAccommadationTypeForAnEmployeeForATaxYearAsync(
                model.TaxYearId,
                model.EmployeeId,
                (int)YearEndAccommadationType.ResidenceProvidedByEmployer);

            // If updating existing record, don't count it as new
            if (model.EmployeeAppendix8AId != 0)
            {
                return existingCount <= SingaporeConstants.YearEndReporting.MaxResidencyProvidedByEmployerRecord;
            }

            // For new records, check if adding one more would exceed limits
            return existingCount < SingaporeConstants.YearEndReporting.MaxResidencyProvidedByEmployerRecord;
        }

        private async Task<bool> ValidateHotelAccommodationCountAsync(EmployeeAppendix8A model, CancellationToken cancellationToken)
        {
            var existingCount = await this.appendix8AService.GetRecordCountByYearEndAccommadationTypeForAnEmployeeForATaxYearAsync(
                model.TaxYearId,
                model.EmployeeId,
               (int)YearEndAccommadationType.HotelAccomodation);

            // If updating existing record, don't count it as new
            if (model.EmployeeAppendix8AId != 0)
            {
                return existingCount <= SingaporeConstants.YearEndReporting.MaxHotelAccomodationRecord;
            }

            // For new records, check if adding one more would exceed limits
            return existingCount < SingaporeConstants.YearEndReporting.MaxHotelAccomodationRecord;
        }

        private async Task<bool> NotExceed11RecordsAsync(EmployeeAppendix8A model, CancellationToken cancellationToken)
        {
            var count = await this.appendix8AService.GetRecordCountForAnEmployeeForATaxYearAsync(model.TaxYearId, model.EmployeeId);
            // Allow update for existing records (i.e., if updating, don't count this one as new)
            if (model.EmployeeAppendix8AId is not 0)
            {
                return count <= SingaporeConstants.YearEndReporting.Appendix8AMaxRecordsPerTaxYear;
            }
            return count < SingaporeConstants.YearEndReporting.Appendix8AMaxRecordsPerTaxYear;
        }

        private string GetResidenceValidationMessage(string propertyName)
        {
            return this.localizer.GetString(SystemAreas.YearEndReporting.Keys.ResidenceProvidedByEmployerValuesStipulationError, this.GetDisplayName(propertyName));
        }

        private string GetHotelValidationMessage(string propertyName)
        {
            return this.localizer.GetString(SystemAreas.YearEndReporting.Keys.HotelAccommodationValuesStipulationError, this.GetDisplayName(propertyName));
        }

        private string GetDisplayName(string propertyName)
        {
            var propertyMetadata = this.modelMetadataProvider.GetMetadataForProperty(typeof(EmployeeAppendix8A), propertyName);
            return propertyMetadata.GetDisplayName();
        }
    }
}
