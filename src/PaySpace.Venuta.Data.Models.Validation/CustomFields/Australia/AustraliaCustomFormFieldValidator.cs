namespace PaySpace.Venuta.Data.Models.Validation.CustomFields.Australia
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Validation.ValidationHelpers;

    using static PaySpace.Venuta.Infrastructure.ErrorCodes;

    [CountryService(CountryCode.AU)]
    internal class AustraliaCustomFormFieldValidator<TCustomField> : CustomFormFieldValidator<TCustomField>
        where TCustomField : CustomFieldValueBase, ICustomFormFieldValue, new()
    {

        private const string SuperannuationPrimarySplitInput = "Percentage";
        private static readonly string[] SuperannuationFieldCodes = { "PFSP", "SFSP", "PFS", "SFS" };

        private static readonly Dictionary<string, List<string>> trnNumberValidationDictionary = new()
        {
            { "TFD", new List<string>(){ "TFNN" } }
        };

        private static readonly Dictionary<string, List<string>> abNumberValidationDictionary = new()
        {
            { "TFD", new List<string>(){ "EABN" } },
            { "SUPER", new List<string>(){ "SABN" } }
        };

        private readonly Dictionary<string, List<string>> taxFileDeclarationValidationPairs = new()
        {
            { "ACT", new List<string> { "TFNP" } },
            { "PRS", new List<string> { "MLR" } },
            { "MCLSA", new List<string> { "MCLC", "MLRC" } },
            { "MCL", new List<string> { "MLRC" } },
            { "TFTC", new List<string> { "MCLC", "MLRC" } }
        };

        private readonly ICustomFormFieldService customFormFieldService;

        public AustraliaCustomFormFieldValidator(
            ICustomFieldService customFieldService,
            ICustomFormFieldService customFormFieldService,
            ICustomFieldValidatorHelper customFieldValidatorHelper)
            : base(customFieldService, customFormFieldService, customFieldValidatorHelper)
        {
            this.customFormFieldService = customFormFieldService;

            this.RuleSet(RuleSetNames.CreateAndUpdate, () =>
            {
                this.RuleForEach(_ => _)
                    .MustAsync(this.AustraliaCodeValidationAsync)
                    .WithMessage("{ValidationMessage}");

                this.RuleForEach(_ => _)
                    .MustAsync(this.AustraliaEmploymeeeTaxFileDeclarationValidation)
                    .WithMessage("{ValidationMessage}");

                this.RuleFor(_ => _).CustomAsync(this.ValidatePercentageSplitFundsAsync);
            });
        }

        public override Task<List<CustomFieldFormField>> GetCustomFieldFormFieldsAsync(long companyId, CustomFieldEntity<TCustomField> entity)
        {
            if (entity is ICustomForm customForm && (customForm.BureauCustomFormCategoryId > 0 || customForm.CompanyCustomFormCategoryId > 0))
            {
                var categoryId = customForm.BureauCustomFormCategoryId ?? customForm.CompanyCustomFormCategoryId;
                var customformType = categoryId == customForm.CompanyCustomFormCategoryId ? "C" : "B";
                return this.customFormFieldService.GetCustomFormFieldsAsync(companyId, categoryId!.Value, customformType);
            }

            return Task.FromResult(new List<CustomFieldFormField>(0));
        }

        private async Task<bool> AustraliaCodeValidationAsync(IList<TCustomField> fields, TCustomField customField, ValidationContext<IList<TCustomField>> validatorContext, CancellationToken cancellationToken)
        {
            var isValid = true;
            var customFormFields = await this.GetCustomFieldFormFieldsAsync(this.companyId, this.entity);
            if (customFormFields.Count == 0)
            {
                return true;
            }

            var field = customFormFields.FirstOrDefault(_ => _.CustomFieldId == customField.CustomFieldId && _.CustomFieldType == customField.CustomFieldType);

            if (field?.CollectionCode is not null && trnNumberValidationDictionary.ContainsKey(field.CollectionCode))
            {
                isValid = this.ValidateTfnNumber(field.CollectionCode, customField.FieldValue!, field.FieldCode, validatorContext); // add tp validation
            }

            if (isValid && field?.CollectionCode is not null && abNumberValidationDictionary.ContainsKey(field.CollectionCode))
            {
                isValid = this.ValidateAbnNumber(field.CollectionCode, customField.FieldValue!, field.FieldCode, validatorContext); // add ab validation
            }
            return isValid;
        }

        private bool ValidateTfnNumber(string collectionCode, string fieldValue, string fieldCode, ValidationContext<IList<TCustomField>> validatorContext)
        {
            trnNumberValidationDictionary.TryGetValue(collectionCode, out var value);

            if (value != null && !string.IsNullOrEmpty(fieldValue) && value.Contains(fieldCode))
            {
                //remove non numeric characters
                fieldValue = new string(fieldValue.Where(char.IsDigit).ToArray());

                if (fieldValue.Length != 9)
                {
                    validatorContext.MessageFormatter.AppendArgument("ValidationMessage", this.localizer?.GetString(CustomField.InvalidTfnNumberLength));
                    return false;
                }

                if (!AustraliaValidationHelper.ValidateTfnNumber(fieldValue))
                {
                    validatorContext.MessageFormatter.AppendArgument("ValidationMessage", this.localizer?.GetString(CustomField.InvalidTfnNumber));
                    return false;
                }
            }

            return true;
        }

        private bool ValidateAbnNumber(string collectionCode, string fieldValue, string fieldCode, ValidationContext<IList<TCustomField>> validatorContext)
        {
            abNumberValidationDictionary.TryGetValue(collectionCode, out var value);

            if (value != null && !string.IsNullOrEmpty(fieldValue) && value.Contains(fieldCode))
            {
                //remove non numeric characters
                fieldValue = new string(fieldValue.Where(char.IsDigit).ToArray());
                if (fieldValue.Length != 11)
                {
                    validatorContext.MessageFormatter.AppendArgument("ValidationMessage", this.localizer?.GetString(CustomField.InvalidAbnNumberLength));
                    return false;
                }

                if (!AustraliaValidationHelper.ValidateAbnNumber(fieldValue))
                {
                    validatorContext.MessageFormatter.AppendArgument("ValidationMessage", this.localizer?.GetString(CustomField.InvalidAbnNumber));
                    return false;
                }
            }

            return true;
        }

        private async Task<bool> AustraliaEmploymeeeTaxFileDeclarationValidation(
            IList<TCustomField> fields,
            TCustomField customField,
            ValidationContext<IList<TCustomField>> validatorContext,
            CancellationToken cancellationToken)
        {
            var customFormFields = await this.GetCustomFieldFormFieldsAsync(this.companyId, this.entity);
            var targetField = customFormFields.FirstOrDefault(_ => _.CustomFieldId == customField.CustomFieldId);

            if (targetField is not null && this.taxFileDeclarationValidationPairs.TryGetValue(targetField.FieldCode, out var relatedFieldCodes))
            {
                switch (targetField.FieldCode)
                {
                    case "ACT" when customField.Code is "T":
                        return this.ValidateACTAsync(fields, validatorContext, customFormFields, relatedFieldCodes);

                    case "PRS" when customField.Code is "S":
                        return this.ValidatePRS(fields, validatorContext, customFormFields, relatedFieldCodes);

                    case "MCL" when customField.Code is "F":
                    case "MCLSA" when Convert.ToBoolean(customField.FieldValue):
                    case "TFTC" when !Convert.ToBoolean(customField.FieldValue):
                        return this.ValidateRelatedFieldWithBooleanValue(fields, validatorContext, customFormFields, relatedFieldCodes, this.localizer.GetString(targetField.FieldCode));

                    default:
                        return true;
                }
            }

            return true;
        }

        private bool ValidatePRS(IList<TCustomField> fields, ValidationContext<IList<TCustomField>> validatorContext, List<CustomFieldFormField> customFormFields, List<string> relatedFieldCodes)
        {
            var relatedFields = customFormFields.Where(_ => relatedFieldCodes.Contains(_.FieldCode)).ToList();

            foreach (var relatedField in relatedFields)
            {
                var relatedFieldValue = fields.FirstOrDefault(_ => _.CustomFieldId == relatedField.CustomFieldId);
                if (relatedFieldValue is not null && relatedFieldValue.Code is "SPOUSE" or "SPOUSECHILD")
                {
                    validatorContext.MessageFormatter.AppendArgument("ValidationMessage", this.localizer?.GetString(CustomField.InvalidPrsOptions));
                    return false;
                }
            }

            return true;
        }

        private bool ValidateRelatedFieldWithBooleanValue(
            IList<TCustomField> fields,
            ValidationContext<IList<TCustomField>> validatorContext,
            List<CustomFieldFormField> customFormFields,
            List<string> relatedFieldCodes,
            string message)
        {
            var relatedFields = customFormFields.Where(_ => relatedFieldCodes.Contains(_.FieldCode)).ToList();

            foreach (var relatedField in relatedFields)
            {
                var relatedFieldValue = fields.FirstOrDefault(_ => _.CustomFieldId == relatedField.CustomFieldId);
                if (relatedFieldValue is not null && Convert.ToBoolean(relatedFieldValue.FieldValue))
                {
                    validatorContext.MessageFormatter.AppendArgument("ValidationMessage", message);
                    return false;
                }
            }

            return true;
        }

        private bool ValidateACTAsync(
            IList<TCustomField> fields,
            ValidationContext<IList<TCustomField>> validatorContext,
            List<CustomFieldFormField> customFormFields,
            List<string> relatedFieldCodes)
        {
            foreach (var relatedField in customFormFields.Where(_ => relatedFieldCodes.Contains(_.FieldCode)))
            {
                var relatedFieldValue = fields.FirstOrDefault(_ => _.CustomFieldId == relatedField.CustomFieldId);
                if (relatedFieldValue is not null && !Convert.ToBoolean(relatedFieldValue.FieldValue))
                {
                    validatorContext.MessageFormatter.AppendArgument("ValidationMessage", this.localizer?.GetString(CustomField.InvalidActOptions));
                    return false;
                }
            }

            return true;
        }

        private async Task ValidatePercentageSplitFundsAsync(IList<TCustomField> fields, ValidationContext<IList<TCustomField>> validatorContext, CancellationToken cancellationToken)
        {
            var customFormFields = await this.GetCustomFieldFormFieldsAsync(this.companyId, this.entity);
            if (customFormFields.Count == 0)
            {
                return;
            }

            var superannuationFundFields = customFormFields.Where(f => SuperannuationFieldCodes.Contains(f.FieldCode)).ToDictionary(f => f.FieldCode, f => (long?)f.CustomFieldId);

            if (SuperannuationFieldCodes.Any(code => !superannuationFundFields.ContainsKey(code)))
            {
                return;
            }

            var PrimaryFundSplitInput = fields.FirstOrDefault(f => f.CustomFieldType == "B" && f.CustomFieldId == superannuationFundFields["PFS"])?.FieldValue;
            if (PrimaryFundSplitInput != SuperannuationPrimarySplitInput)
            {
                return;
            }

            if (!bool.TryParse(fields.FirstOrDefault(f => f.CustomFieldType == "B" && f.CustomFieldId == superannuationFundFields["SFS"])?.FieldValue, out var isSecondaryFundSplit))
            {
                return;
            }

            if (isSecondaryFundSplit)
            {
                if (!int.TryParse(fields.FirstOrDefault(f => f.CustomFieldType == "B" &&
                                                        f.CustomFieldId == superannuationFundFields["PFSP"])?.FieldValue,
                                                        out var primaryPercentage))
                {
                    return;
                }

                if (!int.TryParse(fields.FirstOrDefault(f => f.CustomFieldType == "B" &&
                                                        f.CustomFieldId == superannuationFundFields["SFSP"])?.FieldValue,
                                                        out var secondaryPercentage))
                {
                    return;
                }

                if (primaryPercentage + secondaryPercentage != 100)
                {
                    validatorContext.AddFailure(this.localizer.GetString(CustomField.InvalidSuperannuationFundSplit));
                }
            }
        }
    }
}