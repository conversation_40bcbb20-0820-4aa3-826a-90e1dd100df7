namespace PaySpace.Venuta.Services.Employees
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Services.Abstractions.Models;

    public class EmployeeInboxService : IEmployeeInboxService
    {
        private readonly ApplicationContext context;
        private readonly IMapper mapper;

        public EmployeeInboxService(ApplicationContext context, IMapper mapper)
        {
            this.context = context;
            this.mapper = mapper;
        }

        public Task<bool> HasPendingEntriesAsync(long employeeId)
        {
            return this.context.Set<EmployeeInbox>()
                .AnyAsync(_ => _.EmployeeId == employeeId);
        }

        [Obsolete]
        public Task<bool> HasPendingWorkflowEntry(long? employeeId, long userWorkflowStepId)
        {
            return this.context.Set<EmployeeInbox>()
                .AnyAsync(_ => _.EmployeeId == employeeId && _.UserWorkflowStepId == userWorkflowStepId);
        }

        public Task<bool> HasPendingWorkflowEntry(long? employeeId, long userWorkflowStepId, InboxEntryType inboxEntryType)
        {
            return this.context.Set<EmployeeInbox>()
                .AnyAsync(_ => _.EmployeeId == employeeId && _.UserWorkflowStepId == userWorkflowStepId && _.InboxEntryType == inboxEntryType);
        }

        public Task<EmployeeInboxStatusResult> GetEmployeeInboxStatusAsync(long employeeId, InboxEntryType inboxEntryType, long relatedPrimaryKeyId)
        {
            if (inboxEntryType == InboxEntryType.SimpleLeave)
            {
                return this.context.Set<EmployeeInbox>().AsNoTracking()
                    .TagWithSource()
                    .Where(_ => _.EmployeeId == employeeId && _.InboxEntryType == InboxEntryType.SimpleLeave && _.UserWorkflowStepId == relatedPrimaryKeyId)
                    .Select(_ => new EmployeeInboxStatusResult
                    {
                        InboxId = _.EmployeeInboxId,
                        RelatedPrimaryKey = relatedPrimaryKeyId,
                        InboxStatusId = _.InboxStatusId,
                        IsAssigned = _.IsAssigned,
                        Notes = _.Notes
                    })
                    .FirstOrDefaultAsync();
            }

            return this.context.Set<EmployeeInbox>().AsNoTracking()
                .TagWithSource()
                .Where(_ => _.EmployeeId == employeeId && _.InboxEntryType == inboxEntryType && _.UserWorkflowStep.RelatedPrimaryKey == relatedPrimaryKeyId)
                .Select(_ => new EmployeeInboxStatusResult
                {
                    InboxId = _.EmployeeInboxId,
                    RelatedPrimaryKey = relatedPrimaryKeyId,
                    InboxStatusId = _.InboxStatusId,
                    IsAssigned = _.IsAssigned,
                    Notes = _.Notes
                })
                .FirstOrDefaultAsync();
        }

        public Task<bool> HasOtherApproversAsync(long employeeId, InboxEntryType inboxEntryType, long relatedPrimaryKeyId)
        {
            if (inboxEntryType == InboxEntryType.SimpleLeave)
            {
                return this.context.Set<EmployeeInbox>().AsNoTracking()
                    .TagWithSource()
                    .AnyAsync(_ => _.EmployeeId != employeeId && _.InboxEntryType == InboxEntryType.SimpleLeave && _.UserWorkflowStepId == relatedPrimaryKeyId);
            }

            return this.context.Set<EmployeeInbox>().AsNoTracking()
                .TagWithSource()
                .AnyAsync(_ => _.EmployeeId != employeeId && _.InboxEntryType == inboxEntryType && _.UserWorkflowStep.RelatedPrimaryKey == relatedPrimaryKeyId);
        }

        public IQueryable<Data.Models.Employees.Inbox.EmployeeInbox> GetEmployeeMessages(long employeeId)
        {
            return this.context.Set<Data.Models.Employees.Inbox.EmployeeInbox>().AsNoTracking()
                .Where(_ => _.EmployeeId == employeeId)
                .OrderBy(_ => _.EffectiveDate)
                .Include(inbox => inbox.Employee)
                .Include(inbox => inbox.OBOEmployee)
                .Include(inbox => inbox.StartUser)
                .Include(inbox => ((Data.Models.Employees.Inbox.MultiUserMessage)inbox).RelatedEmployee)
                .Include(inbox => ((Data.Models.Employees.Inbox.UserMessage)inbox).User)
                    .ThenInclude(userMessage => userMessage.Employee)
                .Include(inbox => ((Data.Models.Employees.Inbox.ReviewMessage)inbox).Review)
                    .ThenInclude(reviewMessage => reviewMessage.EmployeeReviewTemplate)
                        .ThenInclude(reviewTemplate => reviewTemplate.CompanyReviewProcess)
                .Include(inbox => ((Data.Models.Employees.Inbox.SimpleLeaveMessage)inbox).LeaveAdjustment)
                    .ThenInclude(leaveAdjustment => leaveAdjustment.Employee)
                .Include(inbox => ((Data.Models.Employees.Inbox.AdvanceLeaveMessage)inbox).UserWorkflowStep)
                .ThenInclude(_ => _.AcceptedStep)
                .Include(inbox => ((Data.Models.Employees.Inbox.AdvanceLeaveMessage)inbox).UserWorkflowStep)
                    .ThenInclude(_ => _.LeaveAdjustment)
                .Include(inbox => ((Data.Models.Employees.Inbox.AdvanceLeaveMessage)inbox).UserWorkflowStep)
                    .ThenInclude(_ => _.RejectedStep)
                .Include(inbox => ((Data.Models.Employees.Inbox.ChangeRequestMessage)inbox).UserWorkflowStep)
                    .ThenInclude(_ => _.AcceptedStep)
                .Include(inbox => ((Data.Models.Employees.Inbox.ChangeRequestMessage)inbox).UserWorkflowStep)
                    .ThenInclude(_ => _.RejectedStep)
                .Include(inbox => ((Data.Models.Employees.Inbox.ClaimsMessage)inbox).UserWorkflowStep)
                    .ThenInclude(_ => _.AcceptedStep)
                .Include(inbox => ((Data.Models.Employees.Inbox.ClaimsMessage)inbox).UserWorkflowStep)
                    .ThenInclude(_ => _.RejectedStep);
        }

        public Task<EmployeeInbox> GetEvaluationDefaultMessageByProcessId(long employeeInboxId, long startedEmployeeId, long processId)
        {
            return this.context.Set<EmployeeInbox>().SingleOrDefaultAsync(_ =>
                _.InboxEntryType == InboxEntryType.EvaluationDefaults
                && _.EmployeeId == employeeInboxId
                && _.UserWorkflowStepId == processId
                && _.StartUserId == startedEmployeeId);
        }

        public async Task SendEvaluationDefaultsInbox(
            long employeeId,
            long? managerEmployeeId,
            long processId,
            bool isManagerSelfServiceActive,
            long? userId,
            string subject,
            string message)
        {
            var inboxEmployeeId = isManagerSelfServiceActive ? employeeId : managerEmployeeId ?? 0;

            var inboxEntry = await this.GetEvaluationDefaultMessageByProcessId(inboxEmployeeId, employeeId, processId);
            if (inboxEntry == null)
            {
                this.context.Add(new EmployeeInbox
                {
                    InboxEntryType = InboxEntryType.EvaluationDefaults,
                    EmployeeId = inboxEmployeeId,
                    UserId = userId,
                    UserWorkflowStepId = processId,
                    StartUserId = employeeId,
                    Subject = subject,
                    Message = message
                });
            }
            else
            {
                inboxEntry.EffectiveDate = DateTimeOffset.Now;
            }

            await this.context.SaveChangesAsync();
        }

        public async Task<DateTimeOffset?> GetSimpleLeaveActionDate(long relatedPrimaryKey)
        {
            var date = await this.context.Set<EmployeeInbox>()
                .Where(_ => _.InboxEntryType == InboxEntryType.SimpleLeave && _.UserWorkflowStepId == relatedPrimaryKey)
                .Select(_ => _.EffectiveDate)
                .SingleOrDefaultAsync();

            if (date == default)
            {
                return null;
            }

            return date;
        }

        public IQueryable<EmployeeName> GetCurrentEmployeesForWorkflow(WorkflowItem workflowItem, long relatedPrimaryKey)
        {
            if (workflowItem == WorkflowItem.SimpleLeave)
            {
                return this.context.Set<EmployeeInbox>()
                    .Where(_ => _.InboxEntryType == InboxEntryType.SimpleLeave && _.UserWorkflowStepId == relatedPrimaryKey)
                    .Select(_ => _.Employee)
                    .ProjectTo<EmployeeName>(this.mapper.ConfigurationProvider);
            }

            var inboxEntryType = this.GetInboxEntryType(workflowItem);
            return this.context.Set<EmployeeInbox>()
                   .Where(_ => _.InboxEntryType == inboxEntryType && _.UserWorkflowStep.WorkflowItem == workflowItem && _.UserWorkflowStep.RelatedPrimaryKey == relatedPrimaryKey)
                   .Select(_ => _.Employee)
                   .ProjectTo<EmployeeName>(this.mapper.ConfigurationProvider);
        }

        public async Task RemoveByWorkflowAsync(WorkflowItem workflowItem, long relatedPrimaryKey)
        {
            if (workflowItem == WorkflowItem.SimpleLeave)
            {
                var messages = await this.context.Set<EmployeeInbox>()
                    .Where(_ => _.InboxEntryType == InboxEntryType.SimpleLeave && _.UserWorkflowStepId == relatedPrimaryKey)
                    .ToListAsync();

                this.context.RemoveRange(messages);
                await this.context.SaveChangesAsync();
            }
            else
            {
                var inboxEntryType = this.GetInboxEntryType(workflowItem);
                var messages = await this.context.Set<EmployeeInbox>()
                    .Where(_ => _.InboxEntryType == inboxEntryType && _.UserWorkflowStep.WorkflowItem == workflowItem && _.UserWorkflowStep.RelatedPrimaryKey == relatedPrimaryKey)
                    .ToListAsync();

                this.context.RemoveRange(messages);
                await this.context.SaveChangesAsync();
            }
        }

        public async Task RemoveByTypeAsync(InboxEntryType entryType, long relatedPrimaryKey)
        {
            var message = await this.context.Set<EmployeeInbox>().SingleOrDefaultAsync(_ => _.InboxEntryType == entryType && _.UserWorkflowStepId == relatedPrimaryKey);
            if (message != null)
            {
                this.context.Remove(message);
                await this.context.SaveChangesAsync();
            }
        }

        public async Task RemoveMultiUserMessageAsync(long employeeId)
        {
            var messages = await this.context.Set<EmployeeInbox>()
                .Where(_ => _.InboxEntryType == InboxEntryType.MultiUserMessage && _.UserId == employeeId)
                .ToListAsync();

            this.context.RemoveRange(messages);
            await this.context.SaveChangesAsync();
        }

        public async Task<EmployeeInbox> AddAsync(EmployeeInbox entity)
        {
            this.context.Add(entity);
            await this.context.SaveChangesAsync();

            return entity;
        }

        public async Task DeleteAsync(long employeeId, long employeeInboxId)
        {
            var message = await this.context.Set<Data.Models.Employees.Inbox.EmployeeInbox>().SingleAsync(_ => _.EmployeeId == employeeId && _.EmployeeInboxId == employeeInboxId);
            this.context.Remove(message);

            await this.context.SaveChangesAsync();
        }

        public Task<bool> HasSimpleLeaveEntryForRelatedPrimaryKeyId(long? employeeId, long relatedPrimaryKeyId)
        {
            return this.context.Set<EmployeeInbox>()
                .AnyAsync(_ => _.InboxEntryType == InboxEntryType.SimpleLeave && _.UserWorkflowStepId == relatedPrimaryKeyId && _.EmployeeId == employeeId);
        }

        public async Task UpdateInboxStatusAsync(long employeeInboxId, InboxStatus inboxStatusId)
        {
            var message = await this.context.Set<EmployeeInbox>().FindAsync(employeeInboxId);
            message.InboxStatusId = inboxStatusId;
            await this.context.SaveChangesAsync();
        }

        public async Task UpdateInboxNotesAsync(long employeeInboxId, string notes)
        {
            var message = await this.context.Set<EmployeeInbox>().FindAsync(employeeInboxId);
            message.Notes = notes;
            await this.context.SaveChangesAsync();
        }

        public async Task UpdateInboxAssignmentAsync(long employeeInboxId, long relatedPrimaryKeyId)
        {
            var message = await this.context.Set<EmployeeInbox>().FindAsync(employeeInboxId);
            message.IsAssigned = true;

            // Remove existing inboxes other than the current users if this option is selected.
            if (message.IsAssigned)
            {
                await this.UnassignExistingInboxesAsync(employeeInboxId, relatedPrimaryKeyId, message.InboxEntryType);
            }

            await this.context.SaveChangesAsync();
        }

        private async Task UnassignExistingInboxesAsync(long employeeInboxId, long relatedPrimaryKeyId, InboxEntryType inboxEntryType)
        {
            if (inboxEntryType == InboxEntryType.SimpleLeave)
            {
                var messages = await this.context.Set<EmployeeInbox>()
                    .TagWithSource()
                    .Where(_ => _.EmployeeInboxId != employeeInboxId && _.InboxEntryType == inboxEntryType && _.UserWorkflowStepId == relatedPrimaryKeyId)
                    .ToListAsync();

                this.context.RemoveRange(messages);
            }
            else
            {
                var messages = await this.context.Set<EmployeeInbox>()
                    .TagWithSource()
                    .Where(_ => _.EmployeeInboxId != employeeInboxId && _.InboxEntryType == inboxEntryType && _.UserWorkflowStep.RelatedPrimaryKey == relatedPrimaryKeyId)
                    .ToListAsync();

                this.context.RemoveRange(messages);
            }
        }

        private InboxEntryType GetInboxEntryType(WorkflowItem workflowItem)
        {
            return workflowItem switch
            {
                WorkflowItem.Claims => InboxEntryType.Claims,
                WorkflowItem.ChangeRequest => InboxEntryType.ChangeRequest,
                WorkflowItem.Commissions => InboxEntryType.Commissions,
                WorkflowItem.Timesheet => InboxEntryType.Timesheet,
                WorkflowItem.SimpleLeave => InboxEntryType.SimpleLeave,
                WorkflowItem.Leave => InboxEntryType.AdvancedLeave,
                WorkflowItem.AdvancedLeave => InboxEntryType.AdvancedLeave,
                _ => InboxEntryType.AdvancedLeave
            };
        }
    }
}