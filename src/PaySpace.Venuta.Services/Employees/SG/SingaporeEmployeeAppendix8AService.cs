namespace PaySpace.Venuta.Services.Employees.SG
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Dynamic.Core;
    using System.Text;
    using System.Threading.Tasks;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Bureau;

    using static Dapper.SqlMapper;

    public interface ISingaporeEmployeeAppendix8AService : ISingaporeYearEndReportingService<EmployeeAppendix8A>
    {
        Task<int> GetRecordCountForAnEmployeeForATaxYearAsync(long taxYearId, long employeeId);
        Task<int> GetRecordCountByYearEndAccommadationTypeForAnEmployeeForATaxYearAsync(long taxYearId, long employeeId, int accommodationOptionId);
        Task<bool> WithinTaxYearAsync(EmployeeAppendix8A entity, DateTime dateToCheck);
        Task<bool> CanAddRecordAsync(int taxYearId, long employeeId);
    }

    [CountryService(CountryCode.SG)]
    public class SingaporeEmployeeAppendix8AService : SingaporeYearEndReportingService<EmployeeAppendix8A>, ISingaporeEmployeeAppendix8AService
    {
        private readonly IEmployeeService employeeService;
        private readonly ICountryTaxYearService countryTaxYearService;

        public SingaporeEmployeeAppendix8AService(
            IDbContextRepository<EmployeeAppendix8A> repository,
            IEmployeeService employeeService,
            ICountryTaxYearService countryTaxYearService)
            : base(repository)
        {
            this.employeeService = employeeService;
            this.countryTaxYearService = countryTaxYearService;
        }

        public override async Task<EmployeeAppendix8A> AddAsync(EmployeeAppendix8A entity)
        {
            var recordCount = await this.Repository.Set
                .Where(_ => _.TaxYearId == entity.TaxYearId && _.EmployeeId == entity.EmployeeId)
                .CountAsync();

            entity.AccommodationNumber = recordCount + 1;

            this.CalculateAllFields(entity);
            return await base.AddAsync(entity);
        }

        public override Task<EmployeeAppendix8A> UpdateAsync(EmployeeAppendix8A entity)
        {
            this.ClearFieldsBasedOnAccommodationType(entity);
            this.CalculateAllFields(entity);
            return base.UpdateAsync(entity);
        }

        public override async Task DeleteAsync(EmployeeAppendix8A entity)
        {
            await this.Repository.Set
                .Where(_ => _.EmployeeId == entity.EmployeeId
                    && _.TaxYearId == entity.TaxYearId
                    && _.AccommodationNumber > entity.AccommodationNumber)
                .ExecuteUpdateAsync(_ => _.SetProperty(c => c.AccommodationNumber, c => c.AccommodationNumber - 1));

            await base.DeleteAsync(entity);
        }

        public async Task<int> GetRecordCountForAnEmployeeForATaxYearAsync(long taxYearId, long employeeId)
        {
            return await this.Repository.Set
                .Where(_ => _.TaxYearId == taxYearId && _.EmployeeId == employeeId)
                .CountAsync();
        }

        public async Task<int> GetRecordCountByYearEndAccommadationTypeForAnEmployeeForATaxYearAsync(long taxYearId, long employeeId, int accommodationOptionId)
        {
            return await this.Repository.Set
                .Where(_ => _.TaxYearId == taxYearId && _.EmployeeId == employeeId && _.YearEndAccommodationTypeId == accommodationOptionId)
                .CountAsync();
        }

        public async Task<bool> WithinTaxYearAsync(EmployeeAppendix8A entity, DateTime dateToCheck)
        {
            var taxCountryId = await this.employeeService.GetTaxCountryIdAsync(entity.EmployeeId);
            var taxYear = await this.countryTaxYearService.GetCountryTaxYearAsync(entity.TaxYearId, taxCountryId);

            if (taxYear == null)
            {
                return false;
            }

            return dateToCheck >= taxYear.YearStartDate.Date && dateToCheck <= taxYear.YearEndDate.Date;
        }

        public async Task<bool> CanAddRecordAsync(int taxYearId, long employeeId)
        {
            return await this.Repository.Set
                .TagWithSource()
                .Where(_ => _.TaxYearId == taxYearId && _.EmployeeId == employeeId)
                .CountAsync() < SingaporeConstants.YearEndReporting.Appendix8AMaxRecordsPerTaxYear;
        }

        private void CalculateAllFields(EmployeeAppendix8A entity)
        {
            entity.NumberOfDays = this.CalculateNumberOfDays(entity.OccupationPeriodStartDate, entity.OccupationPeriodEndDate);
            entity.ValueFurnitureFitting = this.CalculateValueFurnitureFitting(entity.AnnualValuePremises, (FurnitureFittingOption?)entity.FurnitureFittingOptionID);
            entity.TaxableValuePlaceOfResidence = this.CalculateTaxableValuePlaceOfResidence(entity.AnnualValuePremises, entity.ValueFurnitureFitting, entity.RentPaidToLandlord);
            entity.TotalTaxableValuePlaceOfResidence = this.CalculateTotalTaxableValuePlaceOfResidence(entity.TaxableValuePlaceOfResidence, entity.RentPaidByEmployee);
            entity.TaxableValueUtilitiesHousekeeping = this.CalculateTaxableValueUtilitiesHousekeeping(entity.UtilitiesCosts, entity.DriverCosts, entity.ServantCosts);
            entity.TaxableValueHotelAccommodation = this.CalculateTaxableValueHotelAccommodation(entity.CostHotelAccommodation, entity.HotelAmountPaidByEmployee);
            entity.TotalAccommodationBenefit = this.CalculateTotalAccommodationBenefit(entity.TotalTaxableValuePlaceOfResidence, entity.TaxableValueUtilitiesHousekeeping, entity.TaxableValueHotelAccommodation);
        }

        private int? CalculateNumberOfDays(DateTime? start, DateTime? end)
        {
            if (start.HasValue && end.HasValue)
            {
                return (int)(end.Value.Date - start.Value.Date).TotalDays + 1;
            }

            return null;
        }

        private decimal? CalculateValueFurnitureFitting(decimal? annualValue, FurnitureFittingOption? option)
        {
            if (!annualValue.HasValue || !option.HasValue)
            {
                return null;
            }

            var factor = option switch
            {
                FurnitureFittingOption.P => 0.40m,
                FurnitureFittingOption.F => 0.50m,
                _ => 0m     // unknown
            };

            return factor > 0m
                ? Math.Round(annualValue.Value * factor, 2)
                : null;
        }

        private decimal? CalculateTaxableValuePlaceOfResidence(decimal? av, decimal? vff, decimal? rpll)
        {
            var annualValue = av ?? 0;
            var furnitureValue = vff ?? 0;
            var rentPaidLandlord = rpll ?? 0;

            if (annualValue > 0)
            {
                return annualValue + furnitureValue;
            }

            return rentPaidLandlord;
        }

        private decimal? CalculateTotalTaxableValuePlaceOfResidence(decimal? taxableResidence, decimal? rentPaidByEmployee)
        {
            var res = taxableResidence ?? 0;
            var rentPaid = rentPaidByEmployee ?? 0;
            return Math.Max(0, res - rentPaid);
        }

        private decimal? CalculateTaxableValueUtilitiesHousekeeping(decimal? util, decimal? driver, decimal? servant)
        {
            return (util ?? 0) + (driver ?? 0) + (servant ?? 0);
        }

        private decimal? CalculateTaxableValueHotelAccommodation(decimal? hotelCost, decimal? paidByEmployee)
        {
            return Math.Max(0, (hotelCost ?? 0) - (paidByEmployee ?? 0));
        }

        private decimal? CalculateTotalAccommodationBenefit(decimal? totalResidence, decimal? utilHouse, decimal? hotel)
        {
            return (totalResidence ?? 0) + (utilHouse ?? 0) + (hotel ?? 0);
        }

        private void ClearFieldsBasedOnAccommodationType(EmployeeAppendix8A entity)
        {
            if (entity.YearEndAccommodationTypeId == (int)YearEndAccommadationType.HotelAccomodation)
            {
                this.ClearResidenceFields(entity);
            }
            else
            {
                this.ClearHotelFields(entity);
            }
        }

        private void ClearResidenceFields(EmployeeAppendix8A entity)
        {
            entity.AddressLine1 = null;
            entity.AddressLine2 = null;
            entity.AddressLine3 = null;
            entity.OccupationPeriodStartDate = null;
            entity.OccupationPeriodEndDate = null;
            entity.NumberOfDays = null;
            entity.NumberOfEmployeesSharing = null;
            entity.AnnualValuePremises = null;
            entity.FurnitureFittingOptionID = null;
            entity.ValueFurnitureFitting = null;
            entity.RentPaidToLandlord = null;
            entity.TaxableValuePlaceOfResidence = null;
            entity.RentPaidByEmployee = null;
            entity.TotalTaxableValuePlaceOfResidence = null;
            entity.UtilitiesCosts = null;
            entity.DriverCosts = null;
            entity.ServantCosts = null;
            entity.TaxableValueUtilitiesHousekeeping = null;
        }

        private void ClearHotelFields(EmployeeAppendix8A entity)
        {
            entity.CostHotelAccommodation = null;
            entity.HotelAmountPaidByEmployee = null;
            entity.TaxableValueHotelAccommodation = null;
        }
    }
}

