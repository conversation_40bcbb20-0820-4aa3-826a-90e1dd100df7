namespace PaySpace.Venuta.Services.Company
{
    using System.Collections.Generic;
    using System.Linq;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;

    public interface ICompanyGlDetailsService : IGenericService<CompanyGLDetail>
    {
        IQueryable<CompanyGLDetail> GetForCompanyGl(long companyGlId, IEnumerable<long> componentIds);
    }

    public class CompanyGlDetailsService : GenericService<CompanyGLDetail>, ICompanyGlDetailsService
    {
        public CompanyGlDetailsService(
            IDbContextRepository<CompanyGLDetail> repository)
            : base(repository)
        {
        }

        public IQueryable<CompanyGLDetail> GetForCompanyGl(long companyGlId, IEnumerable<long> componentIds)
        {
            var query = this.Query()
                .Where(detail => detail.CompanyGLId == companyGlId);

            if (componentIds.Any())
            {
                query = query.Where(detail => componentIds.Contains(detail.CompanyComponentId));
            }

            return query;
        }
    }
}
