namespace PaySpace.Venuta.Services.Company
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Security;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Organization.Abstractions;
    using PaySpace.Venuta.Modules.SecurityRoles.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company.Helpers;
    using PaySpace.Venuta.Services.Employees;

    public class CompanyDefaultsService : ICompanyDefaultsService
    {
        private readonly IAgencyService agencyService;
        private readonly ICompanyLeaveSchemeService companyLeaveSchemeService;
        private readonly ICompanyService companyService;
        private readonly IOrganizationLevelService organizationLevelService;
        private readonly ICompanyOffenceService companyOffenceService;
        private readonly ISecurityGroupService securityGroupService;
        private readonly ICompanyBankNameService companyBankNameService;
        private readonly ICompanySettingsGenericService companySettingsGenericService;
        private readonly ICountryDefaultService countryDefaultService;
        private readonly IAddressService addressService;

        public CompanyDefaultsService(
            IAgencyService agencyService,
            ICompanyLeaveSchemeService companyLeaveSchemeService,
            ICompanyService companyService,
            IOrganizationLevelService organizationLevelService,
            ICompanyOffenceService companyOffenceService,
            ISecurityGroupService securityGroupService,
            ICompanyBankNameService companyBankNameService,
            ICompanySettingsGenericService companySettingsGenericService,
            ICountryDefaultService countryDefaultService,
            IAddressService addressService)
        {
            this.agencyService = agencyService;
            this.companyService = companyService;
            this.organizationLevelService = organizationLevelService;
            this.companyLeaveSchemeService = companyLeaveSchemeService;
            this.companyOffenceService = companyOffenceService;
            this.securityGroupService = securityGroupService;
            this.companyBankNameService = companyBankNameService;
            this.companySettingsGenericService = companySettingsGenericService;
            this.countryDefaultService = countryDefaultService;
            this.addressService = addressService;
        }

        public async Task AddCompanyDefaultsAsync(Company company)
        {
            await this.CreateCountrySpecificLeaveSchemeParameters(company.CompanyId);

            // By default, the Org Levels screen will now have two parent entries,
            // with the Org Units screen having a record linked to the "Company" Org Level
            var organizationLevels = AddCompanyDefaultHelper.GetOrganizationLevels(company.CompanyId, company.CompanyName);
            foreach (var orgLevel in organizationLevels)
            {
                await this.organizationLevelService.AddAsync(orgLevel);
            }

            // Add default company offence types
            await this.companyOffenceService.AddDefaultTypesAsync(company.CompanyId);

            // Add default company settings
            await this.companySettingsGenericService.AddDefaultSettingsAsync(company.CompanyId);

            await this.companyBankNameService.AddCompanyBankNamesAsync(company.CompanyId, company.TaxCountryId);

            await this.countryDefaultService.SetCountryDefaultSettingsAsync(company.CompanyId);
        }

        public async Task SetUserLinksAsync(Company company, long userId, UserType userType)
        {
            if (userType == UserType.Company)
            {
                await this.companyService.AddUserCompanyLinkAsync(company.CompanyId, userId);
            }

            if (userType == UserType.Agency)
            {
                await this.companyService.AddUserAgencyLinkAsync(company.CompanyId, userId);
            }
        }

        public async Task SetFeesPaymentMethodAsync(Company company, UserType userType, long agencyId)
        {
            if (company.TaxCountryId == (int)TaxCountry.SouthAfrica)
            {
                var allowManualPayment = await this.agencyService.HasAllowManualPaymentAsync(agencyId);
                if (!allowManualPayment || userType == UserType.Company)
                {
                    company.FeesPaymentMethodId = (int)FeesPaymentMethod.Debit;
                }

                company.FeesPaymentMethodId ??= (int)FeesPaymentMethod.Manual;
            }
            else
            {
                company.FeesPaymentMethodId = (int)FeesPaymentMethod.Manual;
            }
        }

        public async Task CreateDefaultEssRole(long companyId)
        {
            var securityGroup = new SecurityGroup
            {
                CompanyId = companyId,
                GroupName = "Employee self-service",
                GroupDescription = "Employee self-service",
                DefaultPermission = "D",
                RoleType = RoleType.Employee,
                SelfServiceUser = true
            };

            securityGroup = await this.securityGroupService.AddAsync(securityGroup);
            await this.securityGroupService.CreateDefaultEssRole(companyId, securityGroup.SecurityGroupId, securityGroup.GroupName);
        }

        public void SanitizeAddress(Company company) => this.addressService.Sanitize(company.Address.ToList());

        public void SetDefaultAddress(ICollection<Address> addresses) => this.addressService.SetDefaultValues(addresses);

        public virtual Company DefaultCompanyValues(Company company)
        {
            var needsDefaultPaymentMethod =
                !company.FeesPaymentMethodId.HasValue &&
                 company.TaxCountryId != (int)TaxCountry.SouthAfrica;

            if (needsDefaultPaymentMethod)
            {
                company.FeesPaymentMethodId = (int)FeesPaymentMethod.Manual;
            }

            return company;
        }

        private async Task CreateCountrySpecificLeaveSchemeParameters(long companyId)
        {
            var taxCountryCode = await this.companyService.GetTaxCountryCodeAsync(companyId);
            if (Enum.TryParse(taxCountryCode, out CountryCode countryCode))
            {
                // We use a switch for countryCode since the new company is not yet selected, thus does not belong to a country
                switch (countryCode)
                {
                    case CountryCode.AU:
                        await this.countryDefaultService.CreateAustralianDefaultLeaveSchemeParametersAsync(companyId);
                        break;

                    case CountryCode.ES:
                        var spainLeaveScheme = AddCompanyDefaultHelper.GetSpainLeaveSchemeParametersAsync(companyId);
                        await this.companyLeaveSchemeService.AddAsync(spainLeaveScheme);
                        break;

                    default:
                        var companyLeaveScheme = AddCompanyDefaultHelper.GetLeaveSchemeParameters(companyId);
                        await this.companyLeaveSchemeService.AddAsync(companyLeaveScheme);
                        break;
                }
            }
        }
    }
}