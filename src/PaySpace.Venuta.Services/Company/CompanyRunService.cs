namespace PaySpace.Venuta.Services.Company
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Linq;
    using System.Linq.Dynamic.Core;
    using System.Threading.Tasks;

    using Dapper;

    using Microsoft.Data.SqlClient;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Cache;
    using PaySpace.Calculation.Cache.Common.Enums;
    using PaySpace.Calculation.Cache.Common.Helpers;
    using PaySpace.Calculation.Cache.WebApi.Client.Interfaces;
    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Company.Result;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Abstractions.Models;

    public interface ICompanyRunService : IGenericService<CompanyRun>
    {
        IQueryable<CompanyRun> GetCompanyRuns(long frequencyId);

        IQueryable<CompanyRunForPayslipResult> GetEmployeeCompanyRuns(long frequencyId, long employeeId);

        IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, DateTime? effectiveDate);

        IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, RunStatus status);

        IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, string periodCode);

        IQueryable<CompanyRun> GetOpenOrFutureMainRuns(long frequencyId, DateTime? effectiveDate);

        IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, bool includeInterimRuns, bool includeFutureRuns = true);

        IQueryable<CompanyRun> GetLatestRun(long frequencyId);

        Task<List<long>> GetPayslipTakeOnRunsAsync(long frequencyId);

        Task<bool> FutureRunExistsAsync(long frequencyId, DateTime? effectiveDate);

        Task<long?> GetLatestRunIdAsync(long frequencyId);

        Task<long?> GetLatestRunIdWithCutOffDateAsync(long frequencyId);

        Task<long?> GetLatestOrFirstFutureRunIdAsync(long frequencyId, bool includeInterimRuns);

        Task<long?> GetEarlistOrFirstFutureRunIdAsync(long frequencyId, bool includeInterimRuns);

        Task<long?> GetCurrentRunIdAsync(long frequencyId);

        Task<long?> GetFirstFutureRunIdAsync(long frequencyId);

        Task<long> GetRunFrequencyIdAsync(long runId);

        long GetRunFrequencyId(long runId);

        Task<RunType> GetRunTypeAsync(long runId);

        Task<RunStatus> GetRunStatusAsync(long runId);

        Task<string> GetRunDescriptionAsync(long runId);

        Task<DateTime> GetPeriodEndDateAsync(long runId);

        DateTime GetPeriodEndDate(long runId);

        DateTime GetPeriodStartDate(long runId);

        Task<CompanyRunPeriodResult> GetRunPeriodAsync(long runId);

        Task<DateTime> GetEarliestPeriodEndDateAsync(long frequencyId);

        Task<CompanyRun> GetEffectiveRunAsync(long frequencyId, DateTime effectiveDate);

        Task<CompanyRunSummaryResult> GetRunSummaryAsync(long runId);

        CompanyRunSummaryResult GetRunSummary(long runId);

        Task<IList<CompanyRun>> GetAvailableRunsAsync(bool allowSelectRun, long companyId, long frequencyId);

        Task<long?> GetRunIdByRunValueCodeAsync(long companyId, long frequencyId, string runValueCode);

        Task<bool> IsCompanyRunAsync(long companyId, long frequencyId, long runId);

        long GetRunIdByRunValueCode(long companyId, long frequencyId, string runValueCode);

        Task<IEnumerable<CompanyRunCloudRoomItem>> GetCloudRoomRunsAsync(long companyId, long? runId = null);

        Task<string> GetFrequencyNameAndRunDescriptionByRunIdAsync(long runId);

        Task<RunStatus> GetEffectiveRunStatusAsync(long frequencyId, DateTime effectiveDate);

        Task<DateTime> GetEarliestPeriodStartDateAsync(long frequencyId);

        DateTime? GetEarliestPeriodEndDate(long frequencyId);

        Task<DateTime> GetEarliestPeriodStartDateForTaxYearAsync(long frequencyId, DateTime taxYearStartDate);

        Task<DateTime> GetEarliestPeriodStartDateForPeriodCodeAsync(long frequencyId, string periodCode);

        Task<CompanyRun> GetMainRunByEffectiveDateAsync(long frequencyId, DateTime effectiveDate);

        Task<bool> HasOpenOrFutureRunsWithinPeriodAsync(long companyId, string periodCode);

        Task<DateTime?> GetLatestCutOffDateAsync(long companyId, string periodCode);

        Task<List<CompanyRun>> GetClosestOpenPeriodRunsAsync(long frequencyId);

        Task<bool> IsEarliestRunInAnyFrequencyClosedAsync(long companyId);

        Task<bool> HasOpenRunsWithinDateRangeAsync(long companyId, DateTime startDate, DateTime endDate);

        Task<bool> HasClosedRunsAsync(long frequencyId);

        Task<long?> GetLatestClosedRunIdAsync(long frequencyId, bool includeInterimRuns);

        int GetFirstYearRun(long companyId);

        Task<bool> IsTakeOnRunPeriodAsync(long companyId, string periodCode);

        IQueryable<CompanyRun> GetGroupedCompanyRunsPerPeriod(long frequencyId, DateTime startDate, DateTime endDate, bool newPeriod);

        Task<bool> DoesPublicHolidayExistAsync(long companyId, DateTime publicHoliday);

        Task<CompanyRun> AddAsync(CompanyRun entity, bool isBureauRun = false);

        Task<CompanyRun> UpdateAsync(long companyId, CompanyRun entity, bool isBureauRun = false);

        Task DeleteAsync(long companyId, CompanyRun entity);

        Task<List<CompanyRun>> GetAllCompanyRunsAsync(long frequencyId);

        Task<List<CompanyRun>> GetAllGroupedCompanyRunsPerPeriodAsync(long frequencyId, DateTime startDate, DateTime endDate, bool newPeriod);

        Task<List<CompanyRunOrderNumberDataset>> GetRunOrderNumbersAsync(
            long frequencyId,
            DateTime? startDate,
            DateTime? endDate,
            UserType? userType,
            long? runId,
            bool isBureau = false,
            long? companyId = null,
            string periodCode = null);

        Task<string> GetPrevTaxPeriodCodeAsync(long companyId, long frequencyId, UserType userType);

        Task<bool> GetTaxErrorsAsync(long runId);

        Task<bool> HasEmployeeTotalsHeaderAsync(long runId);

        Task<(DateTime? StartDate, DateTime? EndDate)> GetPayCalendarStartAndEndDateAsync(long runId);

        Task<bool> IsSkipChecksAsync(long companyId, CompanyRun entity, bool isBureau);

        Task InvalidateCompanyRunCaches(long companyId, CompanyRun companyRun);

        Task TriggerCalculationAsync(CompanyRun entity);

        Task ProcessFutureRunsAsync(long companyId, CompanyRun entity, bool skipChecks);

        Task OpenNextRunAsync(long companyId, CompanyRun entity, bool skipChecks);

        Task<bool> HasImpactOnSubsequentRunsAsync(long companyId, CompanyRun run);

        Task<bool> IsAfterMainRunOrderAsync(CompanyRun run);

        Task<bool> ExistsOpenRunBeforeAsync(CompanyRun run);

        Task<bool> HasDateWithinClosedRunsAsync(long companyId, DateTime effectiveDate);

        Task<DateTime> GetEarliestOpenRunDateAsync(long companyId);

        IQueryable<long> GetOpenRuns(long frequencyId);

        Task<bool> HasEmployeeCustomFormAsync(long runId);
    }

    public class CompanyRunService : GenericService<CompanyRun>, ICompanyRunService
    {
        private readonly ICompanySettingService companySettingService;
        private readonly IDistributedCache distributedCache;
        private readonly IScopedCache scopedCache;
        private readonly ReadOnlyContext readOnlyContext;
        private readonly ICompanyService companyService;
        private readonly ICalculationWebApiClient calculationApiClient;
        private readonly ICalculationCacheWebApiClient calcCacheWebApiClient;
        private readonly ITenantProvider tenantProvider;
        private readonly ICompanyFrequencyService companyFrequencyService;
        private readonly IDapperRepository dapperRepository;

        public CompanyRunService(
            IDbContextRepository<CompanyRun> repository,
            ICompanySettingService companySettingService,
            IDistributedCache distributedCache,
            IScopedCache scopedCache,
            ReadOnlyContext readOnlyContext,
            ICompanyService companyService,
            ICalculationWebApiClient calculationApiClient,
            ICalculationCacheWebApiClient calcCacheWebApiClient,
            ITenantProvider tenantProvider,
            ICompanyFrequencyService companyFrequencyService,
            IDapperRepository dapperRepository)
            : base(repository)
        {
            this.companySettingService = companySettingService;
            this.distributedCache = distributedCache;
            this.scopedCache = scopedCache;
            this.readOnlyContext = readOnlyContext;
            this.companyService = companyService;
            this.calculationApiClient = calculationApiClient;
            this.calcCacheWebApiClient = calcCacheWebApiClient;
            this.tenantProvider = tenantProvider;
            this.companyFrequencyService = companyFrequencyService;
            this.dapperRepository = dapperRepository;
        }

        public IQueryable<CompanyRun> GetCompanyRuns(long frequencyId)
        {
            return this.Repository.Context.Set<CompanyRun>()
                .AsNoTracking()
                .TagWithSource()
                .Where(_ => _.CompanyFrequencyId == frequencyId)
                .OrderBy(_ => _.PeriodEndDate)
                    .ThenBy(_ => _.OrderNumber);
        }

        public IQueryable<CompanyRunForPayslipResult> GetEmployeeCompanyRuns(long frequencyId, long employeeId)
        {
            return this.Repository.Context.Set<CompanyRunForPayslipResult>()
                .AsNoTracking()
                .TagWithSource()
                .Where(_ => _.FrequencyId == frequencyId && _.Status != RunStatus.Future && _.EmployeeId == employeeId);
        }

        public IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, string periodCode)
        {
            return this.GetCompanyRuns(frequencyId)
                .TagWithSource()
                .Where(_ => _.PeriodCode == periodCode && !_.TakeOnRun);
        }

        public IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, DateTime? effectiveDate)
        {
            return this.GetCompanyRuns(frequencyId)
                .TagWithSource()
                .Where(_ => _.PeriodStartDate <= effectiveDate && _.PeriodEndDate >= effectiveDate);
        }

        public IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, RunStatus status)
        {
            return this.GetCompanyRuns(frequencyId)
                .TagWithSource()
                .Where(_ => _.Status == status);
        }

        public IQueryable<CompanyRun> GetOpenOrFutureMainRuns(long frequencyId, DateTime? effectiveDate)
        {
            return this.GetCompanyRuns(frequencyId, effectiveDate)
                .TagWithSource()
                .Where(_ => _.RunType == RunType.Main)
                .Where(_ => _.Status == RunStatus.Open || _.Status == RunStatus.Future);
        }

        public IQueryable<CompanyRun> GetCompanyRuns(long frequencyId, bool includeInterimRuns, bool includeFutureRuns)
        {
            var runs = this.GetCompanyRuns(frequencyId, RunStatus.Open)
                .TagWithSource()
                .Where(_ => !_.TakeOnRun);

            if (includeFutureRuns)
            {
                var futureRuns = this.GetCompanyRuns(frequencyId, RunStatus.Future).Where(_ => !_.TakeOnRun).Take(2);
                runs = runs.Concat(futureRuns).Where(_ => _.CutOffDate == null || _.CutOffDate > DateTime.Today);
            }

            if (!includeInterimRuns)
            {
                runs = runs.Where(_ => _.RunType == RunType.Main);
            }

            return runs;
        }

        public IQueryable<CompanyRun> GetLatestRun(long frequencyId)
        {
            return this.GetCompanyRuns(frequencyId, RunStatus.Open)
                .TagWithSource()
                .Where(_ => _.RunType == RunType.Main)
                .OrderByDescending(_ => _.PeriodEndDate)
                .ThenBy(_ => _.OrderNumber);
        }

        public Task<List<long>> GetPayslipTakeOnRunsAsync(long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"GetPayslipTakeOnRunsCompanyRuns:{frequencyId}",
                () => this.GetCompanyRuns(frequencyId, true, false)
                    .TagWithSource()
                    .Where(_ => _.PayDate <= _.CompanyFrequency.PostRunByRunBeforeDate)
                    .OrderBy(r => r.PeriodStartDate)
                    .ThenBy(r => r.OrderNumber)
                    .Select(_ => _.RunId)
                    .ToListAsync());
        }

        public Task<bool> FutureRunExistsAsync(long frequencyId, DateTime? effectiveDate)
        {
            return this.scopedCache.GetOrCreateAsync(
                $" FutureRunExists:{frequencyId}:{effectiveDate?.ToShortDateString()}",
                () => this.GetCompanyRuns(frequencyId, effectiveDate).TagWithSource().AnyAsync(_ => _.Status == RunStatus.Future || _.Status == RunStatus.Open));
        }

        public Task<long?> GetLatestRunIdAsync(long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRunService:GetLatestRunId:{frequencyId}",
                async () =>
                {
                    var runId = await this.GetLatestRun(frequencyId)
                        .TagWithSource()
                        .Select(_ => _.RunId)
                        .FirstOrDefaultAsync();

                    return runId == default ? (long?)null : runId;
                });
        }

        public Task<long?> GetLatestRunIdWithCutOffDateAsync(long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRunService:GetLatestRunId:{frequencyId}",
                async () =>
                {
                    var runId = await this.GetLatestRun(frequencyId)
                        .TagWithSource()
                        .Where(_ => _.CutOffDate == null || _.CutOffDate > DateTime.Today)
                        .Select(_ => _.RunId)
                        .FirstOrDefaultAsync();

                    return runId == default ? (long?)null : runId;
                });
        }

        public async Task<long?> GetLatestOrFirstFutureRunIdAsync(long frequencyId, bool includeInterimRuns)
        {
            var runId = await this.GetCompanyRuns(frequencyId, RunStatus.Open)
                .TagWithSource()
                .Where(_ => _.RunType == RunType.Main || (includeInterimRuns && _.RunType == RunType.Interim))
                .Where(_ => _.CutOffDate > DateTime.Today || _.CutOffDate == null)
                .Where(_ => !_.TakeOnRun)
                .OrderByDescending(_ => _.PeriodEndDate)
                    .ThenBy(_ => _.OrderNumber)
                .Select(_ => _.RunId)
                .FirstOrDefaultAsync();

            if (runId > default(long))
            {
                return runId;
            }

            return await this.GetFirstFutureRunIdAsync(frequencyId);
        }

        public async Task<long?> GetEarlistOrFirstFutureRunIdAsync(long frequencyId, bool includeInterimRuns)
        {
            var runId = await this.GetCompanyRuns(frequencyId, RunStatus.Open)
                .TagWithSource()
                .Where(_ => _.RunType == RunType.Main || (includeInterimRuns && _.RunType == RunType.Interim))
                .Where(_ => _.CutOffDate > DateTime.Today || _.CutOffDate == null)
                .Where(_ => !_.TakeOnRun)
                .OrderBy(_ => _.PeriodEndDate)
                    .ThenBy(_ => _.OrderNumber)
                .Select(_ => _.RunId)
                .FirstOrDefaultAsync();

            if (runId > default(long))
            {
                return runId;
            }

            return await this.GetFirstFutureRunIdAsync(frequencyId);
        }

        public async Task<long?> GetCurrentRunIdAsync(long frequencyId)
        {
            var date = DateTime.Today;
            var runId = await this.GetCompanyRuns(frequencyId, RunStatus.Open)
                .TagWithSource()
                .Where(_ => _.RunType == RunType.Main && _.PeriodStartDate <= date && _.PeriodEndDate >= date)
                .OrderByDescending(_ => _.PeriodEndDate)
                    .ThenBy(_ => _.OrderNumber)
                .Select(_ => _.RunId)
                .FirstOrDefaultAsync();

            if (runId == default)
            {
                return null;
            }

            return runId;
        }

        public Task<long?> GetFirstFutureRunIdAsync(long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRunService:GetFirstFutureRunId:{frequencyId}",
                async () =>
                {
                    var runId = await this.GetCompanyRuns(frequencyId, RunStatus.Future)
                        .TagWithSource()
                        .Where(x => x.RunType == RunType.Main)
                        .OrderBy(x => x.PeriodEndDate)
                        .ThenBy(_ => _.OrderNumber)
                        .Select(_ => _.RunId)
                        .FirstOrDefaultAsync();

                    if (runId == default)
                    {
                        return (long?)null;
                    }

                    return runId;
                });
        }

        public Task<long> GetRunFrequencyIdAsync(long runId)
        {
            return this.distributedCache.GetOrCreateAsync(
                CompanyRunCacheKeys.FrequencyId(runId),
                () => this.Repository.Set.TagWithSource()
                    .Where(r => r.RunId == runId)
                    .Select(r => r.CompanyFrequencyId)
                    .SingleAsync());
        }

        public long GetRunFrequencyId(long runId)
        {
            return this.distributedCache.GetOrCreate(
                CompanyRunCacheKeys.FrequencyId(runId),
                () => this.Repository.Set.TagWithSource()
                    .Where(r => r.RunId == runId)
                    .Select(r => r.CompanyFrequencyId)
                    .Single());
        }

        public Task<RunType> GetRunTypeAsync(long runId)
        {
            return this.Repository.Context.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.RunId == runId)
                .Select(x => x.RunType)
                .SingleAsync();
        }

        public Task<RunStatus> GetRunStatusAsync(long runId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRunService:RunStatus:{runId}",
                () => this.Repository.Context.Set<CompanyRun>()
                    .TagWithSource()
                    .Where(_ => _.RunId == runId)
                    .Select(x => x.Status)
                    .SingleAsync());
        }

        public Task<string> GetRunDescriptionAsync(long runId)
        {
            return this.Repository.Context.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.RunId == runId)
                .Select(x => x.RunDescription)
                .SingleAsync();
        }

        public Task<DateTime> GetPeriodEndDateAsync(long runId)
        {
            return this.GetRunSummaryAsync(runId).ContinueWith(_ => _.Result.PeriodEndDate);
        }

        public DateTime GetPeriodEndDate(long runId)
        {
            return this.GetRunSummary(runId).PeriodEndDate;
        }

        public DateTime GetPeriodStartDate(long runId)
        {
            return this.GetRunSummary(runId).PeriodStartDate;
        }

        public Task<CompanyRunPeriodResult> GetRunPeriodAsync(long runId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRun:RunId:{runId}:Period",
                () =>
                {
                    return this.Repository.Context.Set<CompanyRun>()
                        .TagWithSource()
                        .Where(_ => _.RunId == runId)
                        .Select(_ => new CompanyRunPeriodResult
                        {
                            RunId = _.RunId,
                            RunDescription = _.RunDescription,
                            Status = _.Status,
                            PeriodStartDate = _.PeriodStartDate,
                            PeriodEndDate = _.PeriodEndDate,
                            OrderNumber = _.OrderNumber,
                            PeriodCode = _.PeriodCode,
                            TakeOnRun = _.TakeOnRun
                        })
                        .SingleOrDefaultAsync();
                });
        }

        public Task<DateTime> GetEarliestPeriodEndDateAsync(long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRun:FrequencyId:{frequencyId}:EarliestPeriodEndDate",
                () =>
                {
                    return this.GetCompanyRuns(frequencyId)
                        .TagWithSource()
                        .Where(_ => _.Status == RunStatus.Open)
                        .Select(_ => _.PeriodEndDate)
                        .FirstOrDefaultAsync();
                });
        }

        public Task<DateTime> GetEarliestPeriodStartDateAsync(long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRun:FrequencyId:{frequencyId}:EarliestPeriodStartDate",
                () =>
                {
                    return this.GetCompanyRuns(frequencyId)
                        .TagWithSource()
                        .Where(_ => _.Status == RunStatus.Open)
                        .OrderBy(_ => _.PeriodStartDate)
                        .ThenBy(_ => _.OrderNumber)
                        .Select(_ => _.PeriodStartDate)
                        .FirstOrDefaultAsync();
                });
        }

        public DateTime? GetEarliestPeriodEndDate(long frequencyId)
        {
            // Converting periodEndDate to a nullable date so I can more easily handle situations where there are no open runs
            return this.GetCompanyRuns(frequencyId)
                .TagWithSource()
                .Where(_ => _.Status == RunStatus.Open)
                .OrderBy(_ => _.OrderNumber)
                .Select(_ => (DateTime?)_.PeriodEndDate)
                .FirstOrDefault();
        }

        public Task<DateTime> GetEarliestPeriodStartDateForTaxYearAsync(long frequencyId, DateTime taxYearStartDate)
        {
            return this.GetCompanyRuns(frequencyId)
                .TagWithSource()
                .Where(_ => _.Status != RunStatus.Future && _.PeriodStartDate > taxYearStartDate)
                .OrderBy(_ => _.PeriodStartDate)
                .Select(_ => _.PeriodStartDate)
                .FirstOrDefaultAsync();
        }

        public Task<DateTime> GetEarliestPeriodStartDateForPeriodCodeAsync(long frequencyId, string periodCode)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRun:FrequencyId:{frequencyId}:PeriodCode:{periodCode}:EarliestPeriodStartDateForTaxYear",
                () =>
                {
                    return this.GetCompanyRuns(frequencyId, periodCode)
                        .TagWithSource()
                        .OrderBy(_ => _.PeriodStartDate)
                        .Select(_ => _.PeriodStartDate)
                        .FirstOrDefaultAsync();
                });
        }

        public Task<CompanyRun> GetEffectiveRunAsync(long frequencyId, DateTime effectiveDate)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CompanyRun:FrequencyId:{frequencyId}:EffectiveRun:{effectiveDate:d}",
                () => this.GetCompanyRuns(frequencyId, effectiveDate).TagWithSource().FirstOrDefaultAsync());
        }

        public Task<CompanyRunSummaryResult> GetRunSummaryAsync(long runId)
        {
            return this.distributedCache.GetOrCreateAsync(
                CompanyRunCacheKeys.Summary(runId),
                () => this.GetRunSummaryQuery(runId).TagWithSource().SingleAsync());
        }

        public CompanyRunSummaryResult GetRunSummary(long runId)
        {
            return this.distributedCache.GetOrCreate(
                CompanyRunCacheKeys.Summary(runId),
                () => this.GetRunSummaryQuery(runId).TagWithSource().Single());
        }

        // TODO: Only used on claims?
        public async Task<IList<CompanyRun>> GetAvailableRunsAsync(bool allowSelectRun, long companyId, long frequencyId)
        {
            // Note negative logic, the setting is "do not display"
            var displayInterimRuns = !await this.companySettingService.IsActiveAsync(companyId, "DISPINTRN");

            if (allowSelectRun)
            {
                return await this.GetCompanyRuns(frequencyId, displayInterimRuns, false).ToListAsync();
            }

            var runId = await this.GetCompanyRunIdAsync(frequencyId, displayInterimRuns);
            if (runId.HasValue)
            {
                return new[] { await this.FindByIdAsync(runId.Value) };
            }

            return Array.Empty<CompanyRun>();
        }

        public Task<long?> GetRunIdByRunValueCodeAsync(long companyId, long frequencyId, string runValueCode)
        {
            return this.distributedCache.GetOrCreateAsync(
                CompanyRunCacheKeys.RunValueCode(companyId, frequencyId, runValueCode),
                async () =>
                {
                    var runId = await this.GetRunIdQuery(companyId, frequencyId, runValueCode).TagWithSource().SingleOrDefaultAsync();
                    if (runId == default)
                    {
                        return default(long?);
                    }

                    return runId;
                });
        }

        public Task<bool> IsCompanyRunAsync(long companyId, long frequencyId, long runId)
        {
            return this.distributedCache.GetOrCreateAsync(
                CompanyRunCacheKeys.IsCompanyRun(companyId, frequencyId, runId),
                () =>
                {
                    return this.GetCompanyRuns(frequencyId).TagWithSource().AnyAsync(_ => _.CompanyFrequency.CompanyId == companyId && _.RunId == runId);
                });
        }

        public long GetRunIdByRunValueCode(long companyId, long frequencyId, string runValueCode)
        {
            return this.distributedCache.GetOrCreate(
                CompanyRunCacheKeys.RunValueCode(companyId, frequencyId, runValueCode),
                () =>
                {
                    var runId = this.GetRunIdQuery(companyId, frequencyId, runValueCode).TagWithSource().SingleOrDefault();
                    if (runId == default)
                    {
                        return default;
                    }

                    return runId;
                });
        }

        public async Task<IEnumerable<CompanyRunCloudRoomItem>> GetCloudRoomRunsAsync(long companyId, long? runId)
        {
            var result = new List<CompanyRunCloudRoomItem>();

            var frequencyIds = await this.readOnlyContext.Set<CompanyRunFrequency>()
                .TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .ToListAsync();

            foreach (var frequency in frequencyIds)
            {
                var pastRuns = this.readOnlyContext.Set<CompanyRun>()
                    .TagWithSource()
                    .Where(_ => _.CompanyFrequencyId == frequency.CompanyFrequencyId)
                    .Where(_ => _.Status == RunStatus.Closed)
                    .OrderByDescending(_ => _.PeriodStartDate)
                    .ThenByDescending(_ => _.OrderNumber)
                    .Select(_ => new CompanyRunCloudRoomItem
                    {
                        Id = _.RunId,
                        Name = _.RunDescription,
                        Frequency = frequency.FrequencyName
                    })
                    .Take(2);

                result.AddRange(pastRuns);

                var currentRuns = this.readOnlyContext.Set<CompanyRun>()
                    .TagWithSource()
                    .Where(_ => _.CompanyFrequencyId == frequency.CompanyFrequencyId)
                    .Where(_ => _.Status == RunStatus.Open)
                    .OrderByDescending(_ => _.PeriodStartDate)
                    .ThenBy(_ => _.OrderNumber)
                    .Select(_ => new CompanyRunCloudRoomItem
                    {
                        Id = _.RunId,
                        Name = _.RunDescription,
                        Frequency = frequency.FrequencyName
                    });

                result.AddRange(currentRuns);

                var futureRuns = this.readOnlyContext.Set<CompanyRun>()
                    .TagWithSource()
                    .Where(_ => _.CompanyFrequencyId == frequency.CompanyFrequencyId)
                    .Where(_ => _.Status == RunStatus.Future)
                    .OrderBy(_ => _.PeriodStartDate)
                    .ThenBy(_ => _.OrderNumber)
                    .Select(_ => new CompanyRunCloudRoomItem
                    {
                        Id = _.RunId,
                        Name = _.RunDescription,
                        Frequency = frequency.FrequencyName
                    })
                    .Take(2);

                result.AddRange(futureRuns);
            }

            if (runId.HasValue && !result.Any(_ => _.Id == runId))
            {
                // Add the current selected run in cases where it has been closed and is no longer available in the list
                result.Add(await this.readOnlyContext.Set<CompanyRun>()
                               .TagWithSource()
                               .Where(_ => _.RunId == runId)
                               .Select(
                                   _ => new CompanyRunCloudRoomItem
                                   {
                                       Id = _.RunId,
                                       Name = _.RunDescription,
                                       Frequency = _.CompanyFrequency.FrequencyName
                                   })
                               .SingleOrDefaultAsync());
            }

            return result;
        }

        public Task<string> GetFrequencyNameAndRunDescriptionByRunIdAsync(long runId)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.RunId == runId)
                .Select(_ => _.CompanyFrequency.FrequencyName + " - " + _.RunDescription)
                .FirstOrDefaultAsync();
        }

        public Task<RunStatus> GetEffectiveRunStatusAsync(long frequencyId, DateTime effectiveDate)
        {
            return this.GetCompanyRuns(frequencyId, effectiveDate)
                .TagWithSource()
                .Select(_ => _.Status)
                .FirstOrDefaultAsync();
        }

        public Task<CompanyRun> GetMainRunByEffectiveDateAsync(long frequencyId, DateTime effectiveDate)
        {
            return this.GetCompanyRuns(frequencyId, effectiveDate)
                .TagWithSource()
                .Where(_ => _.RunType == RunType.Main)
                .OrderBy(_ => _.PeriodStartDate)
                .ThenBy(_ => _.OrderNumber)
                .FirstOrDefaultAsync();
        }

        public Task<bool> HasOpenOrFutureRunsWithinPeriodAsync(long companyId, string periodCode)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.PeriodCode == periodCode
                        && _.CompanyFrequency.CompanyId == companyId
                        && (_.Status == RunStatus.Open || _.Status == RunStatus.Future))
                .AnyAsync();
        }

        public Task<DateTime?> GetLatestCutOffDateAsync(long companyId, string periodCode)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .Where(_ => _.PeriodCode == periodCode
                        && _.CompanyFrequency.CompanyId == companyId
                        && (_.Status != RunStatus.Open || _.Status != RunStatus.Future))
                .OrderBy(_ => _.CutOffDate)
                .Select(_ => _.CutOffDate)
                .LastOrDefaultAsync();
        }

        public Task<List<CompanyRun>> GetClosestOpenPeriodRunsAsync(long frequencyId)
        {
            return this.Repository.Context.Set<CompanyRun>()
                .FromSqlRaw("EXEC company_closts_open_period_runs_search @FrequencyID", new SqlParameter("@FrequencyID", frequencyId))
                .TagWithSource()
                .AsNoTracking()
                .ToListAsync();
        }

        public Task<bool> IsEarliestRunInAnyFrequencyClosedAsync(long companyId)
        {
            return this.Repository.Context.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.CompanyFrequency.CompanyId == companyId)
                .OrderBy(_ => _.PeriodEndDate)
                .ThenBy(_ => _.OrderNumber)
                .AnyAsync(_ => _.Status == RunStatus.Closed
                               && !_.CompanyFrequency.CompanyRuns.Any(x => x.PeriodStartDate < _.PeriodStartDate));
        }

        public Task<bool> HasOpenRunsWithinDateRangeAsync(long companyId, DateTime startDate, DateTime endDate)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.Status == RunStatus.Open
                            && _.PeriodStartDate >= startDate
                            && _.PeriodEndDate <= endDate
                            && _.CompanyFrequency.CompanyId == companyId)
                .AnyAsync();
        }

        public Task<bool> HasClosedRunsAsync(long frequencyId)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.CompanyFrequencyId == frequencyId
                        && (_.Status == RunStatus.Closed))
                .AnyAsync();
        }

        public async Task<long?> GetLatestClosedRunIdAsync(long frequencyId, bool includeInterimRuns)
        {
            return await this.GetCompanyRuns(frequencyId, RunStatus.Closed)
                .TagWithSource()
                .Where(_ => _.RunType == RunType.Main || (includeInterimRuns && _.RunType == RunType.Interim))
                .Where(_ => _.CutOffDate > DateTime.Today || _.CutOffDate == null)
                .Where(_ => !_.TakeOnRun)
                .OrderByDescending(_ => _.PeriodEndDate)
                .ThenBy(_ => _.OrderNumber)
                .Select(_ => _.RunId)
                .FirstOrDefaultAsync();
        }

        public int GetFirstYearRun(long companyId)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.CompanyFrequency.CompanyId == companyId)
                .OrderBy(_ => _.RunId)
                .Select(_ => _.PeriodEndDate.Year)
                .FirstOrDefault();
        }

        public Task<bool> IsTakeOnRunPeriodAsync(long companyId, string periodCode)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .AnyAsync(_ => _.PeriodCode == periodCode
                            && _.CompanyFrequency.CompanyId == companyId
                            && _.TakeOnRun);
        }

        public IQueryable<CompanyRun> GetGroupedCompanyRunsPerPeriod(long frequencyId, DateTime startDate, DateTime endDate, bool newPeriod)
        {
            var query = this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.CompanyFrequencyId == frequencyId && _.PeriodStartDate == startDate && _.PeriodEndDate == endDate);

            if (newPeriod)
            {
                query = query
                    .GroupJoin(
                        this.readOnlyContext.Set<EnumRunType>(),
                        cpr => (int)cpr.RunType,
                        runType => runType.RunTypeId,
                        (cpr, runTypes) => new { CompanyRun = cpr, RunTypes = runTypes.DefaultIfEmpty() })
                    .SelectMany(
                        _ => _.RunTypes.DefaultIfEmpty(),
                        (_, runType) => _.CompanyRun)
                    .Where(_ => _.NewPeriod == true);
            }

            return query.OrderBy(_ => _.OrderNumber);
        }

        public async Task<bool> DoesPublicHolidayExistAsync(long companyId, DateTime publicHoliday)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);

            return await this.readOnlyContext.Set<PublicHoliday>().TagWithSource()
                .AnyAsync(_ => _.HolidayDate == publicHoliday && _.TaxCountryId == countryId);
        }

        public async Task<CompanyRun> AddAsync(CompanyRun entity, bool isBureauRun = false)
        {
            await this.SetDefaultValuesAsync(entity, isBureauRun);

            if (entity.OrderNumber == 1)
            {
                await this.UpdateCompanyRunPeriodAsync(entity.CompanyFrequencyId, entity.PeriodStartDate, entity.PeriodEndDate);
            }

            await this.UpdateCompanyRunOrderNumberAsync(entity.CompanyFrequencyId, entity.PeriodStartDate, entity.PeriodEndDate, entity.OrderNumber);

            await base.AddAsync(entity);

            await this.AddPayCalendarLinkForInterimRunAsync(entity);

            return entity;
        }

        public override Task<CompanyRun> AddAsync(CompanyRun entity)
        {
            throw new NotImplementedException();
        }

        public async Task<CompanyRun> UpdateAsync(long companyId, CompanyRun entity, bool isBureauRun = false)
        {
            await this.SetDefaultValuesAsync(entity, isBureauRun);

            await this.ScheduleCompanyRunsAsync(companyId, entity);

            await this.UpdateYtdToMatchCurrentAsync(entity, isBureauRun);

            await this.ProcessOrderAndPeriod(entity);

            await base.UpdateAsync(entity);

            return entity;
        }

        private async Task ProcessOrderAndPeriod(CompanyRun entity)
        {
            var previousOrderNumber = await this.GetPreviousOrderNumberAsync(entity.RunId);

            if (entity.OrderNumber > previousOrderNumber)
            {
                // Update Company Runs where Order Number is between Previous Order Number and Order Number
                var runsToUpdate = await this.Repository.Context.Set<CompanyRun>().TagWithSource()
                    .Where(_ => _.OrderNumber >= previousOrderNumber && _.OrderNumber <= entity.OrderNumber
                        && _.RunId != entity.RunId
                        && _.CompanyFrequencyId == entity.CompanyFrequencyId
                        && _.PeriodStartDate == entity.PeriodStartDate
                        && _.PeriodEndDate == entity.PeriodEndDate)
                    .ToListAsync();

                foreach (var run in runsToUpdate)
                {
                    run.OrderNumber -= 1;
                    run.NewPeriod = run.OrderNumber == 1;
                }
            }
            else if (entity.OrderNumber != previousOrderNumber)
            {
                // Update Company Runs where Order Number is between Order Number and Previous Order Number
                var runsToUpdate = await this.Repository.Context.Set<CompanyRun>().TagWithSource()
                    .Where(_ => _.OrderNumber <= previousOrderNumber && _.OrderNumber >= entity.OrderNumber
                                && _.RunId != entity.RunId
                                && _.CompanyFrequencyId == entity.CompanyFrequencyId
                                && _.PeriodStartDate == entity.PeriodStartDate
                                && _.PeriodEndDate == entity.PeriodEndDate)
                    .ToListAsync();

                foreach (var run in runsToUpdate)
                {
                    run.OrderNumber += 1;
                    run.NewPeriod = false;
                }
            }

            await this.Repository.SaveChangesAsync();
        }

        public override Task<CompanyRun> UpdateAsync(CompanyRun entity)
        {
            throw new NotImplementedException();
        }

        public async Task DeleteAsync(long companyId, CompanyRun entity)
        {
            if (entity.NewPeriod == true)
            {
                await this.UpdateCompanyRunPeriodByCodeAsync(entity.CompanyFrequencyId, entity.PeriodCode);
            }

            await this.InvalidateCompanyRunCaches(companyId, entity);

            await this.DeletePayCalendarLinkAsync(entity);

            await base.DeleteAsync(entity);
        }

        public override Task DeleteAsync(CompanyRun entity)
        {
            throw new NotImplementedException();
        }

        public Task<bool> HasDateWithinClosedRunsAsync(long companyId, DateTime effectiveDate)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .AnyAsync(_ => _.Status == RunStatus.Closed
                               && _.PeriodStartDate <= effectiveDate && _.PeriodEndDate >= effectiveDate
                               && _.CompanyFrequency.CompanyId == companyId);
        }

        public Task<DateTime> GetEarliestOpenRunDateAsync(long companyId)
        {
            return this.readOnlyContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(_ => _.CompanyFrequency.CompanyId == companyId && _.Status == RunStatus.Open)
                .OrderBy(_ => _.PeriodEndDate)
                .Select(_ => _.PeriodStartDate)
                .FirstOrDefaultAsync();
        }

        private IQueryable<long> GetRunIdQuery(long companyId, long frequencyId, string runValueCode)
        {
            return this.GetCompanyRuns(frequencyId)
                .TagWithSource()
                .Where(_ => _.CompanyFrequency.CompanyId == companyId
                           && (_.CompanyFrequency.RunFrequencyId == (int)PayslipFrequency.Monthly
                                                                           ? _.RunType == RunType.Main ? _.PeriodCode : _.PeriodCode + "-" + _.OrderNumber
                                                                           : _.PeriodCode + "-" + _.PeriodEndDate.Month + "-" + _.PeriodEndDate.Day + "-" + _.OrderNumber) == runValueCode)
                .Select(r => r.RunId);
        }

        public Task<List<CompanyRun>> GetAllCompanyRunsAsync(long frequencyId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"GetAllCompanyRunsAsync:FrequencyId:{frequencyId}",
                () => this.GetCompanyRuns(frequencyId).TagWithSource().ToListAsync());
        }

        public Task<List<CompanyRun>> GetAllGroupedCompanyRunsPerPeriodAsync(long frequencyId, DateTime startDate, DateTime endDate, bool newPeriod)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"GetAllGroupedCompanyRunsPerPeriod:FrequencyId:{frequencyId}:StartDate:{startDate}:EndDate:{endDate}:NewPeriod:{newPeriod}",
                () => this.GetGroupedCompanyRunsPerPeriod(frequencyId, startDate, endDate, newPeriod).TagWithSource().ToListAsync());
        }

        public virtual async Task<List<CompanyRunOrderNumberDataset>> GetRunOrderNumbersAsync(
            long frequencyId,
            DateTime? startDate,
            DateTime? endDate,
            UserType? userType,
            long? runId,
            bool isBureau = false,
            long? companyId = null,
            string periodCode = null)
        {
            // Return default list if start or end date is null.
            if (!startDate.HasValue || !endDate.HasValue)
            {
                return new List<CompanyRunOrderNumberDataset>() { new(1, "1") };
            }

            var runs = await this.GetAllGroupedCompanyRunsPerPeriodAsync(frequencyId, startDate.Value, endDate.Value, false);

            var orderList = new List<CompanyRunOrderNumberDataset>();
            var currentOrderCount = 0;
            var maxClosedOrder = runs.Any(_ => _.Status == RunStatus.Closed) ? runs.Where(_ => _.Status == RunStatus.Closed).Max(_ => _.OrderNumber) : 0;

            //Bureau should see all runs, Company Run on Add should see only runs available after last closed run, on edit Company run should see all runs
            foreach (var run in runs)
            {
                if (runId.HasValue || run.OrderNumber > maxClosedOrder)
                {
                    if (orderList.All(_ => _.Value != run.OrderNumber))
                    {
                        orderList.Add(new(run.OrderNumber, run.OrderNumber.ToString()));
                    }
                    currentOrderCount = run.OrderNumber;
                }
                else
                {
                    currentOrderCount++;
                }
            }

            // If no runId provided, add the next available order count.
            if (!runId.HasValue)
            {
                currentOrderCount++;
                if (orderList.All(_ => _.Value != currentOrderCount))
                {
                    orderList.Add(new(currentOrderCount, currentOrderCount.ToString()));
                }
            }

            return orderList.Count > 0 ? orderList : new List<CompanyRunOrderNumberDataset>() { new(1, "1") };
        }

        public async Task<string> GetPrevTaxPeriodCodeAsync(long companyId, long frequencyId, UserType userType)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            if (userType is UserType.Agency or UserType.Bureau)
            {
                var closedRun = await this.GetLastClosedRunTaxYearAsync(frequencyId, countryId, null);
                if (closedRun != null)
                {
                    return closedRun.PeriodCode;
                }
            }

            return null;
        }

        public async Task<bool> GetTaxErrorsAsync(long runId)
        {
            var parameters = new[]
            {
                new SqlParameter("@EmployeeID", "0"),
                new SqlParameter("@ErrorType", ErrorSearchType.ErrorOnly.ToString()),
                new SqlParameter("@CompanyFrequencyID", "0"),
                new SqlParameter("@GetCount", true),
                new SqlParameter("@IncludeWorkFlow", true),
                new SqlParameter("@RunID", runId)
            };

            var result = await this.Repository.Context.Set<EmployeeTaxCalcErrorResult>()
                .FromSqlRaw("EXEC tax_calc_errors_search @EmployeeID, @ErrorType, @CompanyFrequencyID, @GetCount, @IncludeWorkFlow, @RunID", parameters)
                .AsNoTracking()
                .TagWithSource()
                .ToListAsync();

            return result.Count > 0;
        }

        public Task<bool> HasEmployeeTotalsHeaderAsync(long runId)
        {
            return this.Repository.Context.Set<EmployeeTotalHeader>().TagWithSource()
                .AnyAsync(_ => _.CompanyRunId == runId);
        }

        public Task<(DateTime? StartDate, DateTime? EndDate)> GetPayCalendarStartAndEndDateAsync(long runId)
        {
            return this.readOnlyContext.Set<CompanyRunsPayCalendarsLink>().TagWithSource()
                .Where(_ => _.RunId == runId)
                .Include(_ => _.PayCalendar)
                .Select(_ => new ValueTuple<DateTime?, DateTime?>(_.PayCalendar.StartDate, _.PayCalendar.EndDate))
                .FirstOrDefaultAsync();
        }

        public virtual Task AddPayCalendarLinkForInterimRunAsync(CompanyRun entity)
        {
            return Task.CompletedTask;
        }

        public virtual Task DeletePayCalendarLinkAsync(CompanyRun entity)
        {
            return Task.CompletedTask;
        }

        public async Task TriggerCalculationAsync(CompanyRun entity)
        {
            if (entity.Status == RunStatus.Closed)
            {
                await this.calculationApiClient.CalculateCompanyAsync(entity.RunId);
            }
        }

        public IQueryable<long> GetOpenRuns(long frequencyId)
        {
            return this.GetCompanyRuns(frequencyId)
                .TagWithSource()
                .Where(_ => _.Status == RunStatus.Open)
                .Select(_ => _.RunId);
        }

        public async Task InvalidateCompanyRunCaches(long companyId, CompanyRun companyRun)
        {
            await this.distributedCache.RemoveAsync(CompanyRunCacheKeys.Summary(companyRun.RunId));
            await this.distributedCache.RemoveAsync(CompanyRunCacheKeys.FrequencyId(companyRun.RunId));
            await this.distributedCache.RemoveAsync(CompanyRunCacheKeys.IsCompanyRun(companyId, companyRun.CompanyFrequencyId, companyRun.RunId));
            await this.distributedCache.RemoveAsync(CompanyRunCacheKeys.RunValueCode(
                companyId,
                companyRun.CompanyFrequencyId,
                $"{companyRun.PeriodCode}"));
            await this.distributedCache.RemoveAsync(CompanyRunCacheKeys.RunValueCode(
                companyId,
                companyRun.CompanyFrequencyId,
                $"{companyRun.PeriodCode}-{companyRun.OrderNumber}"));
            await this.distributedCache.RemoveAsync(CompanyRunCacheKeys.RunValueCode(
                companyId,
                companyRun.CompanyFrequencyId,
                $"{companyRun.PeriodCode}-{companyRun.PeriodEndDate.Month}-{companyRun.PeriodEndDate.Day}-{companyRun.OrderNumber}"));
        }

        public async Task ProcessFutureRunsAsync(long companyId, CompanyRun entity, bool skipChecks)
        {
            if (entity.Status != RunStatus.Closed || skipChecks)
            {
                return;
            }

            //Identify if there are runs for the next tax year
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var currentTaxYear = await this.GetCountryTaxYears(countryId)
                .Where(_ => entity.PeriodStartDate >= _.YearStartDate && entity.PeriodStartDate <= _.YearEndDate)
                .Select(_ => _.YearEndDate)
                .FirstOrDefaultAsync();

            if (currentTaxYear == DateTime.MinValue)
            {
                return;
            }

            var nextTaxYearDate = currentTaxYear.AddMonths(1);
            var firstMonthOfNextTaxYear = new DateTime(nextTaxYearDate.Year, nextTaxYearDate.Month, 1);

            var futurePeriodCode = this.GeneratePeriodCode(nextTaxYearDate);

            var futureCompanyRuns = await this.GetCompanyRuns(entity.CompanyFrequencyId, futurePeriodCode).CountAsync();

            var nextTaxYearCount = await this.GetCountryTaxYears(countryId)
                .CountAsync(_ => firstMonthOfNextTaxYear >= _.YearStartDate && firstMonthOfNextTaxYear <= _.YearEndDate);

            if (futureCompanyRuns == 0 && nextTaxYearCount > 0)
            {
                //Open future runs
                await this.OpenFutureRunAsync(companyId, entity, firstMonthOfNextTaxYear);
            }
        }

        public async Task OpenNextRunAsync(long companyId, CompanyRun entity, bool skipChecks)
        {
            if (entity.Status != RunStatus.Closed || skipChecks)
            {
                return;
            }

            //Get next company runs to open
            var openRunExists = await this.GetCompanyRuns(entity.CompanyFrequencyId, RunStatus.Open)
                .TagWithSource()
                .AnyAsync();

            // Exit early if there's already an open run
            if (openRunExists)
            {
                return;
            }

            // Find the first future run to process
            var nextFutureRun = await this.Repository.Context.Set<CompanyRun>().TagWithSource()
                .Where(_ => _.CompanyFrequencyId == entity.CompanyFrequencyId && _.Status == RunStatus.Future)
                .OrderBy(_ => _.PeriodEndDate)
                    .ThenBy(_ => _.OrderNumber)
                .FirstOrDefaultAsync();

            if (nextFutureRun != null)
            {
                nextFutureRun.Status = RunStatus.Open;

                await this.ProcessOrderAndPeriod(nextFutureRun);
                await base.UpdateAsync(nextFutureRun);
                await this.QueCompanyRunCalculationAsync(companyId, nextFutureRun);
            }
        }

        public Task<bool> HasEmployeeCustomFormAsync(long runId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"EmployeeCustomForm:RunId:{runId}",
                () => this.Repository.Context.Set<EmployeeCustomForm>()
                    .TagWithSource()
                    .AnyAsync(_ => _.CompanyRunId == runId));
        }

        private async Task<long?> GetCompanyRunIdAsync(long frequencyId, bool displayInterimRuns)
        {
            var currentRunId = await this.GetEarlistOrFirstFutureRunIdAsync(frequencyId, displayInterimRuns);
            if (currentRunId.HasValue)
            {
                return currentRunId.Value;
            }

            // Get open interim runs - where within date period
            var firstFutureRunId = await this.GetFirstFutureRunIdAsync(frequencyId);
            if (firstFutureRunId.HasValue)
            {
                return firstFutureRunId.Value;
            }

            return null;
        }

        private IQueryable<CompanyRunSummaryResult> GetRunSummaryQuery(long runId)
        {
            return this.Repository.Set.Where(_ => _.RunId == runId)
                .TagWithSource()
                .Select(_ => new CompanyRunSummaryResult
                {
                    RunId = _.RunId,
                    PeriodStartDate = _.PeriodStartDate,
                    PeriodEndDate = _.PeriodEndDate,
                    OrderNumber = _.OrderNumber,
                    PeriodCode = _.PeriodCode
                });
        }

        private async Task UpdateCompanyRunPeriodByCodeAsync(long frequencyID, string periodCode)
        {
            var runToUpdate = await this.Repository.Context.Set<CompanyRun>().TagWithSource()
                .Where(_ => _.CompanyFrequencyId == frequencyID && _.PeriodCode == periodCode)
                .OrderBy(_ => _.OrderNumber)
                .FirstOrDefaultAsync();

            if (runToUpdate != null)
            {
                runToUpdate.NewPeriod = true;
                await this.Repository.SaveChangesAsync();
            }
        }

        private async Task UpdateCompanyRunPeriodAsync(long frequencyID, DateTime periodStartDate, DateTime periodEndDate)
        {
            var runsToUpdate = await this.Repository.Context.Set<CompanyRun>().TagWithSource()
                .Where(_ => _.NewPeriod == true
                            && _.CompanyFrequencyId == frequencyID
                            && _.PeriodStartDate == periodStartDate
                            && _.PeriodEndDate == periodEndDate)
                .ToListAsync();

            foreach (var run in runsToUpdate)
            {
                run.NewPeriod = false;
            }

            await this.Repository.SaveChangesAsync();
        }

        private async Task UpdateCompanyRunOrderNumberAsync(long frequencyID, DateTime periodStartDate, DateTime periodEndDate, int orderNumber)
        {
            var runsToUpdate = await this.Repository.Context.Set<CompanyRun>().TagWithSource()
                .Where(_ => _.OrderNumber >= orderNumber
                            && _.CompanyFrequencyId == frequencyID
                            && _.PeriodStartDate == periodStartDate
                            && _.PeriodEndDate == periodEndDate)
                .ToListAsync();

            foreach (var companyRun in runsToUpdate)
            {
                companyRun.OrderNumber += 1;
            }

            await this.Repository.SaveChangesAsync();
        }

        private Task<int> GetPreviousOrderNumberAsync(long runId)
        {
            return this.Repository.Context.Set<CompanyRun>().TagWithSource()
                .AsNoTracking()
                .Where(_ => _.RunId == runId)
                .Select(_ => _.OrderNumber)
                .SingleOrDefaultAsync();
        }

        private async Task SetDefaultValuesAsync(CompanyRun entity, bool isBureauRun)
        {
            entity.NewPeriod = false;
            entity.HasBeenInvoiced ??= false;
            entity.DisableEssAccess ??= false;

            if (isBureauRun || !entity.CutOffDate.HasValue)
            {
                entity.CutOffDate = null;
                entity.CutOff = null;
            }
            else
            {
                entity.CutOff = entity.CutOffDate <= DateTime.Today;
            }

            if (!isBureauRun)
            {
                entity.IsInterimTaxedPrevious ??= false;
                entity.SendReportsPayDay ??= false;
                entity.RetroRun ??= false;
                entity.DenyTimesheetAccess ??= false;

                var hasTakeOn = await this.GetCompanyRuns(entity.CompanyFrequencyId, RunStatus.Open)
                    .AnyAsync(_ => _.TakeOnRun);

                if (hasTakeOn)
                {
                    if (!entity.TakeOnRun && entity.OrderNumber == 1)
                    {
                        entity.OrderNumber = 2;
                    }
                    else if (entity.TakeOnRun)
                    {
                        entity.OrderNumber = 1;
                    }
                }

                var groupedRuns = await this.GetAllGroupedCompanyRunsPerPeriodAsync(entity.CompanyFrequencyId, entity.PeriodStartDate, entity.PeriodEndDate, true);
                if (groupedRuns.Any())
                {
                    if (entity.OrderNumber == 1)
                    {
                        entity.NewPeriod = true;
                    }
                }
                else
                {
                    // If no grouped runs, check the company runs for the period.
                    var periodRunId = await this.GetRunIdForPeriodAsync(entity.CompanyFrequencyId, entity.PeriodCode);
                    if (periodRunId != default)
                    {
                        // If RunId matches, mark as NewPeriod.
                        entity.NewPeriod = periodRunId == entity.RunId;
                    }
                    else
                    {
                        // No previous runs found, fetch the grouped runs again and set NewPeriod to false.
                        groupedRuns = await this.GetAllGroupedCompanyRunsPerPeriodAsync(entity.CompanyFrequencyId, entity.PeriodStartDate, entity.PeriodEndDate, false);
                        entity.NewPeriod = false;
                    }
                }
            }
        }

        private async Task ScheduleCompanyRunsAsync(long companyId, CompanyRun currentRun)
        {
            var previousRun = await this.GetCompanyRuns(currentRun.CompanyFrequencyId).FirstOrDefaultAsync(_ => _.RunId == currentRun.RunId);
            if (previousRun != null && previousRun.Status == RunStatus.Future && currentRun.Status == RunStatus.Open)
            {
                await this.QueCompanyRunCalculationAsync(companyId, currentRun);
            }
        }

        private async Task QueCompanyRunCalculationAsync(long companyId, CompanyRun currentRun)
        {
            var isEmployeeOnly = companyId == 0;
            if (!isEmployeeOnly)
            {
                var cacheLevelKey = CacheKeyHelper.CreateCacheLevelKey(companyId, KeyLevelEnum.Company);
                await this.calcCacheWebApiClient.RemoveByKeyLevelAsync(cacheLevelKey);
            }

            await this.calculationApiClient.QueueScheduledCalculationAsync(companyId, 0, currentRun.RunId, CalcSource.CompanyRun.ToString(), !isEmployeeOnly);
        }

        private async Task UpdateYtdToMatchCurrentAsync(CompanyRun currentRun, bool mustRunYtdUpdate)
        {
            if (currentRun.Status != RunStatus.Closed)
            {
                return;
            }

            //Bureau by default must run, non bureau we need to check following closed runs
            mustRunYtdUpdate = mustRunYtdUpdate || await this.ShouldRunYtdUpdateAsync(currentRun);
            if (mustRunYtdUpdate)
            {
                var parameters = new DynamicParameters();
                parameters.Add("@RunID", currentRun.RunId);

                //Use daper because proc is already in transaction
                //Configure Timout 2 min
                await this.dapperRepository.ExecuteQueryAsync("update_ytd_to_match_currents", parameters, 120);
            }
        }

        private async Task<bool> ShouldRunYtdUpdateAsync(CompanyRun currentRun)
        {
            return await this.GetCompanyRuns(currentRun.CompanyFrequencyId, RunStatus.Closed)
                .AnyAsync(_ => (_.PeriodEndDate == currentRun.PeriodEndDate && _.OrderNumber > currentRun.OrderNumber)
                               || _.PeriodEndDate > currentRun.PeriodEndDate);
        }

        protected async Task<CompanyRun> GetLastClosedRunTaxYearAsync(long frequencyId, int countryId, RunType? runType)
        {
            // Get the TaxYearEndDate for the country.
            var taxYearEndDate = await this.GetTaxYearEndDateAsync(countryId);

            // Generate the PeriodCode from the TaxYearEndDate.
            var periodCode = this.GeneratePeriodCode(taxYearEndDate);

            // Retrieve the last closed company run.
            return await this.GetLastClosedRunAsync(frequencyId, periodCode, runType);
        }

        protected Task<DateTime> GetTaxYearEndDateAsync(int countryId)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"TaxYearEndDate:CountryId:{countryId}",
                () => this.readOnlyContext.Set<CountryTaxYear>()
                    .TagWithSource()
                    .Where(_ => _.CountryId == countryId && _.YearEndDate < DateTime.Today)
                    .OrderByDescending(_ => _.YearEndDate)
                    .Select(_ => _.YearEndDate)
                    .FirstOrDefaultAsync());
        }

        private IQueryable<CountryTaxYear> GetCountryTaxYears(int countryId)
        {
            return this.readOnlyContext.Set<CountryTaxYear>()
                .TagWithSource()
                .Where(_ => _.CountryId == countryId);
        }

        private string GeneratePeriodCode(DateTime taxYearEndDate)
        {
            return $"{taxYearEndDate.Year}{taxYearEndDate.Month}";
        }

        private Task<CompanyRun> GetLastClosedRunAsync(long frequencyId, string periodCode, RunType? runType)
        {
            return this.readOnlyContext.Set<CompanyRun>().TagWithSource()
                .Where(_ => _.PeriodCode == periodCode && _.CompanyFrequencyId == frequencyId && (runType == null || _.RunType == runType))
                .OrderByDescending(_ => _.PeriodStartDate)
                .ThenByDescending(_ => _.OrderNumber)
                .FirstOrDefaultAsync();
        }

        private async Task OpenFutureRunAsync(long companyId, CompanyRun entity, DateTime firstMonthOfNextTaxYear)
        {
            //Dont process inactive company
            var isInactive = await this.companyService.IsInActiveAsync(companyId);

            if (isInactive)
            {
                return;
            }

            var frequency = await this.GetCompanyRuns(entity.CompanyFrequencyId)
                .Where(_ => _.RunId == entity.RunId)
                .Select(_ => _.CompanyFrequency)
                .FirstOrDefaultAsync();

            if (frequency == null)
            {
                return;
            }

            frequency.PeriodStartDate = firstMonthOfNextTaxYear;

            var preview = await this.companyFrequencyService.CreateCompanyRunsAsync(frequency, true, false);

            if (preview.Count == 0)
            {
                return;
            }

            if (await this.IsValidateBeforeCreateNewRun(preview, entity, frequency))
            {
                await this.companyFrequencyService.CreateCompanyRunsAsync(frequency, false, false);
            }
        }

        private async Task<bool> IsValidateBeforeCreateNewRun(List<CompanyRunPreview> preview, CompanyRun entity, CompanyRunFrequency frequency)
        {
            var closedRuns = await this.GetCompanyRuns(entity.CompanyFrequencyId)
                .TagWithSource()
                .Where(_ => _.Status == RunStatus.Closed && _.PayDate < DateTime.Now.Date).ToListAsync();

            foreach (var run in preview)
            {
                var runChecker = preview.Count(_ =>
                    (_.PayDate > run.PayDate &&
                        ((_.PeriodStartDate < run.PeriodStartDate) ||
                        (_.PeriodStartDate == run.PeriodStartDate && _.OrderNumber < run.OrderNumber)))
                    ||
                    (_.PayDate < run.PayDate &&
                        ((_.PeriodStartDate > run.PeriodStartDate) ||
                        (_.PeriodStartDate == run.PeriodStartDate && _.OrderNumber > run.OrderNumber)))
                );

                if (runChecker > 0)
                {
                    //"Pay date cannot be after a later run or before a previous run.")
                    return false;
                }

                var closeRunsCheck = closedRuns.Count(_ => run.PayDate < _.PayDate);
                if (closeRunsCheck > 0)
                {
                    //"Pay date can not be before another closed run's pay date");
                    return false;
                }

                if (frequency.TransferNetPay.HasValue && frequency.TransferNetPay.Value == 1)
                {
                    if (run.PayDate < DateTime.Now.Date)
                    {
                        //"The pay day selected must be greater than today's date. Please change your pay day or your first run period");
                        return false;
                    }
                }
            }

            return true;
        }

        public async Task<bool> IsSkipChecksAsync(long companyId, CompanyRun entity, bool isBureau)
        {
            var skipChecks = isBureau;

            if (!skipChecks)
            {
                var prevTaxPeriodCode = await this.GetPrevTaxPeriodCodeAsync(companyId, entity.CompanyFrequencyId, this.tenantProvider.GetUserType());
                if (prevTaxPeriodCode == entity.PeriodCode)
                {
                    skipChecks = true;
                }
            }

            return skipChecks;
        }

        private Task<long> GetRunIdForPeriodAsync(long frequencyId, string periodCode)
        {
            return this.Repository.Context.Set<CompanyRun>().TagWithSource()
                .AsNoTracking()
                .Where(_ => _.CompanyFrequencyId == frequencyId && _.PeriodCode == periodCode)
                .OrderBy(_ => _.PeriodCode)
                    .ThenBy(_ => _.PeriodEndDate)
                        .ThenBy(_ => _.OrderNumber)
                .Select(_ => _.RunId)
                .FirstOrDefaultAsync();
        }

        public virtual async Task<bool> HasImpactOnSubsequentRunsAsync(long companyId, CompanyRun run)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);

            var taxYearEndDate = await this.GetTaxYearEndDateAsync(countryId, run);

            return run.RunId == 0
                ? await this.IsNewInterimRunWithImpactAsync(run, taxYearEndDate)
                : await this.IsClosedRunWithImpactAsync(run, taxYearEndDate);
        }

        private Task<DateTime> GetTaxYearEndDateAsync(int countryId, CompanyRun run)
        {
            return this.GetCountryTaxYears(countryId).TagWithSource()
                .Where(_ => run.PeriodStartDate >= _.YearStartDate && run.PeriodEndDate <= _.YearEndDate)
                .Select(_ => _.YearEndDate)
                .FirstOrDefaultAsync();
        }

        private async Task<bool> IsNewInterimRunWithImpactAsync(CompanyRun run, DateTime taxYearEndDate)
        {
            if (run.RunType != RunType.Interim || !await this.IsMainRunClosedForRunPeriodAsync(run.CompanyId, run.PeriodStartDate, run.PeriodEndDate))
            {
                return false;
            }

            return await this.IsNotLastRunInPayPeriodAsync(run)
                || await this.HasMoreMainRunsInTaxYear(run, taxYearEndDate);
        }

        private async Task<bool> IsClosedRunWithImpactAsync(CompanyRun run, DateTime taxYearEndDate)
        {
            var previousStatus = await this.GetRunStatusAsync(run.RunId);
            if (previousStatus != RunStatus.Closed && run.Status == RunStatus.Closed)
            {
                return await this.IsEarlierPayPeriodWithClosedRunsAsync(run, taxYearEndDate)
                    || await this.IsPrecedingOrderNumberWithClosedRunsAsync(run);
            }

            var prevOrder = await this.GetPreviousOrderNumberAsync(run.RunId);
            return prevOrder != run.OrderNumber
                && await this.IsPrecedingOrderNumberWithClosedRunsAsync(run);
        }

        private Task<bool> IsEarlierPayPeriodWithClosedRunsAsync(CompanyRun run, DateTime taxYearEndDate)
        {
            //The run belongs to a pay period that is earlier than another pay period with already closed runs
            return this.GetCompanyRuns(run.CompanyFrequencyId).TagWithSource()
                .AnyAsync(_ => _.PeriodEndDate > run.PeriodEndDate
                    && _.PeriodEndDate <= taxYearEndDate
                    && _.Status == RunStatus.Closed
                    && _.RunId != run.RunId);
        }

        private Task<bool> IsPrecedingOrderNumberWithClosedRunsAsync(CompanyRun run)
        {
            //The run order precedes already closed runs within the same pay period
            return this.GetCompanyRuns(run.CompanyFrequencyId).TagWithSource()
                .AnyAsync(_ => _.OrderNumber > run.OrderNumber
                    && _.PeriodStartDate == run.PeriodStartDate
                    && _.PeriodEndDate == run.PeriodEndDate
                    && _.Status == RunStatus.Closed
                    && _.RunId != run.RunId);
        }

        private Task<bool> HasMoreMainRunsInTaxYear(CompanyRun run, DateTime taxYearEndDate)
        {
            return this.GetCompanyRuns(run.CompanyFrequencyId).TagWithSource()
                .AnyAsync(_ => _.RunType == RunType.Main
                    && _.PeriodEndDate > run.PeriodEndDate
                    && _.PeriodEndDate <= taxYearEndDate);
        }

        private Task<bool> IsNotLastRunInPayPeriodAsync(CompanyRun run)
        {
            return this.GetCompanyRuns(run.CompanyFrequencyId).TagWithSource()
                .AnyAsync(_ => _.OrderNumber > run.OrderNumber
                    && _.PeriodStartDate == run.PeriodStartDate
                    && _.PeriodEndDate == run.PeriodEndDate);
        }

        protected Task<bool> IsMainRunClosedForRunPeriodAsync(long frequencyId, DateTime startDate, DateTime endDate)
        {
            return this.GePeriodMainRun(frequencyId, startDate, endDate)
                .AnyAsync(_ => _.Status == RunStatus.Closed);
        }

        public Task<bool> IsAfterMainRunOrderAsync(CompanyRun run)
        {
            return this.GePeriodMainRun(run.CompanyFrequencyId, run.PeriodStartDate, run.PeriodEndDate)
                .AnyAsync(_ => _.OrderNumber < run.OrderNumber);
        }

        private IQueryable<CompanyRun> GePeriodMainRun(long frequencyId, DateTime startDate, DateTime endDate)
        {
            return this.GetCompanyRuns(frequencyId).TagWithSource()
                .Where(_ => _.RunType == RunType.Main
                    && _.PeriodStartDate == startDate
                    && _.PeriodEndDate == endDate);
        }

        public async Task<bool> ExistsOpenRunBeforeAsync(CompanyRun run)
        {
            return await this.GetCompanyRuns(run.CompanyFrequencyId).TagWithSource()
                .AsNoTracking()
                .AnyAsync(_ =>
                    _.RunId != run.RunId &&
                    _.Status == RunStatus.Open &&
                    _.PeriodStartDate > run.PeriodStartDate);
        }
    }
}