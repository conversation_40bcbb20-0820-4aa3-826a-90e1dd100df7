namespace PaySpace.Venuta.Services.Company
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.External;

    public interface IEmployeeEOnboardingDetailsService : IGenericService<EmployeeEOnboardingDetails>
    {
        Task<bool> CheckEmailExistsAsync(string email, long? employeeDetailId);
    }

    public class EmployeeEOnboardingDetailsService : GenericService<EmployeeEOnboardingDetails>, IEmployeeEOnboardingDetailsService
    {
        private const string EOnboardingUrl = "about:blank";

        private readonly IEmailService emailService;
        private readonly IEmployeeService employeeService;
        private readonly ICompanyService companyService;
        private readonly IUserRegionService userRegionService;

        public EmployeeEOnboardingDetailsService(
            IDbContextRepository<EmployeeEOnboardingDetails> repository,
            IEmailService emailService,
            IEmployeeService employeeService,
            ICompanyService companyService,
            IUserRegionService userRegionService)
            : base(repository)
        {
            this.emailService = emailService;
            this.employeeService = employeeService;
            this.companyService = companyService;
            this.userRegionService = userRegionService;
        }

        public override async Task<EmployeeEOnboardingDetails> AddAsync(EmployeeEOnboardingDetails entity)
        {
            var company = await this.Repository.Context.Set<Company>()
                .Where(_ => _.CompanyId == entity.CompanyId)
                .Select(_ => new { _.GenerateEmpNo, _.CompanyEmployeeNumberPrefix, _.CompanyEmpNoGeneratedLength })
                .SingleOrDefaultAsync();

            if (company.GenerateEmpNo)
            {
                await this.GenerateEmployeeNumberAsync(entity, company.CompanyEmployeeNumberPrefix, company.CompanyEmpNoGeneratedLength);
            }

            entity.EOnboardingStatusId = entity.SendLink ? (int)OnboardingStatus.Pending : (int)OnboardingStatus.Draft;
            await base.AddAsync(entity);
            await this.userRegionService.UpdateEmailAsync(null, entity.Email);

            if (entity.SendLink)
            {
                await this.SendOnboardingEmailAsync(entity.Email, entity.Firstname, entity.CompanyId);
            }

            return entity;
        }

        public override async Task<EmployeeEOnboardingDetails> UpdateAsync(EmployeeEOnboardingDetails entity)
        {
            var isEmailPreviouslySent = entity.EOnboardingStatusId == (int)OnboardingStatus.Pending;

            if ((entity.ResendOnboardingEmail.HasValue && entity.ResendOnboardingEmail.Value) || (entity.SendLink && !isEmailPreviouslySent))
            {
                entity.EOnboardingStatusId = (int)OnboardingStatus.Pending;
                await this.SendOnboardingEmailAsync(entity.Email, entity.Firstname, entity.CompanyId);
            }

            await base.UpdateAsync(entity);

            return entity;
        }

        public async Task<bool> CheckEmailExistsAsync(string email, long? employeeDetailId)
        {
            return await this.employeeService.CheckEmployeeEmailExists(email, default)
                || await this.Repository.Set.AnyAsync(_ => _.Email == email && _.EmployeeDetailsId != employeeDetailId)
                || await this.userRegionService.DoesEmailExistAsync(email);

        }

        private async Task SendOnboardingEmailAsync(string email, string firstName, long companyId)
        {
            await this.emailService.SendEOnboardingEmailAsync(
                new Uri(EOnboardingUrl),
                email,
                firstName,
                await this.companyService.GetCompanyNameAsync(companyId));
        }

        private async Task GenerateEmployeeNumberAsync(EmployeeEOnboardingDetails entity, string prefix, long? companyEmpNoGeneratedLength)
        {
            entity.EmployeeNumber = string.Empty;
            var prefixLength = companyEmpNoGeneratedLength != default ? companyEmpNoGeneratedLength : 4;

            while (string.IsNullOrEmpty(entity.EmployeeNumber) || await this.employeeService.CheckEmployeeNumberExistsAsync(entity.CompanyId, entity.EmployeeNumber))
            {

                var companyEmployeeNumber = await this.Repository.Context.Set<CompanyEmployeeNumber>().FirstOrDefaultAsync(_ => _.CompanyId == entity.CompanyId);
                var employeeNumber = (companyEmployeeNumber?.LastEmployeeNumber ?? 0) + 1;

                entity.EmployeeNumber = prefix + employeeNumber.ToString("D" + prefixLength);

                if (companyEmployeeNumber == null)
                {
                    companyEmployeeNumber = new CompanyEmployeeNumber
                    {
                        CompanyId = entity.CompanyId,
                        LastEmployeeNumber = employeeNumber,
                        LastJobNumber = 0
                    };

                    this.Repository.Context.Add(companyEmployeeNumber);
                }
                else
                {
                    companyEmployeeNumber.LastEmployeeNumber = employeeNumber;
                }
            }
        }
    }
}
