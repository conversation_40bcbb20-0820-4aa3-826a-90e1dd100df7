namespace PaySpace.Venuta.Services.Country.BR
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    public static class BrazilLeaveConstants
    {
        public static class LeaveSchemeDescription
        {
            public const string FeriasClt = "Férias CLT";
            public const string Recesso = "Recesso";
            public const string Autonomo = "Autônomo";
        }

        public static class LeaveSchemeCode
        {
            public const string FeriasClt = "001";
            public const string Recesso = "002";
            public const string Autonomo = "003";
        }

        public static class AnnualDescription
        {
            public const string FeriasPeriodoConcessivo = "Férias Período Concessivo";
            public const string RecessoParaEstagiarios = "Recesso para Estagiários";
            public const string Autonomos = "Autônomos";
        }

        public static class SpecialDescription
        {
            public const string FeriasPeriodoAquisitivo = "Férias Período Aquisitivo";
        }
    }
}
