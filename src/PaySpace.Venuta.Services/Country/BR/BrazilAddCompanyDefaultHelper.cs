namespace PaySpace.Venuta.Services.Country.BR
{
    using System.Collections.Generic;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    internal static class BrazilAddCompanyDefaultHelper
    {
        public static IEnumerable<CompanyLeaveScheme> GetLeaveSchemeParameters(long companyId)
        {
            var schemeNames = new[]
            {
                BrazilLeaveConstants.LeaveSchemeDescription.FeriasClt,
                BrazilLeaveConstants.LeaveSchemeDescription.Recesso,
                BrazilLeaveConstants.LeaveSchemeDescription.Autonomo
            };

            var schemeCodes = new[]
            {
                BrazilLeaveConstants.LeaveSchemeCode.FeriasClt,
                BrazilLeaveConstants.LeaveSchemeCode.Recesso,
                BrazilLeaveConstants.LeaveSchemeCode.Autonomo
            };

            // Map names and codes
            return schemeNames.Select((schemeName, idx) =>
                CreateLeaveScheme(schemeName, schemeCodes[idx], companyId));
        }

        private static CompanyLeaveScheme CreateLeaveScheme(string schemeName, string schemeCode, long companyId)
        {
            return new CompanyLeaveScheme
            {
                SchemeName = schemeName,
                SchemeCode = schemeCode,
                CompanyId = companyId,
                CompanyLeaveSetups = CreateLeaveSetups(schemeName)
            };
        }

        private static List<CompanyLeaveSetup> CreateLeaveSetups(string schemeName)
        {
            return schemeName switch
            {
                BrazilLeaveConstants.LeaveSchemeDescription.FeriasClt => new List<CompanyLeaveSetup>
                {
                    CreateFeriasPeriodoConcessivoAnnualSetup(),
                    CreateFeriasPeriodoAquisitivoSpecialSetup()
                },

                BrazilLeaveConstants.LeaveSchemeDescription.Recesso => new List<CompanyLeaveSetup>
                {
                    CreateRecessoAnnualSetup()
                },

                BrazilLeaveConstants.LeaveSchemeDescription.Autonomo => new List<CompanyLeaveSetup>
                {
                    CreateAutonomoAnnualSetup()
                },

                _ => new List<CompanyLeaveSetup>()
            };
        }

        private static CompanyLeaveSetup CreateFeriasPeriodoConcessivoAnnualSetup()
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Annual,
                LeaveDescription = BrazilLeaveConstants.AnnualDescription.FeriasPeriodoConcessivo,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.NonAccumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 0,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 3,
                        AccrualOptionId = 1,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                        ForfeitPeriod = 1,
                        LeaveForfeitPeriodId = (int)LeaveForfeitPeriod.Year,
                        CarryOverDays = 0,
                        EffectiveDateForfeit = LeaveSchemeConstants.EffectiveDateForfeit.GroupJoinDate,
                        NegativeLeaveAmount = 0,
                        UpfrontProRateOptions = false,
                        ApplyServiceLength = false,
                        ApplyEmployeeDefined = false,
                        ApplyGradeBands = false,
                        DisplayBalanceESS = false,
                        ShowOnPaySlip = false,
                        DoNotCalculateBceaValue = false,
                        IncludePendingApps = false,
                        AttachmentMandatory = false,
                        OffDays = 2,
                        IncludePH = true,
                        MaxBalance = 30
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateFeriasPeriodoAquisitivoSpecialSetup()
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Special,
                LeaveDescription = BrazilLeaveConstants.SpecialDescription.FeriasPeriodoAquisitivo,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.NonAccumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 30,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 3,
                        AccrualOptionId = 2,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                        ForfeitPeriod = 1,
                        LeaveForfeitPeriodId = (int)LeaveForfeitPeriod.Year,
                        CarryOverDays = 30,
                        EffectiveDateForfeit = LeaveSchemeConstants.EffectiveDateForfeit.GroupJoinDate,
                        NegativeLeaveAmount = 0,
                        ApplyServiceLength = false,
                        ApplyEmployeeDefined = false,
                        ApplyGradeBands = false,
                        DisplayBalanceESS = true,
                        ShowOnPaySlip = false,
                        DoNotCalculateBceaValue = false,
                        IncludePendingApps = false,
                        AttachmentMandatory = false,
                        MaxBalance = 30,
                        AccrualEngagementDay = 15
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateRecessoAnnualSetup()
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Annual,
                LeaveDescription = BrazilLeaveConstants.AnnualDescription.RecessoParaEstagiarios,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.Accumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 2.5m,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 2,
                        AccrualOptionId = 2,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                        CarryOverDays = 30,
                        NegativeLeaveAmount = 0,
                        ApplyServiceLength = false,
                        ApplyEmployeeDefined = false,
                        ApplyGradeBands = false,
                        DisplayBalanceESS = false,
                        ShowOnPaySlip = false,
                        DoNotCalculateBceaValue = false,
                        IncludePendingApps = false,
                        AttachmentMandatory = false,
                        IncludePH = true,
                        MaxBalance = 30
                    }
                }
            };
        }

        private static CompanyLeaveSetup CreateAutonomoAnnualSetup()
        {
            return new CompanyLeaveSetup
            {
                OrderNumber = 1,
                LeaveType = LeaveType.Annual,
                LeaveDescription = BrazilLeaveConstants.AnnualDescription.Autonomos,
                LeaveDetails = new List<CompanyLeaveDetail>
                {
                    new()
                    {
                        CompanyLeaveSetupType = CompanyLeaveSetupType.Accumulative,
                        EffectiveDate = LeaveSchemeConstants.MinimumEffectiveDate,
                        Accrual = 0,
                        AccrualPeriodValue = 1,
                        AccrualPeriodId = 3,
                        AccrualOptionId = 1,
                        LeaveAccrualValueId = (int)LeaveAccrualValue.Day,
                        CarryOverDays = 30,
                        UpfrontProRateOptions = false,
                        ApplyServiceLength = false,
                        ApplyEmployeeDefined = false,
                        ApplyGradeBands = false,
                        DisplayBalanceESS = false,
                        ShowOnPaySlip = false,
                        DoNotCalculateBceaValue = false,
                        IncludePendingApps = false,
                        AttachmentMandatory = false
                    }
                }
            };
        }
    }
}
