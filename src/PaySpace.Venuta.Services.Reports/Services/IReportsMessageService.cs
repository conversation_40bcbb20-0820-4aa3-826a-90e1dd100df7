namespace PaySpace.Venuta.Services.Reports
{
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;

    public interface IReportsMessageService
    {
        Task PublishNotificationMessage(long userId, string notificationHash, string reportName);

        Task<ReportStatusResponse> PublishExportReportMessage(long userId, long companyId, string notificationHash, string reportUrl, IDictionary<string, object> parameters, string format, HostString? tenant, string accessToken);

        Task<IDictionary<string, string>> GetReportHistoryMetadataAsync(long userId, IDictionary<string, object> parameters);
    }
}
