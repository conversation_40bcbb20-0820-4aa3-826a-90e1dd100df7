namespace PaySpace.Venuta.Services.Reports.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using static PaySpace.Venuta.Services.Reports.ReportConstants;

    public static class ReportUtils
    {
        public static readonly string[] SystemReportsList = new string[] { SystemReports.TaxBreakdown };

        public static bool IsCustomReport(string reportId)
        {
            return Guid.TryParse(reportId, out _);
        }

        public static bool IsCustomPayslip(string reportId)
        {
            return IsNewCustomPayslip(reportId) || IsOldCustomPayslip(reportId);
        }

        // TODO: Remove this once everyone has migrated to single file.
        public static bool IsOldCustomPayslip(string reportId)
        {
            return reportId == ReportConstants.CustomPayslips.PayslipHeaderReportId
               || reportId == ReportConstants.CustomPayslips.PayslipDetailReportId
               || (long.TryParse(reportId, out var reportHeaderId) && reportHeaderId == ReportConstants.CustomPayslips.ReportHeaderId);
        }

        public static bool IsNewCustomPayslip(string reportId)
        {
            return reportId == "Payslip";
        }

        public static bool IsStandardReport(string reportId)
        {
            // TODO: we need to confirm there is a matching SQL entry.
            return !IsCustomReport(reportId) && !IsCustomPayslip(reportId) && !IsNewCustomPayslip(reportId) && !IsSystemReport(reportId);
        }

        public static bool IsEnabled(IDictionary<string, string> metadata)
        {
            // By default if we dont find enabled metadata then the report is enabled.
            return !metadata.TryGetValue(ReportConstants.Metadata.Enabled, out var enabled) || bool.Parse(enabled);
        }

        public static bool IsDesignable(IDictionary<string, string> metadataValues)
        {
            // By default if we dont find designable metadata then the report is designable.
            return !metadataValues.TryGetValue(ReportConstants.Metadata.Designable, out var designable) || bool.Parse(designable);
        }

        public static bool IsSystemReport(string? reportId)
        {
            return !string.IsNullOrWhiteSpace(reportId) && SystemReportsList.Contains(reportId);
        }
    }
}