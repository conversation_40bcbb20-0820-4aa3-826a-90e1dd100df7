namespace PaySpace.Venuta.Services.Reports.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Web;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Storage;

    public interface ICustomReportListingService
    {
        Task<IList<CustomReport>> GetCustomReportsAsync(
            HostString tenant,
            ISecurityProfile profile,
            long companyId,
            long frequencyId,
            IList<int> reportCategoryIds,
            bool includeSubCategories,
            string accessToken,
            IList<string> standardReportNameList,
            CancellationToken cancellationToken);
    }

    public class CustomReportListingService : BaseReportListingService, ICustomReportListingService
    {
        private readonly IReportStorageService reportStorageService;
        private readonly IReportUrlResolver reportUrlResolver;
        private readonly ICompanyService companyService;
        private readonly IDistributedCache distributedCache;

        public CustomReportListingService(
            IReportStorageService reportStorageService,
            IReportUrlResolver reportUrlResolver,
            ICompanyService companyService,
            IDistributedCache distributedCache,
            IReportUserSecurityService reportUserSecurityService)
            : base(reportStorageService, reportUserSecurityService)
        {
            this.reportStorageService = reportStorageService;
            this.reportUrlResolver = reportUrlResolver;
            this.companyService = companyService;
            this.distributedCache = distributedCache;
        }

        public async Task<IList<CustomReport>> GetCustomReportsAsync(
            HostString tenant,
            ISecurityProfile profile,
            long companyId,
            long frequencyId,
            IList<int> reportCategoryIds,
            bool includeSubCategories,
            string accessToken,
            IList<string> standardReportNameList,
            CancellationToken cancellationToken)
        {
            // Cache for a little time to handle multiple requests from individual report categories loading their reports
            var customReports = await this.distributedCache.GetOrCreateAsync(
                ReportConstants.CacheKeys.CustomReportsList(companyId, frequencyId, includeSubCategories),
                () => this.GetAvailableReportsAsync(tenant, companyId, frequencyId, includeSubCategories, profile, standardReportNameList, cancellationToken),
                new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1) });

            customReports = customReports
                .Where(
                    _ => reportCategoryIds.Contains(_.ReportCategoryId)
                         && (_.ReportCategoryId != ReportConstants.MyReportsCategoryId || _.UserId == profile.UserId))
                .ToList();

            foreach (var customReport in customReports)
            {
                var uriBuilder = new UriBuilder(customReport.ViewerUri);
                var query = HttpUtility.ParseQueryString(uriBuilder.Query);
                query["access_token"] = accessToken;
                uriBuilder.Query = query.ToString();
                customReport.ViewerUri = uriBuilder.ToString();
            }

            return customReports;
        }

        private async Task<IList<CustomReport>> GetAvailableReportsAsync(
            HostString tenant,
            long companyId,
            long frequencyId,
            bool includeSubCategories,
            ISecurityProfile profile,
            IList<string> standardReportNameList,
            CancellationToken cancellationToken)
        {
            var companySummary = await this.companyService.GetCompanySummary(companyId);
            var reportListingResults = await this.GetReportListingsAsync(ReportConstants.CustomReports.ParentFolderPath, companySummary, profile, includeSubCategories, cancellationToken);

            var customReports = new List<CustomReport>();
            foreach (var reportListingResult in reportListingResults)
            {
                if (standardReportNameList.Contains(reportListingResult.ReportSource.ReportPath))
                {
                    continue;
                }

                var customReport = await this.GetCustomReportModel(reportListingResult, tenant, companyId, frequencyId);
                if (customReport is not null)
                {
                    customReports.Add(customReport);
                }
            }

            return customReports;
        }

        private async Task<CustomReport?> GetCustomReportModel(
            ReportListingResult reportListingResult,
            HostString tenant,
            long companyId,
            long frequencyId)
        {
            var reportId = Guid.Parse(reportListingResult.ReportId);
            var reportModel = new CustomReport(reportListingResult.ReportSource)
            {
                ReportId = reportId,
                ViewerUri = $"{new Uri(this.reportUrlResolver.Resolve(tenant), "/viewer")}?companyId={companyId}&frequencyId={frequencyId}&reportId={reportId}&tenant={tenant}&includeDisabledReports=true"
            };

            var metaData = await this.reportStorageService.GetMetadataValuesAsync(reportListingResult.ReportSource.ReportPath);
            if (metaData is null)
            {
                return null;
            }

            var tags = reportListingResult.Tags;
            if (tags.TryGetValue(ReportConstants.Metadata.Description, out var description))
            {
                reportModel.ReportDescription = this.GetDecodedMetadataValue(metaData, ReportConstants.Tags.IsDescriptionEncoded, description);
            }

            if (tags.TryGetValue(ReportConstants.Metadata.ReportCategory, out var categoryId))
            {
                reportModel.ReportCategoryId = int.Parse(categoryId);
            }

            if (tags.TryGetValue(ReportConstants.Metadata.TemplateDisplayName, out var name))
            {
                reportModel.ReportName = this.GetDecodedMetadataValue(metaData, ReportConstants.Tags.IsTemplateDisplayNameEncoded, name);
            }

            if (tags.TryGetValue(ReportConstants.Metadata.UserId, out var userId))
            {
                reportModel.UserId = long.Parse(userId);
            }

            if (tags.TryGetValue(ReportConstants.Metadata.ReportSubCategory, out var reportSubCategoryId))
            {
                reportModel.ReportSubCategoryId = int.TryParse(reportSubCategoryId, out var result) ? result : null;
            }

            reportModel.Enabled = ReportUtils.IsEnabled(metaData);
            reportModel.Designable = ReportUtils.IsDesignable(metaData);

            return reportModel;
        }

        private string? GetDecodedMetadataValue(IDictionary<string, string> metadata, string isEncodedKey, string value)
        {
            return metadata.TryGetValue(isEncodedKey, out var isEncoded) && !string.IsNullOrEmpty(isEncoded) ? value.ConvertFromBase64() : value;
        }
    }
}