namespace PaySpace.Venuta.Services.Reports.Services
{
    using System;
    using System.Collections.Generic;
    using System.Collections.Immutable;
    using System.IO;
    using System.Linq;
    using System.Security.Claims;
    using System.Text.RegularExpressions;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Web;

    using DevExpress.XtraPrinting;
    using DevExpress.XtraReports.UI;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Localization;
    using Microsoft.Extensions.Primitives;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Message;
    using PaySpace.Venuta.Messaging.Notifications;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.Extensions;
    using PaySpace.Venuta.Services.Reports.Messages;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.Models.ContextLevelReportSources;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Storage;

    public class ReportSavingService : IReportSavingService
    {
        private readonly IReportStorageService reportStorage;
        private readonly IReportUserSecurityService reportUserSecurityService;
        private readonly IDistributedCache distributedCache;
        private readonly IReportRetrievalService reportRetrievalService;
        private readonly IMessageBus messageBus;
        private readonly IStringLocalizer stringLocalizer;
        private readonly ICompanyService companyService;
        private readonly IReportSecurityService reportSecurityService;
        private readonly IUserService userService;
        private readonly ISecurityProfileFactory securityProfileFactory;

        public ReportSavingService(
            IReportStorageService reportStorage,
            IReportUserSecurityService reportUserSecurityService,
            IDistributedCache distributedCache,
            IReportRetrievalService reportRetrievalService,
            IMessageBus messageBus,
            IStringLocalizerFactory stringLocalizerFactory,
            ICompanyService companyService,
            IReportSecurityService reportSecurityService,
            IUserService userService,
            ISecurityProfileFactory securityProfileFactory)
        {
            this.reportStorage = reportStorage;
            this.reportUserSecurityService = reportUserSecurityService;
            this.distributedCache = distributedCache;
            this.reportRetrievalService = reportRetrievalService;
            this.messageBus = messageBus;
            this.companyService = companyService;
            this.reportSecurityService = reportSecurityService;
            this.userService = userService;
            this.securityProfileFactory = securityProfileFactory;
            this.stringLocalizer = stringLocalizerFactory.Create(SystemAreas.Report.Area, null);
        }

        public ReportSaveContext GetSaveContext(HttpContext httpContext, XtraReport report)
        {
            var httpHeaders = httpContext.Request.Headers;
            var reportLevel = httpHeaders["levelselected"];
            var companies = httpHeaders["companiesselected"];
            var country = httpHeaders["countriesselected"];
            var agencyId = httpHeaders["agencyid"];
            var companyGroupId = httpHeaders["companygroupid"];
            var reportId = !string.IsNullOrEmpty(httpHeaders["subReportPath"]) ? Path.GetFileNameWithoutExtension(httpHeaders["subReportPath"]) : httpHeaders["reportid"].ToString();
            var isSystemReport = ReportUtils.IsSystemReport(reportId);

            var currentCompanyId = long.TryParse(httpHeaders["companyid"], out var companyId) ? companyId : throw new ArgumentException("CompanyId value is not valid");
            var currentFrequencyId = long.TryParse(httpHeaders["frequencyid"], out var frequencyId) ? frequencyId : throw new ArgumentException("FrequencyId value is not valid");
            var reportContextLevel = Enum.Parse<ReportContextLevel>(reportLevel!);
            var reportType = isSystemReport ? ReportType.SystemReport : report.GetReportType();

            return new ReportSaveContext
            {
                HttpHeaders = httpHeaders,
                ReportType = reportType,
                ReportContextLevel = reportContextLevel,
                AgencyId = long.TryParse(agencyId, out var i) ? i : default,
                CompanyGroupId = long.TryParse(companyGroupId, out var t) ? t : default,
                CurrentCompanyId = currentCompanyId,
                CurrentFrequencyId = currentFrequencyId,
                CompanyIds = string.IsNullOrEmpty(companies)
                    ? [currentCompanyId]
                    : companies.ToString().Split(",").Select(long.Parse).ToList(),
                TaxCountryIds = string.IsNullOrEmpty(country)
                    ? default
                    : country.ToString().Split(",").Select(long.Parse).ToList(),
                ReportId = reportId,
                UserId = httpContext.User.GetUserId(),
                Metadata = this.GetMetadata(
                    httpContext.User.GetUserId(),
                    reportType,
                    httpHeaders)
            };
        }

        public async Task SaveReportAsync(long userId, ReportSaveContext reportSaveContext, XtraReport report, CancellationToken cancellationToken)
        {
            var tags = GetTags(reportSaveContext);

            await this.ValidateSaveReportAccess(reportSaveContext, cancellationToken);
            SetExportSettings(report, reportSaveContext);

            if (reportSaveContext.Metadata.TryGetValue(ReportConstants.Tags.TemplateDisplayName, out var templateDisplayName))
            {
                report.DisplayName = templateDisplayName.ConvertFromBase64();
            }

            var reportId = reportSaveContext.ReportId;
            var parentFolderPath = ReportUtils.IsCustomPayslip(reportId)
                ? ReportConstants.CustomPayslips.ParentFolderPath
                : ReportConstants.CustomReports.ParentFolderPath;

            if (reportSaveContext.ReportContextLevel is ReportContextLevel.Bureau or ReportContextLevel.BureauTaxCountry)
            {
                parentFolderPath = reportSaveContext.ReportType == ReportType.SystemReport ? string.Empty : parentFolderPath;

                if (reportSaveContext.TaxCountryIds != null && reportSaveContext.TaxCountryIds.Count > 0)
                {
                    foreach (var countryId in reportSaveContext.TaxCountryIds)
                    {
                        var bureauTaxCountryReportSource = BureauTaxCountryReportSource.CreateForReport(parentFolderPath, (int)countryId, reportId);
                        await this.reportSecurityService.UpsertReportSecurityAsync(reportSaveContext.Metadata, bureauTaxCountryReportSource.ReportPath, countryId, reportId);
                        await this.SaveReportAsync(reportSaveContext, bureauTaxCountryReportSource, report, tags, countryId, cancellationToken);
                    }
                }
                else
                {
                    var bureauReportSource = BureauReportSource.CreateForReport(parentFolderPath, reportId);
                    await this.reportSecurityService.UpsertReportSecurityAsync(reportSaveContext.Metadata, bureauReportSource.ReportPath, null, reportId);
                    await this.SaveReportAsync(reportSaveContext, bureauReportSource, report, tags, 0, cancellationToken);
                }
            }

            if (reportSaveContext.ReportContextLevel is ReportContextLevel.Agency or ReportContextLevel.AgencyTaxCountry)
            {
                var agencyId = reportSaveContext.AgencyId!.Value;
                if (reportSaveContext.TaxCountryIds != null && reportSaveContext.TaxCountryIds.Any())
                {
                    foreach (var countryId in reportSaveContext.TaxCountryIds)
                    {
                        var agencyTaxCountryReportSource = AgencyTaxCountryReportSource.CreateForReport(agencyId, parentFolderPath, (int)countryId, reportId);
                        await this.SaveReportAsync(reportSaveContext, agencyTaxCountryReportSource, report, tags, countryId, cancellationToken);
                    }
                }
                else
                {
                    var agencyReportSource = AgencyReportSource.CreateForReport(agencyId, parentFolderPath, reportId);
                    await this.SaveReportAsync(reportSaveContext, agencyReportSource, report, tags, agencyId, cancellationToken);
                }
            }
            else if (reportSaveContext.ReportContextLevel == ReportContextLevel.CompanyGroup)
            {
                var companyGroupReportSource = CompanyGroupReportSource.CreateForReport(reportSaveContext.CompanyGroupId!.Value, parentFolderPath, reportId);
                await this.SaveReportAsync(reportSaveContext, companyGroupReportSource, report, tags, reportSaveContext.CompanyGroupId.Value, cancellationToken);
            }
            else if (reportSaveContext is { ReportContextLevel: ReportContextLevel.Company, CompanyIds: not null })
            {
                foreach (var companyId in reportSaveContext.CompanyIds)
                {
                    var companyReportSource = CompanyReportSource.CreateForReport(companyId, parentFolderPath, reportId);
                    await this.SaveReportAsync(reportSaveContext, companyReportSource, report, tags, companyId, cancellationToken);
                }
            }

            await this.ClearReportCacheAsync(reportSaveContext.CurrentCompanyId, reportSaveContext.CurrentFrequencyId, cancellationToken);
        }

        public async Task UploadCustomReportAsync(ReportUploadViewModel reportUploadViewModel, long userId, long companyId, long frequencyId, XtraReport report, CancellationToken cancellationToken)
        {
            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            var reportId = report.Extensions.TryGetValue("ReportId", out var existingReportId) ? existingReportId : Guid.NewGuid().ToString();
            var isEnabled = reportUploadViewModel.ReportType == ReportType.SystemReport ? "true" : reportUploadViewModel.IsEnabled.ToString();
            var isDesignable = reportUploadViewModel.ReportType == ReportType.SystemReport ? "true" : reportUploadViewModel.IsDesignable.ToString();

            var metadata = new Dictionary<string, string>
            {
                { ReportConstants.Metadata.ReportCategory, reportUploadViewModel.ReportCategoryId.ToString() },
                { ReportConstants.Metadata.TemplateDisplayName, reportUploadViewModel.TemplateDisplayName.ConvertToBase64() },
                { ReportConstants.Metadata.Description, reportUploadViewModel.Description.ConvertToBase64() },
                { ReportConstants.Metadata.UserId, userId.ToString() },
                { ReportConstants.Metadata.ChangesMadeDescription, reportUploadViewModel.ChangedDescription.ConvertToBase64() },
                { ReportConstants.Tags.IsChangesMadeDescriptionEncoded, "true" },
                { ReportConstants.Tags.IsTemplateDisplayNameEncoded, "true" },
                { ReportConstants.Tags.IsDescriptionEncoded, "true" },
                { ReportConstants.Metadata.Enabled, isEnabled},
                { ReportConstants.Metadata.Designable, isDesignable }
            };

            if (reportUploadViewModel.Override)
            {
                var existingReportSource = await this.reportRetrievalService.GetReportSourceAsync(reportId, companyId, userId, true, null, cancellationToken);
                var existingMetadata = await this.reportStorage.GetMetadataValuesAsync(existingReportSource.ReportPath);
                if (existingMetadata != null && existingMetadata.TryGetValue(ReportConstants.Metadata.ReportListId, out var reportlistid))
                {
                    metadata.Add(ReportConstants.Metadata.ReportListId, reportlistid);
                }
            }

            var httpHeaders = new HeaderDictionary(new Dictionary<string, StringValues>());

            if (reportUploadViewModel.ReportType == ReportType.Extract)
            {
                if (reportUploadViewModel.ReportSubCategoryId.HasValue)
                {
                    metadata.Add(ReportConstants.Metadata.ReportSubCategory, reportUploadViewModel.ReportSubCategoryId.Value.ToString());
                }

                httpHeaders.Append(ReportConstants.Metadata.ExportSeparator, reportUploadViewModel.ExportSeparator);
                httpHeaders.Append(ReportConstants.Metadata.ExportQuoteStringsWithSeparators, reportUploadViewModel.ExportQuoteStringsWithSeparators.ToString());
            }

            var reportSaveContext = new ReportSaveContext
            {
                CompanyIds = reportUploadViewModel.CompanyIds ?? [companyId],
                ReportContextLevel = reportUploadViewModel.ReportContextLevel,
                CurrentCompanyId = companyId,
                CurrentFrequencyId = frequencyId,
                TaxCountryIds = reportUploadViewModel.CountryIds,
                ReportType = reportUploadViewModel.ReportType,
                UserId = userId,
                AgencyId = agencyId,
                CompanyGroupId = companyGroupId,
                ReportId = reportId,
                Metadata = metadata,
                HttpHeaders = httpHeaders
            };

            await this.SaveReportAsync(userId, reportSaveContext, report, cancellationToken);
        }

        public async Task UploadCustomPayslipAsync(ReportUploadViewModel reportUploadViewModel, long userId, long companyId, long frequencyId, XtraReport report, CancellationToken cancellationToken)
        {
            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            var reportId = report.Extensions.TryGetValue("ReportId", out var existingReportId);
            if (!reportId)
            {
                return;
            }

            var metadata = new Dictionary<string, string>
            {
                { ReportConstants.Metadata.UserId, userId.ToString() },
                { ReportConstants.Metadata.ChangesMadeDescription, reportUploadViewModel.ChangedDescription.ConvertToBase64() },
                { ReportConstants.Tags.IsChangesMadeDescriptionEncoded, "true" },
                { ReportConstants.Metadata.Enabled, reportUploadViewModel.IsEnabled.ToString() },
                { ReportConstants.Metadata.Designable, reportUploadViewModel.IsDesignable.ToString() }
            };

            var reportSaveContext = new ReportSaveContext
            {
                CompanyIds = reportUploadViewModel.CompanyIds ?? [companyId],
                ReportContextLevel = reportUploadViewModel.ReportContextLevel,
                CurrentCompanyId = companyId,
                CurrentFrequencyId = frequencyId,
                TaxCountryIds = reportUploadViewModel.CountryIds,
                ReportType = ReportType.Report,
                UserId = userId,
                AgencyId = agencyId,
                CompanyGroupId = companyGroupId,
                ReportId = existingReportId,
                Metadata = metadata,
                HttpHeaders = new HeaderDictionary(new Dictionary<string, StringValues>())
            };

            await this.SaveReportAsync(userId, reportSaveContext, report, cancellationToken);
        }

        private async Task SaveReportAsync(
            ReportSaveContext reportSaveContext,
            ReportSource reportSource,
            XtraReport report,
            IDictionary<string, string> tags,
            long relatedEntityId,
            CancellationToken cancellationToken)
        {
            this.SetRelatedEntityIdTag(tags, relatedEntityId.ToString());
            this.SetPathGroupTag(tags, reportSource.GetPathGroupTag());
            await this.SaveReportToStorageAsync(report, reportSource.ReportPath, reportSaveContext.Metadata, tags, cancellationToken);
            await this.CloneRemainingReportsAsync(reportSaveContext, reportSource, cancellationToken);
        }

        public async Task DeleteReportAsync(long userId, string reportId, long companyId, long? frequencyId, ReportContextLevel? reportContextLevel, CancellationToken cancellationToken)
        {
            if (ReportUtils.IsCustomPayslip(reportId))
            {
                throw new InvalidOperationException("Unable to delete custom payslip");
            }

            var reportInfo = await this.reportRetrievalService.GetReportInfoAsync(reportId, companyId, userId, true, reportContextLevel, null, cancellationToken);
            await this.ValidateDesignerActionAccessAsync(userId, companyId, reportInfo);

            var hasChildReportDependencies = this.reportStorage.HasDependentReports(reportInfo.ReportSource, cancellationToken);

            var reportPath = reportInfo.ReportSource.ReportPath;
            if (await this.reportStorage.ExistsAsync(reportPath, cancellationToken) && !hasChildReportDependencies)
            {
                var metadata = await this.reportStorage.GetMetadataValuesAsync(reportPath);
                await this.reportStorage.DeleteAsync(reportPath, cancellationToken);
                await this.messageBus.PublishMessageAsync(new ReportDeletedMessage
                {
                    ReportFullPath = reportPath,
                    ReportMetadata = metadata?.ToDictionary()
                }, cancellationToken);
                await this.ClearReportCacheAsync(companyId, frequencyId, cancellationToken);
            }
            else
            {
                await this.reportStorage.SetReportStatusAsync(reportPath, false, cancellationToken);
                await this.messageBus.PublishMessageAsync(new NotificationUserMessage
                {
                    UserId = userId,
                    UserMessage = new UserMessage { Description = this.stringLocalizer.GetString("lblReportDisabledHasDependenies"), MessageType = MessageType.Information }
                });
                await this.ClearReportCacheAsync(companyId, frequencyId, cancellationToken);
            }
        }

        public async Task ResetReportAsync(long userId, string reportId, long companyId, IHeaderDictionary httpHeaders, ReportContextLevel? reportContextLevel, CancellationToken cancellationToken)
        {
            // TODO remove this check once we move to a single file Custom Payslip.
            // This is only used for the CustomPayslips as its the only report that can have a SubReport.
            if (httpHeaders.TryGetValue("subReportPath", out var subreportPath) && !string.IsNullOrEmpty(subreportPath))
            {
                reportId = ReportConstants.CustomPayslips.PayslipDetailReportId;
            }

            var currentReportInfo = await this.reportRetrievalService.GetReportInfoAsync(reportId, companyId, userId, true, reportContextLevel, null, cancellationToken);
            await this.ValidateDesignerActionAccessAsync(userId, companyId, currentReportInfo);

            var parentReportInfo = await this.reportRetrievalService.GetEnabledParentReportInfoAsync(currentReportInfo.ReportSource, companyId, userId, cancellationToken);
            if (parentReportInfo is null)
            {
                throw new InvalidOperationException("No parent report available");
            }

            var reportType = currentReportInfo.Report.GetReportType();
            var metadata = this.GetMetadata(userId, reportType, httpHeaders);
            await this.SaveReportToStorageAsync(parentReportInfo.Report, currentReportInfo.ReportSource.ReportPath, metadata, ImmutableDictionary<string, string>.Empty, cancellationToken);
        }

        public async Task UpdateReportStatusAsync(long userId, string reportId, long companyId, long frequencyId, bool isEnabled, CancellationToken cancellationToken)
        {
            var currentReportInfo = await this.reportRetrievalService.GetReportInfoAsync(reportId, companyId, userId, true, null, null, cancellationToken);
            await this.ValidateDesignerActionAccessAsync(userId, companyId, currentReportInfo);

            await this.reportStorage.SetReportStatusAsync(currentReportInfo.ReportSource.ReportPath, isEnabled, cancellationToken);
            await this.ClearReportCacheAsync(companyId, frequencyId, cancellationToken);
        }

        private static Dictionary<string, string> GetTags(ReportSaveContext reportSaveContext)
        {
            var tags = new Dictionary<string, string>
            {
                { ReportConstants.Tags.InstanceId, Guid.NewGuid().ToString() },
                { ReportConstants.Tags.ContextLevel, ((int)reportSaveContext.ReportContextLevel).ToString() },
                { ReportConstants.Tags.ReportType, ((int)reportSaveContext.ReportType).ToString() }
            };

            if (reportSaveContext.Metadata.TryGetValue(ReportConstants.Tags.ReportCategory, out var reportCategoryId))
            {
                tags.Add(ReportConstants.Tags.ReportCategory, reportCategoryId);
            }

            if (reportSaveContext.Metadata.TryGetValue(ReportConstants.Tags.TemplateDisplayName, out var templateDisplayName))
            {
                tags.Add(ReportConstants.Tags.TemplateDisplayName, templateDisplayName);
            }

            if (reportSaveContext.Metadata.TryGetValue(ReportConstants.Tags.Description, out var description))
            {
                tags.Add(ReportConstants.Tags.Description, description);
            }

            if (reportSaveContext.Metadata.TryGetValue(ReportConstants.Tags.UserId, out var userId))
            {
                tags.Add(ReportConstants.Tags.UserId, userId);
            }

            if (reportSaveContext.Metadata.TryGetValue(ReportConstants.Tags.ReportSubCategory, out var reportSubCategoryId))
            {
                tags.Add(ReportConstants.Tags.ReportSubCategory, reportSubCategoryId);
            }

            return tags;
        }

        private static void SetExportSettings(XtraReport xtraReport, ReportSaveContext reportSaveContext)
        {
            if (reportSaveContext.ReportType != ReportType.Extract)
            {
                return;
            }

            var headers = reportSaveContext.HttpHeaders;
            var exportSeparator = headers[ReportConstants.Metadata.ExportSeparator].ToString() ?? string.Empty;
            var exportQuoteStringsWithSeparators = bool.Parse(headers[ReportConstants.Metadata.ExportQuoteStringsWithSeparators]);

            // Update report export settings
            TextExportOptionsBase exportOptions = string.IsNullOrWhiteSpace(exportSeparator)
                ? xtraReport.ExportOptions.Text
                : xtraReport.ExportOptions.Csv;
            exportOptions.Separator = exportSeparator;
            exportOptions.QuoteStringsWithSeparators = exportQuoteStringsWithSeparators;

            // Update datasource settings
            var reportDataSource = xtraReport.GetReportDataSource();
            reportDataSource!.ExportFormat = string.IsNullOrWhiteSpace(exportSeparator) ? "txt" : "csv";

            xtraReport.UpsertDataSourceSettings(reportDataSource);
        }

        private void SetRelatedEntityIdTag(IDictionary<string, string> tags, string relatedEntityId)
        {
            tags[ReportConstants.Tags.RelatedEntityId] = relatedEntityId;
        }

        private void SetPathGroupTag(IDictionary<string, string> tags, string pathGroup)
        {
            tags[ReportConstants.Tags.PathGroup] = pathGroup;
        }

        private async Task ClearReportCacheAsync(long companyId, long? frequencyId, CancellationToken cancellationToken)
        {
            await this.distributedCache.RemoveAsync(ReportConstants.CacheKeys.CustomReportsList(companyId, frequencyId ?? default, true), cancellationToken);
            await this.distributedCache.RemoveAsync(ReportConstants.CacheKeys.CustomReportsList(companyId, frequencyId ?? default, false), cancellationToken);
        }

        private async Task ValidateSaveReportAccess(ReportSaveContext reportSaveContext, CancellationToken cancellationToken)
        {
            if (!await this.reportUserSecurityService.HasSaveAccessAsync(reportSaveContext.ReportId, reportSaveContext.ReportContextLevel, reportSaveContext.UserId, reportSaveContext.CompanyIds, cancellationToken))
            {
                throw new UnauthorizedAccessException();
            }
        }

        private async Task ValidateDesignerActionAccessAsync(long userId, long companyId, ReportInfo reportInfo)
        {
            var securityProfile = await this.GetSecurityProfileAsync(companyId, userId);
            if (!await this.reportUserSecurityService.HasDesignerActionAccessAsync(securityProfile, reportInfo.ReportSource, reportInfo.Report.Extensions))
            {
                throw new UnauthorizedAccessException();
            }
        }

        private Dictionary<string, string> GetMetadata(long userId, ReportType reportType, IHeaderDictionary httpHeaders)
        {
            var result = new Dictionary<string, string> { { ReportConstants.Metadata.UserId, userId.ToString() } };

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.ChangesMadeDescription, out var changesMadeDescription))
            {
                changesMadeDescription = HttpUtility.UrlDecode(changesMadeDescription);
                if (!Regex.IsMatch(changesMadeDescription, "/^[a-zA-Z0-9_ ]*$/g"))
                {
                    result.Add(ReportConstants.Metadata.ChangesMadeDescription, changesMadeDescription.ToString().ConvertToBase64());
                    result.Add(ReportConstants.Tags.IsChangesMadeDescriptionEncoded, "true");
                }
                else
                {
                    result.Add(ReportConstants.Metadata.ChangesMadeDescription, changesMadeDescription.ToString());
                }
            }
            else
            {
                result.Add(ReportConstants.Metadata.ChangesMadeDescription, "Reset by " + userId);
            }

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.TemplateDisplayName, out var templateDisplayName))
            {
                templateDisplayName = HttpUtility.UrlDecode(templateDisplayName);
                if (!Regex.IsMatch(templateDisplayName, "/^[a-zA-Z0-9_ ]*$/g"))
                {
                    result.Add(ReportConstants.Metadata.TemplateDisplayName, templateDisplayName.ToString().ConvertToBase64());
                    result.Add(ReportConstants.Tags.IsTemplateDisplayNameEncoded, "true");
                }
                else
                {
                    result.Add(ReportConstants.Metadata.TemplateDisplayName, templateDisplayName.ToString());
                }
            }

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.ReportCategory, out var reportCategoryValue) && int.TryParse(reportCategoryValue, out var reportCategoryId))
            {
                result.Add(ReportConstants.Metadata.ReportCategory, reportCategoryId.ToString());
            }
            else if (reportType == ReportType.SystemReport)
            {
                result.Add(ReportConstants.Metadata.ReportCategory, ReportConstants.SystemReportsCategoryId.ToString());
            }

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.ReportSubCategory, out var reportSubCategory) && reportType == ReportType.Extract && !string.IsNullOrEmpty(reportSubCategory.ToString()))
            {
                result.Add(ReportConstants.Metadata.ReportSubCategory, reportSubCategory.ToString());
            }

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.Description, out var description))
            {
                description = HttpUtility.UrlDecode(description);
                if (!Regex.IsMatch(description, "/^[a-zA-Z0-9_ ]*$/g"))
                {
                    result.Add(ReportConstants.Metadata.Description, description.ToString().ConvertToBase64());
                    result.Add(ReportConstants.Tags.IsDescriptionEncoded, "true");
                }
                else
                {
                    result.Add(ReportConstants.Metadata.Description, description.ToString());
                }
            }

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.Enabled, out var isEnabled))
            {
                isEnabled = reportType == ReportType.SystemReport ? "true" : isEnabled;

                result.Add(ReportConstants.Metadata.Enabled, isEnabled.ToString());
            }

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.Designable, out var isDesignable))
            {
                isDesignable = reportType == ReportType.SystemReport ? "true" : isDesignable;

                result.Add(ReportConstants.Metadata.Designable, isDesignable.ToString());
            }

            if (httpHeaders.TryGetValue(ReportConstants.Metadata.ReportListId, out var reportListId) && long.TryParse(reportListId, out var listId))
            {
                result.Add(ReportConstants.Metadata.ReportListId, listId.ToString());
            }

            return result;
        }

        private async Task CloneRemainingReportsAsync(ReportSaveContext reportSaveContext, ReportSource reportSource, CancellationToken cancellationToken)
        {
            // This method is to save sub-reports. This is currently not supported for custom reports
            if (!ReportUtils.IsOldCustomPayslip(reportSource.ReportId))
            {
                return;
            }

            var reportPath = reportSource.ReportPath;
            var reportFolderPath = Path.GetDirectoryName(reportPath);
            var originalReportFolderPath = reportSaveContext.HttpHeaders["dir"];
            var reportFileName = Path.GetFileName(reportPath);

            // Clone the remaining.
            // Everything except the file that was just saved.
            var reportsToAdd = this.reportStorage
                .GetReportsInDirectory(originalReportFolderPath!)
                .Select(
                    _ => new
                    {
                        Uri = _,
                        FileName = Path.GetFileName(_)
                    })
                .Where(_ => !_.FileName.Equals(reportFileName, StringComparison.InvariantCultureIgnoreCase));

            foreach (var reportToAdd in reportsToAdd)
            {
                var report = await this.reportStorage.GetReportAsync(reportToAdd.Uri, null, cancellationToken);
                await this.SaveReportToStorageAsync(
                    report,
                    Path.Combine(reportFolderPath!, reportToAdd.FileName),
                    reportSaveContext.Metadata,
                    ImmutableDictionary<string, string>.Empty,
                    cancellationToken);
            }
        }

        private Task SaveReportToStorageAsync(
            XtraReport report,
            string url,
            IDictionary<string, string> metadata,
            IDictionary<string, string> tags,
            CancellationToken cancellationToken)
        {
            return this.reportStorage.SaveReportAsync(url, metadata, tags, report, cancellationToken);
        }

        private async Task<ISecurityProfile> GetSecurityProfileAsync(long companyId, long userId)
        {
            // TODO: Use Principal
            var userType = await this.userService.GetUserTypeAsync(userId);
            return await this.securityProfileFactory.CreateUserProfileAsync(userId, userType, companyId, false);
        }
    }
}