namespace PaySpace.Venuta.Reports.Services
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Globalization;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using DevExpress.DataAccess.Sql;
    using DevExpress.XtraReports;
    using DevExpress.XtraReports.Parameters;
    using DevExpress.XtraReports.UI;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Search;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.Extensions;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.Services;

    public interface IReportParameterHelper
    {
        void AddAutogeneratedParametersDataSource(ClaimsPrincipal user, ReportPath reportPath, XtraReport report, long companyId, long companyGroupId);
    }

    public sealed class ReportParameterHelper : IReportParameterHelper
    {
        private readonly ISearchProviderFactory searchProviderFactory;
        private readonly IReportDefaultParameterService reportDefaultParameterService;
        private readonly ICompanyService companyService;
        private readonly IUserPermissionService userPermissionService;
        private readonly IStringLocalizer stringLocalizer;

        public ReportParameterHelper(
            ISearchProviderFactory searchProviderFactory,
            IReportDefaultParameterService reportDefaultParameterService,
            ICompanyService companyService,
            IUserPermissionService userPermissionService,
            IStringLocalizerFactory stringLocalizerFactory)
        {
            this.searchProviderFactory = searchProviderFactory;
            this.reportDefaultParameterService = reportDefaultParameterService;
            this.companyService = companyService;
            this.userPermissionService = userPermissionService;
            this.stringLocalizer = stringLocalizerFactory.Create(SystemAreas.Report.Area, null);
        }

        public void AddAutogeneratedParametersDataSource(ClaimsPrincipal user, ReportPath reportPath, XtraReport report, long companyId, long companyGroupId)
        {
            if (report.RequestParameters && report.Parameters["Format"] == null)
            {
                var formatParameter = this.GetFormatParameter(report);
                report.Parameters.Add(formatParameter);
            }

            var sqlDs = new SqlDataSource("DefaultConnection") { Name = "AutogeneratedParametersDataSource" };
            DataSourceManager.AddDataSources(report, sqlDs);

            var companyFrequenciesQuery = this.GetFrequencyQueryAsync(user, companyGroupId, companyId).GetAwaiter().GetResult();
            sqlDs.Queries.Add(companyFrequenciesQuery);

            // SelectExpression periodcode has been altered to be returned as 6 digits always, this is to stop the sort order being wrong from the frontend.
            // Example being 20211 should be 202101
            var companyRunsSelectExpression = "'{ \"displayValue\":\"' + RunDescription + '\", \"PeriodCode\":\"' + (substring(PeriodCode, 0, 4) + IIF(len(PeriodCode) == 6,substring(PeriodCode, 4, len(PeriodCode) - 4),'0' + substring(PeriodCode, 4, len(PeriodCode) - 4))) + '\", \"Month\":\"' + MonthName(PeriodCode) + ' ' + substring(PeriodCode, 0, 4) + '\" }'";

            var companyRunsQuery = SelectQueryFluentBuilder
                .AddTable("CompanyRuns")
                .SelectColumns("pkRunID", "fkCompanyFrequency")
                .SelectExpression(companyRunsSelectExpression, "RunDescription")
                .SortBy("PayDate", ListSortDirection.Descending)
                .Filter("fkStatusID <> 3")
                .Top(48)
                .Build("CompanyRuns");
            sqlDs.Queries.Add(companyRunsQuery);

            var regionsQuery = SelectQueryFluentBuilder
               .AddTable("Company")
               .SelectColumns("CompanyName")
               .Join("OrganizationRegion", "pkCompanyID", "fkCompanyID")
               .SelectColumns("pkRegionID", "fkCompanyID")
               .SelectExpression("'{ \"displayValue\":\"' + RegionDescription + '\", \"CompanyName\":\"' + CompanyName + '\" }'", "RegionDescription")
               .SortBy("RegionDescription")
               .Build("Regions");
            sqlDs.Queries.Add(regionsQuery);

            var projectActivityQuery = SelectQueryFluentBuilder
               .AddTable("Company")
               .SelectColumns("CompanyName")
               .Join("CostingProjAct", "pkCompanyID", "fkCompanyID")
               .SelectColumns("pkProjActID", "fkCompanyID")
               .SelectExpression("'{ \"displayValue\":\"' + ProjActDescription + '\", \"CompanyName\":\"' + CompanyName + '\" }'", "ProjActDescription")
               .SortBy("ProjActDescription")
               .Build("ProjectActivity");
            sqlDs.Queries.Add(projectActivityQuery);

            var periodQuery = SelectQueryFluentBuilder
                .AddTable("CompanyRuns")
                .SelectColumn("PeriodCode")
                .SortBy("PayDate", ListSortDirection.Descending)
                .Join("CompanyRunFrequency", "fkCompanyFrequency", "pkCompanyFrequencyID")
                .SelectColumn("fkCompanyID")
                .SelectExpression("MonthName(PeriodCode) + ' ' + substring(PeriodCode, 0, 4)", "Month")
                .Filter("[fkStatusID] <> 3")
                .Build("Periods");
            sqlDs.Queries.Add(periodQuery);

            var payslipActionsQuery = SelectQueryFluentBuilder
                .AddTable("EnumPayslipAction")
                .SelectColumns("pkPayslipActionID", "PayslipActionDescription")
                .SortBy("OrderNumber")
                .Build("PayslipActions");
            sqlDs.Queries.Add(payslipActionsQuery);

            var componentCodesQuery = SelectQueryFluentBuilder
                .AddTable("vw_nextgen_rpt_all_active_componentcompanies_get")
                .SelectColumns("CompanyId", "FrequencyId", "RunId", "PeriodEndDate", "PeriodCode", "pkComponentCompanyID", "DisplayValue")
                .Build("CompanyComponentCodes");
            sqlDs.Queries.Add(componentCodesQuery);

            foreach (var parameter in report.Parameters)
            {
                if (parameter.Tag?.ToString()?.Contains("date", StringComparison.OrdinalIgnoreCase) == true)
                {
                    parameter.Type = typeof(DateOnly);
                }

                if (parameter.Name == "UserId")
                {
                    parameter.Value = user.GetUserId();
                }

                if (!user.IsInRole(UserTypeCodes.Employee) && (parameter.Name == ReportConstants.Parameters.CompanyId || parameter.Name == ReportConstants.Parameters.CompanyIds))
                {
                    var companies = this.searchProviderFactory.CreateCompanyQuery(user, companyGroupId).Take(1000).ToList();

                    var ds = new StaticListLookUpSettings();
                    ds.LookUpValues.AddRange(companies
                        .OrderBy(_ => _.CompanyName)
                        .Select(_ => new LookUpValue
                        {
                            Value = Convert.ToString(_.CompanyId, CultureInfo.InvariantCulture),
                            Description = _.CompanyName
                        }));

                    parameter.ValueSourceSettings = ds;
                }

                if (parameter.Name == ReportConstants.Parameters.CompanyIds)
                {
                    parameter.Value = Array.Empty<string>();
                    parameter.MultiValue = true;
                }

                if (parameter.Name == "RunOrMonth")
                {
                    parameter.Value = "RunId";
                }

                if (parameter.Name == "FrequencyId")
                {
                    parameter.AllowNull = true;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings
                    {
                        DataSource = sqlDs,
                        DataMember = "Frequencies",
                        DisplayMember = "FrequencyName",
                        ValueMember = "pkCompanyFrequencyID",
                        FilterString = "[fkCompanyID] = ?CompanyId"
                    };
                }

                if (parameter.Name == "PeriodCode" && LookupIsNotConfigured(parameter))
                {
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings
                    {
                        DataSource = sqlDs,
                        DataMember = "Periods",
                        DisplayMember = "Month",
                        ValueMember = "PeriodCode",
                        FilterString = GetCompanyFilter(report)
                    };
                }

                if (parameter.Name == "RunId" && LookupIsNotConfigured(parameter))
                {
                    parameter.Tag = "Grouped";
                    parameter.AllowNull = reportPath.IsCustomReport;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings
                    {
                        DataSource = sqlDs,
                        DataMember = "CompanyRuns",
                        DisplayMember = "RunDescription",
                        ValueMember = "pkRunID",
                        FilterString = "[fkCompanyFrequency] = ?FrequencyId"
                    };
                }

                if (parameter.Name is ReportConstants.Parameters.RegionIds or ReportConstants.Parameters.RegionId)
                {
                    parameter.Tag = "GroupByCompany";
                    parameter.MultiValue = true;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings
                    {
                        DataSource = sqlDs,
                        DataMember = "Regions",
                        DisplayMember = "RegionDescription",
                        ValueMember = "pkRegionID",
                        FilterString = GetCompanyFilter(report)
                    };
                }

                if (parameter.Name == ReportConstants.Parameters.ProjectIds)
                {
                    parameter.Tag = "GroupByCompany";
                    parameter.MultiValue = true;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings
                    {
                        DataSource = sqlDs,
                        DataMember = "ProjectActivity",
                        DisplayMember = "ProjActDescription",
                        ValueMember = "pkProjActID",
                        FilterString = GetCompanyFilter(report)
                    };
                }

                if (parameter.Name == ReportConstants.Parameters.PositionIds)
                {
                    parameter.MultiValue = true;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings();
                }

                if (parameter.Name == "OrgUnitId")
                {
                    parameter.MultiValue = true;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings();
                }

                if (parameter.Name == ReportConstants.Parameters.PayslipActions)
                {
                    parameter.MultiValue = true;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings
                    {
                        DataSource = sqlDs,
                        DataMember = "PayslipActions",
                        DisplayMember = "PayslipActionDescription",
                        ValueMember = "pkPayslipActionID",
                    };

                    parameter.Value = Enum.GetValues<PayslipAction>()
                        .Where(_ => _ != PayslipAction.Note)
                        .Select(_ => ((int)_).ToString());
                }

                if (parameter.Name == ReportConstants.Parameters.ComponentCodes)
                {
                    parameter.MultiValue = true;
                    parameter.ValueSourceSettings = new DynamicListLookUpSettings
                    {
                        DataSource = sqlDs,
                        DataMember = "CompanyComponentCodes",
                        DisplayMember = "DisplayValue",
                        ValueMember = "pkComponentCompanyID",
                        FilterString = GetCompanyComponentFilter(report)
                    };
                }

                if (parameter.Name == "BureauTaxId")
                {
                    parameter.Value = this.reportDefaultParameterService.GetBureauTaxIdAsync(companyId).GetAwaiter().GetResult();
                }

                if (parameter.Name == "CompanyGroupId")
                {
                    parameter.Value = companyGroupId;
                }

                if (parameter.Name == "CountryId")
                {
                    parameter.Value = this.companyService.GetTaxCountryIdAsync(companyId).GetAwaiter().GetResult();
                }

                if (parameter.Name == "UserTimezoneUtcOffset")
                {
                    parameter.Value = TimeZoneConverter.TZConvert.GetTimeZoneInfo(user.GetTimezone()).BaseUtcOffset.Hours;
                }
            }
        }

        private Parameter GetFormatParameter(XtraReport report)
        {
            var formatParameter = new Parameter
            {
                Name = "Format",
                Description = this.stringLocalizer.GetString("lblFormatSelection"),
                Type = typeof(string)
            };

            if (report.GetReportType() == ReportType.Extract)
            {
                var reportDataSource = report.GetReportDataSource();

                formatParameter.Visible = false;
                formatParameter.Value = reportDataSource?.ExportFormat ?? "csv";
            }
            else
            {
                var formats = new StaticListLookUpSettings();
                formats.LookUpValues.Add(new LookUpValue { Value = "pdf", Description = "pdf" });
                formats.LookUpValues.Add(new LookUpValue { Value = "xlsx", Description = "xlsx" });
                formats.LookUpValues.Add(new LookUpValue { Value = "docx", Description = "docx" });
                formats.LookUpValues.Add(new LookUpValue { Value = "csv", Description = "csv" });
                formats.LookUpValues.Add(new LookUpValue { Value = "txt", Description = "txt" });
                formats.LookUpValues.Add(new LookUpValue { Value = "image", Description = this.stringLocalizer.GetString("lblImage") });

                formatParameter.ValueSourceSettings = formats;
                formatParameter.Value = "pdf";
            }

            return formatParameter;
        }

        private static bool LookupIsNotConfigured(Parameter parameter)
        {
            return parameter.ValueSourceSettings == null || (parameter.ValueSourceSettings is LookUpSettings dls && dls.DataSource == null);
        }

        private static string GetCompanyFilter(XtraReport report)
        {
            return report.GetReportViewType() == ReportViewType.CompanyGroup
            ? "[fkCompanyID] in (?CompanyIds)"
            : "[fkCompanyID] = ?CompanyId";
        }

        private static string GetCompanyComponentFilter(XtraReport report)
        {
            var companyFilter = report.GetReportViewType() == ReportViewType.CompanyGroup ? "[CompanyId] in (?CompanyIds)" : "[CompanyId] = ?CompanyId";
            var frequencyFilter = report.Parameters["FrequencyId"] == null ? null : "[FrequencyId] = ?FrequencyId";
            var runIdFilter = report.Parameters["RunId"] == null ? null : "[RunId] = ?RunId";
            var periodCodeFilter = report.Parameters["PeriodCode"] == null ? null : "[PeriodCode] = ?PeriodCode";
            var periodEndDateFilter = report.Parameters["ToDate"] == null ? null : "[PeriodEndDate] = ?ToDate";

            var filters = new[] { companyFilter, frequencyFilter, runIdFilter, periodCodeFilter, periodEndDateFilter };
            return string.Join(" And ", filters.Where(_ => !string.IsNullOrEmpty(_)));
        }

        private async Task<SelectQuery> GetFrequencyQueryAsync(ClaimsPrincipal user, long companyGroupId, long companyId)
        {
            var companyFilters = new List<string>();

            IList<long> userCompanyIds = !user.IsInRole(UserTypeCodes.Employee)
                ? this.searchProviderFactory.CreateCompanyQuery(user, companyGroupId).Take(1000).Select(_ => _.CompanyId).ToList()
                : new[] { companyId };
            foreach (var userCompanyId in userCompanyIds)
            {
                var userFrequencyIds = await this.userPermissionService.GetUserFrequencyIdsAsync(user.GetUserId(), userCompanyId);

                var filter = userFrequencyIds.FullAccess
                    ? $"(fkCompanyID = {userCompanyId})"
                    : $"(fkCompanyID = {userCompanyId} and pkCompanyFrequencyID in ({string.Join(",", userFrequencyIds.FrequencyIds)}))";
                companyFilters.Add(filter);
            }

            return SelectQueryFluentBuilder
                   .AddTable("CompanyRunFrequency")
                   .Filter($"({string.Join(" or ", companyFilters)})")
                   .SelectColumns("pkCompanyFrequencyID", "fkCompanyID", "FrequencyName")
                   .SortBy("FrequencyName")
                   .Build("Frequencies");
        }
    }
}