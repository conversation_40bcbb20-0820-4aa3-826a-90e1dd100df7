namespace PaySpace.Venuta.Services.Reports.Services
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.Models.ContextLevelReportSources;
    using PaySpace.Venuta.Storage;

    public interface ISystemReportListingService
    {
        Task<IList<SystemReport>> GetSystemReportsAsync(
            HostString tenant,
            long companyId,
            long frequencyId);
    }

    public class SystemReportListingService : ISystemReportListingService
    {
        private readonly IReportStorageService reportStorageService;
        private readonly IReportUrlResolver reportUrlResolver;
        private readonly ICompanyService companyService;

        public SystemReportListingService(
            IReportStorageService reportStorageService,
            IReportUrlResolver reportUrlResolver,
            ICompanyService companyService)
        {
            this.reportStorageService = reportStorageService;
            this.reportUrlResolver = reportUrlResolver;
            this.companyService = companyService;
        }

        public async Task<IList<SystemReport>> GetSystemReportsAsync(
            HostString tenant,
            long companyId,
            long frequencyId)
        {
            var systemReports = await this.GetAvailableSystemReportsAsync(tenant, companyId, frequencyId);

            return systemReports;
        }

        private async Task<IList<SystemReport>> GetAvailableSystemReportsAsync(
            HostString tenant,
            long companyId,
            long frequencyId)
        {
            var systemReportsListing = await this.GetSystemReportListingsAsync(companyId);

            var systemReports = new List<SystemReport>();

            foreach (var reportListingResult in systemReportsListing)
            {
                var systemReport = this.GetSystemReportModel(reportListingResult, tenant, companyId, frequencyId);
                systemReports.Add(systemReport);
            }

            return systemReports;
        }

        private List<string> FindSystemReports(string countryId)
        {
            var systemReports = new List<string>();

            var reportPath = BureauTaxCountryReportSource.CreateForRootFolder(string.Empty, int.Parse(countryId)).ReportPath;
            var foundReports = this.reportStorageService.GetReportsInDirectory(reportPath) ?? new List<string>();

            systemReports.AddRange(foundReports);

            return systemReports;
        }

        private async Task<IList<ReportListingResult>> GetSystemReportListingsAsync(long companyId)
        {
            var systemReportResults = new Dictionary<string, ReportListingResult>();

            var companySummary = await this.companyService.GetCompanySummary(companyId);

            var reports = this.FindSystemReports(companySummary.TaxCountryId.ToString());

            foreach (var reportPath in reports)
            {
                var reportId = Path.GetFileNameWithoutExtension(reportPath);

                if (!ReportUtils.IsSystemReport(reportId))
                {
                    continue;
                }

                if (!systemReportResults.ContainsKey(reportId))
                {
                    ReportSource reportSource = reportPath.Contains("/TaxCountry/") ? new BureauTaxCountryReportSource(reportPath) : new BureauReportSource(reportPath);
                    var reportMetadata = await this.reportStorageService.GetMetadataValuesAsync(reportSource.ReportPath);

                    reportMetadata ??= new Dictionary<string, string>
                        {
                            { ReportConstants.Metadata.ReportCategory, ReportConstants.SystemReportsCategoryId.ToString() },
                            { ReportConstants.Metadata.TemplateDisplayName, reportId },
                        };

                    systemReportResults[reportId] = new ReportListingResult(reportId, reportSource, reportMetadata);
                }
            }

            return systemReportResults.Values.ToList();
        }

        private SystemReport GetSystemReportModel(
           ReportListingResult reportListingResult,
           HostString tenant,
           long companyId,
           long frequencyId)
        {
            var reportId = reportListingResult.ReportId;
            var reportModel = new SystemReport(reportListingResult.ReportSource)
            {
                ReportId = reportListingResult.ReportId,
                ViewerUri = $"{new Uri(this.reportUrlResolver.Resolve(tenant), "/viewer")}?companyId={companyId}&frequencyId={frequencyId}&reportId={reportId}&tenant={tenant}&includeDisabledReports=true"
            };

            var tags = reportListingResult.Tags;

            if (tags.TryGetValue(ReportConstants.Metadata.ReportCategory, out var categoryId))
            {
                reportModel.ReportCategoryId = int.Parse(categoryId);
            }
            else
            {
                reportModel.ReportCategoryId = ReportConstants.SystemReportsCategoryId;
            }

            if (tags.TryGetValue(ReportConstants.Metadata.TemplateDisplayName, out var name))
            {
                reportModel.ReportName = GetDecodedMetadataValue(tags, ReportConstants.Tags.IsTemplateDisplayNameEncoded, name);
            }
            else
            {
                reportModel.ReportName = reportId;
            }

            reportModel.Enabled = true;
            reportModel.Designable = true;

            return reportModel;
        }

        private static string? GetDecodedMetadataValue(IDictionary<string, string> metadata, string isEncodedKey, string value)
        {
            return metadata.TryGetValue(isEncodedKey, out var isEncoded) && !string.IsNullOrEmpty(isEncoded) ? value.ConvertFromBase64() : value;
        }
    }
}
