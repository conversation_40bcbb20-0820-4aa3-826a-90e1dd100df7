namespace PaySpace.Venuta.Services.Reports
{
    using System;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Hosting;

    using PaySpace.Configuration;

    public interface IReportUrlResolver
    {
        public Uri Resolve(HostString host);
    }

    public class ReportUrlResolver : IReportUrlResolver
    {
        private readonly IHostEnvironment env;
        private readonly IConfiguration configuration;

        public ReportUrlResolver(IHostEnvironment env, IConfiguration configuration)
        {
            this.env = env;
            this.configuration = configuration;
        }

        public Uri Resolve(HostString host)
        {
            var isDisasterRecovery = this.configuration.GetValue<bool?>("isDisasterRecovery") == true;
            if (isDisasterRecovery)
            {
                // TODO: Use Config setting to override url
                return new Uri("https://dr-reports.payus.co.za");
            }

            if (this.env.IsDevelopment())
            {
                return new Uri("https://localhost:44364");
            }

            var reportsUrl = this.configuration["ClientSettings:ReportsUrl"];
            if (!string.IsNullOrEmpty(reportsUrl))
            {
                return new Uri(reportsUrl);
            }

            if (this.env.IsProduction())
            {
                return new Uri($"https://reports-{this.configuration.GetRegion()}.yourhcm.com");
            }

            return new Uri($"https://{this.env.EnvironmentName.ToLowerInvariant()}-reports.payspace.com");
        }
    }
}