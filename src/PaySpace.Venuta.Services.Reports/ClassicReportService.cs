namespace PaySpace.Venuta.Services.Reports
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Logging.Abstractions;

    using PaySpace.Venuta.Abstractions;

    public interface IClassicReportService
    {
        Task<IList<SsrsParameter>> GetReportParametersAsync(string path, CancellationToken cancellationToken);

        Task<byte[]> ExportReportAsync(ILogger logger, IDictionary<string, string?> parameters, string path, string? format, CancellationToken cancellationToken);

        Task<byte[]> ExportKpaAsync(string kpaIds, long employeeId);

        Task<byte[]> ExportEmployeeEvaluationSummaryAsync(string reportName, long templateReviewId, long companyId, bool isESS);

        Task<byte[]> ExportEmployeeEvaluationHeaderAsync(long reviewId, long companyId, bool hideWeight, bool isESS);

        Task<byte[]> ExportUI19ReportAsync(long companyGroupId, long companyId, long frequencyId, string empNumber, DateTime suspensionDate, long? userId);

        Task<byte[]> ExportSecurityReportAsync(long agencyId);

        Task<byte[]> ExportOrgStructureReportAsync(long companyId);

        Task<byte[]> ExportSecurityProfileReportAsync(long companyGroupId);

        Task<byte[]> ExportCompanyPositionReportAsync(long companyId);
    }

    public class ClassicReportService : IClassicReportService
    {
        private readonly ISsrsClient ssrsClient;

        public ClassicReportService(ISsrsClient ssrsClient)
        {
            this.ssrsClient = ssrsClient;
        }

        public Task<IList<SsrsParameter>> GetReportParametersAsync(string path, CancellationToken cancellationToken)
        {
            return this.ssrsClient.GetParametersAsync(path, cancellationToken);
        }

        public Task<byte[]> ExportReportAsync(ILogger logger, IDictionary<string, string?> parameters, string path, string? format, CancellationToken cancellationToken)
        {
            return this.ssrsClient.ExportAsync(logger, path, format, parameters, cancellationToken);
        }

        public Task<byte[]> ExportKpaAsync(string kpaIds, long employeeId)
        {
            return this.ssrsClient.ExportAsync(
                NullLogger.Instance,
                "/PayrollReports/EmployeeKPA",
                null,
                new Dictionary<string, object> { { "KPAID", kpaIds }, { "EmployeeID", employeeId } }.ToDictionary(_ => _.Key, _ => _.Value?.ToString()));
        }

        public Task<byte[]> ExportEmployeeEvaluationSummaryAsync(string reportName, long templateReviewId, long companyId, bool isESS)
        {
            return this.ssrsClient.ExportAsync(
                NullLogger.Instance,
                "/PayrollReports/" + reportName,
                null,
                new Dictionary<string, object> { { "TemplateReviewID", templateReviewId }, { "CompanyID", companyId }, { "IsSelf", isESS } }.ToDictionary(_ => _.Key, _ => _.Value?.ToString()));
        }

        public Task<byte[]> ExportEmployeeEvaluationHeaderAsync(long reviewId, long companyId, bool hideWeight, bool isESS)
        {
            return this.ssrsClient.ExportAsync(
                NullLogger.Instance,
                "/PayrollReports/EmployeeEvaluationHeader",
                null,
                new Dictionary<string, object> { { "ReviewID", reviewId }, { "CompanyID", companyId }, { "HideWeight", hideWeight }, { "IsSelf", isESS } }.ToDictionary(_ => _.Key, _ => _.Value?.ToString()));
        }

        public Task<byte[]> ExportUI19ReportAsync(long companyGroupId, long companyId, long frequencyId, string empNumber, DateTime suspensionDate, long? userId)
        {
            return this.ssrsClient.ExportAsync(
                NullLogger.Instance,
                "/PayrollReports/UI19Report",
                null,
                new Dictionary<string, object>
                {
                    { "GroupID", 0 },
                    { "ComGroupID", companyGroupId },
                    { "CompanyID", companyId },
                    { "EmpNumber", empNumber },
                    { "StartDate", suspensionDate },
                    { "EndDate", suspensionDate },
                    { "FrequencyID", frequencyId },
                    { "ProjectID", 0 },
                    { "UserID", userId! }
                }.ToDictionary(_ => _.Key, _ => _.Value?.ToString()));
        }

        public Task<byte[]> ExportSecurityReportAsync(long agencyId)
        {
            return this.ssrsClient.ExportAsync(NullLogger.Instance, "/PayrollReports/BPUserReport", "EXCELOPENXML", new Dictionary<string, object> { { "AgencyID", agencyId } }.ToDictionary(_ => _.Key, _ => _.Value?.ToString()));
        }

        public Task<byte[]> ExportOrgStructureReportAsync(long companyId)
        {
            return this.ssrsClient.ExportAsync(
                NullLogger.Instance,
                "/PayrollReports/OrgStructure",
                "EXCELOPENXML",
                new Dictionary<string, object>
                {
                    { "SearchType", "CompanyID" },
                    { "SearchValue", companyId },
                    { "SearchDate", "" },
                    { "GetActive", false },
                    { "CompanyID", companyId }
                }.ToDictionary(_ => _.Key, _ => _.Value?.ToString()));
        }

        public Task<byte[]> ExportSecurityProfileReportAsync(long companyGroupId)
        {
            return this.ssrsClient.ExportAsync(
                NullLogger.Instance,
                "/PayrollReports/Security",
                "EXCELOPENXML",
                new Dictionary<string, string?> { { "fkCompanyGroupID", companyGroupId.ToString() } });
        }

        public Task<byte[]> ExportCompanyPositionReportAsync(long companyId)
        {
            return this.ssrsClient.ExportAsync(
                NullLogger.Instance,
                "/PayrollReports/CompanyPosition",
                "EXCELOPENXML",
                new Dictionary<string, object>
                {
                    { "CompanyID", companyId }
                }.ToDictionary(_ => _.Key, _ => _.Value?.ToString()));
        }
    }
}