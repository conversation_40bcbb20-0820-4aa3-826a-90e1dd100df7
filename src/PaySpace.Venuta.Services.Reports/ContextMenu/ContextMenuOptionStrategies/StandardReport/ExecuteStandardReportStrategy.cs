namespace PaySpace.Venuta.Services.Reports.ContextMenu.ContextMenuOptionStrategies.StandardReport
{
    using System;
    using System.Collections.Generic;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Infrastructure;

    public class ExecuteStandardReportStrategy : ContextMenuOptionStrategy, IStandardReportContextMenuOptionStrategy
    {
        private readonly IStringLocalizer stringLocalizer;

        public ExecuteStandardReportStrategy(
            IReportUrlResolver reportUrlResolver,
            IStringLocalizerFactory stringLocalizerFactory)
            : base(reportUrlResolver)
        {
            this.stringLocalizer = stringLocalizerFactory.Create(SystemAreas.Report.Area, null);
        }

        public string Id => nameof(ExecuteStandardReportStrategy);

        public string ActionId => ContextMenuConstants.ActionId.ExecuteReport;

        public string Description => this.stringLocalizer.GetString("lblExecuteReport");

        public Uri GetActionUrl(HostString tenant, string reportId, long companyId, long frequencyId, string accessToken, string? reportPath)
        {
            var urlQueryParameters = new Dictionary<string, string>
            {
                { "companyId", companyId.ToString() },
                { "frequencyId", frequencyId.ToString() },
                { "reportHeaderId", reportId },
                { "tenant", tenant.ToString() },
                { "access_token", accessToken }
            };
            return this.GenerateUri(tenant, "/classic/detail", urlQueryParameters);
        }
    }
}