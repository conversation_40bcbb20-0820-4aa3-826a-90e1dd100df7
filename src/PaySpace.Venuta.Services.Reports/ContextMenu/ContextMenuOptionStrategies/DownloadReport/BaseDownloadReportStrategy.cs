namespace PaySpace.Venuta.Services.Reports.ContextMenu.ContextMenuOptionStrategies.DownloadReport
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Reports.Services;

    public abstract class BaseDownloadReportStrategy : ContextMenuOptionStrategy, IContextMenuOptionStrategy
    {
        private readonly IReportRetrievalService reportRetrievalService;
        private readonly IReportUserSecurityService reportUserSecurityService;

        protected BaseDownloadReportStrategy(
            IReportUserSecurityService reportUserSecurityService,
            IReportRetrievalService reportRetrievalService,
            IReportUrlResolver reportUrlResolver)
            : base(reportUrlResolver)
        {
            this.reportUserSecurityService = reportUserSecurityService;
            this.reportRetrievalService = reportRetrievalService;
        }

        public abstract ReportContextLevel ReportContext { get; }

        public abstract string Id { get; }

        public abstract string Description { get; }

        public int? GroupOrder => (int)this.ReportContext;

        public string ActionId => ContextMenuConstants.ActionId.DownloadReport;

        public ContextMenuConstants.GroupId? GroupId => ContextMenuConstants.GroupId.DownloadReport;

        public TaxCountry? TaxCountryId => null;

        public async Task<(bool, string?)> ShouldBeIncludedAsync(ISecurityProfile securityProfile, long companyId, string reportId, CancellationToken cancellationToken)
        {
            if (ReportUtils.IsCustomPayslip(reportId))
            {
                reportId = await this.CustomPayslipMigrationConsideration(companyId, securityProfile.UserId, cancellationToken);

                // TODO: Remove this once we have migrated to the single files.
                if (ReportUtils.IsOldCustomPayslip(reportId))
                {
                    return (false, null);
                }
            }

            if (securityProfile.UserType != UserType.Bureau)
            {
                return (false, null);
            }

            var reportContextLevel = this.ReportContext;
            const bool includeInactiveReports = true;

            var reportExistsAtLevel = await this.reportRetrievalService.CheckReportExistsAsync(reportId, companyId, securityProfile.UserId, includeInactiveReports, reportContextLevel, cancellationToken);
            if (!reportExistsAtLevel)
            {
                return (false, null);
            }

            var currentReportInfo = await this.reportRetrievalService.GetReportInfoAsync(
                reportId,
                companyId,
                securityProfile.UserId,
                includeInactiveReports,
                reportContextLevel,
                null,
                cancellationToken);
            var reportSource = currentReportInfo.ReportSource;
            return (await this.reportUserSecurityService.HasDesignerAccessAsync(securityProfile, reportSource.ReportId, currentReportInfo.Report.Extensions), reportSource.ReportPath);
        }

        public Task<Uri> GetActionUrl(HostString tenant, string reportId, long companyId, long frequencyId, long userId, string? accessToken, string? reportPath, CancellationToken cancellationToken)
        {
            var urlQueryParameters = new Dictionary<string, string>
            {
                { "access_token", accessToken ?? throw new ArgumentNullException(nameof(accessToken)) },
                { "companyId", companyId.ToString() },
                { "frequencyId", frequencyId.ToString() },
                { "reportId", reportId },
                { "reportPath", reportPath ?? string.Empty }
            };
            return Task.FromResult(this.GenerateUri(tenant, "/designer/downloadReport", urlQueryParameters));
        }

        public Task<Dictionary<string, string>> GetAdditionalContextValuesAsync(string reportId, long companyId, long userId, CancellationToken cancellationToken)
        {
            return Task.FromResult(new Dictionary<string, string>());
        }

        // TODO: Remove this once all custom payslips have migrated.
        private async Task<string> CustomPayslipMigrationConsideration(
            long companyId,
            long userId,
            CancellationToken cancellationToken)
        {
            var reportExists = await this.reportRetrievalService.CheckReportExistsAsync(ReportConstants.CustomPayslips.NewPayslipReportId, companyId, userId, true, this.ReportContext, cancellationToken);
            if (reportExists)
            {
                return ReportConstants.CustomPayslips.NewPayslipReportId;
            }

            return ReportConstants.CustomPayslips.PayslipHeaderReportId;
        }
    }
}