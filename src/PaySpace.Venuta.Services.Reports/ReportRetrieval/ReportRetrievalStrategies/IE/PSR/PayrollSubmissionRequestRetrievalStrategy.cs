namespace PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies.IE.PSR
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;

    using DevExpress.DataAccess.ObjectBinding;
    using DevExpress.XtraReports;

    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.Extensions;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies;
    using PaySpace.Venuta.Services.Reports.Services;

    public class PayrollSubmissionRequestRetrievalStrategy(
        IReportStorageService reportStorageService,
        IReportUserSecurityService reportUserSecurityService,
        ICompanyService companyService,
        ICompanyRunService companyRunService)
        : BaseReportRetrievalStrategy(reportStorageService, reportUserSecurityService), IReportRetrievalStrategy
    {
        private readonly string parentFolderPath = string.Empty;

        public string Id => nameof(PayrollSubmissionRequestRetrievalStrategy);

        public bool CanExecute(string reportId)
        {
            return reportId == ReportConstants.SystemReports.PayrollSubmissionRequest;
        }

        public Task<ReportInfo> GetReportInfoForGenerationAsync(long userId, ReportPath reportPath, CultureInfo culture, string accessToken, CancellationToken cancellationToken)
        {
            return this.GetReportInfoWithUpdatedDatasourceAsync(userId, reportPath, cancellationToken);
        }

        public Task<ReportInfo> GetReportInfoForDesignAsync(long userId, ReportPath reportPath, CultureInfo culture, string accessToken, CancellationToken cancellationToken)
        {
            return this.GetReportInfoWithUpdatedDatasourceAsync(userId, reportPath, cancellationToken);
        }

        public async Task<ReportInfo> GetReportInfoAsync(
            string reportId,
            long companyId,
            long userId,
            bool includeDisabledReports,
            ReportContextLevel? reportContextLevel,
            string? version,
            CancellationToken cancellationToken)
        {
            var companySummary = await companyService.GetCompanySummary(companyId);
            return await base.GetReportInfoAsync(
                ReportConstants.SystemReports.PayrollSubmissionRequest,
                userId,
                this.parentFolderPath,
                includeDisabledReports,
                companySummary,
                null,
                version,
                cancellationToken);
        }

        public async Task<ReportSource> GetReportSourceAsync(string reportId, long companyId, long userId, bool includeDisabledReports, ReportContextLevel? reportContextLevel, CancellationToken cancellationToken)
        {
            var companySummary = await companyService.GetCompanySummary(companyId);
            return await base.GetReportSourceAsync(reportId, userId, this.parentFolderPath, includeDisabledReports, reportContextLevel, companySummary, cancellationToken);
        }

        public async Task<ReportInfo?> GetEnabledParentReportInfoAsync(ReportSource currentReportSource, long companyId, long userId, CancellationToken cancellationToken)
        {
            var companySummary = await companyService.GetCompanySummary(companyId);
            return await base.GetParentReportInfoAsync(currentReportSource, userId, this.parentFolderPath, companySummary, cancellationToken);
        }

        public async Task<bool> CheckReportExistsAsync(string reportId, long companyId, long userId, bool includeDisabledReports, ReportContextLevel? reportContextLevel, CancellationToken cancellationToken)
        {
            var companySummary = await companyService.GetCompanySummary(companyId);
            return await base.CheckReportExistsAsync(reportId, userId, this.parentFolderPath, companySummary, includeDisabledReports, reportContextLevel, cancellationToken);
        }

        public async Task<IEnumerable<ReportSource>> GetAvailableReportLevelsForReportAsync(string reportId, long userId, long companyId, CancellationToken cancellationToken)
        {
            var companySummary = await companyService.GetCompanySummary(companyId);
            return await base.GetAvailableReportLevelsForReportAsync(reportId, userId, this.parentFolderPath, true, companySummary, cancellationToken);
        }

        private async Task<ReportInfo> GetReportInfoWithUpdatedDatasourceAsync(long userId, ReportPath reportPath, CancellationToken cancellationToken)
        {
            DevExpress.Utils.DeserializationSettings.RegisterTrustedClass(typeof(PayrollSubmissionRequestDatasource));

            var reportInfo = await this.GetReportInfoAsync(
                ReportConstants.SystemReports.PayrollSubmissionRequest,
                reportPath.CompanyId,
                userId,
                reportPath.IncludeDisabledReports,
                null,
                reportPath.Version,
                cancellationToken);

            var objectDataSource = new ObjectDataSource
            {
                Name = "PayrollSubmissionRequest",
                DataMember = "GetData",
                DataSource = new PayrollSubmissionRequestDatasource(companyService, companyRunService)
            };
            objectDataSource.Parameters.Add(new Parameter("xmlBlobUrl", typeof(DevExpress.DataAccess.Expression), new DevExpress.DataAccess.Expression("?XmlBlobUrl", typeof(string))));
            objectDataSource.Parameters.Add(new Parameter("companyId", typeof(DevExpress.DataAccess.Expression), new DevExpress.DataAccess.Expression("?CompanyId", typeof(long))));
            objectDataSource.Parameters.Add(new Parameter("runId", typeof(DevExpress.DataAccess.Expression), new DevExpress.DataAccess.Expression("?RunId", typeof(long))));
            objectDataSource.RebuildResultSchema();

            var report = reportInfo.Report;
            var reportBandDataMembers = report.GetReportDetailBandDataMembers();
            DataSourceManager.ReplaceDataSource(report, report.DataSource, objectDataSource);
            report.SetDetailReportBands(reportBandDataMembers);

            return reportInfo;
        }
    }
}