namespace PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies.IE.PSR
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Xml.Serialization;

    using Azure.Storage.Blobs;

    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;

    public class PayrollSubmissionRequestDatasource(ICompanyService companyService, ICompanyRunService companyRunService)
    {
        public ReportData GetData(string xmlBlobUrl, long companyId, long runId)
        {
            var blobClient = new BlobClient(new Uri(xmlBlobUrl));
            var response = blobClient.DownloadStreaming() ?? throw new InvalidOperationException("Failed to download xml blob");
            var xmlSerializer = new XmlSerializer(typeof(XmlPayrollSubmissionRequest));
            var parsedRequest = (XmlPayrollSubmissionRequest)xmlSerializer.Deserialize(response.Value.Content)!;

            return this.GetMappedReportData(parsedRequest, companyId, runId);
        }

        private ReportData GetMappedReportData(XmlPayrollSubmissionRequest request, long companyId, long runId)
        {
            var employees = this.GetMappedEmployees(request);
            var totals = GetMappedTotals(employees);

            return new ReportData
            {
                Header = this.GetMappedHeader(request, companyId, runId),
                Employees = employees,
                Totals = totals
            };
        }

        private ReportHeader GetMappedHeader(XmlPayrollSubmissionRequest request, long companyId, long runId)
        {
            var companyName = companyService.GetCompanyNameAsync(companyId).Result;
            var runSummary = companyRunService.GetRunSummary(runId);

            return new ReportHeader
            {
                EmployerName = companyName,
                EmployerRegistrationNumber = request.Header.EmployerRegistrationNumber,
                PayPeriodStartDate = runSummary.PeriodStartDate,
                PayPeriodEndDate = runSummary.PeriodEndDate,
                PayrollRunReference = request.Header.PayrollRunReference,
                SoftwareVersion = request.Header.SoftwareUsed.Version,
                SubmissionId = request.SubmissionID
            };
        }

        private List<Employee> GetMappedEmployees(XmlPayrollSubmissionRequest request)
        {
            var employees = new List<Employee>();
            foreach (var employeePayslip in request.Payslips)
            {
                var employmentDetails = new EmploymentDetails
                {
                    DateOfBirth = employeePayslip.DateOfBirth,
                    EmployeeName = $"{employeePayslip.Name.FirstName} {employeePayslip.Name.FamilyName}",
                    EmploymentId = employeePayslip.EmployeeID?.EmploymentID,
                    PPSNumber = employeePayslip.EmployeeID?.EmployeePPSN,
                    Address = new EmployeeAddress
                    {
                        AddressLine1 = employeePayslip.Address?.AddressLines.Count > 0 ? employeePayslip.Address.AddressLines[0] : null,
                        AddressLine2 = employeePayslip.Address?.AddressLines.Count > 1 ? employeePayslip.Address.AddressLines[1] : null,
                        County = employeePayslip.Address?.County,
                        Country = "Ireland"
                    },
                    PayFrequency = employeePayslip.PayFrequency
                };

                var paymentDetails = new PaymentDetails
                {
                    LineItemId = employeePayslip.LineItemID,
                    RPNNumber = employeePayslip.RPNNumber,
                    CalculationBasis = employeePayslip.IncomeTaxCalculationBasis,
                    PayDate = employeePayslip.PayDate,
                    GrossPay = employeePayslip.GrossPay
                };

                var tax = new TaxDetails
                {
                    TaxCredits = employeePayslip.TaxCredits,
                    TaxCutOff = employeePayslip.TaxRates.Count > 0 ? employeePayslip.TaxRates[0].RateCutOff : 0,
                    TaxablePay = employeePayslip.PayForIncomeTax,
                    TaxPaid = employeePayslip.IncomeTaxPaid
                };

                var prsi = new PrsiDetails
                {
                    EmployerPRSIablePay = employeePayslip.PayForEmployerPRSI,
                    EmployerPRSIPaid = employeePayslip.EmployerPRSIPaid,
                    EmployeePRSIablePay = employeePayslip.PayForEmployeePRSI,
                    EmployeePRSIPaid = employeePayslip.EmployeePRSIPaid,
                    PRSIClass = employeePayslip.PRSIClassDetail.Count > 0 ? employeePayslip.PRSIClassDetail[0].PRSIClass : string.Empty,
                    InsurableWeeks = employeePayslip.PRSIClassDetail.Count > 0 ? employeePayslip.PRSIClassDetail[0].InsurableWeeks : 0
                };

                var usc = new UscDetails
                {
                    USCStatus = employeePayslip.USCStatus,
                    USCablePay = employeePayslip.PayForUSC,
                    USCPaid = employeePayslip.USCPaid
                };

                var other = new OtherContributions
                {
                    LPTPaid = employeePayslip.LPTDeducted,
                    EmployerRBS = employeePayslip.EmployerRBS,
                    EmployeeRBS = employeePayslip.EmployeeRBS,
                    EmployerPRSA = employeePayslip.EmployerPRSA,
                    EmployeePRSA = employeePayslip.EmployeePRSA,
                    EmployeeRAC = employeePayslip.EmployeeRAC,
                    EmployerPEPP = employeePayslip.EmployerPEPP,
                    EmployeePEPP = employeePayslip.EmployeePEPP,
                    EmployeeAVC = employeePayslip.EmployeeAVC
                };

                employees.Add(new Employee
                {
                    EmploymentDetails = employmentDetails,
                    PaymentDetails = paymentDetails,
                    Tax = tax,
                    PRSI = prsi,
                    USC = usc,
                    Other = other
                });
            }

            return employees;
        }

        private static Totals GetMappedTotals(List<Employee> employees)
        {
            return new Totals
            {
                NumberOfEmployees = employees.Count,
                TotalTax = employees.Sum(_ => _.Tax.TaxPaid.GetValueOrDefault()),
                TotalEmployeePRSI = employees.Sum(_ => _.PRSI.EmployeePRSIPaid.GetValueOrDefault()),
                TotalEmployerPRSI = employees.Sum(_ => _.PRSI.EmployerPRSIPaid.GetValueOrDefault()),
                TotalUSC = employees.Sum(_ => _.USC.USCPaid.GetValueOrDefault()),
                TotalLPT = employees.Sum(_ => _.Other.LPTPaid.GetValueOrDefault()),
                TotalPension = employees.Sum(_ =>
                    _.Other.EmployerRBS.GetValueOrDefault() + _.Other.EmployeeRBS.GetValueOrDefault() + _.Other.EmployerPRSA.GetValueOrDefault() + _.Other.EmployeePRSA.GetValueOrDefault() +
                    _.Other.EmployerRAC.GetValueOrDefault() + _.Other.EmployeeAVC.GetValueOrDefault() + _.Other.EmployerPEPP.GetValueOrDefault() + _.Other.EmployeePEPP.GetValueOrDefault()
                )
            };
        }

        [XmlRoot(ElementName = "PayrollSubmissionRequest", Namespace = "http://www.ros.ie/schemas/paye-employers/v1/payroll/")]
        public class XmlPayrollSubmissionRequest
        {
            public string SubmissionID { get; set; }

            public XmlSubmissionHeader Header { get; set; }

            [XmlElement(ElementName = "Payslip")]
            public List<XmlPayslip> Payslips { get; set; }
        }

        public class XmlSubmissionHeader
        {
            public string EmployerRegistrationNumber { get; set; }

            public string PayrollRunReference { get; set; }

            public XmlSoftwareUsed SoftwareUsed { get; set; }
        }

        public class XmlSoftwareUsed
        {
            public string Version { get; set; }
        }

        public class XmlPayslip
        {
            public string LineItemID { get; set; }

            public XmlEmployeeId? EmployeeID { get; set; }

            public XmlName Name { get; set; }

            public XmlAddress? Address { get; set; }

            public DateTime? DateOfBirth { get; set; }

            public string PayFrequency { get; set; }

            public string RPNNumber { get; set; }

            public decimal? TaxCredits { get; set; }

            [XmlElement(ElementName = "TaxRate")]
            public List<XmlTaxRate> TaxRates { get; set; }

            public string IncomeTaxCalculationBasis { get; set; }

            public DateTime PayDate { get; set; }

            public decimal GrossPay { get; set; }

            public decimal PayForIncomeTax { get; set; }

            public decimal IncomeTaxPaid { get; set; }

            public decimal PayForEmployeePRSI { get; set; }

            public decimal PayForEmployerPRSI { get; set; }

            public List<XmlPrsiClassDetail> PRSIClassDetail { get; set; }

            public decimal? EmployeePRSIPaid { get; set; }

            public decimal? EmployerPRSIPaid { get; set; }

            public decimal PayForUSC { get; set; }

            public string USCStatus { get; set; }

            public decimal? USCPaid { get; set; }

            public decimal? LPTDeducted { get; set; }

            public decimal? EmployerRBS { get; set; }

            public decimal? EmployeeRBS { get; set; }

            public decimal? EmployerPRSA { get; set; }

            public decimal? EmployeePRSA { get; set; }

            public decimal? EmployeeRAC { get; set; }

            public decimal? EmployeeAVC { get; set; }

            public decimal? EmployeeASC { get; set; }

            public decimal? EmployerPEPP { get; set; }

            public decimal? EmployeePEPP { get; set; }
        }

        public class XmlEmployeeId
        {
            public string EmployeePPSN { get; set; }

            public string EmploymentID { get; set; }
        }

        public class XmlName
        {
            public string FirstName { get; set; }

            public string FamilyName { get; set; }
        }

        public class XmlAddress
        {
            [XmlElement("AddressLine")]
            public List<string> AddressLines { get; set; }

            public string County { get; set; }
        }

        public class XmlTaxRate
        {
            public decimal RateCutOff { get; set; }
        }

        public class XmlPrsiClassDetail
        {
            public string PRSIClass { get; set; }

            public short InsurableWeeks { get; set; }
        }

        public class ReportData
        {
            public ReportHeader Header { get; set; }

            public Totals Totals { get; set; }

            public List<Employee> Employees { get; set; }
        }

        public class ReportHeader
        {
            public string EmployerName { get; set; }

            public string EmployerRegistrationNumber { get; set; }

            public DateTime PayPeriodStartDate { get; set; }

            public DateTime PayPeriodEndDate { get; set; }

            public string SubmissionId { get; set; }

            public string PayrollRunReference { get; set; }

            public string SoftwareVersion { get; set; }
        }

        public class Totals
        {
            public int NumberOfEmployees { get; set; }

            public decimal TotalTax { get; set; }

            public decimal TotalEmployeePRSI { get; set; }

            public decimal TotalEmployerPRSI { get; set; }

            public decimal TotalUSC { get; set; }

            public decimal TotalLPT { get; set; }

            public decimal TotalPension { get; set; }
        }

        public class Employee
        {
            public EmploymentDetails EmploymentDetails { get; set; }

            public PaymentDetails PaymentDetails { get; set; }

            public TaxDetails Tax { get; set; }

            public PrsiDetails PRSI { get; set; }

            public UscDetails USC { get; set; }

            public OtherContributions Other { get; set; }
        }

        public class EmploymentDetails
        {
            public string? PPSNumber { get; set; }

            public string? EmploymentId { get; set; }

            public string EmployeeName { get; set; }

            public EmployeeAddress Address { get; set; }

            public DateTime? DateOfBirth { get; set; }

            public string PayFrequency { get; set; }
        }

        public class EmployeeAddress
        {
            public string? AddressLine1 { get; set; }

            public string? AddressLine2 { get; set; }

            public string? County { get; set; }

            public string? Country { get; set; }
        }

        public class PaymentDetails
        {
            public string LineItemId { get; set; }

            public string RPNNumber { get; set; }

            public string CalculationBasis { get; set; }

            public DateTime PayDate { get; set; }

            public decimal GrossPay { get; set; }
        }

        public class TaxDetails
        {
            public decimal? TaxCredits { get; set; }

            public decimal? TaxCutOff { get; set; }

            public decimal? TaxablePay { get; set; }

            public decimal? TaxPaid { get; set; }
        }

        public class PrsiDetails
        {
            public decimal? EmployerPRSIablePay { get; set; }

            public decimal? EmployerPRSIPaid { get; set; }

            public decimal? EmployeePRSIablePay { get; set; }

            public decimal? EmployeePRSIPaid { get; set; }

            public string PRSIClass { get; set; }

            public int InsurableWeeks { get; set; }
        }

        public class UscDetails
        {
            public string USCStatus { get; set; }

            public decimal? USCablePay { get; set; }

            public decimal? USCPaid { get; set; }
        }

        public class OtherContributions
        {
            public decimal? LPTPaid { get; set; }

            public decimal? EmployerRBS { get; set; }

            public decimal? EmployeeRBS { get; set; }

            public decimal? EmployerPRSA { get; set; }

            public decimal? EmployeePRSA { get; set; }

            public decimal? EmployerRAC { get; set; }

            public decimal? EmployeeRAC { get; set; }

            public decimal? EmployerPEPP { get; set; }

            public decimal? EmployeePEPP { get; set; }

            public decimal? EmployeeAVC { get; set; }
        }
    }
}