namespace PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies.IE.ERR
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Xml.Serialization;

    using Azure.Storage.Blobs;

    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;

    public class EnhancedReportingRequirementsDatasource(ICompanyService companyService, ICompanyRunService companyRunService)
    {
        public ReportData GetData(string xmlBlobUrl, long companyId, long runId)
        {
            var blobClient = new BlobClient(new Uri(xmlBlobUrl));
            var response = blobClient.DownloadStreaming() ?? throw new InvalidOperationException("Failed to download xml blob");
            var xmlSerializer = new XmlSerializer(typeof(XmlEnhancedReportingSubmissionRequest));
            var parsedRequest = (XmlEnhancedReportingSubmissionRequest)xmlSerializer.Deserialize(response.Value.Content)!;

            return this.GetMappedReportData(parsedRequest, companyId, runId);
        }

        private ReportData GetMappedReportData(XmlEnhancedReportingSubmissionRequest request, long companyId, long runId)
        {
            return new ReportData
            {
                SubmissionID = request.SubmissionID,
                Header = this.GetMappedHeader(request.Header, companyId, runId),
                ExpenseBenefits = this.GetMappedExpenseBenefits(request.ExpenseBenefits)
            };
        }

        private ReportHeader GetMappedHeader(XmlSubmissionHeader submissionHeader, long companyId, long runId)
        {
            var companyName = companyService.GetCompanyNameAsync(companyId).Result;
            var runSummary = companyRunService.GetRunSummary(runId);

            return new ReportHeader
            {
                RegistrationNumber = submissionHeader.EmployerRegistrationNumber,
                Name = companyName,
                PayPeriod = new PayPeriod
                {
                    StartDate = runSummary.PeriodStartDate,
                    EndDate = runSummary.PeriodEndDate
                },
                SoftwareUsed = new SoftwareUsed
                {
                    Name = submissionHeader.SoftwareUsed.Name,
                    Version = submissionHeader.SoftwareUsed.Version
                },
                PayrollRunReference = submissionHeader.EnhancedReportingRunReference
            };
        }

        private List<ExpenseBenefit> GetMappedExpenseBenefits(List<XmlExpenseBenefit> expenseBenefits)
        {
            return expenseBenefits.Select((expenseBenefit, index) => new ExpenseBenefit
            {
                Heading = $"Expense {index + 1}",
                Index = index,
                EmployeeName = $"{expenseBenefit.Name.FirstName} {expenseBenefit.Name.FamilyName}",
                EmployeePPSNumber = expenseBenefit.EmployeeID.EmployeePPSN,
                EmployeeDateOfBirth = expenseBenefit.DateOfBirth,
                DateOfPayment = expenseBenefit.PaymentDate,
                SubCategory = expenseBenefit.SubCategory,
                EmployeeWorkNumber = expenseBenefit.EmployeeID.EmploymentID,
                NumberOfDays = expenseBenefit.NumberOfDays,
                NonTaxableAmount = expenseBenefit.Amount,
                Category = expenseBenefit.Category
            }).ToList();
        }

        [XmlRoot("EnhancedReportingSubmissionRequest", Namespace = "http://www.ros.ie/schemas/paye-employers/err/v1/enhanced_reporting/")]
        public class XmlEnhancedReportingSubmissionRequest
        {
            public string SubmissionID { get; set; }

            public XmlSubmissionHeader Header { get; set; }

            [XmlElement(ElementName = "ExpensesBenefits")]
            public List<XmlExpenseBenefit> ExpenseBenefits { get; set; }
        }

        public class XmlSubmissionHeader
        {
            public string EmployerRegistrationNumber { get; set; }

            public string EnhancedReportingRunReference { get; set; }

            public SoftwareUsed SoftwareUsed { get; set; }
        }

        public class SoftwareUsed
        {
            public string Name { get; set; }

            public string Version { get; set; }
        }

        public class XmlExpenseBenefit
        {
            public XmlEmployeeId EmployeeID { get; set; }

            public XmlName Name { get; set; }

            public DateTime? DateOfBirth { get; set; }

            public string Category { get; set; }

            public string SubCategory { get; set; }

            public DateTime PaymentDate { get; set; }

            public int? NumberOfDays { get; set; }

            public decimal Amount { get; set; }
        }

        public class XmlEmployeeId
        {
            public string EmployeePPSN { get; set; }

            public string EmploymentID { get; set; }
        }

        public class XmlName
        {
            public string FirstName { get; set; }

            public string FamilyName { get; set; }
        }

        public class ReportData
        {
            public string SubmissionID { get; set; }

            public ReportHeader Header { get; set; }

            public List<ExpenseBenefit> ExpenseBenefits { get; set; }
        }

        public class ReportHeader
        {
            public string RegistrationNumber { get; set; }

            public string Name { get; set; }

            public PayPeriod PayPeriod { get; set; }

            public SoftwareUsed SoftwareUsed { get; set; }

            public string PayrollRunReference { get; set; }
        }

        public class PayPeriod
        {
            public DateTime StartDate { get; set; }

            public DateTime EndDate { get; set; }
        }

        public class ExpenseBenefit
        {
            public string Heading { get; set; }

            public int Index { get; set; }

            public string EmployeeName { get; set; }

            public string EmployeePPSNumber { get; set; }

            public DateTime? EmployeeDateOfBirth { get; set; }

            public DateTime DateOfPayment { get; set; }

            public string? Category { get; set; }

            public string? SubCategory { get; set; }

            public string EmployeeWorkNumber { get; set; }

            public int? NumberOfDays { get; set; }

            public decimal NonTaxableAmount { get; set; }
        }
    }
}