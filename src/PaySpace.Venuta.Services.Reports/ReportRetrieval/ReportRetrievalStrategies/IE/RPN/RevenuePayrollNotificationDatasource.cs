namespace PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies.IE.RPN
{
    using System;
    using System.Text.Json;

    using Azure.Storage.Blobs;

    using PaySpace.Venuta.Services.Abstractions;

    public class RevenuePayrollNotificationDatasource(ICompanyService companyService)
    {
        private static readonly JsonSerializerOptions JsonSerializerOptions = new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };

        public EmployeeRpnRecord GetData(string jsonBlobUrl, long companyId)
        {
            var blobClient = new BlobClient(new Uri(jsonBlobUrl));
            var result = blobClient.DownloadStreaming() ?? throw new InvalidOperationException("Failed to download json blob");

            var employeeRpnRecord = JsonSerializer.Deserialize<EmployeeRpnRecord>(result.Value.Content, JsonSerializerOptions)!;
            employeeRpnRecord.CompanyName = companyService.GetCompanyNameAsync(companyId).Result;

            return employeeRpnRecord;
        }

        public class EmployeeRpnRecord
        {
            public string CompanyName { get; set; }

            public DateTime EffectiveDate { get; set; }

            public string RpnNumber { get; set; }

            public DateTime RpnIssueDate { get; set; }

            public string EmployeePpsn { get; set; }

            public string IncomeTaxCalculationBasis { get; set; }

            public string RpnFirstName { get; set; }

            public string RpnFamilyName { get; set; }

            public string PreviousEmployeePpsn { get; set; }

            public string RpnEmploymentId { get; set; }

            public DateTime EndDate { get; set; }

            public decimal YearlyTaxCredit { get; set; }

            public decimal YearlyRate1CutOff { get; set; }

            public decimal UscRate1Percent { get; set; }

            public decimal YearlyUscRate1CutOff { get; set; }

            public decimal UscRate2Percent { get; set; }

            public decimal YearlyUscRate2CutOff { get; set; }

            public decimal UscRate3Percent { get; set; }

            public decimal YearlyUscRate3CutOff { get; set; }

            public decimal UscRate4Percent { get; set; }

            public decimal YearlyUscRate4CutOff { get; set; }

            public decimal LptToBeDeducted { get; set; }
        }
    }
}