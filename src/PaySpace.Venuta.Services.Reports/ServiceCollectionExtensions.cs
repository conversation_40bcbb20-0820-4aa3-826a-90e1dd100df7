namespace PaySpace.Venuta.Services.Reports
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using DevExpress.DataAccess.Json;
    using DevExpress.DataAccess.Wizard.Services;

    using Microsoft.AspNetCore.Hosting;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.FileProviders;

    using Nager.PublicSuffix.RuleProviders;
    using Nager.PublicSuffix.RuleProviders.CacheProviders;

    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Reports.Services;
    using PaySpace.Venuta.Services.Reports.ContextMenu;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies.TaxBreakdown;
    using PaySpace.Venuta.Services.Reports.Services;
    using PaySpace.Venuta.Services.Reports.Services.CustomPayslips;
    using PaySpace.Venuta.Services.Reports.Services.DataSourceBuilder;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddReportServices(this IServiceCollection services)
        {
            services.AddHttpClient(); //Required for CachedHttpRuleProvider
            services.AddSingleton<ICacheProvider, LocalFileSystemCacheProvider>();
            services.AddSingleton<IRuleProvider, CachedHttpRuleProvider>();
            services.AddSingleton<IReportUrlResolver, ReportUrlResolver>();

            services.AddCountryServices<IReportUserSecurityService>();

            services.AddSingleton<IConnectionProviderService, SqlConnectionProviderFactory>();
            services.AddSingleton<IJsonDataConnectionProviderService, JsonConnectionProviderFactory>();
            services.AddScoped<IClassicReportService, ClassicReportService>();
            services.AddScoped<IReportHeaderService, ReportHeaderService>();
            services.AddScoped<IReportParameterService, ReportParameterService>();
            services.AddScoped<IReportFavouriteService, ReportFavouriteService>();
            services.AddScoped<IReportParameterService, ReportParameterService>();
            services.AddScoped<IReportHistoryStorageService, ReportHistoryStorageService>();
            services.AddSingleton<IReportStorageService, ReportStorageService>();
            services.AddScoped<IReportVersionService, ReportVersionService>();
            services.AddSingleton<IDataSourceParameterService, DataSourceParameterService>();
            services.AddSingleton<IReportDesignerJsonSchemaService, ReportDesignerJsonSchemaService>();
            services.AddScoped<ICustomReportListingService, CustomReportListingService>();
            services.AddScoped<ISystemReportListingService, SystemReportListingService>();
            services.AddScoped<IReportsMessageService, ReportsMessageService>();
            services.AddSingleton<IReportLocalizationService, ReportLocalizationService>();
            services.AddSingleton<IODataService, ODataService>();
            services.AddSingleton<ICustomFieldGenerator, CustomFieldGenerator>();
            services.AddScoped<IDesignerContextLevelService, DesignerContextLevelService>();
            services.AddScoped<ITempPayslipExecutionLoggerService, TempPayslipExecutionLoggerService>();
            services.AddScoped<IReportSecurityService, ReportSecurityService>();
            services.AddSingleton<ITaxBreakdownTableService, TaxBreakdownTableService>();

            services.AddTransient<IStartupFilter, ReportExpressionRegistration>();

            services.AddAutoMapper(
                config =>
                {
                    config.AddMaps(typeof(ServiceCollectionExtensions).Assembly);
                },
                typeof(ServiceCollectionExtensions).Assembly);

            // TODO: Not needed in Hangfire?
            var typesFromCurrentAssembly = typeof(ServiceCollectionExtensions).Assembly.GetTypes();
            services.AddReportParameterStrategyServices(typesFromCurrentAssembly);
            services.AddReportRetrievalStrategiesAndServices(typesFromCurrentAssembly);
            services.AddReportContextMenuStrategiesAndServices(typesFromCurrentAssembly);

            return services;
        }

        public static IServiceCollection AddReportDataSourceBuilderServices(this IServiceCollection services)
        {
            services.AddSingleton<IDataSourceBuilderService, DataSourceBuilderService>();
            services.AddSingleton<IDataSourceBuilderSchemaService, DataSourceBuilderSchemaService>();
            services.AddSingleton<IDataSourceQueryFilterValidationService, DataSourceQueryFilterValidationService>();
            services.AddSingleton<IDataSourceUriBuilderService, DataSourceUriBuilderService>();
            services.AddSingleton<IDataSourceQueryFilterCriteriaService, DataSourceQueryFilterCriteriaService>();
            services.AddSingleton<IDataSourceTypeGeneratorService, DataSourceTypeGeneratorService>();

            return services;
        }

        // TODO: investigate why this method is needed and if it can be removed
        public static IConfigurationBuilder AddDataSourceBuilderConfiguration(this IConfigurationBuilder config)
        {
            var fileProvider = new EmbeddedFileProvider(typeof(ServiceCollectionExtensions).Assembly);
            config.AddJsonFile(fileProvider, "dataSourceBuilderSettings.json", false, true);

            return config;
        }

        private static void AddReportParameterStrategyServices(this IServiceCollection services, Type[] typesFromCurrentAssembly)
        {
            var strategies = GetStrategiesAssignableFrom<ReportParameterStrategyBase>(typesFromCurrentAssembly);
            foreach (var type in strategies)
            {
                services.AddScoped(typeof(ReportParameterStrategyBase), type);
            }
        }

        private static void AddReportRetrievalStrategiesAndServices(this IServiceCollection services, Type[] typesFromCurrentAssembly)
        {
            services.AddTransient<IReportRetrievalService, ReportRetrievalService>();

            var strategies = GetStrategiesAssignableFrom<IReportRetrievalStrategy>(typesFromCurrentAssembly);
            foreach (var type in strategies)
            {
                services.AddTransient(typeof(IReportRetrievalStrategy), type);
            }
        }

        private static void AddReportContextMenuStrategiesAndServices(this IServiceCollection services, Type[] typesFromCurrentAssembly)
        {
            services.AddScoped<IReportContextMenuService, ReportContextMenuService>();

            var contextMenuOptionStrategies = GetStrategiesAssignableFrom<IContextMenuOptionStrategy>(typesFromCurrentAssembly);
            foreach (var type in contextMenuOptionStrategies)
            {
                services.AddScoped(typeof(IContextMenuOptionStrategy), type);
            }

            var standardReportContextMenuOptionStrategies = GetStrategiesAssignableFrom<IStandardReportContextMenuOptionStrategy>(typesFromCurrentAssembly);
            foreach (var type in standardReportContextMenuOptionStrategies)
            {
                services.AddScoped(typeof(IStandardReportContextMenuOptionStrategy), type);
            }
        }

        private static IEnumerable<Type> GetStrategiesAssignableFrom<T>(Type[] typesFromCurrentAssembly)
        {
            return typesFromCurrentAssembly.Where(type => type is { IsInterface: false, IsAbstract: false } && typeof(T).IsAssignableFrom(type));
        }
    }
}