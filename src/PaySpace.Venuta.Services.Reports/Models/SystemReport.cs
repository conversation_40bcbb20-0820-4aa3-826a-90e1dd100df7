namespace PaySpace.Venuta.Services.Reports.Models
{
    public class SystemReport
    {
        public SystemReport(ReportSource reportSource)
        {
            this.ReportSource = reportSource;
        }

        public long? UserId { get; set; }

        public string? ReportId { get; set; }

        public string? ViewerUri { get; set; }

        public string? ReportName { get; set; }

        public string? ReportDescription { get; set; }

        public int ReportCategoryId { get; set; }

        public int? ReportSubCategoryId { get; set; }

        public bool? Enabled { get; set; }

        public bool Designable { get; set; }

        public ReportSource ReportSource { get; private set; }
    }
}