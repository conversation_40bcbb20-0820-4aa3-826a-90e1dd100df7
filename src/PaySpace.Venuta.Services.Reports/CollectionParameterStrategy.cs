namespace PaySpace.Venuta.Services.Reports
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Reports;

    public class CollectionParameterStrategy : ReportParameterStrategyBase, ILoadReportParameter
    {
        private readonly ApplicationContext reportContext;

        public CollectionParameterStrategy(ApplicationContext reportContext)
        {
            this.reportContext = reportContext;
        }

        public override string Name => "CollectionID";

        public override Task<List<ReportParameterResult>> LoadParametersAsync(StrategyParameter strategyParameters)
        {
            return this.reportContext.Set<CompanyCustomFormCategory>().AsNoTracking()
                .Where(_ => _.CompanyId == strategyParameters.CompanyId && _.EmployeeLevel == true)
                .Select(_ => new ReportParameterResult
                {
                    ResultId = _.CustomFormCategoryId,
                    Description = _.FormName
                })
                .OrderBy(_ => _.Description)
                .ToListAsync();
        }

        public override async Task<object> GetDefaultDisplayValueAsync(StrategyParameter strategyParameters)
        {
            var firstOption = await this.LoadParametersAsync(strategyParameters);
            return firstOption.FirstOrDefault()?.ResultId!;
        }
    }
}