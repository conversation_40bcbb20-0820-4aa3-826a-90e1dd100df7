namespace PaySpace.Venuta.Services.Reports
{
    using System;
    using System.Collections.Generic;

    public static class ReportConstants
    {
        public const string MyReportsCategoryCode = "My Reports";
        public const int MyReportsCategoryId = 10;
        public const string ExtractFilesCategoryCode = "ExtractFiles";
        public const int ExtractFilesCategoryId = 11;
        public const string DataSourceSettingsExtensionName = "DataSourceSettings";
        public const long DevExpressReportType = 2;
        public const string SystemReportsCategoryCode = "SystemReports";
        public const int SystemReportsCategoryId = 12;

        public static List<string> FrequencyFilterParameters => new()
        {
           Parameters.PeriodCode,
           Parameters.DateRange,
           Parameters.RunId
        };

        public static class SystemReports
        {
            public const string TaxBreakdown = "TaxBreakdown";
        }

        public static class Tables
        {
            public const string PayslipLine = "PayslipLine";
            public const string Employee = "Employee";
        }

        public static class Parameters
        {
            public const string CompanyId = "CompanyId";
            public const string CompanyIds = "CompanyIds";
            public const string FrequencyId = "FrequencyId";
            public const string EmployeeNumber = "EmployeeNumber";
            public const string RunId = "RunId";
            public const string PeriodCode = "PeriodCode";
            public const string DateRange = "DateRange";
            public const string FromDate = "FromDate";
            public const string ToDate = "ToDate";
            public const string EffectiveDate = "EffectiveDate";
            public const string RegionId = "RegionId";
            public const string RegionIds = "RegionIds";
            public const string ProjectIds = "ProjectIds";
            public const string OrganisationUnitId = "OrgUnitId";
            public const string PositionIds = "PositionIds";
            public const string PayslipActions = "PayslipActions";
            public const string ComponentCodes = "ComponentCodes";
        }

        public static class Metadata
        {
            public const string UserId = "userId";
            public const string ReportCategory = "reportCategoryId";
            public const string ReportSubCategory = "reportSubCategoryId";
            public const string TemplateDisplayName = "templateDisplayName";
            public const string Description = "description";
            public const string ChangesMadeDescription = "changesmadedescription";
            public const string IsReadOnly = "readOnly";
            public const string Caption = "Caption";
            public const string Lookup = "Lookup";
            public const string Enum = "Enum";
            public const string ExportSeparator = "ExportSeparator";
            public const string ExportQuoteStringsWithSeparators = "ExportQuoteStringsWithSeparators";
            public const string Enabled = "enabled";
            public const string Designable = "designable";
            public const string ReportListId = "reportlistid";

            public static class DefaultValues
            {
                public const bool IsEnabled = false;
                public const bool IsDesignable = false;
            }
        }

        public static class Tags
        {
            public const string UserId = "userId";
            public const string RelatedEntityId = "RelatedEntityId";
            public const string InstanceId = "InstanceId";
            public const string ContextLevel = "contextLevel";
            public const string ReportCategory = "reportCategoryId";
            public const string ReportSubCategory = "reportSubCategoryId";
            public const string ReportType = "reportType";
            public const string TemplateDisplayName = "templateDisplayName";
            public const string Description = "description";
            public const string PathGroup = "reportPathGroup";
            public const string IsChangesMadeDescriptionEncoded = "IsChangesMadeDescriptionEncoded";
            public const string IsTemplateDisplayNameEncoded = "IsTemplateDisplayNameEncoded";
            public const string IsDescriptionEncoded = "IsDescriptionEncoded";
        }

        public static class CacheKeys
        {
            public static Func<long, long, bool, string> CustomReportsList { get; } = (companyId, frequencyId, includeSubCategories) => $"Company:CustomReports:{companyId}:{frequencyId}:{includeSubCategories}";
        }

        public static class CustomPayslips
        {
            public const int ReportHeaderId = 70;

            // TODO: Remove this once everyone has migrated to single file.
            public const string PayslipHeaderReportId = "PayslipHeader";

            public const string PayslipDetailReportId = "PayslipDetail";

            public const string ParentFolderPath = "Payslips";

            public const string NewPayslipReportId = "Payslip";
        }

        public static class CustomReports
        {
            public const string ParentFolderPath = "CustomReports";
        }
    }
}