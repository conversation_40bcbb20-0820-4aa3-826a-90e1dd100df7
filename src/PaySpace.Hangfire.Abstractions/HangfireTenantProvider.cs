namespace PaySpace.Hangfire.Abstractions
{
    using System;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models;

    public class HangfireTenantProvider : ITenantProvider
    {
        private readonly long userId;
        private readonly UserType userType;
        private readonly long companyId;
        private readonly long? frequencyId;
        private readonly string taxCountryCode;
        private readonly bool canEditHistoricalRecords;

        public HangfireTenantProvider(
            long userId,
            UserType userType,
            long companyId,
            long? frequencyId,
            string taxCountryCode,
            bool canEditHistoricalRecords)
        {
            this.userId = userId;
            this.userType = userType;
            this.companyId = companyId;
            this.frequencyId = frequencyId;
            this.taxCountryCode = taxCountryCode;
            this.canEditHistoricalRecords = canEditHistoricalRecords;
        }

        public long GetUserId()
        {
            return this.userId;
        }

        public UserType GetUserType()
        {
            return this.userType;
        }

        public long GetAgencyId()
        {
            throw new NotImplementedException();
        }

        public long? GetCompanyGroupId()
        {
            throw new NotImplementedException();
        }

        public long? GetCompanyId()
        {
            return this.companyId;
        }

        public long? GetEmployeeId()
        {
            return null;
        }

        public long? GetFrequencyId()
        {
            return this.frequencyId;
        }

        public string? GetTaxCountryCode()
        {
            return this.taxCountryCode;
        }

        public int GetDecimalPlaces()
        {
            return 2;
        }

        public bool CanEditHistoricalRecords()
        {
            return this.canEditHistoricalRecords;
        }
    }
}