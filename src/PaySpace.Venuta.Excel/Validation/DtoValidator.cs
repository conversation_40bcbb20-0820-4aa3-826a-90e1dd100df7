namespace PaySpace.Venuta.Excel.Validation
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Data.Models.Workflow;
    using PaySpace.Venuta.Excel.Abstractions.Exceptions;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Localization;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;

    using FluentValidationResult = FluentValidation.Results.ValidationResult;

    public interface IDtoValidator<TEntity>
    {
        Task<FluentValidationResult> ValidateAsync(MutatorContext mutatorContext, TEntity entity, string ruleSet, CancellationToken cancellationToken);
    }

    public class DtoValidator<TEntity> : AbstractValidator<TEntity>, IDtoValidator<TEntity>
    {
        private readonly ApplicationContext applicationContext;
        private readonly IAuthorizationService authorizationService;
        private readonly IStringLocalizer localizer;
        private readonly IModelMetadataProvider modelMetadataProvider;
        private readonly IObjectModelValidator objectModelValidator;
        private readonly IServiceProvider serviceProvider;
        private readonly IScopedCache scopedCache;
        private static readonly char[] separator = new[] { ',', ';' };

        public DtoValidator(
            ApplicationContext applicationContext,
            IAuthorizationService authorizationService,
            IStringLocalizer<TEntity> localizer,
            IModelMetadataProvider modelMetadataProvider,
            IObjectModelValidator objectModelValidator,
            IServiceProvider serviceProvider,
            IScopedCache scopedCache)
        {
            this.applicationContext = applicationContext;
            this.authorizationService = authorizationService;
            this.localizer = localizer;
            this.modelMetadataProvider = modelMetadataProvider;
            this.objectModelValidator = objectModelValidator;
            this.serviceProvider = serviceProvider;
            this.scopedCache = scopedCache;

            this.RuleSet(RuleSetNames.CreateAndUpdate, () =>
            {
                this.RuleFor(_ => _)
                    .MustAsync((entity, dtoEntity, context, cancellationToken) => this.IsAllowedToUpdateAsync(entity, context));

                this.RuleFor(_ => _)
                    .CustomAsync(this.ValidateAnnotationsAsync);
            });

            this.RuleSet(RuleSetNames.Delete, () =>
            {
                this.RuleFor(_ => _)
                    .MustAsync((entity, dtoEntity, context, cancellationToken) => this.IsAllowedToUpdateAsync(entity, context));
            });
        }

        public virtual Task<FluentValidationResult> ValidateAsync(MutatorContext mutatorContext, TEntity entity, string ruleSet, CancellationToken cancellationToken)
        {
            var ruleSets = ruleSet.Split(separator, StringSplitOptions.RemoveEmptyEntries).Select(x => x.Trim()).ToArray();
            var context = this.SetValidationContext(mutatorContext, entity, ruleSets);
            if (mutatorContext.TryGetValue(PaySpaceConstants.ValidationInfo, out var validationInfo))
            {
                context.RootContextData[PaySpaceConstants.ValidationInfo] = validationInfo;
            }

            return this.ValidateAsync(context, cancellationToken);
        }

        protected virtual ValidationContext<TEntity> SetValidationContext(MutatorContext mutatorContext, TEntity entity, string[] ruleSets)
        {
            var context = ValidationContext<TEntity>.CreateWithOptions(entity, opt => opt.IncludeRuleSets(ruleSets));

            context.RootContextData[PaySpaceConstants.SecurityProfile] = mutatorContext.Profile;
            context.RootContextData[PaySpaceConstants.FrequencyId] = mutatorContext.FrequencyId;

            return context;
        }

        private async Task<bool> IsAllowedToUpdateAsync(TEntity dto, ValidationContext<TEntity> validatorContext)
        {
            var profile = GetValue<ISecurityProfile>("Profile", validatorContext);
            var frequencyId = GetValue<long?>(PaySpaceConstants.FrequencyId, validatorContext);

            var requirements = new List<IAuthorizationRequirement>()
            {
                new MutatorRequirement(profile, MetadataHelper.GetSecurityArea(typeof(TEntity)))
            };

            if (dto is IEmployeeNumberEntity employeeEntity)
            {
                if (employeeEntity.EmployeeId > default(long))
                {
                    var isEmployeeCurrentlyInWorkflow = await this.IsEmployeeCurrentlyInWorkflowAsync(employeeEntity.EmployeeId);

                    requirements.Add(new MutatorEmployeeAccessRequirement(profile, employeeEntity.EmployeeId));
                    requirements.Add(new MutatorOrganizationAccessRequirement(profile.UserId, profile.CompanyId, employeeEntity.EmployeeId, isEmployeeCurrentlyInWorkflow));
                    requirements.Add(new MutatorFrequencyAccessRequirement(profile.UserId, profile.AgencyId, profile.CompanyId, employeeEntity.EmployeeId, frequencyId));
                    requirements.Add(new MutatorEmployeeFrequencyAccessRequirement(employeeEntity.EmployeeId, frequencyId));
                }
                else if (dto is EmployeeDto)
                {
                    requirements.Add(new MutatorEmployeeCreateAccessRequirement(profile));
                }
            }

            var authorizationResult = await this.authorizationService.AuthorizeAsync(null, dto, requirements);
            if (!authorizationResult.Succeeded)
            {
                if (authorizationResult.Failure.FailedRequirements.Any(requirement => requirement is MutatorEmployeeCreateAccessRequirement))
                {
                    throw new MutatorUnauthorizedAccessException(this.localizer.GetString("CreateAccessDenied"));
                }

                if (authorizationResult.Failure.FailedRequirements.Any(requirement => requirement is MutatorEmployeeFrequencyAccessRequirement))
                {
                    throw new MutatorUnauthorizedAccessException(this.localizer.GetString("InvalidFrequency"));
                }

                if (dto is IEmployeeNumberEntity employeeNumberEntity)
                {
                    if (authorizationResult.Failure.FailedRequirements.Any(requirement => requirement is MutatorOrganizationAccessRequirement))
                    {
                        throw new MutatorUnauthorizedAccessException(this.localizer.GetString("AccessDeniedForOrgUnit", employeeNumberEntity.EmployeeNumber));
                    }

                    throw new MutatorUnauthorizedAccessException(this.localizer.GetString("AccessDeniedForEmp", employeeNumberEntity.EmployeeNumber));
                }

                throw new MutatorUnauthorizedAccessException(this.localizer.GetString("AccessDenied"));
            }

            return true;
        }

        protected List<(string PropertyName, string ErrorMessage)> GetAnnotationErrors(TEntity dto)
        {
            var controllerContext = this.CreateControllerContext();
            this.objectModelValidator.Validate(controllerContext, new ValidationStateDictionary(), string.Empty, dto);

            if (!controllerContext.ModelState.IsValid)
            {
                return controllerContext.ModelState.Keys.Select(_ => (_, string.Join(", ", controllerContext.ModelState[_]!.Errors.Select(e => e.ErrorMessage)))).ToList();
            }

            return [];
        }

        protected virtual Task ValidateAnnotationsAsync(TEntity dto, ValidationContext<TEntity> context, CancellationToken token)
        {
            var annotationErrors = this.GetAnnotationErrors(dto);
            foreach (var annotationError in annotationErrors)
            {
                context.AddFailure(annotationError.PropertyName, annotationError.ErrorMessage);
            }

            return Task.CompletedTask;
        }

        private ControllerContext CreateControllerContext()
        {
            var controllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    RequestServices = this.serviceProvider
                }
            };

            // See: https://github.com/FluentValidation/FluentValidation/blob/master/src/FluentValidation.AspNetCore/MvcValidationHelper.cs#L43
            controllerContext.HttpContext.Items.Add("_FV_ROOT_METADATA", this.modelMetadataProvider.GetMetadataForType(typeof(TEntity)));

            return controllerContext;
        }

        private static T GetValue<T>(string key, ValidationContext<TEntity> validatorContext)
        {
            return (T)validatorContext.RootContextData[key];
        }

        private Task<bool> IsEmployeeCurrentlyInWorkflowAsync(long employeeId)
        {
            var cacheKey = $"IsEmployeeCurrentlyInWorkflow:{employeeId}";
            return this.scopedCache.GetOrCreateAsync(
                cacheKey,
                () => (from w in this.applicationContext.Set<WorkflowStepRequired>()
                       join h in this.applicationContext.Set<WorkflowHeader>() on w.WorkFlowName equals h.WorkflowName
                       where w.EmployeeId == employeeId
                       select w).AnyAsync());
        }
    }
}