namespace PaySpace.Venuta.Excel.CustomFields
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Linq.Dynamic.Core;

    public static class ComparerHelper
    {
        private static readonly Dictionary<string, string> Operations = new()
        {
            { "eq", "==" },
            { "gt", ">" },
            { "lt", "<" },
            { "ne", "!=" },
            { "gte", ">=" },
            { "lte", "<=" }
        };

        private static readonly ConcurrentDictionary<Type, ConcurrentDictionary<string, Delegate>> Cache = new();

        public static bool Compare(string operation, Type type, object? left, object? right)
        {
            // convert left and right to the correct type.
            if (type == typeof(DateTime))
            {
                var leftConverted = left is string leftDate ? DateTime.TryParse(leftDate, out var leftValue) ? leftValue : default(bool?) : left;
                var rightConverted = right is string rightDate ? DateTime.TryParse(rightDate, out var rightValue) ? rightValue : default(bool?) : right;

                return CompareInternal(operation, type, leftConverted, rightConverted);
            }

            if (type == typeof(bool))
            {
                var leftConverted = left is string leftBool ? bool.TryParse(leftBool, out var leftValue) ? leftValue : default(bool?) : left;
                var rightConverted = right is string rightBool ? bool.TryParse(rightBool, out var rightValue) ? rightValue : default(bool?) : right;

                return CompareInternal(operation, type, leftConverted, rightConverted);
            }

            if (type == typeof(double))
            {
                var leftConverted = left is string leftDouble ? double.TryParse(leftDouble, out var leftValue) ? leftValue : default(double?) : left;
                var rightConverted = right is string rightDouble ? double.TryParse(rightDouble, out var rightValue) ? rightValue : default(double?) : right;

                return CompareInternal(operation, type, leftConverted, rightConverted);
            }

            if (type.IsEnum || type == typeof(int))
            {
                var leftConverted = left is string leftInt ? int.TryParse(leftInt, out var leftValue) ? leftValue : default(int?) : (int)left;
                var rightConverted = right is string rightInt ? int.TryParse(rightInt, out var rightValue) ? rightValue : default(int?) : (int)right;

                return CompareInternal(operation, typeof(int), leftConverted, rightConverted);
            }

            return CompareInternal(operation, type, left, right);
        }

        private static bool CompareInternal(string operation, Type type, object? left, object? right)
        {
            var containerType = typeof(Container<>);
            var valuesType = containerType.MakeGenericType(type);

            var expression = GetCompiledExpression(valuesType, operation);

            var values = Activator.CreateInstance(valuesType, left, right);

            return (bool?)expression.DynamicInvoke(values) ?? false;
        }

        private static Delegate GetCompiledExpression(Type type, string operation)
        {
            var operators = Cache.GetOrAdd(type, _ => new ConcurrentDictionary<string, Delegate>());

            return operators.GetOrAdd(operation, _ => DynamicExpressionParser.ParseLambda(type, typeof(bool), $"c => c.Left {Operations[operation]} c.Right").Compile());
        }

        private sealed class Container<T>
        {
            public Container(object left, object right)
            {
                this.Left = (T)left;
                this.Right = (T)right;
            }

            public T Left { get; set; }

            public T Right { get; set; }
        }
    }
}