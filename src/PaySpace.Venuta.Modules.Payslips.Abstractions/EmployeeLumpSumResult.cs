namespace PaySpace.Venuta.Modules.Payslips.Abstractions
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using Maddalena;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.EmployeeLumpSum.Area)]
    public class EmployeeLumpSumResult
    {
        public long? EmployeeLumpSumId { get; set; }

        public long EmployeeId { get; set; }

        public long PayslipId { get; set; }

        public long RunId { get; set; }

        [Required]
        [DataType(DataType.Date)]
        public DateTime? DirectiveIssuedDate { get; set; }

        [Required]
        [DropDownList]
        public string TaxCode { get; set; }

        [Required]
        public decimal DirectiveAmount { get; set; }

        [CountryRequired(CountryCode.NA)]
        [CountryVisible(CountryCode.NA)]
        public decimal? TaxFreeDirectiveAmount { get; set; }

        [Required]
        public decimal DirectiveTax { get; set; }

        [CountryRequired(CountryCode.ZA, CountryCode.SZ, CountryCode.NA)]
        public string DirectiveNumber { get; set; }

        public string ReferenceNumber { get; set; }

        public string ConfirmDeleteMessage { get; set; }

        public string TaxCodeId => this.TaxCode.Split('-')[1];

        public long ComponentCompanyId => long.Parse(this.TaxCode.Split('-')[0]);

        public string RunDescription { get; set; }

        public string PeriodCode { get; set; }

        // This payslipId is used for comparing reversals.
        [NotMapped]
        public long? CurrentPayslipId { get; set; }
    }
}