namespace PaySpace.Venuta.Excel.Bureau.Mutators
{
    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;
    using PaySpace.Venuta.Storage;

    internal sealed class BureauComponentVariableMutator : EntityMutator<BureauComponentVariableDto, ComponentVariable, long>
    {
        public BureauComponentVariableMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<BureauComponentVariableDto, ComponentVariable> entityValidator,
            IAttachmentStorageService attachmentService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
        }
    }
}