namespace PaySpace.Venuta.Excel.Bureau
{
    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Venuta.Data.Models.Dto.Bureau;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Bureau.Mutators;
    using PaySpace.Venuta.Excel.Bureau.UploadMutators;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Readers;
    using PaySpace.Venuta.Excel.Services.Uploads;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions.Models;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddBureauExcelServices(this IServiceCollection services)
        {
            services.AddScoped<IEntityMutator<BureauPublicHolidayDto, PublicHoliday, int>, BureauPublicHolidayMutator>();
            return services;
        }

        public static IServiceCollection AddBureauExcelUploadServices(this IServiceCollection services)
        {
            services.AddScoped<IUploadEntityMutator<BureauPublicHolidayDto, PublicHoliday, int>, BureauPublicHolidayUploadMutator>();
            services.AddScoped<IExcelUploadService<BureauPublicHolidayDto, PublicHoliday, int>, FileUploadService<BureauPublicHolidayDto, PublicHoliday, int>>();
            services.AddScoped<IExcelFileReader<BureauPublicHolidayDto, int>, ExcelFileReader<BureauPublicHolidayDto, int>>();
            return services;
        }
    }
}