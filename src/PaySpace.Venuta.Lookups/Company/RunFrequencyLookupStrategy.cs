namespace PaySpace.Venuta.Lookups.Company
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class RunFrequencyLookupStrategy : LookupStrategy
    {
        private readonly IEnumService enumService;
        private readonly ICompanyService companyService;

        public RunFrequencyLookupStrategy(IEnumService enumService, ICompanyService companyService)
        {
            this.enumService = enumService;
            this.companyService = companyService;
        }

        public override string Name { get; } = "RunFrequency";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData(companyId)
                .Select(_ => new LookupDataSet { Value = _.PayslipCode, Description = _.PayslipDescription, Id = _.FrequencyId })
                .OrderBy(_ => _.Description);
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            var id = this.GetData(companyId)
                .Where(_ => _.PayslipCode.Equals(value, StringComparison.InvariantCultureIgnoreCase)
                        || _.PayslipDescription.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.FrequencyId)
                .FirstOrDefault();

            return id != default ? id : null;
        }

        private IQueryable<EnumPayslipFrequency> GetData(long companyId)
        {
            var taxCountryId = this.companyService.GetTaxCountryId(companyId);
            var payslipFrequencies = this.enumService.GetPayslipFrequency().AsQueryable();

            switch (taxCountryId)
            {
                case (int)TaxCountry.Singapore:
                    payslipFrequencies = payslipFrequencies
                        .Where(_ => _.FrequencyId == (int)PayslipFrequency.Monthly);
                    break;
                case (int)TaxCountry.Canada:
                    break;
                default:
                    payslipFrequencies = payslipFrequencies
                        .Where(_ => _.FrequencyId != (int)PayslipFrequency.SemiMonthly);
                    break;
            }

            return payslipFrequencies;
        }
    }
}