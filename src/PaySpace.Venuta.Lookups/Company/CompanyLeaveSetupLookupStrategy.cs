namespace PaySpace.Venuta.Lookups.Company
{
    using System;
    using System.Linq;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;

    public class CompanyLeaveSetupLookupStrategy : LookupStrategy, ILookupStrategy<LeaveLookupDataSet>
    {
        private readonly ReadOnlyContext context;

        public CompanyLeaveSetupLookupStrategy(ReadOnlyContext context)
        {
            this.context = context;
        }

        public override string Name => "CompanyLeaveSetup";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData(companyId)
                .Select(
                    _ => new LeaveLookupDataSet
                    {
                        // Value format: 'CompanyLeaveSchemeName - CompanyLeaveSetupDescription'
                        Value = _.Value,
                        Description = _.CompanyLeaveSetupDesc,
                        CompanyLeaveScheme = _.CompanyLeaveSchemeName,
                        LeaveBucket = _.CompanyLeaveSetupDesc,
                        LeaveType = _.LeaveType,
                        IsEmployeeDefined = _.IsEmployeeDefined
                    })
                .OrderByDescending(_ => _.CompanyLeaveScheme);
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            var id = this.GetData(companyId)
                .Where(_ => _.Value == value)
                .Select(_ => _.CompanyLeaveSetupId)
                .FirstOrDefault();

            return id != default ? id : null;
        }

        private IQueryable<LeaveData> GetData(long companyId)
        {
            return from sch in this.context.Set<CompanyLeaveScheme>().TagWithSource()
                join stp in this.context.Set<CompanyLeaveSetup>()
                    on sch.CompanyLeaveSchemeId equals stp.CompanyLeaveSchemeId
                where sch.CompanyId == companyId
                select new LeaveData
                {
                    Value = sch.SchemeName + " - " + stp.LeaveDescription,
                    CompanyLeaveSetupId = stp.CompanyLeaveSetupId,
                    CompanyLeaveSetupDesc = stp.LeaveDescription,
                    CompanyLeaveSchemeName = sch.SchemeName,
                    LeaveType = stp.LeaveType,
                    IsEmployeeDefined = this.context.Set<CompanyLeaveDetail>()
                        .Where(_ => _.CompanyLeaveSetupId == stp.CompanyLeaveSetupId && _.EffectiveDate.Date <= DateTime.Today)
                        .OrderByDescending(_ => _.EffectiveDate)
                        .Select(_ => _.ApplyEmployeeDefined == true)
                        .FirstOrDefault()
                };
        }

        private sealed class LeaveData
        {
            public long CompanyLeaveSetupId { get; init; }

            // Format: 'CompanyLeaveSchemeName - CompanyLeaveSetupDescription'
            public string? Value { get; init; }

            public string? CompanyLeaveSetupDesc { get; init; }

            public string? CompanyLeaveSchemeName { get; init; }

            public LeaveType LeaveType { get; init; }

            public bool IsEmployeeDefined { get; set; }
        }
    }
}