namespace PaySpace.Venuta.Lookups.Employee
{
    using System.Linq;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;

    public class DefaultTemplateLookupStrategy : LookupStrategy
    {
        private readonly ApplicationContext context;

        public DefaultTemplateLookupStrategy(ApplicationContext context)
        {
            this.context = context;
        }

        public override string Name { get; } = "DefaultTemplate";

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return this.GetData(companyId)
                .Select(_ => new LookupDataSet
                {
                    Value = _.Title,
                    Description = _.Description,
                    Id = _.TemplateId
                })
                .OrderBy(_ => _.Value)
                .TagWithSource();
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            var id = this.GetData(companyId)
                .Where(_ => _.Title == value || _.Description == value)
                .Select(_ => _.TemplateId)
                .FirstOrDefault();

            return id != default ? id : null;
        }

        private IQueryable<CompanyTemplateDefinition> GetData(long companyId)
        {
            return this.context.Set<CompanyTemplateDefinition>()
                .AsNoTracking()
                .Where(_ => _.CompanyId == companyId && _.StatusId == (int)TemplateStatus.Active)
                .OrderBy(_ => _.TemplateId);
        }
    }
}