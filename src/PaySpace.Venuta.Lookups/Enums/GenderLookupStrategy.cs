namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Globalization;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;
    using PaySpace.Venuta.Services.Abstractions;

    public class GenderLookupStrategy : TranslatedEnumLookupStrategy, ICountryLookupStrategy<LookupDataSet>
    {
        private readonly ICompanyService companyService;
        private readonly IEnumService enumService;

        public GenderLookupStrategy(ICompanyService companyService, IEnumService enumService)
        {
            this.companyService = companyService;
            this.enumService = enumService;
        }

        public override string Name => "Gender";

        public IQueryable<LookupDataSet> GetCountryLookupData(int taxCountryId)
        {
            return MapToLookup(this.GetDataByCountry(taxCountryId));
        }

        protected override object? GetCountryEnumValueId(string value, int taxCountryId, CultureInfo? culture)
        {
            return TryResolveGenderId(value, this.GetDataByCountry(taxCountryId));
        }

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            return MapToLookup(this.GetDataByCompany(companyId, null));
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            return TryResolveGenderId(value, this.GetDataByCompany(companyId, null));
        }

        private IQueryable<EnumGender> GetDataByCompany(long companyId, CultureInfo? culture)
        {
            var countryId = this.companyService.GetTaxCountryId(companyId);
            return this.enumService.GetGenders(countryId, culture).AsQueryable();
        }

        private IQueryable<EnumGender> GetDataByCountry(int countryId)
        {
            return this.enumService.GetGenders(countryId).AsQueryable();
        }

        private static int? TryResolveGenderId(string value, IQueryable<EnumGender> data)
        {
            if (int.TryParse(value, out var genderId))
            {
                return data.FirstOrDefault(_ => _.GenderId == genderId)?.GenderId;
            }

            return data.FirstOrDefault(_ => _.GenderCode.Equals(value, StringComparison.InvariantCultureIgnoreCase)
                                         || _.GenderDescription.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                       ?.GenderId;
        }

        private static IQueryable<LookupDataSet> MapToLookup(IQueryable<EnumGender> data)
        {
            return data.Select(_ => new LookupDataSet
            {
                Value = _.GenderDescription,
                Description = _.GenderDescription,
                Id = _.GenderId
            }).OrderBy(_ => _.Value);
        }
    }
}