namespace PaySpace.Venuta.Lookups.Enums
{
    using System;
    using System.Linq;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;
    using PaySpace.Venuta.Services.Abstractions;

    public class AddressStreetTypeLookupStrategy : LookupStrategy, ICountryLookupStrategy<LookupDataSet>
    {
        private readonly IEnumService enumService;
        private readonly ICompanyService companyService;

        public AddressStreetTypeLookupStrategy(IEnumService enumService, ICompanyService companyService)
        {
            this.enumService = enumService;
            this.companyService = companyService;
        }

        public override string Name { get; } = "AddressStreetType";

        public IQueryable<LookupDataSet> GetCountryLookupData(int taxCountryId)
        {
            return this.GetData(taxCountryId)
                .Select(x => new LookupDataSet { Value = x.FieldCode, Description = x.FieldCodeDescription, Id = x.AddressStreetTypeId })
                .OrderBy(x => x.Value);
        }

        public object? GetCountryValueId(string value, int taxCountryId)
        {
            return this.GetData(taxCountryId)
                .Where(_ => _.FieldCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.AddressStreetTypeId)
                .FirstOrDefault();
        }

        public override IQueryable<LookupDataSet> GetLookupData(long companyId, long? frequencyId)
        {
            var countryId = this.companyService.GetTaxCountryId(companyId);
            return this.GetData(countryId)
                .Select(x => new LookupDataSet { Value = x.FieldCode, Description = x.FieldCodeDescription, Id = x.AddressStreetTypeId })
                .OrderBy(x => x.Value);
        }

        public override object? GetValueId(string value, long companyId, long? frequencyId)
        {
            var countryId = this.companyService.GetTaxCountryId(companyId);
            return this.GetData(countryId)
                .Where(_ => _.FieldCode.Equals(value, StringComparison.InvariantCultureIgnoreCase))
                .Select(_ => _.AddressStreetTypeId)
                .FirstOrDefault();
        }

        private IQueryable<EnumAddressStreetType> GetData(int countryId)
        {
            return this.enumService.GetAddressStreetType(countryId).AsQueryable();
        }
    }
}
