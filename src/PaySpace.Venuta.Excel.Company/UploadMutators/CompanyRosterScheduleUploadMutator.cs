namespace PaySpace.Venuta.Excel.Company.UploadMutators
{
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Storage;

    public class CompanyRosterScheduleUploadMutator : BulkUploadMutator<CompanyRosterScheduleDto, CompanyRosterSchedule, long>
    {
        private readonly IMapper mapper;

        public CompanyRosterScheduleUploadMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IAttachmentStorageService attachmentService,
            IEntityValidator<CompanyRosterScheduleDto, CompanyRosterSchedule> entityValidator)
            : base(mapper, applicationContext, attachmentService, entityValidator)
        {
            this.mapper = mapper;
        }

        protected override Task<CompanyRosterScheduleDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set
                .Where(_ => _.RosterScheduleId == key && _.CompanyId == context.CompanyId)
                .ProjectTo<CompanyRosterScheduleDto>(this.mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }

        protected override Task<CompanyRosterSchedule?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
                .Include(_ => _.Roster)
                .Include(_ => _.ShiftType)
                .ThenInclude(_ => _.WeekTemplate)
                .FirstOrDefaultAsync(_ => _.RosterScheduleId == key && _.CompanyId == context.CompanyId);
        }
    }
}
