namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Linq.Dynamic.Core;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Excel.Abstractions.Exceptions;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Storage;

    public class CompanyLeaveBucketMappingMutator : EntityMutator<CompanyLeaveBucketMappingDto, CompanyLeaveBucketMapping, long>
    {
        private readonly IMapper mapper;

        public CompanyLeaveBucketMappingMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<CompanyLeaveBucketMappingDto, CompanyLeaveBucketMapping> entityValidator,
            IAttachmentStorageService attachmentService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.mapper = mapper;
        }

        protected override Task AfterMapAsync(MutatorContext context, CompanyLeaveBucketMappingDto dto, CompanyLeaveBucketMapping entity)
        {
            entity.CompanyLeaveBucketMappingDetails = this.mapper.Map<List<CompanyLeaveBucketMappingDetail>>(dto.LeaveBucketMappingDetails);

            return base.AfterMapAsync(context, dto, entity);
        }

        protected override Task<CompanyLeaveBucketMapping?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
             .Include(_ => _.CompanyLeaveBucketMappingDetails)
             .FirstOrDefaultAsync(_ => _.LeaveMappingId == key && _.CompanyId == context.CompanyId);
        }

        protected override Task<CompanyLeaveBucketMappingDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set
                .Where(_ => _.LeaveMappingId == key && _.CompanyId == context.CompanyId)
                .ProjectTo<CompanyLeaveBucketMappingDto>(this.mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, CompanyLeaveBucketMapping entity)
        {
            var dto = this.Set
                .Where(_ => _.LeaveMappingId == entity.LeaveMappingId && _.CompanyId == context.CompanyId)
                .ProjectTo<CompanyLeaveBucketMappingDto>(this.mapper.ConfigurationProvider)
                .FirstOrDefault();

            if (dto != null)
            {
                entity.FromLeaveScheme = dto.FromLeaveScheme;
                entity.ToLeaveScheme = dto.ToLeaveScheme;

                entity.CompanyLeaveBucketMappingDetails = entity.CompanyLeaveBucketMappingDetails
                    .Zip(
                        dto.LeaveBucketMappingDetails,
                        (entityItem, dtoItem) =>
                        {
                            entityItem.FromLeaveBucket = dtoItem.FromLeaveBucket;
                            entityItem.ToLeaveBucket = dtoItem.ToLeaveBucket;
                            entityItem.CompanyId = entity.CompanyId;
                            return entityItem;
                        })
                    .ToList();
            }
            else
            {
                throw new NotFoundException();
            }

            return base.DeleteAsync(context, employeeId, entity);
        }
    }
}
