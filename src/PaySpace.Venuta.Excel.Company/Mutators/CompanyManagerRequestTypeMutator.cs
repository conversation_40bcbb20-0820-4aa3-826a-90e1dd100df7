namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    public class CompanyManagerRequestTypeMutator : EntityMutator<CompanyManagerRequestTypeDto, CompanyManagerRequestType, long>
    {
        private readonly ICompanyAttachmentService companyAttachmentService;
        private readonly ICompanyManagerRequestTypeAttachmentService requestTypeAttachmentService;

        public CompanyManagerRequestTypeMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<CompanyManagerRequestTypeDto, CompanyManagerRequestType> entityValidator,
            IAttachmentStorageService attachmentService,
            ICompanyAttachmentService companyAttachmentService,
            ICompanyManagerRequestTypeAttachmentService requestTypeAttachmentService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.companyAttachmentService = companyAttachmentService;
            this.requestTypeAttachmentService = requestTypeAttachmentService;
        }

        protected override async Task<CompanyManagerRequestType?> FindAsync(MutatorContext context, long key)
        {
            // attachments have to be loaded eagerly
            return await this.Set
                .Include(_ => _.RequestTypeAttachments)
                .FirstOrDefaultAsync(_ => _.RequestTypeId == key && _.CompanyId == context.CompanyId);
        }

        protected override async Task BeforeValidateAsync(MutatorContext context, CompanyManagerRequestTypeDto dto, CompanyManagerRequestType entity)
        {
            if (dto.RequestTypeAttachments is not null)
            {
                var newAttachmentIds = dto.RequestTypeAttachments
                    .Skip(entity.RequestTypeAttachments.Count) // skip already existing attachments
                    .Distinct().ToArray();

                this.RemoveMissingAttachments(entity, newAttachmentIds);
                await this.AddValidAttachmentIds(entity, newAttachmentIds);
            }

            await base.BeforeValidateAsync(context, dto, entity);
        }

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, CompanyManagerRequestType entity)
        {
            this.requestTypeAttachmentService.DeleteAttachmentsFromRequestType(entity);
            return base.DeleteAsync(context, employeeId, entity);
        }

        private void RemoveMissingAttachments(CompanyManagerRequestType entity, long[] attachmentIds)
        {
            // Remove attachments from the entity which are not in the DTO
            var attachmentsToRemove = entity.RequestTypeAttachments
                .Where(ea => !attachmentIds.Any(_ => _ == ea.CompanyAttachmentId))
                .ToArray();
            entity.RequestTypeAttachments.RemoveRange(attachmentsToRemove);
        }

        private async Task AddValidAttachmentIds(CompanyManagerRequestType entity, long[] attachmentIds)
        {
            var companyAttachmentIds = await this.companyAttachmentService.GetCompanyAttachments(entity.CompanyId)
                .Select(_ => _.CompanyAttachmentId).ToArrayAsync();
            var attachmentIdsToAdd = attachmentIds.Where(_ => !entity.RequestTypeAttachments.Any(a => a.CompanyAttachmentId == _));
            var validAttachmentIds = attachmentIdsToAdd.Where(_ => companyAttachmentIds.Contains(_));

            entity.RequestTypeAttachments.AddRange(validAttachmentIds
                .Select(_ => new CompanyManagerRequestTypeAttachment
                {
                    RequestTypeId = entity.RequestTypeId,
                    CompanyAttachmentId = _,
                })
            );
        }
    }
}
