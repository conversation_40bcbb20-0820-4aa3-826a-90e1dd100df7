namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.CustomFields;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Lookups.Enums;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Message;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Workflow.Activities;

    using CompanyModel = PaySpace.Venuta.Data.Models.Company.Company;

    internal sealed class CompanyMutator : CustomFieldEntityMutator<CompanyDto, CompanyModel, CompanyProfileCustomFieldValue>
    {
        private readonly ICalcSchedulingService calcSchedulingService;
        private readonly ICompanyDefaultsService companyDefaultsService;
        private readonly ICompanyGroupService companyGroupService;
        private readonly IDistributedCache distributedCache;
        private readonly ITaxCountryLookUpService taxCountryLookUpService;
        private readonly ICompanyService companyService;
        private readonly IMapper mapper;
        private readonly IMessageBus messageBus;
        private readonly IWorkflowActivityProcessor workflowActivityProcessor;
        private readonly IWorkflowActivity workflowActivity;
        private readonly IEnumService enumService;

        public CompanyMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<CompanyDto, CompanyModel> entityValidator,
            IAttachmentStorageService attachmentService,
            ICustomFieldSettingsParser<CompanyDto, CompanyModel, CompanyProfileCustomFieldValue> customFieldSettingsParser,
            IMessageBus messageBus,
            ICalcSchedulingService calcSchedulingService,
            IWorkflowActivityProcessor workflowActivityProcessor,
            ICompanyGroupService companyGroupService,
            ICompanyDefaultsService companyDefaultsService,
            IEnumService enumService,
            IDistributedCache distributedCache,
            ITaxCountryLookUpService taxCountryLookUpService,
            ICompanyService companyService,
            IWorkflowActivity workflowActivity)
            : base(mapper, applicationContext, entityValidator, attachmentService, customFieldSettingsParser)
        {
            this.mapper = mapper;
            this.messageBus = messageBus;
            this.calcSchedulingService = calcSchedulingService;
            this.workflowActivityProcessor = workflowActivityProcessor;
            this.companyGroupService = companyGroupService;
            this.companyDefaultsService = companyDefaultsService;
            this.enumService = enumService;
            this.distributedCache = distributedCache;
            this.taxCountryLookUpService = taxCountryLookUpService;
            this.companyService = companyService;
            this.workflowActivity = workflowActivity;
        }

        public override Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, CompanyDto Dto)> AddEntity(MutatorContext context, CompanyDto dto)
        {
            this.CleanupAddresses(dto);

            return base.AddEntity(context, dto);
        }

        protected override async Task BeforeValidateAsync(MutatorContext context, CompanyDto dto, CompanyModel entity)
        {
            var payment = (await this.enumService.GetPaymentModulesAsync())
                    .FirstOrDefault(_ => _.PaymentModuleDescription == dto.PaymentModule || _.PaymentModuleCode == dto.PaymentModule);

            if (payment?.PaymentModuleId > 0)
            {
                if (entity.CompanyId > 0 && entity.CompanyPaymentModule.Count > 0)
                {
                    entity.CompanyPaymentModule.First().PaymentModuleId = payment.PaymentModuleId;
                }
                else
                {
                    entity.CompanyPaymentModule = [new CompanyPaymentModule { PaymentModuleId = payment.PaymentModuleId }];
                }
            }

            if (entity?.Address?.Count > 0)
            {
                foreach (var address in entity.Address)
                {
                    address.TaxAuthorityId = entity.TaxCountryId;
                }

                this.companyDefaultsService.SanitizeAddress(entity);
            }
        }

        protected override async Task BeforeAddAsync(MutatorContext context, CompanyDto dto, CompanyModel entity)
        {
            context.TryAdd(nameof(CompanyDto.InitiateWorkFlow), dto.InitiateWorkFlow);

            var groupLink = await this.companyGroupService.GenerateCompanyGroupLinkAsync(dto.AgencyId!.Value, entity.CompanyName, dto.CompanyGroup, dto.IncludeAnalytics);

            entity.GroupLinks = [groupLink];

            await this.companyDefaultsService.SetFeesPaymentMethodAsync(entity, context.Profile.UserType, context.Profile.AgencyId);

            if (entity.TaxCountryId is ((int)TaxCountry.Uganda) or ((int)TaxCountry.Niger))
            {
                var calculationMethods = await this.enumService.GetCalculationMethodsAsync();
                entity.TaxCalculationMethod = calculationMethods.First(_ => _.CalculationMethodCode == "NonCumulativeTaxMethod").CalculationMethodCode;
            }

            await base.BeforeAddAsync(context, dto, entity);
        }

        private void CleanupAddresses(CompanyDto dto)
        {
            var physicalAddress = dto.Address.FirstOrDefault(_ => _.AddressType == AddressType.Physical);
            if (physicalAddress == null)
            {
                return;
            }

            physicalAddress.IsCareofAddress = null;
            physicalAddress.CareOfIntermediary = null;

            var postalAddress = dto.Address.FirstOrDefault(_ => _.AddressType is AddressType.Postal or AddressType.Street or AddressType.PrivateBag);

            postalAddress ??= dto.Address.FirstOrDefault(_ => _ != physicalAddress);

            if (postalAddress == null || postalAddress.SameAsPhysical == true || postalAddress.SameAsPhysical == true)
            {
                postalAddress = this.mapper.Map<CompanyAddressDto>(physicalAddress);
                postalAddress.AddressType = AddressType.Postal;
            }

            // Ensure that second address is not Physical, postal AddressType is null when SameAsPostal set to true in UI
            // Company second address is required to be of type postal
            postalAddress.AddressType = postalAddress.AddressType == AddressType.Physical ? AddressType.Street : postalAddress.AddressType;

            dto.Address = [postalAddress, physicalAddress];
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, CompanyModel entity)
        {
            this.companyDefaultsService.DefaultCompanyValues(entity);
            return this.companyService.AddAsync(entity);
        }

        protected override Task<CompanyDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set.Where(_ => _.CompanyId == key)
                .Include(_ => _.Address)
                .Include(_ => _.CustomFields)
                .ProjectTo<CompanyDto>(this.mapper.ConfigurationProvider)
                .SingleOrDefaultAsync();
        }

        protected override Task<CompanyModel?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
                .Include(_ => _.CompanyPaymentModule)
                .Include(_ => _.Address)
                .Include(_ => _.CustomFields)
                .SingleOrDefaultAsync(_ => _.CompanyId == key);
        }

        protected override async Task AfterAddAsync(MutatorContext context, CompanyModel entity, CompanyDto dto)
        {
            await this.companyDefaultsService.AddCompanyDefaultsAsync(entity);
            await this.companyDefaultsService.SetUserLinksAsync(entity, context.UserId, context.Profile.UserType);
            await this.companyDefaultsService.CreateDefaultEssRole(entity.CompanyId);
            await this.distributedCache.RemoveAsync(CacheKeys.UserCompanyLinks(context.UserId));

            await this.InitiateWorkflow(context, entity, dto);

            await this.calcSchedulingService.RecalculateCompany(entity.CompanyId);

            await this.messageBus.PublishMessageAsync(new ClearCompanyCacheMessage()
            {
                CompanyIds = new[] { entity.CompanyId }
            });

            await base.AfterAddAsync(context, entity, dto);
        }

        protected override void MapToEntity<TFrom, TTo>(MutatorContext context, TFrom fromObj, TTo toObj, long key)
        {
            this.mapper.Map(fromObj, toObj, opts =>
            {
                opts.Items["Entity"] = nameof(Company);
                opts.Items["EntityKey"] = key;
                opts.Items["CompanyId"] = key;
                opts.Items["TaxCountryId"] = fromObj is CompanyDto dto ? this.taxCountryLookUpService.GetValueId(dto.Country, 0, 0) : null;
                opts.Items.Add(PaySpaceConstants.Version, context.Version);
            });
        }

        protected override void MapToEntity<TFrom, TTo>(MutatorContext context, TFrom fromObj, TTo toObj)
        {
            this.mapper.Map(fromObj, toObj, opts =>
            {
                opts.Items["Entity"] = nameof(Company);
                opts.Items["TaxCountryId"] = fromObj is CompanyDto dto ? dto.Country : null;
                opts.Items.Add(PaySpaceConstants.Version, context.Version);
            });
        }

        protected override Task UpdateAsync(MutatorContext context, long key, CompanyModel entity)
        {
            this.companyDefaultsService.DefaultCompanyValues(entity);
            return this.companyService.UpdateAsync(entity);
        }

        protected override async Task AfterUpdateAsync(MutatorContext context, CompanyModel entity, CompanyDto dto)
        {
            await this.InitiateWorkflow(context, entity, dto);
            await base.AfterUpdateAsync(context, entity, dto);
        }

        private async Task InitiateWorkflow(MutatorContext context, CompanyModel entity, CompanyDto dto)
        {
            if ((dto.InitiateWorkFlow || context.GetValue<bool>(nameof(CompanyDto.InitiateWorkFlow))) && !await this.workflowActivity.IsCurrentlyInWorkflowAsync((int)WorkflowHeaderEnum.CompanyRegister, entity.CompanyId))
            {
                try
                {
                    await this.workflowActivityProcessor.ProcessAddWorkflowApplicationAsync((int)WorkflowHeaderEnum.CompanyRegister, context.UserId, entity.CompanyId, null);
                }
                catch (WorkflowNextStepNotFoundException)
                {
                    // there is no workflow, nothing to do here
                }
            }
        }
    }
}