namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    internal sealed class EmployeeEOnboardingDetailsEntityMutator : EntityMutator<EmployeeEOnboardingDetailsDto, EmployeeEOnboardingDetails, long>
    {
        private readonly IEmployeeEOnboardingDetailsService employeeEOnboardingDetailsService;

        public EmployeeEOnboardingDetailsEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<EmployeeEOnboardingDetailsDto, EmployeeEOnboardingDetails> entityValidator,
            IAttachmentStorageService attachmentService,
            IEmployeeEOnboardingDetailsService employeeEOnboardingDetailsService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.employeeEOnboardingDetailsService = employeeEOnboardingDetailsService;
        }

        protected override Task AddAsync(MutatorContext context, long? employeeId, EmployeeEOnboardingDetails entity)
        {
            return this.employeeEOnboardingDetailsService.AddAsync(entity);
        }

        protected override Task UpdateAsync(MutatorContext context, long key, EmployeeEOnboardingDetails entity)
        {
            return this.employeeEOnboardingDetailsService.UpdateAsync(entity);
        }
    }
}
