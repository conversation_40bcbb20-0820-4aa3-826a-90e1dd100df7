namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Calculation.Cache.Common.Enums;
    using PaySpace.Calculation.Cache.Common.Helpers;
    using PaySpace.Calculation.Cache.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Extensions;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Messaging.Message;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    public class ComponentCompanyMutator : EntityMutator<ComponentCompanyDto, ComponentCompany, long>
    {
        private readonly ApplicationContext applicationContext;
        private readonly ICalculationCacheWebApiClient calculationCacheWebApiClient;
        private readonly ICalcSchedulingService calcSchedulingService;
        private readonly ICompanyRunService companyRunService;

        public ComponentCompanyMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<ComponentCompanyDto, ComponentCompany> entityValidator,
            IAttachmentStorageService attachmentService,
            ICalculationCacheWebApiClient calculationCacheWebApiClient,
            ICalcSchedulingService calcSchedulingService,
            ICompanyRunService companyRunService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.applicationContext = applicationContext;
            this.calculationCacheWebApiClient = calculationCacheWebApiClient;
            this.calcSchedulingService = calcSchedulingService;
            this.companyRunService = companyRunService;
        }

        protected override async Task AfterUpdateAsync(MutatorContext context, ComponentCompany entity, ComponentCompanyDto dto)
        {
            var message = new ClearComponentCacheMessage
            {
                ComponentCompanyId = entity.ComponentId,
                FrequencyId = entity.CompanyFrequencyId,
                AliasDescription = entity.AliasDescription,
                ComponentCode = entity.ComponentCode,
                CompanyRunIds = await this.GetRunIdsAsync(entity.CompanyFrequencyId)
            };

            await this.applicationContext.PublishAfterTransactionCommitAsync(message);
            await this.applicationContext.RunAfterTransactionCommitAsync(
                async () =>
                {
                    var cacheLevelKey = CacheKeyHelper.CreateCacheLevelKey(entity.CompanyFrequencyId, KeyLevelEnum.CompanyFrequency);

                    await this.calculationCacheWebApiClient.RemoveByKeyLevelAsync(cacheLevelKey);
                    await this.calcSchedulingService.RecalculateCompany(context.CompanyId, runId: context.RunId!.Value, CalcSource.APIComponentCompany);
                });
        }

        private Task<long[]> GetRunIdsAsync(long frequencyId)
        {
            return this.companyRunService.GetCompanyRuns(frequencyId)
                .Where(_ => _.Status != RunStatus.Closed)
                .Select(_ => _.RunId)
                .ToArrayAsync();
        }
    }
}