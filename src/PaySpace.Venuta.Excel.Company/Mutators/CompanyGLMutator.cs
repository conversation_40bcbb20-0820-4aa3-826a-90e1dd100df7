namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    public class CompanyGLMutator : DefaultEntityMutator<CompanyGLDto, CompanyGl, long>
    {
        private readonly ICompanyGlService companyGlService;

        public CompanyGLMutator(
            IMapper mapper,
            IEntityValidator<CompanyGLDto, CompanyGl> entityValidator,
            IAttachmentStorageService attachmentService,
            ApplicationContext applicationContext,
            ICompanyGlService companyGlService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.companyGlService = companyGlService;
        }

        protected override async Task BeforeValidateAsync(MutatorContext context, CompanyGLDto dto, CompanyGl entity)
        {
            // Ensure FrequencyId is available
            if (context.FrequencyId is null)
            {
                throw new InvalidOperationException("FrequencyId is required.");
            }

            var companyId = context.CompanyId;
            var frequencyId = context.FrequencyId.Value;

            // Core assignments
            entity.CompanyFrequencyId = frequencyId;

            // Additional assignments
            await this.PopulateGlDetails(context, entity, companyId, frequencyId);
        }

        /// <summary>
        /// Populates the GL details based on the action being performed (create or clone).
        /// </summary>
        private async Task PopulateGlDetails(MutatorContext context, CompanyGl entity, long companyId, long frequencyId)
        {
            if (entity.CompanyGlId > 0)
            {
                return; // Applicable only when creating or cloning
            }

            // Decide whether to clone from an existing GL or populate missing details
            await (
                TryGetCloneId(context, out var cloneId)
                    ? this.companyGlService.CloneDetailsFromSourceAsync(cloneId, entity)
                    : this.companyGlService.PopulateMissingDetailsAsync(companyId, frequencyId, entity)
            );
        }

        /// <summary>
        /// Fetches the CloneId from the context - is required for cloning functionality
        /// </summary>
        private static bool TryGetCloneId(MutatorContext context, out long id) => context.TryGetValue(PaySpaceConstants.CloneId, out id);

        protected override Task DeleteAsync(MutatorContext context, long? employeeId, CompanyGl entity)
        {
            return this.companyGlService.DeleteAsync(context.UserId, entity);
        }
    }
}