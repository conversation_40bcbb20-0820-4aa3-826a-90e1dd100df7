namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Extensions;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    public class PayRateCategoryMutator : EntityMutator<PayRateCategoryDto, OrganizationCategory, long>
    {
        private readonly IStringLocalizer localizer;
        private readonly IMapper mapper;
        private readonly ApplicationContext applicationContext;
        private readonly IIncreaseReasonService increaseReasonService;
        private readonly ICompanySettingService companySettingService;

        public PayRateCategoryMutator(
            IStringLocalizer<OrganizationCategory> localizer,
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<PayRateCategoryDto, OrganizationCategory> entityValidator,
            IAttachmentStorageService attachmentService,
            IIncreaseReasonService increaseReasonService,
            ICompanySettingService companySettingService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.localizer = localizer;
            this.mapper = mapper;
            this.applicationContext = applicationContext;
            this.increaseReasonService = increaseReasonService;
            this.companySettingService = companySettingService;
        }

        public override async Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, PayRateCategoryDto Dto)> UpdateEntity(
            MutatorContext context,
            long key,
            Func<PayRateCategoryDto, Task<bool>> patchDelegate)
        {
            var updateResult = await base.UpdateEntity(context, key, patchDelegate);

            var dateChanged = updateResult.Errors.Any(_ => _.ErrorCode == "ModifyEffectiveDate");
            if (dateChanged)
            {
                updateResult.Dto!.CategoryId = 0;
                context.Add("PayRateRecordLinked", updateResult.Dto!.IsPayRateRecordLinked);
                context.Add("IncreaseReason", updateResult.Dto!.Reason);

                return await this.AddEntity(context, updateResult.Dto);
            }

            return updateResult;
        }

        protected override Task<OrganizationCategory?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
                .Include(_ => _.AdditionalValues)
                .SingleOrDefaultAsync(_ => _.CategoryId == key && _.CompanyId == context.CompanyId);
        }

        protected override async Task<PayRateCategoryDto?> FindDtoAsync(MutatorContext context, long key)
        {
            var dto = await this.Set.Where(_ => _.CategoryId == key && _.CompanyId == context.CompanyId)
               .ProjectTo<PayRateCategoryDto>(this.mapper.ConfigurationProvider)
               .SingleOrDefaultAsync();

            dto!.IsPayRateRecordLinked = context.GetValue<bool?>("PayRateRecordLinked");
            dto!.Reason = context.GetValue<string>("IncreaseReason");

            return dto;
        }

        protected override Task AfterAddAsync(MutatorContext context, OrganizationCategory entity, PayRateCategoryDto dto)
        {
            if (dto.IsPayRateRecordLinked == true)
            {
                return this.applicationContext.PublishAfterTransactionCommitAsync(new ProcessPayRateMessage
                {
                    UserId = context.UserId,
                    CompanyId = entity.CompanyId,
                    EffectiveDate = entity.EffectiveDate,
                    CategoryCode = entity.Code,
                    Description = entity.Description,
                    Package = entity.PayRateDefault,
                    HoursPerDay = entity.PayRateHoursPerDay,
                    DaysPerPeriod = entity.PayRateDaysPerPeriod,
                    IncreaseReason = dto.Reason
                });
            }

            return base.AfterAddAsync(context, entity, dto);
        }

        protected override Task AfterUpdateAsync(MutatorContext context, OrganizationCategory entity, PayRateCategoryDto dto)
        {
            if (dto.IsPayRateRecordLinked == true)
            {
                return this.applicationContext.PublishAfterTransactionCommitAsync(new ProcessPayRateMessage
                {
                    UserId = context.UserId,
                    CompanyId = entity.CompanyId,
                    EffectiveDate = entity.EffectiveDate,
                    CategoryCode = entity.Code,
                    Description = entity.Description,
                    Package = entity.PayRateDefault,
                    HoursPerDay = entity.PayRateHoursPerDay,
                    DaysPerPeriod = entity.PayRateDaysPerPeriod,
                    IncreaseReason = dto.Reason
                });
            }

            return base.AfterUpdateAsync(context, entity, dto);
        }

        protected override async Task<EntityValidationResult> ValidateAsync(
            MutatorContext context,
            PayRateCategoryDto dto,
            OrganizationCategory entity,
            string ruleSet,
            CancellationToken cancellationToken = default)
        {
            var hasCompanySetting = await this.companySettingService.IsActiveAsync(entity.CompanyId, CompanySettingCode.PayRate.Reason);
            if (hasCompanySetting && dto.IsPayRateRecordLinked == true && string.IsNullOrEmpty(dto.Reason))
            {
                var hasReasonRecords = await this.increaseReasonService.CompanyHasIncreaseReasonsAsync(entity.CompanyId);
                if (hasReasonRecords)
                {
                    return new EntityValidationResult(
                        false,
                        new[] { new ErrorCodeValidationResult(nameof(dto.Reason))
                        {
                            ErrorMessage = this.localizer.GetString(SystemAreas.OrganizationCategory.Keys.IncreaseReasonRequiredError)
                        } });
                }
            }

            return await base.ValidateAsync(context, dto, entity, ruleSet, cancellationToken);
        }

        protected override void DetachEntity(OrganizationCategory? entity)
        {
            base.DetachEntity(entity);

            if (entity == null)
            {
                return;
            }

            if (entity.AdditionalValues != null)
            {
                foreach (var additionalValue in entity.AdditionalValues)
                {
                    this.applicationContext.Entry(additionalValue).State = EntityState.Detached;
                }
            }
        }
    }
}