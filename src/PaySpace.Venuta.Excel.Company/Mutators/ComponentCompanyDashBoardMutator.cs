namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    public class ComponentCompanyDashBoardMutator : EntityMutator<ComponentCompanyDashBoardDto, ComponentCompany, long>
    {

        private readonly ICompanyRunService companyRunService;
        private readonly ICompanyComponentService componentCompanyService;

        public ComponentCompanyDashBoardMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<ComponentCompanyDashBoardDto, ComponentCompany> entityValidator,
            IAttachmentStorageService attachmentService,

            ICompanyRunService companyRunService,
            ICompanyComponentService componentCompanyService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.companyRunService = companyRunService;
            this.componentCompanyService = componentCompanyService;
        }

        protected override async Task DeleteAsync(MutatorContext context, long? employeeId, ComponentCompany entity)
        {
            await this.componentCompanyService.DeleteComponentRecursivelyAsync(entity.ComponentId);
            await base.DeleteAsync(context, employeeId, entity);
        }

        protected override async Task AfterDeleteAsync(MutatorContext context, ComponentCompany entity)
        {
            var runIds = await this.GetRunIdsAsync(entity.CompanyFrequencyId);
            await this.componentCompanyService.InvalidateComponentCompanyCacheAsync(context, entity, runIds);
        }

        private Task<long[]> GetRunIdsAsync(long frequencyId)
        {
            return this.companyRunService.GetCompanyRuns(frequencyId)
                .Where(_ => _.Status != RunStatus.Closed)
                .Select(_ => _.RunId)
                .ToArrayAsync();
        }
    }
}