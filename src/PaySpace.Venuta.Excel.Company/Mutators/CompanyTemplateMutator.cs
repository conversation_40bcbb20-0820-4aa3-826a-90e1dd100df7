namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using AutoMapper;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;
    using PaySpace.Venuta.Storage;

    internal sealed class CompanyTemplateMutator : EntityMutator<CompanyTemplateDto, Template, long>
    {
        public CompanyTemplateMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<CompanyTemplateDto, Template> entityValidator,
            IAttachmentStorageService attachmentService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
        }

        public override Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, CompanyTemplateDto Dto)> AddEntity(MutatorContext context, CompanyTemplateDto dto)
        {
            dto.CompanyId = context.CompanyId;
            return base.AddEntity(context, dto);
        }
    }
}