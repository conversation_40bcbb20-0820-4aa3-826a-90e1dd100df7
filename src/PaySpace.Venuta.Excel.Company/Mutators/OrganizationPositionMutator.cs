namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Mutators;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Storage;

    public class OrganizationPositionMutator : DefaultEntityMutator<OrganizationPositionDetailDto, OrganizationPositionDetail, long>
    {
        private readonly IMapper mapper;
        private readonly IOrganizationPositionService organizationPositionService;

        public OrganizationPositionMutator(
            IMapper mapper,
            IEntityValidator<OrganizationPositionDetailDto, OrganizationPositionDetail> entityValidator,
            IAttachmentStorageService attachmentService,
            IOrganizationPositionService organizationPositionService,
            ApplicationContext applicationContext)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.mapper = mapper;
            this.organizationPositionService = organizationPositionService;
        }

        protected override async Task AddAsync(MutatorContext context, long? employeeId, OrganizationPositionDetail entity)
        {
            if (context.IsNextgen())
            {
                await base.AddAsync(context, employeeId, entity);
            }
            else
            {
                await this.organizationPositionService.AddAsync(entity, entity.OrganizationPosition.CompanyId);
            }
        }

        protected override Task<OrganizationPositionDetailDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set.Where(_ => _.PositionDetailId == key && _.OrganizationPosition.CompanyId == context.CompanyId)
                .ProjectTo<OrganizationPositionDetailDto>(this.mapper.ConfigurationProvider)
                .SingleOrDefaultAsync();
        }

        protected override Task<OrganizationPositionDetail?> FindAsync(MutatorContext context, long key)
        {
            return this.Set.SingleOrDefaultAsync(_ => _.PositionDetailId == key && _.OrganizationPosition.CompanyId == context.CompanyId);
        }

        protected override Task<EntityValidationResult> ValidateAsync(
            MutatorContext context, OrganizationPositionDetailDto dto, OrganizationPositionDetail entity, string ruleSet, CancellationToken cancellationToken = default)
        {
            context.AddValidationInfo("DeleteAll", context.ContainsKey("DeleteAll"));
            context.AddValidationInfo("IsCopy", context.IsNextgen() && ruleSet == RuleSetNames.Create && dto.OrganizationPositionId.HasValue);

            return base.ValidateAsync(context, dto, entity, ruleSet, cancellationToken);
        }

        protected override async Task DeleteAsync(MutatorContext context, long? employeeId, OrganizationPositionDetail entity)
        {
            if (context.ContainsKey("DeleteAll"))
            {
                await this.organizationPositionService.DeleteAllAsync(entity.OrganizationPositionId);
            }
            else
            {
                await this.organizationPositionService.DeleteAsync(entity);
            }
        }

        protected override async Task UpdateAsync(MutatorContext context, long key, OrganizationPositionDetail entity)
        {
            if (context.IsNextgen())
            {
                await base.UpdateAsync(context, key, entity);
            }
            else
            {
                await this.organizationPositionService.UpdateAsync(entity);
            }
        }
    }
}
