namespace PaySpace.Venuta.Excel.Company.Mutators
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Validation;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Services;
    using PaySpace.Venuta.Storage;

    internal sealed class CompanyTemplateConfigurationEntityMutator : EntityMutator<CompanyTemplateConfigurationDto, TemplateConfiguration, long>
    {
        private readonly IMapper mapper;
        private readonly IComponentVariableValueService componentVariableValueService;

        public CompanyTemplateConfigurationEntityMutator(
            IMapper mapper,
            ApplicationContext applicationContext,
            IEntityValidator<CompanyTemplateConfigurationDto, TemplateConfiguration> entityValidator,
            IAttachmentStorageService attachmentService,
            IComponentVariableValueService componentVariableValueService)
            : base(mapper, applicationContext, entityValidator, attachmentService)
        {
            this.mapper = mapper;
            this.componentVariableValueService = componentVariableValueService;
        }

        public override Task<(bool IsValid, IList<ErrorCodeValidationResult> Errors, CompanyTemplateConfigurationDto Dto)> AddEntity(MutatorContext context, CompanyTemplateConfigurationDto dto)
        {
            // Map the CompanyId - This should only happen on Add and not AfterMap to avoid agency template configs having a companyId if updated
            dto.CompanyId = context.CompanyId;
            return base.AddEntity(context, dto);
        }

        protected override async Task AfterMapAsync(MutatorContext context, CompanyTemplateConfigurationDto dto, TemplateConfiguration entity)
        {
            // Ensure we cannot update the companyId on an Agency level config
            entity.CompanyId = entity.AgencyId.HasValue ? null : dto.CompanyId;

            var originalValues = await this.componentVariableValueService
                .GetByTemplateConfigurationId(entity.TemplateConfigurationId)
                .ToListAsync();

            var dtoValues = dto.ComponentVariableValues;
            var entityValues = entity.ComponentVariableValues;
            var dtoIds = dtoValues
                .Where(_ => _.ComponentVariableValueId > 0)
                .Select(_ => _.ComponentVariableValueId)
                .ToHashSet();

            this.AddNewValues(dtoValues, entityValues, context.CompanyId);
            this.UpdateOrOverrideValues(dtoValues, originalValues, entityValues, context.CompanyId);
            this.RemoveDeletedValues(context.CompanyId, entityValues, dtoIds);

            await base.AfterMapAsync(context, dto, entity);
        }

        protected override Task<EntityValidationResult> ValidateAndDelete(MutatorContext context, CompanyTemplateConfigurationDto dto, TemplateConfiguration entity, long key)
        {
            if (entity.CompanyId.HasValue)
            {
                entity.AllowDelete = true;
            }

            return base.ValidateAndDelete(context, dto, entity, key);
        }

        protected override Task<TemplateConfiguration?> FindAsync(MutatorContext context, long key)
        {
            return this.Set
                .Include(_ => _.ComponentVariableValues)
                .SingleOrDefaultAsync(_ => _.TemplateConfigurationId == key);
        }

        protected override Task<CompanyTemplateConfigurationDto?> FindDtoAsync(MutatorContext context, long key)
        {
            return this.Set
                .ProjectTo<CompanyTemplateConfigurationDto>(this.mapper.ConfigurationProvider)
                .SingleOrDefaultAsync(_ => _.TemplateConfigurationId == key);
        }

        private void AddNewValues(List<ComponentVariableValueDto> dtoValues, List<ComponentVariableValue> entityValues, long companyId)
        {
            foreach (var dto in dtoValues.Where(_ => _.ComponentVariableValueId == 0))
            {
                dto.IsOverride = false;
                dto.AgencyId = null;
                dto.CompanyId = companyId;
                entityValues.Add(this.mapper.Map<ComponentVariableValue>(dto));
            }
        }

        private void UpdateOrOverrideValues(List<ComponentVariableValueDto> dtoValues,
            List<ComponentVariableValue> originalValues,
            List<ComponentVariableValue> entityValues,
            long companyId)
        {
            foreach (var dto in dtoValues.Where(_ => _.ComponentVariableValueId > 0))
            {
                var original = originalValues.FirstOrDefault(_ => _.ComponentVariableValueId == dto.ComponentVariableValueId);
                if (original == null)
                {
                    continue;
                }

                // Override agency-level value
                if (original.AgencyId.HasValue && dto.IsOverride)
                {
                    var newValue = this.mapper.Map<ComponentVariableValue>(dto);
                    newValue.ComponentVariableValueId = 0;
                    newValue.CompanyId = companyId;
                    newValue.AgencyId = null;
                    newValue.IsOverride = true;
                    entityValues.Add(newValue);
                }
                // Update company-level value
                else if (original.CompanyId == companyId && original.AgencyId == null && (original.Value != dto.Value || original.IsOverride != dto.IsOverride))
                {
                    original.Value = dto.Value;
                    original.IsOverride = dto.IsOverride;
                }
            }
        }

        private void RemoveDeletedValues(long companyId, List<ComponentVariableValue> entityValues, HashSet<long> dtoIds)
        {
            entityValues.RemoveAll(_ => _.CompanyId == companyId && _.ComponentVariableValueId > 0 &&
                !dtoIds.Contains(_.ComponentVariableValueId));
        }
    }
}