namespace PaySpace.Venuta.Search.Queryable
{
    using System;
    using System.Collections.Generic;
    using System.Linq.Expressions;

    using Newtonsoft.Json.Serialization;

    using Remotion.Linq;
    using Remotion.Linq.Clauses;
    using Remotion.Linq.Clauses.Expressions;
    using Remotion.Linq.Clauses.ResultOperators;
    using Remotion.Linq.Parsing;

    internal class SearchQueryTranslator : QueryModelVisitorBase
    {
        private readonly TranslatedQueryModel model;

        public SearchQueryTranslator(TranslatedQueryModel model)
        {
            this.model = model;
        }

        public override void VisitSelectClause(SelectClause selectClause, QueryModel queryModel)
        {
            if (queryModel.IsIdentityQuery() || queryModel.MainFromClause.ItemType == queryModel.SelectClause.Selector.Type)
            {
                return;
            }

            this.model.Select = ReplaceQuerySourceWithParameterVisitor.ReplaceQuerySourceWithItemType(selectClause.Selector);
        }

        public override void VisitWhereClause(WhereClause whereClause, QueryModel queryModel, int index)
        {
            // Because of the way this block is implemented we are ignoring any previously parsed information.
            // Ex: If we have an expression like: companyQuery.Where(_ => _.somecondition).Where(_ => _.anotherCondition)
            // The first 'Where' data will be overwriten and ignored. So the result QueryModel will only contain information on the last 'Where' clause.
            // In the end only the .Where(_ => _.anotherCondition) will be executed.
            var constants = new QueryVisitor().Eval(whereClause.Predicate);
            this.model.Query = constants.Query;
            this.model.CompanyIds = constants.CompanyIds;
            this.model.EqualTerms = constants.EqualTerms;
            this.model.NotEqualTerms = constants.NotEqualTerms;
        }

        public override void VisitResultOperator(ResultOperatorBase resultOperator, QueryModel queryModel, int index)
        {
            if (resultOperator is SkipResultOperator skip)
            {
                this.model.Skip = skip.GetConstantCount();
            }

            if (resultOperator is TakeResultOperator take)
            {
                this.model.Take = take.GetConstantCount();
            }

            if (resultOperator is CountResultOperator)
            {
                this.model.Skip = 0;
                this.model.Take = 0;
            }
        }

        internal sealed class ReplaceQuerySourceWithParameterVisitor : RelinqExpressionVisitor
        {
            private ParameterExpression parameter;

            private ReplaceQuerySourceWithParameterVisitor()
            {
            }

            public static LambdaExpression ReplaceQuerySourceWithItemType(Expression expression)
            {
                var visitor = new ReplaceQuerySourceWithParameterVisitor();
                var expr = visitor.Visit(expression);

                return Expression.Lambda(expr, visitor.parameter);
            }

            protected override Expression VisitQuerySourceReference(QuerySourceReferenceExpression expression)
            {
                this.parameter ??= Expression.Parameter(expression.ReferencedQuerySource.ItemType);
                return this.parameter;
            }
        }

        private sealed class QueryVisitor : RelinqExpressionVisitor
        {
            private string? query;
            private List<long>? companyIds;
            private readonly Dictionary<string, object> equalTerms = new();
            private readonly Dictionary<string, object> notEqualTerms = new();
            private readonly CamelCaseNamingStrategy namingStrategy = new();

            public (string Query, IList<long> CompanyIds, Dictionary<string, object> EqualTerms, Dictionary<string, object> NotEqualTerms) Eval(Expression expression)
            {
                this.Visit(expression);
                return (this.query, this.companyIds, this.equalTerms, this.notEqualTerms);
            }

            protected override Expression VisitBinary(BinaryExpression node)
            {
                string? memberName;
                object? memberValue;
#pragma warning disable IDE0010
                switch (node.NodeType)
                {
                    case ExpressionType.Equal:
                        (memberName, memberValue) = this.GetEqualExpressionData(node);

                        if (memberName is not null && memberValue is not null)
                        {
                            if (memberName == "companyId")
                            {
                                this.ProcessCompanyId(memberValue);
                                return node;
                            }

                            this.AddMemberToTermsDictionary(this.equalTerms, memberName, memberValue);
                        }
                        break;
                    case ExpressionType.NotEqual:
                        (memberName, memberValue) = this.GetEqualExpressionData(node);
                        if (memberName is not null && memberValue is not null)
                        {
                            this.AddMemberToTermsDictionary(this.notEqualTerms, memberName, memberValue);
                        }
                        break;
                    case ExpressionType.OrElse:
                    case ExpressionType.AndAlso:
                        this.Visit(node.Left);
                        this.Visit(node.Right);
                        break;
                    default:
                        break;
                }
#pragma warning restore IDE0010

                return node;
            }

            protected override Expression VisitConstant(ConstantExpression node)
            {
                if (node.Value is string)
                {
                    this.query = ExpressionUtils.GetValue<string>(node);
                }

                if (node.Value is long)
                {
                    this.companyIds ??= new List<long>();
                    this.companyIds.Add(ExpressionUtils.GetValue<long>(node));
                }

                return node;
            }

            private (string? memeberName, object? memberValue) GetEqualExpressionData(BinaryExpression node)
            {
                string? memberName = null;
                object? memberValue = null;
                if (node.Left.NodeType == ExpressionType.MemberAccess)
                {
                    memberName = (node.Left as MemberExpression)?.Member.Name;
                    memberName = this.namingStrategy.GetPropertyName(memberName, false);
                }

                if (node.Right.NodeType == ExpressionType.Constant)
                {
                    memberValue = (node.Right as ConstantExpression)?.Value;
                }

                return (memberName, memberValue);
            }

            private void ProcessCompanyId(object memberValue)
            {
                this.companyIds ??= new List<long>();
                this.companyIds.Add((long)memberValue);
            }

            private void AddMemberToTermsDictionary(Dictionary<string, object> memberTerms, string memberName, object memberValue)
            {
                ArgumentNullException.ThrowIfNull(memberName);
                ArgumentNullException.ThrowIfNull(memberValue);

                if (memberTerms.TryGetValue(memberName, out var currentTerm))
                {
                    if (currentTerm is IList<object> currentTermsList)
                    {
                        currentTermsList.Add(memberValue);
                    }
                    else
                    {
                        memberTerms.Remove(memberName);
                        memberTerms[memberName] = new List<object>() { currentTerm, memberValue };
                    }
                }
                else
                {
                    memberTerms.Add(memberName, memberValue);
                }
            }
        }
    }
}