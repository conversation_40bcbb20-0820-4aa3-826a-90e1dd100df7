namespace PaySpace.Venuta.Search
{
    using Elasticsearch.Net;

    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;

    using Nest;

    using PaySpace.Venuta.Search.Services;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddSearch(this IServiceCollection services, IConfigurationSection configuration)
        {
            var elasticSettings = configuration.Get<ElasticSettings>();
            var pool = new StaticConnectionPool(elasticSettings.Urls);
            services.AddSingleton<IElasticClient>(new ElasticClient(new ConnectionSettings(pool).EnableDebugMode()));

            services.AddScoped<IIndexClient, IndexClient>();
            services.AddScoped<IEmployeeClient, EmployeeClient>();
            services.AddScoped<ICompanyClient, CompanyClient>();

            services.AddScoped<ISearchProviderFactory, SearchProviderFactory>();

            services.AddScoped<ISearchProvider, EmployeeProvider>();
            services.AddScoped<ISearchProvider, CompanyProvider>();
            services.AddScoped<ISearchProvider, AgencyProvider>();
            services.AddScoped<ISearchProvider, BureauProvider>();

            services.AddScoped<ISearchFilterService, SearchFilterService>();
            services.AddScoped<IResultProvider, ResultProvider>();

            return services;
        }
    }
}