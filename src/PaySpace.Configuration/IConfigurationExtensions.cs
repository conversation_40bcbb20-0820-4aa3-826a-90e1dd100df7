namespace PaySpace.Configuration
{
    using System.ComponentModel;

    using Microsoft.Extensions.Configuration;

    using PaySpace.Venuta.Infrastructure;

    public enum HostRegion
    {
        [Description("Africa")]
        san,
        [Description("Europe")]
        euw,
        [Description("Brazil")]
        brs,
        [Description("Sweden")]
        sdc
    }

    public static class ConfigurationExtensions
    {
        public static string GetRegion(this IConfiguration configuration)
        {
            return configuration["AppConfiguration:Region"] ?? nameof(HostRegion.san).ToLower();
        }

        public static string GetRegionDisplayName(this IConfiguration configuration)
        {
            var region = GetRegion(configuration);
            return RegionHelper.GetRegionDisplayName(region);
        }
    }

    public static class RegionHelper
    {
        public static string? GetRegionDisplayName(string? region)
        {
            if (string.IsNullOrEmpty(region))
            {
                return null;
            }

            return Enum.Parse<HostRegion>(region).GetEnumDescription();
        }
    }
}