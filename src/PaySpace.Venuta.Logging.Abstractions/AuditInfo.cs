namespace PaySpace.Venuta.Logging.Abstractions
{
    using PaySpace.Venuta.Logging.Audit.AuditEntities;

    public class AuditInfo
    {
        public long? CompanyId { get; set; }

        public long? CompanyGroupId { get; set; }

        public long? AgencyId { get; set; }

        public long? EmployeeId { get; set; }

        public string Value { get; set; }

        public string Description { get; set; }

        public long? PayslipId { get; set; }

        public long? EmployeeComponentId { get; set; }

        public long? AlternativeId { get; set; }

        public long? RunId { get; set; }

        public object AuditingValues { get; set; }

        public string LocalizationTableName { get; set; }

        public string? HeaderTableName { get; set; }

        public AuditActionType? OverrideAction { get; set; }

        public int? CountryId { get; set; }

        public long? CompanyComponentId { get; set; }

        public long? CompanyCustomFormCategoryId { get; set; }

        public long? BureauCustomFormCategoryId { get; set; }
    }
}