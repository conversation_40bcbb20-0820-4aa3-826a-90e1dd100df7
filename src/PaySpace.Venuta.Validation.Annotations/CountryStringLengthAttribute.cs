namespace PaySpace.Venuta.Validation.Annotations
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Reflection;

    using Maddalena;

    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Venuta.Abstractions;

    [AttributeUsage(AttributeTargets.Property, AllowMultiple = true)]
    public class CountryStringLengthAttribute : StringLengthAttribute
    {
        public CountryStringLengthAttribute(int maximumLength, params CountryCode[] taxCountryCodes)
            : base(maximumLength)
        {
            this.TaxCountryCodes = taxCountryCodes.Select(tc => tc.ToString()).ToArray();
        }

        public string[] TaxCountryCodes { get; set; }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (string.IsNullOrEmpty(Convert.ToString(value)))
            {
                return ValidationResult.Success;
            }

            var taxCountryCode = validationContext.GetRequiredService<ITenantProvider>().GetTaxCountryCode();

            if (this.TaxCountryCodes.Length == 0)
            {
                //TODO: cache this?
                var property = validationContext.ObjectType.GetProperty(validationContext.MemberName!);

                var otherCodes = property!
                    .GetCustomAttributes<CountryStringLengthAttribute>()
                    .SelectMany(_ => _.TaxCountryCodes)
                    .ToArray();

                return otherCodes.Contains(taxCountryCode) ? ValidationResult.Success : base.IsValid(value, validationContext);
            }

            return !this.TaxCountryCodes.Contains(taxCountryCode) ? ValidationResult.Success : base.IsValid(value, validationContext);
        }
    }
}