namespace PaySpace.Venuta.Validation.Annotations
{
    using System;
    using System.Linq;

    using Maddalena;

    [AttributeUsage(AttributeTargets.Property, AllowMultiple = true)]
    public class MetadataInfoAttribute : Attribute
    {
        public MetadataInfoAttribute(params CountryCode[] taxCountryCodes)
        {
            this.TaxCountryCodes = taxCountryCodes.Select(tc => tc.ToString()).ToArray();
        }

        public string[] TaxCountryCodes { get; set; }

        public bool HasAdditionalValidation { get; set; }

        public bool MustBeUniqueValue { get; set; }

        public string[] CompanySettings { get; set; }

        public string RegExFormat { get; set; }
    }
}