namespace PaySpace.Venuta.Infrastructure
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Text.RegularExpressions;

    public static partial class ValidationHelper
    {
        private static readonly char[] InvalidSpecialCharacters = { '\\', '|', '%', '"', '(', ')' };
        private static readonly int[] BrazilSocialSecurityValidationWeights = { 3, 2, 9, 8, 7, 6, 5, 4, 3, 2 };

        private static readonly EmailAddressAttribute EmailAddressValidator = new();

        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return false;
            }

            try
            {
                EmailAddressValidator.Validate(email, "Email");

                return HasValidTld(email);
            }
            catch (ValidationException)
            {
                return false;
            }
        }

        public static bool IsLetter(string text)
        {
            return LetterRegex().IsMatch(text);
        }

        public static bool IsDecimalNumber(string text)
        {
            return DecimalNumberRegex().IsMatch(text);
        }

        public static bool IsWholeNumber(string text)
        {
            return WholeNumberRegex().IsMatch(text);
        }

        public static bool IsAlphaNumeric(string text)
        {
            return AlphaNumericRegex().IsMatch(text ?? string.Empty);
        }

        public static bool IsTelephoneNumber(string text)
        {
            return TelephoneNumberRegex().IsMatch(text);
        }

        public static bool HasNoConsecutiveSpecialCharacters(string text)
        {
            return NoConsecutiveSpecialCharactersRegex().IsMatch(text);
        }

        public static bool ValidateText(string text, string expression)
        {
            if (string.IsNullOrEmpty(expression))
            {
                return true;
            }

            return Regex.IsMatch(text, expression);
        }

        public static bool IsValidIdNumber(string idNumber)
        {
            if (!string.IsNullOrEmpty(idNumber) && idNumber.Length == 13)
            {
                var oddSum = 0d;

                foreach (var digit in idNumber.Substring(0, 12).Where((c, i) => i % 2 == 0))
                {
                    oddSum += Convert.ToInt32(digit.ToString());
                }

                var evenSum = 0d;
                var evenDigits = (Convert.ToInt64(string.Concat(idNumber.Substring(0, 12).Where((c, i) => i % 2 != 0))) * 2).ToString();

                foreach (var digit in evenDigits)
                {
                    evenSum += Convert.ToInt32(digit.ToString());
                }

                var oddEvenSum = (oddSum + evenSum).ToString();
                var checkDigit = 10 - Convert.ToInt32(oddEvenSum.Substring(oddEvenSum.Length - 1, 1));
                checkDigit = checkDigit > 9 ? 0 : checkDigit;

                return checkDigit == Convert.ToInt32(idNumber.Substring(12, 1));
            }

            return false;
        }

        public static bool IsValidDayOfWeek(string day)
        {
            return Enum.GetNames<DayOfWeek>().Contains(day);
        }

        public static bool EmailAddressContainsInvalidSpecialCharacters(string email)
        {
            if (email.Contains("@") && IsAlphaNumericStartAndEnd(email))
            {
                var username = email.Split('@')[0];
                if (!HasNoConsecutiveSpecialCharacters(username))
                {
                    return false;
                }

                var domain = email.Split('@')[1];
                if (!HasNoConsecutiveSpecialCharacters(domain) || domain.StartsWith("."))
                {
                    return false;
                }

                return email.IndexOfAny(InvalidSpecialCharacters) == -1;
            }

            return false;
        }

        public static bool IsValidPayeRegistrationNumber(string payeNumber)
        {
            if (!string.IsNullOrEmpty(payeNumber) && payeNumber.Length == 10)
            {
                var oddSum = 0d;
                var evenSum = 0d;
                var totalSum = 0d;

                var oddDigits = Convert.ToInt64(string.Concat(payeNumber.Substring(0, 9).Where((c, i) => i % 2 == 0))).ToString();
                var oddDiditList = oddDigits.Select(digit => int.Parse(digit.ToString())).ToList();

                if (oddDiditList[0] == 7)
                {
                    oddDiditList[0] = 4;
                }

                foreach (var digit in oddDiditList)
                {
                    var oddDigit = (digit * 2).ToString();

                    foreach (var item in oddDigit.Substring(0, oddDigit.Length))
                    {
                        oddSum += Convert.ToInt32(item.ToString());
                    }
                }

                var evenDigits = Convert.ToInt64(string.Concat(payeNumber.Substring(0, 10).Where((c, i) => i % 2 != 0))).ToString();
                foreach (var digit in evenDigits)
                {
                    evenSum += Convert.ToInt32(digit.ToString());
                }

                totalSum += evenSum + oddSum;

                return totalSum % 10 == 0;
            }

            return false;
        }

        // Social Security Number validation for Brazil employees. See #63059 for details
        public static bool IsValidSocialSecurityNumber(string idNumber, int pisDivisor)
        {
            if (idNumber.Length < BrazilSocialSecurityValidationWeights.Length)
            {
                return false;
            }

            var checkDigit = int.Parse(idNumber.Substring(idNumber.Length - 1));
            var totalValue = 0;

            for (var i = 0; i < BrazilSocialSecurityValidationWeights.Length; i++)
            {
                var weightedValue = BrazilSocialSecurityValidationWeights[i] * (int)char.GetNumericValue(idNumber, i);
                totalValue += weightedValue;
            }

            var divisorResult = totalValue % pisDivisor;
            var lastCheck = pisDivisor - divisorResult;
            var validationDigit = (lastCheck is 10 or 11) ? 0 : lastCheck;

            return validationDigit == checkDigit;
        }

        public static bool IsValidCode(string text)
        {
            // Only allow alphanumeric characters, underscores, and dashes
            return CodeRegex().IsMatch(text);
        }

        public static bool IsValidUnitedKingdomPostalCode(string text)
        {
            return UnitedKingdomPostalCodeRegex().IsMatch(text);
        }

        public static bool IsValidBankAccountNumber(string text)
        {
            return BankAccountNumberRegex().IsMatch(text) && PrintableASCIIRegex().IsMatch(text);
        }

        public static bool IsValidSouthAfricaIdNumber(string idNumber)
        {
            return SouthAfricaIdNumberRegex().IsMatch(idNumber);
        }

        public static bool IsValidMalaysiaPcbNumber(string text)
        {
            return MalaysiaPcbNumberRegex().IsMatch(text);
        }

        private static bool HasValidTld(string email)
        {
            // This is an easy way to quickly test if the TLD is valid, if we still get false positives/negatives then we need to use 3rd party to validate
            var domainPart = email.Split('@')[1];
            if (!domainPart.Contains("."))
            {
                return false;
            }

            var lastSection = domainPart.Split('.').Last();

            // Can not start with numbers, can have numbers, can not be only numbers. but currently there is no TLD with numbers.
            // see https://data.iana.org/TLD/tlds-alpha-by-domain.txt
            return ValidTldRegex().IsMatch(lastSection);
        }

        private static bool IsAlphaNumericStartAndEnd(string email)
        {
            var start = email[0];
            var end = email[^1];

            if (!char.IsLetterOrDigit(start) || !char.IsLetterOrDigit(end))
            {
                return false;
            }

            return true;
        }

        public static bool EmailAddressContainsSpace(string email)
        {
            return ContainsSpaceRegex().IsMatch(email);
        }

        // Generated Regex Section
        [GeneratedRegex(@"^[a-zA-Z]*$")]
        private static partial Regex LetterRegex();

        [GeneratedRegex(@"^-?\d+\.?\d*$")]
        private static partial Regex DecimalNumberRegex();

        [GeneratedRegex(@"^[0-9]*$")]
        private static partial Regex WholeNumberRegex();

        [GeneratedRegex(@"^[+]{0,1}[0-9]{0,4}[\s]{0,1}[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$")]
        private static partial Regex TelephoneNumberRegex();

        [GeneratedRegex(@"^(([\W]?[\w])+|([\w][\W]?)+)$")]
        private static partial Regex NoConsecutiveSpecialCharactersRegex();

        [GeneratedRegex(@"^[a-zA-Z0-9_-]*$")]
        private static partial Regex CodeRegex();

        [GeneratedRegex(@"^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9]?[A-Za-z])))) [0-9][A-Za-z]{2})$")]
        private static partial Regex UnitedKingdomPostalCodeRegex();

        [GeneratedRegex(@"^[^,:?!@]+$")]
        private static partial Regex BankAccountNumberRegex();

        [GeneratedRegex(@"^[\x20-\x7E]+$")]
        private static partial Regex PrintableASCIIRegex();

        [GeneratedRegex(@"^[a-zA-Z]+$")]
        private static partial Regex ValidTldRegex();

        [GeneratedRegex(@"^(18[0-9]{2}|19[0-9]{2}|20[0-9]{2})/\d{6}/(06|07|08|09|10|11|20|21|22|23|24|25|26|30|31)$")]
        private static partial Regex SouthAfricaIdNumberRegex();

        [GeneratedRegex(@"^[a-zA-Z0-9]*$")]
        private static partial Regex AlphaNumericRegex();

        [GeneratedRegex(@"^[A-Z]{2}\d{9,11}$")]
        private static partial Regex MalaysiaPcbNumberRegex();

        [GeneratedRegex(@"\s")]
        private static partial Regex ContainsSpaceRegex();
    }
}