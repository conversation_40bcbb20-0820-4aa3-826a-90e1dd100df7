namespace PaySpace.Venuta.Modules.CustomFields
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Reports.Api.Service;

    public static class CustomEntities
    {
        public static class Names
        {
            public const string Company = "Company";
            public const string CompanyRoster = "CompanyRoster";
            public const string Dependant = "Dependant";
            public const string Employee = "Employee";
            public const string EmploymentStatus = "EmploymentStatus";
            public const string Payslip = "Payslip";
            public const string PayslipLine = "PayslipLine";
            public const string PayRate = "PayRate";
            public const string Position = "Position";
            public const string OrganizationRegion = "OrganizationRegion";
            public const string Training = "Training";
            public const string EmployeeClaim = "EmployeeClaim";
        }
    }

    internal sealed class CustomEntitiesService : ICustomEntitiesService
    {
        private readonly ICustomFieldService customFieldService;
        private readonly ITableBuilderService tableBuilderService;

        public CustomEntitiesService(ICustomFieldService customFieldService, ITableBuilderService tableBuilderService)
        {
            this.customFieldService = customFieldService;
            this.tableBuilderService = tableBuilderService;
        }

        // TODO: Revert string Entity Type Names back to Type.
        public async Task<(CustomForm[] CustomForms, CustomEntityDefinition[] CustomFieldDefinitions)> GetCustomEntitiesAsync(long companyGroupId, bool includeEmptyCustomForms = false)
        {
            var tableBuilder = await this.tableBuilderService.GetEntitiesAsync(companyGroupId);

            var customForms = new List<CustomForm>();
            customForms.AddRange(await this.customFieldService.GetCustomFormsAsync(companyGroupId, includeEmptyCustomForms));
            customForms.AddRange(tableBuilder.Where(_ => !customForms.Any(x => string.Equals(x.SanitizedCollectionCode, _.SanitizedCollectionCode, StringComparison.OrdinalIgnoreCase))));

            // Ignore custom fields on employee that have the same name as any custom form.
            var customFields = await this.customFieldService.GetCustomFieldsAsync(companyGroupId);
            if (customFields.TryGetValue("Employee", out var employeeCustomFields))
            {
                employeeCustomFields.RemoveRange(employeeCustomFields
                    .Where(_ => customForms.Any(x => string.Equals(x.SanitizedCollectionCode, _.SanitizedFieldCode, StringComparison.OrdinalIgnoreCase)))
                    .ToList());
            }

            // Entity property should match the classname of the custom field entity. It is used to match an entity to a definition when a custom field value is changed
            // or to get custom field values when an entity with custom fields is created.
            var customFieldDefinitions = this.GetCustomEntityDefinitions(companyGroupId, customFields);

            return (customForms.ToArray(), customFieldDefinitions);
        }

        public CustomEntityDefinition[] GetCustomEntityDefinitions(long companyGroupId, IDictionary<string, List<CustomField>>? customFields)
        {
            return
            [
                new CustomEntityDefinition(CustomEntities.Names.Payslip, "PayslipHeaderModel",
                        new CustomFieldSchema(companyGroupId, "Payslip", string.Empty), null, null, null)
                    .Reference(CustomEntities.Names.PayslipLine, "PayslipHeader", true)
                    .Collection(CustomEntities.Names.Employee, "Payslips"),

                new CustomEntityDefinition(CustomEntities.Names.PayslipLine, "PayslipLineModel",
                        new CustomFieldSchema(companyGroupId,"PayslipLine", string.Empty), null, null, null)
                    .Collection(CustomEntities.Names.Payslip, "PayslipLines"),

                new CustomEntityDefinition(CustomEntities.Names.Company, "CompanyModel",
                        new CustomFieldSchema(companyGroupId, "Company", "fkCompanyId"), customFields, "", "v.fkCompanyID")
                    .Reference(CustomEntities.Names.Employee, "Company"),

                new CustomEntityDefinition(CustomEntities.Names.CompanyRoster, "CompanyRosterModel",
                        new CustomFieldSchema(companyGroupId, "CompanyRoster", "fkRosterId"), customFields,
                        "JOIN CompanyRosters cr ON v.fkRosterID = cr.pkRosterID", "cr.fkCompanyID")
                    .Collection(CustomEntities.Names.Company, "CompanyRosters"),

                new CustomEntityDefinition(CustomEntities.Names.Employee, "EmployeeModel",
                    new CustomFieldSchema(companyGroupId, "Employee", "fkEmpID"), customFields,
                    "JOIN Employee e ON v.fkEmpId = e.pkEmpID", "e.fkCompanyID"),

                new CustomEntityDefinition(CustomEntities.Names.Dependant, "DependantModel",
                        new CustomFieldSchema(companyGroupId, "EmployeeDependant", "fkEmpDepId"), customFields,
                        "JOIN EmployeeDependants ed ON v.fkEmpDepID = ed.pkEmpDepID JOIN Employee e on ed.fkEmpID = e.pkEmpID", "e.fkCompanyID")
                    .Collection(CustomEntities.Names.Employee, "Dependants"),

                new CustomEntityDefinition(CustomEntities.Names.EmploymentStatus, "EmploymentStatusModel",
                        new CustomFieldSchema(companyGroupId, "EmployeeEmploymentStatus", "fkEmpEmploymentStatusId"), customFields,
                        "JOIN EmployeeEmploymentStatus ees ON v.fkEmpEmploymentStatusId = ees.pkEmpEmploymentStatusID", "ees.fkCompanyID")
                    .Collection(CustomEntities.Names.Employee, "EmploymentStatus"),

                new CustomEntityDefinition(CustomEntities.Names.PayRate, "PayRateModel",
                        new CustomFieldSchema(companyGroupId, "EmployeePayRate", "fkPayRateId"), customFields,
                        "JOIN PayRates pr ON v.fkPayRateId = pr.pkPayRateId JOIN Employee e on pr.fkEmpID = e.pkEmpID", "e.fkCompanyID")
                    .Collection(CustomEntities.Names.Employee, "PayRates"),

                new CustomEntityDefinition(CustomEntities.Names.Position, "PositionModel",
                        new CustomFieldSchema(companyGroupId, "EmployeePosition", "fkPosEmpID"), customFields,
                        "JOIN EmployeePosition ep ON v.fkPosEmpID = ep.pkPosEmpID JOIN Employee e on ep.fkEmpID = e.pkEmpID", "e.fkCompanyID")
                    .Collection(CustomEntities.Names.Employee, "Positions"),

                new CustomEntityDefinition(CustomEntities.Names.Training, "TrainingModel",
                        new CustomFieldSchema(companyGroupId, "EmployeeTraining", "fkEmpTrainingID"), customFields,
                        "JOIN EmployeeTraining et ON v.fkEmpTrainingId = et.pkEmpTrainingID JOIN Employee e on et.fkEmpID = e.pkEmpID", "e.fkCompanyID")
                    .Collection(CustomEntities.Names.Employee, "Training"),

                new CustomEntityDefinition(CustomEntities.Names.EmployeeClaim, "EmployeeClaimItemModel",
                        new CustomFieldSchema(companyGroupId, "ClaimComponent", "fkEmpClaimID"), customFields,
                        "JOIN EmployeeClaimItems eci ON v.fkEmpClaimID = eci.pkEmpClaimID JOIN Employee e on eci.fkEmployeeID = e.pkEmpID", "e.fkCompanyID")
                    .Collection(CustomEntities.Names.Employee, "EmployeeClaims"),

                new CustomEntityDefinition(CustomEntities.Names.OrganizationRegion, "OrganizationRegionModel",
                        new CustomFieldSchema(companyGroupId, "OrganizationRegion", "fkRegionID"), customFields,
                        "JOIN OrganizationRegion org ON v.fkRegionID = org.pkRegionID", "org.fkCompanyID")
                    .Reference(CustomEntities.Names.Position, "OrganizationRegion")
                    .Collection(CustomEntities.Names.Company, "Regions")
            ];
        }
    }
}
