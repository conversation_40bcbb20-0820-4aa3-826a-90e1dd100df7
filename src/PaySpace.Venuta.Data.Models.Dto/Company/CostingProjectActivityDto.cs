namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Company)]
    [Category(BulkUploadCategory.Costing)]
    [Display(Name = SystemAreas.CostingProjectActivity.Keys.PageHeader)]
    [DisplayName(SystemAreas.CostingProjectActivity.Area)]
    [NavigationControllerName("CostingProjectActivity")]
    public class CostingProjectActivityDto : CustomFieldEntityDto<CostingProjectActivity>
    {
        [Key]
        public long ProjectId { get; set; }

        [ODataNotEditable]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        public long CompanyId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.ProjectActivityDescription, Order = 0)]
        public string ProjectActivityDescription { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.ProjectActivityCode, Order = 1)]
        public string ProjectActivityCode { get; set; }

        [StringLength(50)]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.Glcode, Order = 2)]
        public string Glcode { get; set; }

        [BulkUploadLookup]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.OrganizationUnit, Order = 3)]
        public string OrganizationGroup { get; set; }

        [BulkUploadLookup]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.RegionId, Order = 4)]
        public string OrganizationRegion { get; set; }

        [BulkUploadLookup]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.KeyAccountManagerEmployeeNumber, Order = 5)]
        public string KeyAccountManagerEmployeeNumber { get; set; }

        [Display(Name = SystemAreas.CostingProjectActivity.Keys.MonthlyStaffBudget, Order = 6)]
        public int? MonthlyStaffBudget { get; set; }

        [Display(Name = SystemAreas.CostingProjectActivity.Keys.MonthlyBudgetedHours, Order = 7)]
        public decimal? MonthlyBudgetedHours { get; set; }

        [Display(Name = SystemAreas.CostingProjectActivity.Keys.HourlyCost, Order = 8)]
        public decimal? HourlyCost { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.InactiveDate, Order = 14)]
        public DateTime? InactiveDate { get; set; }

        [ODataNotEditable]
        [BulkUploadHidden]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.DoNotShow, Order = 9)]
        public bool? DoNotShow { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.ActiveHourlyRate, Order = 10)]
        public decimal? ActivityHourlyRate { get; set; }

        [ODataNotEditable]
        [BulkUploadHidden]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.TsdefaultHours, Order = 11)]
        public decimal? TsdefaultHours { get; set; }

        [Display(Name = SystemAreas.CostingProjectActivity.Keys.MaxHours, Order = 12)]
        public decimal? MaxHours { get; set; }

        [StringLength(50)]
        [BulkUploadLookup]
        [Display(Name = SystemAreas.CostingProjectActivity.Keys.CategoryCode, Order = 13)]
        public string OrganizationCategory { get; set; }
    }
}