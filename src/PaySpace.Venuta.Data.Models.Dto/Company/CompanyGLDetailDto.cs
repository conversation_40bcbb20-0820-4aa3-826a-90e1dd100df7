namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ModelLookup.GeneralLedger, ScreenType = ModelScreenType.Company)]
    [Category(BulkUploadCategory.PayrollConfig)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.CompanyGLDetail.Area)]
    [NavigationControllerName("CompanyGLDetail")]
    public class CompanyGLDetailDto : ICloneable
    {
        private string componentCode;
        private string glAccountNumber;
        private string glContraAccountNumber;

        [Key]
        public long CompanyGLDetailId { get; set; }

        [ODataNotEditable]
        [Display(Name = "lblCompanyComponentID", Order = 1)]
        public string ComponentCompany { get; set; }

        [ODataNotEditable]
        [Display(Name = "lblTaxCode", Order = 2)]
        public string TaxCode { get; set; }

        [Display(Name = "lblComponentCode", Order = 3)]
        public string ComponentCode { get => this.componentCode?.Trim(); set => this.componentCode = value; }

        [Display(Name = "lblGLAccountNumber", Order = 4)]
        public string GLAccountNumber { get => this.glAccountNumber?.Trim(); set => this.glAccountNumber = value; }

        [Display(Name = "lblGLContraAccountNumber", Order = 5)]
        public string GLContraAccountNumber { get => this.glContraAccountNumber?.Trim(); set => this.glContraAccountNumber = value; }

        [ODataNotEditable]
        [Display(Name = "lblPayslipAction", Order = 6)]
        public string PayslipAction { get; set; }

        [ODataNotEditable]
        [Display(Name = "lblGeneralLedger", Order = 8)]
        public string GeneralLedgerHeader { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}