namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName("ComponentCompany")]
    [DisplayName(SystemAreas.CompanyComponents.Area)]
    public class ComponentCompanyDashBoardDto : ICloneable
    {
        [Key]
        public long ComponentId { get; set; }

        [Display(Name = SystemAreas.CompanyComponents.Keys.ComponentName)]
        public string AliasDescription { get; set; }

        public bool? InPackage { get; set; }

        public decimal CustomNumbering { get; set; }

        public string ComponentDescription { get; set; }

        [Display(Name = SystemAreas.CompanyComponents.Keys.TaxCode)]
        public string TaxCode { get; set; }

        public string TaxCodeDescription { get; set; }

        public string FormulaDefinition { get; set; }

        public bool DisplayReadOnlyComp { get; set; }

        public bool Active { get; set; }

        public string OriginalTaxCode { get; set; }

        public bool IsLegislative { get; set; }

        public bool DisallowCompanyExemption { get; set; }

        [Display(Name = SystemAreas.CompanyComponents.Keys.HasSubCodes)]
        public bool HasSubCodes { get; set; }

        public bool AllocationType { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}