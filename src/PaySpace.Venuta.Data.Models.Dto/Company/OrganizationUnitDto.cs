namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Company)]
    [Category(BulkUploadCategory.OrganizationStructure)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.OrganizationUnit.Area)]
    [NavigationControllerName("OrganizationGroup")]
    public class OrganizationUnitDto : ICloneable
    {
        [Key]
        [BulkUploadExcelColumn(3)]
        public long OrganizationUnitId { get; set; }

        [Display(Order = 1)]
        public long? ParentOrganizationUnitId { get; set; }

        [Required]
        [Display(Order = 3)]
        [MaxLength(100)]
        [BulkUploadExcelColumn(2)]
        public string UploadCode { get; set; }

        [Required]
        [Display(Order = 0)]
        [MaxLength(100)]
        [BulkUploadExcelColumn(0)]
        public string Description { get; set; }

        [Display(Order = 5)]
        [BulkUploadExcelColumn(4)]
        public bool? CostCentre { get; set; }

        [Required]
        [Display(Order = 2)]
        [BulkUploadLookup]
        public string OrganizationLevel { get; set; }

        [Display(Order = 4)]
        [MaxLength(100)]
        [BulkUploadExcelColumn(1)]
        public string GroupGlKey { get; set; }

        public decimal? Budget { get; set; }

        [MaxLength(100)]
        public string Reference { get; set; }

        [BulkUploadLookup]
        public string ManagerEmployeeNumber { get; set; }

        [DataType(DataType.Date)]
        public DateTime? InactiveDate { get; set; }

        public OrganizationUnitAddressDto Address { get; set; }

        [Display(Order = 6)]
        public int ActiveEmployees { get; set; }

        [ClientDisplay(Client.NextGen)]
        public bool HasChildOrganizationUnits { get; set; }

        [ScaffoldColumn(false)]
        public List<string> ParentUnits { get; } = [];

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}