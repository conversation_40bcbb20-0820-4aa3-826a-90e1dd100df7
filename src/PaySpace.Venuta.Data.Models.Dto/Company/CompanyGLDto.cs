namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyGL.Area)]
    [NavigationControllerName(SystemAreas.CompanyGL.Area)]
    public class CompanyGLDto : ICloneable
    {
        [Key]
        public long CompanyGlId { get; set; }

        [Required]
        [Display(Name = SystemAreas.CompanyGL.Keys.HeaderName)]
        public string HeaderName { get; set; }

        [Required]
        [Display(Name = SystemAreas.CompanyGL.Keys.EffectiveDate)]
        public DateTime? EffectiveDate { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<CompanyGLDetailDto> CompanyGLDetails { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<CompanyGLExtraDto> CompanyGLExtras { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<CompanyGLInterAccountDto> CompanyGLInterAccounts { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}
