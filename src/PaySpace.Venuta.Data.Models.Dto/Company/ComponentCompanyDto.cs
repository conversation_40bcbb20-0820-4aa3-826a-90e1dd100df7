namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName("ComponentCompany")]
    [DisplayName(SystemAreas.CompanyComponents.Area)]
    public class ComponentCompanyDto : ICloneable
    {
        [Key]
        public long CompanyComponentId { get; set; }

        [Required]
        public string? Description { get; set; }

        public string? ComponentCode { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}