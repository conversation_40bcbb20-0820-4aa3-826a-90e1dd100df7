namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using Newtonsoft.Json;

    using PaySpace.Data.Models.Converters;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Company)]
    [Category(BulkUploadCategory.OrganizationStructure)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.OrganizationPosition.Area)]
    [NavigationControllerName("OrganizationPosition")]
    public class OrganizationPositionDetailDto : ICloneable
    {
        [Key]
        public long PositionDetailId { get; set; }

        [StringLength(80, ErrorMessage = "lblDescriptionLength")]
        [Required]
        [Display(Name = "lblPositionTitle", Order = 0)]
        public string PositionTitle { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        public long? OrganizationPositionId { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [Display(Name = "lblEffectiveDate", Order = 1)]
        public DateTime? EffectiveDate { get; set; }

        [ClientRequired(conditional: true)]
        [BulkUploadLookup]
        [Display(Name = "lblOrganizationGrade", Order = 2)]
        public string Grade { get; set; }

        [StringLength(1000, ErrorMessage = "lblPositionPurposeLength")]
        [Display(Name = "lblPositionPurpose", Order = 3)]
        public string PositionPurpose { get; set; }

        [StringLength(1000, ErrorMessage = "lblSpecialFeaturesLength")]
        [Display(Name = "lblSpecialFeatures", Order = 4)]
        public string SpecialFeatures { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumPositionType))]
        [Display(Name = "lblPositionType", Order = 5)]
        public string PositionType { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblSstLevel", Order = 6)]
        public string SSTLevel { get; set; }

        [StringLength(50, ErrorMessage = "lblPositionCodeLength")]
        [Display(Name = "lblPositionCode", Order = 8)]
        public string PositionCode { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        public string OrganizationPositionWithCode { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblDirectlyReportsPositionOverride", Order = 9)]
        public string DirectlyReportsPositionOverride { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblTemplateId", Order = 10)]
        public string CompanyTemplateDefinition { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "lblInactiveDate", Order = 11)]
        public DateTime? InactiveDate { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumOccupationalLevel))]
        [Display(Name = "lblOccupationalLevel", Order = 13)]
        public string OccupationalLevel { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblPositionFunction", Order = 14)]
        public EEFunction? EeFunction { get; set; }

        [Display(Name = "lblBeeLevelId", Order = 15)]
        public BEELevel? BeeLevel { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumOfoLevel))]
        [Display(Name = "lblOfoCodeId", Order = 16)]
        public string OfoCode { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblFamilyId", Order = 18)]
        public string CompanyPositionFamily { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblFunctAreaId", Order = 20)]
        public string FunctionArea { get; set; }

        [StringLength(50, ErrorMessage = "lblSalarySurvey1CodeLength")]
        [CountryVisible(CountryCode.ZA, CountryCode.GB, CountryCode.BR)]
        [Display(Name = "lblSalarySurveyCode1", Order = 21)]
        public string SalarySurveyCode1 { get; set; }

        [StringLength(50, ErrorMessage = "lblSalarySurvey2CodeLength")]
        [Display(Name = "lblSalarySurveyCode2", Order = 22)]
        public string SalarySurveyCode2 { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}