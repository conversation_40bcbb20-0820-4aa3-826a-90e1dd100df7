namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyLeaveBucketMapping.Area)]
    [NavigationControllerName("CompanyLeaveBucketMapping")]
    public class CompanyLeaveBucketMappingDto : ICloneable
    {
        [Key]
        public long LeaveBucketMappingId { get; set; }

        [BulkUploadLookup(OverrideLookup = "CompanyLeaveScheme")]
        public string FromLeaveScheme { get; set; }

        [BulkUploadLookup(OverrideLookup = "CompanyLeaveScheme")]
        public string ToLeaveScheme { get; set; }

        public List<CompanyLeaveBucketMappingDetailDto> LeaveBucketMappingDetails { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}
