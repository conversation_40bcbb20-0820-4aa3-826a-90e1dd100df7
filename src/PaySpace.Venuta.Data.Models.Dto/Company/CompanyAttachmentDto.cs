namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    public class CompanyAttachmentDto
    {
        [Key]
        public long CompanyAttachmentId { get; set; }

        [Required]
        public string SectionName { get; set; }

        [Required]
        public string AttachmentTitle { get; set; }

        [Required]
        public string AttachmentName { get; set; }
    }
}
