namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Company)]
    [Category(BulkUploadCategory.OrganizationStructure)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.OrganizationGrade.Area)]
    [NavigationControllerName("OrganizationGrade")]
    public class OrganizationGradeDto : ICloneable
    {
        [Key]
        public long GradeId { get; set; }

        [Required]
        [Display(Order = 1)]
        public string Description { get; set; }

        [Required]
        [Display(Order = 2)]
        public string Code { get; set; }

        [Display(Order = 3)]
        public decimal? Minimum { get; set; }

        [Display(Order = 4)]

        public decimal? Maximum { get; set; }
        [Display(Order = 5)]

        public decimal? BonusPercentage { get; set; }
        [Display(Order = 6)]

        public decimal? Average { get; set; }

        [Display(Order = 7)]
        public bool? IsEligiblePm { get; set; }

        [BulkUploadIgnore]
        public List<CompanyGradeFieldValueDto> AdditionalValues { get; set; } = new();

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}