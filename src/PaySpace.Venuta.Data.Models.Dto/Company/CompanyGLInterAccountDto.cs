namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyGLInterAccount.Area)]
    [NavigationControllerName(SystemAreas.CompanyGLDetail.Area)]
    public class CompanyGLInterAccountDto : ICloneable
    {
        [Key]
        public long CompanyGlInterAccId { get; set; }

        [Display(Name = SystemAreas.CompanyGLInterAccount.Keys.InterGLAccount)]
        public string InterGLAccount { get; set; }

        [Display(Name = SystemAreas.CompanyGLInterAccount.Keys.GLAccountNo)]
        public string GLAccountNo { get; set; }

        [Display(Name = SystemAreas.CompanyGLInterAccount.Keys.GLContraAccountNo)]
        public string GLContraAccountNo { get; set; }

        [BulkUploadLookup(OverrideLookup = "CompanyGlAccount")]
        public string GeneralLedgerHeader { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}
