namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    // Flattened DTO structure for the Company Leave Scheme Parameters screen / API
    [DisplayName(SystemAreas.CompanyLeaveSchemeParameter.Area)]
    [NavigationControllerName("CompanyLeaveSchemeParameter")]
    public class CompanyLeaveSetupDto : ICloneable
    {
        [Key]
        public long CompanyLeaveDetailId { get; set; }

        [ScaffoldColumn(false)]
        public long CompanyLeaveSetupId { get; set; }

        [BulkUploadLookup(OverrideLookup = "CompanyLeaveScheme")]
        public string? CompanyLeaveScheme { get; set; }

        public long? CompanyLeaveSchemeId { get; set; }

        [Required]
        public LeaveType LeaveType { get; set; }

        [Required]
        [StringLength(100)]
        public string LeaveDescription { get; set; }

        [Required]
        public int OrderNumber { get; set; }

        public CompanyLeaveSetupType CompanyLeaveSetupType { get; set; }

        [Required]
        [ODataNotEditable]
        [DataType(DataType.Date)]
        public DateTime EffectiveDate { get; set; }

        public string EffectiveDateForfeit { get; set; }

        [BulkUploadLookup(OverrideLookup = "MonthsOfYear")]
        public string? DropOffMonth { get; set; }

        public LeaveForfeitPeriod? LeaveForfeitPeriod { get; set; }

        public LeaveAccrualValue? LeaveAccrualValue { get; set; }

        [Display(Name = "AccrualPeriodId")]
        public LeaveAccrualPeriod AccrualPeriod { get; set; }

        [Display(Name = "AccrualOptionId")]
        public LeaveAccrualOption AccrualOption { get; set; }

        public int? ForfeitPeriod { get; set; }

        [BulkUploadLookup(OverrideLookup = "ForfeitCompanyLeaveSetup")]
        public string? ForfeitCompanyLeaveSetupId { get; set; }

        public decimal? CarryOverDays { get; set; }

        public int? NegativeLeaveAmount { get; set; }

        public double? ValueLessThan { get; set; }

        public double? ValueMoreThan { get; set; }

        // Uses the same lookup as EncashComponentId
        [BulkUploadLookup(OverrideLookup = "LeaveEncashmentComponent")]
        public string? LiabilityComponentId { get; set; }

        [BulkUploadLookup(OverrideLookup = "LeaveEncashmentComponent")]
        public string? EncashComponentId { get; set; }

        public bool? DoNotCalculateBceaValue { get; set; }

        public bool? AttachmentMandatory { get; set; }

        public bool? DoNotForceAttachment { get; set; }

        [Display(Name = "ForceAttachOnSecond")]
        public bool? ForceAttachmentOnSecond { get; set; }

        public double? AfterDays { get; set; }

        public bool? DisplayBalanceESS { get; set; }

        public bool? ReflectInHours { get; set; }

        public bool? IncludePendingApps { get; set; }

        public bool? IncludePH { get; set; }

        public bool? ApplyGradeBands { get; set; }

        [DataType(DataType.Date)]
        public DateTime? StopDate { get; set; }

        [StringLength(500)]
        public string BucketRules { get; set; }

        public bool? DoNotShowOnPaySlip { get; set; }

        public bool? ShowOnPaySlip { get; set; }

        public int? ConsecutiveDays { get; set; }

        public int? OffDays { get; set; }

        [StringLength(50)]
        public string? ParcelCombination { get; set; }

        [Required]
        public decimal Accrual { get; set; }

        public decimal? AccrualPeriodValue { get; set; }

        public bool ApplyServiceLength { get; set; }

        public bool? ApplyEmployeeDefined { get; set; }

        public bool? UpfrontProRateOptions { get; set; }

        public decimal? UpfrontMonthlyAccrual { get; set; }

        public int? UpfrontAccrualPeriod { get; set; }

        public decimal? MaxBalance { get; set; }

        public int? AccrualEngagementDay { get; set; }

        public bool? ProrateAccrual { get; set; }

        [StringLength(50)]
        public string? AccrualComponentCodeHours { get; set; }

        public IList<CompanyLeaveServiceLengthDto> CompanyLeaveServiceLengths { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}