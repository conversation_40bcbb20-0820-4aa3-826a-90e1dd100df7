namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyComponents.Area)]
    public class ComponentCompanyValueDto
    {
        [Key]
        public long ComponentValueId { get; set; }

        [ODataReadOnly]
        public string Description { get; set; }

        public decimal? ComponentValue { get; set; }

        [ODataNotEditable]
        public long ComponentCompanyId { get; set; }

        public int ValueTypeId { get; set; }

        [ScaffoldColumn(false)]
        public bool Active { get; set; }

        [ScaffoldColumn(false)]
        public long? CompanyRunId { get; set; }
    }
}