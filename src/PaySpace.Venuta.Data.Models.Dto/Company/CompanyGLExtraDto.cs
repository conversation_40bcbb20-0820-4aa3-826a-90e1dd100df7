namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyGLExtra.Area)]
    [NavigationControllerName(SystemAreas.CompanyGLDetail.Area)]
    public class CompanyGLExtraDto : ICloneable
    {
        [Key]
        public long CompanyExtraGLId { get; set; }

        [Display(Name = SystemAreas.CompanyGLExtra.Keys.EffectiveDate)]
        public DateTime? EffectiveDate { get; set; }

        [Display(Name = SystemAreas.CompanyGLExtra.Keys.ComponentDescription)]
        public string ComponentDescription { get; set; }

        [Display(Name = SystemAreas.CompanyGLExtra.Keys.GLAccountNo)]
        public string GLAccountNo { get; set; }

        [Display(Name = SystemAreas.CompanyGLExtra.Keys.GLContraAccountNo)]
        public string GLContraAccountNo { get; set; }

        [Display(Name = SystemAreas.CompanyGLExtra.Keys.GLAmount)]
        public decimal GLAmount { get; set; }

        [BulkUploadLookup(OverrideLookup = "CompanyGlAccount")]
        public string GeneralLedgerHeader { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}
