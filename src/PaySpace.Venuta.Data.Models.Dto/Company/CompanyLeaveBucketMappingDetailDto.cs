namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyLeaveBucketMapping.Area)]
    [NavigationControllerName("CompanyLeaveBucketMapping")]
    public class CompanyLeaveBucketMappingDetailDto : ICloneable
    {
        [Key]
        public long LeaveBucketMappingDetailId { get; set; }

        [ScaffoldColumn(false)]
        public long LeaveMappingId { get; set; }

        [ScaffoldColumn(false)]
        public string FromLeaveScheme { get; set; }

        [ScaffoldColumn(false)]
        public string ToLeaveScheme { get; set; }

        [BulkUploadLookup(nameof(FromLeaveScheme), OverrideLookup = "CompanyLeaveSetup")]
        public string FromLeaveBucket { get; set; }

        [BulkUploadLookup(nameof(ToLeaveScheme), OverrideLookup = "CompanyLeaveSetup")]
        public string ToLeaveBucket { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}
