namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyComponents.Area)]
    public class ComponentCompanyIndicatorLineDto
    {
        [Key]
        public long IndicatorLineId { get; set; }

        [ODataReadOnly]
        public long IndicatorId { get; set; }

        [Required]
        public string? IndicatorLineDescription { get; set; }

        [Required]
        public decimal? IndicatorLineValue { get; set; }
    }
}