namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.CompanyLeaveSchemeParameter.Area)]
    [NavigationControllerName("CompanyLeaveSchemeParameter")]
    public class CompanyLeaveServiceLengthDto : ICloneable
    {
        [Key]
        public long ServiceLengthId { get; set; }

        public long CompanyLeaveDetailId { get; set; }

        [Required]
        [StringLength(50)]
        public string ServiceDescription { get; set; }

        public int StartYear { get; set; }

        public int EndYear { get; set; }

        [Required]
        public decimal Accrual { get; set; }

        [Required]
        public LeaveAccrualPeriod AccrualPeriod { get; set; }

        [Required]
        public LeaveAccrualValue LeaveAccrualValue { get; set; }

        [Required]
        public decimal AccrualPeriodValue { get; set; }

        [StringLength(20)]
        public string GradeCode { get; set; }

        public long? ExtraFieldId { get; set; }

        public long? MaxBalanceFieldId { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}