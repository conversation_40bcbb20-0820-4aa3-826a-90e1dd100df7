namespace PaySpace.Venuta.Data.Models.Dto.Company
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using System.ComponentModel;
    using PaySpace.Venuta.Validation.Annotations;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Organization;

    [DisplayName(SystemAreas.EOnboarding.Area)]
    [NavigationControllerName(SystemAreas.EOnboarding.Area)]
    public class EmployeeEOnboardingDetailsDto : ICloneable
    {
        [Key]
        public long EmployeeDetailsId { get; set; }

        [ClientRequired(true)]
        public string EmployeeNumber { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "lblFirstname")]
        public string Firstname { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "lblLastname")]
        public string Lastname { get; set; }

        [Required]
        [Display(Name = "lblEmail")]
        public string Email { get; set; }

        [Display(Name = "lblPhoneNumber")]
        public string? PhoneNumber { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [Display(Name = "lblEmploymentDate")]
        public DateTime EmploymentDate { get; set; }

        [Display(Name = "lblOrganizationGroup")]
        [BulkUploadLookup(EnumType = typeof(OrganizationGroup))]
        public string? OrganizationGroup { get; set; }

        [Display(Name = "lblOrganizationPosition")]
        [BulkUploadLookup(OverrideLookup = "OrganizationPositionWithCode", EnumType = typeof(OrganizationPosition))]
        public string? OrganizationPosition { get; set; }

        [Display(Name = "lblSendOnboardingLink")]
        public bool SendLink { get; set; }

        [ODataReadOnly]
        public string EOnboardingStatus { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        public bool? ResendOnboardingEmail { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}
