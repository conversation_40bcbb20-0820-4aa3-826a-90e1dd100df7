namespace PaySpace.Venuta.Data.Models.Dto
{
    using System;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Validation.Annotations;

    public abstract class AddressDto : ICloneable
    {
        [Key]
        public long AddressId { get; set; }

        [MetadataInfo(MustBeUniqueValue = true)]
        [CountryNotRequired(CountryCode.SG)]
        [Display(Name = "lblAddressType", Order = 1)]
        public virtual AddressType AddressType { get; set; }

        [MetadataInfo(HasAdditionalValidation = true)]
        [CountryRequired(CountryCode.MY, CountryCode.IN, CountryCode.IE)]
        [ClientRequired(true)]
        [Display(Name = "AddressLine1", Order = 6)]
        [SecurityDisplay("lblStreetName", "lblAddressPostal1")]
        public string AddressLine1 { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblBlock", Order = 7)]
        [StringLength(50)]
        public virtual string Block { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblEntrance", Order = 8)]
        [StringLength(50)]
        public virtual string Entrance { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblStaircase", Order = 9)]
        [StringLength(50)]
        public virtual string Staircase { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblFloor", Order = 10)]
        [StringLength(50)]
        public virtual string Floor { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblDoor", Order = 11)]
        [StringLength(50)]
        public virtual string Door { get; set; }

        [MetadataInfo(HasAdditionalValidation = true)]
        [CountryRequired(CountryCode.AU)]
        [Display(Name = "AddressLine2", Order = 12)]
        [SecurityDisplay("lblSuburb", "lblAddressPostal2")]
        public string AddressLine2 { get; set; }

        [CountryRequired(CountryCode.MY, CountryCode.IN)]
        [ClientRequired(true)]
        [Display(Name = "AddressLine3", Order = 13)]
        [SecurityDisplay("lblCityTown", "lblAddressPostal3")]
        public string AddressLine3 { get; set; }

        [MetadataInfo(HasAdditionalValidation = true)]
        [CountryRequired(CountryCode.MY, CountryCode.IN, CountryCode.IE, CountryCode.AU)]
        [Display(Name = "lblCode", Order = 14)]
        [SecurityDisplay("lblCodePostal")]
        public string AddressCode { get; set; }

        [BulkUploadLookup(nameof(AddressCountry), EnumType = typeof(EnumProvince))]
        [SecurityDisplay("lblProvincePostal")]
        [CountryNotRequired(CountryCode.SG, CountryCode.IE)]
        [Display(Name = "lblProvince", Order = 16)]
        public string Province { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumAddressCountry))]
        [CountryNotRequired(CountryCode.SG, CountryCode.IE)]
        [Display(Name = "lblCountry", Order = 15)]
        [SecurityDisplay("lblCountry", "lblCountryPostal")]
        public string AddressCountry { get; set; }

        [Display(Name = "lblUnitNumber", Order = 3)]
        [StringLength(8)]
        public string UnitNumber { get; set; }

        [Display(Name = "lblComplexName", Order = 4)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string Complex { get; set; }

        [CountryRequired(CountryCode.AU)]
        [Display(Name = "lblStreetNum", Order = 5)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string StreetNumber { get; set; }

        [Display(Name = "lblSameAsPostal", Order = 2)]
        public virtual bool? SameAsPhysical { get; set; }

        [Display(Name = "lblIsCareofAddress", Order = 18)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public virtual bool? IsCareofAddress { get; set; }

        [Display(Name = "lblCareofAddress", Order = 19)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public virtual string CareOfIntermediary { get; set; }

        [Display(Name = "lblSpecialServices", Order = 17)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public virtual string SpecialServices { get; set; }

        [CountryRequired(CountryCode.ES)]
        [BulkUploadLookup(EnumType = typeof(EnumMunicipality))]
        [CountryVisible(CountryCode.BR, CountryCode.ES, CountryCode.FR)]
        [Display(Name = "lblMunicipality")]
        public string Municipality { get; set; }

        [CountryRequired(CountryCode.ES)]
        [BulkUploadLookup(EnumType = typeof(EnumAddressStreetType))]
        [CountryVisible(CountryCode.BR, CountryCode.ES)]
        [Display(Name = "lblAddressStreetType")]
        public string AddressStreetType { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}