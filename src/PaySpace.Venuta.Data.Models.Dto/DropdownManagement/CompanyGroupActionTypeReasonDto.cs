namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyGroupActionTypeReason)]
    public class CompanyGroupActionTypeReasonDto : ICloneable
    {
        [Key]
        public long ActionTypeReasonId { get; set; }

        [Required]
        [BulkUploadLookup]
        public string CompanyGroupActionType { get; set; }

        [Required]
        [StringLength(10)]
        public string ActionReasonCode { get; set; }

        [Required]
        [StringLength(50)]
        public string ActionReasonDescription { get; set; }

        [DataType(DataType.Date)]
        public DateTime? InactiveDate { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
