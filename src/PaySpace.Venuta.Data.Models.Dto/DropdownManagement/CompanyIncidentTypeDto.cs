namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyIncidentType)]
    public class CompanyIncidentTypeDto : ICloneable
    {
        [Key]
        public long CompanyIncidentTypeId { get; set; }

        [Required]
        [StringLength(100)]
        public string IncidentType { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}