namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyAttachmentClassification)]
    public class CompanyAttachmentClassificationDto : ICloneable
    {
        [Key]
        public long ClassificationId { get; set; }

        [Required]
        [StringLength(50)]
        public string Classification { get; set; }

        [StringLength(800)]
        public string ExcludeSecurityRoles { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
