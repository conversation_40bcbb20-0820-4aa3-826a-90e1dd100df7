namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyEmploymentCategory)]
    public class CompanyEmploymentCategoryDto : ICloneable
    {
        [Key]
        public long CategoryId { get; set; }

        [Required]
        [StringLength(10)]
        public string CategoryCode { get; set; }

        [Required]
        [StringLength(100)]
        public string CategoryDescription { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}