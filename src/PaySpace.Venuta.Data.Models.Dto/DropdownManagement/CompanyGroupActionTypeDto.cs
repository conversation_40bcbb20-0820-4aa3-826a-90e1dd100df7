namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyGroupActionType)]
    public class CompanyGroupActionTypeDto : ICloneable
    {
        [Key]
        public long ActionTypeId { get; set; }

        [Required]
        [StringLength(10)]
        public string ActionTypeCode { get; set; }

        [Required]
        [StringLength(50)]
        public string ActionTypeDescription { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
