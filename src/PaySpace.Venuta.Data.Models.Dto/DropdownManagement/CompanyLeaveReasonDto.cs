namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyLeaveReason)]
    public class CompanyLeaveReasonDto : ICloneable
    {
        [Key]
        public long LeaveReasonId { get; set; }

        [BulkUploadLookup]
        public string CompanyLeaveSetup { get; set; }

        [Required]
        public LeaveType LeaveType { get; set; }

        [Required]
        [StringLength(100)]
        public string LeaveReason { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
