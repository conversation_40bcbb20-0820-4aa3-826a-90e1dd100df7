namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyLegalBody)]
    public class CompanyLegalBodyDto : ICloneable
    {
        [Key]
        public long CompanyLegalBodyID { get; set; }

        [Required]
        [StringLength(100)]
        public string LegalBodyDescription { get; set; }

        [Required]
        [StringLength(100)]
        public string LegalBodyCode { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
