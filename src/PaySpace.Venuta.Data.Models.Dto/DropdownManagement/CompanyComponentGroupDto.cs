namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyComponentGroup)]
    public class CompanyComponentGroupDto : ICloneable
    {
        [Key]
        public int ComponentGroupId { get; set; }

        [Required]
        [StringLength(10)]
        public string ComponentGroupCode { get; set; }

        [Required]
        [StringLength(100)]
        public string ComponentGroupDescription { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
