namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyBankName)]
    public class CompanyBankNameDto : ICloneable
    {
        [Key]
        public long CompanyBankNameId { get; set; }

        [Required]
        [StringLength(100)]
        public string BankName { get; set; }

        [StringLength(10)]
        public string DefaultBranchCode { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
