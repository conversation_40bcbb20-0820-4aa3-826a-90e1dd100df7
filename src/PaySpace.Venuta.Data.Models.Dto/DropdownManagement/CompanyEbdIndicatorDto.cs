namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyEbdIndicator)]
    public class CompanyEbdIndicatorDto : ICloneable
    {
        [Key]
        public long EbdIndicatorId { get; set; }

        [Required]
        [StringLength(50)]
        public string EbdIndicator { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}
