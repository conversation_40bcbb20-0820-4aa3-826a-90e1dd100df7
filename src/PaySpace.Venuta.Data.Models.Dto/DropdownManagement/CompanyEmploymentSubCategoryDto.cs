namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyEmploymentSubCategory)]
    public class CompanyEmploymentSubCategoryDto : ICloneable
    {
        [Key]
        public long SubCategoryId { get; set; }

        [Required]
        [StringLength(10)]
        public string SubCategoryCode { get; set; }

        [Required]
        [StringLength(100)]
        public string Description { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}