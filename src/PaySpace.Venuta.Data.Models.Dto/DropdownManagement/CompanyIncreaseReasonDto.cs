namespace PaySpace.Venuta.Data.Models.Dto.DropdownManagement
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName(SystemAreas.OtherDropdowns.Area)]
    [DisplayName(SystemAreas.OtherDropdowns.Areas.CompanyIncreaseReason)]
    public class CompanyIncreaseReasonDto : ICloneable
    {
        [Key]
        public long ReasonId { get; set; }

        [Required]
        [StringLength(50)]
        public string ReasonCode { get; set; }

        [Required]
        [StringLength(100)]
        public string Description { get; set; }

        public object Clone() => this.MemberwiseClone();
    }
}