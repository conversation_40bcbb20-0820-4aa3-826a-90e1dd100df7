namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.BasicInformation)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.EmploymentStatus.Area)]
    [NavigationControllerName("EmploymentStatus")]
    public class EmployeeEmploymentStatusDto : CustomFieldEntityDto<EmployeeEmploymentStatusDto>, IEmployeeTerminationDtoEntity
    {
        [Key]
        public long EmploymentStatusId { get; set; }

        [ODataNotEditable]
        [BulkUploadIgnore]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [Required]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        [ODataNotEditable]
        [Display(Order = 0)]
        public string EmployeeNumber { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [Display(Name = "lblFullName", Order = 1)]
        public string FullName { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [Display(Order = 3)]
        public DateTime? GroupJoinDate { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [Display(Order = 4)]
        public DateTime? EmploymentDate { get; set; }

        [DataType(DataType.Date)]
        [Display(Order = 31)]
        public DateTime? TerminationDate { get; set; }

        [Display(Order = 32)]
        [BulkUploadLookup(EnumType = typeof(EnumTerminationReason), RelatedProperty = nameof(this.TaxStatus))]
        public string TerminationReason { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? TerminationReasonId { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumTaxStatus))]
        [CountryNotRequired(CountryCode.GB, CountryCode.SG, CountryCode.AU)]
        [Display(Order = 18)]
        public string TaxStatus { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? TaxStatusId { get; set; }

        [ClientRequired(conditional: true)]
        [Display(Order = 27)]
        [CountryRequired(CountryCode.IN)]
        public string TaxReferenceNumber { get; set; }

        [CountryNotRequired(CountryCode.GB, CountryCode.SG, CountryCode.CA, CountryCode.AU)]
        [BulkUploadLookup(EnumType = typeof(EnumNaturePerson))]
        [Display(Order = 8)]
        public string NatureOfPerson { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int NatureOfPersonId { get; set; }

        [Display(Order = 19)]
        [CountryVisible(CountryCode.ZA, CountryCode.BW, CountryCode.SZ)]
        public string TaxDirectiveNumber { get; set; }

        [Display(Order = 2)]
        public EmploymentAction? EmploymentAction { get; set; }

        [Display(Order = 33)]
        [BulkUploadLookup]
        [ClientRequired(conditional: true)]
        public string TerminationCompanyRun { get; set; }

        [BulkUploadLookup(nameof(NatureOfPerson), EnumType = typeof(EnumIdentityType))]
        [Display(Order = 9)]
        public string IdentityType { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? IdentityTypeId { get; set; }

        [Display(Name = "lblIdNumber", Order = 10)]
        public string IdNumber { get; set; }

        [DataType(DataType.Date)]
        [CountryVisible(CountryCode.CA)]
        [Display(Name = "lblIdNumberExpiryDate", Order = 11)]
        public DateTime? IdNumberExpiryDate { get; set; }

        [Display(Order = 15)]
        public string PassportNumber { get; set; }

        [Display(Order = 24)]
        [CountryVisible(CountryCode.ZA, CountryCode.BW, CountryCode.SZ, CountryCode.MU, CountryCode.LS, CountryCode.ZM, CountryCode.SL, CountryCode.TD, CountryCode.SN)]
        public PercentageAmount? PercentageAmount { get; set; }

        [Display(Name = "lblAmount", Order = 26)]
        [CountryVisible(CountryCode.ZA, CountryCode.BW, CountryCode.SZ, CountryCode.MU, CountryCode.LS, CountryCode.ZM, CountryCode.SL, CountryCode.TD, CountryCode.SN)]
        public decimal? Amount { get; set; }

        [Display(Name = "lblPercent", Order = 25)]
        [CountryVisible(CountryCode.ZA, CountryCode.BW, CountryCode.SZ, CountryCode.MU, CountryCode.LS, CountryCode.ZM, CountryCode.SL, CountryCode.TD, CountryCode.SN)]
        public decimal? Percentage { get; set; }

        [BulkUploadHidden]
        [CountryVisible(CountryCode.ZA)]
        [Obsolete("Removed due to legislation ZA changes")]
        [Display(Order = 22, Name = "Do not use, will be removed in the future - DeemedMonthlyRemuneration")]
        public decimal? DeemedMonthlyRemuneration { get; set; }

        [BulkUploadHidden]
        [CountryVisible(CountryCode.ZA)]
        [Obsolete("Removed due to legislation ZA changes")]
        [Display(Order = 21, Name = "Do not use, will be removed in the future - Deemed75Indicator")]
        public bool? Deemed75Indicator { get; set; }

        [BulkUploadHidden]
        [CountryVisible(CountryCode.ZA)]
        [Obsolete("Removed due to legislation ZA changes")]
        [Display(Order = 23, Name = "Do not use, will be removed in the future - DeemedRecoveryMonthly")]
        public bool? DeemedRecoveryMonthly { get; set; }

        [Display(Order = 34)]
        public bool? EncashLeave { get; set; }

        [Display(Order = 20)]
        [CountryVisible(CountryCode.ZA)]
        public bool? Irp30 { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        public bool? FinalizeIssueTaxCert { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumPassportCountry))]
        [Display(Name = "PassportCountryId", Order = 14)]
        public string PassportCountry { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? PassportCountryId { get; set; }

        [DataType(DataType.Date)]
        [Display(Order = 16)]
        public DateTime? PassportIssued { get; set; }

        [DataType(DataType.Date)]
        [Display(Order = 17)]
        public DateTime? PassportExpiry { get; set; }

        [DataType(DataType.Date)]
        [Display(Order = 12)]
        public DateTime? PermitIssued { get; set; }

        [DataType(DataType.Date)]
        [Display(Order = 13)]
        public DateTime? PermitExpiry { get; set; }

        [DataType(DataType.Date)]
        [CountryVisible(CountryCode.MY)]
        public DateTime? OtherIssued { get; set; }

        [DataType(DataType.Date)]
        [CountryVisible(CountryCode.MY)]
        public DateTime? OtherExpiry { get; set; }

        [DataType(DataType.Date)]
        [CountryVisible(CountryCode.MY)]
        public DateTime? MyKadIssued { get; set; }

        [DataType(DataType.Date)]
        [CountryVisible(CountryCode.MY)]
        public DateTime? MyKadExpiry { get; set; }

        [CompanyDisplayName(Code = CompanySettingCode.EmploymentStatus.AdditionalDate1Display)]
        [CompanyRequired(CompanySettingCode.EmploymentStatus.AdditionalDate1Required)]
        [CompanyVisible(Code = CompanySettingCode.EmploymentStatus.AdditionalDate1Display)]
        [DataType(DataType.Date)]
        [Display(Order = 5, Name = "lblCustomDate1")]
        public DateTime? AdditionalDate { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        [DataType(DataType.Date)]
        public DateTime? EmploymentCaptureDate { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        [DataType(DataType.Date)]
        public DateTime? TerminationCaptureDate { get; set; }

        [Display(Order = 28)]
        [CompanyVisible(Code = CompanySettingCode.EmploymentStatus.TempWorkerCheckDisplay)]
        public bool? TempWorker { get; set; }

        [CountryVisible(CountryCode.IE)]
        [Display(Order = 29)]
        public bool? ShadowPayroll { get; set; }

        [CompanyDisplayName(Code = CompanySettingCode.EmploymentStatus.AdditionalDate2Display)]
        [CompanyRequired(CompanySettingCode.EmploymentStatus.AdditionalDate2Required)]
        [CompanyVisible(Code = CompanySettingCode.EmploymentStatus.AdditionalDate2Display)]
        [DataType(DataType.Date)]
        [Display(Order = 6, Name = "lblCustomDate2")]
        public DateTime? AdditionalDate1 { get; set; }

        [Display(Order = 7)]
        public bool? NotReEmployable { get; set; }

        [Display(Order = 29)]
        public string ReferenceNumber { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        [ODataNotEditable]
        public long? OldEmployeeId { get; set; }

        [Obsolete("Do not use, will be removed in the future - TaxOffice")]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "Do not use, will be removed in the future - TaxOffice")]
        public int? TaxOffice { get; set; }

        [Obsolete("Do not use, will be removed in the future - IT3AReason")]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "Do not use, will be removed in the future - IT3AReason")]
        public int? IT3AReason { get; set; }

        [CountryVisible(CountryCode.CA)]
        [BulkUploadLookup(EnumType = typeof(EnumDentalBenefit))]
        [Display(Name = "DentalBenefitId", Order = 29)]
        public string DentalBenefit { get; set; }

        [CountryVisible(CountryCode.IE)]
        [Display(Name = "Directorship", Order = 30)]
        public Directorship? Directorship { get; set; }

        [CountryVisible(CountryCode.IE)]
        [Display(Name = "EmploymentIdentifier", Order = 30)]
        public string EmploymentIdentifier { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "TerminationPaySeverance", Order = 35)]
        public bool? TerminationPaySeverance { get; set; }

        [CountryVisible(CountryCode.ES)]
        [ClientRequired(conditional: true)]
        [BulkUploadLookup(EnumType = typeof(EnumSeveranceDays))]
        [Display(Name = "SeveranceDay", Order = 36)]
        public string SeveranceDay { get; set; }
    }
}