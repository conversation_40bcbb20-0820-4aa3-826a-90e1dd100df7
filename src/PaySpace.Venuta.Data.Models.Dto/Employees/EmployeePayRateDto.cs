namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Dto.Converters;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.PayrollProcessing)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.Payroll.PayRates.Area)]
    [NavigationControllerName("PayRate")]
    public class EmployeePayRateDto : CustomFieldEntityDto<EmployeePayRateDto>, IEmployeeNumberEntity
    {
        [Key]
        public long PayRateId { get; set; }

        [ODataNotEditable]
        [BulkUploadIgnore]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [Required]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        [ODataNotEditable]
        [Display(Name = "lblEmpNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [Display(Name = "FullName", Order = 1)]
        public string FullName { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [Display(Name = "lblEffectiveDate", Order = 2)]
        public DateTime EffectiveDate { get; set; }

        [Display(Name = "lblAutoPay", Order = 3)]
        public bool? AutomaticPayInd { get; set; }

        [ClientRequired(conditional: true)]
        [BulkUploadLookup]
        [ODataNotEditable]
        [Display(Name = "lblCompanyFrequency", Order = 4)]
        [CompanyVisible(Code = CompanySettingCode.BasicProfile.CompanyFrequency, Hide = true)]
        public string CompanyFrequency { get; set; }

        [ClientDisplay(Client.PowerBi)]
        public long FrequencyId { get; set; }

        [ClientRequired]
        [Display(Name = "lblFrequency", Order = 5)]
        public PayFrequency PayFrequency { get; set; }

        [JsonConverter(typeof(DoubleDecimalPlacesConverter), "0.##############")]
        [ClientRequired(conditional: true)]
        [Display(Name = "lblHoursPerDay", Order = 6)]
        public double? HoursPerDay { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblHoursPerMonth", Order = 7)]
        public double? HoursPerMonth { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblHoursPerWeek", Order = 8)]
        public double? HoursPerWeek { get; set; }

        [JsonConverter(typeof(DoubleDecimalPlacesConverter), "0.##############")]
        [ClientRequired(conditional: true)]
        [Display(Name = "lblDaysPerPeriod", Order = 9)]
        public double? DaysPerPeriod { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblWeeksPerMonth", Order = 10)]
        public double? WeeksPerMonth { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblHourlyRate", Order = 11)]
        [SensitiveData]
        public decimal? HourlyRate { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblDailyRate", Order = 12)]
        [SensitiveData]
        public decimal? DailyRate { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "WeeklyRate", Order = 13)]
        [SensitiveData]
        public decimal? WeeklyRate { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblMonthlyRate", Order = 14)]
        [SensitiveData]
        public decimal? MonthlyRate { get; set; }

        [Display(Name = "lblDailyRatePercentageChange", Order = 15)]
        public double? PercentageOfPreviousPackage { get; set; }

        [Required]
        [Display(Name = "lblPackage", Order = 16)]
        [SensitiveData]
        public decimal Package { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblHoursPerFortnight")]
        public double? HoursPerFortnight { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblFortnightlyRate")]
        [SensitiveData]
        public decimal? FortnightlyRate { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        public double? HoursPerSemiMonth { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [SensitiveData]
        public decimal? SemiMonthlyRate { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [SensitiveData]
        public decimal? AnnualPackage { get; set; }

        [Display(Name = "lblThirteenCheque", Order = 17)]
        public bool? ThirteenCheque { get; set; }

        [Display(Name = "IsAnnual", Order = 18)]
        public bool? IsAnnual { get; set; }

        [Display(Name = "lblPackage2", Order = 19)]
        [CompanyVisible(Code = CompanySettingCode.PayRate.SecondPayRate)]
        [SensitiveData]
        public decimal? Package2 { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblReason", Order = 20)]
        public string Reason { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumCurrency))]
        [ClientRequired(conditional: true)]
        [Display(Name = "lblCurrency", Order = 21)]
        public string Currency { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? CurrencyId { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblCategory", Order = 22)]
        public string OrganizationCategory { get; set; }

        [SecurityDisplay("lblPackage")]
        [Display(Name = "lblSalaryCoefficient")]
        [CountryVisible(CountryCode.ES)]
        [SensitiveData]
        public decimal? SalaryCoefficient { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblPartialSalary")]
        [CountryVisible(CountryCode.ES)]
        [SensitiveData]
        public decimal? PartialSalary { get; set; }

        [Display(Name = "lblMonday", Order = 23)]
        [CompanyVisible(
            Code = CompanyCalculationSettingConstants.ProrateLookupCode,
            ExpectedValues = [
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.DaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualRosterDaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.WorkingDaysNoProration])]
        public bool? WorkingDayMonday { get; set; }

        [Display(Name = "lblTuesday", Order = 24)]
        [CompanyVisible(
            Code = CompanyCalculationSettingConstants.ProrateLookupCode,
            ExpectedValues = [
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.DaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualRosterDaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.WorkingDaysNoProration])]
        public bool? WorkingDayTuesday { get; set; }

        [Display(Name = "lblWednesday", Order = 25)]
        [CompanyVisible(
            Code = CompanyCalculationSettingConstants.ProrateLookupCode,
            ExpectedValues = [CompanyCalculationSettingConstants.ProrateLookupValues.ActualWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.DaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualRosterDaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.WorkingDaysNoProration])]
        public bool? WorkingDayWednesday { get; set; }

        [Display(Name = "lblThursday", Order = 26)]
        [CompanyVisible(
            Code = CompanyCalculationSettingConstants.ProrateLookupCode,
            ExpectedValues = [CompanyCalculationSettingConstants.ProrateLookupValues.ActualWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.DaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualRosterDaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.WorkingDaysNoProration])]
        public bool? WorkingDayThursday { get; set; }

        [Display(Name = "lblFriday", Order = 27)]
        [CompanyVisible(
            Code = CompanyCalculationSettingConstants.ProrateLookupCode,
            ExpectedValues = [CompanyCalculationSettingConstants.ProrateLookupValues.ActualWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.DaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualRosterDaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.WorkingDaysNoProration])]
        public bool? WorkingDayFriday { get; set; }

        [Display(Name = "lblSaturday", Order = 28)]
        [CompanyVisible(
            Code = CompanyCalculationSettingConstants.ProrateLookupCode,
            ExpectedValues = [CompanyCalculationSettingConstants.ProrateLookupValues.ActualWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.DaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualRosterDaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.WorkingDaysNoProration])]
        public bool? WorkingDaySaturday { get; set; }

        [Display(Name = "lblSunday", Order = 29)]
        [CompanyVisible(
            Code = CompanyCalculationSettingConstants.ProrateLookupCode,
            ExpectedValues = [CompanyCalculationSettingConstants.ProrateLookupValues.ActualWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.DaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.ActualRosterDaysPerPeriodWorkingDays,
                CompanyCalculationSettingConstants.ProrateLookupValues.WorkingDaysNoProration])]
        public bool? WorkingDaySunday { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblRetro", Order = 30)]
        public DateTime? DateAdded { get; set; }

        [DataType(DataType.MultilineText)]
        [Display(Name = "lblComments", Order = 31)]
        public string Comments { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "DateCreated")]
        public DateTime? DateCreated { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        public decimal? OneDollarInCurrency { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumBureauTaxabilityOption))]
        [CountryVisible(CountryCode.CA, CountryCode.IN, CountryCode.ES)]
        [Display(Name = "lblProvinceOfEmployment", Order = 32)]
        [CountryRequired(CountryCode.CA, CountryCode.IN, CountryCode.ES)]
        public string BureauTaxabilityOption { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ODataReadOnly(HideInEmptySheet = true)]
        [ClientDisplay(Client.NextGen)]
        public int? BureauTaxabilityOptionId { get; set; }
    }
}