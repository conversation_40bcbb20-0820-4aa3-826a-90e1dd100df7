namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [EntityType(typeof(Employee))]
    [BulkUploadEntity(ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.BasicInformation)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.Profile.Basic)]
    [NavigationControllerName("Profile")]
    public class EmployeeHistoryDto : EmployeeDtoBase, IEmployeeNumberEntity
    {
        [Key]
        public long EmployeeHistoryId { get; set; }

        [ScaffoldColumn(false)]
        public long EmployeeId { get; set; }

        [MetadataInfo(MustBeUniqueValue = true)]
        [DataType(DataType.Date)]
        [Display(Order = 10)]
        public DateTime EffectiveDate { get; set; }
    }
}