namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.Leave)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.Leave.Setup)]
    [NavigationControllerName("LeaveSetup")]
    public class EmployeeLeaveSetupDto : IEmployeeNumberEntity, ICloneable
    {
        [Key]
        public long LeaveSetupId { get; set; }

        [Required]
        [BulkUploadLookup]
        [ODataNotEditable]
        [Display(Name = "lblLeaveScheme", Order = 2)]
        public string CompanyLeaveScheme { get; set; }

        [ODataNotEditable]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [Required]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        [ODataNotEditable]
        [Display(Name = "lblEmpNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [Display(Name = "FullName", Order = 1)]
        public string FullName { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [ODataNotEditable]
        [Display(Name = "lblEffectiveDate", Order = 2)]
        public DateTime? EffectiveDate { get; set; }

        [BulkUploadLookup]
        [ODataNotEditable]
        [Display(Name = "lblHolidayCategory", Order = 3)]
        public string PublicHolidayCategory { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}