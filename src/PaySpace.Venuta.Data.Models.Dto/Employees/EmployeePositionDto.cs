namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.BasicInformation)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.Profile.Positions)]
    [NavigationControllerName("Positions")]
    public class EmployeePositionDto : CustomFieldEntityDto<EmployeePositionDto>, IEmployeeNumberEntity
    {
        [Key]
        public long EmployeePositionId { get; set; }

        [ODataNotEditable]
        [BulkUploadIgnore]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [Required]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        [ODataNotEditable]
        [Display(Name = "lblEmpNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [Display(Name = "lblFullName", Order = 1)]
        public string FullName { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [Display(Name = "lblEffectiveDate", Order = 2)]
        public DateTime EffectiveDate { get; set; }

        [BulkUploadLookup]
        [ClientRequired(true)]
        [Display(Name = "lblOrganizationPosition", Order = 6)]
        [ReadOnlyFromVersion("1.1")]
        public string OrganizationPosition { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        public long? OrganizationPositionId { get; set; }

        [BulkUploadLookup]
        [ClientRequired(true)]
        [Display(Name = "lblOrganizationPosition", Order = 6)]
        [IgnoreBeforeVersion("1.1")]
        public string OrganizationPositionWithCode { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumPositionType))]
        [Display(Name = "lblPositionType")]
        public string PositionType { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? PositionTypeId { get; set; }

        [BulkUploadLookup]
        [CompanyRequired(CompanySettingCode.Positions.PositionGrade)]
        [Display(Name = "lblGrade")]
        public string Grade { get; set; }

        [ODataReadOnly]
        [ODataNotEditable]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblOccupationalLevel")]
        public string OccupationalLevel { get; set; }

        [ClientDisplay(Client.PowerBi)]
        [Display(Name = "lblPositionFunction")]
        public string EeFunction { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblOverridingReportsToPosition")]
        public string DirectlyReportsPositionOverride { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblDirectlyReportsPosition")]
        public string DirectlyReportsPosition { get; set; }

        [ClientDisplay(Client.PowerBi)]
        public long? OrganizationGroupId { get; set; }

        [BulkUploadLookup]
        [ClientRequired(true)]
        [Display(Name = "lblOrganizationGroup", Order = 7)]
        public string OrganizationGroup { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        public string OrganizationGroupDescription { get; set; }

        [ODataReadOnly]
        [BulkUploadIgnore]
        [IgnoreAfterVersion("1.0")]
        [Display(Name = "OrganizationGroups")]
        public IEnumerable<string>? OrganizationGroups { get; set; }

        [ODataReadOnly]
        [BulkUploadIgnore]
        [IgnoreBeforeVersion("1.1")]
        [Display(Name = "OrganizationGroups")]
        public IEnumerable<PositionOrganizationUnit>? OrganizationGroups_1_1 { get; set; } = Array.Empty<PositionOrganizationUnit>();

        [BulkUploadLookup]
        [CountryRequired(CountryCode.BR)]
        [CompanyRequired(CompanySettingCode.Positions.Region)]
        [Display(Name = "lblOrganizationRegion", Order = 10)]
        public string OrganizationRegion { get; set; }

        [BulkUploadLookup]
        [CompanyRequired(CompanySettingCode.Positions.PayPoint)]
        [Display(Name = "lblPayPoint", Order = 11)]
        public string PayPoint { get; set; }

        [ODataReadOnly]
        [ODataNotEditable]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [Display(Name = "lblDirectlyReportsEmployee", Order = 4)]
        public string DirectlyReportsEmployee { get; set; }

        [BulkUploadLookup(OverrideLookup = "DirectlyReportsEmployeeNumberV2")]
        [CompanyRequired(CompanySettingCode.Positions.DirectReportPerson)]
        [Display(Name = "lblDirectlyReportsEmployeeNumber", Order = 3)]
        public string DirectlyReportsEmployeeNumber { get; set; }

        [BulkUploadLookup]
        [CompanyRequired(CompanySettingCode.Positions.Category)]
        [Display(Name = "lblCategory")]
        public string EmploymentCategory { get; set; }

        [BulkUploadLookup]
        [CompanyRequired(CompanySettingCode.Positions.SubCategory)]
        [Display(Name = "lblSubCategory")]
        public string EmploymentSubCategory { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        [ODataNotEditable]
        [Display(Name = "lblAdministrator")]
        public string Administrator { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblAdministratorEmployeeNumber")]
        public string AdministratorEmployeeNumber { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblWorkflow")]
        public string WorkflowRole { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblGl")]
        public string GeneralLedger { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumTradeUnion))]
        [CompanyVisible(Code = CompanySettingCode.Positions.ShowTradeUnion)]
        [Display(Name = "lblTradeUnion")]
        public string TradeUnion { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? TradeUnionId { get; set; }

        [Display(Name = "lblIsPromotion")]
        public bool? IsPromotion { get; set; }

        [Display(Name = "lblKeepJob", Order = 8)]
        [CompanyVisible(Code = CompanySettingCode.Positions.JobManagement)]
        public bool KeepJob { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblRoster")]
        public string Roster { get; set; }

        [BulkUploadLookup]
        [ClientRequired(true)]
        [Display(Name = "lblJob", Order = 9)]
        [CompanyVisible(Code = CompanySettingCode.Positions.JobManagement)]
        public string Job { get; set; }

        [Display(Name = "lblComments")]
        public string Comments { get; set; }

        [Display(Name = "lblAltPositionName", Order = 12)]
        [CompanyVisible(Code = CompanySettingCode.Positions.EmployeeAltJobTitle)]
        public string AltPositionName { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "lblPositionEffectiveDate", Order = 5)]
        public DateTime? PositionEffectiveDate { get; set; }

        [CountryVisible(CountryCode.BR)]
        [CompanyVisible(Code = CompanySettingCode.Positions.ShowTradeUnion)]
        [BulkUploadLookup]
        [Display(Name = "CustomTradeUnion")]
        public string CustomTradeUnion { get; set; }
    }

    [NavigationControllerName("Positions")]
    public class PositionOrganizationUnit
    {
        [Key]
        public long OrganizationUnitId { get; set; }

        [ScaffoldColumn(false)]
        public long EmployeePositionId { get; set; }

        public long? ParentOrganizationUnitId { get; set; }

        public string UploadCode { get; set; }

        public string Description { get; set; }

        public bool? CostCentre { get; set; }

        public string OrganizationLevel { get; set; }

        public string GroupGlKey { get; set; }

        public decimal? Budget { get; set; }

        public string Reference { get; set; }

        public string ManagerEmployeeNumber { get; set; }

        [DataType(DataType.Date)]
        public DateTime? InactiveDate { get; set; }

        public PositionOrganizationUnitAddress Address { get; set; }
    }

    // We need this for backwards compatibility
    public class PositionOrganizationUnitAddress : PaySpace.Venuta.Data.Models.Dto.AddressDto
    {
        [ScaffoldColumn(false)]
        public override AddressType AddressType { get; set; } = AddressType.Physical;

        [ScaffoldColumn(false)]
        public override bool? SameAsPhysical { get; set; }

        [ScaffoldColumn(false)]
        public override bool? IsCareofAddress { get; set; }

        [ScaffoldColumn(false)]
        public override string CareOfIntermediary { get; set; }

        [ScaffoldColumn(false)]
        public override string SpecialServices { get; set; }

        [ScaffoldColumn(false)]
        public override string Block { get; set; }

        [ScaffoldColumn(false)]
        public override string Entrance { get; set; }

        [ScaffoldColumn(false)]
        public override string Staircase { get; set; }

        [ScaffoldColumn(false)]
        public override string Floor { get; set; }

        [ScaffoldColumn(false)]
        public override string Door { get; set; }
    }
}