namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using Maddalena;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [Display(Name = "lblPageHeader")]
    [Category(BulkUploadCategory.BasicInformation)]
    [DisplayName(SystemAreas.DynamicHistoricalInformation.Area)]
    [NavigationControllerName("DynamicHistoricalInformation")]
    [BulkUploadEntity(ModelLookup.CustomFormCategory, ScreenType = ModelScreenType.Employee)]
    public class EmployeeCustomFormDto : CustomFieldEntityDto<EmployeeCustomForm>, IEmployeeNumberEntity, ICustomFormDtoEntity
    {
        public EmployeeCustomFormDto()
        {
            this.CustomFields = new CustomFieldListDto<Models.Employees.EmployeeCustomForm>(() => new CustomFormProperties
            {
                CustomFormCategory = this.CustomFormCategoryCode
            });
        }

        [Key]
        public long CustomFormId { get; set; }

        [ODataNotEditable]
        [BulkUploadIgnore]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [ClientRequired(conditional: true)]
        [Display(Name = "lblEmpNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [Required]
        [BulkUploadLookup]
        [ODataNotEditable]
        [BulkUploadHidden]
        [Display(Order = 1)]
        public string CustomFormCategoryCode { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "lblEffectiveDate", Order = 3)]
        public DateTime? EffectiveDate { get; set; }

        [ScaffoldColumn(false)]
        public long? BureauCustomFormCategoryId { get; set; }

        [ScaffoldColumn(false)]
        public long? CompanyCustomFormCategoryId { get; set; }

        [BulkUploadHidden]
        [Display(Name = "lblParenCustomForm")]
        public long? ParentCustomFormId { get; set; }

        [ScaffoldColumn(false)]
        public long? CompanyRunId { get; set; }

        [BulkUploadLookup(OverrideLookup = "CompanyRun")]
        [CountryVisible(CountryCode.IE)]
        public string CompanyRun { get; set; }

        public CustomFormType CustomFormType { get; set; }
    }
}