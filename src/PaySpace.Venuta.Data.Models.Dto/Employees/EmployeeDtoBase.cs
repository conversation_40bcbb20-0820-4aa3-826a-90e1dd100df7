namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;

    using Maddalena;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    public abstract class EmployeeDtoBase : CustomFieldEntityDto<EmployeeDto>, ICloneable
    {
        [ClientRequired(conditional: true)]
        [MetadataInfo(MustBeUniqueValue = true)]
        [CountryStringLength(20)]
        [CountryStringLength(30, CountryCode.BR)]
        [Display(Name = "lblEmpNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [BulkUploadLookup]
        [ODataNotEditable]
        [ClientRequired(conditional: true)]
        [Display(Name = "CompanyFrequencyId", Order = 101)]
        [CompanyVisible(Code = CompanySettingCode.BasicProfile.CompanyFrequency)]
        public string CompanyFrequency { get; set; }

        [CountryNotRequired(CountryCode.CA, CountryCode.IE, CountryCode.MY, CountryCode.IN)]
        [Display(Name = "lblTitle", Order = 108)]
        [BulkUploadLookup(EnumType = typeof(EnumTitle))]
        public string Title { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? TitleId { get; set; }

        [Required]
        [CountryStringLength(100)]
        [CountryStringLength(maximumLength: 40, CountryCode.AU)]
        [Display(Name = "lblFirstName", Order = 102)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string FirstName { get; set; }

        [Required]
        [CountryStringLength(100)]
        [CountryStringLength(maximumLength: 40, CountryCode.AU)]
        [Display(Name = "lblLastName", Order = 103)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string LastName { get; set; }

        [Display(Name = "lblPrefName", Order = 106)]
        [CountryRequired(CountryCode.SG, CountryCode.MY, CountryCode.IN)]
        [CountryStringLength(100)]
        [CountryStringLength(150, CountryCode.SG, CountryCode.MY, CountryCode.IN)]
        public string PreferredName { get; set; }

        [StringLength(50)]
        [Display(Name = "lblMaidenName", Order = 107)]
        public string MaidenName { get; set; }

        [CountryStringLength(100)]
        [CountryStringLength(maximumLength: 40, CountryCode.AU)]
        [Display(Name = "lblMiddleName", Order = 104)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string MiddleName { get; set; }

        [CountryStringLength(maximumLength: 5)]
        [CountryStringLength(maximumLength: 10, CountryCode.IN)]
        [CountryNotRequired(CountryCode.CA, CountryCode.IE, CountryCode.MY, CountryCode.IN, CountryCode.AU)]
        [MetadataInfo(HasAdditionalValidation = true)]
        [Display(Name = "lblInitial", Order = 105)]
        public string Initials { get; set; }

        [MetadataInfo(MustBeUniqueValue = true, HasAdditionalValidation = true)]
        [StringLength(150)]
        [Display(Name = "lblEmailAdd", Order = 123)]
        public string Email { get; set; }

        [Required]
        [MetadataInfo(HasAdditionalValidation = true)]
        [DataType(DataType.Date)]
        [Display(Name = "lblBirthdate", Order = 118)]
        public DateTime? Birthday { get; set; }

        [Display(Name = "lblHomeNumber", Order = 119)]
        [StringLength(100)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string HomeNumber { get; set; }

        [Display(Name = "lblWorkNumber", Order = 120)]
        [StringLength(100)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string WorkNumber { get; set; }

        [Display(Name = "lblCellNumber", Order = 122)]
        [CountryStringLength(maximumLength: 100, MinimumLength = 10)]
        [MetadataInfo(HasAdditionalValidation = true)]
        public string CellNumber { get; set; }

        [Display(Name = "lblWorkEx", Order = 121)]
        [StringLength(6)]
        public string WorkExtension { get; set; }

        [CountryRequired(CountryCode.IE)]
        [BulkUploadLookup(EnumType = typeof(EnumLanguage))]
        [Display(Name = "lblLanguage", Order = 110)]
        public string Language { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? LanguageId { get; set; }

        [CountryNotRequired(CountryCode.IE)]
        [Display(Name = "lblGender", Order = 111)]
        [BulkUploadLookup(EnumType = typeof(EnumGender))]
        public string? Gender { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? GenderId { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumMaritalStatus))]
        [CountryRequired(CountryCode.MY)]
        [CompanyRequired(CompanySettingCode.BasicProfile.MaritalStatus)]
        [Display(Name = "lblMaritalStat", Order = 116)]
        public string MaritalStatus { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? MaritalStatusId { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumRace))]
        [CountryRequired(CountryCode.ZA, CountryCode.SG)]
        [Display(Name = "lblRace", Order = 112)]
        public string Race { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? RaceId { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumCountry))]
        [CountryNotRequired(CountryCode.IE)]
        [Display(Name = "lblNationality", Order = 113)]
        public string Nationality { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? NationalityId { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumCountry))]
        [Display(Name = "lblCitiz", Order = 114)]
        public string Citizenship { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? CitizenshipId { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumDisabledType))]
        [Display(Name = "lblDisabledT", Order = 115)]
        public string DisabledType { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? DisabledTypeId { get; set; }

        [Display(Name = "lblForeignNat", Order = 117)]
        public bool? ForeignNational { get; set; }

        [BulkUploadIgnore]
        [ODataNotEditable]
        [BulkUploadHidden]
        public DateTime? DateCreated { get; set; }

        [Display(Name = "lblEmergContName", Order = 124)]
        [StringLength(200)]
        public string EmergencyContactName { get; set; }

        [Display(Name = "lblEmergContNumber", Order = 125)]
        [StringLength(100)]
        public string EmergencyContactNumber { get; set; }

        [Display(Name = "lblEmergContAddress", Order = 126)]
        [StringLength(400)]
        public string EmergencyContactAddress { get; set; }

        [Display(Name = "lblIsEmpRetired", Order = 109)]
        public bool? IsRetired { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumUIFExemption))]
        [ClientRequired(conditional: true)]
        [CountryVisible(CountryCode.ZA)]
        [Display(Name = "lblUifexemption", Order = 127)]
        public string UifExemption { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int? UifExemptionId { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumSDLExemption))]
        [ClientRequired(true)]
        [SdlExemptionCountries]
        [Display(Name = "lblSdlexemption", Order = 128)]
        public string SdlExemption { get; set; }

        [BulkUploadIgnore]
        [BulkUploadHidden]
        [ClientDisplay(Client.NextGen)]
        [ODataReadOnly(HideInEmptySheet = true)]
        public int SdlExemptionId { get; set; }

        [Display(Name = "lblEtiExempt", Order = 130)]
        [CountryVisible(CountryCode.ZA)]
        public bool? EtiExempt { get; set; }

        [CompanyDisplayName(Code = CompanySettingCode.BasicProfile.CustomFieldOne)]
        [CompanyVisible(Code = CompanySettingCode.BasicProfile.CustomFieldOne)]
        [StringLength(100)]
        public string CustomFieldValue { get; set; }

        [CompanyDisplayName(Code = CompanySettingCode.BasicProfile.CustomFieldTwo)]
        [CompanyVisible(Code = CompanySettingCode.BasicProfile.CustomFieldTwo)]
        [StringLength(100)]
        public string CustomFieldValue2 { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblReportId")]
        public string DefaultPayslip { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        [ReadOnly(true)]
        public string? ImageDownloadUrl { get; set; }

        [BulkUploadHidden]
        [BulkUploadIgnore]
        [ReadOnly(true)]
        public long CompanyId { get; set; }

        [BulkUploadIgnore]
        public IList<EmployeeAddressDto> Address { get; set; } = new List<EmployeeAddressDto>();

        public override object Clone()
        {
            var employee = (EmployeeDtoBase)base.Clone();

            // MemberwiseClone does a shallow copy, the address in employee is references to the original. we need to copy the address separately
            employee.Address = this.Address.Select(_ => (EmployeeAddressDto)_.Clone()).ToList();

            return employee;
        }
    }
}