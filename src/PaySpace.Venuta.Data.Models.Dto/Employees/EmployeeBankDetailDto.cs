namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.BasicInformation)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.BankDetail.Area)]
    [NavigationControllerName("BankDetail")]
    public class EmployeeBankDetailDto : IEmployeeNumberEntity, ICloneable
    {
        private AccountType accountType;

        [Key]
        public long BankDetailId { get; set; }

        [BulkUploadHidden]
        [ODataNotEditable]
        [BulkUploadIgnore]
        public long EmployeeId { get; set; }

        [Required]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        [ODataNotEditable]
        [Display(Name = "lblEmpNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [Display(Name = "lblFullName", Order = 1)]
        public string FullName { get; set; }

        [Required]
        [Display(Name = "lblPaymentMethod", Order = 2)]
        [BulkUploadLookup(EnumType = typeof(EnumPaymentMethod))]
        public string PaymentMethod { get; set; }

        [Display(Name = "lblSplitBy", Order = 16)]
        public BankDetailSplitType? SplitType { get; set; }

        [ClientRequired(conditional: true)]
        [Display(Name = "lblBankAccountOwner", Order = 4)]
        public BankAccountOwnerType BankAccountOwner { get; set; }

        [Display(Name = "lblBankAccountOwnerName", Order = 5)]
        public string BankAccountOwnerName { get; set; }

        [Display(Name = "lblAccountType", Order = 6)]
        [ClientRequired(conditional: true)]
        public AccountType AccountType
        {
            get => Enum.IsDefined(this.accountType) ? this.accountType : AccountType.Cheque;
            set => this.accountType = Enum.IsDefined(value) ? value : AccountType.Cheque;
        }

        // Not required for creating a Cash/Cheque record - only required if EFT
        // Conditional Requirement moved to Fluent Validator.
        [BulkUploadLookup]
        [ClientRequired(conditional: true)]
        [Display(Name = "lblBankName", Order = 7)]
        public string BankName { get; set; }

        [ClientRequired(conditional: true)]
        [Display(Name = "lblBankBranchNo", Order = 8)]
        public string BankBranchNo { get; set; }

        [CountryVisible(CountryCode.BR)]
        [ClientRequired(conditional: true)]
        [Display(Name = "lblAgencyCheckDigit", Order = 9)]
        public string AgencyCheckDigit { get; set; }

        [ClientRequired(conditional: true)]
        [Display(Name = "lblBankAccountNo", Order = 10)]
        public string BankAccountNo { get; set; }

        [CountryVisible(CountryCode.BR)]
        [ClientRequired(conditional: true)]
        [Display(Name = "lblAccountCheckDigit", Order = 11)]
        public string AccountCheckDigit { get; set; }

        [Display(Name = "lblBankReference", Order = 12)]
        public string Reference { get; set; }

        [Display(Name = "lblBankSplitAmount", Order = 18)]
        public decimal? Amount { get; set; }

        [Display(Name = "lblBankAccountComments", Order = 13)]
        public string Comments { get; set; }

        [Display(Name = "lblBankAccountSwiftCode", Order = 14)]
        public string SwiftCode { get; set; }

        [Display(Name = "lblBankAccountRoutingCode", Order = 15)]
        public string RoutingCode { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblBankAccountComponent", Order = 19)]
        public string CompanyComponent { get; set; }

        [BulkUploadLookup(EnumType = typeof(EnumCurrency))]
        [Display(Name = "lblBankAccountCurrencyForPayment", Order = 20)]
        public string Currency { get; set; }

        [BulkUploadLookup]
        [Display(Name = "lblBankAccountEbdIndicatorId", Order = 16)]
        public string CompanyEdbIndicator { get; set; }

        [Display(Name = "lblSkipValidation", Order = 21)]
        [UserVisible(UserType.Agency, UserType.Bureau)]
        public bool SkipValidation { get; set; }

        [CountryVisible(CountryCode.BR)]
        public BankingKeyType? BankingKey { get; set; }

        [CountryVisible(CountryCode.BR)]
        public string CpfNumber { get; set; }

        [CountryVisible(CountryCode.BR)]
        public string CnpjNumber { get; set; }

        [CountryVisible(CountryCode.BR)]
        public string CellphoneNumber { get; set; }

        [CountryVisible(CountryCode.BR)]
        public string EmailAddress { get; set; }

        [CountryVisible(CountryCode.BR)]
        public string RandomKey { get; set; }

        [ScaffoldColumn(false)]
        public PaymentMethod PaymentMethodId { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}