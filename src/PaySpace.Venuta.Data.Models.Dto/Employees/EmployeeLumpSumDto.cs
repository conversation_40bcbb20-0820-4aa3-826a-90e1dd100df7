namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ModelLookup.Run, ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.PayrollProcessing)]
    [Display(Name = "PageHeader")]
    [SecurityDisplayName(SystemAreas.Payslip.Edit, SystemAreas.EmployeeLumpSum.Area)]
    [NavigationControllerName("PayslipEdit")]
    public class EmployeeLumpSumDto : IEmployeeNumberEntity, ICloneable
    {
        [Key]
        public long EmployeeLumpSumId { get; set; }

        [ScaffoldColumn(false)]
        public long PayslipId { get; set; }

        [ODataNotEditable]
        [BulkUploadIgnore]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [Required]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        [ODataNotEditable]
        [Display(Name = "EmployeeNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [Display(Name = "FullName", Order = 1)]
        public string FullName { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [Display(Name = "DirectiveIssuedDate", Order = 2)]
        public DateTime? DirectiveIssuedDate { get; set; }

        [Required]
        [BulkUploadLookup]
        [ODataNotEditable]
        [Display(Name = "TaxCode", Order = 3)]
        public string TaxCode { get; set; }

        [Required]
        [Display(Name = "DirectiveAmount", Order = 4)]
        public decimal? DirectiveAmount { get; set; }

        [Required]
        [Display(Name = "DirectiveTax", Order = 5)]
        public decimal? DirectiveTax { get; set; }

        [CountryRequired(CountryCode.ZA, CountryCode.SZ, CountryCode.NA)]
        [Display(Name = "DirectiveNumber", Order = 6)]
        public string DirectiveNumber { get; set; }

        [Display(Name = "ReferenceNumber", Order = 7)]
        public string ReferenceNumber { get; set; }

        [CountryRequired(CountryCode.NA)]
        [CountryVisible(CountryCode.NA)]
        [Display(Name = "TaxFreeDirectiveAmount", Order = 8)]
        public decimal? TaxFreeDirectiveAmount { get; set; }

        public object Clone()
        {
            return this.MemberwiseClone();
        }
    }
}