namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;
    using PaySpace.Venuta.Data.Models.Country;

    using PaySpace.Venuta.Infrastructure;

    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee, ShowByDefault = false)]
    [DisplayName(SystemAreas.YearEndReporting.Area)]
    [NavigationControllerName("YearEndReporting")]
    public class EmployeeAppendix8ADto : IEmployeeNumberEntity
    {
        [Key]
        public long EmployeeAppendix8AId { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.BasisYear)]
        [BulkUploadLookup(EnumType = typeof(CountryTaxYear), OverrideLookup = "TaxYear")]
        public string TaxYear { get; set; }

        [ODataNotEditable]
        public long CompanyId { get; set; }

        [ODataNotEditable]
        public string EmployeeNumber { get; set; }

        [ODataNotEditable]
        public long EmployeeId { get; set; }

        [ODataNotEditable]
        public int TaxYearId { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.OccupationPeriodStartDate)]
        [DataType(DataType.Date)]
        public DateTime? OccupationPeriodStartDate { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.OccupationPeriodEndDate)]
        [DataType(DataType.Date)]
        public DateTime? OccupationPeriodEndDate { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.NumberOfDays)]
        public int? NumberOfDays { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.AddressLine1)]
        public string AddressLine1 { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.AddressLine2)]
        public string AddressLine2 { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.AddressLine3)]
        public string AddressLine3 { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.NumberOfEmployeesSharing)]
        public int? NumberOfEmployeesSharing { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.AnnualValuePremises)]
        public decimal? AnnualValuePremises { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.FurnitureFittingOption)]
        public string FurnitureFittingOption { get; set; } // "P" or "F"

        [Display(Name = SystemAreas.YearEndReporting.Keys.ValueFurnitureFitting)]
        public decimal? ValueFurnitureFitting { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.RentPaidToLandlord)]
        public decimal? RentPaidToLandlord { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.TaxableValuePlaceOfResidence)]
        public decimal? TaxableValuePlaceOfResidence { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.RentPaidByEmployee)]
        public decimal? RentPaidByEmployee { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.TotalTaxableValuePlaceOfResidence)]
        public decimal? TotalTaxableValuePlaceOfResidence { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.UtilitiesCosts)]
        public decimal? UtilitiesCosts { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.DriverCosts)]
        public decimal? DriverCosts { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.ServantCosts)]
        public decimal? ServantCosts { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.TaxableValueUtilitiesHousekeeping)]
        public decimal? TaxableValueUtilitiesHousekeeping { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.CostHotelAccommodation)]
        public decimal? CostHotelAccommodation { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.HotelAmountPaidByEmployee)]
        public decimal? HotelAmountPaidByEmployee { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.TaxableValueHotelAccommodation)]
        public decimal? TaxableValueHotelAccommodation { get; set; }

        [Display(Name = SystemAreas.YearEndReporting.Keys.TotalAccommodationBenefit)]
        public decimal? TotalAccommodationBenefit { get; set; }

        [BulkUploadLookup(EnumType = typeof(CountryTaxYear), OverrideLookup = "TaxYear")]
        public string TaxYearLabel { get; set; }

        public DateTime TaxYearStartDate { get; set; }

        public DateTime TaxYearEndDate { get; set; }
    }
}
