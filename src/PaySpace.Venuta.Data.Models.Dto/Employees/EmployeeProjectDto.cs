namespace PaySpace.Venuta.Data.Models.Dto.Employees
{
    using System;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [BulkUploadEntity(ScreenType = ModelScreenType.Employee)]
    [Category(BulkUploadCategory.Costing)]
    [Display(Name = "lblPageHeader")]
    [DisplayName(SystemAreas.Project.Area)]
    [NavigationControllerName("Project")]
    public class EmployeeProjectDto : CustomFieldEntityDto<EmployeeProjectDto>, IEmployeeNumberEntity
    {
        [Key]
        public long EmployeeProjectId { get; set; }

        [ODataNotEditable]
        [BulkUploadIgnore]
        [BulkUploadHidden]
        public long EmployeeId { get; set; }

        [Required]
        [BulkUploadLookup(DisableInExcel = true, ShowLookupSheetInExcel = true)]
        [ODataNotEditable]
        [Display(Name = "lblEmpNumber", Order = 0)]
        public string EmployeeNumber { get; set; }

        [ODataReadOnly(HideInEmptySheet = true)]
        [Display(Name = "FullName", Order = 1)]
        public string FullName { get; set; }

        [BulkUploadLookup]
        [Required]
        [Display(Name = "lblProject", Order = 3)]
        public string ProjectCode { get; set; }

        [ODataReadOnly]
        [BulkUploadHidden]
        [BulkUploadIgnore]
        public string ProjectDescription { get; set; }

        [BulkUploadLookup(nameof(ProjectCode))]
        [Display(Name = "lblActivity", Order = 4)]
        public string ProjectActivityCode { get; set; }

        [DataType(DataType.Date)]
        [Required]
        [Display(Name = "lblEffectiveDate", Order = 2)]
        public DateTime? EffectiveDate { get; set; }

        [DataType(DataType.Date)]
        [Display(Name = "lblEndDate", Order = 6)]
        public DateTime? EndDate { get; set; }

        [Display(Name = "lblProjectBased", Order = 5)]
        public bool? ProjectBased { get; set; }

        [Display(Name = "lblReminderOption", Order = 7)]
        public ReminderOption? ReminderOption { get; set; }

        [Display(Name = "lblPaidbyInvoice", Order = 8)]
        public bool? PaidByInvoice { get; set; }

        [Display(Name = "lblFrequency", Order = 9)]
        [CompanyVisible(Code = CompanySettingCode.Project.ShowPayRate)]
        public PayFrequency? PayFrequency { get; set; }

        [Display(Name = "lblProjectPayRate", Order = 10)]
        [CompanyVisible(Code = CompanySettingCode.Project.ShowPayRate)]
        public decimal? ProjectPayRate { get; set; }

        [Display(Name = "lblHoursPerDay", Order = 11)]
        [CompanyVisible(Code = CompanySettingCode.Project.ShowPayRate)]
        public decimal? HoursPerDay { get; set; }

        [Display(Name = "lblDaysPerMonth", Order = 12)]
        [CompanyVisible(Code = CompanySettingCode.Project.ShowPayRate)]
        public decimal? DaysPerMonth { get; set; }

        [Display(Name = "lblComments", Order = 13)]
        public string Comments { get; set; }
    }
}