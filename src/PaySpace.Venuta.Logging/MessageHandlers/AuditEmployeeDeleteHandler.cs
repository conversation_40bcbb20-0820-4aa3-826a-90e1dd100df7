namespace PaySpace.Venuta.Logging.MessageHandlers
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Messages;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Message;

    internal class AuditEmployeeDeleteHandler : MessageHandlerBase<AuditEmployeeDeleteMessage>
    {
        private readonly IServiceScopeFactory scopeFactory;

        public AuditEmployeeDeleteHandler(IServiceScopeFactory scopeFactory)
        {
            this.scopeFactory = scopeFactory;
        }

        public override string SubscriptionName => "audit_log";

        public override TimeSpan AutoDeleteOnIdle => TimeSpan.MaxValue;

        public override TimeSpan DefaultMessageTimeToLive => TimeSpan.MaxValue;

        protected override async Task<bool> ProcessMessageAsync(AuditEmployeeDeleteMessage message, CancellationToken cancellationToken)
        {
            using (var scope = this.scopeFactory.CreateScope())
            {
                var auditService = scope.ServiceProvider.GetService<IAuditTrailService>();
                var headerService = scope.ServiceProvider.GetService<IAuditTrailHeaderService>();

                var tableName = auditService.GetTableName(typeof(Company));

                var headerId = await headerService.GetHeaderIdAsync(tableName);
                if (headerId == null)
                {
                    return false;
                }

                await auditService.AddAuditTrailAsync(
                    new AuditTrail
                    {
                        AuditTrailHeaderId = headerId.Value,
                        AuditAction = AuditActionType.Delete.ToString(),
                        UserId = message.UserId,
                        CompanyId = message.CompanyId,
                        OldValue = message.EmployeeDetails,
                        AliasName = "employee delete",
                        NewValue = string.Empty,
                        DateOccured = message.Timestamp
                    }, cancellationToken);
            }

            return true;
        }
    }
}