namespace PaySpace.Venuta.Logging.MessageHandlers
{
    using System;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Logging;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.Factories;
    using PaySpace.Venuta.Messaging;

    public class AuditMessageHandler : MessageHandlerBase<AuditLogMessage>
    {
        private readonly ILogger logger;
        private readonly IServiceScopeFactory scopeFactory;

        public AuditMessageHandler(ILogger<AuditMessageHandler> logger, IServiceScopeFactory scopeFactory)
        {
            this.logger = logger;
            this.scopeFactory = scopeFactory;
        }

        public override string SubscriptionName { get; } = "audit_log";

        public override int MaxConcurrentCalls => 1;

        protected override async Task<bool> ProcessMessageAsync(AuditLogMessage message, CancellationToken cancellationToken)
        {
            if (message.UserId == default)
            {
                return true;
            }

            using (var scope = this.scopeFactory.CreateScope())
            {
                var auditHandlerFactory = scope.ServiceProvider.GetRequiredService<IAuditHandlerFactory>();

                var handler = auditHandlerFactory.GetHandler(message.ActionType);
                if (handler == null)
                {
                    this.logger.LogError("Unable to handle message for '{EntityType}'. Action: '{ActionType}'.", message.Entity.EntityType, message.ActionType);
                    return false;
                }

                this.SetCultureInfo();

                try
                {
                    await handler.LogDataAsync(message.Entity, cancellationToken);
                }
                catch (Exception e)
                {
                    this.logger.LogError(e, "Unable to handle message for '{EntityType}'. Action: '{ActionType}'.", message.Entity.EntityType, message.ActionType);
                    return false;
                }
            }

            return true;
        }

        private void SetCultureInfo()
        {
            CultureInfo.CurrentCulture = CultureData.DefaultCulture;
            CultureInfo.CurrentUICulture = CultureData.DefaultCulture;

            if (CultureData.ShouldOverrideDecimalSeperator())
            {
                var culture = new CultureInfo(CultureInfo.CurrentCulture.Name);
                culture.NumberFormat.NumberDecimalSeparator = ".";

                CultureInfo.CurrentCulture = culture;
                CultureInfo.CurrentUICulture = culture;
            }
        }
    }
}