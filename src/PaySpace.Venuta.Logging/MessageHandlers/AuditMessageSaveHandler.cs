namespace PaySpace.Venuta.Logging.MessageHandlers
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;

    using Mapster;

    using MassTransit;

    using Microsoft.Extensions.Logging;

    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Messaging;

    public class AuditMessageSaveHandler : MessageHandlerBase<AuditSaveMessage>
    {
        private readonly ILogger<AuditMessageSaveHandler> logger;

        public AuditMessageSaveHandler(ILogger<AuditMessageSaveHandler> logger)
        {
            this.logger = logger;
        }

        public override string SubscriptionName => "save_audit";

        public override async Task Consume(ConsumeContext<AuditSaveMessage> context)
        {
            try
            {
                if (!context.Message.IsSaved)
                {
                    var auditTrailService = context.GetServiceOrCreateInstance<IAuditTrailService>();
                    var audits = context.Message.AuditTrails.Adapt<AuditTrail[]>();
                    await auditTrailService.AddAuditTrailsAsync(audits, context.CancellationToken);
                }
            }
            catch (Exception e)
            {
                this.logger.LogError(e, "Error Saving Audit Trail");
                throw;
            }
        }

        protected override Task<bool> ProcessMessageAsync(AuditSaveMessage message, CancellationToken cancellationToken)
        {
            throw new System.NotImplementedException();
        }
    }
}