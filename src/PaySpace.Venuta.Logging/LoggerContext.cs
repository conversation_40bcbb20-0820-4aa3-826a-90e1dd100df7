namespace PaySpace.Venuta.Logging
{
    using System;
    using System.Linq;
    using System.Reflection;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Agency;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Data.Models.Bureau;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Country.Brazil;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Data.Models.Security;
    using PaySpace.Venuta.Data.Models.Tax;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Data.Models.Workflow;
    using PaySpace.Venuta.Modules.Component.SubCodes.Abstractions.Models;
    using PaySpace.Venuta.Modules.EmploymentStability.Abstractions.Models;
    using PaySpace.Venuta.Modules.HmrcPaymentRecord.Abstractions.Models;
    using PaySpace.Venuta.Modules.PensionEnrolment.Abstractions.Models;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions.Models;

    public class LoggerContext : DbContext
    {
        public LoggerContext(DbContextOptions<LoggerContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>();

            modelBuilder.Entity<AuditTrail>();
            modelBuilder.Entity<AuditTrailHeader>();

            modelBuilder.Entity<PayslipHeader>();
            modelBuilder.Entity<PayslipLine>();

            modelBuilder.Entity<CompanyExternalQuickLink>();

            modelBuilder.Entity<CompanyWebhookConfig>();
            modelBuilder.Entity<CompanyWebhookSecret>();

            modelBuilder.Entity<CompanyWorkflowRole>();

            modelBuilder.Entity<BureauReleaseNoteAudience>();
            modelBuilder.Entity<BureauReleaseNote>();
            modelBuilder.Entity<EnumTaxCountry>();

            // needed to get values for auditing && resolving table names
            modelBuilder.Entity<CostingProjectActivity>();
            modelBuilder.Entity<OrganizationGroup>();
            modelBuilder.Entity<OrganizationLevel>();
            modelBuilder.Entity<CompanyAttachmentClassification>();
            modelBuilder.Entity<CompanyTrainingCourse>();
            modelBuilder.Entity<ComponentEmployee>();
            modelBuilder.Entity<ComponentCompany>();
            modelBuilder.Entity<BulkCaptureCode>();
            modelBuilder.Entity<EmployeeCustomForm>();
            modelBuilder.Entity<EmployeeAsset>();
            modelBuilder.Entity<EmployeeReviewKpa>();
            modelBuilder.Entity<EmployeeReviewKpaDetail>();
            modelBuilder.Entity<CompanyCustomExplanationLink>();
            modelBuilder.Entity<EmployeeTraining>();
            modelBuilder.Entity<EmployeeJournal>();
            modelBuilder.Entity<EmployeePayRate>();
            modelBuilder.Entity<EmployeeProject>();
            modelBuilder.Entity<EmployeeEmploymentStatus>();
            modelBuilder.Entity<EmployeeDependant>();
            modelBuilder.Entity<EmployeeDependantHistory>();
            modelBuilder.Entity<EmployeeAttachment>()
                .Ignore(_ => _.Attachment);
            modelBuilder.Entity<ComponentValues>();
            modelBuilder.Entity<EmployeeLeaveAdjustment>();
            modelBuilder.Entity<EmployeeLeaveSetup>();
            modelBuilder.Entity<CompanyLeaveScheme>();
            modelBuilder.Entity<OrganizationPosition>()
                .HasMany(_ => _.OrganizationPositionDetails).WithOne(_ => _.OrganizationPosition);
            modelBuilder.Entity<OrganizationPositionDetail>();
            modelBuilder.Entity<CboCode>();
            modelBuilder.Entity<OrganizationCategory>();
            modelBuilder.Entity<CompanyCategoryField>();
            modelBuilder.Entity<CompanyCategoryFieldValue>();
            modelBuilder.Entity<EmployeeBankDetail>();
            modelBuilder.Entity<CompanyEbdIndicator>();
            modelBuilder.Entity<EmployeeBankHeader>();
            modelBuilder.Entity<CompanyRun>();
            modelBuilder.Entity<CompanyEmployeeNumber>();
            modelBuilder.Entity<EmployeePosition>();
            modelBuilder.Entity<Address>();
            modelBuilder.Entity<EmployeeSuspension>();
            modelBuilder.Entity<EmployeeNote>();
            modelBuilder.Entity<EmployeeNoteRecurring>();
            modelBuilder.Entity<EmployeeLumpSum>();
            modelBuilder.Entity<EmployeeWorkflowSetting>();
            modelBuilder.Entity<EmployeeQualification>();
            modelBuilder.Entity<CompanyQualification>();
            modelBuilder.Entity<EmployeeEtiTakeOn>();
            modelBuilder.Entity<Agency>();
            modelBuilder.Entity<CompanyTheme>();
            modelBuilder.Entity<AgencyBankDetail>();
            modelBuilder.Entity<BureauBillingCountry>();
            modelBuilder.Entity<CompanyPensionEnrolment>();
            modelBuilder.Entity<EmployeePensionEnrolment>();
            modelBuilder.Entity<BureauUserCountryPermission>();
            modelBuilder.Entity<BureauSuspension>();

            // Company Employment Equity
            modelBuilder.Entity<EquityPlan>();
            modelBuilder.Entity<EquityPlanPeriod>()
                .HasOne(_ => _.EquityPlan)
                .WithMany(_ => _.EquityPlanPeriods)
                .HasForeignKey(_ => _.EquityPlanId);
            modelBuilder.Entity<EquityValue>()
                .HasKey(_ => new { _.EquityPlanPeriodId, _.OccupationalLevelId, _.GenderId, _.RaceId });
            modelBuilder.Entity<EquityValue>()
                .HasOne(_ => _.EquityPlanPeriod)
                .WithMany(_ => _.EquityPlanValues)
                .HasForeignKey(_ => _.EquityPlanPeriodId);

            modelBuilder.Entity<CompanySkillCategory>();
            modelBuilder.Entity<CompanySkill>()
                .HasOne(_ => _.CompanySkillCategory)
                .WithMany(_ => _.CompanySkills)
                .HasForeignKey(_ => new { _.CompanySkillCategoryCode, _.CompanyId })
                .HasPrincipalKey(_ => new { _.CompanySkillCategoryCode, _.CompanyId });
            modelBuilder.Entity<EmployeeSkill>()
                .Property(_ => _.CompetencyId)
                .HasConversion<int?>();

            modelBuilder.Entity<EmployeeTakeOn>()
                .HasKey(_ => new { _.ComponentEmployeeId, _.CompanyRunId });

            // Register entities needed to audit job management
            modelBuilder.Entity<CompanyJobManagement>();
            modelBuilder.Entity<CompanyEmploymentSubCategory>();
            modelBuilder.Entity<OrganizationRegion>();
            modelBuilder.Entity<CompanyBudgetGroup>();
            modelBuilder.Entity<OrganizationGrade>();
            modelBuilder.Entity<CompanyGradeField>();
            modelBuilder.Entity<CompanyGradeFieldValue>();
            modelBuilder.Entity<CompanyRoster>();
            modelBuilder.Entity<CompanyRosterSchedule>();
            modelBuilder.Entity<CompanyGl>();
            modelBuilder.Entity<EnumOccupationalLevel>();
            modelBuilder.Entity<EnumLocalization>();
            modelBuilder.Entity<EnumOfoLevel>();
            modelBuilder.Entity<EnumPositionType>();
            modelBuilder.Entity<EnumRace>();

            // Review KPA's and Defaults
            modelBuilder.Entity<CompanyReviewProcess>();
            modelBuilder.Entity<CompanyProcessType>();
            modelBuilder.Entity<EmployeeReviewKpa>();
            modelBuilder.Entity<EmployeeReviewKpaDetail>();
            modelBuilder.Entity<CompanyCustomExplanationLink>();
            modelBuilder.Entity<CompanyTemplateSectionHeader>();
            modelBuilder.Entity<CompanyTemplateSection>();
            modelBuilder.Entity<EmployeeReviewDefaults>();
            modelBuilder.Entity<EmployeeReviewDefRaters>();
            modelBuilder.Entity<EmployeeReviewHeader>();
            modelBuilder.Entity<EmployeeReviewTemplate>();

            // Recurring components
            modelBuilder.Entity<ComponentEmployee>();
            modelBuilder.Entity<EmployeeBonusProvision>();
            modelBuilder.Entity<EmployeeCompanyCarDetail>();
            modelBuilder.Entity<EmployeeDisability>();
            modelBuilder.Entity<EmployeeHousePaymentDetail>();
            modelBuilder.Entity<EmployeeFinancialHousePayment>();
            modelBuilder.Entity<EmployeeGarnishee>();
            modelBuilder.Entity<EmployeeGroupLife>();
            modelBuilder.Entity<EmployeeIncomeProtection>();
            modelBuilder.Entity<EmployeeLoan>();
            modelBuilder.Entity<EmployeeMultiContractWork>();
            modelBuilder.Entity<EmployeeMedical>();
            modelBuilder.Entity<EmployeeAlimony>();
            modelBuilder.Entity<EmployeeTableBuilder>();
            modelBuilder.Entity<EmployeePayslipTakeOn>();

            // Company Sub Codes
            modelBuilder.Entity<ComponentCalcException>();
            modelBuilder.Entity<ComponentCalcExceptionDetail>();
            modelBuilder.Entity<BureauTaxSubCode>();
            modelBuilder.Entity<ComponentBureau>();

            // Cloud Room
            modelBuilder.Entity<CompanyCloudRoom>();

            // Public Holidays
            modelBuilder.Entity<PublicHoliday>();
            modelBuilder.Entity<PublicHolidayProvinces>();
            modelBuilder.Entity<PublicHolidayCategory>();
            modelBuilder.Entity<CompanyPublicHoliday>();
            modelBuilder.Entity<PublicHolidayMunicipality>();

            modelBuilder.Entity<CompanyGLDetail>();

            var companyMedical = modelBuilder.Entity<CompanyMedicalAid>();
            companyMedical.Property(_ => _.MedicalAidId).HasConversion(this.IntToLongConverter());

            modelBuilder.Entity<BureauMedicalAid>();
            modelBuilder.Entity<BureauMedicalAidLine>()
                .HasKey(t => new { t.MedicalAidId, t.EffectiveDate });
            modelBuilder.Entity<EmployeePensionFund>();
            modelBuilder.Entity<EmployeeRetirementAnnuity>();
            modelBuilder.Entity<EmployeeSaving>();
            modelBuilder.Entity<EmployeeTravelBusinessUsage>();

            var employeeUnion = modelBuilder.Entity<EmployeeUnion>();
            employeeUnion.Property(_ => _.UnionId).HasConversion(this.IntToLongConverter());

            modelBuilder.Entity<CompanyGroupLifeLink>();

            var employee = modelBuilder.Entity<Employee>();
            employee.HasMany(_ => _.EmploymentStatuses).WithOne(_ => _.Employee);
            employee.HasMany(_ => _.EmployeePositions).WithOne(_ => _.Employee);
            employee.HasOne(_ => _.EmployeeWithPosition).WithOne(_ => _.Employee).HasForeignKey<EmployeeWithPosition>(_ => _.EmployeeId);
            employee.HasMany(_ => _.EmployeeHistory).WithOne(_ => _.Employee).HasForeignKey(_ => _.EmployeeId);

            modelBuilder.Entity<EmployeeHistory>().HasOne(_ => _.Employee).WithMany(_ => _.EmployeeHistory).HasForeignKey(_ => _.EmployeeId);
            modelBuilder.Entity<EmployeeAddressHistory>();

            var models = Assembly.Load("PaySpace.Venuta.Data.Models");
            var enums = models.GetTypes().Where(type => !type.IsInterface && !type.IsAbstract && type.Name.StartsWith("Enum"));
            foreach (var type in enums)
            {
                // Specific configuration for EnumCompanySettingType
                if (type == typeof(EnumCompanySettingType))
                {
                    ModelBuilderHelper.BuildCompanySettingType(modelBuilder);
                }
                else
                {
                    modelBuilder.Entity(type);
                }
            }

            // Custom Fields
            modelBuilder.Entity<EmployeeAssetCustomFieldValue>();
            modelBuilder.Entity<EmployeeAttachmentCustomFieldValue>();
            modelBuilder.Entity<EmployeeClaimItemsCustomFieldValue>();
            modelBuilder.Entity<EmployeeCustomFieldValue>();
            modelBuilder.Entity<EmployeeDependantCustomFieldValue>();
            modelBuilder.Entity<EmployeeDependantHistoryCustomFieldValue>();
            modelBuilder.Entity<EmployeeEmploymentStatusCustomFieldValue>();
            modelBuilder.Entity<EmployeePositionCustomFieldValue>();
            modelBuilder.Entity<EmployeeSuspensionCustomFieldValue>();
            modelBuilder.Entity<EmployeeTrainingCustomFieldValue>();
            modelBuilder.Entity<EmployeePayRateCustomFieldValue>();
            modelBuilder.Entity<EmployeeLeaveAdjustmentCustomFieldValue>();
            modelBuilder.Entity<EmployeeSkillCustomFieldValue>();
            modelBuilder.Entity<EmployeeQualificationCustomFieldValue>();
            modelBuilder.Entity<TableBuilderCustomFieldValue>();
            modelBuilder.Entity<CustomFieldOption>();

            modelBuilder.Entity<CustomFieldFormField>()
                .HasKey(_ => new { _.CustomFieldId, _.CustomFieldType });

            modelBuilder.Entity<SecurityGroup>();
            modelBuilder.Entity<StabilityRule>();
            modelBuilder.Entity<TableBuilder>();
            modelBuilder.Entity<TableBuilderStabilityRule>();
            modelBuilder.Entity<CompanyRunFrequency>();
            modelBuilder.Entity<EmployeeIncident>();
            modelBuilder.Entity<EmployeeLeaveSetupEntitlement>();

            // Country Tax Year Detail
            modelBuilder.Entity<CountryTaxYear>();
            modelBuilder.Entity<CountryTaxYearRateDetail>();
            modelBuilder.Entity<CountryTaxYearBracketDetail>();

            modelBuilder.Entity<TaxCode>();
            modelBuilder.Entity<TaxCodeIRP5Action>();
            modelBuilder.Entity<TaxCodeTaxabilityOverride>();

            modelBuilder.Entity<LegislationOverride>();
            modelBuilder.Entity<WeekTemplateRosterLink>();
            modelBuilder.Entity<CompanyTAShiftType>();

            modelBuilder.Entity<EmployeeRecurringCostingSplitHeader>()
                .HasMany(_ => _.RecurringCostingSplitDetails).WithOne(_ => _.RecurringCostingSplitHeader);
            modelBuilder.Entity<EmployeeRecurringCostingSplitDetail>();
        }

        private ValueConverter<int?, long?> IntToLongConverter()
        {
            return new ValueConverter<int?, long?>(
                v => v == null ? null : (long?)Convert.ToInt64(v),
                v => v == null ? null : (int?)Convert.ToInt32(v));
        }
    }
}