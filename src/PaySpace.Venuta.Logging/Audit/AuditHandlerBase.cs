namespace PaySpace.Venuta.Logging.Audit
{
    using System.Collections.Generic;
    using System.IO;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.Extensions.Localization;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Factories;
    using PaySpace.Venuta.Logging.Audit.Models;

    internal abstract class AuditHandlerBase
    {
        private static readonly JsonSerializer Serializer = JsonSerializer.Create(new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, DateFormatHandling = DateFormatHandling.IsoDateFormat });

        private readonly IStringLocalizerFactory stringLocalizerFactory;
        private readonly IAuditTrailService auditService;
        private readonly IAuditTrailHeaderService headerService;
        private readonly IAuditInfoBuilderFactory builderFactory;

        protected AuditHandlerBase(
            IStringLocalizerFactory stringLocalizerFactory,
            IAuditTrailService auditService,
            IAuditTrailHeaderService headerService,
            IAuditInfoBuilderFactory builderFactory)
        {
            this.stringLocalizerFactory = stringLocalizerFactory;
            this.auditService = auditService;
            this.headerService = headerService;
            this.builderFactory = builderFactory;
        }

        protected string? GetLocalizedAuditValues(string tableName, object auditingValues, string prefix)
        {
            if (auditingValues == null)
            {
                return null;
            }

            var localizer = this.stringLocalizerFactory.Create($"{prefix}.{tableName}", null);
            return LocalizerHelper.LocalizeAudit(localizer, auditingValues);
        }

        protected async Task<long?> GetHeaderIdAsync(string? tableName)
        {
            if (string.IsNullOrEmpty(tableName))
            {
                return null;
            }

            return await this.headerService.GetHeaderIdAsync(tableName);
        }

        protected Task SaveAuditTrailAsync(AuditTrail auditTrail, CancellationToken cancellationToken)
        {
            return this.auditService.AddAuditTrailAsync(auditTrail, cancellationToken);
        }

        protected Task SaveAuditTrailsAsync(IEnumerable<AuditTrail> auditTrails, CancellationToken cancellationToken)
        {
            return this.auditService.AddAuditTrailsAsync(auditTrails, cancellationToken);
        }

        protected Task<AuditInfo?> GetAuditInfoAsync(AuditEntity entity, AuditActionType actionType)
        {
            var builder = this.builderFactory.Get(entity.EntityType);
            if (builder == null)
            {
                return Task.FromResult((AuditInfo?)null);
            }

            return builder.GetAuditInfoAsync(entity, actionType);
        }

        protected string? SerializeEntity(object value)
        {
            if (value == null)
            {
                return null;
            }

            var sb = new StringBuilder();
            using (var writer = new StringWriter(sb))
            {
                Serializer.Serialize(writer, value);
                return sb.ToString();
            }
        }
    }
}