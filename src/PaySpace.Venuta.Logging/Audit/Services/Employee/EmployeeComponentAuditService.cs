namespace PaySpace.Venuta.Logging.Audit.Services.Employee
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class EmployeeComponentAuditService : EmployeeAuditService
    {
        private readonly ReadOnlyContext readOnlyContext;
        private const int PayslipHeaderId = 6;

        public EmployeeComponentAuditService(ReadOnlyContext readOnlyContext)
            : base(readOnlyContext)
        {
            this.readOnlyContext = readOnlyContext;
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            var query = from audit in this.readOnlyContext.Set<AuditTrail>().AsNoTracking().TagWithSource()
                join ce in this.readOnlyContext.Set<ComponentEmployee>()
                        .Where(_ => _.EmployeeId == searchRequest.EmployeeId && _.ComponentId == searchRequest.EmployeeComponentId)
                    on audit.EmployeeComponentId equals ce.ComponentId
                where audit.EmployeeComponentId == searchRequest.EmployeeComponentId && audit.AuditTrailHeaderId != PayslipHeaderId
                select audit;

            return Task.FromResult(query);
        }

        protected override bool IsParametersValid(AuditSearchRequest searchRequest)
        {
            if (!base.IsParametersValid(searchRequest))
            {
                return false;
            }

            return searchRequest.EmployeeComponentId > 0;
        }
    }
}