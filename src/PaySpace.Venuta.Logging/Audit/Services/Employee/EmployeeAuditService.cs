namespace PaySpace.Venuta.Logging.Audit.Services.Employee
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Logging.Abstractions;

    internal class EmployeeAuditService : AuditSearchService
    {
        private readonly ReadOnlyContext readOnlyContext;

        public EmployeeAuditService(ReadOnlyContext readOnlyContext)
        {
            this.readOnlyContext = readOnlyContext;
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            return this.GetBaseAuditQuery(searchRequest)
                .ForAlternativeId(searchRequest)
                .ForAreaAndControllersAsync(this.readOnlyContext, searchRequest);
        }

        protected override bool IsParametersValid(AuditSearchRequest searchRequest)
        {
            return searchRequest.EmployeeId is not (null or 0);
        }

        protected IQueryable<AuditTrail> GetBaseAuditQuery(AuditSearchRequest searchRequest)
        {
            return this.readOnlyContext.Set<AuditTrail>()
                .AsNoTracking()
                .TagWithSource()
                .Where(_ => _.EmployeeId == searchRequest.EmployeeId);
        }
    }
}