namespace PaySpace.Venuta.Logging.Audit.Services.Employee
{
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;

    internal sealed class EmployeePayslipAuditService : EmployeeAuditService
    {
        public EmployeePayslipAuditService(ReadOnlyContext readOnlyContext)
            : base(readOnlyContext)
        {
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            var showDeletedPayslips = searchRequest.Controller == PaySpaceConstants.PayslipsController;

            var query = this.GetBaseAuditQuery(searchRequest)
                .Where(_ => (searchRequest.PayslipId.HasValue && _.PayslipId == searchRequest.PayslipId)
                            || (_.PayslipId == 0 && _.EmployeeId == searchRequest.EmployeeId && _.CompanyRunId == searchRequest.CompanyRunId)
                            || _.AuditTrailHeader.Controller == searchRequest.Controller
                            || (showDeletedPayslips
                                && _.AuditTrailHeader.Area == searchRequest.Area
                                && _.AuditTrailHeader.Controller == PaySpaceConstants.PayslipEditController
                                && _.AuditAction == AuditActionType.Delete.ToString()));

            return Task.FromResult(query);
        }

        protected override bool IsParametersValid(AuditSearchRequest searchRequest)
        {
            if (!base.IsParametersValid(searchRequest))
            {
                return false;
            }

            return searchRequest.PayslipId > 0 || searchRequest.Controller == PaySpaceConstants.PayslipsController;
        }
    }
}