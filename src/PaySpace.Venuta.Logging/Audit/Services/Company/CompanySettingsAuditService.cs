namespace PaySpace.Venuta.Logging.Audit.Services.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanySettingsAuditService : CompanyAuditService
    {
        private readonly ReadOnlyContext readOnlyContext;

        public CompanySettingsAuditService(ReadOnlyContext readOnlyContext)
            : base(readOnlyContext)
        {
            this.readOnlyContext = readOnlyContext;
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            return this.GetBaseAuditQuery(searchRequest)
                .ForAreaAndControllersAsync(this.readOnlyContext, searchRequest);
        }

        protected override bool IsParametersValid(AuditSearchRequest searchRequest)
        {
            return searchRequest.CompanyId is not (null or 0);
        }

        protected override IQueryable<AuditTrail> GetBaseAuditQuery(AuditSearchRequest searchRequest)
        {
            var query = base.GetBaseAuditQuery(searchRequest);

            return from at in query
                   join cst in this.readOnlyContext.Set<EnumCompanySettingType>()
                       on at.AlternativeId equals cst.SettingTypeId into cstJoin
                   from enumCompanySettingType in cstJoin.DefaultIfEmpty()
                   where at.CompanyId == searchRequest.CompanyId
                         && (enumCompanySettingType.Screen == searchRequest.Action || (searchRequest.Action == CompanySettingConstants.CompanyGeneralSettingsController && enumCompanySettingType == null))
                   select at;
        }
    }
}