namespace PaySpace.Venuta.Logging.Audit.Services.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Logging.Abstractions;

    internal class CompanyAuditService : AuditSearchService
    {
        private readonly ReadOnlyContext readOnlyContext;

        public CompanyAuditService(ReadOnlyContext readOnlyContext)
        {
            this.readOnlyContext = readOnlyContext;
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            return this.GetBaseAuditQuery(searchRequest)
                .ForCompanyGroupOrAgency(searchRequest)
                .ForAlternativeId(searchRequest)
                .ForAreaAndControllersAsync(this.readOnlyContext, searchRequest);
        }

        protected override bool IsParametersValid(AuditSearchRequest searchRequest)
        {
            return searchRequest.CompanyId is not (null or 0);
        }

        protected virtual IQueryable<AuditTrail> GetBaseAuditQuery(AuditSearchRequest searchRequest)
        {
            return this.readOnlyContext.Set<AuditTrail>()
                .AsNoTracking()
                .TagWithSource();
        }
    }
}