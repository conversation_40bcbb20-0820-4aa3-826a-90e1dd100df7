namespace PaySpace.Venuta.Logging.Audit.Services.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyComponentAuditService : CompanyAuditService
    {
        private readonly ReadOnlyContext readOnlyContext;

        public CompanyComponentAuditService(ReadOnlyContext readOnlyContext)
            : base(readOnlyContext)
        {
            this.readOnlyContext = readOnlyContext;
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            return Task.FromResult(this.GetBaseAuditQuery(searchRequest));
        }

        protected override Task<bool> IsParametersValidAsync(AuditSearchRequest searchRequest)
        {
            if (searchRequest.CompanyComponentId is null or 0 || searchRequest.Controller != PaySpaceConstants.CompanyComponentsController)
            {
                return Task.FromResult(false);
            }

            return this.readOnlyContext.Set<ComponentCompany>()
                .AnyAsync(_ => _.ComponentId == searchRequest.CompanyComponentId && _.CompanyFrequency.CompanyId == searchRequest.CompanyId);
        }

        protected override IQueryable<AuditTrail> GetBaseAuditQuery(AuditSearchRequest searchRequest)
        {
            return this.readOnlyContext.Set<AuditTrail>().AsNoTracking().TagWithSource()
                .Where(_ => _.CompanyComponentId == searchRequest.CompanyComponentId);
        }
    }
}