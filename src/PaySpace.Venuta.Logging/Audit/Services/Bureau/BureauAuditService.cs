namespace PaySpace.Venuta.Logging.Audit.Services.Bureau
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class BureauAuditService : AuditSearchService
    {
        private readonly ReadOnlyContext readOnlyContext;

        public BureauAuditService(ReadOnlyContext readOnlyContext)
        {
            this.readOnlyContext = readOnlyContext;
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            return this.GetBaseAuditQuery(searchRequest)
                .ForAlternativeId(searchRequest)
                .ForAreaAndControllersAsync(this.readOnlyContext, searchRequest);
        }

        protected override bool IsParametersValid(AuditSearchRequest searchRequest)
        {
            return searchRequest.AuditArea != AuditArea.Bureau || !string.IsNullOrEmpty(searchRequest.Controller);
        }

        private IQueryable<AuditTrail> GetBaseAuditQuery(AuditSearchRequest searchRequest)
        {
            var query = this.readOnlyContext.Set<AuditTrail>()
                .AsNoTracking()
                .TagWithSource();

            if (searchRequest.Controller == "BureauSecurityRole")
            {
                query = query.Where(_ => _.CompanyId == null);
            }
            else if (searchRequest.CompanyId > 1)
            {
                query = query.Where(_ => _.CompanyId == searchRequest.CompanyId);
            }

            if (searchRequest.BureauId > 0)
            {
                query = query.Where(_ => _.BureauId == searchRequest.BureauId);
            }

            return query;
        }
    }
}