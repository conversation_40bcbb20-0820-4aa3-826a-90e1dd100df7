namespace PaySpace.Venuta.Logging.Audit.Services.Agency
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class AgencyAuditService : AuditSearchService
    {
        private readonly ReadOnlyContext readOnlyContext;

        public AgencyAuditService(ReadOnlyContext readOnlyContext)
        {
            this.readOnlyContext = readOnlyContext;
        }

        protected override Task<IQueryable<AuditTrail>> BuildQueryAsync(AuditSearchRequest searchRequest)
        {
            return this.GetBaseAuditQuery()
                .ForCompanyGroupOrAgency(searchRequest)
                .ForAlternativeId(searchRequest)
                .ForAreaAndControllersAsync(this.readOnlyContext, searchRequest);
        }

        protected override bool IsParametersValid(AuditSearchRequest searchRequest)
        {
            return searchRequest.AuditArea != AuditArea.Agency || !string.IsNullOrEmpty(searchRequest.Controller);
        }

        private IQueryable<AuditTrail> GetBaseAuditQuery()
        {
            return this.readOnlyContext.Set<AuditTrail>()
                .AsNoTracking()
                .TagWithSource();
        }
    }
}