namespace PaySpace.Venuta.Logging.Audit.AuditHandlers
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.Extensions.Localization;
    using Microsoft.Extensions.Logging;

    using PaySpace.Venuta.Data.Models.Audit;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Factories;

    public interface IDeleteAuditHandler : IAuditLogHandler
    {
    }

    internal class DeleteAuditHandler : AuditHandlerBase, IDeleteAuditHandler
    {
        private readonly ILogger<DeleteAuditHandler> logger;

        public DeleteAuditHandler(
            IStringLocalizerFactory stringLocalizerFactory,
            IAuditTrailService auditService,
            IAuditTrailHeaderService headerService,
            IAuditInfoBuilderFactory builderFactory,
            ILogger<DeleteAuditHandler> logger)
            : base(
                stringLocalizerFactory,
                auditService,
                headerService,
                builderFactory)
        {
            this.logger = logger;
        }

        public async Task LogDataAsync(AuditEntity data, CancellationToken cancellationToken)
        {
            try
            {
                await this.LogDataInternalAsync(data, cancellationToken);
            }
            catch (Exception ex) when (ex is EmployeeNotFoundException or RecordDeletedException)
            {
                this.logger.LogInformation(ex, $"type: {data.EntityType}");
            }
        }

        private async Task LogDataInternalAsync(AuditEntity data, CancellationToken cancellationToken)
        {
            var auditInfo = await this.GetAuditInfoAsync(data, AuditActionType.Delete);
            var headerId = await this.GetHeaderIdAsync(auditInfo?.HeaderTableName);

            if (!headerId.HasValue || auditInfo == null)
            {
                return;
            }

            await this.SaveAuditTrailAsync(
                new AuditTrail
                {
                    AuditTrailHeaderId = headerId.Value,
                    AuditAction = AuditActionType.Delete.ToString(),
                    UserId = data.UserId,
                    CompanyId = auditInfo.CompanyId,
                    CompanyGroupId = auditInfo.CompanyGroupId,
                    EmployeeId = auditInfo.EmployeeId,
                    OldValue = this.GetLocalizedAuditValues(auditInfo.LocalizationTableName, auditInfo.AuditingValues, "Audit.Delete.OldValues") ?? string.Empty,
                    AliasName = this.GetLocalizedAuditValues(auditInfo.LocalizationTableName, auditInfo.AuditingValues, "Audit.Delete") ?? string.Empty,
                    ValueAsJson = this.SerializeEntity(auditInfo.AuditingValues) ?? string.Empty,
                    NewValue = auditInfo.Value ?? string.Empty,
                    PayslipId = auditInfo.PayslipId,
                    EmployeeComponentId = auditInfo.EmployeeComponentId,
                    AlternativeId = auditInfo.AlternativeId,
                    DateOccured = data.Timestamp,
                    CompanyRunId = auditInfo.RunId,
                    BureauId = auditInfo.CountryId,
                    AgencyId = auditInfo.AgencyId,
                    CompanyCustomFormCategoryId = auditInfo.CompanyCustomFormCategoryId,
                    BureauCustomFormCategoryId = auditInfo?.BureauCustomFormCategoryId
                },
                cancellationToken);
        }
    }
}