namespace PaySpace.Venuta.Logging.Audit
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore.ChangeTracking;

    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.Abstractions;

    internal class AuditPublisherBase
    {
        private readonly IAuditLogHandler handler;

        protected static readonly Type[] ImmediatelySavedEntityTypes = { typeof(EmployeeLoan), typeof(EmployeeSaving) };

        protected AuditPublisherBase(IAuditLogHandler handler)
        {
            this.handler = handler;
        }

        protected IDictionary<string, object> GetProperties(EntityEntry entry)
        {
            var properties = new Dictionary<string, object>();

            // Add EF entity properties.
            foreach (var property in entry.CurrentValues.Properties)
            {
                var value = entry.CurrentValues[property];
                if (this.IsValidType(property.ClrType) && value != null)
                {
                    properties.Add(property.Name, value);
                }
            }

            // Add non EF entity properties if missing.
            foreach (var propertyInfo in entry.Metadata.ClrType.GetProperties())
            {
                if (!properties.ContainsKey(propertyInfo.Name))
                {
                    var value = propertyInfo.GetValue(entry.Entity);
                    if (this.IsValidType(propertyInfo.PropertyType) && value != null)
                    {
                        properties.Add(propertyInfo.Name, value);
                    }
                }
            }

            return properties;
        }

        protected Task HandleMessageAsync(AuditLogMessage message, CancellationToken cancellationToken)
        {
            return this.handler.LogDataAsync(message.Entity, cancellationToken);
        }

        private bool IsValidType(Type propertyType)
        {
            var actualType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;
            if (actualType.IsValueType || actualType == typeof(string))
            {
                return true;
            }

            return false;
        }
    }
}