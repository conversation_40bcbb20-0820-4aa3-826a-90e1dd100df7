namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Components
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class ComponentIndicatorInfoBuilder : AuditInfoBuilderBase<ComponentIndicator>
    {
        public ComponentIndicatorInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override object GetAuditingValues(ComponentIndicator entity)
        {
            return new
            {
                entity.IndicatorDescription
            };
        }

        protected override Task<long?> GetEmployeeIdAsync(ComponentIndicator entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override Task<long?> GetCompanyIdAsync(ComponentIndicator entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override long? GetCompanyComponentId(ComponentIndicator entity)
        {
            return entity.ComponentCompanyId;
        }

        protected override long? GetAlternativeId(ComponentIndicator entity)
        {
            return entity.IndicatorId;
        }

        protected override string GetDescription(ComponentIndicator entity)
        {
            return "Indicator";
        }

        protected override string GetHeaderTableName(ComponentIndicator entity)
        {
            return entity.ComponentCompanyId > 0 ? nameof(ComponentCompany) : base.GetHeaderTableName(entity);
        }
    }
}