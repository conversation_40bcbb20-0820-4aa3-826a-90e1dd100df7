namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Components
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class ComponentIndicatorLineInfoBuilder : AuditInfoBuilderBase<ComponentIndicatorLine>
    {
        public ComponentIndicatorLineInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override object GetAuditingValues(ComponentIndicatorLine entity)
        {
            return new
            {
                entity.IndicatorDescription,
                entity.IndicatorLineDescription,
                entity.IndicatorVariable
            };
        }

        protected override Task<long?> GetEmployeeIdAsync(ComponentIndicatorLine entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override Task<long?> GetCompanyIdAsync(ComponentIndicatorLine entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override long? GetCompanyComponentId(ComponentIndicatorLine entity)
        {
            return entity.ComponentCompanyId;
        }

        protected override long? GetAlternativeId(ComponentIndicatorLine entity)
        {
            return entity.IndicatorLineId;
        }

        protected override string GetDescription(ComponentIndicatorLine entity)
        {
            return $"Indicator: {entity.IndicatorDescription}";
        }

        protected override string GetHeaderTableName(ComponentIndicatorLine entity)
        {
            return entity.ComponentCompanyId > 0 ? nameof(ComponentCompany) : base.GetHeaderTableName(entity);
        }
    }
}