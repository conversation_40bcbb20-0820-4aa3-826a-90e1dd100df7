namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Components
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Models;

    internal class ComponentValuesInfoBuilder : EmployeeComponentAuditInfoBuilderBase<ComponentValues>
    {
        private readonly LoggerContext loggerContext;

        public ComponentValuesInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        public override async Task<AuditInfo?> GetAuditInfoAsync(AuditEntity audit, AuditActionType actionType)
        {
            var auditInfo = await base.GetAuditInfoAsync(audit, actionType);
            if (auditInfo != null && actionType == AuditActionType.Delete)
            {
                auditInfo.Value = string.Empty;
            }

            return auditInfo;
        }

        protected override async Task<object> GetAuditingValuesAsync(ComponentValues entity)
        {
            if (entity.IsOnceOffValue == true)
            {
                var alias = await this.loggerContext.Set<ComponentEmployee>()
                    .Where(_ => _.ComponentId == entity.ComponentEmployeeId)
                    .Select(_ => _.ComponentCompany.AliasDescription)
                    .SingleOrDefaultAsync();

                return new
                {
                    AliasDescription = string.IsNullOrEmpty(alias) ? entity.AliasDescription : $"{alias} - {entity.AliasDescription}",
                    Value = this.GetValue(entity)
                };
            }

            return new
            {
                RecurringDescription = entity.AliasDescription,
                Value = this.GetValue(entity)
            };
        }

        protected override string GetDescription(ComponentValues entity)
        {
            return entity.AliasDescription;
        }

        protected override async Task<long?> GetPayslipIdAsync(ComponentValues entity)
        {
            // Only return a PayslipId for Once Off Component changes, or else the Edit Payslip Audit trail will show Recurring Component Audits.
            if (entity.IsOnceOffValue == false)
            {
                return null;
            }

            var employeeId = await this.loggerContext.Set<ComponentEmployee>()
                .Where(_ => _.ComponentId == entity.ComponentEmployeeId)
                .Select(_ => _.EmployeeId)
                .SingleOrDefaultAsync();

            return await this.loggerContext.Set<PayslipHeader>()
                .Where(_ => _.EmployeeId == employeeId && _.CompanyRunId == entity.CompanyRunId)
                .Select(_ => _.PayslipId)
                .FirstOrDefaultAsync();
        }

        protected override long? GetEmployeeComponentId(ComponentValues entity)
        {
            return entity.ComponentEmployeeId;
        }

        protected override string GetValue(ComponentValues entity)
        {
            return entity.ComponentValue?.ToString("n2");
        }

        protected override long? GetRunId(ComponentValues entity)
        {
            return entity.CompanyRunId;
        }

        public override async Task<IEnumerable<FormattedModifiedProperty>> GetModifiedPropertiesAsync(AuditEntity data)
        {
            var modifiedPropertyDetails = await base.GetModifiedPropertiesAsync(data);
            var componentEmployeeId = (data.Entity as ComponentValues)?.ComponentEmployeeId;
            if (componentEmployeeId.HasValue)
            {
                var companyComponentDescription = await this.loggerContext.Set<ComponentEmployee>().AsNoTracking()
                    .Where(_ => _.ComponentId == componentEmployeeId)
                    .Select(_ => _.ComponentCompany.AliasDescription)
                    .SingleOrDefaultAsync();

                foreach (var modifiedPropertyDetail in modifiedPropertyDetails)
                {
                    modifiedPropertyDetail.DetailsFormat = companyComponentDescription + " - " + modifiedPropertyDetail.DetailsFormat;
                }
            }

            return modifiedPropertyDetails;
        }

        protected override async Task<AuditActionType?> GetOverrideActionAsync(AuditEntity auditEntity, AuditActionType actionType)
        {
            var entity = (ComponentValues)auditEntity.Entity;
            if (actionType == AuditActionType.New)
            {
                var existingRecords = await this.loggerContext.Set<ComponentValues>().AnyAsync(_ => _.ComponentEmployeeId == entity.ComponentEmployeeId);
                if (existingRecords && entity.Active == false)
                {
                    return AuditActionType.Delete;
                }
                else if (existingRecords)
                {
                    return AuditActionType.Edit;
                }
            }
            else if (actionType == AuditActionType.Edit && auditEntity.ModifiedProperties.Any(_ => _.PropertyName == nameof(ComponentValues.Active)))
            {
                if (entity.Active == false)
                {
                    return AuditActionType.Delete;
                }
                else
                {
                    return AuditActionType.New;
                }
            }

            return await base.GetOverrideActionAsync(auditEntity, actionType);
        }
    }
}