namespace PaySpace.Venuta.Logging.Audit.InfoBuilders
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    internal class UserInfoBuilder : AuditInfoBuilderBase<User>
    {
        private readonly IEnumService enumService;
        private readonly ICompanyService companyService;

        public UserInfoBuilder(
            IAuditTrailService auditTrailService,
            IEnumService enumService,
            ICompanyService companyService,
            LoggerContext loggerContext)
            : base(auditTrailService, loggerContext)
        {
            this.enumService = enumService;
            this.companyService = companyService;
        }

        public override async Task<IEnumerable<FormattedModifiedProperty>> GetModifiedPropertiesAsync(AuditEntity data)
        {
            var modifiedProperties = await base.GetModifiedPropertiesAsync(data);
            var newProperties = new List<FormattedModifiedProperty>();

            foreach (var property in modifiedProperties)
            {
                if (property.NewValue is bool value)
                {
                    if (property.OldValue == null && !value)
                    {
                       continue;
                    }

                    newProperties.Add(property);
                }
                else
                {
                    newProperties.Add(property);
                }
            }

            return newProperties.Count > 0 ? newProperties : modifiedProperties;
        }

        protected override async Task<object> GetAuditingValuesAsync(User entity)
        {
            return new
            {
                UserType = await this.GetUserType(entity),
                FirstName = entity.FirstName,
                LastName = entity.LastName,
                Email = entity.Email
            };
        }

        protected override int? GetCountryId(User entity)
        {
            if (entity.UserType == UserType.Bureau)
            {
                return 1;
            }

            return null;
        }

        protected override string GetDescription(User entity)
        {
            return entity.FullName;
        }

        protected override Task<long?> GetCompanyIdAsync(User entity)
        {
            return entity.UserType == UserType.Employee
                ? Task.FromResult(entity.CompanyId)
                : Task.FromResult((long?)null);
        }

        protected override async Task<long?> GetCompanyGroupIdAsync(User entity)
        {
            if (entity.UserType is UserType.Employee or UserType.Bureau)
            {
                return null;
            }

            return entity.CompanyGroupId ?? await this.companyService.GetCompanyGroupIdAsync(entity.CompanyId!.Value);
        }

        protected override Task<long?> GetEmployeeIdAsync(User entity)
        {
            return Task.FromResult((long?)null);
        }

        protected override string GetHeaderTableName(User entity)
        {
            if (entity.IsUserActive)
            {
                return SystemAreas.UserActivate.TableName;
            }

            return base.GetHeaderTableName(entity);
        }

        private async Task<string> GetUserType(User entity)
        {
            var query = await this.enumService.GetUserTypesAsync();
            return query.Where(_ => _.UserTypeId == (int)entity.UserType)
                .Select(_ => _.UserTypeDescription)
                .FirstOrDefault();
        }

        protected override long? GetAlternativeId(User entity)
        {
            return entity.UserId;
        }
    }
}