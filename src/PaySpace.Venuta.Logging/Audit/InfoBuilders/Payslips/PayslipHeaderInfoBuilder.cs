namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Payslips
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Logging.Abstractions;

    internal class PayslipHeaderInfoBuilder : EmployeeAuditEntityInfoBuilder<PayslipHeader>
    {
        private readonly IStringLocalizer localizer;
        private readonly LoggerContext loggerContext;

        public PayslipHeaderInfoBuilder(IAuditTrailService auditService, IStringLocalizer<PayslipHeader> localizer, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.localizer = localizer;
            this.loggerContext = loggerContext;
        }

        protected override async Task<object> GetAuditingValuesAsync(PayslipHeader entity)
        {
            var runDescription = await this.loggerContext.Set<CompanyRun>().Where(_ => _.RunId == entity.CompanyRunId)
                .Select(_ => _.RunDescription)
                .FirstOrDefaultAsync();

            return new
            {
                entity.Paid,
                entity.PayslipComments,
                runDescription
            };
        }

        protected override string GetDescription(PayslipHeader entity)
        {
            return this.localizer.GetString("lblBasicPayText");
        }

        protected override long? GetPayslipId(PayslipHeader entity)
        {
            return entity.PayslipId;
        }

        protected override long? GetRunId(PayslipHeader entity)
        {
            return entity.CompanyRunId;
        }

        public override async Task<IEnumerable<FormattedModifiedProperty>> GetModifiedPropertiesAsync(AuditEntity data)
        {
            var payslipHeader = (data.Entity as PayslipHeader);
            var modifiedProperties = await base.GetModifiedPropertiesAsync(data);

            // only return modified properties matching the InputType, as well as the InputType and HasBeenEdited if they have been changed.
            modifiedProperties = modifiedProperties.Where(_ => _.PropertyName == payslipHeader?.InputType
            || _.PropertyName == nameof(payslipHeader.InputType)
            || _.PropertyName == nameof(payslipHeader.HasBeenEdited)
            || _.PropertyName == nameof(payslipHeader.Paid));

            // if present, change the wording for HasBeenEdited to make more sense in the audit trail
            var hasBeenEditedProperty = modifiedProperties.FirstOrDefault(_ => _.PropertyName == nameof(payslipHeader.HasBeenEdited));
            if (hasBeenEditedProperty != default)
            {
                hasBeenEditedProperty.DetailsFormat = hasBeenEditedProperty.DetailsFormat.Replace("{0}", this.localizer.GetString("lblHasBeenEdited"));
            }

            return modifiedProperties;
        }
    }
}