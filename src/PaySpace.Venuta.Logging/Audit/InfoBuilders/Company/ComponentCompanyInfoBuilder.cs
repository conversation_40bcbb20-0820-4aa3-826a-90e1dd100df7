namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;

    internal sealed class ComponentCompanyInfoBuilder : AuditInfoBuilderBase<ComponentCompany>
    {
        private readonly LoggerContext loggerContext;

        public ComponentCompanyInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        public override async Task<AuditInfo?> GetAuditInfoAsync(AuditEntity audit, AuditActionType actionType)
        {
            var auditInfo = await base.GetAuditInfoAsync(audit, actionType);
            if (auditInfo != null && actionType == AuditActionType.Delete)
            {
                // Audit must be on landing page for delete
                auditInfo.CompanyComponentId = null;
            }
            else if (auditInfo != null && actionType != AuditActionType.Delete)
            {
                // Audit must be on edit page for Add/Update
                auditInfo.CompanyId = null;
            }

            return auditInfo;
        }

        protected override long? GetAlternativeId(ComponentCompany entity) => entity.ComponentId;

        protected override object GetAuditingValues(ComponentCompany entity)
        {
            return new
            {
                entity.AliasDescription,
                entity.ComponentCode,
                entity.ActiveFromMonth,
                entity.ActiveToMonth,
                entity.AddToEmployee,
                entity.InPackage,
                entity.IsCostToCompany,
                entity.MultiplyByComponentCompanyId,
                entity.DoNotConvertCurrency,
                entity.AutoRecoveryType,
                entity.CalcRegardlessOfBasicPosted,
                entity.HidePayslipComments,
            };
        }

        protected override string GetDescription(ComponentCompany entity) => entity.AliasDescription;

        protected override long? GetCompanyComponentId(ComponentCompany entity)
        {
            return entity.ComponentId;
        }

        protected override async Task<long?> GetCompanyIdAsync(ComponentCompany entity)
        {
            return await this.loggerContext.Set<CompanyRunFrequency>()
                .TagWithSource()
                .Where(_ => _.CompanyFrequencyId == entity.CompanyFrequencyId)
                .Select(_ => _.CompanyId)
                .FirstOrDefaultAsync();
        }

        protected override Task<long?> GetEmployeeIdAsync(ComponentCompany entity)
        {
            return Task.FromResult<long?>(null);
        }
    }
}