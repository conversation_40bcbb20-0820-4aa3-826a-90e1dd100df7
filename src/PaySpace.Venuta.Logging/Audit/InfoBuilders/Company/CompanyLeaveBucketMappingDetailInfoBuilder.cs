namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyLeaveBucketMappingDetailInfoBuilder : AuditInfoBuilderBase<CompanyLeaveBucketMappingDetail>
    {
        public CompanyLeaveBucketMappingDetailInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
        }

        protected override object GetAuditingValues(CompanyLeaveBucketMappingDetail entity)
        {
            return new
            {
                FromLeave = entity.FromLeaveBucket,
                ToLeave = entity.ToLeaveBucket
            };
        }

        protected override Task<long?> GetCompanyIdAsync(CompanyLeaveBucketMappingDetail entity)
        {
            return Task.FromResult<long?>(entity.CompanyId);
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyLeaveBucketMappingDetail entity)
        {
            return Task.FromResult((long?)null);
        }
    }
}
