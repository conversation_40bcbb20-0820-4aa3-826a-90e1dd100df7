namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyEbdIndicatorInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyEbdIndicator>
    {
        public CompanyEbdIndicatorInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyEbdIndicator entity) => entity.EbdIndicatorId;

        protected override object GetAuditingValues(CompanyEbdIndicator entity)
        {
            return new
            {
                entity.EbdIndicator
            };
        }

        protected override string GetDescription(CompanyEbdIndicator entity) => entity.EbdIndicator;
    }
}
