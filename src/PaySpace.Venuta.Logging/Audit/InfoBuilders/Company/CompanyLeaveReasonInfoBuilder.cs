namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyLeaveReasonInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyLeaveReason>
    {
        public CompanyLeaveReasonInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyLeaveReason entity) => entity.LeaveReasonId;

        protected override object GetAuditingValues(CompanyLeaveReason entity)
        {
            return new
            {
                entity.LeaveReason,
            };
        }

        protected override string GetDescription(CompanyLeaveReason entity) => entity.ToString();
    }
}
