namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyManagerRequestTypeAttachmentInfoBuilder : AuditInfoBuilderBase<CompanyManagerRequestTypeAttachment>
    {
        private readonly LoggerContext loggerContext;

        public CompanyManagerRequestTypeAttachmentInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyManagerRequestTypeAttachment entity)
        {
            return Task.FromResult((long?)null);
        }

        protected override async Task<long?> GetCompanyIdAsync(CompanyManagerRequestTypeAttachment entity)
        {
            return await this.loggerContext.Set<CompanyManagerRequestType>()
                .Where(_ => _.RequestTypeId == entity.RequestTypeId)
                .Select(_ => _.CompanyId)
                .FirstOrDefaultAsync();
        }

        protected override long? GetAlternativeId(CompanyManagerRequestTypeAttachment entity) => entity.RequestTypeId;

        protected override async Task<string> GetDescriptionAsync(CompanyManagerRequestTypeAttachment entity)
        {
            var requestTypeName = await this.loggerContext.Set<CompanyManagerRequestType>()
                .Where(_ => _.RequestTypeId == entity.RequestTypeId)
                .Select(_ => _.RequestTypeName)
                .FirstOrDefaultAsync();
            return requestTypeName ?? string.Empty;
        }

        protected override async Task<object> GetAuditingValuesAsync(CompanyManagerRequestTypeAttachment entity)
        {
            return new
            {
                CompanyAttachment = await this.GetAttachmentAsync(entity.CompanyAttachmentId),
            };
        }

        private async Task<string?> GetAttachmentAsync(long companyAttachmentId)
        {
            return await this.loggerContext.Set<CompanyAttachment>()
                .Where(_ => _.CompanyAttachmentId == companyAttachmentId)
                .Select(_ => _.SectionName + " - " + _.AttachmentTitle)
                .FirstOrDefaultAsync();
        }
    }
}
