namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    // Used in Leave Scheme Parameters screen
    internal class CompanyLeaveSetupInfoBuilder : AuditInfoBuilderBase<CompanyLeaveSetup>
    {
        private readonly LoggerContext loggerContext;
        private readonly IScopedCache scopedCache;

        public CompanyLeaveSetupInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext, IScopedCache scopedCache)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
            this.scopedCache = scopedCache;
        }

        protected override object GetAuditingValues(CompanyLeaveSetup entity)
        {
            return new
            {
                LeaveType = Enum.GetName(entity.LeaveType),
                entity.LeaveDescription,
                entity.OrderNumber
            };
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyLeaveSetup entity) => Task.FromResult((long?)null);

        protected override async Task<long?> GetCompanyIdAsync(CompanyLeaveSetup entity)
        {
            if (entity.CompanyId != 0)
            {
                return entity.CompanyId;
            }

            // Cater for delete when the non-mapped companyId is not set
            // CompanyId will always be the same for the same SchemeId
            return await this.scopedCache.GetOrCreateAsync(
                $"CompanyLeaveSetup:{entity.CompanyLeaveSchemeId}:SchemeId",
                () => this.loggerContext.Set<CompanyLeaveSetup>()
                    .Where(_ => _.CompanyLeaveSetupId == entity.CompanyLeaveSetupId)
                    .Select(_ => _.CompanyLeaveScheme.CompanyId)
                    .FirstOrDefaultAsync());
        }

        protected override string GetDescription(CompanyLeaveSetup entity)
        {
            return entity.LeaveDescription;
        }
    }
}