namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyBankNameInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyBankName>
    {
        public CompanyBankNameInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyBankName entity) => entity.CompanyBankNameId;

        protected override object GetAuditingValues(CompanyBankName entity)
        {
            return new
            {
                entity.BankName,
                entity.DefaultBranchCode
            };
        }

        protected override string GetDescription(CompanyBankName entity) => entity.BankName;
    }
}