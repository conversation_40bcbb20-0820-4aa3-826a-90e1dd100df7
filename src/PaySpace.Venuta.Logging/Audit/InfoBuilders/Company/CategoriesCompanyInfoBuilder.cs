namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CategoriesCompanyInfoBuilder : AuditInfoBuilderBase<CompanyComponentCategory>
    {
        private readonly LoggerContext loggerContext;

        public CategoriesCompanyInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override long? GetAlternativeId(CompanyComponentCategory entity) => entity.CategoryCompanyId;

        protected override object GetAuditingValues(CompanyComponentCategory entity)
        {
            return new
            {
                entity.Alias
            };
        }

        protected override string GetDescription(CompanyComponentCategory entity) => entity.ToString();

        protected override async Task<long?> GetCompanyIdAsync(CompanyComponentCategory entity)
        {
            return await this.loggerContext.Set<CompanyRunFrequency>()
                .TagWithSource()
                .Where(_ => _.CompanyFrequencyId == entity.CompanyFrequencyId)
                .Select(_ => _.CompanyId)
                .FirstOrDefaultAsync();
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyComponentCategory entity)
        {
            return Task.FromResult<long?>(null);
        }
    }
}