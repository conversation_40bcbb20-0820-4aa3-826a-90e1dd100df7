namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyComponentGroupInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyComponentGroup>
    {
        public CompanyComponentGroupInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyComponentGroup entity) => entity.ComponentGroupId;

        protected override object GetAuditingValues(CompanyComponentGroup entity)
        {
            return new
            {
                entity.ComponentGroupCode,
                entity.ComponentGroupDescription
            };
        }

        protected override string GetDescription(CompanyComponentGroup entity) => entity.ComponentGroupDescription;
    }
}
