namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Models;

    internal class OrganizationRegionHistoryInfoBuilder : AuditInfoBuilderBase<OrganizationRegionHistory>
    {
        private readonly LoggerContext loggerContext;
        private readonly IScopedCache scopedCache;

        public OrganizationRegionHistoryInfoBuilder(IAuditTrailService auditTrailService, LoggerContext loggerContext, IScopedCache scopedCache)
            : base(auditTrailService, loggerContext)
        {
            this.loggerContext = loggerContext;
            this.scopedCache = scopedCache;
        }

        public override async Task<AuditInfo?> GetAuditInfoAsync(AuditEntity audit, AuditActionType actionType)
        {
            if (actionType == AuditActionType.Delete)
            {
                // dont continue, if the region doesn't exist
                var entity = audit.Entity as OrganizationRegionHistory;
                if (entity?.RegionId == 0)
                {
                    return null;
                }

                var hasRegion = await this.loggerContext.Set<OrganizationRegion>().AnyAsync(_ => _.RegionId == entity.RegionId);
                if (!hasRegion)
                {
                    return null;
                }
            }

            return await base.GetAuditInfoAsync(audit, actionType);
        }

        public override async Task<IEnumerable<FormattedModifiedProperty>> GetModifiedPropertiesAsync(AuditEntity data)
        {
            var properties = new List<FormattedModifiedProperty>();
            var entity = data.Entity as OrganizationRegionHistory;

            var regionDescription = await this.GetRegionNameAsync(entity);

            foreach (var property in data.ModifiedProperties)
            {
                var formattedProperty = await this.GetModifiedPropertyDetails(property, data);
                formattedProperty.PropertyName = string.Join(" - ", regionDescription, formattedProperty.PropertyName);
                properties.Add(formattedProperty);
            }

            return properties;
        }

        protected override async Task<object> GetAuditingValuesAsync(OrganizationRegionHistory entity)
        {
            var regionDescription = await this.GetRegionNameAsync(entity);

            return new
            {
                Description = string.Join(" - ", regionDescription, entity.EffectiveDate.ToString("d", CultureInfo.CurrentUICulture)),
                entity.RegionId,
                entity.EffectiveDate
            };
        }

        protected override long? GetAlternativeId(OrganizationRegionHistory entity)
        {
            return entity.HistoryEntityDetailId;
        }

        protected override Task<long?> GetEmployeeIdAsync(OrganizationRegionHistory entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override async Task<long?> GetCompanyIdAsync(OrganizationRegionHistory entity)
        {
            return await this.loggerContext.Set<OrganizationRegion>()
                .Where(_ => _.RegionId == entity.RegionId)
                .Select(_ => _.CompanyId)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetRegionNameAsync(OrganizationRegionHistory? entity)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"RegionName:{entity.RegionId}",
                () => this.loggerContext.Set<OrganizationRegion>()
                    .Where(_ => _.RegionId == entity.RegionId)
                    .Select(_ => _.Description)
                    .FirstOrDefaultAsync());
        }
    }
}