namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyAttachmentClassificationInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyAttachmentClassification>
    {
        public CompanyAttachmentClassificationInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyAttachmentClassification entity) => entity.ClassificationId;

        protected override string GetDescription(CompanyAttachmentClassification entity) => entity.Classification;

        protected override object GetAuditingValues(CompanyAttachmentClassification entity)
        {
            return new
            {
                entity.Classification,
                entity.ExcludeSecurityRoles,
            };
        }
    }
}
