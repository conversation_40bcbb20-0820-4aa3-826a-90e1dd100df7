namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Globalization;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    internal sealed record LeaveDetailAuditInfo(long CompanyId, string? SchemeName, string? LeaveDescription, LeaveType? LeaveType, int? OrderNumber);

    // Used in Leave Scheme Parameters screen
    internal class CompanyLeaveDetailInfoBuilder : AuditInfoBuilderBase<CompanyLeaveDetail>
    {
        private readonly LoggerContext loggerContext;
        private readonly IStringLocalizer localizer;
        private readonly IScopedCache scopedCache;
        private readonly IEnumService enumService;

        private static readonly List<string> CustomLookupProperties = new() { "EncashComponentId", "LiabilityComponentId", "ForfeitCompanyLeaveSetupId" };

        // TODO: Convert to FrozenDictionary when .NET 9 goes to master
        private static readonly IReadOnlyDictionary<string, string> ForfeiturePeriodLocalizationKeys =
            new ReadOnlyDictionary<string, string>(
                new Dictionary<string, string>
                {
                    { SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.SpecifyMonth, "lblSpecifyMonth" },
                    { SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.GroupJoinDate, "lblGroupJoinDate" },
                    { SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.EmploymentDate, "lblEmploymentDate" },
                    { SystemAreas.CompanyLeaveSchemeParameter.ForfeiturePeriodOption.EffectiveDateEmpLinked, "lblEffectiveDateEmpLinked" }
                });

        public CompanyLeaveDetailInfoBuilder(
            IAuditTrailService auditService,
            LoggerContext loggerContext,
            IStringLocalizer<CompanyLeaveDetail> localizer,
            IScopedCache scopedCache,
            IEnumService enumService)
                : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
            this.localizer = localizer;
            this.scopedCache = scopedCache;
            this.enumService = enumService;
        }

        protected override async Task<object> GetAuditingValuesAsync(CompanyLeaveDetail entity)
        {
            var companyLeaveSetupDetails = await this.GetCompanyLeaveSetupDetailsAsync(entity);

            // Non-accumulative is set as Enabled
            var isForfeitureRulesEnabled = entity.CompanyLeaveSetupType == CompanyLeaveSetupType.NonAccumulative;
            var isProrateAccrualEnabled = entity.UpfrontProRateOptions ?? false;

            var baseValues = new
            {
                // Used for Insert and Delete
                CompanyLeaveDetailId = $"{companyLeaveSetupDetails.LeaveDescription} - ({companyLeaveSetupDetails.SchemeName})",

                // Used in Delete.OldValues
                companyLeaveSetupDetails.OrderNumber,
                EffectiveDate = entity.EffectiveDate.ToString("d", CultureInfo.CurrentUICulture),
                LeaveType = companyLeaveSetupDetails.LeaveType != null ? Enum.GetName(companyLeaveSetupDetails.LeaveType.Value) : string.Empty,

                companyLeaveSetupDetails.LeaveDescription,
                UpfrontProRateOptions = isProrateAccrualEnabled,
                EntitlementBand = this.GetLocalizedEntitlementBandSelection(entity),
                StopDate = entity.StopDate?.ToString("d", CultureInfo.CurrentUICulture),

                // Note both of these fields use the same lookup
                EncashComponentId = await this.GetEncashmentComponentDescriptionAsync(entity.EncashComponentId),
                LiabilityComponentIdentity = await this.GetEncashmentComponentDescriptionAsync(entity.EncashComponentId),

                entity.OffDays,
                entity.IncludePH,
                entity.AfterDays,
                entity.MaxBalance,
                entity.BucketRules,
                entity.ValueLessThan,
                entity.ValueMoreThan,
                entity.ReflectInHours,
                entity.ProrateAccrual,
                entity.ConsecutiveDays,
                entity.DisplayBalanceESS,
                entity.ParcelCombination,
                entity.AccrualPeriodValue,
                entity.DoNotShowOnPaySlip,
                entity.IncludePendingApps,
                entity.NegativeLeaveAmount,
                entity.AttachmentMandatory,
                entity.DoNotForceAttachment,
                entity.DoNotCalculateBceaValue,
                entity.ForceAttachmentOnSecond,
                entity.AccrualComponentCodeHours
            };

            // convert base values to a dictionary so we can append the objects being audited/omitted
            var result = baseValues.GetType().GetProperties()
                .ToDictionary(_ => _.Name, _ => _.GetValue(baseValues));

            // Only audit the Accrual value when no entitlement band is selected
            if (entity is { ApplyServiceLength: false, ApplyEmployeeDefined: not true, ApplyGradeBands: not true })
            {
                result.Add("Accrual", entity.Accrual);
            }

            // We don't want to audit these as we set default values when the entitlement band is changed to LengthOfService/Grades
            if (entity.ApplyEmployeeDefined is true or null)
            {
                result.Add("CompanyLeaveSetupType", isForfeitureRulesEnabled);
                result.Add("AccrualPeriodId", this.GetLeaveAccrualPeriodDescription(entity.AccrualPeriodId));
                result.Add("AccrualOptionId", this.GetLeaveAccrualOptionDescription(entity.AccrualOptionId));
                result.Add("LeaveAccrualValue", this.GetLeaveAccrualValueDescription(entity.LeaveAccrualValueId));
            }

            // We don't want to audit these fields if the checkbox is disabled as the fields are hidden on FE & default values are set on BE
            if (isProrateAccrualEnabled)
            {
                result.Add("UpfrontMonthlyAccrual", entity.UpfrontMonthlyAccrual); // Leave days p/month to accrue
                result.Add("UpfrontAccrualPeriod", entity.UpfrontAccrualPeriod); // For the first number of months
            }

            if (isForfeitureRulesEnabled)
            {
                result.Add("CarryOverDays", entity.CarryOverDays); // Accumulation balance
                result.Add("ForfeitPeriod", entity.ForfeitPeriod); // Forfeited after every
                result.Add("DropOffMonth", this.GetDropOffMonthDescription(entity.DropOffMonthId)); // Month
                result.Add("LeaveForfeitPeriod", this.GetLeaveForfeitPeriodDescription(entity.LeaveForfeitPeriodId)); // Accrual period
                result.Add("EffectiveDateForfeit", this.GetLocalizedEffectiveDateForfeit(entity.EffectiveDateForfeit)); // Forfeiture period
                result.Add("ForfeitCompanyLeaveSetupId", await this.GetForfeitCompanyLeaveSetupDescAsync(entity.ForfeitCompanyLeaveSetupId)); // Carried forward bucket
            }

            return result;
        }

        // Resolve descriptions for custom lookups on update
        public override async Task<IEnumerable<FormattedModifiedProperty>> GetModifiedPropertiesAsync(AuditEntity data)
        {
            var modifiedProperties = await base.GetModifiedPropertiesAsync(data);
            foreach (var property in modifiedProperties.Where(_ => CustomLookupProperties.Contains(_.PropertyName)))
            {
                switch (property.PropertyName)
                {
                    case "EncashComponentId":
                    case "LiabilityComponentId":
                        property.OldValue = await this.GetEncashmentComponentDescriptionAsync((long?)property.OldValue);
                        property.NewValue = await this.GetEncashmentComponentDescriptionAsync((long?)property.NewValue);
                        break;
                    case "ForfeitCompanyLeaveSetupId":
                        property.OldValue = await this.GetForfeitCompanyLeaveSetupDescAsync((long?)property.OldValue);
                        property.NewValue = await this.GetForfeitCompanyLeaveSetupDescAsync((long?)property.NewValue);
                        break;
                }
            }

            return modifiedProperties;
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyLeaveDetail entity) => Task.FromResult((long?)null);

        protected override long? GetAlternativeId(CompanyLeaveDetail entity) => entity.CompanyLeaveDetailId;

        protected override async Task<long?> GetCompanyIdAsync(CompanyLeaveDetail entity)
        {
            return await this.GetCompanyLeaveSetupDetailsAsync(entity)
                .ContinueWith(_ => _.Result.CompanyId);
        }

        protected override async Task<string> GetDescriptionAsync(CompanyLeaveDetail entity)
        {
            var companyLeaveSetupDetails = await this.GetCompanyLeaveSetupDetailsAsync(entity);

            return $"{companyLeaveSetupDetails.SchemeName} - {companyLeaveSetupDetails.LeaveDescription}";
        }

        private string? GetLeaveAccrualValueDescription(int? leaveAccrualValue)
        {
            if (leaveAccrualValue is null or 0)
            {
                return string.Empty;
            }

            return this.enumService.GetLeaveAccrualValues()
                .Where(_ => _.AccrualValueId == (int)leaveAccrualValue)
                .Select(_ => _.AccrualValue)
                .FirstOrDefault();
        }

        private string? GetDropOffMonthDescription(int? monthId)
        {
            if (monthId is null or 0)
            {
                return string.Empty;
            }

            return this.enumService.GetMonthsOfYear()
                .Where(_ => _.MonthId == monthId)
                .Select(_ => _.MonthDescription)
                .FirstOrDefault();
        }

        private string? GetLeaveAccrualPeriodDescription(int accrualPeriodId)
        {
            if (accrualPeriodId == 0)
            {
                return string.Empty;
            }

            return this.enumService.GetLeaveAccrualPeriods()
                .Where(_ => _.AccrualPeriodId == accrualPeriodId)
                .Select(_ => _.AccrualPeriodDescription)
                .FirstOrDefault();
        }

        private string? GetLeaveAccrualOptionDescription(int accrualOptionId)
        {
            if (accrualOptionId == 0)
            {
                return string.Empty;
            }

            return this.enumService.GetLeaveAccrualOptions()
                .Where(_ => _.AccrualOptionId == accrualOptionId)
                .Select(_ => _.AccrualOptionDescription)
                .FirstOrDefault();
        }

        private string? GetLeaveForfeitPeriodDescription(int? leaveForfeitPeriod)
        {
            if (leaveForfeitPeriod is null or 0)
            {
                return string.Empty;
            }

            return this.enumService.GetLeaveForfeitPeriods()
                .Where(_ => _.ForfeitPeriodId == leaveForfeitPeriod)
                .Select(_ => _.ForfeitPeriodDescription)
                .FirstOrDefault();
        }

        private async Task<string?> GetForfeitCompanyLeaveSetupDescAsync(long? forfeitCompanyLeaveSetupId)
        {
            if (forfeitCompanyLeaveSetupId is null or -1 or 0)
            {
                return this.localizer.GetString(SystemAreas.CompanyLeaveSchemeParameter.Keys.NextCycle).ToString();
            }

            var carriedForwardBucket = await this.loggerContext.Set<CompanyLeaveSetup>()
                .Where(_ => _.CompanyLeaveSetupId == (long)forfeitCompanyLeaveSetupId)
                .Select(_ => new { value = $"({_.OrderNumber}) - {_.LeaveDescription}"})
                .FirstOrDefaultAsync();

            return carriedForwardBucket?.value ?? string.Empty;
        }

        private string GetLocalizedEffectiveDateForfeit(string effectiveDateForfeit)
        {
            return ForfeiturePeriodLocalizationKeys.TryGetValue(effectiveDateForfeit, out var localizationKey)
                ? this.localizer.GetString(localizationKey)
                : string.Empty;
        }

        private string GetLocalizedEntitlementBandSelection(CompanyLeaveDetail entity)
        {
            if (entity.ApplyServiceLength)
            {
                return this.localizer.GetString(nameof(entity.ApplyServiceLength));
            }

            if (entity.ApplyGradeBands == true)
            {
                return this.localizer.GetString(nameof(entity.ApplyGradeBands));
            }

            if (entity.ApplyEmployeeDefined is true or null)
            {
                return this.localizer.GetString(nameof(entity.ApplyEmployeeDefined));
            }

            return string.Empty;
        }

        private async Task<string?> GetEncashmentComponentDescriptionAsync(long? encashComponentId)
        {
            if (encashComponentId is null or 0)
            {
                return string.Empty;
            }

            var encashmentDescription = await this.loggerContext.Set<ComponentCompany>().TagWithSource()
                .Where(_ => _.ComponentId == encashComponentId)
                .Include(_ => _.CompanyFrequency)
                .Select(_ => new { Description = _.CompanyFrequency.FrequencyName + " - " + _.AliasDescription })
                .FirstOrDefaultAsync();

            return encashmentDescription?.Description ?? string.Empty;
        }

        private Task<LeaveDetailAuditInfo> GetCompanyLeaveSetupDetailsAsync(CompanyLeaveDetail entity)
        {
            // If SchemeName is populated, NotMapped properties were set (likely a delete operation)
            if (entity.SchemeName is not null)
            {
                return Task.FromResult(
                    new LeaveDetailAuditInfo(entity.CompanyId, entity.SchemeName, entity.LeaveDescription, entity.LeaveType, entity.OrderNumber)
                );
            }

            return this.scopedCache.GetOrCreateAsync(
                $"CompanyLeaveSetup:{entity.CompanyLeaveSetupId}:LeaveSetupId:{entity.CompanyLeaveDetailId}:LeaveDetailId",
                () => this.loggerContext.Set<CompanyLeaveSetup>().TagWithSource()
                    .Include(_ => _.CompanyLeaveScheme)
                    .Where(_ => _.CompanyLeaveSetupId == entity.CompanyLeaveSetupId)
                    .Select(_ => new LeaveDetailAuditInfo(
                        _.CompanyLeaveScheme.CompanyId,
                        _.CompanyLeaveScheme.SchemeName,
                        _.LeaveDescription,
                        _.LeaveType,
                        _.OrderNumber))
                    .FirstOrDefaultAsync());
        }
    }
}