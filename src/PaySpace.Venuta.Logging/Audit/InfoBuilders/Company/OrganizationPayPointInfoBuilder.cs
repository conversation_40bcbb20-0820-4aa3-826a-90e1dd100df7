namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class OrganizationPayPointInfoBuilder : CompanyAuditEntityInfoBuilder<OrganizationPayPoint>
    {
        public OrganizationPayPointInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(OrganizationPayPoint entity) => entity.PayPointId;

        protected override object GetAuditingValues(OrganizationPayPoint entity)
        {
            return new
            {
                entity.PayPointId,
            };
        }

        protected override string GetDescription(OrganizationPayPoint entity) => entity.ToString();

    }
}
