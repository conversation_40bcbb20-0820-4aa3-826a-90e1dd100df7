namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    // Used in Leave Scheme Parameters screen
    internal class CompanyLeaveServiceLengthInfoBuilder : AuditInfoBuilderBase<CompanyLeaveServiceLength>
    {
        private readonly LoggerContext loggerContext;
        private readonly IScopedCache scopedCache;
        private readonly IEnumService enumService;
        private readonly ICompanySettingService companySettingService;

        public CompanyLeaveServiceLengthInfoBuilder(
            IAuditTrailService auditService,
            LoggerContext loggerContext,
            IScopedCache scopedCache,
            IEnumService enumService,
            ICompanySettingService companySettingService)
                : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
            this.scopedCache = scopedCache;
            this.enumService = enumService;
            this.companySettingService = companySettingService;
        }

        protected override async Task<object> GetAuditingValuesAsync(CompanyLeaveServiceLength entity)
        {
            var auditingValues = new Dictionary<string, object?>
            {
                { "ServiceLengthId", entity.ServiceDescription }, // Deletes
                { "ServiceDescription", entity.ServiceDescription },
                { "AccrualPeriodValue", entity.AccrualPeriodValue },
                { "AccrualPeriodId", this.GetLeaveAccrualPeriodDescription(entity.AccrualPeriodId) },
                { "LeaveAccrualValueId", this.GetLeaveAccrualValueDescription(entity.LeaveAccrualValueId) }
            };

            // Note, the label changes based on a company setting
            var companyId = await this.GetServiceLengthCompanyIdAsync(entity);
            var gradeSettings = await this.GetRelevantGradeSettingsAsync(companyId);
            if (gradeSettings.isGradeBasedAccrualEnabled)
            {
                auditingValues.Add("ServiceGrade", entity.Accrual); // Grade value
            }
            else
            {
                auditingValues.Add("Accrual", entity.Accrual); // Leave credits to accrue
            }

            // If this is null, then the user has enabled the Length of Service entitlement band
            if (entity.GradeCode == null)
            {
                auditingValues.Add("InsertLoS", entity.ServiceDescription); // Insert Length of Service: {0}
                auditingValues.Add("EndYear", entity.EndYear);
                auditingValues.Add("StartYear", entity.StartYear);

                if (gradeSettings.isGradeBasedMaxBalanceEnabled)
                {
                    auditingValues.Add("MaxBalanceFieldId", entity.MaxBalanceFieldId);
                }
            }
            // Otherwise, the user has enabled the Grades entitlement band
            else
            {
                auditingValues.Add("InsertGrade", entity.ServiceDescription); // Insert Grade: {0}
                auditingValues.Add("GradeDescription", await this.GetOrganizationGradeDescAsync(entity));
            }

            return auditingValues;
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyLeaveServiceLength entity) => Task.FromResult((long?)null);

        protected override async Task<long?> GetCompanyIdAsync(CompanyLeaveServiceLength entity)
        {
            var companyId = await this.GetServiceLengthCompanyIdAsync(entity);

            return companyId == 0 ? null : companyId;
        }

        private string? GetLeaveAccrualPeriodDescription(int accrualPeriodId)
        {
            if (accrualPeriodId == 0)
            {
                return string.Empty;
            }

            return this.enumService.GetLeaveAccrualPeriods()
                .Where(_ => _.AccrualPeriodId == accrualPeriodId)
                .Select(_ => _.AccrualPeriodDescription)
                .FirstOrDefault();
        }

        private string? GetLeaveAccrualValueDescription(int leaveAccrualValue)
        {
            if (leaveAccrualValue is 0)
            {
                return string.Empty;
            }

            return this.enumService.GetLeaveAccrualValues()
                .Where(_ => _.AccrualValueId == leaveAccrualValue)
                .Select(_ => _.AccrualValue)
                .FirstOrDefault();
        }

        private async Task<string?> GetOrganizationGradeDescAsync(CompanyLeaveServiceLength entity)
        {
            var companyId = await this.GetServiceLengthCompanyIdAsync(entity);

            var gradeDescription = await this.loggerContext.Set<OrganizationGrade>().TagWithSource()
                .Where(_ => _.CompanyId == companyId && _.Code == entity.GradeCode)
                .Select(_ => _.Description)
                .FirstOrDefaultAsync();

            return gradeDescription == default ? null : gradeDescription;
        }

        private Task<long> GetServiceLengthCompanyIdAsync(CompanyLeaveServiceLength entity)
        {
            if (entity.CompanyId != 0)
            {
                return Task.FromResult(entity.CompanyId);
            }

            return this.scopedCache.GetOrCreateAsync(
                $"Company:LeaveServiceLength:{entity.ServiceLengthId}:ServiceLengthId",
                () => this.loggerContext.Set<CompanyLeaveDetail>().TagWithSource()
                    .Where(_ => _.CompanyLeaveDetailId == entity.CompanyLeaveDetailId)
                    .Select(_ => _.CompanyLeaveSetup.CompanyLeaveScheme.CompanyId)
                    .FirstOrDefaultAsync());
        }

        private async Task<(bool isGradeBasedAccrualEnabled, bool isGradeBasedMaxBalanceEnabled)> GetRelevantGradeSettingsAsync(long companyId)
        {
            var isGradeBasedAccrualEnabled = await this.companySettingService.IsActiveAsync(companyId, SystemAreas.CompanyLeaveSchemeParameter.Keys.GradeBasedAccrualSettingCode);
            var isGradeBasedMaxBalanceEnabled = await this.companySettingService.IsActiveAsync(companyId, SystemAreas.CompanyLeaveSchemeParameter.Keys.GradeBasedMaxBalanceSettingCode);

            return (isGradeBasedAccrualEnabled, isGradeBasedMaxBalanceEnabled);
        }
    }
}