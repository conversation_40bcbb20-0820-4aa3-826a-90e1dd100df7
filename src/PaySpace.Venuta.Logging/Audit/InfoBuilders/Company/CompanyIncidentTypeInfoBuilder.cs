namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyIncidentTypeInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyIncidentType>
    {
        public CompanyIncidentTypeInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyIncidentType entity) => entity.CompanyIncidentTypeId;

        protected override object GetAuditingValues(CompanyIncidentType entity)
        {
            return new
            {
                entity.IncidentType
            };
        }

        protected override string GetDescription(CompanyIncidentType entity) => entity.IncidentType;
    }
}