namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Employees
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Logging.Abstractions;

    internal class CompanyGLDetailInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyGLDetail>
    {
        private readonly LoggerContext loggerContext;

        public CompanyGLDetailInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
        : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override object GetAuditingValues(CompanyGLDetail entity)
        {
            return new
            {
                entity.CompanyGLDetailId
            };
        }

        protected override string GetDescription(CompanyGLDetail entity)
        {
            return this.GetCompanyGLComponentCompany(entity.CompanyComponentId);
        }

        protected override long? GetCompanyComponentId(CompanyGLDetail entity)
        {
            return entity.CompanyComponentId;
        }

        protected override async Task<long?> GetCompanyIdAsync(CompanyGLDetail entity)
        {
            return await this.loggerContext.Set<CompanyGLDetail>()
                .Include(_ => _.ComponentCompany.CompanyFrequency)
                .Where(_ => _.CompanyGLDetailId == entity.CompanyGLDetailId)
                .Select(_ => _.ComponentCompany.CompanyFrequency.CompanyId)
                .FirstOrDefaultAsync();
        }

        private string GetCompanyGLComponentCompany(long companyComponentId)
        {
            return this.loggerContext.Set<ComponentCompany>()
                .Where(_ => _.ComponentId == companyComponentId)
                .Select(_ => _.AliasDescription + " - " + _.ComponentCode)
                .FirstOrDefault();
        }
    }
}