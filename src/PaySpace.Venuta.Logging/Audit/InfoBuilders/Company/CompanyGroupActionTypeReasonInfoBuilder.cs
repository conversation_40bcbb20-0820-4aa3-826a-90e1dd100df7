namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyGroupActionTypeReasonInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyGroupActionTypeReason>
    {
        private readonly LoggerContext loggerContext;

        public CompanyGroupActionTypeReasonInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override long? GetAlternativeId(CompanyGroupActionTypeReason entity) => entity.ActionTypeReasonId;

        protected override async Task<object> GetAuditingValuesAsync(CompanyGroupActionTypeReason entity)
        {
            return new
            {
                entity.ActionReasonCode,
                entity.ActionReasonDescription,
                entity.InactiveDate,
                CompanyGroupActionType = await this.GetCompanyGroupActionTypeDescriptionAsync(entity.CompanyGroupActionTypeId)
            };
        }

        private Task<string?> GetCompanyGroupActionTypeDescriptionAsync(long companyGroupActionTypeId)
        {
            return this.loggerContext.Set<CompanyGroupActionType>()
                .TagWithSource()
                .Where(_ => _.ActionTypeId == companyGroupActionTypeId)
                .Select(_ => _.ActionTypeDescription)
                .FirstOrDefaultAsync();
        }

        protected override string GetDescription(CompanyGroupActionTypeReason entity) => entity.ActionReasonDescription;

        protected override Task<long?> GetCompanyIdAsync(CompanyGroupActionTypeReason entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override Task<long?> GetCompanyGroupIdAsync(CompanyGroupActionTypeReason entity)
        {
            return Task.FromResult<long?>(entity.CompanyGroupActionType.CompanyGroupId);
        }
    }
}
