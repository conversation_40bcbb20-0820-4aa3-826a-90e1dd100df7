namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyLeaveBucketMappingInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyLeaveBucketMapping>
    {
        public CompanyLeaveBucketMappingInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
        }

        protected override object GetAuditingValues(CompanyLeaveBucketMapping entity)
        {
            return new
            {
                FromScheme = entity.FromLeaveScheme,
                ToScheme = entity.ToLeaveScheme,
                entity.CompanyId
            };
        }

        protected override long? GetAlternativeId(CompanyLeaveBucketMapping entity) => entity.LeaveMappingId;
    }
}
