namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyPositionFamilyInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyPositionFamily>
    {
        public CompanyPositionFamilyInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyPositionFamily entity) => entity.FamilyId;

        protected override object GetAuditingValues(CompanyPositionFamily entity)
        {
            return new
            {
                entity.Code,
                entity.Description
            };
        }

        protected override string GetDescription(CompanyPositionFamily entity) => entity.ToString();
    }
}