namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyGLInterAccountInfoBuilder : AuditInfoBuilderBase<CompanyGLInterAccount>
    {
        private readonly LoggerContext loggerContext;

        public CompanyGLInterAccountInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
        : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override object GetAuditingValues(CompanyGLInterAccount entity)
        {
            return new
            {
                entity.CompanyGlInterAccId,
                entity.InterGLAccount
            };
        }

        protected override string GetDescription(CompanyGLInterAccount entity)
        {
            return entity.InterGLAccount;
        }

        protected override async Task<long?> GetCompanyIdAsync(CompanyGLInterAccount entity)
        {
            return await this.loggerContext.Set<CompanyGLInterAccount>()
                .Where(_ => _.CompanyGlInterAccId == entity.CompanyGlInterAccId)
                .Select(_ => _.CompanyGL.CompanyFrequency.CompanyId)
                .FirstOrDefaultAsync();
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyGLInterAccount entity)
        {
            return Task.FromResult((long?)null);
        }
    }
}