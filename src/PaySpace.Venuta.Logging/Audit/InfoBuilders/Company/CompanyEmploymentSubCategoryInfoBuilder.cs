namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyEmploymentSubCategoryInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyEmploymentSubCategory>
    {
        public CompanyEmploymentSubCategoryInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyEmploymentSubCategory entity) => entity.SubCategoryId;

        protected override object GetAuditingValues(CompanyEmploymentSubCategory entity)
        {
            return new
            {
                entity.SubCategoryCode,
                entity.Description,
            };
        }

        protected override string GetDescription(CompanyEmploymentSubCategory entity) => entity.Description;
    }
}