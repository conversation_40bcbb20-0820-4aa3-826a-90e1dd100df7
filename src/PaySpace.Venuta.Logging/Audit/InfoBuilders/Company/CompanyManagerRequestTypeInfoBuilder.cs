namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyManagerRequestTypeInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyManagerRequestType>
    {
        private readonly LoggerContext loggerContext;

        public CompanyManagerRequestTypeInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override long? GetAlternativeId(CompanyManagerRequestType entity) => entity.RequestTypeId;

        protected override string GetDescription(CompanyManagerRequestType entity) => entity.RequestTypeName;

        protected override async Task<object> GetAuditingValuesAsync(CompanyManagerRequestType entity)
        {
            return new
            {
                entity.RequestTypeName,
                RequestTypeAttachments = await this.GetAttachmentsAsync(entity.RequestTypeId),
                entity.IsAttachmentReq,
                entity.RequestTypeDescription,
                entity.ExcludeSecurityRoles,
            };
        }

        private async Task<string> GetAttachmentsAsync(long requestTypeId)
        {
            var attachments = await this.loggerContext.Set<CompanyManagerRequestTypeAttachment>()
                .Where(_ => _.RequestTypeId == requestTypeId)
                .Include(_ => _.CompanyAttachment)
                .Select(_ => _.CompanyAttachment.SectionName + " - " + _.CompanyAttachment.AttachmentTitle)
                .ToArrayAsync();
            return string.Join(", ", attachments);
        }
    }
}
