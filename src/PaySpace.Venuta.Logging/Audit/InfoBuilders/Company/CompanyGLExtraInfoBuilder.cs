namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyGLExtraInfoBuilder : AuditInfoBuilderBase<CompanyGLExtra>
    {
        private readonly LoggerContext loggerContext;

        public CompanyGLExtraInfoBuilder(
            IAuditTrailService auditService,
            LoggerContext loggerContext)
        : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override object GetAuditingValues(CompanyGLExtra entity)
        {
            return new
            {
                entity.CompanyExtraGLId,
                entity.ComponentDescription
            };
        }

        protected override string GetDescription(CompanyGLExtra entity)
        {
            return entity.ComponentDescription;
        }

        protected override async Task<long?> GetCompanyIdAsync(CompanyGLExtra entity)
        {
            return await this.loggerContext.Set<CompanyGLExtra>()
                .Where(_ => _.CompanyExtraGLId == entity.CompanyExtraGLId)
                .Select(_ => _.CompanyFrequency.CompanyId)
                .FirstOrDefaultAsync();
        }
        protected override Task<long?> GetEmployeeIdAsync(CompanyGLExtra entity)
        {
            return Task.FromResult((long?)null);
        }
    }
}