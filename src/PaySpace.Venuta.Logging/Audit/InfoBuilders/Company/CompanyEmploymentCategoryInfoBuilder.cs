namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyEmploymentCategoryInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyEmploymentCategory>
    {
        public CompanyEmploymentCategoryInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyEmploymentCategory entity) => entity.CategoryId;

        protected override object GetAuditingValues(CompanyEmploymentCategory entity)
        {
            return new
            {
                entity.CategoryCode,
                entity.CategoryDescription,
            };
        }

        protected override string GetDescription(CompanyEmploymentCategory entity) => entity.CategoryDescription;
    }
}