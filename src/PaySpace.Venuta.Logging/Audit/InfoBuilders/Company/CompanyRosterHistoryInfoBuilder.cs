namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Models;

    internal class CompanyRosterHistoryInfoBuilder : AuditInfoBuilderBase<CompanyRosterHistory>
    {
        private readonly LoggerContext loggerContext;
        private readonly IScopedCache scopedCache;

        public CompanyRosterHistoryInfoBuilder(IAuditTrailService auditTrailService, LoggerContext loggerContext, IScopedCache scopedCache)
        : base(auditTrailService, loggerContext)
        {
            this.loggerContext = loggerContext;
            this.scopedCache = scopedCache;
        }

        public override async Task<AuditInfo?> GetAuditInfoAsync(AuditEntity audit, AuditActionType actionType)
        {
            if (actionType == AuditActionType.Delete)
            {
                // dont continue, if the roster doesn't exist
                var entity = audit.Entity as CompanyRosterHistory;
                if (entity?.RosterId == 0)
                {
                    return null;
                }

                var hasRoster = await this.loggerContext.Set<CompanyRoster>().AnyAsync(_ => _.RosterId == entity.RosterId);
                if (!hasRoster)
                {
                    return null;
                }
            }

            return await base.GetAuditInfoAsync(audit, actionType);
        }

        public override async Task<IEnumerable<FormattedModifiedProperty>> GetModifiedPropertiesAsync(AuditEntity data)
        {
            var properties = new List<FormattedModifiedProperty>();
            var entity = data.Entity as CompanyRosterHistory;

            var rosterDescription = await this.GetRosterNameAsync(entity);

            foreach (var property in data.ModifiedProperties)
            {
                var formattedProperty = await this.GetModifiedPropertyDetails(property, data);
                formattedProperty.PropertyName = string.Join(" - ", rosterDescription, formattedProperty.PropertyName);
                properties.Add(formattedProperty);
            }

            return properties;
        }

        protected override async Task<object> GetAuditingValuesAsync(CompanyRosterHistory entity)
        {
            var rosterDescription = await this.GetRosterNameAsync(entity);

            return new
            {
                Description = string.Join(" - ", rosterDescription, entity.EffectiveDate.ToShortDateString())
            };
        }

        protected override long? GetAlternativeId(CompanyRosterHistory entity)
        {
            return entity.HistoryEntityDetailId;
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyRosterHistory entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override async Task<long?> GetCompanyIdAsync(CompanyRosterHistory entity)
        {
            return await this.loggerContext.Set<CompanyRoster>()
                .Where(_ => _.RosterId == entity.RosterId)
                .Select(_ => _.CompanyId)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetRosterNameAsync(CompanyRosterHistory entity)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"RosterName:{entity.RosterId}",
                () => this.loggerContext.Set<CompanyRoster>()
                    .Where(_ => _.RosterId == entity.RosterId)
                    .Select(_ => _.RosterName)
                    .FirstOrDefaultAsync());
        }
    }
}