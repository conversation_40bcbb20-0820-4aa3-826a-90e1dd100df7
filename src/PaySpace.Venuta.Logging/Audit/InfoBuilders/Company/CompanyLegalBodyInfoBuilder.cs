namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyLegalBodyInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyLegalBody>
    {
        public CompanyLegalBodyInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyLegalBody entity) => entity.CompanyLegalBodyId;

        protected override object GetAuditingValues(CompanyLegalBody entity)
        {
            return new
            {
                entity.LegalBodyCode,
                entity.LegalBodyDescription
            };
        }

        protected override string GetDescription(CompanyLegalBody entity) => entity.ToString();
    }
}

