namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyIncreaseReasonInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyIncreaseReason>
    {
        public CompanyIncreaseReasonInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyIncreaseReason entity) => entity.ReasonId;

        protected override object GetAuditingValues(CompanyIncreaseReason entity)
        {
            return new
            {
                entity.ReasonCode,
                entity.Description
            };
        }

        protected override string GetDescription(CompanyIncreaseReason entity) => entity.Description;
    }
}