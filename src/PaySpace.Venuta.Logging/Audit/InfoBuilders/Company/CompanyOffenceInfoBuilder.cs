namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyOffenceInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyOffence>
    {
        public CompanyOffenceInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyOffence entity) => entity.CompanyOffenceId;

        protected override object GetAuditingValues(CompanyOffence entity)
        {
            return new
            {
                entity.OffenceCode,
                entity.OffenceDescription
            };
        }

        protected override string GetDescription(CompanyOffence entity) => entity.ToString();
    }
}
