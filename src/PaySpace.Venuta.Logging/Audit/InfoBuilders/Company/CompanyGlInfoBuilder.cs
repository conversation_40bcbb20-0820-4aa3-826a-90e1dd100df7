namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyGlInfoBuilder : AuditInfoBuilderBase<CompanyGl>
    {
        private readonly LoggerContext loggerContext;

        public CompanyGlInfoBuilder(
            IAuditTrailService auditService,
            LoggerContext loggerContext)
        : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override object GetAuditingValues(CompanyGl entity)
        {
            return new
            {
                entity.CompanyGlId,
                entity.HeaderName
            };
        }

        protected override string GetDescription(CompanyGl entity)
        {
            return entity.HeaderName;
        }

        protected override async Task<long?> GetCompanyIdAsync(CompanyGl entity)
        {
            return await this.loggerContext.Set<CompanyGl>()
                .Where(_ => _.CompanyGlId == entity.CompanyGlId)
                .Select(_ => _.CompanyFrequency.CompanyId)
                .FirstOrDefaultAsync();
        }

        protected override Task<long?> GetEmployeeIdAsync(CompanyGl entity)
        {
            return Task.FromResult((long?)null);
        }
    }
}