namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyPositionFunctionAreaInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyPositionFunctionArea>
    {
        public CompanyPositionFunctionAreaInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyPositionFunctionArea entity) => entity.FunctionAreaId;

        protected override object GetAuditingValues(CompanyPositionFunctionArea entity)
        {
            return new
            {
                entity.Code,
                entity.Description
            };
        }

        protected override string GetDescription(CompanyPositionFunctionArea entity) => entity.ToString();
    }
}