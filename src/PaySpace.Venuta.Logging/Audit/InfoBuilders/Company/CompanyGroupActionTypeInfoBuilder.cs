namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class CompanyGroupActionTypeInfoBuilder : CompanyAuditEntityInfoBuilder<CompanyGroupActionType>
    {
        public CompanyGroupActionTypeInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(CompanyGroupActionType entity) => entity.ActionTypeId;

        protected override object GetAuditingValues(CompanyGroupActionType entity)
        {
            return new
            {
                entity.ActionTypeCode,
                entity.ActionTypeDescription
            };
        }

        protected override string GetDescription(CompanyGroupActionType entity) => entity.ActionTypeDescription;

        protected override Task<long?> GetCompanyIdAsync(CompanyGroupActionType entity)
        {
            return Task.FromResult<long?>(null);
        }

        protected override Task<long?> GetCompanyGroupIdAsync(CompanyGroupActionType entity)
        {
            return Task.FromResult<long?>(entity.CompanyGroupId);
        }
    }
}
