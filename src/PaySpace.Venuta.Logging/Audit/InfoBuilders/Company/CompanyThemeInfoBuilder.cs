namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Agency;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Models;

    internal class CompanyThemeInfoBuilder : AuditInfoBuilderBase<CompanyTheme>
    {
        private readonly IAuditTrailService auditTrailService;

        public CompanyThemeInfoBuilder(IAuditTrailService auditTrailService, LoggerContext loggerContext)
            : base(auditTrailService, loggerContext)
        {
            this.auditTrailService = auditTrailService;
        }

        public override Task<AuditInfo?> GetAuditInfoAsync(AuditEntity audit, AuditActionType actionType)
        {
            if (actionType is AuditActionType.New or AuditActionType.Delete)
            {
                return Task.FromResult((AuditInfo?)null);
            }

            return base.GetAuditInfoAsync(audit, actionType);
        }

        protected override string GetHeaderTableName(CompanyTheme entity)
        {
            if (entity.AgencyId != null)
            {
                return this.auditTrailService.GetTableName(typeof(Agency));
            }

            return base.GetHeaderTableName(entity);
        }

        protected override long? GetAlternativeId(CompanyTheme entity) => entity.AgencyId;

        protected override long? GetAgencyId(CompanyTheme entity) => entity.AgencyId;

        protected override Task<long?> GetEmployeeIdAsync(CompanyTheme entity)
        {
            return Task.FromResult((long?)null);
        }

        protected override Task<long?> GetCompanyIdAsync(CompanyTheme entity)
        {
            if (entity.AgencyId != null)
            {
                return Task.FromResult((long?)null);
            }

            return Task.FromResult(entity.CompanyId);
        }
    }
}