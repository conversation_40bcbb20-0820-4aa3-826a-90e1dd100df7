namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class EmployeeEOnboardingDetailsInfoBuilder : CompanyAuditEntityInfoBuilder<EmployeeEOnboardingDetails>
    {
        public EmployeeEOnboardingDetailsInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }
        protected override object GetAuditingValues(EmployeeEOnboardingDetails entity)
        {
            return new
            {
                entity.EmployeeDetailsId,
                entity.EOnboardingStatus,
                entity.EmploymentDate,
                entity.Firstname,
                entity.Lastname,
                entity.Email
            };
        }

        protected override long? GetAlternativeId(EmployeeEOnboardingDetails entity)
        {
            return entity.EmployeeDetailsId;
        }
    }
}
