namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Templates
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;

    internal sealed class ComponentVariableInfoBuilder : AuditInfoBuilderBase<ComponentVariable>
    {
        public ComponentVariableInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
        }

        protected override long? GetAlternativeId(ComponentVariable entity) => entity.ComponentVariableId;

        protected override Task<long?> GetEmployeeIdAsync(ComponentVariable entity) => Task.FromResult<long?>(null);

        protected override Task<long?> GetCompanyIdAsync(ComponentVariable entity) => Task.FromResult(entity.CompanyId);

        protected override long? GetAgencyId(ComponentVariable entity) => entity.AgencyId;

        protected override int? GetCountryId(ComponentVariable entity) => 0; // System wide - all countries, handled in BureauAuditService

        protected override string GetLocalizationTableName(ComponentVariable entity) => "ComponentVariables";

        protected override object GetAuditingValues(ComponentVariable entity)
        {
            return new
            {
                entity.Name,
                entity.Code,
                entity.EffectiveDate,
                entity.InactiveDate
            };
        }

        protected override string GetHeaderTableName(ComponentVariable entity)
        {
            if (entity.CompanyId.HasValue)
            {
                return "CompanyComponentVariables";
            }

            if (entity.AgencyId.HasValue)
            {
                return "AgencyComponentVariables";
            }

            return "BureauComponentVariables";
        }
    }
}