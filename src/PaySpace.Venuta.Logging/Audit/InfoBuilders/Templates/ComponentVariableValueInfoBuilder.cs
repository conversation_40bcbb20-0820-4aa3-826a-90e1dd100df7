namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Templates
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;

    internal sealed class ComponentVariableValueInfoBuilder : AuditInfoBuilderBase<ComponentVariableValue>
    {
        private readonly LoggerContext loggerContext;

        public ComponentVariableValueInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override Task<long?> GetEmployeeIdAsync(ComponentVariableValue entity) => Task.FromResult(entity.EmployeeId);

        protected override Task<long?> GetCompanyIdAsync(ComponentVariableValue entity) => Task.FromResult(entity.CompanyId);

        protected override long? GetAgencyId(ComponentVariableValue entity) => entity.AgencyId;

        protected override string GetLocalizationTableName(ComponentVariableValue entity) => "ComponentVariableValues";

        protected override string GetHeaderTableName(ComponentVariableValue entity)
        {
            if (entity.AgencyId.HasValue)
            {
                return "AgencyTemplateConfigurations";
            }

            if (entity.CompanyId.HasValue)
            {
                return "CompanyTemplateConfigurations";
            }

            return "EmployeeTemplateConfigurations";
        }

        protected override async Task<object> GetAuditingValuesAsync(ComponentVariableValue entity)
        {
            var componentVariableCode = await this.GetComponentVariableCodeAsync(entity.ComponentVariableId);
            var templateCode = await this.GetTemplateCodeAsync(entity.TemplateConfigurationId);

            return new
            {
                ComponentVariableCode = componentVariableCode,
                TemplateCode = templateCode,
                entity.IsOverride,
                entity.Value
            };
        }

        protected override async Task<FormattedModifiedProperty> GetModifiedPropertyDetails(ModifiedProperty property, AuditEntity data)
        {
            var entity = (ComponentVariableValue)data.Entity;

            var componentVariableCode = await this.GetComponentVariableCodeAsync(entity.ComponentVariableId);
            var templateCode = await this.GetTemplateCodeAsync(entity.TemplateConfigurationId);

            var componentVariableValueAuditDetail = $"Template: {templateCode}{Environment.NewLine}Component Variable Code: {componentVariableCode}{Environment.NewLine}";

            return new FormattedModifiedProperty()
            {
                PropertyName = property.PropertyName,
                OldValue = property.OldValue,
                NewValue = property.NewValue,
                EntityType = data.EntityType,
                DetailsFormat = this.GetAliasNameFormat($"{componentVariableValueAuditDetail}"),
            };
        }

        private async Task<string?> GetComponentVariableCodeAsync(long? componentVariableId)
        {
            return await this.loggerContext.Set<ComponentVariable>()
                .AsNoTracking()
                .Where(_ => _.ComponentVariableId == componentVariableId)
                .Select(_ => _.Code)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetTemplateCodeAsync(long? templateConfigurationId)
        {
            return this.loggerContext.Set<TemplateConfiguration>()
                .AsNoTracking()
                .Where(_ => _.TemplateConfigurationId == templateConfigurationId && _.TemplateId > 0)
                .Select(_ => _.Template!.Code)
                .FirstOrDefaultAsync();
        }
    }
}