namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Templates
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;

    internal sealed class TemplateInfoBuilder : AuditInfoBuilderBase<Template>
    {
        private readonly LoggerContext loggerContext;

        public TemplateInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override long? GetAgencyId(Template entity) => entity.AgencyId;

        protected override long? GetAlternativeId(Template entity) => entity.TemplateId;

        protected override Task<long?> GetEmployeeIdAsync(Template entity) => Task.FromResult((long?)null);

        protected override Task<long?> GetCompanyIdAsync(Template entity) => Task.FromResult(entity.CompanyId);

        protected override string GetLocalizationTableName(Template entity) => "Templates";

        protected override string GetHeaderTableName(Template entity)
        {
            if (entity.AgencyId.HasValue)
            {
                return "AgencyTemplates";
            }

            return "CompanyTemplates";
        }

        protected override async Task<object> GetAuditingValuesAsync(Template entity)
        {
            return new
            {
                entity.Name,
                entity.Code,
                entity.EffectiveDate,
                ParentTemplateCode = await this.GetTemplateNameAsync(entity.ParentTemplateId)
            };
        }

        private Task<string?> GetTemplateNameAsync(long? templateId)
        {
            return this.loggerContext.Set<Template>()
                .AsNoTracking()
                .Where(_ => _.TemplateId == templateId)
                .Select(_ => _.Name)
                .FirstOrDefaultAsync();
        }
    }
}