namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Templates
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;

    internal sealed class TemplateConfigurationInfoBuilder : AuditInfoBuilderBase<TemplateConfiguration>
    {
        private readonly LoggerContext loggerContext;

        public TemplateConfigurationInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext) : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override Task<long?> GetEmployeeIdAsync(TemplateConfiguration entity) => Task.FromResult(entity.EmployeeId);

        protected override Task<long?> GetCompanyIdAsync(TemplateConfiguration entity) => Task.FromResult(entity.CompanyId);

        protected override long? GetAgencyId(TemplateConfiguration entity) => entity.AgencyId;

        protected override string GetLocalizationTableName(TemplateConfiguration entity) => "TemplateConfigurations";

        protected override string GetHeaderTableName(TemplateConfiguration entity)
        {
            if (entity.AgencyId.HasValue)
            {
                return "AgencyTemplateConfigurations";
            }

            if (entity.CompanyId.HasValue)
            {
                return "CompanyTemplateConfigurations";
            }

            return "EmployeeTemplateConfigurations";
        }

        protected override async Task<object> GetAuditingValuesAsync(TemplateConfiguration entity)
        {
            var templateCode = await this.GetTemplateCodeAsync(entity.TemplateId);

            return new
            {
                TemplateCode = templateCode,
                entity.EffectiveDate
            };
        }

        private Task<string?> GetTemplateCodeAsync(long? templateId)
        {
            return this.loggerContext.Set<Template>()
                .AsNoTracking()
                .Where(_ => _.TemplateId == templateId)
                .Select(_ => _.Code)
                .FirstOrDefaultAsync();
        }
    }
}