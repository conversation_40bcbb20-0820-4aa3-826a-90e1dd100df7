namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Company
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Models;

    public sealed record UserCompanyLinkDescription(string? companyTradingName, string? email, string? groupDescription, string? companyFrequencyName);

    internal class UserCompanyLinkInfoBuilder : CompanyAuditEntityInfoBuilder<UserCompanyLink>
    {
        private readonly LoggerContext loggerContext;
        private readonly IAuditTrailService auditTrailService;
        private readonly IScopedCache scopedCache;

        public UserCompanyLinkInfoBuilder(IAuditTrailService auditTrailService, LoggerContext loggerContext, IScopedCache scopedCache)
            : base(auditTrailService, loggerContext)
        {
            this.loggerContext = loggerContext;
            this.auditTrailService = auditTrailService;
            this.scopedCache = scopedCache;
        }

        public override async Task<AuditInfo?> GetAuditInfoAsync(AuditEntity audit, AuditActionType actionType)
        {
            if (audit.Entity is UserCompanyLink userCompanyLink)
            {
                if (userCompanyLink.Checked == false && actionType == AuditActionType.Delete)
                {
                    return null;
                }
            }

            return await base.GetAuditInfoAsync(audit, actionType);
        }

        protected override async Task<object> GetAuditingValuesAsync(UserCompanyLink entity)
        {
            var userCompanyLinkDescription = await this.GetUserCompanyLinkDescriptionAsync(entity);

            if (entity.IsUserProfile)
            {
                return new
                {
                    DefaultCompanyLink = $"{userCompanyLinkDescription?.companyTradingName} - {userCompanyLinkDescription?.companyFrequencyName}: {userCompanyLinkDescription?.email} - Checked: {entity.Checked}",
                    entity.CompanyId,
                    entity.CompanyFrequency,
                    entity.Checked
                };
            }

            return new
            {
                CompanyTradingName = userCompanyLinkDescription?.companyTradingName,
                UserEmail = userCompanyLinkDescription?.email,
                GroupDescription = userCompanyLinkDescription?.groupDescription
            };
        }

        protected override string GetLocalizationTableName(UserCompanyLink entity)
        {
            return entity.IsUserProfile ? this.auditTrailService.GetTableName(typeof(User)) : base.GetLocalizationTableName(entity);
        }

        protected override async Task<string> GetDescriptionAsync(UserCompanyLink entity)
        {
            return await this.GetCompanyLinkDescriptionAsync(entity);
        }

        protected override async Task<string> GetValueAsync(UserCompanyLink entity)
        {
            if (entity.IsUserProfile)
            {
                return string.Empty;
            }

            return await this.GetCompanyLinkDescriptionAsync(entity);
        }

        protected override async Task<long?> GetCompanyGroupIdAsync(UserCompanyLink entity)
        {
            return await this.loggerContext.Set<CompanyGroupLink>()
                .Where(_ => _.CompanyId == entity.CompanyId)
                .Select(_ => _.CompanyGroup.CompanyGroupId)
                .FirstOrDefaultAsync();
        }

        protected override Task<long?> GetCompanyIdAsync(UserCompanyLink entity)
        {
            return Task.FromResult((long?)entity.CompanyId);
        }

        protected override long? GetAlternativeId(UserCompanyLink entity)
        {
            return entity.UsersCompanyLinkId;
        }

        private async Task<UserCompanyLinkDescription?> GetUserCompanyLinkDescriptionAsync(UserCompanyLink entity)
        {
            return await this.scopedCache.GetOrCreateAsync(
                $"UserCompanyLink:Description:{entity.CompanyId}:{entity.CompanyFrequencyId}:{entity.UserId}",
                async () =>
                {
                    var companyTradingName = await this.loggerContext.Set<Company>()
                            .Where(_ => _.CompanyId == entity.CompanyId)
                            .Select(_ => _.CompanyTradingName)
                            .FirstOrDefaultAsync();

                    var userEmail = await this.loggerContext.Set<User>()
                        .Where(_ => _.UserId == entity.UserId)
                        .Select(_ => _.Email)
                        .FirstOrDefaultAsync();

                    var groupDescription = await this.loggerContext.Set<CompanyGroupLink>()
                        .Where(_ => _.CompanyId == entity.CompanyId)
                        .Select(_ => _.CompanyGroup.GroupDescription)
                        .FirstOrDefaultAsync();

                    var companyFrequencyName = await this.loggerContext.Set<CompanyRunFrequency>()
                        .Where(_ => _.CompanyFrequencyId == entity.CompanyFrequencyId)
                        .Select(_ => _.FrequencyName)
                        .FirstOrDefaultAsync();

                    return new UserCompanyLinkDescription(companyTradingName, userEmail, groupDescription, companyFrequencyName);
                });
        }

        private async Task<string> GetCompanyLinkDescriptionAsync(UserCompanyLink entity)
        {
            var userCompanyLinkDescription = await this.GetUserCompanyLinkDescriptionAsync(entity);

            if (entity.IsUserProfile)
            {
                return $"{userCompanyLinkDescription?.companyTradingName} - {userCompanyLinkDescription?.companyFrequencyName}: {userCompanyLinkDescription?.email}";
            }

            return $"{userCompanyLinkDescription?.email}-{userCompanyLinkDescription?.companyTradingName}-{userCompanyLinkDescription?.groupDescription}";
        }
    }
}