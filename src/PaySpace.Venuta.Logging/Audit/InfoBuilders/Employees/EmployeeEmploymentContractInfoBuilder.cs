namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Employees
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class EmployeeEmploymentContractInfoBuilder : AuditInfoBuilderBase<EmployeeEmploymentContract>
    {
        private readonly LoggerContext loggerContext;

        public EmployeeEmploymentContractInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override async Task<object> GetAuditingValues(EmployeeEmploymentContract entity)
        {
            return new
            {
                entity.EmploymentStartDate,
                entity.ContractNumber,
                entity.ContractStartDate,
                entity.ProvisionalContractEndDate,
                entity.WorkplaceSIRET,
                entity.NotReEmployable,
                entity.ReplacedEmployeeNumber,
                entity.ContractModificationDate,
                entity.ExtendContract,
                entity.ModifyContract,
                entity.TerminateContract,
                entity.ContractEndDate,
                entity.EncashLeave,
                entity.EffectiveDate,
                entity.ReinstateContract,
                entity.EmployeeId,
                CompanyRun = await this.GetRunDescription(entity.RunId),
                ContractType = await this.GetContractType(entity.ContractTypeId),
                SpecialSponsoredContract = await this.GetSpecialSponsoredContract(entity.SpecialSponsoredContractId),
                ContractTypeGroup = await this.GetContractTypeGroup(entity.ContractTypeGroupId),
                Workplace = await this.GetWorkPlace(entity.WorkplaceId),
                CddReason = await this.GetCddReason(entity.CddReasonId),
                ContractEndReason = await this.GetContractEndReason(entity.ContractEndReasonId),
                HighestEducationLevel = await this.GetHighestEducationLevel(entity.HighestEducationLevelId),
                InProgressEducationLevel = await this.GetInProgressEducationLevel(entity.InProgressEducationLevelId),
            };
        }

        protected override long? GetAlternativeId(EmployeeEmploymentContract entity)
        {
            return entity.EmploymentContractId;
        }

        protected override Task<long?> GetEmployeeIdAsync(EmployeeEmploymentContract entity)
        {
            return Task.FromResult<long?>(entity.EmployeeId);
        }

        protected override Task<long?> GetCompanyIdAsync(EmployeeEmploymentContract entity)
        {
            return Task.FromResult<long?>(null);
        }

        private Task<string?> GetContractType(int contractTypeId)
        {
            return this.loggerContext.Set<EnumContractType>()
                .TagWithSource()
                .Where(ct => ct.ContractTypeId == contractTypeId)
                .Select(ct => ct.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetSpecialSponsoredContract(int specialSponsoredContractId)
        {
            return this.loggerContext.Set<EnumSpecialSponsoredContract>()
                .TagWithSource()
                .Where(ssc => ssc.SpecialSponsoredContractId == specialSponsoredContractId)
                .Select(ssc => ssc.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetContractTypeGroup(int contractTypeGroupId)
        {
            return this.loggerContext.Set<EnumContractTypeGroup>()
                .TagWithSource()
                .Where(ctg => ctg.ContractTypeGroupId == contractTypeGroupId)
                .Select(ctg => ctg.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetWorkPlace(long workplaceId)
        {
            return this.loggerContext.Set<OrganizationRegion>()
                .TagWithSource()
                .Where(wp => wp.RegionId == workplaceId)
                .Select(wp => wp.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetCddReason(int cddReasonId)
        {
            return this.loggerContext.Set<EnumCddReason>()
                .TagWithSource()
                .Where(cr => cr.CddReasonId == cddReasonId)
                .Select(cr => cr.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetContractEndReason(int contractEndReasonId)
        {
            return this.loggerContext.Set<EnumContractEndReason>()
                .TagWithSource()
                .Where(ce => ce.ContractEndReasonId == contractEndReasonId)
                .Select(ce => ce.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetHighestEducationLevel(int? highestEducationLevelId)
        {
            return this.loggerContext.Set<EnumHighestEducationLevel>()
                .TagWithSource()
                .Where(el => el.HighestEducationLevelId == highestEducationLevelId)
                .Select(el => el.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetInProgressEducationLevel(int? inProgressEducationLevelId)
        {
            return this.loggerContext.Set<EnumInProgressEducationLevel>()
                .TagWithSource()
                .Where(el => el.InProgressEducationLevelId == inProgressEducationLevelId)
                .Select(el => el.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetRunDescription(long? runId)
        {
            return this.loggerContext.Set<CompanyRun>()
                .TagWithSource()
                .Where(r => r.RunId == runId)
                .Select(r => r.RunDescription)
                .FirstOrDefaultAsync();
        }
    }
}