namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Employees
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditEntities;
    using PaySpace.Venuta.Logging.Audit.Models;

    internal class EmployeeInfoBuilder : EmployeeAuditEntityInfoBuilder<Employee>
    {

        public EmployeeInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
        }

        public override Task<AuditInfo?> GetAuditInfoAsync(AuditEntity audit, AuditActionType actionType)
        {
            if (audit.Entity is Employee employee && (employee.EmployeeHistoryId > 0 || employee.EffectiveDate != null) && actionType != AuditActionType.Edit)
            {
                return Task.FromResult<AuditInfo?>(null);
            }

            return base.GetAuditInfoAsync(audit, actionType);
        }

        public override Task<IEnumerable<FormattedModifiedProperty>> GetModifiedPropertiesAsync(AuditEntity data)
        {
            if (data.Entity is Employee employee && (employee.EmployeeHistoryId > 0 || employee.EffectiveDate != null))
            {
                // we only want to log custom fields if the employee is linked to history table.
                // other changes will be linked there
                data.ModifiedProperties = data.ModifiedProperties.Where(_ => _.IsCustomField).ToList();
            }

            return base.GetModifiedPropertiesAsync(data);
        }

        protected override object GetAuditingValues(Employee entity)
        {
            return new
            {
                entity.EmployeeNumber
            };
        }

        protected override long? GetAlternativeId(Employee entity)
        {
            return entity.EmployeeId;
        }
    }
}