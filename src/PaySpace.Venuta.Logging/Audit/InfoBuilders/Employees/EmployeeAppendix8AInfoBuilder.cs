namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Employees
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Text;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Logging.Abstractions;

    internal sealed class EmployeeAppendix8AInfoBuilder : EmployeeAuditEntityInfoBuilder<EmployeeAppendix8A>
    {
        private readonly LoggerContext loggerContext;

        public EmployeeAppendix8AInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected override async Task<object> GetAuditingValuesAsync(EmployeeAppendix8A entity)
        {
            return new
            {
                entity.OccupationPeriodStartDate,
                entity.OccupationPeriodEndDate,
                entity.AddressLine1,
                entity.AddressLine2,
                entity.AddressLine3,
                entity.NumberOfEmployeesSharing,
                entity.AnnualValuePremises,
                entity.ValueFurnitureFitting,
                entity.RentPaidToLandlord,
                entity.TaxableValuePlaceOfResidence,
                entity.RentPaidByEmployee,
                entity.UtilitiesCosts,
                entity.DriverCosts,
                entity.ServantCosts,
                entity.TaxableValueUtilitiesHousekeeping,
                entity.CostHotelAccommodation,
                entity.HotelAmountPaidByEmployee,
                entity.TaxableValueHotelAccommodation,
                entity.TotalAccommodationBenefit,
                TaxYear = await this.GetTaxYearDescription(entity),
                FurnitureFittingOption = await this.GetFurnitureFittingOptionDescription(entity)
            };
        }

        protected override long? GetAlternativeId(EmployeeAppendix8A entity)
        {
            return entity.EmployeeAppendix8AId;
        }

        private async Task<string?> GetTaxYearDescription(EmployeeAppendix8A entity)
        {
            return await this.loggerContext.Set<CountryTaxYear>()
                .Where(_ => _.TaxYearId == entity.TaxYearId)
                .Select(_ => _.YearStartDate.Year.ToString())
                .FirstOrDefaultAsync();
        }

        private async Task<string?> GetFurnitureFittingOptionDescription(EmployeeAppendix8A entity)
        {
            if (entity.FurnitureFittingOptionID == null)
            {
                return null;
            }

            return await this.loggerContext.Set<EnumFurnitureFittingOption>()
                .Where(_ => _.FurnitureFittingOptionId == entity.FurnitureFittingOptionID)
                .Select(_ => _.FurnitureFittingOptionDescription)
                .FirstOrDefaultAsync();
        }
    }
}