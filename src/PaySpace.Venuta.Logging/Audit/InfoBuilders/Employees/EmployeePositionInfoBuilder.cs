namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Employees
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Logging.Abstractions;

    internal class EmployeePositionInfoBuilder : EmployeeAuditEntityInfoBuilder<EmployeePosition>
    {
        private readonly LoggerContext loggerContext;

        public EmployeePositionInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.loggerContext = loggerContext;
        }

        protected async override Task<object> GetAuditingValuesAsync(EmployeePosition entity)
        {
            var position = await this.GetPositionDescriptionAsync(entity);
            var grade = await this.GetGradeCodeAsync(entity);
            var orgunit = await this.GetOrgUnitDescriptionAsync(entity);
            var region = await this.GetRegionAsync(entity);
            var paypoint = await this.GetPaypointDescriptionAsync(entity);
            var directlyReportsTo = await this.GetDirectlyReportsToAsync(entity);
            var uploadCode = await this.GetUploadCodeAsync(entity);

            return new
            {
                Position = position,
                Grade = grade,
                entity.OrganizationPositionId,
                entity.GradeId,
                entity.EffectiveDate,
                entity.DirectlyReportsPositionOverrideId,
                entity.OrganizationGroupId,
                OrganizationGroup = orgunit,
                UploadCode = uploadCode,
                Region = region,
                entity.OrganizationRegionId,
                entity.DirectlyReportsEmployeeId,
                entity.AdministratorId,
                entity.JobId,
                entity.TradeUnion,
                entity.EmploymentCategory,
                entity.EmploymentSubCategory,
                entity.RosterId,
                entity.WorkflowRole,
                entity.PositionTypeId,
                Paypoint = paypoint,
                DirectlyReportsToPerson = directlyReportsTo,
            };
        }

        protected override string GetDescription(EmployeePosition entity)
        {
            return entity.EffectiveDate.ToShortDateString();
        }

        protected override long? GetAlternativeId(EmployeePosition entity)
        {
            return entity.EmployeePositionId;
        }

        private Task<string?> GetUploadCodeAsync(EmployeePosition entity)
        {
            return this.loggerContext.Set<OrganizationGroup>()
                .Where(_ => _.OrganizationGroupId == entity.OrganizationGroupId)
                .Select(_ => _.UploadCode)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetGradeCodeAsync(EmployeePosition entity)
        {
            return this.loggerContext.Set<OrganizationGrade>()
                .Where(_ => _.GradeId == entity.GradeId)
                .Select(_ => _.Code)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetPositionDescriptionAsync(EmployeePosition entity)
        {
            return this.loggerContext.Set<OrganizationPosition>()
                .Where(_ => _.OrganizationPositionId == entity.OrganizationPositionId)
                .Select(_ => _.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetOrgUnitDescriptionAsync(EmployeePosition entity)
        {
            return this.loggerContext.Set<OrganizationGroup>()
                .Where(_ => _.OrganizationGroupId == entity.OrganizationGroupId)
                .Select(_ => _.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetDirectlyReportsToAsync(EmployeePosition entity)
        {
            return this.loggerContext.Set<Employee>()
                .Where(_ => _.EmployeeId == entity.DirectlyReportsEmployeeId)
                .Select(_ => _.FullName)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetRegionAsync(EmployeePosition entity)
        {
            return this.loggerContext.Set<OrganizationRegion>()
                .Where(_ => _.RegionId == entity.OrganizationRegionId)
                .Select(_ => _.Description)
                .FirstOrDefaultAsync();
        }

        private Task<string?> GetPaypointDescriptionAsync(EmployeePosition entity)
        {
            return this.loggerContext.Set<OrganizationPayPoint>().Where(_ => _.PayPointId == entity.PayPointId)
                .Select(_ => _.Description)
                .FirstOrDefaultAsync();
        }
    }
}