namespace PaySpace.Venuta.Logging.Audit.InfoBuilders.Employees
{
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Modules.RPN.Abstractions.Models;

    internal sealed class EmployeeRpnInfoBuilder : AuditInfoBuilderBase<EmployeeRpn>
    {
        private readonly IAuditTrailService auditService;

        public EmployeeRpnInfoBuilder(IAuditTrailService auditService, LoggerContext loggerContext)
            : base(auditService, loggerContext)
        {
            this.auditService = auditService;
        }

        protected override string GetLocalizationTableName(EmployeeRpn entity)
        {
            return this.auditService.GetTableName(typeof(Employee));
        }

        protected override object GetAuditingValues(EmployeeRpn entity)
        {
            return new
            {
                entity.RpnNumber,
                entity.RpnIssueDate,
                entity.RpnFirstName,
                entity.RpnFamilyName,
                entity.EmployeePpsn,
                entity.EmployerReference,
                entity.RpnEffectiveDate,
                entity.EndDate,
                entity.SourceType,
                entity.EffectiveDate,
            };
        }

        protected override string GetDescription(EmployeeRpn entity)
        {
            return $"Employee: {entity.RpnFirstName} {entity.RpnFamilyName} - Rpn Number: {entity.RpnNumber}";
        }

        protected override long? GetAlternativeId(EmployeeRpn entity)
        {
            return entity.EmployeeRpnId;
        }

        protected override async Task<long?> GetEmployeeIdAsync(EmployeeRpn entity)
        {
            if (entity.EmployeeId == default || !await this.auditService.IsValidEmployeeIdAsync(entity.EmployeeId))
            {
                throw new EmployeeNotFoundException(nameof(EmployeeRpn));
            }

            return entity.EmployeeId;
        }

        protected override Task<long?> GetCompanyIdAsync(EmployeeRpn entity)
        {
            return Task.FromResult<long?>(null);
        }
    }
}