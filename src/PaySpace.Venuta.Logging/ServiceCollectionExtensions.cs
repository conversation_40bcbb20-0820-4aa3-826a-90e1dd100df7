namespace PaySpace.Venuta.Logging
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Venuta.Excel;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Logging.Audit.Abstractions;
    using PaySpace.Venuta.Logging.Audit.AuditHandlers;
    using PaySpace.Venuta.Logging.Audit.AuditPublishers;
    using PaySpace.Venuta.Logging.Audit.Factories;
    using PaySpace.Venuta.Logging.Audit.InfoBuilders.Components;
    using PaySpace.Venuta.Logging.Audit.Services;
    using PaySpace.Venuta.Logging.Audit.Services.Agency;
    using PaySpace.Venuta.Logging.Audit.Services.Bureau;
    using PaySpace.Venuta.Logging.Audit.Services.Company;
    using PaySpace.Venuta.Logging.Audit.Services.Employee;
    using PaySpace.Venuta.Logging.MessageHandlers;
    using PaySpace.Venuta.Messaging;

    public static class ServiceCollectionExtensions
    {
        public static LoggingServiceBuilder AddLoggingServices(this IServiceCollection services, string connectionString)
        {
            services.AddDbContext<LoggerContext>(options => options.UseSqlServer(connectionString, sql => sql.EnableRetryOnFailure()));

            services.AddScoped<IAuditTrailService, AuditTrailService>();
            services.AddScoped<IAuditTrailHeaderService, AuditTrailHeaderService>();

            services.AddScoped<ISearchRequestFactory, SearchRequestFactory>();
            services.AddScoped<IAuditSearchServiceFactory, AuditSearchServiceFactory>();
            services.AddKeyedScoped<IAuditSearchService, EmployeeAuditService>("Employee");
            services.AddKeyedScoped<IAuditSearchService, EmployeePayslipAuditService>("Employee-Payslip");
            services.AddKeyedScoped<IAuditSearchService, EmployeeComponentAuditService>("Employee-Components");
            services.AddKeyedScoped<IAuditSearchService, EmployeeEvaluationHistoryAuditService>("Employee-Evaluation-History");

            services.AddKeyedScoped<IAuditSearchService, CompanyAuditService>("Company");
            services.AddKeyedScoped<IAuditSearchService, CompanySettingsAuditService>("Company-Settings");

            services.AddKeyedScoped<IAuditSearchService, AgencyAuditService>("Agency");
            services.AddKeyedScoped<IAuditSearchService, BureauAuditService>("Bureau");

            return new LoggingServiceBuilder(services);
        }

        private static IServiceCollection AddAuditInfoBuilderTypes(this IServiceCollection services)
        {
            foreach (var type in GetAuditInfoBuilderTypes())
            {
                services.AddScoped(typeof(IAuditInfoBuilder), type);
            }

            services.AddScoped<IAuditInfoBuilderFactory, AuditInfoBuilderFactory>();

            return services;
        }

        private static IEnumerable<Type> GetAuditInfoBuilderTypes()
        {
            return typeof(ServiceCollectionExtensions).Assembly.GetTypes().Where(type => !type.IsInterface && !type.IsAbstract && typeof(IAuditInfoBuilder).IsAssignableFrom(type));
        }

        public class LoggingServiceBuilder
        {
            private readonly IServiceCollection services;

            public LoggingServiceBuilder(IServiceCollection services)
            {
                this.services = services;
            }

            public LoggingServiceBuilder AddPublishers()
            {
                this.services.AddSingleton<IInfoBuilderRegistry, InfoBuilderRegistry>();

                // Messaging.
                this.services.AddScoped<IAuditPublisherService, AuditPublisherService>();
                this.services.AddScoped<IAuditPublisherFactory, AuditPublisherFactory>();

                this.services.AddScoped<IInsertAuditPublisher, InsertAuditPublisher>();
                this.services.AddScoped<IDeletedAuditPublisher, DeletedAuditPublisher>();
                this.services.AddScoped<IEditAuditPublisher, EditAuditPublisher>();

                // Add Services required for loan component.
                // Loan Components needs to be audited immediately. Calc service requires audit to function properly.
                // Audit publishers is responsible for logging loan.
                this.services.AddScoped<IAuditInfoBuilderFactory, AuditInfoBuilderFactory>();
                this.services.AddScoped<IAuditInfoBuilder, EmployeeLoanInfoBuilder>();
                this.services.AddScoped<IAuditInfoBuilder, EmployeeSavingInfoBuilder>();

                // Audit Handlers
                this.services.AddScoped<IEditAuditHandler, EditAuditHandler>();
                this.services.AddScoped<IInsertAuditHandler, InsertAuditHandler>();
                this.services.AddScoped<IDeleteAuditHandler, DeleteAuditHandler>();

                return this;
            }

            public void AddMessageHandlers()
            {
                this.services.AddAuditInfoBuilderTypes();
                this.services.AddScoped<IInfoBuilderRegistry, InfoBuilderRegistry>();

                // Messaging.
                this.services.AddSingleton<AuditMessageHandler>();
                this.services.AddMessageHandler<AuditMessageHandler>();
                this.services.AddMessageHandler<AuditEmployeeDeleteHandler>();

                // Audit Handlers
                this.services.AddScoped<IAuditHandlerFactory, AuditHandlerFactory>();
                this.services.AddScoped<IEditAuditHandler, EditAuditHandler>();
                this.services.AddScoped<IInsertAuditHandler, InsertAuditHandler>();
                this.services.AddScoped<IDeleteAuditHandler, DeleteAuditHandler>();

                this.services.AddScoped<IAuditTrailHeaderService, AuditTrailHeaderService>();
                this.services.AddScoped<IAuditTrailService, AuditTrailService>();
            }
        }
    }
}