namespace PaySpace.Venuta.Modules.Components.Abstractions
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Formula;
    using PaySpace.Venuta.Modules.Components.Abstractions.Models;
    using PaySpace.Venuta.Modules.Components.Models;
    using PaySpace.Venuta.Services.Abstractions;

    public interface ICompanyComponentService : IGenericService<ComponentCompany>
    {
        Task<CompanyComponentResult> GetComponentResultAsync(long companyComponentId, long frequencyId);

        Task<List<ActiveComponentCompanyResult>> GetSpecialComponentsAsync(int specialComponentId, long frequencyId, long runId);

        List<ActiveComponentCompanyResult> GetSpecialComponents(int specialComponentId, long frequencyId, long runId);

        IQueryable<CompanyComponentTemplate> GetRecurringTemplates(long frequencyId);

        Task<IList<ComponentCompanyChildrenInformationResult>> GetChildComponents(long componentCompanyId);

        Task<bool?> PartOfPackageAsync(long componentCompanyId);

        Task<bool> IsCompanyComponentActive(long companyComponentId, long companyRunId);

        Task<List<long>> GetComponentIdsBySpecialComponentAsync(long specialComponentId, long companyFrequencyId);

        Task<string> GetComponentFieldCodeAsync(long componentCompanyId);

        Task<FormulaTables> GetComponentFormulaTableAsync(long componentCompanyId);

        IQueryable<ComponentCompany> GetByComponentsFieldCode(long frequencyId, string fieldCode);

        Task<string> GetComponentAliasDescriptionAsync(long companyComponentId);

        IQueryable<ComponentCompany> GetNetpayDeductionNoteComponentsByFrequency(long companyFrequencyId);

        IQueryable<ComponentCompany> GetComponentsByFormulaLineCode(long companyFrequency, string formulaLineCode);

        Task<bool> HasSpecialComponent(long companyFrequencyId, string specialComponentCode);

        Task<long> GetCompanyIdAsync(long componentCompanyId);

        Task<bool> HasUniqueComponentCodeOrDescriptionAsync(long frequencyId, long runId, long? companyComponentId);

        Task<ComponentCompanyInfoResult> GetComponentCompanyIdByDescriptionAsync(long frequencyId, long runId, string componentCodeDescription);

        ComponentCompanyInfoResult GetComponentCompanyIdByDescriptionOrCode(long frequencyId, long runId, string componentCodeDescription);

        Task<long?> GetComponentCompanyIdByEmployeeComponentAsync(long? employeeComponentId);

        PayslipAction GetPayslipAction(long companyComponentId);

        Task<PayslipAction> GetPayslipActionAsync(long companyComponentId);

        Task<bool> IsComponentReadonlyAsync(long componentCompanyId);

        Task<bool> IsComponentActiveForSpecifiedRunAsync(long componentCompanyId, long frequencyId, long runId);

        Task<bool> IsBasicSalarySpecialComponentAsync(long companyComponentId);

        bool IsBasicSalarySpecialComponent(long companyComponentId);

        bool IsLumpSumSpecialComponent(long companyComponentId);

        Task<bool> IsLeaveEncashmentComponentAsync(long? companyComponentId);

        List<ActiveComponentCompanyResult> GetAllActiveCompanyComponents(long frequencyId, long runId);

        Task<List<ActiveComponentCompanyResult>> GetAllActiveCompanyComponentsAsync(long frequencyId, long runId, bool skipRecurringDisplayCheck = false);

        Task<ActiveComponentCompanyResult> GetRecurringCompanyComponentAsync(long componentCompanyId, long frequencyId, long runId);

        Task<List<CompanyComponentPayslipActionResult>> GetComponentsByPayslipActionAsync(
            long countryId,
            long companyFrequencyId,
            PayslipAction payslipAction,
            CompanyRunPeriodResult companyRunPeriod,
            bool includeInactive);

        Task<(int? ComponentOrderNumber, int? CategoryOrderNumber, string specialComponentCode)> GetComponentCategoryAsync(long companyFrequencyId, long componentId);

        Task<long?> GetParentComponentIdAsync(long companyComponentId);

        Task<long> GetComponentIdBySpecialComponentCodeAsync(long companyFrequencyId, string specialComponentCode);

        Task<bool> HasUniqueComponentCodeAsync(long frequencyId, long companyComponentId, string componentCode);

        Task<bool> HasUniqueDescriptionAsync(long frequencyId, long companyComponentId, string description);

        Task<List<ComponentCompanyInfoResult>> GetCompanyGroupComponentsListAsync(long companyGroupId);

        Task<bool> DisallowDeleteEmployeeRecurringAsync(long employeeComponentId);
    }
}