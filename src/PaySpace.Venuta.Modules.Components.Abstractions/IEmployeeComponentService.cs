namespace PaySpace.Venuta.Modules.Components.Abstractions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Modules.Components.Models;

    public interface IEmployeeComponentService : IGenericService<ComponentEmployee>
    {
        Task<ComponentEmployee> CreateComponentEmployeeFromComponentCompany(long employeeId, long componentCompanyId);

        Task<IList<EmployeeComponentResult>> GetComponentResultsAsync(
            long employeeId,
            DateTime runStartDate,
            DateTime runEndDate,
            int countryId);

        Task<IList<string>> GetArrearsComponentsAsync(
            long employeeId,
            DateTime runStartDate,
            DateTime runEndDate);

        Task<ComponentEmployee> GetSpecialComponentAsync(long employeeId, long frequencyId, string componentCode);

        Task<IList<EmployeeComponentProjectedResult>> GetProjectedTotals(long runId, long employeeId);

        Task<long?> GetIdByCompanyComponentIdAsync(long employeeId, long componentCompanyId);

        long GetIdByCompanyComponent(long employeeId, long componentCompanyId);

        Task<IList<EmployeeComponentResult>> GetChildComponentsAsync(long employeeComponentId);

        Task<IList<ComponentEmployee>> GetChildComponentEmployeeAsync(long employeeComponentId);

        Task<bool> HasComponentWithTaxCodeAsync(long employeeId, string taxCode);

        Task<ComponentEmployee> GetComponentAsync(long employeeId, long componentCompanyId);

        Task<ComponentEmployee> GetComponentByCompanyComponentIdAsync(long employeeId, long companyComponentId);

        long GetEmployeeComponentId(long employeeId, long companyComponentId);

        Task<ComponentEmployee> UpsertAsync(long frequencyId, long runId, long employeeId, long companyComponentId);

        IQueryable<ComponentEmployee> GetComponentsByEmployeeComponentId(long employeeComponentId);

        IQueryable<ComponentEmployee> GetComponentsByEmployeeId(long employeeId);

        Task<bool> IsBasSalSpecialComponentAsync(long employeeComponentId);
    }
}