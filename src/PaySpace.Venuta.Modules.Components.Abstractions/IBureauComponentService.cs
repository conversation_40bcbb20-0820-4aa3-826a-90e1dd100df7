namespace PaySpace.Venuta.Modules.Components.Abstractions
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.Components;

    public interface IBureauComponentService
    {
        IQueryable<ComponentBureau> GetComponentAsync(long componentCompanyId);

        IQueryable<ComponentBureau> GetComponentAsync(long categoryId, long countryId);

        IQueryable<ComponentBureau> GetComponentsByCountryId(long countryId);

        IQueryable<ComponentBureau> GetCalcStatutoryComponentsByCountryId(long countryId);

        Task<List<ComponentBureau>> GetStatutoryComponentsNotInFrequencyAsync(long countryId, long companyFrequencyId);

        Task<List<BureauChildComponents>> GetChildComponentsAsync(List<long> bureauComponentIds);

        Task<bool> HasDependenciesAsync(long tableBuilderCategoryId);

        IQueryable<ComponentBureau> GetAllowanceComponentsByCountryId(long countryId);

        Task<bool> IsStatutoryOrAutomaticComponentAsync(long componentCompanyId);
    }

    public record BureauChildComponents(long ComponentBureauId, List<ComponentBureau> childComponent);
}