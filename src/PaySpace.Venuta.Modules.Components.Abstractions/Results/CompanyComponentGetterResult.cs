namespace PaySpace.Venuta.Modules.Components.Abstractions.Results
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Data.Models.Enums;

    public class CompanyComponentGetterResult
    {
        [Key]
        [Column("pkComponentCompanyID")]
        public long ComponentCompanyId { get; set; }

        public long CompanyFrequencyId { get; set; }

        public string ComponentBureau { get; set; }

        public long ComponentBureauId { get; set; }

        public string? ComponentName { get; set; }

        public string? Formula { get; set; }

        public string? Description { get; set; }

        public bool? InPackage { get; set; }

        public string? ComponentCode { get; set; }

        public decimal? MinValue { get; set; }

        public decimal? MaxValue { get; set; }

        public string? ActiveFromMonth { get; set; }

        public string? ActiveToMonth { get; set; }

        public RecoveryRule? AutoRecoveryType { get; set; }

        public decimal? MaxRatioRecovery { get; set; }

        public string? MultiplyByComponentCompany { get; set; }

        public bool? RecoverFigures { get; set; }

        public bool? Inactive { get; set; }

        public bool? AddToEmployee { get; set; }

        public bool? ProRata { get; set; }

        public bool? DoNotConvertCurrency { get; set; }

        public string? Currency { get; set; }

        public bool? DoNotShowOnPayslip { get; set; }

        public string? LanguageAliasDescription { get; set; }

        public string? OverridingTaxCode { get; set; }

        public bool? OnceOffValuesActSameAsPackage { get; set; }

        public bool? ExclComponentRetrospective { get; set; }

        public bool? IsCostToCompany { get; set; }

        public bool? ShowOnMockPayslipOnly { get; set; }

        public bool? HidePayslipComments { get; set; }

        public bool? RetroDoNotCalc { get; set; }

        public bool? CalcRegardlessOfBasicPosted { get; set; }

        public long? CompanyRunId { get; set; }

        public int? OverrideDecimalPlaces { get; set; }

        public string? PayslipMessage { get; set; }

        public string? RunsToCompleteTheComponentIn { get; set; }

        public long? IndicatorId { get; set; }

        public string? IndicatorDescription { get; set; }

        [ForeignKey(nameof(this.ComponentCompanyId))]
        public virtual ICollection<CompanyComponentValueResult> ComponentValues { get; set; }

        [ForeignKey(nameof(this.ComponentCompanyId))]
        public virtual ICollection<CompanyComponentIndicatorResult> IndicatorLines { get; set; }
    }
}