namespace PaySpace.Venuta.Modules.Components.Abstractions.Results
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("vw_api_companyComponentValues")]
    public class CompanyComponentValueResult
    {
        [Key]
        public long ComponentValueId { get; set; }

        public string Description { get; set; }

        public decimal? ComponentValue { get; set; }

        public long ComponentCompanyId { get; set; }

        public int ValueTypeId { get; set; }

        public bool Active { get; set; }

        public long? CompanyRunId { get; set; }
    }
}