namespace PaySpace.Venuta.Modules.Components.Abstractions.Results
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("vw_api_companyComponentIndicatorLines")]
    public class CompanyComponentIndicatorResult
    {
        [Key]
        public long IndicatorLineId { get; set; }

        public long ComponentCompanyId { get; set; }

        public long IndicatorId { get; set; }

        public string? IndicatorLineDescription { get; set; }

        public decimal? IndicatorLineValue { get; set; }
    }
}