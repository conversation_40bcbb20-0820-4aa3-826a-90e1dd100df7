namespace PaySpace.Venuta.Modules.Components.Abstractions.Models
{
    using System.ComponentModel.DataAnnotations.Schema;

    public class CompanyComponentPayslipActionResult
    {
        [Column("pkComponentCompanyID")]
        public long ComponentId { get; set; }

        public string AliasDescription { get; set; }

        public bool? InPackage { get; set; }

        public decimal CustomNumbering { get; set; }

        public string ComponentDescription { get; set; }

        public string TaxCode { get; set; }

        public string TaxCodeDescription { get; set; }

        public string FormulaDefinition { get; set; }

        public bool DisplayReadOnlyComp { get; set; }

        public bool Active { get; set; }

        public string OriginalTaxCode { get; set; }

        public bool IsLegislative { get; set; }

        public bool DisallowCompanyExemption { get; set; }

        public bool HasSubCodes { get; set; }
    }
}