namespace PaySpace.Venuta.Modules.Components.Services
{
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Modules.Components.Abstractions;

    public class PensionFundService(ApplicationContext applicationContext) : IPensionFundService
    {
        public Task<bool> HasCompanyPensionFundSetUpAsync(long companyId)
        {
            return applicationContext
                .Set<CompanyPensionFund>()
                .AsNoTracking()
                .AnyAsync(_ => _.CompanyId == companyId);
        }
    }
}