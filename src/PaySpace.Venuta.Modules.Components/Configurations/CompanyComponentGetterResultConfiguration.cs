namespace PaySpace.Venuta.Modules.Components.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    using PaySpace.Venuta.Modules.Components.Abstractions.Results;

    public class CompanyComponentGetterResultConfiguration : IEntityTypeConfiguration<CompanyComponentGetterResult>
    {
        public void Configure(EntityTypeBuilder<CompanyComponentGetterResult> builder)
        {
            builder.HasMany<CompanyComponentIndicatorResult>()
                .WithOne()
                .HasPrincipalKey(_ => _.IndicatorId)
                .HasForeignKey(_ => _.IndicatorId);
        }
    }
}