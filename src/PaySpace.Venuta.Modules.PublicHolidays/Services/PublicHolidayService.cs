namespace PaySpace.Venuta.Modules.PublicHolidays.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions.Models;
    using PaySpace.Venuta.Services;

    public class PublicHolidayService : GenericService<PublicHoliday>, IPublicHolidayService
    {
        private readonly ApplicationContext context;
        private readonly IDistributedCache distributedCache;

        public PublicHolidayService(
            ApplicationContext context,
            IDistributedCache distributedCache,
            IDbContextRepository<PublicHoliday> repository)
            : base(repository)
        {
            this.context = context;
            this.distributedCache = distributedCache;
        }

        public IQueryable<PublicHoliday> GetPublicHolidays(string taxCountryCode)
        {
            return from hol in this.context.Set<PublicHoliday>().TagWithSource().AsNoTracking()
                   join enumAllCountries in this.context.Set<EnumCountry>()
                       on hol.TaxCountry!.Iso3DigitCode equals enumAllCountries.Iso3DigitCode
                   where enumAllCountries.Iso2DigitCode == taxCountryCode
                   select hol;
        }

        public virtual async Task<IList<DateTime>> GetPublicHolidaysAsync(PublicHolidayRequest model)
        {
            var holidays = new List<DateTime>();
            if (model.Info.PublicHolidayCategoryId.HasValue)
            {
                var categoryLinkedHolidays = await this.GetCategoryLinkedHolidaysAsync(model.Info.PublicHolidayCategoryId);
                holidays = holidays.Union(categoryLinkedHolidays).ToList();

                // Only return holidays linked to that category if the override-flag is true
                var overrideDefaultHoliday = this.context.Set<PublicHolidayCategory>().TagWithSource()
                    .Any(_ => _.PublicHolidayCategoryId == model.Info.PublicHolidayCategoryId &&
                              _.OverrideDefaultHoliday == true &&
                              _.CompanyId == model.Info.CompanyId);

                if (overrideDefaultHoliday)
                {
                    return holidays;
                }
            }

            var provincialHolidays = await this.GetProvincialHolidaysAsync(model.Info.ProvinceId, model.Info.CompanyId, model.TaxCountryId);
            var nationalHolidays = await this.GetNationalHolidaysAsync(model.TaxCountryId, model.Info.CompanyId);
            holidays = holidays.Union(nationalHolidays).ToList();

            return provincialHolidays.Any()
                ? holidays.Union(provincialHolidays).ToList()

                // If no Provincial holidays are setup for the user, return National + Categorical (if applicable)
                : holidays;
        }

        public async Task<int> GetPublicHolidayTotalAsync(PublicHolidayTotalRequest model)
        {
            var publicHolidays = await this.GetPublicHolidaysAsync(
                new PublicHolidayRequest(model.Info, model.CountryId));

            return publicHolidays.Count(_ => _.Date >= model.StartDate && _.Date <= model.EndDate);
        }

        public async Task<bool> IsPublicHolidayAsync(IsPublicHolidayRequest model)
        {
            var publicHolidays = await this.GetPublicHolidaysAsync(
                new PublicHolidayRequest(model.Info, model.TaxCountryId));

            return publicHolidays.Any(d => d == model.Date.Date);
        }

        public Task<int?> GetProvinceIdAsync(long employeeId)
        {
            // used for Leave Module
            return this.context.Set<Address>().TagWithSource()
                .Where(_ => _.EmployeeId == employeeId && _.AddressType == AddressType.Physical)
                .Select(_ => _.ProvinceId)
                .FirstOrDefaultAsync();
        }

        public Task<long?> GetPublicHolidayCategoryAsync(long? companySchemeId, long employeeId)
        {
            // used for Leave Module - find the active leave setup and return the public-holiday-category (if applicable)
            return this.context.Set<EmployeeLeaveSetup>().TagWithSource()
                .Where(_ => _.CompanyLeaveSchemeId == companySchemeId &&
                            _.EffectiveDate <= DateTime.Today &&
                            _.EmployeeId == employeeId)
                .OrderByDescending(_ => _.EffectiveDate)
                .Select(_ => _.PublicHolidayCategoryId)
                .FirstOrDefaultAsync();
        }

        protected async Task<IList<DateTime>> GetCategoryLinkedHolidaysAsync(long? publicHolidayCategoryId)
        {
            return await this.distributedCache.GetOrCreateAsync(
                CacheKeys.CompanyCategoryPublicHolidays(publicHolidayCategoryId),
                () => this.context.Set<CompanyPublicHolidayCategory>().TagWithSource()
                    .Where(_ => _.PublicHolidayCategoryId == publicHolidayCategoryId)
                    .Select(_ => _.CompanyPublicHoliday.HolidayDate)
                    .ToListAsync());
        }

        protected async Task<IList<DateTime>> GetNationalHolidaysAsync(long countryId, long companyId)
        {
            var bureauNationalHolidays = await this.distributedCache.GetOrCreateAsync(
                CacheKeys.BureauNationalPublicHolidays(countryId),
                () => this.context.Set<PublicHoliday>().TagWithSource()
                    .Where(_ => _.TaxCountryId == countryId && _.PublicHolidayLevel == PublicHolidayLevel.National)
                    .Select(_ => _.HolidayDate)
                    .ToListAsync());

            var companyNationalHolidays = await this.distributedCache.GetOrCreateAsync(
                CacheKeys.CompanyNationalPublicHolidays(companyId),
                () => this.context.Set<CompanyPublicHoliday>().TagWithSource()
                    .Where(_ => _.CompanyId == companyId && _.PublicHolidayLevel == PublicHolidayLevel.National)
                    .Select(_ => _.HolidayDate)
                    .ToListAsync());

            return bureauNationalHolidays.Union(companyNationalHolidays).ToList();
        }

        private async Task<IList<DateTime>> GetProvincialHolidaysAsync(int? provinceId, long companyId, int countryId)
        {
            var bureauProvincialHolidays = await this.distributedCache.GetOrCreateAsync(
                CacheKeys.BureauProvincialPublicHolidays(countryId, provinceId),
                () => this.context.Set<PublicHolidayProvinces>().TagWithSource()
                    .Where(_ => _.ProvinceId == provinceId && _.PublicHoliday.TaxCountryId == countryId)
                    .Select(_ => _.PublicHoliday.HolidayDate)
                    .ToListAsync());

            var companyProvincialHolidays = await this.distributedCache.GetOrCreateAsync(
                CacheKeys.CompanyProvincialPublicHolidays(companyId, provinceId),
                () => this.context.Set<PublicHolidayProvinces>().TagWithSource()
                    .Where(_ => _.ProvinceId == provinceId && _.CompanyPublicHoliday.CompanyId == companyId)
                    .Select(_ => _.CompanyPublicHoliday.HolidayDate)
                    .ToListAsync());

            return bureauProvincialHolidays.Union(companyProvincialHolidays).ToList();
        }
    }
}