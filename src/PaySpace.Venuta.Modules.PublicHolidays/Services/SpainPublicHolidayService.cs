namespace PaySpace.Venuta.Modules.PublicHolidays.Services
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions;
    using PaySpace.Venuta.Modules.PublicHolidays.Abstractions.Models;
    using PaySpace.Venuta.Services.Abstractions;

    [CountryService(CountryCode.ES)]
    public class SpainPublicHolidayService : PublicHolidayService
    {
        private readonly ICustomFieldService customFieldService;
        private readonly IEmployeeProfileService employeeProfileService;
        private readonly ApplicationContext context;
        private readonly IEnumService enumService;

        public SpainPublicHolidayService(
            ApplicationContext context,
            IDistributedCache distributedCache,
            IDbContextRepository<PublicHoliday> repository,
            ICustomFieldService customFieldService,
            IEmployeeProfileService employeeProfileService,
            IEnumService enumService)
            : base(context, distributedCache, repository)
        {
            this.customFieldService = customFieldService;
            this.employeeProfileService = employeeProfileService;
            this.context = context;
            this.enumService = enumService;
        }

        public override async Task<int?> GetProvinceIdAsync(long employeeId, long companyId)
        {
            var bankHolidayOption = await this.GetEmployeeCustomFieldValueAsync(
                companyId,
                employeeId,
                SpainConstants.PublicHolidayOptionFieldCode);

            if (bankHolidayOption == SpainConstants.FollowWorkLocationValue)
            {
                return await base.GetProvinceIdAsync(employeeId, companyId);
            }

            var provinceCode = await this.employeeProfileService.GetEmployeeRegionCustomFieldCodeAsync(
                companyId,
                employeeId,
                SpainConstants.ProvinceFieldCode);

            var provinceId = this.enumService.GetAllProvinces()
                .Where(p => p.ProvinceCode == provinceCode && p.AddressCountryId == SpainConstants.AddressCountryId)
                .Select(p => p.ProvinceId)
                .FirstOrDefault();

            return provinceId == 0 ? null : provinceId;
        }

        private async Task<string> GetEmployeeCustomFieldValueAsync(long companyId, long employeeId, string fieldCode)
        {
            var customField = await this.customFieldService.GetCustomFieldAsync(companyId, "Employee", null, fieldCode);

            var customFieldValue = await this.context.Set<EmployeeCustomFieldValue>()
                .TagWithSource()
                .Where(cf => cf.EmployeeId == employeeId && cf.CustomFieldId == customField.CustomFieldId)
                .OrderByDescending(cf => cf.EffectiveDate)
                .Select(cf => cf.Code)
                .FirstOrDefaultAsync();

            return customFieldValue ?? string.Empty;
        }
    }
}