namespace PaySpace.Venuta.Api.Converters
{
    using System;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.DependencyInjection;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Company;

    public class CompanyCustomFormConverter : JsonConverter<CompanyCustomFormDto>
    {
        private readonly IHttpContextAccessor contextAccessor;
        private readonly ITenantProvider tenantProvider;

        public CompanyCustomFormConverter(IHttpContextAccessor contextAccessor, ITenantProvider tenantProvider)
        {
            this.contextAccessor = contextAccessor;
            this.tenantProvider = tenantProvider;
        }

        public override bool CanRead => true;

        public override bool CanWrite => false;

        public override void WriteJson(JsonWriter writer, CompanyCustomFormDto value, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }

        public override CompanyCustomFormDto ReadJson(JsonReader reader, Type objectType, CompanyCustomFormDto existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            var json = JObject.Load(reader);

            existingValue ??= new CompanyCustomFormDto();

            existingValue.EffectiveDate = json.GetValue("EffectiveDate", StringComparison.InvariantCultureIgnoreCase)?.ToObject<DateTime?>(serializer);
            existingValue.CustomFormCategoryCode = json.GetValue("CustomFormCategoryCode", StringComparison.InvariantCultureIgnoreCase)?.ToObject<string?>(serializer);
            existingValue.ProjectCode = json.GetValue("ProjectCode", StringComparison.InvariantCultureIgnoreCase)?.ToObject<string?>(serializer);
            existingValue.Code = json.GetValue("Code", StringComparison.InvariantCultureIgnoreCase)?.ToObject<string?>(serializer);
            existingValue.Description = json.GetValue("Description", StringComparison.InvariantCultureIgnoreCase)?.ToObject<string?>(serializer);
            existingValue.InactiveDate = json.GetValue("InactiveDate", StringComparison.InvariantCultureIgnoreCase)?.ToObject<DateTime?>(serializer);
            existingValue.ParentCustomFormId = json.GetValue("ParentCustomFormId", StringComparison.InvariantCultureIgnoreCase)?.ToObject<long?>(serializer);

            var fields = json.GetValue("CustomFields", StringComparison.InvariantCultureIgnoreCase) as JArray;

            var helperService = this.contextAccessor.HttpContext!.RequestServices.GetRequiredService<ICustomFieldConverterService<Data.Models.Company.CompanyCustomForm>>();
            helperService.Merge(this.tenantProvider.GetCompanyId()!.Value, (CustomFieldListDto)existingValue.CustomFields, fields);

            return existingValue;
        }
    }
}