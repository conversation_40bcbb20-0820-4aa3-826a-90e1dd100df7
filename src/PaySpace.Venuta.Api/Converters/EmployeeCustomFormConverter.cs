namespace PaySpace.Venuta.Api.Converters
{
    using System;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.DependencyInjection;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Api.Infrastructure;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Services.Abstractions;

    public class EmployeeCustomFormConverter : JsonConverter<EmployeeCustomFormDto>
    {
        private readonly IHttpContextAccessor contextAccessor;
        private readonly ITenantProvider tenantProvider;

        public EmployeeCustomFormConverter(IHttpContextAccessor contextAccessor, ITenantProvider tenantProvider)
        {
            this.contextAccessor = contextAccessor;
            this.tenantProvider = tenantProvider;
        }

        public override bool CanRead => true;

        public override bool CanWrite => false;

        public override void WriteJson(JsonWriter writer, EmployeeCustomFormDto value, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }

        public override EmployeeCustomFormDto ReadJson(JsonReader reader, Type objectType, EmployeeCustomFormDto existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            var json = JObject.Load(reader);

            existingValue ??= new EmployeeCustomFormDto();

            existingValue.EffectiveDate = json.GetValue("EffectiveDate", StringComparison.InvariantCultureIgnoreCase)?.ToObject<DateTime?>(serializer);
            existingValue.CompanyRun = json.GetValue("CompanyRun", StringComparison.InvariantCultureIgnoreCase)?.ToObject<string?>(serializer);
            existingValue.EmployeeNumber = json.GetValue("EmployeeNumber", StringComparison.InvariantCultureIgnoreCase)?.ToObject<string?>(serializer);
            existingValue.CustomFormCategoryCode = json.GetValue("CustomFormCategoryCode", StringComparison.InvariantCultureIgnoreCase)?.ToObject<string?>(serializer);
            existingValue.ParentCustomFormId = json.GetValue("ParentCustomFormId", StringComparison.InvariantCultureIgnoreCase)?.ToObject<long?>(serializer);

            existingValue.EmployeeId = !string.IsNullOrWhiteSpace(existingValue.EmployeeNumber)
                ? this.GetEmployeeId(existingValue.EmployeeNumber)
                : this.tenantProvider.GetEmployeeId()!.Value;

            var fields = json.GetValue("CustomFields", StringComparison.InvariantCultureIgnoreCase) as JArray;

            var helperService = this.contextAccessor.HttpContext!.RequestServices.GetRequiredService<ICustomFieldConverterService<Data.Models.Employees.EmployeeCustomForm>>();
            helperService.Merge(this.tenantProvider.GetCompanyId()!.Value, (CustomFieldListDto)existingValue.CustomFields, fields);

            return existingValue;
        }

        private long GetEmployeeId(string employeeNumber)
        {
            if (!string.IsNullOrEmpty(employeeNumber))
            {
                var employeeService = this.contextAccessor.HttpContext!.RequestServices.GetRequiredService<IEmployeeService>();

                var employeeId = employeeService.GetEmployeeId(this.tenantProvider.GetCompanyId().Value, employeeNumber);
                if (employeeId.HasValue)
                {
                    return employeeId.Value;
                }
            }

            throw new InvalidEmployeeNumberException(employeeNumber);
        }
    }
}