namespace PaySpace.Venuta.Api.Controllers.Employee
{
    using System;
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api;
    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Templates;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Modules.Templates.Abstractions.Models;
    using PaySpace.Venuta.Security;

    public class EmployeeTemplateConfigurationController : ODataController<EmployeeTemplateConfigurationDto, TemplateConfiguration, long>
    {
        private readonly IClaimsTenantProvider tenantProvider;
        private readonly IUserSecurityService securityService;
        private readonly IMapper mapper;

        public EmployeeTemplateConfigurationController(
            ApplicationContext context,
            IMapper mapper,
            IClaimsTenantProvider tenantProvider,
            IUserSecurityService securityService)
            : base(context, mapper)
        {
            this.tenantProvider = tenantProvider;
            this.securityService = securityService;
            this.mapper = mapper;
        }

        public override IQueryable<EmployeeTemplateConfigurationDto> GetCollection() => this.GetData(DateTime.Now);

        private IQueryable<EmployeeTemplateConfigurationDto> GetData(DateTime effectiveDate)
        {
            var employeeId = this.tenantProvider.GetEmployeeId(this.User);
            var frequencyIds = this.HttpContext.GetFrequencyIds();
            var requestFrequencyId = this.tenantProvider.GetFrequencyId(this.User);

            return this.DbSet
                .ForEmployees(_ => _.Employee, this.HttpContext)
                .HasAccess(_ => _.Employee, this.HttpContext.GetSecurityProfile(), employeeId, frequencyIds, requestFrequencyId, effectiveDate, this.securityService)
                .Include(_ => _.EmployeeComponentVariableValues)
                .ProjectTo<EmployeeTemplateConfigurationDto>(this.mapper.ConfigurationProvider)
                .OrderByDescending(_ => _.EffectiveDate);
        }
    }
}