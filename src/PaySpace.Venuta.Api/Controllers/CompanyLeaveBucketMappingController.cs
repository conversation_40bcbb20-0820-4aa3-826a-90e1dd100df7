namespace PaySpace.Venuta.Api.Controllers
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;

    public class CompanyLeaveBucketMappingController : ODataController<CompanyLeaveBucketMappingDto, CompanyLeaveBucketMapping, long>
    {
        private readonly IMapper mapper;

        public CompanyLeaveBucketMappingController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        [HttpGet]
        public override IQueryable<CompanyLeaveBucketMappingDto> GetCollection() => this.GetData();

        private IQueryable<CompanyLeaveBucketMappingDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();

            return this.DbSet.Where(_ => _.CompanyId == companyId)
                             .Where(_ => key == null || _.LeaveMappingId == key)
                             .ProjectTo<CompanyLeaveBucketMappingDto>(this.mapper.ConfigurationProvider);
        }
    }
}
