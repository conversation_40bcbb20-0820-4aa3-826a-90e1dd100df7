namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Security;

    public class EmployeeAppendix8AController : BulkController<EmployeeAppendix8ADto, EmployeeAppendix8A, long>
    {
        private readonly IMapper mapper;
        private readonly IClaimsTenantProvider tenantProvider;
        private readonly IUserSecurityService securityService;

        public EmployeeAppendix8AController(ApplicationContext context, IMapper mapper, IClaimsTenantProvider tenantProvider, IUserSecurityService securityService)
            : base(context, mapper)
        {
            this.mapper = mapper;
            this.tenantProvider = tenantProvider;
            this.securityService = securityService;
        }

        [HttpGet("all")]
        public IQueryable<EmployeeAppendix8ADto> GetAll() => this.GetData(null);

        public override IQueryable<EmployeeAppendix8ADto> GetCollection() => this.GetData(DateTime.Today);

        private IQueryable<EmployeeAppendix8ADto> GetData(DateTime? effectiveDate)
        {
            var frequencyIds = this.HttpContext.GetFrequencyIds();

            var employeeId = this.tenantProvider.GetEmployeeId(this.User);
            var requestFrequencyId = this.tenantProvider.GetFrequencyId(this.User);
            var key = this.HttpContext.GetKey();

            return this.DbSet
                .ForEmployees(_ => _.Employee, this.HttpContext)
                .HasAccess(_ => _.Employee, this.HttpContext.GetSecurityProfile(), employeeId, frequencyIds, requestFrequencyId, effectiveDate ?? DateTime.Today, this.securityService)
                .Where(_ => key == null || _.EmployeeAppendix8AId == key)
                .ProjectTo<EmployeeAppendix8ADto>(this.mapper.ConfigurationProvider);
        }
    }
}
