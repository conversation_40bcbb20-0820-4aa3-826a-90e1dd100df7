namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Net.Mime;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Data.SqlClient;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees.Results;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Tax;

    public class EmployeeTaxCertificateController : ControllerBase<EmployeeTaxCertificateDto>
    {
        private readonly ReadOnlyContext context;
        private readonly IMapper mapper;
        private readonly IUserSecurityService securityService;
        private readonly ITaxCertificateService taxCertificateService;
        private readonly IClaimsTenantProvider tenantProvider;
        private readonly IMessageBus messageBus;

        public EmployeeTaxCertificateController(
            ReadOnlyContext context,
            IMapper mapper,
            IClaimsTenantProvider tenantProvider,
            IUserSecurityService securityService,
            ITaxCertificateService taxCertificateService,
            IMessageBus messageBus)
        {
            this.context = context;
            this.mapper = mapper;
            this.tenantProvider = tenantProvider;
            this.securityService = securityService;
            this.taxCertificateService = taxCertificateService;
            this.messageBus = messageBus;
        }

        [HttpGet]
        public IQueryable<EmployeeTaxCertificateDto> GetCollection()
        {
            return this.GetData();
        }

        [HttpGet("{employeeId}/{employmentStatusId}/{taxYearId}/download")]
        public async Task<IActionResult> DownloadTaxCertificate(long employeeId, long employmentStatusId, int taxYearId)
        {
            var userId = this.User.GetUserId();

            var taxCertificate = this.GetData()
                .Where(_ => _.EmployeeId == employeeId && _.EmploymentStatusId == employmentStatusId && _.TaxYearId == taxYearId)
                .Select(_ => new { _.OldEmployeeId, _.CompanyId })
                .FirstOrDefault();

            if (taxCertificate?.OldEmployeeId == null)
            {
                return this.Forbid();
            }

            if (this.User.IsDeveloper())
            {
                var dummyPdf = await this.taxCertificateService.GetDummyPdfAsync(this.HttpContext.RequestAborted);
                return this.File(dummyPdf, MediaTypeNames.Application.Pdf, "IRP5.pdf");
            }

            var reportParameterValues = await this.taxCertificateService.GetTaxReportParametersAsync(taxCertificate.CompanyId, taxCertificate.OldEmployeeId, employmentStatusId, taxYearId, userId);
            if (reportParameterValues == default)
            {
                throw new InvalidOperationException("No report exists for this country.");
            }

            var report = await this.GetReportAsync(userId, taxCertificate.CompanyId, reportParameterValues.ReportPath, reportParameterValues.Parameters);
            if (report == null || report.Length == 0)
            {
                return this.NotFound();
            }

            var reportFormat = this.GetReportExportMimeTypeFormat(reportParameterValues.FileType);

            return this.File(report, reportFormat, $"Tax certificate.{reportParameterValues.FileType}");
        }

        public IQueryable<EmployeeTaxCertificateDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var frequencyIds = this.HttpContext.GetFrequencyIds();
            var employeeId = this.tenantProvider.GetEmployeeId(this.User);
            var requestFrequencyId = this.tenantProvider.GetFrequencyId(this.User);

            var query = ResourceHelper.GetEmbeddedContent("Sql.EmployeeTaxCertificate.sql");
            var parameters = new List<SqlParameter>
            {
                new("@companyId", companyId)
            };

            return this.context.Set<EmployeeTaxCertificateResult>()
                .FromSqlRaw(query, parameters.ToArray())
                .HasAccess(_ => _.Employee, this.HttpContext.GetSecurityProfile(), employeeId, frequencyIds, requestFrequencyId, DateTime.Today, this.securityService)
                .ProjectTo<EmployeeTaxCertificateDto>(this.mapper.ConfigurationProvider, new { urlHelper = this.Url, companyId });
        }

        private async Task<byte[]> GetReportAsync(long userId, long companyId, string reportPath, IDictionary<string, object?> parameters)
        {
            var notificationHash = ExportReportMessage.GetHash(parameters);

            if (!reportPath.Contains(".repx"))
            {
                var report = await this.messageBus.RequestAsync<ExportSSRSReportMessage, ReportResponse>(
                    new ExportSSRSReportMessage
                    {
                        Background = false,
                        NotificationId = notificationHash,
                        UserId = userId,
                        CompanyId = companyId,
                        FormatId = 1,
                        ReportHeaderId = 0,
                        ReportPath = reportPath,
                        Parameters = parameters.Select(_ => new ExportReportParameter
                        {
                            Id = 0,
                            Name = _.Key,
                            Value = _.Value?.ToString()
                        }).ToArray()
                    },
                    this.HttpContext.RequestAborted);

                return report.Document.HasValue ? await report.Document.Value : null;
            }
            else
            {
                var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);

                var report = await this.messageBus.RequestAsync<ExportCustomReportMessage, ReportResponse>(
                    new ExportCustomReportMessage
                    {
                        Background = false,
                        AccessToken = accessToken,
                        NotificationId = notificationHash,
                        UserId = userId,
                        CompanyId = companyId,
                        ReportUrl = reportPath + $"?CompanyId={companyId}",
                        Parameters = parameters,
                        Format = "pdf",
                        Culture = CultureInfo.CurrentCulture
                    },
                    this.HttpContext.RequestAborted);

                return report.Document.HasValue ? await report.Document.Value : null;
            }
        }

        private string GetReportExportMimeTypeFormat(string format)
        {
            return format == "xlsx" ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/pdf";
        }
    }
}