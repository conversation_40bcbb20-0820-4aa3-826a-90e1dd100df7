namespace PaySpace.Venuta.Api.Controllers
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;

    // Used in the Company Leave Scheme Parameters screen / API
    [ApiVersion("2.0")]
    public class CompanyLeaveSetupController : ODataController<CompanyLeaveSetupDto, CompanyLeaveDetail, long>
    {
        private readonly IMapper mapper;

        public CompanyLeaveSetupController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        public override IQueryable<CompanyLeaveSetupDto> GetCollection() => this.GetData();

        private IQueryable<CompanyLeaveSetupDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();

            return this.DbSet
                .Where(_ => _.CompanyLeaveSetup.CompanyLeaveScheme.CompanyId == companyId)
                .Where(_ => key == null || _.CompanyLeaveDetailId == key)
                .ProjectTo<CompanyLeaveSetupDto>(this.mapper.ConfigurationProvider);
        }
    }
}