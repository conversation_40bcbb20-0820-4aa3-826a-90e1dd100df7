namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Company;

    public class CompanyGLDetailController : BulkController<CompanyGLDetailDto, Data.Models.Company.CompanyGLDetail, long>
    {
        private readonly IMapper mapper;

        public CompanyGLDetailController(
            ApplicationContext context,
            IMapper mapper)
            : base(
                context,
                mapper)
        {
            this.mapper = mapper;
        }

        public override IQueryable<CompanyGLDetailDto> GetCollection()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var frequencyId = this.HttpContext.GetFrequencyId();
            var key = this.HttpContext.GetKey();

            return this.DbSet
                .Where(_ => key == null || _.CompanyGLDetailId == key)
                .Where(_ => _.ComponentCompany.CompanyFrequency.CompanyId == companyId
                        && _.ComponentCompany.CompanyFrequency.CompanyFrequencyId == frequencyId)
                .OrderBy(_ => _.CompanyGLDetailId)
                .ProjectTo<CompanyGLDetailDto>(this.mapper.ConfigurationProvider);
        }

        public override Task<IActionResult> Post([Required] CompanyGLDetailDto dto)
        {
            throw new NotImplementedException();
        }

        public override Task<IActionResult> Delete(long key)
        {
            throw new NotImplementedException();
        }
    }
}