namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Net.Mime;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;

    using FluentValidation;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Localization;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.HmrcPaymentRecord.Abstractions;
    using PaySpace.Venuta.Modules.HmrcPaymentRecord.Abstractions.Models;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Services.Reports;

    // Note that the GetAllHmrcSummaries request relies on a SP, as such their will be a null LegislationOverrideId key if no overriden values are set.
    // The LegislationOverride table is only used to store updated fields for HMRC Payment Records.
    // Furthermore, we rely on a composite key of PeriodCode & Quarter with TaxYearID on POST request - which will handles both Add & Update.
    [ApiController]
    [ApiVersionNeutral]
    [Authorize("company")]
    [Route("v{version:apiVersion}/{company}/[controller]")]
    public class HmrcPaymentRecordController : ControllerBase<LegislationOverrideDto>
    {
        private const string P32MonthlyReportPath = "Bureau/UK/UK_P32_Monthly/UK_P32_Monthly.repx";
        private const string P32QuarterlyReportPath = "Bureau/UK/UK_P32_Quarterly/UK_P32_Quarterly.repx";
        private const string P30ReportPath = "Bureau/UK/UK_P30/UK_P30.repx";
        private const string P30QuarterlyReportPath = "Bureau/UK/UK_P30_Quarterly/UK_P30_Quarterly.repx";

        private readonly IReportHeaderService reportHeaderService;
        private readonly IHmrcCalculationService hmrcCalculationService;
        private readonly IMessageBus messageBus;
        private readonly IValidator<LegislationOverride> validator;
        private readonly IMapper mapper;
        private readonly IStringLocalizer stringLocalizer;

        public HmrcPaymentRecordController(
            IReportHeaderService reportHeaderService,
            IHmrcCalculationService hmrcCalculationService,
            IValidator<LegislationOverride> validator,
            IMessageBus messageBus,
            IMapper mapper,
            IStringLocalizerFactory stringLocalizerFactory)
        {
            this.reportHeaderService = reportHeaderService;
            this.hmrcCalculationService = hmrcCalculationService;
            this.messageBus = messageBus;
            this.validator = validator;
            this.mapper = mapper;

            this.stringLocalizer = stringLocalizerFactory.Create(SystemAreas.Report.Area, null);
        }

        [HttpGet("{taxYearId}")]
        public async Task<List<LegislationSummaryDetailDto>> GetAllHmrcSummaries(int taxYearId)
        {
            var companyId = this.HttpContext.GetCompanyId();
            var hmrcSummary = await this.hmrcCalculationService.GetAllHmrcSummariesAsync(companyId, taxYearId);
            var result = this.mapper.Map<List<LegislationSummaryDetailDto>>(hmrcSummary);

            return result;
        }

        [HttpGet("{taxYearId}/ni-deductions")]
        public async Task<List<NiDeductionDetailDto>> GetNiDeductions(int taxYearId)
        {
            var companyId = this.HttpContext.GetCompanyId();
            var hmrcSummary = await this.hmrcCalculationService.GetNiDeductionAsync(companyId, taxYearId);
            var result = this.mapper.Map<List<NiDeductionDetailDto>>(hmrcSummary);

            return result;
        }

        [HttpGet("{taxYearId}/apprenticeship-levy")]
        public async Task<List<ApprenticesLevyDto>> GetApprenticeshipLevy(int taxYearId)
        {
            var companyId = this.HttpContext.GetCompanyId();
            var hmrcSummary = await this.hmrcCalculationService.GetApprenticeshipLevyAsync(companyId, taxYearId);
            var result = this.mapper.Map<List<ApprenticesLevyDto>>(hmrcSummary);

            return result;
        }

        [HttpGet("{taxYearId}/hmrc-details")]
        public async Task<LegislationDetailDto> GetDetailedHmrcSummaryByPeriodOrQuarter(int taxYearId, string? periodCode, int? quarter, bool? takeOnRun)
        {
            var companyId = this.HttpContext.GetCompanyId();
            if (string.IsNullOrEmpty(periodCode) && quarter == null)
            {
                throw new InvalidOperationException(ErrorCodes.HmrcPaymentRecord.PeriodCodeOrQuarterRequired);
            }

            var slideSummary = await this.hmrcCalculationService.GetDetailedHmrcSummaryByPeriodOrQuarterAsync(companyId, taxYearId, periodCode, quarter, takeOnRun ?? false);
            var result = this.mapper.Map<LegislationDetailDto>(slideSummary);

            return result;
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] LegislationOverrideDto model)
        {
            var result = this.mapper.Map<LegislationOverride>(model);

            var isDuplicateRecord = await this.hmrcCalculationService.IsDuplicateLegislationAsync(result.CompanyId, result.TaxYearId, result.PeriodCode, result.Quarter, result.TakeOnRun);
            if (isDuplicateRecord)
            {
                return await this.UpdatePaymentRecordAsync(result);
            }

            return await this.AddPaymentRecordAsync(result);
        }

        [HttpGet("{taxYearId}/Report/{reportName}")]
        public async Task<IActionResult> DownloadHmrcReport(int taxYearId, string reportName, long? periodCode, int? quarter, bool? takeOnRun)
        {
            var companyId = this.HttpContext.GetCompanyId();

            var reportPath = GetReportPath(reportName);
            if (string.IsNullOrEmpty(reportPath))
            {
                return this.BadRequest($"Invalid report name: {reportName}");
            }

            var reportNameKey = await this.reportHeaderService.GetReportNameKey(reportPath);
            var rptName = reportNameKey != null ? this.stringLocalizer.GetString(reportNameKey) : reportName;
            var userId = this.User.GetUserId();
            var parameters = CreateReportParameters(companyId, taxYearId, periodCode.ToString(), quarter, takeOnRun);
            var report = await this.GetReportAsync(userId, companyId, reportPath, parameters);
            if (report == null || report.Length == 0)
            {
                return this.NotFound();
            }

            return this.File(report, MediaTypeNames.Application.Pdf, $"{rptName}.pdf");
        }

        [HttpDelete("{taxYearId}/{isTakeOnRun}")]
        public async Task<IActionResult> DeletePaymentRecord(int taxYearId, bool isTakeOnRun, string? periodCode, int? quarter)
        {
            var companyId = this.HttpContext.GetCompanyId();
            var legislationOverrideId = await this.hmrcCalculationService.GetLegislationOverrideIdAsync(companyId, taxYearId, periodCode, quarter, isTakeOnRun);
            if (legislationOverrideId == null)
            {
                return this.NotFound();
            }

            var paymentRecord = await this.hmrcCalculationService.FindByIdAsync(legislationOverrideId.Value);
            if (paymentRecord.LegislationOverrideId == null)
            {
                return this.NotFound();
            }

            await this.hmrcCalculationService.DeleteAsync(paymentRecord);
            return this.Ok();
        }

        private async Task<IActionResult> AddPaymentRecordAsync(LegislationOverride result)
        {
            var validationResult = await this.validator.ValidateAsync(result, options => options.IncludeRuleSets(RuleSetNames.Create));
            if (validationResult.Errors.Count > 0)
            {
                var errors = validationResult.Errors.Select(_ => new ErrorCodeValidationResult(_.ErrorMessage, _.ErrorCode, _.PropertyName));
                return this.ODataBadRequest(errors);
            }

            await this.hmrcCalculationService.AddAsync(result);
            return this.Ok();
        }

        private async Task<IActionResult> UpdatePaymentRecordAsync(LegislationOverride result)
        {
            var legislationOverrideId = (long?)0;
            if (result.LegislationOverrideId == null)
            {
                // Determine the PK based on a composite key of Quarter & PeriodCode with TaxYearId
                legislationOverrideId = await this.hmrcCalculationService.GetLegislationOverrideIdAsync(result.CompanyId, result.TaxYearId, result.PeriodCode, result.Quarter, result.TakeOnRun);
            }

            var paymentRecord = await this.hmrcCalculationService.FindByIdAsync(legislationOverrideId!.Value);
            this.mapper.Map(result, paymentRecord);

            var validationResult = await this.validator.ValidateAsync(paymentRecord, options => options.IncludeRuleSets(RuleSetNames.Update));
            if (validationResult.Errors.Count > 0)
            {
                var errors = validationResult.Errors.Select(_ => new ErrorCodeValidationResult(_.ErrorMessage, _.ErrorCode, _.PropertyName));
                return this.ODataBadRequest(errors);
            }

            await this.hmrcCalculationService.UpdateAsync(paymentRecord);
            return this.Ok();
        }

        private static string GetReportPath(string reportName)
        {
            return reportName.ToLower() switch
            {
                "p32monthly" => P32MonthlyReportPath,
                "p32quarterly" => P32QuarterlyReportPath,
                "p30" => P30ReportPath,
                "p30quarterly" => P30QuarterlyReportPath,
                _ => null
            };
        }

        private static Dictionary<string, object> CreateReportParameters(long companyId, int taxYearId, string? periodCode, int? quarter, bool? takeOnRun)
        {
            return new Dictionary<string, object>()
            {
                { "CompanyId", companyId },
                { "BureauTaxId", taxYearId },
                { "PeriodCode", periodCode },
                { "Quarter", quarter },
                { "IncludeTakeOn", takeOnRun },
                { "UserId", 0 }
            };
        }

        private async Task<byte[]> GetReportAsync(long userId, long companyId, string reportPath, IDictionary<string, object?> parameters)
        {
            var notificationHash = ExportReportMessage.GetHash(parameters);

            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);

            var report = await this.messageBus.RequestAsync<ExportCustomReportMessage, ReportResponse>(
                new ExportCustomReportMessage
                {
                    Background = false,
                    AccessToken = accessToken,
                    NotificationId = notificationHash,
                    UserId = userId,
                    CompanyId = companyId,
                    ReportUrl = $"{reportPath}?CompanyId={companyId}",
                    Parameters = parameters,
                    Format = "pdf",
                    Culture = CultureInfo.CurrentCulture
                },
                this.HttpContext.RequestAborted);

            if (report is { Document: not null })
            {
                return report.Document.HasValue ? await report.Document.Value : null;
            }

            return null;
        }
    }
}