namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.Filters;
    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Modules.RPN.Abstractions.Models;
    using PaySpace.Venuta.Security;

    public class EmployeeRpnController : BulkController<EmployeeRpnDto, EmployeeRpn, long>
    {
        private readonly IMapper mapper;
        private readonly IClaimsTenantProvider tenantProvider;
        private readonly IUserSecurityService securityService;

        public EmployeeRpnController(
            ApplicationContext context,
            IMapper mapper,
            IClaimsTenantProvider tenantProvider,
            IUserSecurityService securityService)
            : base(context, mapper)
        {
            this.mapper = mapper;
            this.tenantProvider = tenantProvider;
            this.securityService = securityService;
        }

        public override IQueryable<EmployeeRpnDto> GetCollection() => this.GetData();

        [RequestNotSupported]
        public override Task<IActionResult> Delete([Required] long key)
        {
            return base.Delete(key);
        }

        [RequestNotSupported]
        public override Task<IActionResult> Post([Required] EmployeeRpnDto model)
        {
            return base.Post(model);
        }

        private IQueryable<EmployeeRpnDto> GetData()
        {
            var frequencyIds = this.HttpContext.GetFrequencyIds();

            var employeeId = this.tenantProvider.GetEmployeeId(this.User);
            var requestFrequencyId = this.tenantProvider.GetFrequencyId(this.User);
            var key = this.HttpContext.GetKey();

            return this.DbSet
                .TagWithSource()
                .ForEmployees(_ => _.Employee, this.HttpContext)
                .HasAccess(_ => _.Employee, this.HttpContext.GetSecurityProfile(), employeeId, frequencyIds, requestFrequencyId, DateTime.Today, this.securityService)
                .Where(_ => key == null || _.EmployeeRpnId == key)
                .Where(_ => key != null
                    || this.DbSet
                        .Where(s => s.EmployeeId == _.EmployeeId && s.EffectiveDate.Date <= DateTime.Now)
                        .OrderByDescending(s => s.EffectiveDate)
                        .Select(s => s.EmployeeRpnId)
                        .FirstOrDefault() == _.EmployeeRpnId)
                .ProjectTo<EmployeeRpnDto>(this.mapper.ConfigurationProvider);
        }
    }
}
