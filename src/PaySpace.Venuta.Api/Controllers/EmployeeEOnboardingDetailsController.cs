namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Security;

    public class EmployeeEOnboardingDetailsController : ODataController<EmployeeEOnboardingDetailsDto, EmployeeEOnboardingDetails, long>
    {
        private readonly IMapper mapper;

        public EmployeeEOnboardingDetailsController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        [HttpGet("all")]
        public IQueryable<EmployeeEOnboardingDetailsDto> GetAll() => this.GetData();

        public override IQueryable<EmployeeEOnboardingDetailsDto> GetCollection() => this.GetData();

        private IQueryable<EmployeeEOnboardingDetailsDto> GetData()
        {
            var key = this.HttpContext.GetKey();
            var companyId = this.HttpContext.GetCompanyId();

            return this.DbSet
                .Where(_ => _.CompanyId == companyId)
                .Where(_ => key == null || _.EmployeeDetailsId == key)
                .ProjectTo<EmployeeEOnboardingDetailsDto>(this.mapper.ConfigurationProvider);
        }
    }
}
