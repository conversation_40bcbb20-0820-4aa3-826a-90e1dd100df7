namespace PaySpace.Venuta.Api.Controllers
{
    using System;
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Security;

    public class EmployeeEmploymentContractController : BulkController<EmployeeEmploymentContractDto, EmployeeEmploymentContract, long>
    {
        private readonly ApplicationContext context;
        private readonly IMapper mapper;
        private readonly IClaimsTenantProvider tenantProvider;
        private readonly IUserSecurityService securityService;

        public EmployeeEmploymentContractController(ApplicationContext context, IMapper mapper, IClaimsTenantProvider tenantProvider, IUserSecurityService securityService)
            : base(context, mapper)
        {
            this.context = context;
            this.mapper = mapper;
            this.tenantProvider = tenantProvider;
            this.securityService = securityService;
        }

        public override IQueryable<EmployeeEmploymentContractDto> GetCollection()
        {
            return this.GetData(DateTime.Today);
        }

        public IQueryable<EmployeeEmploymentContractDto> GetData(DateTime? effectiveDate)
        {
            var companyId = this.HttpContext.GetCompanyId();
            var frequencyIds = this.HttpContext.GetFrequencyIds();

            var employeeId = this.tenantProvider.GetEmployeeId(this.User);
            var requestFrequencyId = this.tenantProvider.GetFrequencyId(this.User);
            var key = this.HttpContext.GetKey();

            return this.context.Set<EmployeeEmploymentContract>()
                .ForEmployees(ec => ec.Employee, this.HttpContext)
                .HasAccess(ec => ec.Employee, this.HttpContext.GetSecurityProfile(), employeeId, frequencyIds, requestFrequencyId, effectiveDate ?? DateTime.Today, this.securityService)
                .Where(ec => ec.CompanyId == companyId)
                .Where(ec => key == null || (key != null && ec.EmploymentContractId == key))
                .OrderBy(ec => ec.EmploymentContractId)
                .ProjectTo<EmployeeEmploymentContractDto>(this.mapper.ConfigurationProvider);
        }
    }
}