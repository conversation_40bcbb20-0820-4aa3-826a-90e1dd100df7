namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Data.Models.Organization;

    public class CompanyPositionFunctionAreaController : ODataController<CompanyPositionFunctionAreaDto, CompanyPositionFunctionArea, long>
    {
        private readonly IMapper mapper;

        public CompanyPositionFunctionAreaController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        public override IQueryable<CompanyPositionFunctionAreaDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<CompanyPositionFunctionAreaDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .Where(_ => key == null || _.FunctionAreaId == key)
                .ProjectTo<CompanyPositionFunctionAreaDto>(this.mapper.ConfigurationProvider);
        }
    }
}
