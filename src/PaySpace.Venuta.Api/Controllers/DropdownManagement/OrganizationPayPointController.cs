namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Data.Models.Organization;

    public class OrganizationPayPointController : ODataController<OrganizationPayPointDto, OrganizationPayPoint, long>
    {
        private readonly IMapper mapper;

        public OrganizationPayPointController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        public override IQueryable<OrganizationPayPointDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<OrganizationPayPointDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .Where(_ => key == null || _.PayPointId == key)
                .ProjectTo<OrganizationPayPointDto>(this.mapper.ConfigurationProvider);
        }
    }
}
