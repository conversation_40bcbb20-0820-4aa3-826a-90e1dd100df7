namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;

    [ApiVersion("2.0")]
    public class CompanyLegalBodyController : ODataController<CompanyLegalBodyDto, CompanyLegalBody, long>
    {
        private readonly IMapper mapper;

        public CompanyLegalBodyController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        public override IQueryable<CompanyLegalBodyDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<CompanyLegalBodyDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .Where(_ => key == null || _.CompanyLegalBodyId == key)
                .ProjectTo<CompanyLegalBodyDto>(this.mapper.ConfigurationProvider);
        }
    }
}