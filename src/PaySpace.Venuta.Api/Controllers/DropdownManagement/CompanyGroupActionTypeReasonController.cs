namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Services.Abstractions;

    public class CompanyGroupActionTypeReasonController : ODataController<CompanyGroupActionTypeReasonDto, CompanyGroupActionTypeReason, long>
    {
        private readonly ICompanyService companyService;
        private readonly IMapper mapper;

        public CompanyGroupActionTypeReasonController(
            ICompanyService companyService,
            ApplicationContext context,
            IMapper mapper)
            : base(context, mapper)
        {
            this.companyService = companyService;
            this.mapper = mapper;
        }

        public override IQueryable<CompanyGroupActionTypeReasonDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<CompanyGroupActionTypeReasonDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            var companyGroupId = this.companyService.GetCompanyGroupId(companyId);

            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyGroupActionType.CompanyGroupId == companyGroupId)
                .Where(_ => key == null || _.ActionTypeReasonId == key)
                .ProjectTo<CompanyGroupActionTypeReasonDto>(this.mapper.ConfigurationProvider);
        }
    }
}
