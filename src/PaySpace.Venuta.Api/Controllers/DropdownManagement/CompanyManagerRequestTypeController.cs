namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Services.Company;

    public class CompanyManagerRequestTypeController : ODataController<CompanyManagerRequestTypeDto, CompanyManagerRequestType, long>
    {
        private readonly IMapper mapper;
        private readonly ICompanyAttachmentService companyAttachmentService;

        public CompanyManagerRequestTypeController(ApplicationContext context, IMapper mapper, ICompanyAttachmentService companyAttachmentService)
            : base(context, mapper)
        {
            this.mapper = mapper;
            this.companyAttachmentService = companyAttachmentService;
        }

        [HttpGet("company-attachments")]
        public IQueryable<CompanyAttachmentDto> GetCompanyAttachments()
        {
            var companyId = this.HttpContext.GetCompanyId();
            return this.companyAttachmentService.GetCompanyAttachments(companyId)
                .ProjectTo<CompanyAttachmentDto>(this.mapper.ConfigurationProvider);
        }

        public override IQueryable<CompanyManagerRequestTypeDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<CompanyManagerRequestTypeDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .Where(_ => key == null || _.RequestTypeId == key)
                .ProjectTo<CompanyManagerRequestTypeDto>(this.mapper.ConfigurationProvider);
        }
    }
}
