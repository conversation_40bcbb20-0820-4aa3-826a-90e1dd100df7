namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{

    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;

    [ApiVersion("2.0")]
    public class CompanyOffenceController : ODataController<CompanyOffenceDto, CompanyOffence, long>
    {
        private readonly IMapper mapper;

        public CompanyOffenceController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        public override IQueryable<CompanyOffenceDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<CompanyOffenceDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .Where(_ => key == null || _.CompanyOffenceId == key)
                .ProjectTo<CompanyOffenceDto>(this.mapper.ConfigurationProvider);
        }
    }
}