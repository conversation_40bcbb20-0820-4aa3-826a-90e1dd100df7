namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;
    using PaySpace.Venuta.Services.Abstractions;

    [ApiVersion("2.0")]
    public class CompanyGroupActionTypeController : ODataController<CompanyGroupActionTypeDto, CompanyGroupActionType, long>
    {
        private readonly ICompanyService companyService;
        private readonly IMapper mapper;

        public CompanyGroupActionTypeController(
            ICompanyService companyService,
            ApplicationContext context,
            IMapper mapper)
            : base(context, mapper)
        {
            this.companyService = companyService;
            this.mapper = mapper;
        }

        public override IQueryable<CompanyGroupActionTypeDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<CompanyGroupActionTypeDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            var companyGroupId = this.companyService.GetCompanyGroupId(companyId);
            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyGroupId == companyGroupId)
                .Where(_ => key == null || _.ActionTypeId == key)
                .ProjectTo<CompanyGroupActionTypeDto>(this.mapper.ConfigurationProvider);
        }
    }
}