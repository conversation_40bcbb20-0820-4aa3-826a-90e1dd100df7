namespace PaySpace.Venuta.Api.Controllers.DropdownManagement
{
    using System.Linq;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.DropdownManagement;

    public class CompanyComponentGroupController : ODataController<CompanyComponentGroupDto, CompanyComponentGroup, int>
    {
        private readonly IMapper mapper;

        public CompanyComponentGroupController(ApplicationContext context, IMapper mapper)
            : base(context, mapper)
        {
            this.mapper = mapper;
        }

        public override IQueryable<CompanyComponentGroupDto> GetCollection()
        {
            return this.GetData();
        }

        private IQueryable<CompanyComponentGroupDto> GetData()
        {
            var companyId = this.HttpContext.GetCompanyId();
            var key = this.HttpContext.GetKey();
            return this.DbSet
                .TagWithSource()
                .Where(_ => _.CompanyId == companyId)
                .Where(_ => key == null || _.ComponentGroupId == key)
                .ProjectTo<CompanyComponentGroupDto>(this.mapper.ConfigurationProvider);
        }
    }
}
