namespace PaySpace.Venuta.Api.Controllers.Company
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Api.Filters;
    using PaySpace.Venuta.Api.Middleware;
    using PaySpace.Venuta.Api.OData;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Exceptions;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;
    using PaySpace.Venuta.Services.Company;

    [ApiVersion("2.0")]
    [RequireRun]
    public class ComponentCompanyController : ODataController<ComponentCompanyDto, ComponentCompany, long>
    {
        private readonly IMapper mapper;
        private readonly ICompanyRunService companyRunService;
        private readonly IComponentCompanyDetailLookupStrategy lookupStrategy;

        public ComponentCompanyController(ApplicationContext context, IMapper mapper, ICompanyRunService companyRunService, IComponentCompanyDetailLookupStrategy lookupStrategy)
            : base(context, mapper)
        {
            this.mapper = mapper;
            this.companyRunService = companyRunService;
            this.lookupStrategy = lookupStrategy;
        }

        public override IQueryable<ComponentCompanyDto> GetCollection()
        {
            return this.GetData();
        }

        [RequestNotSupported]
        public override Task<IActionResult> Post(ComponentCompanyDto model)
        {
            return base.Post(model);
        }

        [RequestNotSupported]
        public override Task<IActionResult> Delete(long key)
        {
            return base.Delete(key);
        }

        private IQueryable<ComponentCompanyDto> GetData()
        {
            var frequencyId = this.HttpContext.GetFrequencyId()!.Value;
            var runId = this.HttpContext.GetRunId()!.Value;
            var key = this.HttpContext.GetKey();

            if (!this.IsRunOpen(frequencyId, runId))
            {
                throw new InvalidOperationException("An open run is required");
            }

            var companyId = this.HttpContext.GetCompanyId();

            // Using the lookup strategy is far more efficient than creating a query with all the joins to check if the component is active
            // The result of the lookup strategy is cached.
            var componentIds = this.lookupStrategy.GetComponents(companyId, frequencyId, runId, true).Select(_ => _.ComponentId).ToList();

            if (key > 0 && !componentIds.Any(_ => _ == key))
            {
                // ensure that you have access to update
                throw new NotFoundException();
            }

            return this.DbSet.Where(_ => _.CompanyFrequencyId == frequencyId)
                .Where(_ => (key != null && _.ComponentId == key) || (key == null && componentIds.Any(id => id == _.ComponentId)))
                .ProjectTo<ComponentCompanyDto>(this.mapper.ConfigurationProvider);
        }

        private bool IsRunOpen(long frequencyId, long runId)
        {
            return this.companyRunService.GetCompanyRuns(frequencyId).Any(_ => _.RunId == runId && _.Status == RunStatus.Open);
        }
    }
}