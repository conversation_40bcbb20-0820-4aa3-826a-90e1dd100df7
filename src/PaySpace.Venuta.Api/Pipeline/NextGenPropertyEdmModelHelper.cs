namespace PaySpace.Venuta.Api.Pipeline
{
    using System;
    using System.Globalization;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.OData.Edm;
    using Microsoft.OData.Edm.Vocabularies;

    using PaySpace.Venuta.Validation;
    using PaySpace.Venuta.Validation.Annotations;

    public class NextGenPropertyEdmModelHelper : DefaultPropertyEdmModelHelper
    {
        private readonly IEdmModel edmModel;
        private readonly IEdmStructuralProperty structuralProperty;
        private readonly ModelMetadata entityMetadata;

        protected NextGenPropertyEdmModelHelper(
            IEdmModel edmModel,
            IEdmStructuralProperty structuralProperty,
            ModelMetadata entityMetadata,
            ModelMetadata propertyMetadata,
            string countryCode,
            Version version)
            : base(edmModel, structuralProperty, entityMetadata, propertyMetadata, countryCode, version)
        {
            this.edmModel = edmModel;
            this.structuralProperty = structuralProperty;
            this.entityMetadata = entityMetadata;
        }

        public override void Annotate()
        {
            base.Annotate();
            this.AnnotateOrder();
        }

        public static new DefaultPropertyEdmModelHelper Create(IEdmModel model, IEdmStructuralProperty structuralProperty, ModelMetadata entityMetadata, string countryCode, Version version)
        {
            var propertyMetadata = entityMetadata.Properties[structuralProperty.Name];
            if (propertyMetadata == null)
            {
                return null;
            }

            return new NextGenPropertyEdmModelHelper(model, structuralProperty, entityMetadata, propertyMetadata, countryCode, version);
        }

        private void AnnotateOrder()
        {
            var metadata = this.entityMetadata.Properties[this.structuralProperty.Name];

            this.edmModel.SetAnnotationValue(
                this.structuralProperty,
                ApiConstants.Namespace,
                "Order",
                new EdmStringConstant(EdmCoreModel.Instance.GetString(false), Convert.ToString(metadata.Order, CultureInfo.InvariantCulture)));

            if (metadata.TryGetAttribute<DefaultSortAttribute>(out var oDataSortingAttribute))
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "SortOrder", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), oDataSortingAttribute.SortOrder));
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "SortIndex", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), oDataSortingAttribute.SortIndex));
            }
        }
    }
}