namespace PaySpace.Venuta.Api.Pipeline.ModelBuilders
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Configuration;
    using Microsoft.OData.ModelBuilder;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Api.Infrastructure;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;

    public abstract class SecureModelBuilder : MetadataModelBuilder
    {
        private readonly IModelMetadataProvider metadataProvider;
        private readonly PaymentModule paymentModule;
        private readonly IList<CachedMenu> userMenus;
        private readonly ISecurityProfile securityProfile;

        /// <summary>
        /// List of all the screens that is not in NextGen yet, We need to use the link ids.
        /// If you update this list you must also update BulkUploadAuthorizationHandler.cs in NextGen.
        /// </summary>
        private static readonly Dictionary<string, long> ControllerLinkIds = PaySpaceConstants.ControllerLinkIds;

        protected SecureModelBuilder(
            IConfiguration configuration,
            IModelMetadataProvider metadataProvider,
            Version version,
            PaymentModule paymentModule,
            IList<CachedMenu> userMenus,
            ISecurityProfile securityProfile)
            : base(configuration, metadataProvider, version, securityProfile)
        {
            this.metadataProvider = metadataProvider;
            this.paymentModule = paymentModule;
            this.userMenus = userMenus;
            this.securityProfile = securityProfile;
        }

        protected override EntitySetConfiguration<TEntity> RegisterEntity<TEntity>(ODataConventionModelBuilder builder, string entityName = null)
        {
            var hasPermission = this.HasPermission<TEntity>();
            if (!hasPermission)
            {
                return null;
            }

            // Checking signature as a quick fix, we need to add proper support for MSS.
            var metadata = this.metadataProvider.GetMetadataForType(typeof(TEntity));

            // If the Key is ignored then the model cannot be created successfully.
            // Sometimes only custom fields is enabled, then clients don't have access to key/page.
            var shouldIgnoreKey = this.ShouldIgnoreKey(metadata);
            if (shouldIgnoreKey || metadata.Properties.All(property => property.Name == "Signature" || this.ShouldIgnoreProperty(property)))
            {
                return null;
            }

            return base.RegisterEntity<TEntity>(builder, entityName: entityName);
        }

        protected EntitySetConfiguration<TEntity>? RegisterEntityWithArea<TEntity>(ODataConventionModelBuilder builder, string area)
            where TEntity : class
        {
            var isDeniedAccess = this.securityProfile?.IsDenied(area, area) ?? true;

            return !isDeniedAccess ? this.RegisterEntity<TEntity>(builder) : null;
        }

        protected override ComplexTypeConfiguration<TEntity> RegisterComplexType<TEntity>(ODataConventionModelBuilder builder)
        {
            var hasPermission = this.HasPermission<TEntity>();
            if (hasPermission)
            {
                return base.RegisterComplexType<TEntity>(builder);
            }

            return null;
        }

        protected void RegisterComplexTypeWithArea<TEntity>(ODataConventionModelBuilder builder, string area)
            where TEntity : class
        {
            var isDeniedAccess = this.securityProfile?.IsDenied(area, area) ?? true;
            if (!isDeniedAccess)
            {
                this.RegisterComplexType<TEntity>(builder);
            }
        }

        protected EntitySetConfiguration<TEntity> RegisterEntityWithNoSecurity<TEntity>(ODataConventionModelBuilder builder, string entityName = null)
            where TEntity : class
        {
            return base.RegisterEntity<TEntity>(builder, entityName: entityName);
        }

        protected ComplexTypeConfiguration<TEntity> RegisterComplexTypeWithNoSecurity<TEntity>(ODataConventionModelBuilder builder)
            where TEntity : class
        {
            return base.RegisterComplexType<TEntity>(builder);
        }

        protected virtual bool HasPermission<TEntity>()
            where TEntity : class
        {
            // TODO: Use MenuRequirement.
            var controllerNames = typeof(TEntity).GetNavigationControllerNames();
            return controllerNames.Length == 0 || controllerNames.All(this.HasAccessToController);
        }

        private bool HasAccessToController(string controllerName)
        {
            var bureauRouteValues = Array.Empty<RouteValues>();
            var routeValues = new[]
            {
                new RouteValues("Employees", controllerName, "Index"),
                new RouteValues("Employees", controllerName, "Edit"),
                new RouteValues("Company", controllerName, "Index"),
                new RouteValues("Company", controllerName, "Edit"),
                new RouteValues("Company", controllerName, "Create")
            };

            if (this.securityProfile.UserType == UserType.Bureau)
            {
                bureauRouteValues = [
                    new RouteValues("Bureau", controllerName, "Index"),
                    new RouteValues("Bureau", controllerName, "Edit"),
                    new RouteValues("Bureau", controllerName, "Create")
                ];
            }

            Func<CachedMenu, CachedMenu?> findMenu = (menu) => menu.FindByRoute([..routeValues, ..bureauRouteValues]);

            if (ControllerLinkIds.TryGetValue(controllerName, out var linkId))
            {
                // Todo: Temp workaround, we can't add controller details in NavigationLinks
                findMenu = (menu) => menu.FindById(linkId);
            }

            foreach (var menu in this.userMenus)
            {
                var link = findMenu(menu);
                if (link == null)
                {
                    continue;
                }

                if (link.HasMenuOverride)
                {
                    return true;
                }

                return this.paymentModule switch
                {
                    // remove screens from metadata that do not belong to the companies edition
                    PaymentModule.Lite or PaymentModule.NewLite => link.Lite,
                    PaymentModule.Premier or PaymentModule.NewPremier => link.Premier,
                    _ => true
                };
            }

            return false;
        }

        private bool ShouldIgnoreKey(ModelMetadata metadata)
        {
            var keyProperty = metadata.Properties.FirstOrDefault(_ => _.IsKey());
            if (keyProperty == null)
            {
                // not all types has a key.
                return false;
            }

            return this.ShouldIgnoreProperty(keyProperty);
        }
    }
}