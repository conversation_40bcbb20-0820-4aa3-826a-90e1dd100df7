namespace PaySpace.Venuta.Api.Pipeline.ModelBuilders
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Linq.Expressions;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Configuration;
    using Microsoft.OData.Edm;
    using Microsoft.OData.Edm.Csdl;
    using Microsoft.OData.Edm.Vocabularies;
    using Microsoft.OData.ModelBuilder;

    using PaySpace.Venuta.Api.Infrastructure;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Localization;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Validation;
    using PaySpace.Venuta.Validation.Annotations;

    using Expression = System.Linq.Expressions.Expression;
    using IAttachmentEntity = PaySpace.Venuta.Data.Models.Dto.IAttachmentEntity;
    using LambdaExpression = System.Linq.Expressions.LambdaExpression;

    public abstract class MetadataModelBuilder
    {
        private static readonly string[] IgnoredProperties = ["AttachmentUrl", "AttachmentUrls"];
        private static readonly IDictionary<string, string> Prefixes = new Dictionary<string, string> { { ApiConstants.NamespacePrefix, ApiConstants.Namespace } };

        private readonly IConfiguration configuration;
        private readonly IModelMetadataProvider metadataProvider;
        private readonly Version version;
        private readonly ISecurityProfile? securityProfile;

        protected MetadataModelBuilder(
            IConfiguration configuration,
            IModelMetadataProvider metadataProvider,
            Version version,
            ISecurityProfile? securityProfile)
        {
            this.configuration = configuration;
            this.metadataProvider = metadataProvider;
            this.version = version;
            this.securityProfile = securityProfile;
        }

        protected ODataConventionModelBuilder GetBuilder(string taxCountryCode)
        {
            var builder = new ODataConventionModelBuilder
            {
                OnModelCreating = b =>
                {
                    this.TranslateEnums(b, taxCountryCode);
                    NormalizeEntityNames(b);
                }
            };

            return builder;
        }

        protected void AnnotateEntities(IEdmModel edmModel)
        {
            edmModel.SetNamespacePrefixMappings(Prefixes);

            var schemaElements = (List<IEdmSchemaElement>)edmModel.SchemaElements;
            schemaElements.RemoveAll(_ => _.Name is "AttachmentUrl" or "AttachmentUrl_1OfTEntity" or "AttachmentUrls" or "AttachmentUrls_1OfTEntity");

            foreach (var entityType in schemaElements.OfType<IEdmStructuredType>())
            {
                var declaringType = edmModel.GetAnnotationValue<ClrTypeAnnotation>(entityType);
                var metadata = this.metadataProvider.GetMetadataForType(declaringType.ClrType);

                this.AnnotateEntity(edmModel, entityType, declaringType);

                if (entityType is EdmStructuredType structuredType)
                {
                    if (declaringType.ClrType.IsAssignableTo(typeof(IAttachmentEntity)))
                    {
                        // Re-add AttachmentUrl as a string Property.
                        var stringType = (IEdmPrimitiveType)edmModel.FindType("Edm.String");
                        var property = new EdmStructuralProperty(structuredType, "AttachmentUrl", new EdmStringTypeReference(stringType, true));
                        structuredType.AddProperty(property);
                    }
                    else if (declaringType.ClrType.IsAssignableTo(typeof(IAttachmentsEntity)))
                    {
                        var stringType = (IEdmPrimitiveType)edmModel.FindType("Edm.String");
                        var stringCollectionType = new EdmCollectionTypeReference(new EdmCollectionType(new EdmStringTypeReference(stringType, true)));
                        var property = new EdmStructuralProperty(structuredType, "AttachmentUrls", stringCollectionType);
                        structuredType.AddProperty(property);
                    }
                }

                foreach (var property in entityType.DeclaredProperties.OfType<IEdmStructuralProperty>())
                {
                    this.AnnotateProperty(edmModel, property, metadata);
                }
            }
        }

        protected virtual void AnnotateEntity(IEdmModel edmModel, IEdmType entityType, ClrTypeAnnotation declaringType)
        {
            var metadata = this.metadataProvider.GetMetadataForType(declaringType.ClrType);

            var displayName = metadata.GetDisplayName().TrimDto();
            var displayNameArea = metadata.GetAttribute<DisplayNameAttribute>()?.DisplayName;

            if (displayName == displayNameArea)
            {
                displayName = metadata.Name ?? declaringType.ClrType.Name;
            }

            edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "Caption", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), displayName.TrimDto().TrimResult()));

            if (metadata.Properties.All(p => p.IsReadOnly))
            {
                edmModel.SetAnnotationValue(entityType, ApiConstants.Namespace, "ReadOnly", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(true), true));
            }
        }

        protected virtual void AnnotateProperty(IEdmModel edmModel, IEdmStructuralProperty structuralProperty, ModelMetadata entityMetadata)
        {
            var taxCountryCode = this.securityProfile?.TaxCountryCode;

            var modelHelper = DefaultPropertyEdmModelHelper.Create(edmModel, structuralProperty, entityMetadata, taxCountryCode, this.version);

            modelHelper?.Annotate();
        }

        protected virtual EntitySetConfiguration<TEntity> RegisterEntity<TEntity>(ODataConventionModelBuilder builder, string entityName = null)
            where TEntity : class
        {
            // The name registered in the entity set is used in the Default Schema - which cannot be changed in the NormalizeEntityNames method.
            var config = builder.EntitySet<TEntity>(entityName ?? NameHelper.GetDisplayName(typeof(TEntity).Name));
            config.EntityType.Page(this.configuration.GetValue<int>("ODataQuerySettings:MaxTop"), this.configuration.GetValue<int>("ODataQuerySettings:PageSize"));

            var metadata = this.metadataProvider.GetMetadataForType(typeof(TEntity));
            foreach (var property in metadata.Properties)
            {
                if (this.ShouldIgnoreProperty(property))
                {
                    this.IgnoreProperty(config, property);
                }

                if (string.Equals(property.PropertyName, "EmployeeId", StringComparison.OrdinalIgnoreCase) && !property.IsKey())
                {
                    this.IgnoreEmployeeId(config, property);
                }

                if (IgnoredProperties.Any(name => string.Equals(property.PropertyName, name, StringComparison.OrdinalIgnoreCase)))
                {
                    // Remove AttachmentUrl, It will be added back later as a string property.
                    // The type "AttachmentUrl" is not compatible with OData clients.
                    this.IgnoreProperty(config, property);
                }

                if (this.version >= new Version(2, 0) && property.TryGetAttribute<DataTypeAttribute>(out var dataTypeAttribute) && dataTypeAttribute.DataType == DataType.Date)
                {
                    this.AsDate(config.EntityType, property);
                }
            }

            return config;
        }

        protected virtual ComplexTypeConfiguration<TEntity> RegisterComplexType<TEntity>(ODataConventionModelBuilder builder)
            where TEntity : class
        {
            var config = builder.ComplexType<TEntity>();

            var metadata = this.metadataProvider.GetMetadataForType(typeof(TEntity));

            foreach (var property in metadata.Properties)
            {
                if (this.ShouldIgnoreProperty(property))
                {
                    this.Ignore(config, property.ModelType, property.Name);
                }
            }

            return config;
        }

        protected virtual void IgnoreProperty<TEntity>(EntitySetConfiguration<TEntity> config, ModelMetadata modelProperty)
            where TEntity : class
        {
            this.Ignore(config.EntityType, modelProperty.ModelType, modelProperty.PropertyName);
        }

        protected virtual void IgnoreEmployeeId<TEntity>(EntitySetConfiguration<TEntity> config, ModelMetadata modelProperty)
            where TEntity : class
        {
            this.Ignore(config.EntityType, modelProperty.ModelType, modelProperty.PropertyName);
        }

        protected virtual bool ShouldIgnoreProperty(ModelMetadata modelProperty)
        {
            if (modelProperty.TryGetAttribute<IgnoreBeforeVersionAttribute>(out var ignoreVersion) && ignoreVersion.Before > this.version)
            {
                return true;
            }

            return !modelProperty.ShowForDisplay || !modelProperty.ShowForEdit || modelProperty.HasAttribute<ClientDisplayAttribute>();
        }

        protected virtual void DisableCustomFieldSelect<TEntity>(EntitySetConfiguration<TEntity> config)
            where TEntity : class
        {
            if (typeof(ICustomFieldEntityDto).IsAssignableFrom(typeof(TEntity)))
            {
                config.EntityType.Select(SelectExpandType.Disabled, nameof(ICustomFieldEntityDto.CustomFields));
            }
        }

        private void Ignore<TEntity>(StructuralTypeConfiguration<TEntity> entityTypeConfiguration, Type propertyType, string propertyName)
            where TEntity : class
        {
            var ignoreMethod = entityTypeConfiguration.GetType().GetMethod("Ignore")!.MakeGenericMethod(propertyType);
            ignoreMethod.Invoke(entityTypeConfiguration, new object[] { this.GetPropertyExpression(typeof(TEntity), propertyName) });
        }

        private void TranslateEnums(ODataConventionModelBuilder builder, string taxCountryCode)
        {
            foreach (var enumType in builder.EnumTypes)
            {
                var metadata = this.metadataProvider.GetMetadataForType(enumType.ClrType);
                foreach (var member in enumType.Members)
                {
                    if (metadata.EnumNamesAndValues!.TryGetValue(EnumUtils.ToResourceKey(member.MemberInfo), out var localizedValue))
                    {
                        member.Name = localizedValue;
                    }
                }

                // Adjustments have a separate endpoint so item should not be visible on the dropdown
                if (enumType.ClrType == typeof(LeaveEntryType))
                {
                    enumType.RemoveMember(LeaveEntryType.Adjustment);
                }

                // Brazil no longer uses the options
                if (enumType.ClrType == typeof(LeaveType) && taxCountryCode == CountryCode.BR.ToString())
                {
                    enumType.RemoveMember(LeaveType.FamilyResponsibility);
                    enumType.RemoveMember(LeaveType.Sick);
                    enumType.RemoveMember(LeaveType.Study);
                    enumType.RemoveMember(LeaveType.Special);
                }

                if (enumType.ClrType == typeof(EmploymentAction) && taxCountryCode == CountryCode.GB.ToString())
                {
                    enumType.RemoveMember(EmploymentAction.ReInBreak);
                }

                if (enumType.ClrType == typeof(AddressType) && taxCountryCode == CountryCode.IN.ToString())
                {
                    enumType.RemoveMember(AddressType.PrivateBag);
                    enumType.RemoveMember(AddressType.Postal);
                }

                this.DisableInvalidPayFrequencyEnum(enumType, taxCountryCode);
            }
        }

        private static void NormalizeEntityNames(ODataConventionModelBuilder builder)
        {
            foreach (var entitySet in builder.EntitySets)
            {
                entitySet.EntityType.Name = NameHelper.GetDisplayName(entitySet.EntityType.Name);
                entitySet.EntityType.Namespace = NameHelper.GetDtoDisplayNamespace(entitySet.EntityType.Name);
            }

            foreach (var entitySet in builder.NavigationSources)
            {
                entitySet.EntityType.Name = NameHelper.GetDisplayName(entitySet.EntityType.Name);
                entitySet.EntityType.Namespace = NameHelper.GetDtoDisplayNamespace(entitySet.EntityType.Name);
            }

            foreach (var entitySet in builder.StructuralTypes)
            {
                entitySet.Name = NameHelper.GetDisplayName(entitySet.Name);
                entitySet.Namespace = NameHelper.GetDtoDisplayNamespace(entitySet.Name);
            }

            foreach (var entitySet in builder.EnumTypes)
            {
                entitySet.Namespace = NameHelper.EnumNamespace;
            }
        }

        private void AsDate<TEntity>(StructuralTypeConfiguration<TEntity> entityTypeConfiguration, ModelMetadata property)
            where TEntity : class
        {
            if (property.IsNullableValueType)
            {
                entityTypeConfiguration.Property(this.GetDatePropertyExpression<TEntity, DateTime?>(property.Name)).AsDate();
            }
            else
            {
                entityTypeConfiguration.Property(this.GetDatePropertyExpression<TEntity, DateTime>(property.Name)).AsDate();
            }
        }

        private Expression<Func<TEntity, TDate>> GetDatePropertyExpression<TEntity, TDate>(string propertyName)
        {
            // Create parameter expression for TEntity
            var parameter = Expression.Parameter(typeof(TEntity));

            // Create property access expression
            var property = Expression.Property(parameter, propertyName);

            // Create lambda expression
            var lambda = Expression.Lambda<Func<TEntity, TDate>>(property, parameter);

            return lambda;
        }

        private LambdaExpression GetPropertyExpression(Type containerType, string propertyName)
        {
            var param = Expression.Parameter(containerType);
            var prop = Expression.Property(param, propertyName);

            return Expression.Lambda(prop, param);
        }

        private void DisableInvalidPayFrequencyEnum(EnumTypeConfiguration enumType, string taxCountryCode)
        {
            if (enumType.ClrType == typeof(PayFrequency) && taxCountryCode != CountryCode.CA.ToString())
            {
                enumType.RemoveMember(PayFrequency.SemiMonthly);
            }
        }
    }
}