namespace PaySpace.Venuta.Api.Pipeline.ModelBuilders
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using Maddalena;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Configuration;
    using Microsoft.OData.Edm;
    using Microsoft.OData.ModelBuilder;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.Components;
    using PaySpace.Venuta.Data.Models.Dto.Converters;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Dto.Employees.TaxCalculationErrorDto;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Excel.Abstractions.Messages;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Lookups;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Validation;
    using PaySpace.Venuta.Validation.Annotations;

    public interface IModelBuilder
    {
        IEdmModel GetEdmModel();
    }

    public class CompanyModelBuilder : SecureModelBuilder, IModelBuilder
    {
        private readonly IModelMetadataProvider metadataProvider;
        private readonly IStrategyRegistry strategyRegistry;
        private readonly Version version;
        private readonly PaymentModule paymentModule;
        private readonly ISecurityProfile securityProfile;

        public CompanyModelBuilder(
            IConfiguration configuration,
            IModelMetadataProvider metadataProvider,
            IStrategyRegistry strategyRegistry,
            Version version,
            PaymentModule paymentModule,
            IList<CachedMenu> userMenus,
            ISecurityProfile securityProfile)
            : base(configuration, metadataProvider, version, paymentModule, userMenus, securityProfile)
        {
            this.metadataProvider = metadataProvider;
            this.strategyRegistry = strategyRegistry;
            this.version = version;
            this.paymentModule = paymentModule;
            this.securityProfile = securityProfile;
        }

        public virtual IEdmModel GetEdmModel()
        {
            var builder = this.GetBuilder(this.securityProfile.TaxCountryCode);

            this.RegisterEntities(builder);
            this.RegisterComponents(builder);
            this.RegisterLookups(builder);

            var edmModel = builder.GetEdmModel();

            this.AnnotateEntities(edmModel);

            return edmModel;
        }

        protected virtual void RegisterEntities(ODataConventionModelBuilder builder)
        {
            this.RegisterAdminEntities(builder);
            this.RegisterEmployee(builder);
            this.RegisterEmployeeAddress(builder);
            this.RegisterEmployeeDependant(builder);
            this.RegisterEmployeePosition(builder);

            // Register entities that uses menu for permission
            this.RegisterEntity<BudgetArchiveEmployeeDto>(builder);
            this.RegisterEntity<BudgetArchiveOrganizationDto>(builder);
            this.RegisterEntity<CompanyCategoryFieldDto>(builder);
            this.RegisterEntity<CompanyCloudRoomDto>(builder);
            this.RegisterEntity<CompanyCustomFormDto>(builder);
            this.RegisterEntity<CompanyGLDetailDto>(builder);
            this.RegisterEntity<CompanyGradeFieldDto>(builder);
            this.RegisterEntity<CompanyPublicHolidayDto>(builder);
            this.RegisterEntity<CompanyQualificationDto>(builder);
            this.RegisterEntity<CompanyRegionDto>(builder);
            this.RegisterEntity<CompanyRegionHistoryDto>(builder);
            this.RegisterEntity<CompanySkillCategoryDto>(builder);
            this.RegisterEntity<CompanySkillDto>(builder);
            this.RegisterEntity<CompanyTrainingCourseDto>(builder);
            this.RegisterEntity<CostingProjectActivityDto>(builder);
            this.RegisterEntity<EmployeeAssetDto>(builder);
            this.RegisterEntity<EmployeeAttachmentDto>(builder);
            this.RegisterEntity<EmployeeBankDetailDto>(builder);
            this.RegisterEntity<EmployeeClaimDto>(builder);
            this.RegisterEntity<EmployeeCostedPayslipLineDto>(builder);
            this.RegisterEntity<EmployeeCustomFormDto>(builder);
            this.RegisterEntity<EmployeeEmploymentStatusDto>(builder);
            this.RegisterEntity<EmployeeInboxDto>(builder);
            this.RegisterEntity<EmployeeIncidentDto>(builder);
            this.RegisterEntity<EmployeeJournalDto>(builder);
            this.RegisterEntity<EmployeeLeaveAdjustmentDto>(builder);
            this.RegisterEntity<EmployeeLeaveApplicationDto>(builder);
            this.RegisterEntity<EmployeeLeaveSetupDto>(builder);
            this.RegisterEntity<EmployeeLeaveSetupEntitlementDto>(builder);
            this.RegisterEntity<EmployeeNoteDto>(builder);
            this.RegisterEntity<EmployeeOutOfOfficeDto>(builder);
            this.RegisterEntity<EmployeePayRateDto>(builder);
            this.RegisterEntity<EmployeePayslipDto>(builder);
            this.RegisterEntity<EmployeePayslipLineDto>(builder);
            this.RegisterEntity<EmployeePayslipPdfDto>(builder)?.EntityType.Property(_ => _.DownloadUrl).IsNotFilterable().OrderBy(false);
            this.RegisterEntity<EmployeeProjectDto>(builder);
            this.RegisterEntity<EmployeeQualificationDto>(builder);
            this.RegisterEntity<EmployeeRecurringCostingSplitDto>(builder);
            this.RegisterEntity<EmployeeReviewTemplateDto>(builder);
            this.RegisterEntity<EmployeeReviewHeaderDto>(builder);
            this.RegisterEntity<EmployeeReviewKpaDto>(builder);
            this.RegisterEntity<CompanyReviewProcessDto>(builder);
            this.RegisterEntity<EmployeeSkillDto>(builder);
            this.RegisterEntity<EmployeeSuspensionDto>(builder)?.EntityType.Property(_ => _.UI19DownloadUrl).IsNonFilterable().OrderBy(false);
            this.RegisterEntity<EmployeeTakeOnDto>(builder);
            this.RegisterEntity<EmployeeTaxCertificateDto>(builder);
            this.RegisterEntity<EmployeeTrainingDto>(builder);
            this.RegisterEntity<EmployeeWorkflowDto>(builder);
            this.RegisterEntity<JobManagementBudgetCostDto>(builder);
            this.RegisterEntity<JobManagementDto>(builder);
            this.RegisterEntity<JobManagementHistoryDto>(builder);
            this.RegisterEntity<LeaveAdjustmentDto>(builder);
            this.RegisterEntity<OrganizationGradeDto>(builder);
            this.RegisterEntity<OrganizationPositionDetailDto>(builder);
            this.RegisterEntity<OrganizationUnitAddressDto>(builder);
            this.RegisterEntity<OrganizationUnitDto>(builder);
            this.RegisterEntity<PayRateCategoryDto>(builder);
            this.RegisterEntity<ReportRequestDto>(builder);

            this.RegisterComplexType<ConsolidatedEmployeePayslipDto>(builder);
            this.RegisterComplexType<EmployeePayslipCommentDto>(builder);
            this.RegisterComplexType<EmployeeWorkflowProcessDto>(builder);
            this.RegisterComplexType<PayslipLineDto>(builder);
            this.RegisterComplexType<PositionOrganizationUnit>(builder);
            this.RegisterComplexType<DataExtractionProgress>(builder);

            // Register entities that uses custom system area for permissions
            this.RegisterEntityWithArea<EditPayslip>(builder, SystemAreas.Payslip.View);
            this.RegisterEntityWithArea<EmployeeLumpSumDto>(builder, SystemAreas.Payslip.View);
            this.RegisterEntityWithArea<EmployeePayslipTakeOnDto>(builder, SystemAreas.Payslip.View);

            this.RegisterComplexTypeWithArea<CompanyEFTFile>(builder, SystemAreas.SalaryPaymentFiles.Area);
            this.RegisterComplexTypeWithArea<GeneralLedgerExtraction>(builder, SystemAreas.GeneralLedgerReport.Area);
            this.RegisterComplexTypeWithArea<GeneralLedgerExtractionProgress>(builder, SystemAreas.GeneralLedgerReport.Area);

            if (this.securityProfile.UserType is not UserType.Employee)
            {
                this.RegisterEntityWithNoSecurity<CompanySettingDto>(builder);
            }

            if (this.securityProfile.UserType is UserType.Bureau)
            {
                this.RegisterBureauEntities(builder);
            }

            if (this.paymentModule is not (PaymentModule.Lite or PaymentModule.NewLite))
            {
                this.RegisterEntity<EmployeeRecurringTemplateDto>(builder);
            }

            if (this.securityProfile.TaxCountryCode == Country.SouthAfrica.CountryCode.ToString())
            {
                this.RegisterEntity<EmployeeEtiTakeOnDto>(builder);
            }

            if (this.securityProfile.TaxCountryCode == Country.Singapore.CountryCode.ToString())
            {
                this.RegisterEntity<EmployeeIR8ADto>(builder);
                this.RegisterEntity<EmployeeAppendix8ADto>(builder);
                this.RegisterEntity<EmployeeAppendix8BDto>(builder);
            }

            if (this.version >= PaySpaceApiVersions.V2_0)
            {
                this.RegisterEntity<ComponentCompanyDto>(builder);
                this.RegisterEntity<CompanyFrequencyDto>(builder);
                this.RegisterEntity<CompanyRunDto>(builder);
                this.RegisterEntity<EmployeeRecurringCostingDto>(builder);
                this.RegisterEntity<EmployeeRecurringCostingDetailDto>(builder);
                this.RegisterEntity<TaxCalculationErrorDto>(builder);
            }

            // Always available.
            this.RegisterEntityWithNoSecurity<CompanyAddressDto>(builder);
            this.RegisterEntityWithNoSecurity<CompanyDto>(builder);
            this.RegisterEntityWithNoSecurity<CompanySettingValueDto>(builder);
            this.RegisterEntityWithNoSecurity<CustomFieldDetailDto>(builder);
            this.RegisterEntityWithNoSecurity<CustomFieldEditorOption>(builder);

            this.RegisterComplexTypeWithNoSecurity<FileUploadDto>(builder);
            this.RegisterComplexTypeWithNoSecurity<CustomFieldDto>(builder);
            this.RegisterComplexTypeWithNoSecurity<BudgetArchiveEmployeeFigureDto>(builder);
            this.RegisterComplexTypeWithNoSecurity<BudgetArchiveOrganizationFigureDto>(builder);
            this.RegisterComplexTypeWithNoSecurity<BudgetArchiveExtractionProgress>(builder);

            // Remove types not supported by OData XML Schema
            builder.RemoveStructuralType(typeof(AttachmentUrl));
            builder.RemoveStructuralType(typeof(AttachmentUrls));
        }

        protected virtual void RegisterAdminEntities(ODataConventionModelBuilder builder)
        {
            this.RegisterEntity<CompanyGroupExchangeRateDto>(builder);
        }

        protected virtual void RegisterBureauEntities(ODataConventionModelBuilder builder)
        {
        }

        protected virtual void RegisterComponents(ODataConventionModelBuilder builder)
        {
            this.RegisterEntity<ComponentHistoryDto>(builder);
            this.RegisterEntity<EmployeeBonusProvisionResult>(builder);
            this.RegisterEntity<EmployeeDisabilityResult>(builder);
            this.RegisterEntity<EmployeeCompanyCarDetailResult>(builder);
            this.RegisterEntity<EmployeeComponentResult>(builder);
            this.RegisterEntity<EmployeeGroupLifeResult>(builder);
            this.RegisterEntity<EmployeeIncomeProtectionResult>(builder);
            this.RegisterEntity<EmployeeMedicalResult>(builder);
            this.RegisterEntity<EmployeePensionFundResult>(builder);
            this.RegisterEntity<EmployeeGarnisheeResult>(builder);
            this.RegisterEntity<EmployeeSavingResult>(builder);
            this.RegisterEntity<EmployeeTravelBusinessUsageResult>(builder);
            this.RegisterEntity<EmployeeUnionResult>(builder);
            this.RegisterEntity<EmployeeRetirementAnnuityResult>(builder);
            this.RegisterEntity<EmployeeLoanResult>(builder);
            this.RegisterEntity<EmployeeHousePaymentResult>(builder);
            this.RegisterEntity<EmployeeTableBuilderDto>(builder);
            this.RegisterEntity<EmployeeComponentValueResult>(builder);
        }

        protected virtual void RegisterLookups(ODataConventionModelBuilder builder)
        {
            var strategies = this.strategyRegistry.LookupStrategies
                .GroupBy(_ => _.Name)
                .Select(_ => new { Name = _.Key, DataType = _.Select(x => x.DataType).First() });

            foreach (var lookupStrategy in strategies)
            {
                var entityType = builder.AddEntityType(lookupStrategy.DataType);
                var name = this.version >= PaySpaceApiVersions.V2_0 ? $"Lookup_{lookupStrategy.Name}" : lookupStrategy.Name;
                var config = builder.AddEntitySet(name, entityType);

                var properties = this.metadataProvider.GetMetadataForProperties(lookupStrategy.DataType);
                foreach (var modelProperty in properties)
                {
                    if (this.ShouldIgnoreProperty(modelProperty))
                    {
                        var prop = lookupStrategy.DataType.GetProperty(modelProperty.PropertyName!);
                        config.EntityType.RemoveProperty(prop);
                    }
                }
            }
        }

        protected override EntitySetConfiguration<TEntity> RegisterEntity<TEntity>(ODataConventionModelBuilder builder, string entityName = null)
        {
            var config = base.RegisterEntity<TEntity>(builder, entityName: entityName);
            if (config == null)
            {
                return null;
            }

            var metadata = this.metadataProvider.GetMetadataForType(typeof(TEntity));

            this.DisableCustomFieldSelect(config);

            if (metadata.TryGetAttribute<DisableOdataFeaturesAttribute>(out _))
            {
                foreach (var metadataProperty in metadata.Properties)
                {
                    if (metadataProperty.TryGetAttribute<DisableOdataFeaturesAttribute>(out var propertyFeatures))
                    {
                        config.EntityType.Filter(propertyFeatures.EnableFilter, metadataProperty.Name);
                        config.EntityType.OrderBy(propertyFeatures.EnableOrderBy, metadataProperty.Name);
                    }
                    else
                    {
                        config.EntityType.Filter(false, metadataProperty.Name);
                        config.EntityType.OrderBy(false, metadataProperty.Name);
                    }
                }

                config.EntityType.Filter(false);
                config.EntityType.OrderBy(false);
                config.EntityType.Expand(SelectExpandType.Disabled);
            }

            return config;
        }

        protected virtual void RemoveEmployeeNavigationProperties(EntitySetConfiguration<EmployeeDto> employee)
        {
            employee.EntityType.Ignore(_ => _.BankDetails);
            employee.EntityType.Ignore(_ => _.Company);
            employee.EntityType.Ignore(_ => _.EmployeeSuspensions);
            employee.EntityType.Ignore(_ => _.EmploymentStatus);
            employee.EntityType.Ignore(_ => _.OrganizationGroups);
            employee.EntityType.Ignore(_ => _.PayRates);
            employee.EntityType.Ignore(_ => _.Payslips);
            employee.EntityType.Ignore(_ => _.Positions);
            employee.EntityType.Ignore(_ => _.Projects);
        }

        private void RegisterEmployeePosition(ODataConventionModelBuilder builder)
        {
            var employeePosition = this.RegisterEntity<EmployeePositionDto>(builder);
            if (employeePosition != null)
            {
                if (this.version >= PaySpaceApiVersions.V1_1)
                {
                    employeePosition.EntityType.Ignore(_ => _.OrganizationGroups);
                    var p = employeePosition.EntityType.CollectionProperty(_ => _.OrganizationGroups_1_1);
                    p.Name = "OrganizationGroups";
                    p.NullableProperty = true;
                }
                else
                {
                    employeePosition.EntityType.Ignore(_ => _.OrganizationGroups_1_1);
                }
            }
        }

        private void RegisterEmployeeAddress(ODataConventionModelBuilder builder)
        {
            if (this.securityProfile.UserId is 443 or 672458)
            {
                // For the integration tests we need this to me a complex type.
                // If we don't then the odata client sees it as a navigation property and then wants to post EmployeeAddress separately.
                this.RegisterComplexType<EmployeeAddressDto>(builder);
            }
            else
            {
                // Register as Entity for EmployeeAddress endpoint
                this.RegisterEntity<EmployeeAddressDto>(builder);
            }
        }

        private void RegisterEmployee(ODataConventionModelBuilder builder)
        {
            if (PaySpaceConstants.EmployeeHistoryCountries.Contains(this.securityProfile.TaxCountryCode))
            {
                var employee = this.RegisterEntity<EmployeeHistoryDto>(builder, entityName: nameof(EmployeeDto).TrimDto());
                if (employee != null)
                {
                    employee.EntityType.Name = nameof(EmployeeDto).TrimDto();
                    employee.EntityType.Property(_ => _.ImageDownloadUrl).IsNonFilterable().OrderBy(false);
                }
            }
            else
            {
                var employee = this.RegisterEntity<EmployeeDto>(builder);
                employee?.EntityType.Property(_ => _.ImageDownloadUrl).IsNonFilterable().OrderBy(false);

                this.RemoveEmployeeNavigationProperties(employee);
            }
        }

        private void RegisterEmployeeDependant(ODataConventionModelBuilder builder)
        {
            if (PaySpaceConstants.DependantHistoryCountries.Contains(this.securityProfile.TaxCountryCode))
            {
                var employeeDependant = this.RegisterEntity<EmployeeDependantHistoryDto>(builder, entityName: nameof(EmployeeDependantDto).TrimDto());
                if (employeeDependant != null)
                {
                    employeeDependant.EntityType.Name = nameof(EmployeeDependantDto).TrimDto();
                }
            }
            else
            {
                this.RegisterEntity<EmployeeDependantDto>(builder);
            }
        }
    }
}