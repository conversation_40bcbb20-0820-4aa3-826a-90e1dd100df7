namespace PaySpace.Venuta.Api.Pipeline.ModelBuilders
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.Extensions.Configuration;
    using Microsoft.OData.ModelBuilder;

    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Lookups;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Validation;
    using PaySpace.Venuta.Validation.Annotations;

    /// <summary>
    /// This is temporarily required for Brazil, and is only needed for specific ClientDisplaysAttributes of ApiAdmin.
    /// Once the custom payslips are switched over to a stored proc, this logic can be removed.
    /// </summary>
    public sealed class ApiAdminModelBuilder : CompanyModelBuilder
    {
        public ApiAdminModelBuilder(
            IConfiguration configuration,
            IModelMetadataProvider metadataProvider,
            IStrategyRegistry strategyRegistry,
            Version version,
            PaymentModule paymentModule,
            IList<CachedMenu> userMenus,
            ISecurityProfile securityProfile)
            : base(configuration, metadataProvider, strategyRegistry, version, paymentModule, userMenus, securityProfile)
        {
        }

        protected override void RemoveEmployeeNavigationProperties(EntitySetConfiguration<EmployeeDto> employee)
        {
            // Don't remove
        }

        protected override void IgnoreProperty<TEntity>(EntitySetConfiguration<TEntity> config, ModelMetadata modelProperty)
        {
            var clientIds = modelProperty.GetAttributes<ClientDisplayAttribute>().Select(_ => _.ClientId).ToList();
            if (clientIds.Count == 0 || (clientIds.Count > 0 && !clientIds.Contains(Client.ApiAdmin.ToString(), StringComparer.OrdinalIgnoreCase)))
            {
                base.IgnoreProperty(config, modelProperty);
            }
        }

        protected override bool ShouldIgnoreProperty(ModelMetadata modelProperty)
        {
            // Don't ignore any properties marked specifically for Api Admin.
            var clientIds = modelProperty.GetAttributes<ClientDisplayAttribute>().Select(_ => _.ClientId).ToList();
            if (clientIds.Contains(Client.ApiAdmin.ToString(), StringComparer.OrdinalIgnoreCase))
            {
                return false;
            }

            return base.ShouldIgnoreProperty(modelProperty);
        }

        protected override bool HasPermission<TEntity>()
        {
            return typeof(TEntity) == typeof(CompanyGroupExchangeRateDto) || base.HasPermission<TEntity>();
        }
    }
}