namespace PaySpace.Venuta.Api.Pipeline
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;

    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.OData.Edm;
    using Microsoft.OData.Edm.Vocabularies;

    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Validation;
    using PaySpace.Venuta.Validation.Annotations;

    public class DefaultPropertyEdmModelHelper
    {
        private readonly IEdmModel edmModel;
        private readonly IEdmStructuralProperty structuralProperty;
        private readonly ModelMetadata entityMetadata;
        private readonly ModelMetadata propertyMetadata;
        private readonly string countryCode;
        private readonly Version version;

        protected DefaultPropertyEdmModelHelper(
            IEdmModel edmModel,
            IEdmStructuralProperty structuralProperty,
            ModelMetadata entityMetadata,
            ModelMetadata propertyMetadata,
            string countryCode,
            Version version)
        {
            this.edmModel = edmModel;
            this.structuralProperty = structuralProperty;
            this.entityMetadata = entityMetadata;
            this.propertyMetadata = propertyMetadata;
            this.countryCode = countryCode;
            this.version = version;
        }

        public virtual void Annotate()
        {
            this.AnnotateCaption();
            this.AnnotateStringLength();
            this.AnnotateEnum();
            this.AnnotateIsEditable();
            this.AnnotateOdataFeatures();
            this.AnnotateValidationInfo();

            var isVersionedReadOnly = this.AnnotateIsReadOnly();

            this.AnnotateVisibility(isVersionedReadOnly);
            this.AnnotateLookup(isVersionedReadOnly);
            this.AnnotateRequired(isVersionedReadOnly);
        }

        private void AnnotateValidationInfo()
        {
            var companysettings = new List<string>();
            var metadataInfoAttributes = this.propertyMetadata.GetAttributes<MetadataInfoAttribute>().ToArray();
            if (metadataInfoAttributes.Length > 0)
            {
                this.ApplyMetadataInfo(metadataInfoAttributes, companysettings);
            }

            if (this.propertyMetadata.HasAttribute<ClientRequiredAttribute>())
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "HasAdditionalValidation", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(false), true));
            }

            if (this.propertyMetadata.TryGetAttribute<CompanyVisibleAttribute>(out var companyVisibleAttribute))
            {
                companysettings.Add(companyVisibleAttribute.Code);
            }

            if (companysettings.Count > 0)
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "CompanySetting", new EdmStringConstant(EdmCoreModel.Instance.GetString(false), string.Join(',', companysettings)));
            }
        }

        public static DefaultPropertyEdmModelHelper Create(IEdmModel model, IEdmStructuralProperty structuralProperty, ModelMetadata entityMetadata, string countryCode, Version version)
        {
            var propertyMetadata = entityMetadata.Properties[structuralProperty.Name];
            if (propertyMetadata == null)
            {
                return null;
            }

            return new DefaultPropertyEdmModelHelper(model, structuralProperty, entityMetadata, propertyMetadata, countryCode, version);
        }

        private void ApplyMetadataInfo(MetadataInfoAttribute[] metadataInfoAttributes, List<string> companysettings)
        {
            var metadataInfoAttribute = metadataInfoAttributes.FirstOrDefault(_ => _.TaxCountryCodes.Contains(this.countryCode)) ?? metadataInfoAttributes.FirstOrDefault(_ => _.TaxCountryCodes.Length == 0);
            if (metadataInfoAttribute == null)
            {
                return;
            }

            if (metadataInfoAttribute.MustBeUniqueValue)
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Unique", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(false), true));
            }

            if (metadataInfoAttribute.HasAdditionalValidation)
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "HasAdditionalValidation", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(false), true));
            }

            if (!string.IsNullOrEmpty(metadataInfoAttribute.RegExFormat))
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "RegExFormat", new EdmStringConstant(EdmCoreModel.Instance.GetString(false), metadataInfoAttribute.RegExFormat));
            }

            if (metadataInfoAttribute.CompanySettings != null)
            {
                companysettings.AddRange(metadataInfoAttribute.CompanySettings);
            }
        }

        private void AnnotateOdataFeatures()
        {
            if (this.entityMetadata.HasAttribute<DisableOdataFeaturesAttribute>())
            {
                var enableFilter = false;
                var enableOrderBy = false;

                if (this.propertyMetadata.TryGetAttribute<DisableOdataFeaturesAttribute>(out var propertyFeatures))
                {
                    enableFilter = propertyFeatures.EnableFilter;
                    enableOrderBy = propertyFeatures.EnableOrderBy;
                }

                if (!enableFilter)
                {
                    this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "CanFilter", new EdmStringConstant(EdmCoreModel.Instance.GetString(false), "false"));
                }

                if (!enableOrderBy)
                {
                    this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "CanOrderBy", new EdmStringConstant(EdmCoreModel.Instance.GetString(false), "false"));
                }
            }
        }

        private void AnnotateRequired(bool isVersionedReadOnly)
        {
            if (!isVersionedReadOnly && this.propertyMetadata.IsRequired)
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Required", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(false), true));
            }

            if (!isVersionedReadOnly && this.propertyMetadata.TryGetAttribute<ClientRequiredAttribute>(out var clientRequiredAttribute))
            {
                var value = clientRequiredAttribute.Conditional && !this.propertyMetadata.IsRequired ? "conditional" : "true";
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Required", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), value));
            }
        }

        private void AnnotateLookup(bool isVersionedReadOnly)
        {
            if (!isVersionedReadOnly && this.propertyMetadata.TryGetAttribute<BulkUploadLookupAttribute>(out var bulkUploadLookupAttribute) && this.IsTaxCountryDependant(bulkUploadLookupAttribute))
            {
                var relatedLookupName = string.Empty;
                var lookupEndpoint = string.IsNullOrEmpty(bulkUploadLookupAttribute.OverrideLookup) ? this.propertyMetadata.PropertyName : bulkUploadLookupAttribute.OverrideLookup;

                var relatedPropertyName = bulkUploadLookupAttribute.RelatedProperty;
                if (!string.IsNullOrEmpty(relatedPropertyName))
                {
                    var relatedProp = this.entityMetadata.Properties.First(_ => _.Name == relatedPropertyName);
                    relatedLookupName = relatedProp.Name;
                    if (relatedProp.TryGetAttribute<BulkUploadLookupAttribute>(out var relatedLookupAttribute) && !string.IsNullOrWhiteSpace(relatedLookupAttribute.OverrideLookup))
                    {
                        relatedLookupName = relatedLookupAttribute.OverrideLookup;
                    }
                }

                var lookupName = !string.IsNullOrEmpty(relatedLookupName) ? $"{relatedLookupName}/{{{bulkUploadLookupAttribute.RelatedProperty}}}" : lookupEndpoint;

                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Lookup", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), lookupName));

                if (bulkUploadLookupAttribute.IsEnumType)
                {
                    this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "IsEnumType", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(true), true));
                }
            }
        }

        private void AnnotateVisibility(bool isVersionedReadOnly)
        {
            BulkUploadHiddenAttribute bulkUploadHiddenAttribute = null;

            if (this.propertyMetadata.IsCustomHiddenField())
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Hidden", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(true), true));
            }
            else if (isVersionedReadOnly || this.propertyMetadata.TryGetAttribute(out bulkUploadHiddenAttribute))
            {
                var hiddenMode = bulkUploadHiddenAttribute?.FormHidden == true ? "FormHidden" : "Hidden";

                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, hiddenMode, new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(true), true));
            }
        }

        private void AnnotateStringLength()
        {
            var countryAttributes = this.propertyMetadata.GetAttributes<CountryStringLengthAttribute>().ToArray();
            var stringLength = countryAttributes.FirstOrDefault(_ => _.TaxCountryCodes.Contains(this.countryCode)) ?? countryAttributes.FirstOrDefault(_ => _.TaxCountryCodes.Length == 0);
            if (stringLength != null)
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "MaxLength", new EdmIntegerConstant(EdmCoreModel.Instance.GetInt32(false), stringLength.MaximumLength));

                if (stringLength.MinimumLength > 0)
                {
                    this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "MinLength", new EdmIntegerConstant(EdmCoreModel.Instance.GetInt32(false), stringLength.MinimumLength));
                }

                return;
            }

            var stringLengthAttribute = this.propertyMetadata.GetAttributes<StringLengthAttribute>().FirstOrDefault(_ => _ is not CountryStringLengthAttribute);
            if (stringLengthAttribute != null)
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "MaxLength", new EdmIntegerConstant(EdmCoreModel.Instance.GetInt32(false), stringLengthAttribute.MaximumLength));

                if (stringLengthAttribute.MinimumLength > 0)
                {
                    this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "MinLength", new EdmIntegerConstant(EdmCoreModel.Instance.GetInt32(false), stringLengthAttribute.MinimumLength));
                }
            }
        }

        private void AnnotateCaption()
        {
            var caption = this.propertyMetadata.GetDisplayName();
            if (caption.StartsWith("lbl"))
            {
                caption = this.propertyMetadata.Name;
            }

            this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Caption", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), caption));
        }

        private void AnnotateEnum()
        {
            if (this.propertyMetadata.IsEnum)
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Enum", new EdmStringConstant(EdmCoreModel.Instance.GetString(true), this.propertyMetadata.UnderlyingOrModelType.Name));
            }
        }

        private void AnnotateIsEditable()
        {
            if (this.propertyMetadata.HasAttribute<ODataNotEditableAttribute>())
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "Editable", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(false), false));
            }
        }

        private bool AnnotateIsReadOnly()
        {
            // the versioned readonly attribute overrides BulkUploadLookup and ClientRequired attributes
            var isVersionedReadOnly = this.propertyMetadata.TryGetAttribute<ReadOnlyFromVersionAttribute>(out var versionedReadonlyAttribute) && versionedReadonlyAttribute.From <= this.version;
            if (isVersionedReadOnly || this.propertyMetadata.IsReadOnly || this.propertyMetadata.HasAttribute<ODataReadOnlyAttribute>())
            {
                this.edmModel.SetAnnotationValue(this.structuralProperty, ApiConstants.Namespace, "ReadOnly", new EdmBooleanConstant(EdmCoreModel.Instance.GetBoolean(false), true));
            }

            return isVersionedReadOnly;
        }

        private bool IsTaxCountryDependant(BulkUploadLookupAttribute bulkUploadLookupAttribute)
        {
            if (!string.IsNullOrEmpty(this.countryCode) && !string.IsNullOrEmpty(bulkUploadLookupAttribute.TaxCountryCode))
            {
                return bulkUploadLookupAttribute.TaxCountryCode == this.countryCode;
            }

            return true;
        }
    }
}