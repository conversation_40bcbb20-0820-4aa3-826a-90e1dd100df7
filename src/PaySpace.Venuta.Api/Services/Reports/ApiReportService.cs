namespace PaySpace.Venuta.Api.Services.Reports
{
    using System;
    using System.Collections.Generic;
    using System.Collections.Immutable;
    using System.ComponentModel.DataAnnotations;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Api.Services.Reports.Models;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Reports;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Storage;

    public class ApiReportService : IApiReportService
    {
        private const string CacheKeyPrefix = "Reports:API:";
        private const string NotAvailable = "N/A";

        private const int XlsxReportOutputTypeId = 11;

        private static readonly DistributedCacheEntryOptions RequestStatusCacheOptions = new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(8),
        };

        private readonly IMessageBus messageBus;
        private readonly IDistributedCache distributedCache;
        private readonly IDocumentService documentService;
        private readonly IStringLocalizer localizer;
        private readonly ApplicationContext context;
        private readonly ICompanyService companyService;

        public ApiReportService(
            IMessageBus messageBus,
            IDistributedCache distributedCache,
            IDocumentService documentService,
            IStringLocalizerFactory localizerFactory,
            ApplicationContext context,
            ICompanyService companyService)
        {
            this.messageBus = messageBus;
            this.distributedCache = distributedCache;
            this.documentService = documentService;
            this.localizer = localizerFactory.Create(SystemAreas.Report.Area, null!);
            this.context = context;
            this.companyService = companyService;
        }

        public IEnumerable<ReportDetail> GetReportDetails() => ApiReports.GetReportDetails(this.localizer);

        public ReportParameterDefinition[] GetReportParameters(string reportId)
        {
            ApiReports.TryGetReportDetail(reportId, out var reportDetail);

            return ReportParameters.GetParameters(reportDetail?.Id).Where(_ => _.IsVisible).ToArray();
        }

        // The report team is working on a unified interface for reports, which means this method can be simplified once that's ready.
        public Task<string> GetReportAsync(
            string reportId,
            long companyId,
            long? frequencyId,
            long userId,
            string accessToken,
            IDictionary<string, object> parameters,
            CancellationToken cancellationToken)
        {
            if (!ApiReports.TryGetReportDetail(reportId, out var reportDetail))
            {
                return Task.FromResult(default(string));
            }

            if (!reportDetail.IsClassicReport)
            {
                return this.RequestCustomReportAsync(reportId, companyId, frequencyId, userId, accessToken, parameters, reportDetail, cancellationToken);
            }

            return this.GetSsrsReportAsync(reportId, companyId, frequencyId, userId, parameters, reportDetail, cancellationToken);
        }

        public async Task<(bool IsReady, string Reason, string ReportUrl)> GetReportStatusAsync(string reportId, string requestId, CancellationToken cancellationToken)
        {
            if (await this.GetReportStatusDetailAsync(requestId) is not ReportStatusDetail detail || !detail.ReportId.Equals(reportId, StringComparison.OrdinalIgnoreCase))
            {
                throw new ValidationException("Invalid request or report ID.");
            }

            var status = await this.messageBus.RequestAsync<ReportStatusRequest, ReportStatusResponse>(new() { JobId = detail.JobId }, cancellationToken);

            if (!status.Succeeded)
            {
                var reason = status.FailureReason switch
                {
                    null => "Processing",
                    _ when status.FailureReason.MissingReportParameter() is { Length: > 0 } parameter => $"'{parameter}' parameter is required.",
                    _ when status.FailureReason.Contains("timeout", StringComparison.InvariantCultureIgnoreCase) => "Request timed out.",
                    _ when status.FailureReason.IsUnauthorized() => throw new UnauthorizedAccessException(),
                    _ => "Request failed. Please verify report parameters and retry."
                };

                return (false, reason, NotAvailable);
            }

            var reportUri = await this.documentService.GetBlobUriAsync(StorageContainers.TmpReports, status.Name?.ExtractGuid() ?? string.Empty, cancellationToken);

            return reportUri?.ToString() is { Length: > 0 } url ? (true, "Completed", url) : (false, "Report unavailable", NotAvailable);
        }

        private async Task<string> RequestCustomReportAsync(
            string reportId,
            long companyId,
            long? frequencyId,
            long userId,
            string accessToken,
            IDictionary<string, object> parameters,
            ReportDetail reportDetail,
            CancellationToken cancellationToken)
        {
            ApplyReportParameterDefinitions(reportId, companyId, frequencyId, userId, parameters);

            var reportResponse = await this.messageBus.RequestAsync<ExportCustomReportMessage, ReportStatusResponse>(new()
            {
                SendNotifications = false,
                AccessToken = accessToken,
                NotificationId = ExportReportMessage.GetHash(parameters),
                UserId = userId,
                CompanyId = companyId,
                ReportUrl = reportDetail.ReportPath + $"?CompanyId={companyId}",
                Parameters = parameters,
                Format = ApiReports.PdfFormat,
                Culture = CultureInfo.CurrentCulture
            }, cancellationToken);

            return await this.WithCachedRequestIdAsync(reportId, reportResponse.JobId);
        }

        private async Task<string> GetSsrsReportAsync(
            string reportId,
            long companyId,
            long? frequencyId,
            long userId,
            IDictionary<string, object> parameters,
            ReportDetail reportDetail,
            CancellationToken cancellationToken)
        {
            _ = frequencyId ?? throw new ValidationException("FrequencyId is required");

            parameters = new Dictionary<string, object>(parameters, StringComparer.InvariantCultureIgnoreCase);

            ApplyReportParameterDefinitions(reportId, companyId, frequencyId, userId, parameters);

            // Hidden parameter defaults
            parameters["CompanyID"] = companyId;
            parameters["FrequencyID"] = frequencyId;
            parameters["UserID"] = userId;
            parameters["fkCompanyGroupID"] = await this.companyService.GetCompanyGroupIdAsync(companyId);
            parameters["fkBaseCurrencyID"] = await this.companyService.GetTaxCurrencyIdAsync(companyId);
            parameters["ShowColumns"] = string.Empty;
            parameters["ShowExtraColumns"] = string.Empty;
            parameters["NonFormatted"] = string.Empty;

            var parameterIds = await this.GetReportParameterIdsAsync(reportDetail.InternalReportId, parameters, cancellationToken);

            var reportResponse = await this.messageBus.RequestAsync<ExportSSRSReportMessage, ReportStatusResponse>(new()
            {
                NotificationId = ExportReportMessage.GetHash(parameters),
                SendNotifications = false,
                UserId = userId,
                CompanyId = companyId,
                FormatId = XlsxReportOutputTypeId,
                ReportHeaderId = await this.GetReportHeaderIdAsync(reportDetail.InternalReportId, cancellationToken),
                ReportPath = reportDetail.ReportPath,
                Parameters =
                [
                    .. parameters.Select(_ => new ExportReportParameter
                    {
                        Id = parameterIds.TryGetValue(_.Key, out var id) ? id : 0,
                        Name = _.Key,
                        Value = _.Value?.ToString()
                    })
                ]
            }, cancellationToken);

            return await this.WithCachedRequestIdAsync(reportId, reportResponse.JobId);
        }

        private Task<long> GetReportHeaderIdAsync(string reportId, CancellationToken cancellationToken)
        {
            return this.distributedCache.GetOrCreateAsync(CacheKey($"HeaderId:{reportId}"), () =>
            {
                return this.GetReportHeaderParamLinks(reportId)
                           .Select(_ => _.ReportHeaderId)
                           .FirstOrDefaultAsync(cancellationToken);
            });
        }

        private Task<Dictionary<string, long>> GetReportParameterIdsAsync(string reportId, IDictionary<string, object> parameters, CancellationToken cancellationToken)
        {
            return this.distributedCache.GetOrCreateAsync(CacheKey($"ParameterIds:{reportId}"), () =>
            {
                return this.GetReportHeaderParamLinks(reportId)
                           .Select(_ => new { _.ReportParamId, _.ReportParameterDetail.ParamName })
                           .ToListAsync(cancellationToken)
                           .ContinueWith(_ => _.Result.Where(_ => parameters.ContainsKey(_.ParamName)).ToDictionary(_ => _.ParamName, _ => _.ReportParamId));
            });
        }

        private IQueryable<ReportHeaderParamLink> GetReportHeaderParamLinks(string reportId)
        {
            return this.context.Set<ReportHeaderParamLink>()
                               .TagWithSource()
                               .AsNoTracking()
                               .Where(_ => _.ReportHeader.ReportNameKey == reportId);
        }

        private static void ApplyReportParameterDefinitions(string reportId, long companyId, long? frequencyId, long userId, IDictionary<string, object> parameters)
        {
            var parameterDefinitions = ReportParameters.GetParameters(reportId).ToImmutableDictionary(p => p.ParameterName, StringComparer.InvariantCultureIgnoreCase);

            if (parameterDefinitions.TryGetKey("CompanyId", out _) && !parameters.ContainsKey("CompanyId"))
            {
                parameters.Add("CompanyId", companyId);
            }

            if (parameterDefinitions.TryGetKey("UserId", out _) && !parameters.ContainsKey("UserId"))
            {
                parameters.Add("UserId", userId);
            }

            if (parameterDefinitions.TryGetValue("Frequency", out var frequencyDef) && !parameters.ContainsKey("Frequency"))
            {
                if (frequencyDef.IsRequired && !frequencyId.HasValue)
                {
                    throw new ValidationException("FrequencyId is required");
                }

                if (frequencyDef.Type == ParameterDefinitionType.StringArray)
                {
                    parameters.Add("Frequency", new[] { frequencyId.ToString() });
                }
                else
                {
                    parameters.Add("Frequency", frequencyId);
                }
            }

            var parameterContext = new ParameterDefinitionContext(parameters.ToDictionary(StringComparer.InvariantCultureIgnoreCase), parameterDefinitions);

            foreach (var parameterDefinition in parameterDefinitions.Values.OrderBy(_ => _.Order))
            {
                parameters[parameterDefinition.ReportParameterName] = parameterDefinition.ResolveValue(parameterContext);
            }

            foreach (var parameterDefinition in parameterDefinitions.Values.Where(_ => _.ParameterName != _.ReportParameterName))
            {
                parameters.Remove(parameterDefinition.ParameterName);
            }
        }

        private Task<string> WithCachedRequestIdAsync(string reportId, string jobId)
        {
            var requestId = ExportReportMessage.GetHash($"{reportId}:{jobId}").ToLower();

            return this.distributedCache.SetAsync(CacheKey($"RequestId:{requestId}"), new ReportStatusDetail(reportId, jobId), RequestStatusCacheOptions)
                                        .ContinueWith(_ => requestId);
        }

        private Task<ReportStatusDetail> GetReportStatusDetailAsync(string requestId)
        {
            if (string.IsNullOrWhiteSpace(requestId))
            {
                return Task.FromResult(default(ReportStatusDetail));
            }

            return this.distributedCache.GetAsync<ReportStatusDetail>(CacheKey($"RequestId:{requestId.ToLower()}"));
        }

        private static string CacheKey(string key) => $"{CacheKeyPrefix}:{key}";

        private sealed record ReportStatusDetail(string ReportId, string JobId);
    }
}