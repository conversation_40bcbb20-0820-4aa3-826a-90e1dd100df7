SELECT ep.pkPosEmpID                                          AS EmployeePositionId,
       ep.fkEmpID                                             AS EmployeeId,
       e1.EmpNumber                                           AS EmployeeNumber,
       e1.FullName,
       ep.EffectiveDate,
       op.Description                                         AS OrganizationPosition,
       ep.fkPositionID                                        AS OrganizationPositionId,
       CASE
           WHEN orgd.PositionCode IS NOT NULL
               AND orgd.PositionCode <> N''
               THEN op.Description + N' - ' + orgd.PositionCode
           ELSE op.Description
           END                                                AS OrganizationPositionWithCode,
       orgd.LevelDescription                                  AS OccupationalLevel,
       orgd.EEFunctionDescription                             AS EeFunction,
       CASE
           WHEN @isBulkDownload = 1 THEN
               CASE WHEN ep.fkGradeID != -1 THEN og.Code ELSE NULL END
           ELSE IIF(ep.fkGradeID = -1, defaultgrade.Code, og.Code)
       END                                                    AS Grade,
       orgd.DirectlyReportsPosition,
       drpo.Description                                       AS DirectlyReportsPositionOverride,
       ogrp.UploadCode                                        AS OrganizationGroup,
       ogrp.Description                                       AS OrganizationGroupDescription,
       ept.PositionTypeDesc                                   AS PositionType,
       ep.fkOrgGroupID                                        AS OrganizationGroupId,
       ep.fkRegionID                                          AS OrganizationRegionId,
       orgr.RegionDescription                                 AS OrganizationRegion,
       opp.PayPointDescription                                AS PayPoint,
       e2.FullName                                            AS DirectlyReportsEmployee,
       e2.EmpNumber                                           AS DirectlyReportsEmployeeNumber,
       CONCAT(e2.EmpNumber, ' (', c2.CompanyName, ')')        AS DirectlyReportsEmployeeNumberWithCompanyName,
       cec.EmploymentCatCode                                  AS EmploymentCategory,
       cesc.EmploymentSubCatCode                              AS EmploymentSubCategory,
       e3.FullName                                            AS Administrator,
       e3.EmpNumber                                           AS AdministratorEmployeeNumber,
       cwfr.WorkFlowRoleDesc                                  AS WorkflowRole,
       ep.GLName                                              AS GeneralLedger,
       etu.TradeUnionDescription                              AS TradeUnion,
       ep.IsPromotion,
       cr.RosterName                                          AS Roster,
       cjm.JobNumber                                          AS Job,
       ep.Comments,
       ep.AltPositionName,
       ep.PositionEffectiveDate,
       tb.Name                                                AS CustomTradeUnion,
       ISNULL(pr.fkCompanyFrequencyID, e1.CompanyFrequencyId) AS FrequencyId,
       ep.fkPositionTypeID                                    AS PositionTypeId,
       ep.fkTradeUnionID                                      AS TradeUnionId
FROM EmployeePosition ep
         JOIN Employee e1 ON e1.pkEmpID = ep.fkEmpID
         JOIN OrganizationPosition op ON op.pkPositionID = ep.fkPositionID
         LEFT JOIN OrganizationGrade og ON og.pkGradeID = ep.fkGradeID
         LEFT JOIN OrganizationPosition drpo ON drpo.pkPositionID = ep.DirectlyReportsID
         LEFT JOIN OrganizationGroup ogrp ON ogrp.pkOrgGroupID = ep.fkOrgGroupID
         LEFT JOIN EnumPositionType ept ON ept.pkPositionTypeID = ep.fkPositionTypeID
         LEFT JOIN OrganizationRegion orgr ON orgr.pkRegionID = ep.fkRegionID
         LEFT JOIN OrganizationPayPoint opp ON opp.pkPayPointID = ep.fkPayPointID
         LEFT JOIN Employee e2 ON e2.pkEmpID = ep.fkDirectlyReportsEmpID
         LEFT JOIN Employee e3 ON e3.pkEmpID = ep.fkAdministratorID
         LEFT JOIN Company c2 ON c2.pkCompanyID = e2.fkCompanyID
         LEFT JOIN CompanyEmploymentCategory cec ON cec.pkEmploymentCategoryID = ep.fkEmploymentCategoryID
         LEFT JOIN CompanyEmploymentSubCategory cesc ON cesc.pkEmploymentSubCategoryID = ep.fkEmploymentSubCategoryID
         LEFT JOIN CompanyWorkFlowRoles cwfr ON cwfr.pkWFRoleID = ep.fkWFRoleID
         LEFT JOIN EnumTradeUnions etu ON etu.pkTradeUnionID = ep.fkTradeUnionID
         LEFT JOIN CompanyRosters cr ON cr.pkRosterID = ep.fkRosterID
         LEFT JOIN CompanyJobManagement cjm ON cjm.pkJobID = ep.fkJobID
         LEFT JOIN TableBuilder tb on tb.pkTableBuilderId = ep.fkCustomTradeUnionID

         OUTER APPLY (SELECT TOP 1 opd.PositionCode,
                                   eol.LevelDescription,
                                   eef.EEFunctionDescription,
                                   og1.Code,
                                   orgPos.Description AS DirectlyReportsPosition

                      FROM OrganizationPositionDetails opd
                               LEFT JOIN EnumOccupationalLevel eol ON eol.pkOccupationalLevel = opd.fkOccupationLevel
                               LEFT JOIN EnumEEFunction eef ON eef.pkEEFunctionID = opd.fkEEFunctionID
                               LEFT JOIN OrganizationGrade og1 ON og1.pkGradeID = opd.fkGradeID
                               LEFT JOIN OrganizationPosition orgPos ON orgPos.pkPositionID = opd.DirectlyReportsID
                      WHERE opd.fkPositionID = op.pkPositionID
                        AND opd.EffectiveDate <= ep.EffectiveDate
                      ORDER BY opd.EffectiveDate DESC) AS orgd

         OUTER APPLY (SELECT TOP 1 og.Code
                      FROM OrganizationPositionDetails opd
                        JOIN OrganizationGrade og ON og.pkGradeID = opd.fkGradeID
                      WHERE opd.fkPositionID = ep.fkPositionID
                      ORDER BY opd.EffectiveDate DESC) defaultgrade

         OUTER APPLY (SELECT TOP 1 pr.fkCompanyFrequencyID
                      FROM PayRates pr
                      WHERE pr.fkEmpID = ep.fkEmpID) pr


WHERE e1.fkCompanyID = @companyId
  AND (e1.IsMockEmployee = 0 OR e1.IsMockEmployee IS NULL)
  AND (e1.ApplicantIndicator = 0 OR e1.ApplicantIndicator IS NULL)
  AND (e1.IsCompanyRecurringTemplate = 0 OR e1.IsCompanyRecurringTemplate IS NULL)
  AND (@includeTerminated = 1
    OR EXISTS(SELECT 1
             FROM EmployeeEmploymentStatus es
             WHERE es.fkCompanyID = @companyId
                AND es.fkEmpID = e1.pkEmpID
                AND (es.TerminationDate IS NULL OR es.TerminationDate >= @effectiveDate)))
  AND ep.pkPosEmpID = (SELECT TOP 1 ep2.pkPosEmpID
                       FROM EmployeePosition ep2
                       WHERE ep2.fkEmpID = ep.fkEmpID
                         AND ep2.EffectiveDate <= @effectiveDate
                       ORDER BY ep2.EffectiveDate DESC)