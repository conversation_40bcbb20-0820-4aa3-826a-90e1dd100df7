"use strict";

import gulp from "gulp";
import concat from "gulp-concat";
import cssmin from "gulp-cssmin";
import fs from "fs";
import jsonmerge from "gulp-merge-json";
import jsonmodify from "gulp-json-modify";
import modify from "gulp-modify-file";
import uglify from "gulp-uglify";
import merge from "merge-stream";

import bundleconfig from "./bundleconfig.json" assert { type: "json" };

let regex = {
    css: /\.css$/
};

let paths = {
    wwwroot: "./wwwroot/",
    css: "./wwwroot/css/",
    data: "./wwwroot/data/"
};

let cultures = [
    "af",
    "af-ZA",
    "zu",
    "zu-ZA",
    "en",
    "en-AU",
    "en-BZ",
    "en-CA",
    "en-IE",
    "en-JM",
    "en-NZ",
    "en-PH",
    "en-ZA",
    "en-TT",
    "en-GB",
    "en-US",
    "en-ZW",
    "pt",
    "pt-PT",
    "pt-BR",
    "fr",
    "fr-FR",
    "pl",
    "pl-PL",
    "fr-CA",
    "en-MY",
    "en-SG",
    "en-IN",
    "de",
    "de-DE"
];

gulp.task("cldr-overrides", function () {
    let overrideDecimalSeperatorCultures = ["af", "af-ZA", "zu", "zu-ZA", "en-ZA"];

    let tasks = overrideDecimalSeperatorCultures.map(function (culture) {
        console.log(culture);
        return gulp
            .src("./wwwroot/lib/cldr-data/main/" + culture + "/numbers.json", { base: ".", allowEmpty: true })
            .pipe(jsonmodify({
                key: "main." + culture + ".numbers.symbols-numberSystem-latn.decimal",
                value: '.'
            }))
            .pipe(gulp.dest('.'));
    });
    return merge(tasks);
});

gulp.task("cldr-cultures", function () {
    let nonExistentCultures = [
        { src: "en", dest: "en-US" },
        { src: "af", dest: "af-ZA" },
        { src: "zu", dest: "zu-ZA" },
        { src: "fr", dest: "fr-FR" },
        { src: "pl", dest: "pl-PL" },
        { src: "pt", dest: "pt-BR" },
        { src: "de", dest: "de-DE" }
    ];

    let tasks = nonExistentCultures.map(function (culture) {
        console.log(culture);
        return gulp
            .src("./wwwroot/lib/cldr-data/main/" + culture.src + "/**/*")
            .pipe(gulp.dest("./wwwroot/lib/cldr-data/main/" + culture.dest));
    });
    return merge(tasks);
});

gulp.task("cldr-bundle", function () {
    let tasks = cultures.map(function (culture) {
        return gulp
            .src([
                "./wwwroot/lib/cldr-data/supplemental/likelySubtags.json",
                "./wwwroot/lib/cldr-data/main/" + culture + "/numbers.json",
                "./wwwroot/lib/cldr-data/supplemental/numberingSystems.json",
                "./wwwroot/lib/cldr-data/main/" + culture + "/currencies.json",
                "./wwwroot/lib/cldr-data/supplemental/currencyData.json",
                "./wwwroot/lib/cldr-data/main/" + culture + "/ca-gregorian.json",
                "./wwwroot/lib/cldr-data/main/" + culture + "/dateFields.json",
                "./wwwroot/lib/cldr-data/supplemental/plurals.json",
                "./wwwroot/lib/cldr-data/main/" + culture + "/units.json"
            ])
            .pipe(jsonmerge({
                fileName: "cldr-data." + culture + ".min.js",
                startObj: [],
                concatArrays: true
            }))
            .pipe(modify(function (content, path, file) {
                return `var CldrData = ${content};`;
            }))
            .pipe(uglify().on('error', console.log))
            .pipe(gulp.dest(paths.data));
    });
    return merge(tasks);
});

gulp.task("min:iana-tz-data", function (callback) {
    let regions = [
        "Africa",
        "Atlantic",
        "America",
        "Asia",
        "Europe",
        "Etc",
        "Pacific",
        "Indian",
        "Australia"
    ];

    let data = JSON.parse(fs.readFileSync("./wwwroot/lib/iana-tz-data/iana-tz-data.json", "utf8"));

    regions.forEach(function (region) {
        let t = {};
        t[region] = data.zoneData[region];

        let content = {
            "version": "2019b",
            zoneData: t
        };

        fs.writeFile(paths.data + 'iana-tz-data.' + region + '.min.js', `var TzData = ${JSON.stringify(content)};`, 'utf8', callback);
    });
});

gulp.task("min:css", function () {
    let tasks = getBundles(regex.css).map(function (bundle) {
        return gulp
            .src(bundle.inputFiles, { base: "." })
            .pipe(concat(bundle.outputFileName))
            .pipe(cssmin({
                relativeTo: "./wwwroot/css",
                showLog: true
            }))
            .pipe(gulp.dest("."));
    });
    return merge(tasks);
});

gulp.task("assets:cldr-data-main", function () {
    let tasks = cultures.map(function (culture) {
        console.log(culture);
        return gulp
            .src("./node_modules/cldr-data/main/" + culture + "/**/*")
            .pipe(gulp.dest("./wwwroot/lib/cldr-data/main/" + culture));
    });

    return merge(tasks);
});

gulp.task("assets:cldr-data-supplemental", function () {
    return gulp
        .src("./node_modules/cldr-data/supplemental/**/*")
        .pipe(gulp.dest("./wwwroot/lib/cldr-data/supplemental"));
});

gulp.task("assets:iana-tz-data", function () {
    return gulp
        .src("./node_modules/iana-tz-data/**/*")
        .pipe(gulp.dest("./wwwroot/lib/iana-tz-data"));
});

gulp.task("assets", gulp.parallel("assets:cldr-data-main", "assets:cldr-data-supplemental", "assets:iana-tz-data"));
gulp.task("cldr", gulp.series("cldr-overrides", "cldr-cultures", "cldr-bundle"));
gulp.task("min", gulp.parallel("min:css", "min:iana-tz-data"));
gulp.task("build", gulp.series("assets", "cldr", "min"));

function getBundles(regexPattern) {
    return bundleconfig.filter(function (bundle) {
        return regexPattern.test(bundle.outputFileName);
    });
}