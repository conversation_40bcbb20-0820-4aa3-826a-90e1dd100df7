"use strict";

import gulp from "gulp";
import concat from "gulp-concat";
import cssmin from "gulp-cssmin";
import merge from "merge-stream";

import bundleconfig from "./bundleconfig.json" assert { type: "json" };

let regex = {
    css: /\.css$/
};

gulp.task("min:css", function () {
    let tasks = getBundles(regex.css).map(function (bundle) {
        return gulp
            .src(bundle.inputFiles, { base: "." })
            .pipe(concat(bundle.outputFileName))
            .pipe(cssmin({
                relativeTo: "./wwwroot/css",
                showLog: true
            }))
            .pipe(gulp.dest("."));
    });
    return merge(tasks);
});

gulp.task("min", gulp.parallel("min:css"));
gulp.task("build", gulp.series("min"));

function getBundles(regexPattern) {
    return bundleconfig.filter(function (bundle) {
        return regexPattern.test(bundle.outputFileName);
    });
}