using Microsoft.AspNetCore.Hosting;

[assembly: HostingStartup(typeof(PaySpace.Venuta.StartupHosts))]

namespace PaySpace.Venuta
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net;

    using Dapper;

    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.HostFiltering;
    using Microsoft.AspNetCore.HttpOverrides;
    using Microsoft.Data.SqlClient;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;

    public class StartupHosts : IHostingStartup
    {
        public void Configure(IWebHostBuilder builder)
        {
            builder.ConfigureServices((hostingContext, services) =>
            {
                var hosts = this.GetHosts(hostingContext);
                if (hosts.Count > 0)
                {
                    services.Configure<HostFilteringOptions>(options => options.AllowedHosts = hosts);
                    services.AddCors(_ => _.AddDefaultPolicy(options =>
                    {
                        if (hosts.Count > 0)
                        {
                            options.SetIsOriginAllowedToAllowWildcardSubdomains();
                            options.AllowAnyHeader();
                            options.AllowAnyMethod();
                            options.AllowCredentials();
                            options.WithOrigins(hosts.Select(host => Path.Combine("https://", host)).ToArray());
                        }
                    }));
                }

                services.Configure<ForwardedHeadersOptions>(options =>
                {
                    options.ForwardedHeaders = ForwardedHeaders.All;
                    options.ForwardedHostHeaderName = "X-ORIGINAL-HOST";

                    // Addresses of known proxies to accept forwarded headers from. (proxy/load balancer)
                    var paySpaceApplicationGatewayIp = Environment.GetEnvironmentVariable("PAYSPACE_APPLICATION_GATEWAY_IP");
                    if (!string.IsNullOrEmpty(paySpaceApplicationGatewayIp))
                    {
                        options.KnownProxies.Add(IPAddress.Parse(paySpaceApplicationGatewayIp));
                    }
                });
            });
        }

        private IList<string> GetHosts(WebHostBuilderContext hostingContext)
        {
            if (hostingContext.HostingEnvironment.IsDevelopment())
            {
                return new[] { "localhost" };
            }

            /*
            * AllowedHosts - Should only be the hostnames (Protocol Scheme (https|http) should not be added.)
            * WithOrigins - Requires a Protocol Scheme (https|http) and host
            */
            var query = "SELECT DISTINCT '*.'+SUBSTRING(APayURL, (CHARINDEX('.',APayURL )+1), 100) AS APayURL FROM Agency WHERE APayURL IS NOT NULL";
            var connectionString = hostingContext.Configuration.GetConnectionString("DefaultConnection");
            using (var connection = new SqlConnection(connectionString))
            {
                var hosts = connection.Query<string>(query).ToList();
                hosts.Add("*.azurewebsites.net");
                hosts.Add("*.azurecontainerapps.io");
                return hosts;
            }
        }
    }
}