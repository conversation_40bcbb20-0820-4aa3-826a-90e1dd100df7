namespace PaySpace.Venuta.Infrastructure
{
    using System.Diagnostics.CodeAnalysis;

    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Routing;

    public static class EmployeeEndpointRouteBuilder
    {
        private const string EmployeeArea = "Employees";

        [SuppressMessage("Roslynator", "RCS0056:A line is too long")]
        public static void MapEmployeeRoutes(this IEndpointRouteBuilder router)
        {
            router.MapAreaControllerRoute("AppraisalApproval", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/review/{EmployeeReviewHeaderId:long}/appraisal/approval", Defaults("EmployeeEvaluations", "AppraisalApproval"));

            router.MapAreaControllerRoute("Calendar", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/calendar", Defaults("Calendar", "Index"));

            router.MapAreaControllerRoute("ChangeRequestApply", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/change-request", Defaults("ChangeRequest", "Index"));
            router.MapAreaControllerRoute("ChangeRequestEdit", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/change-request/{ChangeRequestId:long}", Defaults("ChangeRequest", "Edit"));
            router.MapAreaControllerRoute("ChangeRequestView", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/change-request/{ChangeRequestId:long}/view", Defaults("ChangeRequest", "View"));

            router.MapAreaControllerRoute("Claims", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims", Defaults("Claims", "Index"));
            router.MapAreaControllerRoute("ClaimsAddItem", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims-create", Defaults("Claims", "Create"));
            router.MapAreaControllerRoute("ClaimsAddItemDetail", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims-create/{ClaimBatchId?}", Defaults("Claims", "CreateDetail"));
            router.MapAreaControllerRoute("ClaimsAttachmentsExport", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims-export/{ClaimComponentId:long}", Defaults("Claims", "ExportAttachments"));
            router.MapAreaControllerRoute("ClaimsEditItem", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims-edit/{ClaimBatchId:long}", Defaults("Claims", "Edit"));
            router.MapAreaControllerRoute("ClaimsEditItemDetail", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims-edit-item/{ClaimBatchId:long}/{ClaimComponentId:long}/detail/{ClaimDetailId:long}", Defaults("Claims", "EditDetail"));
            router.MapAreaControllerRoute("ClaimsSubmit", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims-submit/{ClaimBatchId:long}", Defaults("Claims", "SubmitForApproval"));
            router.MapAreaControllerRoute("ClaimsView", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/claims-view/{ClaimBatchId:long}", Defaults("Claims", "View"));

            router.MapAreaControllerRoute("EmployeeCustomForms", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/custom-forms", Defaults("DynamicHistoricalInformation", "Index"));
            router.MapAreaControllerRoute("EmployeeAssetRegister", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/asset-register", Defaults("AssetRegister", "Index"));
            router.MapAreaControllerRoute("EmployeeAttachment", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/attachments", Defaults("Attachments", "Index"));
            router.MapAreaControllerRoute("EmployeeAssetExport", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/export-asset-register/{assetId}", Defaults("AssetRegister", "ExportAttachments"));
            router.MapAreaControllerRoute("EmployeeAttachmentExport", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/export-attachments/{attachmentId}", Defaults("Attachments", "ExportAttachments"));
            router.MapAreaControllerRoute("EmployeeBankDetails", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/banking-details", Defaults("BankDetail", "Index"));
            router.MapAreaControllerRoute("EmployeeDependants", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/dependants", Defaults("Dependants", "Index"));
            router.MapAreaControllerRoute("EmployeeEvaluation", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/evaluation/{EmployeeReviewHeaderId:long}/section/{SectionId?}", Defaults("EmployeeEvaluations", "Index"));
            router.MapAreaControllerRoute("EmployeeExportJournalAttachments", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/export-journal/{JournalId:long}", Defaults("JournalApi", "ExportAttachments"));
            router.MapAreaControllerRoute("EmployeeHistoryProfile", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/profile/{employeeHistoryId:long}", Defaults("Profile", "EmployeeHistoryIndex"));
            router.MapAreaControllerRoute("EmployeeHistoryProfileCreate", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/profile/new", Defaults("Profile", "CreateNewEmployeeHistory"));
            router.MapAreaControllerRoute("EmployeeIncident", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/employee-incident", Defaults("EmployeeIncident", "Index"));
            router.MapAreaControllerRoute("EmployeeKpis", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/key-performance-indicator/{ProcessId?}", Defaults("EvaluationDefaults", "Index"));
            router.MapAreaControllerRoute("EmployeeNotes", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/notes-reminders", Defaults("Notes", "Index"));
            router.MapAreaControllerRoute("EmployeeOnBehalfOf", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/on-behalf-of", Defaults("OnBehalfOf", "Index"));
            router.MapAreaControllerRoute("EmployeeOutOfOffice", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/out-of-office", Defaults("OutOfOffice", "Index"));
            router.MapAreaControllerRoute("EmployeePensionEnrolment", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/employee-pension-enrolment", Defaults("EmployeePensionEnrolment", "Index"));
            router.MapAreaControllerRoute("EmployeePerformanceJournal", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/performance-journal", Defaults("Journal", "Index"));
            router.MapAreaControllerRoute("EmployeePosition", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/position", Defaults("Positions", "Index"));
            router.MapAreaControllerRoute("EmployeePositionNew", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/position/create", Defaults("Positions", "New"));
            router.MapAreaControllerRoute("EmployeeProfile", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/profile", Defaults("Profile", "Index"));
            router.MapAreaControllerRoute("EmployeeProfileDelete", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/profile/delete", Defaults("Profile", "Delete"));
            router.MapAreaControllerRoute("EmployeeProject", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/project", Defaults("Project", "Index"));
            router.MapAreaControllerRoute("EmployeeProjectCreate", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/project/create", Defaults("Project", "Post"));
            router.MapAreaControllerRoute("EmployeeQualification", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/employee-qualification", Defaults("EmployeeQualification", "Index"));
            router.MapAreaControllerRoute("EmployeeRecordOfEmploymentAdjustment", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/record-of-employment-adjustment", Defaults("RecordOfEmploymentAdjustment", "Index"));
            router.MapAreaControllerRoute("EmployeeRecurringComponent", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/recurring-components", Defaults("RecurringComponent", "Index"));
            router.MapAreaControllerRoute("EmployeeSkills", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/employee-skills", Defaults("EmployeeSkills", "Index"));
            router.MapAreaControllerRoute("EmployeeSuspension", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/suspensions", Defaults("EmployeeSuspension", "Index"));
            router.MapAreaControllerRoute("EmployeeTrainingRecords", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/training-records", Defaults("TrainingRecord", "Index"));
            router.MapAreaControllerRoute("EmployeeYearToDateTakeOn", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/year-to-date-take-on", Defaults("YearToDateTakeOn", "Index"));
            router.MapAreaControllerRoute("EmploymentStatus", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/tax-profile", Defaults("EmploymentStatus", "Index"));
            router.MapAreaControllerRoute("EmploymentStatusEdit", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/tax-profile/{EmploymentStatusId:long}", Defaults("EmploymentStatus", "Edit"));
            router.MapAreaControllerRoute("EvaluationHistory", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/evaluation-history", Defaults("EvaluationHistory", "Index"));

            router.MapAreaControllerRoute("LeaveAdjustment", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/leave-adjustment", Defaults("LeaveAdjustment", "Index"));
            router.MapAreaControllerRoute("LeaveApply", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/leave-application", Defaults("Leave", "Index"));
            router.MapAreaControllerRoute("LeaveBalance", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/leave-balance", Defaults("LeaveBalance", "Index"));
            router.MapAreaControllerRoute("LeaveCancel", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/leave/{LeaveAdjustmentId:long}/cancel", Defaults("Leave", "Cancel"));
            router.MapAreaControllerRoute("LeaveHistory", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/leave-history", Defaults("Leave", "History"));
            router.MapAreaControllerRoute("LeaveSetup", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/leave-setup", Defaults("LeaveSetup", "Index"));
            router.MapAreaControllerRoute("LeaveView", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/leave/{LeaveAdjustmentId:long}/view", Defaults("Leave", "View"));

            router.MapAreaControllerRoute("OnboardingNotification", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/on-boarding/{ProcessId}/notification", Defaults("OnboardingNotifications", "Index"));
            router.MapAreaControllerRoute("PayRate", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/pay-rate", Defaults("Payrate", "Index"));

            router.MapAreaControllerRoute("PayslipCalculationBreakdown", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/CalculationBreakdown", Defaults("PayslipEdit", "CalculationBreakdown"));
            router.MapAreaControllerRoute("PayslipCompare", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/payslips-compare", Defaults("PayslipCompare", "Index"));
            router.MapAreaControllerRoute("PayslipCostCentres", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/costcentre", Defaults("PayslipEdit", "GetCostCentres"));
            router.MapAreaControllerRoute("PayslipCreate", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/create", Defaults("Payslips", "CreateBlankPayslip"));
            router.MapAreaControllerRoute("PayslipDetail", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips-view/{PayslipId:long}", Defaults("Payslips", "Detail"));
            router.MapAreaControllerRoute("PayslipEdit", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/edit/{PayslipId:long}", Defaults("PayslipEdit", "Index"));
            router.MapAreaControllerRoute("PayslipEditCostingUpdate", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/costing/update", Defaults("PayslipEdit", "UpdateCostingCentre"));
            router.MapAreaControllerRoute("PayslipEditDefault", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/edit", Defaults("PayslipEdit", "Index"));
            router.MapAreaControllerRoute("PayslipEssTaxBreakdown", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/ess-taxbreakdown", Defaults("Payslips", "TaxBreakdown"));
            router.MapAreaControllerRoute("PayslipEssTaxBreakdownExport", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/ess-export-taxbreakdown", Defaults("Payslips", "ExportTaxBreakdown"));
            router.MapAreaControllerRoute("PayslipExport", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips-export", Defaults("Payslips", "Export"));
            router.MapAreaControllerRoute("PayslipLumpSumDirective", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/lumpsum", Defaults("PayslipEdit", "LumpSumDirective"));
            router.MapAreaControllerRoute("PayslipTaxBreakdown", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/taxbreakdown", Defaults("PayslipEdit", "TaxBreakdown"));
            router.MapAreaControllerRoute("PayslipTaxBreakdownExport", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/ExportTaxBreakdown", Defaults("PayslipEdit", "ExportTaxBreakdown"));
            router.MapAreaControllerRoute("PayslipTaxErrors", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips/{PayslipId:long}/{RunId:long}/taxerrors", Defaults("PayslipEdit", "TaxErrors"));
            router.MapAreaControllerRoute("Payslips", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/payslips", Defaults("Payslips", "Index"));

            router.MapAreaControllerRoute("RecurringCosting", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/recurring-costing", Defaults("RecurringCosting", "Index"));

            router.MapAreaControllerRoute("Tax", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/tax-certificate", Defaults("Tax", "Index"));
            router.MapAreaControllerRoute("TaxDetail", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/tax-certificate/{TaxYearId:int}/tax-profile/{EmploymentStatusId:long}", Defaults("Tax", "Detail"));

            router.MapControllerRoute("EmployeeYearEndReporting", "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/year-end-reporting", new { area = "Employees", controller = "YearEndReporting", action = "Index" });
            router.MapControllerRoute("EmployeeYearEndReportingIR8A", "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/year-end-reporting/ir8a/{employeeIR8AId?}", new { area = "Employees", controller = "YearEndReporting", action = "IR8AEdit" });
            router.MapAreaControllerRoute("EmployeeYearEndReportingAppendix8B", EmployeeArea, "{CompanyId:long}/employees/{EmployeeId:long}/{FrequencyId:long}/year-end-reporting/appendix8b/edit/{EmployeeAppendix8BId?}", Defaults("YearEndReporting", "EditAppendix8B"));
        }

        private static object Defaults(string controller, string action)
        {
            return new
            {
                controller,
                action
            };
        }
    }
}