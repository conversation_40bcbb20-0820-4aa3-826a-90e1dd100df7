namespace PaySpace.Venuta.Infrastructure
{
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Routing;

    public static class BureauEndpointRouteBuilder
    {
        private const string BureauArea = "Bureau";

        public static void MapBureauRoutes(this IEndpointRouteBuilder router)
        {
            router.MapAreaControllerRoute("BureauCategory", BureauArea, "bureau/category", Defaults("BureauCategory", "Index"));
            router.MapAreaControllerRoute("BureauConfigSettings", BureauArea, "bureau/config-settings/{CountryId:int}", Defaults("BureauConfigSettings", "Index"));
            router.MapAreaControllerRoute("BureauCustomFields", BureauArea, "bureau/custom-fields/{CountryId?}", Defaults("BureauCustomFields", "Index"));
            router.MapAreaControllerRoute("BureauCustomFormConfiguration", BureauArea, "bureau/custom-form-config/{CountryId?}", Defaults("BureauCustomFormConfig", "Index"));
            router.MapAreaControllerRoute("BureauDynamicFormBuilder", BureauArea, "bureau/company/{CompanyId:long}/frequency/{FrequencyId:long}/dynamic-form-builder", Defaults("BureauDynamicFormBuilder", "Index"));
            router.MapAreaControllerRoute("BureauPublicHoliday", BureauArea, "bureau/public-holiday", Defaults("BureauPublicHoliday", "Index"));
            router.MapAreaControllerRoute("BureauRunManagement", BureauArea, "bureau/company/{CompanyId:long}/frequency/{FrequencyId:long}/run-management/{CountryId?}", Defaults("BureauRunManagement", "Index"));
            router.MapAreaControllerRoute("BureauSecurityRole", BureauArea, "bureau/security-role", Defaults("BureauSecurityRole", "Index"));
            router.MapAreaControllerRoute("BureauSecurityRoleEdit", BureauArea, "bureau/security-role/edit/{securityGroupId?}", Defaults("BureauSecurityRole", "Edit"));
            router.MapAreaControllerRoute("BureauSuspension", BureauArea, "bureau/suspension/{CountryId?}", Defaults("BureauSuspension", "Index"));
            router.MapAreaControllerRoute("BureauTaxCountryConfiguration", BureauArea, "bureau/tax-country-config", Defaults("TaxCountryConfig", "Index"));
            router.MapAreaControllerRoute("BureauUserProfile", BureauArea, "bureau/user-profile", Defaults("BureauUserProfile", "Index"));
            router.MapAreaControllerRoute("BureauUserProfileEdit", BureauArea, "bureau/user-profile/edit", Defaults("BureauUserProfile", "Edit"));
            router.MapAreaControllerRoute("CompanyGroupLink", BureauArea, "company/{CompanyId:long}/frequency/{FrequencyId:long}/company-group-link", Defaults("CompanyGroupLink", "Index"));
            router.MapAreaControllerRoute("EmailValidation", BureauArea, "bureau/email-validation", Defaults("EmailValidation", "Index"));
            router.MapAreaControllerRoute("ReleaseNotesConfig", BureauArea, "bureau/release-notes-configuration", Defaults("ReleaseNotesConfig", "Index"));
            router.MapAreaControllerRoute("SpecialComponent", BureauArea, "bureau/special-component", Defaults("SpecialComponent", "Index"));
            router.MapAreaControllerRoute("StabilityRules", BureauArea, "bureau/stability-rules/{CountryId?}", Defaults("StabilityRules", "Index"));
            router.MapAreaControllerRoute("TableBuilderCategory", BureauArea, "bureau/table-builder-category", Defaults("TableBuilderCategory", "Index"));
            router.MapAreaControllerRoute("TableBuilderCustomFields", BureauArea, "bureau/table-builder/custom-fields/{CountryId?}", Defaults("TableBuilderCustomFields", "Index"));
            router.MapAreaControllerRoute("TaxDefinition", BureauArea, "bureau/tax-definition/{CountryId?}", Defaults("TaxDefinition", "Index"));
            router.MapAreaControllerRoute("TaxDefinitionCreate", BureauArea, "bureau/tax-definition/{CountryId:int}/create", Defaults("TaxDefinition", "Create"));
            router.MapAreaControllerRoute("TaxDefinitionEdit", BureauArea, "bureau/tax-definition/{CountryId:int}/taxability-option/{taxabilityOptionId:int}/income-base/{componentId:long}", Defaults("TaxDefinition", "Edit"));
            router.MapAreaControllerRoute("TaxYearDetail", BureauArea, "bureau/tax-year-detail/{CountryId?}", Defaults("TaxYearDetail", "Index"));
            router.MapAreaControllerRoute("TaxYearDetailCreate", BureauArea, "bureau/tax-year-detail/{CountryId:int}/create", Defaults("TaxYearDetail", "Create"));
            router.MapAreaControllerRoute("TaxYearDetailEdit", BureauArea, "bureau/tax-year-detail/{CountryId:int}/tax-year/{TaxYearId}", Defaults("TaxYearDetail", "Edit"));
            router.MapAreaControllerRoute("BureauTaxCodes", BureauArea, "bureau/tax-codes", Defaults("TaxCodes", "Index"));
        }

        private static object Defaults(string controller, string action)
        {
            return new
            {
                controller,
                action
            };
        }
    }
}