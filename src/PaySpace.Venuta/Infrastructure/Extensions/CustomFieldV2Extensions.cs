namespace PaySpace.Venuta.Infrastructure.Extensions
{
    using System;
    using System.Reflection;

    using DevExtreme.AspNet.Mvc;
    using DevExtreme.AspNet.Mvc.Builders;

    using Microsoft.AspNetCore.Mvc.Rendering;

    using PaySpace.Venuta.Localization;
    using PaySpace.Venuta.Security;

    public static class CustomFieldV2Extensions
    {
        private static readonly PropertyInfo ViewContextProperty = typeof(OptionsOwnerBuilder).GetProperty("ViewContext", BindingFlags.Instance | BindingFlags.NonPublic);

        public static DataGridColumnBuilder<T> AddCustomFieldTemplate<T>(
            this DataGridColumnBuilder<T> builder,
            Type entity,
            bool? visible = null,
            bool? readOnly = null,
            long? categoryId = null,
            bool isCompanyTableBuilder = false,
            bool isEmployeeTableBuilder = false)
        {
            var viewContext = (ViewContext)ViewContextProperty.GetValue(builder);
            var profile = viewContext.HttpContext.GetSecurityProfile();

            if (!visible.HasValue || !readOnly.HasValue)
            {
                visible = !profile.IsDenied(MetadataHelper.GetArea(typeof(T)), SystemAreas.CustomFields.Keys.Field);
                readOnly = profile.IsReadOnly(MetadataHelper.GetArea(typeof(T)), SystemAreas.CustomFields.Keys.Field);
            }

            builder.FormItem(_ =>
            {
                _.CssClass("custom-fields").Label(label => label.Visible(false));
                _.Visible(visible!.Value);
            })
            .Visible(false) // Don't show in Grid columns
            .AllowEditing(visible!.Value) // Don't show in form
            .EditCellTemplate(
                new JS(
                        "function(cellElement,cellInfo){var el = document.createElement('custom-grid-fields');el.isBasicFormat=true;el.entity='" + entity.Name +
                        "';el.categoryId='" + categoryId +
                        "';el.isCompanyTableBuilder='" + isCompanyTableBuilder +
                        "';el.isEmployeeTableBuilder='" + isEmployeeTableBuilder +
                        "';el.readOnly='" + Convert.ToString(readOnly!.Value).ToLower() +
                        "';cellElement.append(el); if(el._instance) { el._instance.proxy.init(cellInfo); }}"
                    ));

            return builder;
        }

        public static FormSimpleItemBuilder AddCustomFieldTemplate(this FormSimpleItemBuilder builder, Type entity, int colSpan = 3, int customFieldsColCount = 3)
        {
            var viewContext = (ViewContext)ViewContextProperty.GetValue(builder);
            var profile = viewContext.HttpContext.GetSecurityProfile();

            var visible = !profile.IsDenied(MetadataHelper.GetArea(entity), SystemAreas.CustomFields.Keys.Field);
            var readOnly = profile.IsReadOnly(MetadataHelper.GetArea(entity), SystemAreas.CustomFields.Keys.Field);

            builder.CssClass("custom-fields")
                .Label(_ => _.Visible(false))
                .ColSpan(colSpan)
                .Visible(visible)
                .Template(
                new JS(
                        "function(e,element){var el = document.createElement('custom-form-fields');el.entity='" + entity.Name +
                        "';el.colCount='" + customFieldsColCount +
                        "';el.isBasicFormat=true;el.readOnly='" + Convert.ToString(readOnly).ToLower() +
                        "';element.append(el); if(el._instance) { el._instance.proxy.init(e); }}"
                    ));

            return builder;
        }
    }
}