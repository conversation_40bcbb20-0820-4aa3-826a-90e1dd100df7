namespace PaySpace.Venuta.Infrastructure
{
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Routing;

    public static class AgencyEndpointRouteBuilder
    {
        private const string AgencyArea = "Agency";

        public static void MapAgencyRoutes(this IEndpointRouteBuilder router)
        {
            router.MapAreaControllerRoute("SecurityReport", AgencyArea, "agency/reports/security-report", Defaults("Reports", "DownloadSecurityReport"));
            router.MapAreaControllerRoute("BusinessPartnerDetail", AgencyArea, "agency/business-partner/details", Defaults("BusinessPartnerDetail", "Index"));
            router.MapAreaControllerRoute("BusinessPartnerDetailNew", AgencyArea, "agency/business-partner/details/add", Defaults("BusinessPartnerDetail", "AddDetails"));
            router.MapAreaControllerRoute("BusinessPartnerDetailEdit", AgencyArea, "agency/business-partner/details/{AgencyId:long}/edit", Defaults("BusinessPartnerDetail", "EditDetails"));
        }

        private static object Defaults(string controller, string action)
        {
            return new
            {
                controller,
                action
            };
        }
    }
}