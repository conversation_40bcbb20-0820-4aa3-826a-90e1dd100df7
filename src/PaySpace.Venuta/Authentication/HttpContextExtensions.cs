namespace PaySpace.Venuta.Security
{
    using System;
    using System.Globalization;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Infrastructure;

    public static class HttpContextExtensions
    {
        private static readonly string[] IgnoreRoles = new[] { Roles.MSS, Roles.ESS, Roles.CSS, Roles.Admin, Roles.TopLevel, Roles.Insights, Roles.ASS };

        public static async Task UpdateUserTypeAsync(this HttpContext httpContext, UserType userType)
        {
            var authenticateResult = await httpContext.AuthenticateAsync();
            if (authenticateResult.Succeeded)
            {
                var claimsIdentity = (ClaimsIdentity)authenticateResult.Principal.Identity;

                var userTypeClaim = httpContext.User.FindFirst(_ => _.Type == claimsIdentity.RoleClaimType && !IgnoreRoles.Contains(_.Value));
                if (userTypeClaim != null)
                {
                    claimsIdentity.RemoveClaim(userTypeClaim);
                    claimsIdentity.AddClaim(new Claim(claimsIdentity.RoleClaimType, Convert.ToString(userType), ClaimValueTypes.String));
                }

                await httpContext.SignInAsync(authenticateResult.Principal, authenticateResult.Properties);
            }
        }

        public static async Task UpdateUserLocaleAsync(this HttpContext httpContext, CultureInfo culture, TimeZoneInfo timezone)
        {
            var authenticateResult = await httpContext.AuthenticateAsync();
            if (authenticateResult.Succeeded)
            {
                var claimsIdentity = (ClaimsIdentity)authenticateResult.Principal.Identity;

                var localeClaim = httpContext.User.FindFirst(_ => _.Type == PaySpaceClaimTypes.Locale);
                if (localeClaim != null)
                {
                    claimsIdentity.RemoveClaim(localeClaim);
                    claimsIdentity.AddClaim(new Claim(PaySpaceClaimTypes.Locale, culture.Name, ClaimValueTypes.String));
                }

                var timezoneClaim = httpContext.User.FindFirst(_ => _.Type == PaySpaceClaimTypes.Timezone);
                if (timezoneClaim != null)
                {
                    claimsIdentity.RemoveClaim(timezoneClaim);
                    claimsIdentity.AddClaim(new Claim(PaySpaceClaimTypes.Timezone, timezone.Id, ClaimValueTypes.String));
                }

                await httpContext.SignInAsync(authenticateResult.Principal, authenticateResult.Properties);
            }
        }

        public static async Task UpdateEmployeeName(this HttpContext httpContext, string employeeName)
        {
            var employeeNameClaim = httpContext.User.FindFirst(_ => _.Type == PaySpaceClaimTypes.EmployeeName);
            if (employeeNameClaim != null)
            {
                var authenticateResult = await httpContext.AuthenticateAsync();
                if (authenticateResult.Succeeded)
                {
                    var claimsIdentity = (ClaimsIdentity)authenticateResult.Principal.Identity;
                    claimsIdentity.RemoveClaim(employeeNameClaim);
                    claimsIdentity.AddClaim(new Claim(PaySpaceClaimTypes.EmployeeName, employeeName, ClaimValueTypes.String));

                    await httpContext.SignInAsync(authenticateResult.Principal, authenticateResult.Properties);
                }
            }
        }
    }
}