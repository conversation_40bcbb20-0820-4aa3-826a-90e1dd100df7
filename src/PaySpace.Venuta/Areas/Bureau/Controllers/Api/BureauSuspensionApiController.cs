namespace PaySpace.Venuta.Areas.Bureau.Controllers.Api
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Data.ResponseModel;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.EntityFrameworkCore;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Areas.Bureau.ViewModels;
    using PaySpace.Venuta.Controllers.Api;
    using PaySpace.Venuta.Data.Models.Bureau;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Bureau;
    using PaySpace.Venuta.Validation.Annotations;

    [Area("Bureau")]
    [Route("bureau/{countryId}/[controller]/api")]
    [NavigationControllerName("BureauSuspension")]
    public class BureauSuspensionApiController : ApiController
    {
        private readonly IMapper mapper;
        private readonly IValidator<BureauSuspension> validator;
        private readonly IBureauComponentService bureauComponentService;
        private readonly IBureauSuspensionService bureauSuspensionService;
        private readonly IBureauSuspensionHistoryService bureauSuspensionHistoryService;
        private readonly IEnumService enumService;

        public BureauSuspensionApiController(
            IMapper mapper,
            IValidator<BureauSuspension> validator,
            IEnumService enumService,
            IBureauComponentService bureauComponentService,
            IBureauSuspensionService bureauSuspensionService,
            IBureauSuspensionHistoryService bureauSuspensionHistoryService)
        {
            this.mapper = mapper;
            this.validator = validator;
            this.enumService = enumService;
            this.bureauComponentService = bureauComponentService;
            this.bureauSuspensionService = bureauSuspensionService;
            this.bureauSuspensionHistoryService = bureauSuspensionHistoryService;
        }

        [HttpGet]
        public async Task<LoadResult> Get(DataSourceLoadOptions loadOptions, int countryId)
        {
            var suspensionResults = new List<BureauSuspensionModel>();
            var suspensions = await this.bureauSuspensionService.GetWithLatestHistoryByCountry(countryId).ToListAsync();

            foreach (var suspension in suspensions)
            {
                var history = suspension.HistoryEntityDetails?.FirstOrDefault();

                var suspensionModel = new BureauSuspensionModel
                {
                    CountryId = countryId,
                    SuspensionId = suspension.SuspensionId,
                    SuspensionReasonId = suspension.SuspensionReasonId,
                    SuspensionReason = suspension.SuspensionReason,
                    HistoryEntityId = history?.HistoryEntityDetailId,
                    ComponentDescription = history?.ComponentBureau?.ComponentDescription,
                    ComponentBureauId = history?.ComponentBureauId,
                    DefaultAmount = history?.DefaultAmount,
                    DefaultPercentage = history?.DefaultPercentage,
                    EffectiveDate = history?.EffectiveDate,
                    InactiveDate = suspension.InactiveDate,
                    ProrateQTYforBasicPay = history?.ShouldProrateBasicPayQuantity
                };

                suspensionResults.Add(suspensionModel);
            }

            return DataSourceLoader.Load(suspensionResults, loadOptions);
        }

        [HttpPost]
        [CreateMessage]
        public async Task<IActionResult> Post(string values)
        {
            var model = new BureauSuspensionModel();

            JsonConvert.PopulateObject(values, model);

            model.DefaultPercentage = model.PaymentMethod == SuspensionPaymentMethod.Percentage ? model.DefaultPercentage : null;
            model.DefaultAmount = model.PaymentMethod == SuspensionPaymentMethod.Override ? model.DefaultAmount : null;

            var entity = this.mapper.Map<BureauSuspension>(model);
            var historyEntity = this.mapper.Map<BureauSuspensionHistory>(model);
            entity.HistoryEntityDetails.Add(historyEntity);

            var result = await this.validator.ValidateAsync(entity, opt => opt.IncludeRuleSets(RuleSetNames.Create));
            result.AddToModelState(this.ModelState, null);

            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.bureauSuspensionService.AddAsync(entity);
            return this.Ok();
        }

        [HttpPut]
        [UpdateMessage]
        public async Task<IActionResult> Put(int key, string values)
        {
            var model = new BureauSuspensionModel();

            JsonConvert.PopulateObject(values, model);

            model.DefaultPercentage = model.PaymentMethod == SuspensionPaymentMethod.Percentage ? model.DefaultPercentage : null;
            model.DefaultAmount = model.PaymentMethod == SuspensionPaymentMethod.Override ? model.DefaultAmount : null;

            var suspension = await this.bureauSuspensionService.FindByIdAsync(key);
            var entity = this.mapper.Map(model, suspension);

            var history = await this.bureauSuspensionHistoryService.FindByIdAsync(model.HistoryEntityId.Value);
            var historyEntity = this.mapper.Map(model, history);

            entity.HistoryEntityDetails.Add(historyEntity);

            var result = await this.validator.ValidateAsync(entity, opt => opt.IncludeRuleSets(RuleSetNames.Update));
            result.AddToModelState(this.ModelState, null);

            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.bureauSuspensionHistoryService.UpdateAsync(historyEntity);
            await this.bureauSuspensionService.UpdateAsync(entity);
            return this.Ok();
        }

        [HttpDelete]
        [DeleteMessage]
        public async Task<IActionResult> Delete(int key)
        {
            var entity = await this.bureauSuspensionService.FindByIdAsync(key);

            await this.bureauSuspensionService.DeleteAsync(entity);

            return this.Ok();
        }

        [HttpGet("suspension-reasons")]
        public async Task<LoadResult> GetSuspensionReasonsAsync(DataSourceLoadOptions loadOptions, int countryId)
        {
            var suspensionReasons = await this.enumService.GetSuspensionReasonAsync(countryId);
            var result = suspensionReasons
                .OrderBy(_ => _.SuspensionDescription)
                .Select(_ => new { value = _.SuspensionReasonId, text = _.SuspensionDescription });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("components")]
        public async Task<LoadResult> GetComponentsAsync(DataSourceLoadOptions loadOptions, int countryId)
        {
            var components = await this.bureauComponentService.GetAllowanceComponentsByCountryId(countryId)
                .OrderBy(_ => _.ComponentDescription)
                .Select(_ => new { value = _.ComponentBureauId, text = _.ComponentDescription })
                .ToListAsync();

            return DataSourceLoader.Load(components, loadOptions);
        }

        [HttpGet("history")]
        public async Task<LoadResult> GetHistoryAsync(DataSourceLoadOptions loadOptions, long suspensionId)
        {
            var history = this.bureauSuspensionService.GetHistory(suspensionId);

            return await DataSourceLoader.LoadAsync(history, loadOptions);
        }

        [HttpDelete("history")]
        public async Task<IActionResult> DeleteHistoryAsync(long key)
        {
            await this.bureauSuspensionService.DeleteHistoryAsync(key);

            return this.Ok();
        }
    }
}