namespace PaySpace.Venuta.Areas.Bureau.Controllers
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Controllers.Api;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Data.Models.Helpers;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Bureau;
    using PaySpace.Venuta.Services.CustomFields;
    using PaySpace.Venuta.Validation.Annotations;

    [Area("Bureau")]
    [NavigationControllerName("TableBuilderCustomFields")]
    [DisplayName(SystemAreas.TableBuilderCategory.Area)]
    public class TableBuilderCustomFieldsApiController : ApiController
    {
        private readonly ICustomFieldService customFieldService;
        private readonly ITableBuilderConfigurationService tableBuilderService;
        private readonly ITableBuilderCategoryService tableBuilderCategoryService;
        private readonly IValidator<BureauCustomField> validator;

        public TableBuilderCustomFieldsApiController(
            ICustomFieldService customFieldService,
            ITableBuilderConfigurationService tableBuilderService,
            ITableBuilderCategoryService tableBuilderCategoryService,
            IValidator<BureauCustomField> validator)
        {
            this.customFieldService = customFieldService;
            this.tableBuilderService = tableBuilderService;
            this.tableBuilderCategoryService = tableBuilderCategoryService;
            this.validator = validator;
        }

        [HttpGet("[controller]/{countryId}/customfields")]
        public async Task<object> Get(DataSourceLoadOptions loadOptions, int countryId, long categoryId)
        {
            var tableBuilderFields = await this.tableBuilderService.GetCustomFields(countryId, categoryId).ToListAsync();

            var restrictedToEmployeeLevel = await this.tableBuilderCategoryService.IsRestrictedToEmployeeLevelAsync(countryId, categoryId);
            if (restrictedToEmployeeLevel)
            {
                foreach (var field in tableBuilderFields)
                {
                    field.IsCompany = false;
                    field.IsEmployee = true;
                }
            }

            return DataSourceLoader.Load(tableBuilderFields, loadOptions);
        }

        [HttpGet("[controller]/{countryId}/customfields/categories")]
        public object GetCategories(DataSourceLoadOptions loadOptions, int countryId)
        {
            var result = this.tableBuilderCategoryService.GetTableBuilderCategories(countryId).Select(_ => new
            {
                value = _.CategoryId,
                text = _.Name,
                entity = _.Code,
                employeeLevel = _.EmployeeLevel,
                companyLevel = _.CompanyLevel
            });

            return result.Any() ? DataSourceLoader.Load(result, loadOptions) : this.NotFound();
        }

        [CreateMessage]
        [HttpPost("[controller]/{countryId}/customfields")]
        public async Task<IActionResult> Post(int countryId, string values)
        {
            var customField = new BureauCustomField();

            if (values.Contains("preparedEditorOptions"))
            {
                customField.PreparedEditorOptions = new List<CustomFieldEditorOptionResult>();
            }

            if (values.Contains("customFieldOptions"))
            {
                values = EditorOptionsHelper.BuildCustomFieldLookupOptions(values, customField);
            }

            JsonConvert.PopulateObject(values, customField);
            customField.EditorOptions = EditorOptionsHelper.BuildEditorOptionsString(customField.PreparedEditorOptions);
            customField.FormId = SystemAreas.CustomFields.CustomFieldEntity.TableBuilder;
            customField.CountryId = countryId;

            var result = await this.validator.ValidateAsync(customField, _ => _.IncludeRuleSets(RuleSetNames.Create, RuleSetNames.Update));
            result.AddToModelState(this.ModelState, null);

            if (this.ModelState.IsValid)
            {
                await this.tableBuilderService.AddCustomFieldAsync(customField.CountryId, customField, null);

                return this.Ok();
            }

            return this.BadRequest(this.ModelState);
        }

        [UpdateMessage]
        [HttpPut("[controller]/{countryId}/customfields")]
        public async Task<IActionResult> Put(int countryId, long key, string values)
        {
            var customField = this.tableBuilderService.GetCustomFieldWithOptions(countryId, key);

            if (values.Contains("preparedEditorOptions"))
            {
                customField.PreparedEditorOptions = new List<CustomFieldEditorOptionResult>();
            }

            if (values.Contains("customFieldOptions"))
            {
                values = EditorOptionsHelper.BuildCustomFieldLookupOptions(values, customField);
            }

            JsonConvert.PopulateObject(values, customField);
            customField.EditorOptions = EditorOptionsHelper.BuildEditorOptionsString(customField.PreparedEditorOptions);

            var result = await this.validator.ValidateAsync(customField, _ => _.IncludeRuleSets(RuleSetNames.Create, RuleSetNames.Update));
            result.AddToModelState(this.ModelState, null);

            if (this.ModelState.IsValid)
            {
                await this.tableBuilderService.UpdateCustomFieldAsync(customField.CountryId, customField, null);

                return this.Ok();
            }

            return this.BadRequest(this.ModelState);
        }

        [HttpPut("[controller]/{countryId}/customfields/reorder")]
        public async Task<IActionResult> UpdateOrder(int countryId, long customFieldId, int? orderNumber)
        {
            var customField = this.tableBuilderService.GetCustomFieldWithOptions(countryId, customFieldId);
            customField.Order = orderNumber;
            await this.tableBuilderService.UpdateCustomFieldAsync(customField.CountryId, customField, null);

            return this.Ok(customField);
        }

        [DeleteMessage]
        [HttpDelete("[controller]/{countryId}/customfields")]
        public async Task<IActionResult> Delete(int countryId, long key)
        {
            var customField = await this.tableBuilderService.GetCustomFieldAsync(countryId, key);

            var result = await this.validator.ValidateAsync(customField, _ => _.IncludeRuleSets(RuleSetNames.Delete));
            result.AddToModelState(this.ModelState, null);

            if (this.ModelState.IsValid)
            {
                await this.customFieldService.DeleteExistingCustomFieldValuesAsync(customField.CustomFieldId, "B", customField.FormId);
                await this.tableBuilderService.DeleteCustomFieldAsync(customField.CountryId, customField, null);
                return this.Ok();
            }

            return this.BadRequest(this.ModelState);
        }

        [HttpGet("[controller]/{countryId}/customfields/lookupParents")]
        public object GetCustomFieldLookupParentsData(DataSourceLoadOptions loadOptions, int countryId, long formId)
        {
            var result = this.tableBuilderService.GetCustomFields(countryId, formId)
                .Where(_ => _.EditorType == "lookup"
                    && string.IsNullOrEmpty(_.ParentFieldCode))
                .Select(_ => new { value = _.FieldCode, text = _.FieldLabel, customFieldId = _.CustomFieldId });

            return result.Any() ? DataSourceLoader.Load(result, loadOptions) : this.NotFound();
        }

        [HttpGet("[controller]/{countryId}/customfields/lookupParentValues")]
        public object GetCustomFieldLookupParentValues(DataSourceLoadOptions loadOptions, long entity)
        {
            var result = this.customFieldService.GetFieldOptions(entity, "B")
                .Select(_ => new { value = _.Code, text = _.Description });

            return result.Any() ? DataSourceLoader.Load(result, loadOptions) : this.NotFound();
        }

        [HttpGet("[controller]/{countryId}/customfields/customFieldGroup")]
        public object GetCustomFieldGroupValues(DataSourceLoadOptions loadOptions, int countryId, long selectedValue)
        {
            var formId = SystemAreas.CustomFields.CustomFieldEntity.TableBuilder;

            var result = this.tableBuilderService.GetCustomFieldGroups(countryId, formId, selectedValue)
                .Select(_ => new { value = _.CustomFieldGroupId, text = _.Name, order = _.Order })
                .OrderBy(_ => _.order);

            return result.Any() ? DataSourceLoader.Load(result, loadOptions) : this.NotFound();
        }

        [HttpGet("[controller]/{countryId}/customfields/advancedSettings/api")]
        public async Task<IActionResult> GetCustomFieldAdvancedSettings(int countryId, long customFieldId)
        {
            var result = await this.tableBuilderService.GetAdvancedSettingsAsync(countryId, customFieldId);
            return result == null ? this.NotFound() : this.Content(result, "application/json");
        }

        [DeleteMessage]
        [HttpDelete("[controller]/{countryId}/customfields/advancedSettings/api")]
        public Task RemoveCustomFieldAdvancedSettings(int countryId, long customFieldId)
        {
            return this.tableBuilderService.RemoveAdvancedSettingsAsync(countryId, customFieldId);
        }

        [UpdateMessage]
        [HttpPost("[controller]/{countryId}/customfields/advancedSettings/api")]
        public async Task<IActionResult> AddOrUpdateAdvancedSettings(int countryId, long customFieldId, [FromBody] string values)
        {
            var customField = await this.tableBuilderService.GetCustomFieldAsync(countryId, customFieldId);
            if (customField == null)
            {
                return this.NotFound();
            }

            // Add/Overwrite the advanced settings on the custom field
            customField.AdvancedSettings = values;
            await this.tableBuilderService.AddOrUpdateAdvancedSettingsAsync(countryId, customField);

            return this.Ok(customField);
        }
    }
}