namespace PaySpace.Venuta.Areas.Bureau.Controllers
{
    using System.ComponentModel;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.ViewModels.Templates;

    [Area("Bureau")]
    [DisplayName(SystemAreas.TemplateSetup.BureauArea)]
    public class BureauTemplateSetupController : Controller
    {
        public IActionResult Index()
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.TemplateSetup.BureauArea, SystemAreas.TemplateSetup.BureauArea);

            var model = new TemplateSetupViewModel
            {
                AllowEdit = allowEdit,
                ComponentVariableApiControllerName = "BureauComponentVariable",
                HideTemplateTab = true
            };

            return this.View("Templates/TemplateSetup", model);
        }
    }
}