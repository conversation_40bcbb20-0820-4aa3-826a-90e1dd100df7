namespace PaySpace.Venuta.Areas.Bureau.Controllers
{
    using System.ComponentModel;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Model;
    using PaySpace.Venuta.Services.Abstractions;

    [Area("Bureau")]
    [DisplayName(SystemAreas.DynamicFormBuilder.Area)]
    public class BureauDynamicFormBuilderController : Controller
    {
        private readonly ITenantProvider tenantProvider;
        private readonly ICompanyService companyService;

        public BureauDynamicFormBuilderController(
            ITenantProvider tenantProvider,
            ICompanyService companyService)
        {
            this.tenantProvider = tenantProvider;
            this.companyService = companyService;
        }

        public async Task<IActionResult> Index()
        {
            var companyId = this.tenantProvider.GetCompanyId()!.Value;
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var vm = new DynamicFormBuilderPageLoadViewModel
            {
                GenerateEmployeeNumber = await this.companyService.ShouldGenerateEmployeeNumber(companyId),
                TaxCountryId = taxCountryId
            };

            return this.View(vm);
        }
    }
}