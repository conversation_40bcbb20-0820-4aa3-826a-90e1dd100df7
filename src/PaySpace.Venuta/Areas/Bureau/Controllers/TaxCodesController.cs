namespace PaySpace.Venuta.Areas.Bureau.Controllers
{
    using System.ComponentModel;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Bureau.ViewModels;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;

    [Area("Bureau")]
    [DisplayName(SystemAreas.TaxCode.Area)]
    public class TaxCodesController : Controller
    {
        private readonly ICompanyService companyService;
        private readonly ITenantProvider tenantProvider;

        public TaxCodesController(
            ICompanyService companyService,
            ITenantProvider tenantProvider)
        {
            this.companyService = companyService;
            this.tenantProvider = tenantProvider;
        }

        public async Task<IActionResult> Index(int? countryId)
        {
            if (countryId is null)
            {
                var companyId = this.tenantProvider.GetCompanyId();
                countryId = await this.companyService.GetTaxCountryIdAsync(companyId.Value);
            }

            var profile = this.HttpContext.GetSecurityProfile();
            var isFullAccess = profile.IsFullAccess(SystemAreas.TaxCode.Area, SystemAreas.TaxCode.Area);

            return this.View(new TaxCodeConfigViewModel()
            {
                CountryId = countryId.Value,
                AllowInsert = isFullAccess,
                AllowEdit = isFullAccess,
                AllowDelete = isFullAccess
            });
        }
    }
}
