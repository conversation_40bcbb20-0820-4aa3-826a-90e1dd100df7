namespace PaySpace.Venuta.Areas.Company.ViewModelBuilder
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Web;

    using Microsoft.AspNetCore.Http;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Reports;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Security.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Country;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Extensions;
    using PaySpace.Venuta.Services.Reports.Models;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Reports.Services;

    public interface IReportViewModelBuilder
    {
        Task<ReportViewModel> BuildViewModelAsync(HostString host, ISecurityProfile profile, long companyId, long frequencyId, string classicRedirectUri, string accessToken);

        Task<IList<ReportListingViewModel>> GetReportsAsync(
            HostString tenant,
            ISecurityProfile profile,
            long companyId,
            long frequencyId,
            IList<int> categoryIds,
            string toggleFavouriteReportUrlBase,
            string contextMenuItemsUrlBase,
            string accessToken,
            CancellationToken cancellationToken);

        Task<IList<ReportListingViewModel>> GetFavouriteReportsAsync(
            ISecurityProfile profile,
            long companyId,
            IList<int> categoryIds,
            IList<ReportListingViewModel> notFavouritedReports,
            CancellationToken cancellationToken);
    }

    public class ReportViewModelBuilder : IReportViewModelBuilder
    {
        private const long DevExpressReportType = 2;

        private readonly IStringLocalizer<ReportHeader> localizer;
        private readonly IReportHeaderService reportHeaderService;
        private readonly IReportFavouriteService reportFavouriteService;
        private readonly IReportUrlResolver reportUrlResolver;
        private readonly IEnumService enumService;
        private readonly IUserSecurityService userSecurityService;
        private readonly ICompanyPaymentModuleService companyPaymentModuleService;
        private readonly ICompanyService companyService;
        private readonly ICachedNavigationLinksService navigationLinksService;
        private readonly ICustomReportListingService customReportListingService;
        private readonly ICountryMenuOverrideService countryMenuOverrideService;
        private readonly ICompanyMenuOverrideService companyMenuOverrideService;
        private readonly IReportRetrievalService reportRetrievalService;
        private readonly IReportStorageService reportStorageService;
        private readonly IReportUserSecurityService reportUserSecurityService;
        private readonly IUserReportsPermissionService userReportsPermissionService;
        private readonly ISystemReportListingService systemReportListingService;

        public ReportViewModelBuilder(
            IStringLocalizer<ReportHeader> localizer,
            IReportHeaderService reportHeaderService,
            IReportFavouriteService reportFavouriteService,
            IReportUrlResolver reportUrlResolver,
            IEnumService enumService,
            IUserSecurityService userSecurityService,
            ICompanyPaymentModuleService companyPaymentModuleService,
            ICompanyService companyService,
            ICachedNavigationLinksService navigationLinksService,
            ICustomReportListingService customReportListingService,
            ICountryMenuOverrideService countryMenuOverrideService,
            ICompanyMenuOverrideService companyMenuOverrideService,
            IReportRetrievalService reportRetrievalService,
            IReportStorageService reportStorageService,
            IReportUserSecurityService reportUserSecurityService,
            IUserReportsPermissionService userReportsPermissionService,
            ISystemReportListingService systemReportListingService)
        {
            this.reportHeaderService = reportHeaderService;
            this.reportFavouriteService = reportFavouriteService;
            this.reportUrlResolver = reportUrlResolver;
            this.localizer = localizer;
            this.enumService = enumService;
            this.userSecurityService = userSecurityService;
            this.companyPaymentModuleService = companyPaymentModuleService;
            this.companyService = companyService;
            this.navigationLinksService = navigationLinksService;
            this.customReportListingService = customReportListingService;
            this.countryMenuOverrideService = countryMenuOverrideService;
            this.companyMenuOverrideService = companyMenuOverrideService;
            this.reportRetrievalService = reportRetrievalService;
            this.reportStorageService = reportStorageService;
            this.reportUserSecurityService = reportUserSecurityService;
            this.userReportsPermissionService = userReportsPermissionService;
            this.systemReportListingService = systemReportListingService;
        }

        public virtual async Task<ReportViewModel> BuildViewModelAsync(HostString host, ISecurityProfile profile, long companyId, long frequencyId, string classicRedirectUri, string accessToken)
        {
            var reportWriterUri = $"{new Uri(this.reportUrlResolver.Resolve(host), $"/company/{companyId}/frequency/{frequencyId}/datasource/new")}?access_token={accessToken}";
            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            var queryParameters = new Dictionary<string, string>
            {
                { "companyId", companyId.ToString() },
                { "frequencyId", frequencyId.ToString() },
                { "access_token", accessToken }
            };
            var queryParametersWithCompanyGroup = new Dictionary<string, string>(queryParameters)
            {
                { "companyGroupId", companyGroupId.ToString() }
            };
            var hasReportCreationAccess = await this.HasReportCreationAccessAsync(profile, companyId);

            return new ReportViewModel
            {
                CompanyId = companyId,
                FrequencyId = frequencyId,
                Categories = await this.enumService.GetReportCategoriesAsync(),
                ClassicRedirectUri = new Uri(classicRedirectUri),
                ReportWriterUrl = new Uri(reportWriterUri),
                ReportWriterExtractReportUrl = new Uri($"{reportWriterUri}&reportType=Extract"),
                AllowReportDesignerAccess = hasReportCreationAccess,
                HasUploadAccess = hasReportCreationAccess && profile.UserType == UserType.Bureau,
                CanSetDesignableStatus = profile.UserType == UserType.Bureau,
                GetAvailableCompaniesUrl = this.GenerateUri(host, "/designer/GetCompanies", queryParametersWithCompanyGroup),
                GetCountriesUrl = this.GenerateUri(host, "/designer/GetCountries", queryParametersWithCompanyGroup),
                GetReportCategoriesUrl = this.GenerateUri(host, "/designer/GetReportCategories", queryParameters),
                GetReportLevelsUrl = this.GenerateUri(host, "/designer/GetReportLevels", queryParameters),
                GetReportSubCategoriesUrl = this.GenerateUri(host, "/designer/GetReportSubCategories", queryParameters),
                UploadReportUrl = this.GenerateUri(host, "/designer/UploadReport", queryParameters),
                ShowClassicReportTab = true
            };
        }

        public async Task<IList<ReportListingViewModel>> GetReportsAsync(
            HostString tenant,
            ISecurityProfile profile,
            long companyId,
            long frequencyId,
            IList<int> categoryIds,
            string toggleFavouriteReportUrlBase,
            string contextMenuItemsUrlBase,
            string accessToken,
            CancellationToken cancellationToken)
        {
            var taxCountryId = this.companyService.GetTaxCountryId(companyId);

            var standardReports = await this.BuildReportHeaderViewModelAsync(
                tenant,
                profile,
                await this.reportHeaderService.GetReportHeadersByCategoryIds(taxCountryId, categoryIds).ToArrayAsync(cancellationToken),
                companyId,
                frequencyId,
                taxCountryId,
                toggleFavouriteReportUrlBase,
                contextMenuItemsUrlBase,
                accessToken,
                cancellationToken);

            var currentStandardReports = standardReports.Select(_ => _.ReportPath).ToList();
            var customBureauExtracts = await this.HasExtractFileAccessAsync(profile, companyId)
                ? await this.GetCustomBureauExtractsAsync(
                    tenant,
                    profile,
                    companyId,
                    frequencyId,
                    ReportConstants.ExtractFilesCategoryId,
                    toggleFavouriteReportUrlBase,
                    contextMenuItemsUrlBase,
                    accessToken,
                    currentStandardReports,
                    cancellationToken)
                : new List<ReportListingViewModel>();

            // extract files needs to be excluded as above we fetch them seperately from other reports.
            categoryIds.Remove(ReportConstants.ExtractFilesCategoryId);
            var customReports = await this.GetCustomReportsAsync(
                tenant,
                profile,
                companyId,
                frequencyId,
                categoryIds,
                toggleFavouriteReportUrlBase,
                contextMenuItemsUrlBase,
                accessToken,
                currentStandardReports,
                cancellationToken);

            var systemReports = await this.GetSystemReportsAsync(
                tenant,
                profile,
                companyId,
                frequencyId,
                contextMenuItemsUrlBase,
                cancellationToken);

            var reports = standardReports.Union(customBureauExtracts).Union(customReports).Union(systemReports);
            return this.SetReportSuffix(reports, false, "-Standard-Report").ToList();
        }

        public async Task<IList<ReportListingViewModel>> GetFavouriteReportsAsync(
            ISecurityProfile profile,
            long companyId,
            IList<int> categoryIds,
            IList<ReportListingViewModel> notFavouritedReports,
            CancellationToken cancellationToken)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var favouritedReports = await this.reportFavouriteService.GetFavouriteReportHeadersByCategory(profile.UserId, taxCountryId, categoryIds).ToArrayAsync(cancellationToken);

            var favouriteCustomReportUrls = await this.reportFavouriteService.GetFavouriteReports(profile.UserId)
                .Where(_ => !string.IsNullOrWhiteSpace(_.ReportUrl))
                .Select(_ => _.ReportUrl)
                .ToListAsync(cancellationToken: cancellationToken);

            return notFavouritedReports
                .Where(_ => (_.ReportId != null && (favouriteCustomReportUrls.Any(f => f.Contains(_.ReportId))
                    || favouritedReports.Any(a => a.ReportHeaderId.ToString() == _.ReportId)))
                    || (_.Items != null && _.Items.Any(_ => favouriteCustomReportUrls.Any(f => f.Contains(_.ReportId)))))
                .Select(reportToFavourite => this.SetFavouriteReportSuffix(reportToFavourite, favouriteCustomReportUrls, favouritedReports))
                .ToList();
        }

        /// <summary>
        /// Filters by user security permissions on context and localize text.
        /// </summary>
        /// <param name="reports">reports you wish to examine.</param>
        /// <returns>returns reports that the user is allowed to access, i.e. if deny flag is false, and localizes report name and/or description's.</returns>
        private async Task<IList<ReportListingViewModel>> BuildReportHeaderViewModelAsync(
            HostString tenant,
            ISecurityProfile profile,
            IEnumerable<ReportHeader> reports,
            long companyId,
            long frequencyId,
            int taxCountryId,
            string toggleFavouriteReportUrlBase,
            string contextMenuItemsUrlBase,
            string accessToken,
            CancellationToken cancellationToken)
        {
            var reportPermission = await this.BuildReportPermission(profile.UserId, profile.CompanyId);
            var navigationLinks = this.navigationLinksService.GetAllLinks();
            var countryMenuOverrides = await this.countryMenuOverrideService.GetCountryMenuOverride(taxCountryId);
            var companyMenuOverrides = await this.companyMenuOverrideService.GetCompanyMenuOverride(companyId);

            var reportHeaders = reports
                .Where(_ => this.EvaluateReportPermission(_.ReportPath, _.ReportHeaderId, reportPermission))
                .Where(_ => this.IsAllowedToViewReport(profile, _.ReportsList.ReportUrl))
                .Where(_ => this.HasReportAccess(_.ReportHeaderId, _.ReportsList.ReportUrl, reportPermission.PaymentModule, navigationLinks, countryMenuOverrides, companyMenuOverrides))
                .ToArray();

            var reportHeaderViewModels = new List<ReportListingViewModel>();
            foreach (var reportHeader in reportHeaders)
            {
                var isReportDevexpressType = reportHeader.ReportTypeId == DevExpressReportType;
                // Custom payslips is a unique case where we cant use the SQL Id but need the repx file name.
                var isCustomPayslip = ReportUtils.IsCustomPayslip(reportHeader.ReportHeaderId.ToString());
                var reportId = isCustomPayslip
                    ? ReportConstants.CustomPayslips.PayslipHeaderReportId
                    : reportHeader.ReportHeaderId.ToString();

                // TODO: Remove this check once we migrate all old custom payslips to the new single file payslip.
                if (isCustomPayslip)
                {
                    try
                    {
                        var oldCustomPayslipReportSource = await this.reportRetrievalService
                            .GetReportSourceAsync(ReportConstants.CustomPayslips.PayslipHeaderReportId, companyId, profile.UserId, true, null, cancellationToken);
                        var newCustomPayslipReportSource = await this.reportRetrievalService
                            .GetReportSourceAsync(ReportConstants.CustomPayslips.NewPayslipReportId, companyId, profile.UserId, true, null, cancellationToken);
                        reportId = oldCustomPayslipReportSource.ToReportContextLevel() < newCustomPayslipReportSource.ToReportContextLevel()
                            ? oldCustomPayslipReportSource.ReportId
                            : newCustomPayslipReportSource.ReportId;
                    }
                    catch
                    {
                        reportId = ReportConstants.CustomPayslips.NewPayslipReportId;
                    }
                }

                ReportSource? reportSource = null;
                IDictionary<string, string>? reportMetadata = null;
                // Try catch is needed for instances where SQL and blob dont match because of bi weekly reset.
                try
                {
                    if (isReportDevexpressType)
                    {
                        reportSource = await this.reportRetrievalService.GetReportSourceAsync(reportId, companyId, profile.UserId, true, null, cancellationToken);
                        reportMetadata = await this.reportStorageService.GetMetadataValuesAsync(reportSource.ReportPath);
                    }
                }
                catch (InvalidOperationException)
                {
                    continue;
                }

                var isBureauLevelReport = reportSource == null || reportSource.IsBureauLevelReport();

                var commonQueryParams = $"?access_token={accessToken}&companyId={companyId}&frequencyId={frequencyId}&tenant={tenant}";
                var viewerUri = isCustomPayslip
                    ? new Uri($"{new Uri(this.reportUrlResolver.Resolve(tenant), "/viewer")}{commonQueryParams}&reportId={reportId}&includeDisabledReports=true")
                    : new Uri($"{new Uri(this.reportUrlResolver.Resolve(tenant), "/classic/detail")}{commonQueryParams}&reportHeaderId={reportHeader.ReportHeaderId}");

                reportHeaderViewModels.Add(
                    new ReportListingItemViewModel
                    {
                        ReportId = reportId,
                        ReportCategoryId = reportHeader.ReportCategoryId,
                        ViewerUri = viewerUri,
                        ReportName = this.localizer.GetString(reportHeader.ReportNameKey).Value,
                        ReportDescription = this.localizer.GetString(reportHeader.ReportDescriptionKey),
                        Enabled = reportMetadata == null || ReportUtils.IsEnabled(reportMetadata),
                        HasReportBeenEdited = !isBureauLevelReport,
                        ToggleFavouriteReportUrl = this.GenerateUri(tenant, toggleFavouriteReportUrlBase, new Dictionary<string, string> { { "reportId", reportHeader.ReportHeaderId.ToString() } }), // Favourites uses the ReportHeaderId within SQL.ContextMenuItemsUrl
                        ContextMenuItemsUrl = this.GenerateUri(tenant, contextMenuItemsUrlBase, new Dictionary<string, string> { { "reportId", isCustomPayslip ? reportId : reportHeader.ReportHeaderId.ToString() } }),
                        IsUserGeneratedReport = false,
                        ReportPath = reportHeader.ReportPath
                    });
            }

            return reportHeaderViewModels;
        }

        private bool IsAllowedToViewReport(ISecurityProfile profile, string url)
        {
            return this.userReportsPermissionService.HasAccessToReport(profile, url);
        }

        private bool HasReportAccess(
            long reportHeaderId,
            string url,
            PaymentModule paymentModule,
            IEnumerable<CachedNavigationLink> navigationLinks,
            IList<CountryMenuOverride> countryMenuOverrides,
            IList<CompanyMenuOverride> companyMenuOverrides)
        {
            // Permissions for custom payslips are handled by the report user security service
            if (ReportUtils.IsCustomPayslip(reportHeaderId.ToString()))
            {
                return true;
            }

            var navigationLink = navigationLinks
                .Where(_ => string.Equals(_.Url, url, StringComparison.OrdinalIgnoreCase))
                .Select(_ => new { _.NavigationLinkId, _.Lite, _.Premier, _.Master })
                .FirstOrDefault();

            if (navigationLink == null)
            {
                return true;
            }

            var countryMenuOverride = countryMenuOverrides.SingleOrDefault(_ => _.NavigationLinkId == navigationLink.NavigationLinkId);
            var companyMenuOverride = companyMenuOverrides.SingleOrDefault(_ => _.NavigationLinkId == navigationLink.NavigationLinkId);

            if (countryMenuOverride is { IsAllowed: false } || companyMenuOverride is { IsAllowed: false })
            {
                return false;
            }

            if (countryMenuOverride is { IsAllowed: true } || companyMenuOverride is { IsAllowed: true })
            {
                return true;
            }

            return paymentModule is not (PaymentModule.Lite or PaymentModule.NewLite) || navigationLink.Lite;
        }

        private bool EvaluateReportPermission(string reportPath, long reportHeaderId, ReportPermissions permission)
        {
            if (reportPath is "/PayrollReports/EMP201View" or "/PayrollReports/EMP201BreakDown")
            {
                if (permission.HasAccessToEmp201 == false)
                {
                    return false;
                }
            }

            if (reportPath == "Bureau/Payslips/PayslipHeader.repx" && reportHeaderId == 70)
            {
                return this.reportUserSecurityService.CanViewCustomPayslip(permission.PaymentModule);
            }

            return true;
        }

        private async Task<ReportPermissions> BuildReportPermission(long userId, long companyId)
        {
            var permissionGranted = true;

            var userPostions = await this.userSecurityService.GetUserHierarchyPositionsAsync(userId, companyId);
            var userRegions = await this.userSecurityService.GetUserHierarchyRegionsAsync(userId, companyId);
            var paymentModule = await this.companyPaymentModuleService.GetPaymentModuleAsync(companyId);

            if (userRegions.Count > 0 || userPostions.Count > 0)
            {
                permissionGranted = false;
            }

            return new ReportPermissions { HasAccessToEmp201 = permissionGranted, PaymentModule = paymentModule };
        }

        private async Task<IList<ReportListingViewModel>> GetCustomReportsAsync(
            HostString tenant,
            ISecurityProfile profile,
            long companyId,
            long frequencyId,
            IList<int> reportCategoryIds,
            string toggleFavouriteReportUrlBase,
            string contextMenuItemsUrlBase,
            string accessToken,
            IList<string> standardReportNameList,
            CancellationToken cancellationToken)
        {
            var customReports = await this.customReportListingService.GetCustomReportsAsync(
                tenant,
                profile,
                companyId,
                frequencyId,
                reportCategoryIds,
                false,
                accessToken,
                standardReportNameList,
                cancellationToken);
            var reportHeaderViewModels = await this.BuildReportListingItems(
                profile,
                customReports,
                tenant,
                companyId,
                toggleFavouriteReportUrlBase,
                contextMenuItemsUrlBase,
                true,
                cancellationToken);

            return reportHeaderViewModels;
        }

        private async Task<IList<ReportListingViewModel>> GetSystemReportsAsync(
           HostString tenant,
           ISecurityProfile profile,
           long companyId,
           long frequencyId,
           string contextMenuItemsUrlBase,
           CancellationToken cancellationToken)
        {
            var reportHeaderViewModels = new List<ReportListingViewModel>();
            if (profile.UserType == UserType.Bureau && profile.IsFullAccess(SystemAreas.Report.Area, SystemAreas.Report.Keys.BureauLevelReports))
            {
                var systemReports = await this.systemReportListingService.GetSystemReportsAsync(tenant, companyId, frequencyId);

                foreach (var report in systemReports)
                {
                    var parentReportInfo = await this.reportRetrievalService.GetEnabledParentReportInfoAsync(report.ReportSource, companyId, profile.UserId, cancellationToken);
                    var reportIdQueryParameter = new Dictionary<string, string> { { "reportId", report.ReportId.ToString() } };

                    reportHeaderViewModels.Add(
                        new ReportListingItemViewModel
                        {
                            ReportId = report.ReportId.ToString(),
                            ReportCategoryId = report.ReportCategoryId,
                            ReportName = report.ReportName,
                            ReportDescription = report.ReportDescription,
                            ViewerUri = new Uri(report.ViewerUri),
                            HasReportBeenEdited = parentReportInfo is not null,
                            ToggleFavouriteReportUrl = null,
                            ContextMenuItemsUrl = this.GenerateUri(tenant, contextMenuItemsUrlBase, reportIdQueryParameter),
                            IsUserGeneratedReport = false,
                            ReportPath = report.ReportSource.ReportPath,
                            ReportSubCategoryId = report.ReportSubCategoryId,
                            ReportContextLevel = report.ReportSource.ToReportContextLevel().GetEnumDescription(),
                            Enabled = report.Enabled,
                            IsSystemReport = true
                        });
                }
            }

            return reportHeaderViewModels;
        }

        private async Task<IList<ReportListingViewModel>> GetCustomBureauExtractsAsync(
            HostString tenant,
            ISecurityProfile profile,
            long companyId,
            long frequencyId,
            int reportCategoryId,
            string toggleFavouriteReportUrlBase,
            string contextMenuItemsUrlBase,
            string accessToken,
            IList<string> standardReportNameList,
            CancellationToken cancellationToken)
        {
            if (!profile.IsFullAccess(SystemAreas.Report.Area, SystemAreas.Report.Keys.ExtractFile))
            {
                return Array.Empty<ReportListingViewModel>();
            }

            var categories = new List<int> { reportCategoryId };
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var reportSubCategories = await this.enumService.GetReportSubCategoriesAsync(countryId);

            var customExtractsWithSubCategories = await this.GetCustomExtractsWithSubCategoriesAsync(
                tenant,
                profile,
                companyId,
                frequencyId,
                accessToken,
                standardReportNameList,
                categories,
                reportSubCategories,
                cancellationToken);
            var customExtractsWithoutSubCategories = await this.customReportListingService
                .GetCustomReportsAsync(tenant, profile, companyId, frequencyId, categories, false, accessToken, standardReportNameList, cancellationToken);

            this.RemoveReportDuplicationsByContextLevel(customExtractsWithSubCategories, customExtractsWithoutSubCategories);

            var extracts = await this.BuildReportListingItems(
                profile,
                customExtractsWithoutSubCategories,
                tenant,
                companyId,
                toggleFavouriteReportUrlBase,
                contextMenuItemsUrlBase,
                true,
                cancellationToken);
            var extractsWithSubCategory = await this.BuildReportListingItems(
                profile,
                customExtractsWithSubCategories,
                tenant,
                companyId,
                toggleFavouriteReportUrlBase,
                contextMenuItemsUrlBase,
                true,
                cancellationToken);

            // Group the reports by subcategory and show the subcategories to the user
            var availableSubCategories = customExtractsWithSubCategories.Select(_ => _.ReportSubCategoryId).Distinct().ToList();
            foreach (var subCategoryId in availableSubCategories)
            {
                var subCategory = reportSubCategories.First(_ => _.ReportSubCategoryId == subCategoryId);
                extracts.Add(
                    new ReportListingCategoryViewModel(extractsWithSubCategory.Where(_ => _.ReportSubCategoryId == subCategoryId))
                    {
                        Id = $"ReportSubCategory{subCategoryId}",
                        ReportName = subCategory.SubCategoryName,
                        ReportCategoryId = reportCategoryId
                    });
            }

            return extracts;
        }

        private IEnumerable<ReportListingViewModel> SetReportSuffix(IEnumerable<ReportListingViewModel> reportListingItem, bool favourite, string suffix)
        {
            foreach (var reportItem in reportListingItem)
            {
                if (reportItem.Items != null)
                {
                    this.SetReportSuffix(reportItem.Items, favourite, suffix);
                }

                reportItem.Id = reportItem.Id == null ? reportItem.ReportId + suffix : reportItem.Id + suffix;
                reportItem.IsFavourited = favourite;
            }

            return reportListingItem;
        }

        private ReportListingViewModel? SetFavouriteReportSuffix(ReportListingViewModel reportListingItem, IList<string> favouriteCustomReports, ReportHeader[] favouriteStandardReports)
        {
            // We need to loop through Categories to know if they have inner subcategories with items, we then repeat this for every inner category.
            if (reportListingItem.Items != null)
            {
                var favoritedSubCategoryReportItems = new List<ReportListingViewModel>();

                foreach (var reportItem in reportListingItem.Items)
                {
                    if (reportItem.Items != null)
                    {
                        this.SetFavouriteReportSuffix(reportItem, favouriteCustomReports, favouriteStandardReports);
                    }

                    if (!favouriteCustomReports.Any(_ => _.Contains(reportItem.ReportId))
                        && !favouriteStandardReports.Any(_ => _.ReportHeaderId.ToString() == reportItem.ReportId))
                    {
                        continue;
                    }

                    reportItem.IsFavourited = true;
                    favoritedSubCategoryReportItems.Add(this.SetFavouriteReportListingItemSuffix(reportItem));
                }

                var favouritedSubCategory = new ReportListingCategoryViewModel(favoritedSubCategoryReportItems)
                {
                    Id = reportListingItem.Id + "-Favourite-Report",
                    ReportName = reportListingItem.ReportName,
                    ReportCategoryId = reportListingItem.ReportCategoryId
                };

                return favouritedSubCategory;
            }

            reportListingItem.IsFavourited = true;
            return this.SetFavouriteReportListingItemSuffix(reportListingItem);
        }

        private async Task<bool> HasExtractFileAccessAsync(ISecurityProfile profile, long companyId)
        {
            var extractFilesFullAccess = profile.IsFullAccess(SystemAreas.Report.Area, SystemAreas.Report.Keys.ExtractFile);
            return await this.reportUserSecurityService.CanViewCustomReportsAsync(profile, companyId) && extractFilesFullAccess;
        }

        private async Task<bool> HasReportCreationAccessAsync(ISecurityProfile profile, long companyId)
        {
            var designerFullAccess = profile.IsFullAccess(SystemAreas.Report.Area, SystemAreas.Report.Keys.ReportDesigner);
            return await this.reportUserSecurityService.CanViewCustomReportsAsync(profile, companyId) && designerFullAccess;
        }

        private Uri GenerateUri(HostString host, string path, Dictionary<string, string> queryParameters)
        {
            var reportsUrl = this.reportUrlResolver.Resolve(host);

            var uriBuilder = new UriBuilder(new Uri(reportsUrl, path));
            var query = HttpUtility.ParseQueryString(string.Empty);
            foreach (var queryParameter in queryParameters)
            {
                query.Add(queryParameter.Key, queryParameter.Value);
            }

            uriBuilder.Query = query.ToString();
            return uriBuilder.Uri;
        }

        private async Task<List<ReportListingViewModel>> BuildReportListingItems(
            ISecurityProfile profile,
            IEnumerable<CustomReport> reports,
            HostString tenant,
            long companyId,
            string toggleFavouriteReportUrlBase,
            string contextMenuItemsUrlBase,
            bool isUserGeneratedReport,
            CancellationToken cancellationToken)
        {
            var reportList = new List<ReportListingViewModel>();
            foreach (var report in reports)
            {
                var parentReportInfo = await this.reportRetrievalService.GetEnabledParentReportInfoAsync(report.ReportSource, companyId, profile.UserId, cancellationToken);
                var reportIdQueryParameter = new Dictionary<string, string> { { "reportId", report.ReportId.ToString() } };

                reportList.Add(
                    new ReportListingItemViewModel
                    {
                        ReportId = report.ReportId.ToString(),
                        ReportCategoryId = report.ReportCategoryId,
                        ReportName = report.ReportName,
                        ReportDescription = report.ReportDescription,
                        ViewerUri = new Uri(report.ViewerUri),
                        HasReportBeenEdited = parentReportInfo is not null,
                        ToggleFavouriteReportUrl = this.GenerateUri(tenant, toggleFavouriteReportUrlBase, reportIdQueryParameter),
                        ContextMenuItemsUrl = this.GenerateUri(tenant, contextMenuItemsUrlBase, reportIdQueryParameter),
                        IsUserGeneratedReport = isUserGeneratedReport,
                        ReportPath = report.ReportSource.ReportPath,
                        ReportSubCategoryId = report.ReportSubCategoryId,
                        ReportContextLevel = profile.UserType == UserType.Bureau ? report.ReportSource.ToReportContextLevel().GetEnumDescription() : string.Empty,
                        Enabled = report.Enabled
                    });
            }

            return reportList;
        }

        private ReportListingItemViewModel SetFavouriteReportListingItemSuffix(ReportListingViewModel reportItem)
        {
            return new ReportListingItemViewModel
            {
                Id = reportItem.ReportId + "-Favourite-Report",
                ReportId = reportItem.ReportId,
                ViewerUri = reportItem.ViewerUri,
                ReportName = reportItem.ReportName,
                ReportDescription = reportItem.ReportDescription,
                ReportCategoryId = reportItem.ReportCategoryId,
                IsFavourited = true,
                HasReportBeenEdited = reportItem.HasReportBeenEdited,
                ToggleFavouriteReportUrl = reportItem.ToggleFavouriteReportUrl,
                ContextMenuItemsUrl = reportItem.ContextMenuItemsUrl,
                IsUserGeneratedReport = reportItem.IsUserGeneratedReport,
                ReportPath = reportItem.ReportPath,
                Enabled = reportItem.Enabled,
                ReportSubCategoryId = reportItem.ReportSubCategoryId
            };
        }

        private void RemoveReportDuplicationsByContextLevel(IList<CustomReport> customExtractsWithSubCategories, IList<CustomReport> customExtractsWithoutSubCategories)
        {
            var reportsWithSubCategories = customExtractsWithSubCategories.ToDictionary(_ => _.ReportId, _ => _.ReportSource.ToReportContextLevel());
            var reportsWithoutSubCategories = customExtractsWithoutSubCategories.ToDictionary(_ => _.ReportId, _ => _.ReportSource.ToReportContextLevel());

            foreach (var report in reportsWithoutSubCategories)
            {
                if (reportsWithSubCategories.TryGetValue(report.Key, out var reportWithSubcategoryContextLevel))
                {
                    var removeFromReportsWithoutSubCategories = (int)report.Value >= (int)reportWithSubcategoryContextLevel;

                    if (removeFromReportsWithoutSubCategories)
                    {
                        var extractToRemove = customExtractsWithoutSubCategories.First(_ => _.ReportId == report.Key);
                        customExtractsWithoutSubCategories.Remove(extractToRemove);
                    }
                    else
                    {
                        var extractToRemove = customExtractsWithSubCategories.First(_ => _.ReportId == report.Key);
                        customExtractsWithSubCategories.Remove(extractToRemove);
                    }
                }
            }
        }

        private async Task<IList<CustomReport>> GetCustomExtractsWithSubCategoriesAsync(
            HostString tenant,
            ISecurityProfile profile,
            long companyId,
            long frequencyId,
            string accessToken,
            IList<string> standardReportNameList,
            IList<int> categories,
            IList<EnumReportSubCategory> reportSubCategories,
            CancellationToken cancellationToken)
        {
            var customExtractsWithSubCategories = await this.customReportListingService
                .GetCustomReportsAsync(tenant, profile, companyId, frequencyId, categories, true, accessToken, standardReportNameList, cancellationToken);
            var validReportSubcategories = reportSubCategories.Select(_ => _.ReportSubCategoryId);
            return customExtractsWithSubCategories.Where(_ => _.ReportSubCategoryId.HasValue && validReportSubcategories.Contains(_.ReportSubCategoryId.Value)).ToList();
        }
    }
}