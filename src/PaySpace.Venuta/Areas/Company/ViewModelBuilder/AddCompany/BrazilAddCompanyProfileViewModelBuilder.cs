namespace PaySpace.Venuta.Areas.Company.ViewModelBuilder.AddCompany
{
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Maddalena;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Bureau;

    [CountryService(CountryCode.BR)]
    public class BrazilAddCompanyProfileViewModelBuilder(
        ITenantProvider tenantProvider,
        IAgencyService agencyService,
        IUserService userService,
        ITaxCountryService taxCountryService,
        ICompanyService companyService,
        ICountryCustomFieldService customFieldService)
        : AddCompanyProfileViewModelBuilder(
            tenantProvider,
            agencyService,
            userService,
            taxCountryService,
            companyService,
            customFieldService)
    {
        public override async Task<AddCompanyViewModel> BuildViewModelAsync(ClaimsPrincipal user, int? countryId, GroupAction? groupAction, bool hasSession)
        {
            var vm = await base.BuildViewModelAsync(user, countryId, groupAction, hasSession);

            vm.AdvancedPositionManagement = true;

            return vm;
        }
    }
}