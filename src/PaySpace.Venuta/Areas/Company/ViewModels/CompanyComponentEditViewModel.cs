namespace PaySpace.Venuta.Areas.Company.ViewModels
{
    using PaySpace.Venuta.Data.Models.Enums;

    public class CompanyComponentEditViewModel
    {
        public long? CompanyComponentId { get; set; }

        public bool IsCopy { get; set; }

        public long RunId { get; set; }

        public bool ShowCurrencies { get; set; }

        public PayslipFrequency PayslipFrequency { get; set; }
    }
}