namespace PaySpace.Venuta.Areas.Company.ViewModels
{
    using System;
    using System.ComponentModel;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;

    [DisplayName(SystemAreas.OrganizationPosition.Area)]
    public class OrganizationPositionViewModel
    {
        public long? OrganizationPositionId { get; set; }

        public long? PositionDetailId { get; set; }

        public bool AllowEdit { get; set; }

        public string EditUrl { get; set; }

        public string ClassicUrl { get; set; }

        public bool IsMasterOrPremier { get; set; }

        public int CountryId { get; set; }

        public long CompanyId { get; set; }
    }
}