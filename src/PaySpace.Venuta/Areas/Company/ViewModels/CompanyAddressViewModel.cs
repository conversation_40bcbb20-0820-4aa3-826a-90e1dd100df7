namespace PaySpace.Venuta.Areas.Company.ViewModels
{
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.Company.Area)]
    public class CompanyAddressViewModel : AddressViewModel
    {
        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblBlock")]
        public string Block { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblEntrance")]
        public string Entrance { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblStaircase")]
        public string Staircase { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblFloor")]
        public string Floor { get; set; }

        [CountryVisible(CountryCode.ES)]
        [Display(Name = "lblDoor")]
        public string Door { get; set; }
    }
}