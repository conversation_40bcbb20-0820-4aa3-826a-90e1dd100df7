namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Abstractions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Model;
    using PaySpace.Venuta.Services.Abstractions;

    [Area("Company")]
    [DisplayName(SystemAreas.Profile.Basic)]
    public abstract class EmployeeProfileBaseController : Controller
    {
        private readonly ICompanyService companyService;
        private readonly IDynamicFormBuilderService dynamicFormBuilderService;
        private readonly ICompanySettingService companySettingService;

        public EmployeeProfileBaseController(
            ICompanyService companyService,
            IDynamicFormBuilderService dynamicFormBuilderService,
            ICompanySettingService companySettingService)
        {
            this.companyService = companyService;
            this.dynamicFormBuilderService = dynamicFormBuilderService;
            this.companySettingService = companySettingService;
        }

        protected async Task<IActionResult> CreateView(
            ProfileViewModel profileViewModel,
            long companyId,
            long frequencyId,
            OnboardingTemplateType? templateType,
            string? dynamicFormId,
            string? dynamicFormName)
        {
            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
            return await this.PaymentModuleResult(profileViewModel, companyId, frequencyId, templateType, dynamicFormId, dynamicFormName, accessToken);
        }

        private async Task<IActionResult> PaymentModuleResult(
            ProfileViewModel profileViewModel,
            long companyId,
            long frequencyId,
            OnboardingTemplateType? templateType,
            string? dynamicFormId,
            string? dynamicFormName,
            string accessToken)
        {
            var companyPaymentModule = await this.companyService.GetCompanyPaymentModule(companyId);
            switch (companyPaymentModule)
            {
                case PaymentModule.Premier:
                case PaymentModule.Master:
                case PaymentModule.NewPremier:
                case PaymentModule.NewMaster:
                    return await this.PremierMasterResult(profileViewModel, companyId, frequencyId, accessToken,
                        templateType, dynamicFormId, dynamicFormName);
                case PaymentModule.Lite:
                case PaymentModule.NewLite:
                    return await this.LiteResult(profileViewModel, companyId, frequencyId, accessToken);
                default:
                    return this.AdvancedResult(profileViewModel);
            }
        }

        private async Task<IActionResult> LiteResult(ProfileViewModel profileViewModel, long companyId, long frequencyId, string accessToken)
        {
            var useAdvanceWorkflow = await this.companySettingService.IsActiveAsync(companyId, CompanySettingCode.DynamicFormBuilder.UseAdvanceWorkflow);
            if (!useAdvanceWorkflow)
            {
                var bureauTemplate = await this.dynamicFormBuilderService.FindBureauAddNewEmployeeDynamicFormAsync(companyId, frequencyId, accessToken);

                if (bureauTemplate is not null)
                {
                    return this.CreateBasic(bureauTemplate.DynamicFormId, bureauTemplate.FormName, profileViewModel.CompanyGenerateEmployeeNumber);
                }
            }

            return this.AdvancedResult(profileViewModel);
        }

        private async Task<IActionResult> PremierMasterResult(
            ProfileViewModel profileViewModel,
            long companyId,
            long frequencyId,
            string accessToken,
            OnboardingTemplateType? templateType,
            string? dynamicFormId,
            string? dynamicFormName)
        {
            if (templateType == OnboardingTemplateType.Advanced)
            {
                return this.AdvancedResult(profileViewModel);
            }
            else if (templateType == OnboardingTemplateType.Basic && !string.IsNullOrEmpty(dynamicFormId))
            {
                return this.CreateBasic(dynamicFormId, dynamicFormName, profileViewModel.CompanyGenerateEmployeeNumber);
            }

            var availableDynamicFormsContainer = await this.dynamicFormBuilderService.GetAvailableDynamicFormForModuleAsync(
                companyId, frequencyId, accessToken, DynamicFormBuilderConstants.ModuleType.AddNewEmployee);
            var hasDynamicFormTemplate = availableDynamicFormsContainer.DynamicFormBuilderModels is not null &&
                availableDynamicFormsContainer.DynamicFormBuilderModels.Count > 0;

            // If the user has access to all required API endpoints, and there are companyTemplates, show those options.
            if (availableDynamicFormsContainer.UserHasRequiredApiAccessForQuickAdd &&
                hasDynamicFormTemplate &&
                availableDynamicFormsContainer.ContextLevel == DynamicFormBuilderContextLevel.Company)
            {
                // No point in showing just 1 option on the screen, so we auto navigate to the option available.
                if (!availableDynamicFormsContainer.CanShowAdvancedWorkflow &&
                    availableDynamicFormsContainer.DynamicFormBuilderModels.Count == 1)
                {
                    var dynamicFormBuilderModel = availableDynamicFormsContainer.DynamicFormBuilderModels[0];
                    return this.CreateBasic(dynamicFormBuilderModel.DynamicFormId, dynamicFormBuilderModel.FormName, profileViewModel.CompanyGenerateEmployeeNumber);
                }

                return this.CreateOptions(companyId, !availableDynamicFormsContainer.CanShowAdvancedWorkflow, availableDynamicFormsContainer.DynamicFormBuilderModels);
            }
            else if (hasDynamicFormTemplate && availableDynamicFormsContainer.ContextLevel == DynamicFormBuilderContextLevel.Bureau)
            {
                if (!availableDynamicFormsContainer.UserHasRequiredApiAccessForQuickAdd)
                {
                    // User does not have access API access
                    throw new UnauthorizedAccessException();
                }

                var dynamicFormBuilderModel = availableDynamicFormsContainer.DynamicFormBuilderModels[0];
                return this.CreateBasic(dynamicFormBuilderModel.DynamicFormId, dynamicFormBuilderModel.FormName,
                    profileViewModel.CompanyGenerateEmployeeNumber);
            }

            return this.AdvancedResult(profileViewModel);
        }

        private IActionResult AdvancedResult(ProfileViewModel profileViewModel)
        {
            return this.View("Create", profileViewModel);
        }

        private IActionResult CreateBasic(string dynamicFormId, string dynamicFormName, bool companyGenerateEmployeeNumber)
        {
            return this.View("CreateBasic", new ProfileViewModel
            {
                DynamicFormId = dynamicFormId,
                DynamicFormName = dynamicFormName,
                CompanyGenerateEmployeeNumber = companyGenerateEmployeeNumber
            });
        }

        private IActionResult CreateOptions(
            long companyId,
            bool excludesAdvancedWorkflowFromQuickOnboarding,
            IList<DynamicFormBuilderModel> dynamicForms)
        {
            return this.View("CreateOptions", new ProfileViewModel
            {
                CompanyId = companyId,
                ExcludesAdvancedWorkflowFromQuickOnboarding = excludesAdvancedWorkflowFromQuickOnboarding,
                DynamicForms = dynamicForms.Select(_ => new DynamicFormBuilderModel
                {
                    FormName = _.FormName,
                    DynamicFormId = _.DynamicFormId
                }).OrderBy(_ => _.FormName).ToList()
            });
        }
    }
}