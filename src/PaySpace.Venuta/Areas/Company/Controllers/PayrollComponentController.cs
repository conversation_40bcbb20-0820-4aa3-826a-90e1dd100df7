namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;

    [Area("Company")]
    [DisplayName(SystemAreas.CompanyComponents.Area)]
    public class PayrollComponentController : Controller
    {
        private readonly ICompanyRunService companyRunService;
        private readonly IEnumService enumService;
        private readonly ICompanyService companyService;

        public PayrollComponentController(
            ICompanyRunService companyRunService,
            IEnumService enumService,
            ICompanyService companyService)
        {
            this.companyRunService = companyRunService;
            this.enumService = enumService;
            this.companyService = companyService;
        }

        public async Task<IActionResult> Index(long companyId, long frequencyId, long? runId)
        {
            runId ??= await this.companyRunService.GetCompanyRuns(frequencyId, true, false)
                .OrderBy(r => r.PeriodStartDate)
                    .ThenBy(r => r.OrderNumber)
                .Select(r => r.RunId)
                .FirstOrDefaultAsync();

            var profile = this.HttpContext.GetSecurityProfile();
            var isFullAccess = profile.IsFullAccess(SystemAreas.CompanyComponents.Area, SystemAreas.CompanyComponentSubCodes.Area);
            var showCalcExceptions = !profile.IsDenied(SystemAreas.CompanyComponents.Area, SystemAreas.CompanyComponentSubCodes.Area);
            var payslipActions = await this.enumService.GetPayslipActionsAsync(false);
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);

            return this.View(new CompanyComponentViewModel
            {
                CompanyId = companyId,
                FrequencyId = frequencyId,
                RunId = runId!.Value,
                CountryId = countryId,
                AllowEdit = isFullAccess,
                ShowCalcExceptions = showCalcExceptions,
                ShowTaxabilityOption = (await this.enumService.GetEnumBureauTaxabilityOptionsAsync()).Any(_ => _.CountryId == countryId),
                ShowReportButton = countryId == (long)TaxCountry.UnitedKingdom,
                PayslipActions = JsonConvert.SerializeObject(payslipActions.Where(_ => (PayslipAction)_.PayslipAction != PayslipAction.Totals)
                    .Select(_ => new
                    {
                        _.PayslipAction,
                        _.ActionDescription,
                        _.Icon,
                        _.OrderNumber
                    }))
            });
        }
    }
}