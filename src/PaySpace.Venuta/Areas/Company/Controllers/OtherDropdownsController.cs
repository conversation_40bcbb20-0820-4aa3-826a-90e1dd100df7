namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;

    [Area("Company")]
    [DisplayName(SystemAreas.OtherDropdowns.Area)]
    public class OtherDropdownsController : Controller
    {
        private readonly ITenantProvider tenantProvider;

        public OtherDropdownsController(ITenantProvider tenantProvider)
        {
            this.tenantProvider = tenantProvider;
        }

        public IActionResult Index()
        {
            var companyId = this.tenantProvider.GetCompanyId();
            var profile = this.HttpContext.GetSecurityProfile();

            return this.View(new OtherDropdownsViewModel
            {
                CompanyId = companyId.Value,
                AllowEdit = profile.IsFullAccess(SystemAreas.OtherDropdowns.Area, SystemAreas.OtherDropdowns.Area)
            });
        }
    }
}