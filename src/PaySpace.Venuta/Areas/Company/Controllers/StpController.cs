namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.ComponentModel;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Configuration;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Infrastructure;

    [Area("Company")]
    [DisplayName(SystemAreas.Stp.Area)]
    public class StpController : Controller
    {
        private readonly Uri stpUrl;

        public StpController(IConfiguration configuration)
        {
            this.stpUrl = configuration.GetValue<Uri>("ClientSettings:StpUrl");
        }

        public async Task<IActionResult> Index(long companyId)
        {
            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
            var redirectUrl = $"{this.stpUrl}?access_token={accessToken}&companyid={companyId}";

            return this.Redirect(redirectUrl);
        }
    }
}