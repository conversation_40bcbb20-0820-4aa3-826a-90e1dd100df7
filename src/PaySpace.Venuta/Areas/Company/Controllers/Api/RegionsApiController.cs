namespace PaySpace.Venuta.Areas.Company.Controllers.Api
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Areas.Company.ViewModels.CustomFields;
    using PaySpace.Venuta.Controllers.Api;
    using PaySpace.Venuta.Conventions;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Infrastructure.Extensions;
    using PaySpace.Venuta.ModelBinders;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Models;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName("Regions")]
    [Area("Company")]
    public class RegionsApiController : ApiController
    {
        private readonly ICustomFieldService customFieldService;
        private readonly IOrganizationRegionService organizationRegionService;
        private readonly IValidator<OrganizationRegion> validator;
        private readonly IValidator<OrganizationRegionHistory> historyValidator;
        private readonly IMapper mapper;

        public RegionsApiController(
            ICustomFieldService customFieldService,
            IOrganizationRegionService organizationRegionService,
            IValidator<OrganizationRegion> organizationRegionValidator,
            IValidator<OrganizationRegionHistory> historyValidator,
            IMapper mapper)
        {
            this.customFieldService = customFieldService;
            this.organizationRegionService = organizationRegionService;
            this.validator = organizationRegionValidator;
            this.historyValidator = historyValidator;
            this.mapper = mapper;
        }

        [HttpGet("company/{companyId}")]
        public async Task<object> Get(DataSourceLoadOptions loadOptions, long companyId)
        {
            var rosters = await this.organizationRegionService.GetWithCustomFields(companyId);
            return DataSourceLoader.Load(rosters, loadOptions);
        }

        [CreateMessage]
        [HttpPost("company/{companyId}")]
        [Permission(SystemAreas.OrganizationRegions.Area, SystemAreas.OrganizationRegions.Area)]
        public async Task<IActionResult> Post(long companyId, string values)
        {
            var model = new RegionResult();
            JsonConvert.PopulateObject(values, model);

            if (model.CustomFields.Count == 0)
            {
                await this.AddDefaultCustomFieldsAsync(companyId, model);
            }

            if (model.Id > 0)
            {
                // The records was cloned, RegionId will still be set, we need to add a "History" record with a new effective date
                // Devextreme grid sees that as a completely new record and does a post
                return await this.Put(companyId, model.Id, values);
            }

            var region = new OrganizationRegion
            {
                CompanyId = companyId,
                HistoryEntityDetails = new List<OrganizationRegionHistory>()
            };

            this.MapAndSetEffectiveDate(model, region);

            await this.ValidateAsync(model, region, RuleSetNames.CreateAndUpdate);

            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.organizationRegionService.AddAsync(region);

            return this.Ok(model);
        }

        [UpdateMessage]
        [HttpPut("company/{companyId}")]
        [Permission(SystemAreas.OrganizationRegions.Area, SystemAreas.OrganizationRegions.Area)]
        public async Task<IActionResult> Put(long companyId, long key, string values)
        {
            var model = new RegionResult() { Id = key };

            JsonConvert.PopulateObject(values, model);

            if (model.Id != key)
            {
                return this.Forbid();
            }

            var region = await this.organizationRegionService.GetRegionAsync(companyId, key, model.HistoryEntityId);
            if (region == null)
            {
                return this.NotFound();
            }

            this.MapAndSetEffectiveDate(model, region);

            await this.ValidateAsync(model, region, RuleSetNames.CreateAndUpdate);
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.organizationRegionService.UpdateAsync(region);

            return this.Ok(model);
        }

        [DeleteMessage]
        [HttpDelete("company/{companyId}")]
        [Permission(SystemAreas.OrganizationRegions.Area, SystemAreas.OrganizationRegions.Area)]
        public async Task<IActionResult> Delete(long companyId, long key)
        {
            var roster = await this.organizationRegionService.GetRegionAsync(companyId, key);
            if (roster == null)
            {
                return this.NotFound();
            }

            await this.ValidateAsync(null, roster, RuleSetNames.Delete);
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.organizationRegionService.DeleteAsync(roster);
            return this.Ok();
        }

        [DeleteMessage]
        [HttpDelete("company/{companyId}/history")]
        [Permission(SystemAreas.OrganizationRegions.Area, SystemAreas.OrganizationRegions.Area)]
        public async Task<IActionResult> DeleteHistory(long companyId, [JsonModelBinder] DeleteViewModel key)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            var roster = await this.organizationRegionService.GetRegionAsync(companyId, key.Id, key.HistoryEntityId);
            if (roster == null)
            {
                return this.NotFound();
            }

            await this.organizationRegionService.DeleteAsync(roster, key.HistoryEntityId);
            return this.Ok();
        }

        [HttpGet("company/{companyId}/history")]
        public async Task<object> GetHistory(DataSourceLoadOptions loadOptions, long companyId, long id)
        {
            var rosters = await this.organizationRegionService.GetWithCustomFields(companyId, id);
            return DataSourceLoader.Load(rosters, loadOptions);
        }

        private async Task ValidateAsync(RegionResult? model, OrganizationRegion? region, string ruleset)
        {
            var validationData = new Dictionary<string, object>();

            if (model?.EffectiveDate != null)
            {
                validationData.Add(PaySpaceConstants.CustomFieldEffectiveDate, model.EffectiveDate);
            }

            validationData.Add(nameof(region.InactiveDate), region.InactiveDate);

            var result = await this.validator.ValidateAsync(region, validationData, ruleset);
            result.AddToModelState(this.ModelState, null);

            foreach (var history in region.HistoryEntityDetails)
            {
                var historyValidationResult = await this.historyValidator.ValidateAsync(history, validationData, ruleset);
                historyValidationResult.AddToModelState(this.ModelState, null);
            }
        }

        private void MapAndSetEffectiveDate(RegionResult? model, OrganizationRegion? region)
        {
            // if there's no custom fields, then we dont need a history record
            if (model.EffectiveDate != null && (model.CustomFields?.Count > 0 || model.HistoryEntityId > 0))
            {
                this.LinkRegionEntityHistory(model, region);
            }

            this.mapper.Map(model, region);
        }

        private void LinkRegionEntityHistory(RegionResult? model, OrganizationRegion? region)
        {
            var existingEntityHistory = region.HistoryEntityDetails == null || model.HistoryEntityId == 0
                ? null
                : region.HistoryEntityDetails.SingleOrDefault(_ => _.HistoryEntityDetailId == model.HistoryEntityId);

            // if no existing history, then create new history
            if (existingEntityHistory == null)
            {
                var newHistoryEntry = new OrganizationRegionHistory()
                {
                    RegionId = model.Id,
                    EffectiveDate = model.EffectiveDate.Value
                };

                if (model.CustomFields?.Count > 0)
                {
                    this.LinkCustomFieldRegion(model, region);
                    newHistoryEntry.CustomFields.AddRange(model.CustomFields);
                }

                region.HistoryEntityDetails.Add(newHistoryEntry);
            }
            else
            {
                this.LinkCustomFieldRegion(model, region, existingEntityHistory);
                this.mapper.Map(model, existingEntityHistory);

                foreach (var customField in existingEntityHistory.CustomFields)
                {
                    // TODO get rid of RegionId once Calc changes their logic
                    customField.RegionId = existingEntityHistory.RegionId;
                }
            }
        }

        private void LinkCustomFieldRegion(RegionResult? model, OrganizationRegion region)
        {
            foreach (var fieldValue in model.CustomFields)
            {
                fieldValue.EffectiveDate = model.EffectiveDate;
                fieldValue.Region = region;
            }
        }

        private void LinkCustomFieldRegion(RegionResult? model, OrganizationRegion region, OrganizationRegionHistory regionHistory)
        {
            foreach (var fieldValue in model.CustomFields)
            {
                fieldValue.EffectiveDate = model.EffectiveDate;
                fieldValue.RegionId = region.RegionId;
                fieldValue.HistoryEntityId = regionHistory.HistoryEntityDetailId;
            }
        }

        private async Task AddDefaultCustomFieldsAsync(long companyId, RegionResult model)
        {
            /*
             If no custom fields were posted from the front-end, we need to add them manually so that we can save the effective date against the empty custom field.
             The effective date will not be saved if there is no custom fields on the page.
             */
            var customFields = await this.customFieldService.GetCustomFieldFormFieldsAsync(companyId, nameof(OrganizationRegion));
            foreach (var customField in customFields)
            {
                model.CustomFields.Add(new OrganizationRegionCustomFieldValue
                {
                    CustomFieldId = customField.CustomFieldId,
                    CustomFieldType = customField.CustomFieldType
                });
            }
        }
    }
}