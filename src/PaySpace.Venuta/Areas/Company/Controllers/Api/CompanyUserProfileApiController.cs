namespace PaySpace.Venuta.Areas.Company.Controllers.Api
{
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;

    using Microsoft.AspNetCore.Mvc;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Controllers.Api;
    using PaySpace.Venuta.Conventions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Security;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.Users.Abstractions;
    using PaySpace.Venuta.Modules.Users.Abstractions.Models;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Validation.Annotations;

    [ApiController]
    [Area("Company")]
    [Route("company/{companyId}/{frequencyId}/user-profile/api")]
    [NavigationControllerName("CompanyUserProfile")]
    public class CompanyUserProfileApiController : BaseUserProfileApiController
    {
        private readonly IAgencySubInvoiceDetailService agencySubInvoiceDetailService;
        private readonly IUserProfileService userProfileService;
        private readonly ICompanyService companyService;
        private readonly IMapper mapper;
        private readonly ITenantProvider tenantProvider;
        private readonly IEnumService enumService;

        public CompanyUserProfileApiController(
            IAgencySubInvoiceDetailService agencySubInvoiceDetailService,
            IUserProfileService userProfileService,
            IEnumService enumService,
            ICompanyService companyService,
            IMapper mapper,
            IMessageBus messageBus,
            IValidator<User> validator,
            ITenantProvider tenantProvider,
            IEmailAddressService emailAddressService)
            : base(userProfileService, enumService, messageBus, validator, tenantProvider, emailAddressService)
        {
            this.agencySubInvoiceDetailService = agencySubInvoiceDetailService;
            this.userProfileService = userProfileService;
            this.companyService = companyService;
            this.mapper = mapper;
            this.tenantProvider = tenantProvider;
            this.enumService = enumService;
        }

        [HttpGet]
        public async Task<object> Get(DataSourceLoadOptions loadOptions, long companyId, long? securityGroupUsers = null, long? userCompanyLinks = null, long? userAgencyLinks = null, int? userCompanyContactTypes = null)
        {
            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);

            var agencyId = default(long?);

            if (this.User.IsInRole(Roles.TopLevel) || this.User.IsInRole(UserTypeCodes.Bureau))
            {
                agencyId = await this.companyService.GetAgencyIdAsync(companyId);

                if (agencyId == PaySpaceConstants.PaySpaceAgencyId)
                {
                    agencyId = default;
                }
            }

            var userProfiles = this.userProfileService.GetUserProfiles(companyGroupId, companyId, agencyId, securityGroupUsers, userCompanyLinks, userAgencyLinks, userCompanyContactTypes);

            return await DataSourceLoader.LoadAsync(userProfiles, loadOptions);
        }

        [HttpGet("security-role")]
        public async Task<object> GetSecurityRoles(DataSourceLoadOptions loadOptions, long companyId, UserType? userType)
        {
            if (userType is not null and not (UserType.Company or UserType.Agency))
            {
                return this.BadRequest();
            }

            userType ??= this.User.IsInRole(Roles.TopLevel) || this.User.IsInRole(UserTypeCodes.Bureau) ? UserType.Agency : UserType.Company;

            var securityGroupQuery = await this.userProfileService.GetSecurityGroupsAsync(userType.Value, companyId);
            var data = securityGroupQuery
                .Where(_ => _.MssProfile != true && _.SelfServiceUser != true)
                .Select(_ => new
                {
                    _.CompanyId,
                    _.GroupName,
                    _.Company.CompanyName,
                    Value = _.SecurityGroupId,
                    Text = _.Company.CompanyName + " : " + _.GroupName
                });

            return await DataSourceLoader.LoadAsync(data, loadOptions);
        }

        [HttpGet("contact-type")]
        public async Task<object> GetContactType(DataSourceLoadOptions loadOptions)
        {
            var contactTypes = await this.enumService.GetContactTypeAsync();
            var result = contactTypes.OrderBy(_ => _.ContactTypeDescription).Select(_ => new { value = _.ContactTypeId, text = _.ContactTypeDescription });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("company-frequencies")]
        public async Task<object> GetCompanyGroupFrequencies(DataSourceLoadOptions loadOptions, long companyId, UserType? userType)
        {
            if (userType is not null and not (UserType.Company or UserType.Agency))
            {
                return this.BadRequest();
            }

            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            var data = this.companyService.GetByGroup(companyGroupId).SelectMany(_ => _.CompanyRunFrequencies).Select(_ => new
            {
                _.CompanyId,
                _.Company.CompanyName,
                _.FrequencyName,
                Value = _.CompanyFrequencyId,
                Text = _.Company.CompanyName + " : " + _.FrequencyName
            });

            return await DataSourceLoader.LoadAsync(data, loadOptions);
        }

        [HttpGet("agency-companies")]
        public async Task<object> GetAgencyCompanies(DataSourceLoadOptions loadOptions, long companyId, UserType? userType)
        {
            if (userType is not null and not UserType.Agency)
            {
                return this.BadRequest();
            }

            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var data = this.companyService.GetByAgency(agencyId).Select(_ => new
            {
                Value = _.CompanyId,
                Text = _.CompanyName
            });

            return await DataSourceLoader.LoadAsync(data, loadOptions);
        }

        [HttpGet("completed-contact-types")]
        public async Task<object> GetSelectedContactTypes(long companyId, UserType userType)
        {
            if (userType == UserType.Agency)
            {
                var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
                return await this.userProfileService.GetUserContactTypesForAgencyAsync(agencyId);
            }

            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            return await this.userProfileService.GetUserContactTypesForCompanyGroupAsync(companyGroupId);
        }

        [HttpGet("sub-agency-detail")]
        public async Task<IActionResult> GetSubAgencyDetails(long companyId, long userId)
        {
            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var result = await this.agencySubInvoiceDetailService.FindByUserIdAsync(agencyId, userId) ??
                new AgencySubInvoiceDetails
                {
                    UserId = userId
                };

            return this.Ok(result);
        }

        [UpdateMessage]
        [HttpPost("sub-agency-detail/{userId}")]
        [Permission(SystemAreas.User.Area, SystemAreas.User.Area)]
        public async Task<IActionResult> PostSubAgencyDetails(long companyId, long userId, [FromBody] string values)
        {
            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var model = await this.agencySubInvoiceDetailService.FindByUserIdAsync(agencyId, userId) ??
                new AgencySubInvoiceDetails
                {
                    UserId = userId,
                    IsNew = true
                };

            // Storing the isNew from the model as json populate always sets it to false
            var isNew = model.IsNew;
            JsonConvert.PopulateObject(values, model);

            if (isNew)
            {
                await this.agencySubInvoiceDetailService.AddAsync(model);
            }
            else
            {
                await this.agencySubInvoiceDetailService.UpdateAsync(model);
            }

            return this.Ok();
        }

        [HttpPost("user-search")]
        public async Task<object> GetUsers(GlobalUserSearchModel model)
        {
            if (!this.User.IsInRole(UserTypeCodes.Bureau))
            {
                return this.NotFound();
            }

            return await this.userProfileService.GetGlobalUsersAsync(model);
        }

        [AllowAll]
        [HttpGet("user-type")]
        public async Task<object> GetUserTypes(DataSourceLoadOptions loadOptions, long companyId, UserType? userType, bool isNew = false)
        {
            var userTypeList = new Collection<int>
            {
                (int)UserType.Company,
                (int)UserType.Employee
            };

            if ((this.User.IsInRole(UserTypeCodes.Bureau)
                 || this.User.IsInRole(Roles.TopLevel))
                && await this.companyService.GetAgencyIdAsync(companyId) != PaySpaceConstants.PaySpaceAgencyId
                && userType is null or UserType.Agency)
            {
                userTypeList.Add((int)UserType.Agency);
            }

            if (isNew)
            {
                userTypeList.Remove((int)UserType.Employee);
            }

            var userTypes = await this.enumService.GetUserTypesAsync();
            var result = userTypes.Where(_ => userTypeList.Contains(_.UserTypeId))
                .OrderBy(_ => _.UserTypeDescription)
                .Select(_ => new { value = _.UserTypeId, text = _.UserTypeDescription });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [Permission(SystemAreas.UserProfiles.Area, SystemAreas.UserProfiles.Area)]
        public override Task<IActionResult> Post(UserPostResult model) => base.Post(model);

        [Permission(SystemAreas.UserProfiles.Area, SystemAreas.UserProfiles.Area)]
        public override Task<IActionResult> Put(long userId, UserResult model) => base.Put(userId, model);

        protected override async Task<User> CreateUserAsync(UserPostResult model)
        {
            var companyId = this.tenantProvider.GetCompanyId();
            var frequencyId = this.tenantProvider.GetFrequencyId();
            var user = this.mapper.Map<User>(model);

            user.SecurityGroupsUsers = new List<SecurityGroupUser>();
            user.UserCompanyLinks = new List<UserCompanyLink>();
            user.UserAgencyLinks = new List<UserAgencyLink>();
            user.UserCompanyContactTypes = new List<UserContactType>();

            await this.userProfileService.SetUserSecurityGroupsAsync(user, companyId!.Value, model.DoesNotRequireSystemAccess != true, model.SecurityGroupUsers);
            await this.userProfileService.SetUserCompanyContactTypeAsync(user, companyId!.Value, model.AllocateContact, model.UserCompanyContactTypes);

            switch (user.UserType)
            {
                case UserType.Company:
                    await this.userProfileService.SetUserCompanyLinksAsync(user, companyId!.Value, frequencyId!.Value, model.DoesNotRequireSystemAccess != true, model.UserCompanyLinks);
                    user.CompanyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId!.Value);
                    user.CompanyId = companyId;
                    break;
                case UserType.Agency:
                    await this.userProfileService.SetUserAgencyLinksAsync(user, companyId!.Value, model.UserCompanyLinks);
                    user.AgencyId = await this.companyService.GetAgencyIdAsync(companyId!.Value);
                    user.CompanyId = companyId;
                    break;
                case UserType.Employee:
                    user.UserAgencyLinks = null;
                    user.SecurityGroupsUsers = null;
                    user.UserCompanyLinks = null;
                    break;
            }

            return user;
        }

        protected override async Task<User> UpdateUserAsync(UserResult model, User user, long userId)
        {
            this.mapper.Map(model, user);

            var companyId = this.tenantProvider.GetCompanyId();
            await this.userProfileService.SetUserSecurityGroupsAsync(user, companyId!.Value, model.DoesNotRequireSystemAccess != true, model.SecurityGroupUsers);
            await this.userProfileService.SetUserCompanyContactTypeAsync(user, companyId!.Value, model.AllocateContact, model.UserCompanyContactTypes);

            var frequencyId = this.tenantProvider.GetFrequencyId();
            switch (user.UserType)
            {
                case UserType.Company:
                    await this.userProfileService.SetUserCompanyLinksAsync(user, companyId!.Value, frequencyId!.Value, model.DoesNotRequireSystemAccess != true, model.UserCompanyLinks);
                    user.CompanyGroupId ??= await this.companyService.GetCompanyGroupIdAsync(companyId!.Value);
                    user.CompanyId = companyId;
                    user.AgencyId = null;
                    break;
                case UserType.Agency:
                    await this.userProfileService.SetUserAgencyLinksAsync(user, companyId!.Value, model.UserCompanyLinks);
                    user.CompanyGroupId = null;
                    user.CompanyId = companyId;
                    break;
                case UserType.Employee:
                    this.EmployeeCleanUp(user);
                    break;
            }

            return user;
        }

        // Employee should not have any of the fields below populated as it triggers the validations
        private void EmployeeCleanUp(User user)
        {
            user.UserAgencyLinks = null;
            user.SecurityGroupsUsers = null;
            user.UserCompanyLinks = null;
            user.UserCountryPermissions = null;

            if (user.UserCompanyContactTypes.Count > 0)
            {
                user.AllocateContact = true;
            }
            else
            {
                user.AllocateContact = false;
                user.UserCompanyContactTypes = null;
            }

            user.DoesNotRequireSystemAccess = false;
            user.ReceiveNewsletters = false;
            user.CanEditHistoricalRecords = false;
            user.AnalyticsUser = false;
            user.IsCloudRoomUser = false;
            user.IsBudgetUser = false;
            user.IsOrgChartUser = false;
        }
    }
}