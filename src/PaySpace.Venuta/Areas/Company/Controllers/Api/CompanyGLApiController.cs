namespace PaySpace.Venuta.Areas.Company.Controllers.Api
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Data.ResponseModel;
    using DevExtreme.AspNet.Mvc;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Areas.Company.ViewModels.CompanyGL;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Validation.Annotations;

    [Area("Company")]
    [Route("company/{companyId:long}/frequency/{frequencyId:long}/company-gl/api")]
    [NavigationControllerName("CompanyGl")]
    [ApiController]
    public class CompanyGLApiController : ControllerBase
    {
        private readonly ICompanyGlService companyGlService;
        private readonly ICompanyGlDetailsService companyGlDetailsService;

        public CompanyGLApiController(
            ICompanyGlService companyGlService,
            ICompanyGlDetailsService companyGlDetailsService)
        {
            this.companyGlService = companyGlService;
            this.companyGlDetailsService = companyGlDetailsService;
        }

        [HttpGet("components/{payslipAction}")]
        public async Task<ActionResult<LoadResult>> GetInitialComponents(
            DataSourceLoadOptions loadOptions,
            long companyId,
            long frequencyId,
            PayslipAction payslipAction,
            [FromQuery] bool includeInactive = false)
        {
            return await this.ComponentsResultAsync(
                companyId,
                frequencyId,
                payslipAction,
                loadOptions,
                includeInactive);
        }

        [HttpGet("{id:long}/components/{payslipAction}")]
        public async Task<ActionResult<LoadResult>> GetCompanyGLDetails(
            DataSourceLoadOptions loadOptions,
            long id,
            PayslipAction payslipAction,
            long companyId,
            long frequencyId,
            [FromQuery] bool includeInactive = false)
        {
            if (!await this.companyGlService.CompanyGlExistsAsync(id, companyId, frequencyId))
            {
                return this.NotFound();
            }

            return await this.ComponentsResultAsync(
                companyId,
                frequencyId,
                payslipAction,
                loadOptions,
                includeInactive,
                companyGlId: id);
        }

        /// <summary>
        /// Fetch components available for the given company and frequency
        /// optionally pre-loading existing detail records when GL id if supplied.
        /// </summary>
        private async Task<LoadResult> ComponentsResultAsync(
            long companyId,
            long frequencyId,
            PayslipAction payslipAction,
            DataSourceLoadOptions loadOptions,
            bool includeInactive,
            long? companyGlId = null)
        {
            // Fetch available components
            var components = await this.companyGlService.GetAvailableComponentsAsync(
                companyId,
                frequencyId,
                includeInactive,
                payslipAction);

            if (components.Count == 0)
            {
                return DataSourceLoader.Load(
                    Enumerable.Empty<CompanyGLComponentsViewModel>(),
                    loadOptions);
            }

            // If we are editing an existing CompanyGL, preload its details once
            Dictionary<long, CompanyGLDetail> detailLookup = [];

            if (companyGlId.HasValue)
            {
                var ids = components.Select(c => c.ComponentId).ToHashSet();

                detailLookup = this.companyGlDetailsService
                               .GetForCompanyGl(companyGlId.Value, ids)
                               .ToDictionary(d => d.CompanyComponentId);
            }

            // Map to the grid view-model
            var viewModels = components
                .Select(c =>
                {
                    detailLookup.TryGetValue(c.ComponentId, out var detail);

                    return new CompanyGLComponentsViewModel
                    {
                        CompanyGLDetailId = detail?.CompanyGLDetailId,
                        ComponentCompany = c.AliasDescription,
                        TaxCode = c.TaxCode,
                        ComponentCode = c.TaxCodeDescription,
                        GLAccountNumber = detail?.GLAccountNumber ?? string.Empty,
                        GLContraAccountNumber = detail?.GLContraAccountNumber ?? string.Empty,
                        IsActive = c.Active
                    };
                })
                .OrderBy(vm => vm.ComponentCompany);

            return DataSourceLoader.Load(viewModels, loadOptions);
        }
    }
}