namespace PaySpace.Venuta.Areas.Company.Controllers.Api
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Controllers.Api;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions.Models;
    using PaySpace.Venuta.Validation.Annotations;

    [Area("Company")]
    [DisplayName(SystemAreas.CompanyLeaveScheme.Area)]
    [Route("company/{companyId}/leave-scheme-parameter/api")]
    [NavigationControllerName("CompanyLeaveSchemeParameter")]
    public class CompanyLeaveSchemeParameterApiController : ApiController
    {
        private readonly ICompanyLeaveSchemeParameterService leaveSchemeParameterService;

        public CompanyLeaveSchemeParameterApiController(ICompanyLeaveSchemeParameterService leaveSchemeParameterService)
        {
            this.leaveSchemeParameterService = leaveSchemeParameterService;
        }

        [HttpGet]
        public Task<List<LeaveSchemeResult>> Get(long companyId)
        {
            return this.leaveSchemeParameterService.GetTreeListStructureAsync(companyId);
        }

        [HttpGet("order-numbers/{companyLeaveSchemeId}/{leaveTypeId}/{isNew}")]
        public object GetOrderNumbers(long companyLeaveSchemeId, int leaveTypeId, bool isNew = false)
        {
            if (!Enum.IsDefined(typeof(LeaveType), leaveTypeId))
            {
                return this.BadRequest("Invalid Leave Type");
            }

            var orderNumbers = this.leaveSchemeParameterService.GetOrderNumbersLookup(companyLeaveSchemeId, (LeaveType)leaveTypeId).ToList();
            if (!isNew)
            {
                return orderNumbers;
            }

            // Add one for new / copy records
            var nextOrderNumber = orderNumbers.Count + 1;
            var newOrderNumberObj = new
            {
                text = nextOrderNumber.ToString(),
                value = nextOrderNumber
            };

            orderNumbers.Add(newOrderNumberObj);
            return orderNumbers;
        }
    }
}