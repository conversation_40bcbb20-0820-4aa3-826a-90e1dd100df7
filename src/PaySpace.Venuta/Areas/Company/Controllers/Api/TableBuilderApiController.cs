namespace PaySpace.Venuta.Areas.Company.Controllers.Api
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Data.ResponseModel;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Areas.Company.ViewModels.CustomFields;
    using PaySpace.Venuta.Controllers.Api;
    using PaySpace.Venuta.Conventions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Infrastructure.Extensions;
    using PaySpace.Venuta.ModelBinders;
    using PaySpace.Venuta.Modules.EmploymentStability.Abstractions.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Abstractions.Models;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName("CompanyTableBuilder")]
    [Area("Company")]
    public class TableBuilderApiController : ApiController
    {
        private readonly IValidator<TableBuilder> validator;
        private readonly ITableBuilderService tableBuilderService;
        private readonly IStabilityRuleService stabilityRuleService;
        private readonly IMapper mapper;
        private readonly IEnumService enumService;
        private readonly ICompanyService companyService;

        public TableBuilderApiController(
            IValidator<TableBuilder> validator,
            ICompanyService companyService,
            ITableBuilderService tableBuilderService,
            IStabilityRuleService stabilityRuleService,
            IMapper mapper,
            IEnumService enumService)
        {
            this.validator = validator;
            this.companyService = companyService;
            this.tableBuilderService = tableBuilderService;
            this.stabilityRuleService = stabilityRuleService;
            this.mapper = mapper;
            this.enumService = enumService;
        }

        [HttpGet("company/{companyId}/api")]
        public async Task<object> Get(DataSourceLoadOptions loadOptions, long companyId, TableBuilderLevel level, long categoryId)
        {
            if (categoryId == 0)
            {
                return DataSourceLoader.Load(Array.Empty<TableBuilderResult>(), loadOptions);
            }

            var tablebuilders = await this.tableBuilderService.GetWithCustomFields(level, companyId, categoryId);
            return DataSourceLoader.Load(tablebuilders, loadOptions);
        }

        [CreateMessage]
        [HttpPost("company/{companyId}/api")]
        [Permission(SystemAreas.CompanyTableBuilder.Area, SystemAreas.CompanyTableBuilder.Area)]
        public async Task<IActionResult> Post(long companyId, string values, TableBuilderLevel level, long categoryId)
        {
            var model = new TableBuilderResult
            {
                CategoryId = categoryId
            };

            JsonConvert.PopulateObject(values, model);

            var tableBuilder = new TableBuilder();

            await this.SetLevelId(tableBuilder, level, companyId);

            this.MapAndSetEffectiveDate(model, tableBuilder, null);

            await this.ValidateAsync(model, tableBuilder, RuleSetNames.CreateAndUpdate);
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.tableBuilderService.AddAsync(tableBuilder);

            return this.Ok(model);
        }

        [UpdateMessage]
        [HttpPut("company/{companyId}/api")]
        [Permission(SystemAreas.CompanyTableBuilder.Area, SystemAreas.CompanyTableBuilder.Area)]
        public async Task<IActionResult> Put(long companyId, long key, string values, TableBuilderLevel level, long categoryId)
        {
            var model = new TableBuilderResult
            {
                CategoryId = categoryId,
                Id = key
            };

            JsonConvert.PopulateObject(values, model);
            if (model.Id != key)
            {
                return this.Forbid();
            }

            var tableBuilder = await this.tableBuilderService.FindAsync(categoryId, key);
            if (tableBuilder == null)
            {
                return this.NotFound();
            }

            this.MapAndSetEffectiveDate(model, tableBuilder, model.EffectiveDate);

            await this.ValidateAsync(model, tableBuilder, RuleSetNames.CreateAndUpdate);
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.tableBuilderService.UpdateAsync(tableBuilder);

            return this.Ok(model);
        }

        [DeleteMessage]
        [HttpDelete("company/{companyId}/api")]
        [Permission(SystemAreas.CompanyTableBuilder.Area, SystemAreas.CompanyTableBuilder.Area)]
        public async Task<IActionResult> Delete(long companyId, long key, TableBuilderLevel level, long categoryId)
        {
            var tableBuilder = await this.tableBuilderService.FindAsync(categoryId, key);
            if (tableBuilder == null)
            {
                return this.NotFound();
            }

            await this.ValidateAsync(null, tableBuilder, RuleSetNames.Delete);
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.tableBuilderService.DeleteAsync(tableBuilder);
            return this.Ok();
        }

        [DeleteMessage]
        [HttpDelete("company/{companyId}/EffectiveDate/api")]
        [Permission(SystemAreas.CompanyTableBuilder.Area, SystemAreas.CompanyTableBuilder.Area)]
        public async Task<IActionResult> DeleteByEffectiveDate(long companyId, [JsonModelBinder] TableBuilderDeleteViewModel key, TableBuilderLevel level, long categoryId)
        {
            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            var tableBuilder = await this.tableBuilderService.FindAsync(categoryId, key.Id);
            if (tableBuilder == null)
            {
                return this.NotFound();
            }

            await this.tableBuilderService.DeleteAsync(tableBuilder, key.EffectiveDate.Value);

            return this.Ok();
        }

        [HttpGet("company/{companyId}/history/api")]
        public async Task<LoadResult> GetHistory(DataSourceLoadOptions loadOptions, long companyId, TableBuilderLevel level, long id)
        {
            var tableBuilders = await this.tableBuilderService.GetHistory(level, companyId, id);

            return DataSourceLoader.Load(tableBuilders, loadOptions);
        }

        [HttpGet("company/{companyId}/stability/{categoryId}/api")]
        public async Task<IActionResult> GetStabilities(DataSourceLoadOptions loadOptions, long companyId, long categoryId, long tableBuilderId)
        {
            if (companyId == default || categoryId == default || tableBuilderId == default)
            {
                return this.BadRequest();
            }

            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var stabilities = await this.stabilityRuleService.GetTableBuilderStabilityRulesAsync(countryId, categoryId, tableBuilderId);
            await this.LocalizeStabilitiesAsync(stabilities, countryId);

            return this.Ok(DataSourceLoader.Load(stabilities, loadOptions));
        }

        [UpdateMessage]
        [HttpPut("company/{companyId}/stability/{categoryId}/api")]
        public async Task<IActionResult> PutStabilities(long companyId, long categoryId, long tableBuilderId, long key, string values)
        {
            var model = new ConfiguredStabilityRule()
            {
                Id = key
            };

            JsonConvert.PopulateObject(values, model);

            await this.stabilityRuleService.UpdateTableBuilderStabilityRuleAsync(categoryId, tableBuilderId, model);

            return this.Ok();
        }

        private async Task SetLevelId(TableBuilder? tableBuilder, TableBuilderLevel level, long companyId)
        {
            switch (level)
            {
                case TableBuilderLevel.AgencyLevel:
                    tableBuilder.CompanyGroupId = null;
                    tableBuilder.AgencyId = await this.companyService.GetAgencyIdAsync(companyId);
                    break;

                case TableBuilderLevel.GroupLevel:
                    tableBuilder.AgencyId = null;
                    tableBuilder.CompanyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
                    break;
            }
        }

        private async Task ValidateAsync(TableBuilderResult? model, TableBuilder? tableBuilder, string ruleset)
        {
            var validationData = new Dictionary<string, object>();

            if (model?.EffectiveDate != null)
            {
                validationData.Add(PaySpaceConstants.CustomFieldEffectiveDate, model.EffectiveDate);
            }

            var result = await this.validator.ValidateAsync(tableBuilder, validationData, ruleset);
            result.AddToModelState(this.ModelState, null);
        }

        private void MapAndSetEffectiveDate(TableBuilderResult model, TableBuilder tableBuilder, DateTime? effectiveDate)
        {
            this.SetEffectiveDate(model);

            this.mapper.Map(model, tableBuilder, opts => opts.Items[nameof(effectiveDate)] = effectiveDate);
        }

        private void SetEffectiveDate(TableBuilderResult model)
        {
            foreach (var fieldValue in model.CustomFields)
            {
                fieldValue.EffectiveDate = model.EffectiveDate;
            }
        }

        private async Task LocalizeStabilitiesAsync(List<ConfiguredStabilityRule> stabilities, long countryId)
        {
            var localizedStabilities = await this.enumService.GetStabilitiesAsync(countryId);
            var localizedStabilityTypes = await this.enumService.GetStabilityTypesAsync(countryId);

            foreach (var stability in stabilities)
            {
                stability.StabilityType = localizedStabilityTypes.FirstOrDefault(_ => _.StabilityTypeId == stability.StabilityTypeId)?.Description ?? stability.StabilityType;
                stability.Stability = localizedStabilities.FirstOrDefault(_ => _.StabilityId == stability.StabilityId)?.Description ?? stability.StabilityType;
            }
        }
    }
}