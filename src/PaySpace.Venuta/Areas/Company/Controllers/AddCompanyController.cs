namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Caching.Distributed;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Company.ViewModelBuilder;
    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Authentication;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Infrastructure.Helpers;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Message;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Bureau;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Validation.Annotations;
    using PaySpace.Venuta.Workflow.Activities;

    [Area("Company")]
    [SecurityDisplayName(SystemAreas.AddCompany.Area, SystemAreas.Company.Area)]
    public class AddCompanyController : Controller
    {
        private readonly ICompanyDefaultsService companyDefaultsService;
        private readonly ICompanyProfileViewModelBuilder builder;
        private readonly IAddCompanyProfileViewModelBuilder addCompanyBuilder;
        private readonly ICalcSchedulingService calcSchedulingService;
        private readonly ICompanyService companyService;
        private readonly ICompanyGroupService companyGroupService;
        private readonly IDistributedCache distributedCache;
        private readonly IImageService imageService;
        private readonly IManagerService managerService;
        private readonly IMapper mapper;
        private readonly ITenantProvider tenantProvider;
        private readonly IUserSecurityService userSecurityService;
        private readonly IValidator<Company> validator;
        private readonly IWorkflowActivityProcessor workflowActivityProcessor;
        private readonly IMessageBus messageBus;
        private readonly IEnumService enumService;

        public AddCompanyController(
            ICompanyDefaultsService companyDefaultsService,
            ICompanyProfileViewModelBuilder builder,
            IAddCompanyProfileViewModelBuilder addCompanyBuilder,
            ICalcSchedulingService calcSchedulingService,
            ICompanyGroupService companyGroupService,
            ICompanyService companyService,
            IDistributedCache distributedCache,
            IImageService imageService,
            IManagerService managerService,
            IMapper mapper,
            ITenantProvider tenantProvider,
            IUserSecurityService userSecurityService,
            IValidator<Company> validator,
            IWorkflowActivityProcessor workflowActivityProcessor,
            IMessageBus messageBus,
            IEnumService enumService)
        {
            this.companyDefaultsService = companyDefaultsService;
            this.builder = builder;
            this.addCompanyBuilder = addCompanyBuilder;
            this.calcSchedulingService = calcSchedulingService;
            this.companyGroupService = companyGroupService;
            this.companyService = companyService;
            this.distributedCache = distributedCache;
            this.imageService = imageService;
            this.managerService = managerService;
            this.mapper = mapper;
            this.tenantProvider = tenantProvider;
            this.userSecurityService = userSecurityService;
            this.validator = validator;
            this.workflowActivityProcessor = workflowActivityProcessor;
            this.messageBus = messageBus;
            this.enumService = enumService;
        }

        public async Task<IActionResult> Index(int? countryId, GroupAction? groupAction)
        {
            if (!await this.CanAddCompanyAsync())
            {
                return this.Unauthorized();
            }

            this.HttpContext.Items.Add("IgnoreCustomFields", true);
            var hasSession = this.managerService.TryGetUserSessionSettings(this.HttpContext, this.User, out var session);
            if (!hasSession)
            {
                this.ViewBag.Layout = "_Layout-Auth-Blank-Nav";
            }
            else
            {
                this.ViewBag.Layout = "_Layout-Auth";
            }

            var viewModel = await this.addCompanyBuilder.BuildViewModelAsync(this.User, countryId, groupAction, hasSession);

            return this.View("Create", viewModel);
        }

        [HttpPost]
        [ErrorMessage]
        public async Task<IActionResult> Index(int? countryId, AddCompanyViewModel model)
        {
            if (!await this.CanAddCompanyAsync())
            {
                return this.Unauthorized();
            }

            // Addresses
            model.Address.Add(model.PostalAddress);
            model.Address.Add(model.PhysicalAddress);
            model.UserType = Enum.Parse<UserType>(this.User.GetUserType());

            await CompanyAddressHelper.UpdateCompanyAddressEntitiesAsync(model.Address, this, this.mapper, model.TaxCountryId);

            // Company
            var company = this.mapper.Map<Company>(model);

            // Company Group
            company.GroupLinks = new List<CompanyGroupLink>
            {
                await this.companyGroupService.GenerateCompanyGroupAsync(model.GroupAction, model.CompanyName, model.IncludeAnalytics, model.Region)
            };

            company.CompanyPaymentModule = new List<CompanyPaymentModule>
            {
                new() { PaymentModuleId = model.PaymentModuleId }
            };

            // set agency when BP users add company otherwise security will not allow access to the company
            if (this.User.IsInRole(UserTypeCodes.Company) || this.User.IsInRole(UserTypeCodes.Agency))
            {
                company.AgencyId = this.User.GetAgencyId();
            }

            this.companyDefaultsService.SanitizeAddress(company);
            this.companyDefaultsService.SetDefaultAddress(company.Address);
            this.companyDefaultsService.DefaultCompanyValues(company);

            // We need to clear the model state because the Address is being validated before the AddressHelper is run.
            this.ModelState.Clear();
            var validateResult = await this.validator.ValidateAsync(company, opt => opt.IncludeRuleSets(RuleSetNames.Create));
            validateResult.AddToModelState(this.ModelState, null);

            if (!this.ModelState.IsValid)
            {
                var hasSession = this.managerService.TryGetUserSessionSettings(this.HttpContext, this.User, out var session);
                if (!hasSession)
                {
                    this.ViewBag.Layout = "_Layout-Auth-Blank-Nav";
                }
                else
                {
                    this.ViewBag.Layout = "_Layout-Auth";
                }

                model = await this.builder.BuildAddCompanyViewModelDefaultsAsync(model, model.CompanyId, this.User, this.HttpContext);
                model.HasSession = hasSession;

                return this.View("Create", model);
            }

            var userType = Enum.Parse<UserType>(this.User.GetUserType());

            await this.companyDefaultsService.SetFeesPaymentMethodAsync(company, userType, this.User.GetAgencyId());

            var newCompany = await this.companyService.AddAsync(company);

            await this.companyDefaultsService.AddCompanyDefaultsAsync(newCompany);
            await this.companyDefaultsService.SetUserLinksAsync(newCompany, this.User.GetUserId(), userType);
            await this.companyDefaultsService.CreateDefaultEssRole(newCompany.CompanyId);
            await this.distributedCache.RemoveAsync(CacheKeys.UserCompanyLinks(this.User.GetUserId()));
            await this.SetDefaultTaxCalculationMethodAsync(newCompany);

            // Images
            if (model.CompanyLogo?.Length > 0)
            {
                await this.imageService.StoreBlobAsync(StorageContainers.Images, "Company", Convert.ToString(newCompany.CompanyId), model.CompanyLogo);
            }

            if (model.CompanyBackground?.Length > 0)
            {
                await this.imageService.StoreBlobAsync(StorageContainers.CompanyTheme, Convert.ToString(newCompany.CompanyId), "login_bg.jpg", model.CompanyBackground);
            }

            await this.workflowActivityProcessor.ProcessAddWorkflowApplicationAsync((int)WorkflowHeaderEnum.CompanyRegister, this.User.GetUserId(), newCompany.CompanyId, null);
            await this.calcSchedulingService.RecalculateCompany(newCompany.CompanyId);

            await this.messageBus.PublishMessageAsync(new ClearCompanyCacheMessage()
            {
                CompanyIds = new[] { newCompany.CompanyId }
            });

            return this.RedirectToAction("SelectCompany", "Select", new { area = string.Empty, newCompany.CompanyId });
        }

        private Task<bool> CanAddCompanyAsync()
        {
            var companyId = this.tenantProvider.GetCompanyId();
            var securityProfile = companyId.HasValue ? this.HttpContext.GetSecurityProfile() : null;

            return this.userSecurityService.CanAddNewCompanyAsync(companyId, securityProfile, this.User);
        }

        private async Task SetDefaultTaxCalculationMethodAsync(Company company)
        {
            if (company.TaxCountryId is ((int)TaxCountry.Uganda) or ((int)TaxCountry.Niger))
            {
                var calculationMethods = await this.enumService.GetCalculationMethodsAsync();
                company.TaxCalculationMethod = calculationMethods.First(_ => _.CalculationMethodCode == "NonCumulativeTaxMethod").CalculationMethodCode;
            }
        }
    }
}