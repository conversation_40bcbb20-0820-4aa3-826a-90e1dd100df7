namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.ViewModels.Templates;

    [Area("Company")]
    [DisplayName(SystemAreas.TemplateSetup.CompanyArea)]
    public class CompanyTemplateSetupController : Controller
    {
        public IActionResult Index()
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.TemplateSetup.CompanyArea, SystemAreas.TemplateSetup.CompanyArea);

            var model = new TemplateSetupViewModel
            {
                AllowEdit = allowEdit,
                TemplateApiControllerName = "CompanyTemplate",
                ComponentVariableApiControllerName = "CompanyComponentVariable"
            };

            return this.View("Templates/TemplateSetup", model);
        }
    }
}