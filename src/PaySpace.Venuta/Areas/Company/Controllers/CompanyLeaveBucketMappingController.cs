namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Infrastructure;

    [Area("Company")]
    [DisplayName(SystemAreas.CompanyLeaveBucketMapping.Area)]
    public class CompanyLeaveBucketMappingController : Controller
    {
        [HttpGet]
        public IActionResult Index()
        {
            return this.View(new CompanyLeaveBucketMappingViewModel(this.Url.Action(nameof(this.Edit))));
        }

        [HttpGet]
        public IActionResult Edit(long? leaveBucketMappingId)
        {
            return this.View(new CompanyLeaveBucketMappingEditViewModel(leaveBucketMappingId, this.Url.Action(nameof(this.Index))));
        }
    }
}
