namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;

    [Area("Company")]
    [DisplayName(SystemAreas.CompanyFrequencies.Area)]
    public class CompanyFrequencyController : Controller
    {
        private readonly ICompanyService companyService;
        private readonly ICountryTaxYearService taxYearService;
        private readonly ICompanyRunService companyRunService;
        private readonly ICompanySettingService companySettingService;
        private readonly ITenantProvider tenantProvider;
        private readonly IStringLocalizer<CompanyFrequencyController> localizer;

        public CompanyFrequencyController(
            ICompanyService companyService,
            ICountryTaxYearService taxYearService,
            ICompanyRunService companyRunService,
            ICompanySettingService companySettingService,
            ITenantProvider tenantProvider,
            IStringLocalizer<CompanyFrequencyController> localizer)
        {
            this.companyService = companyService;
            this.taxYearService = taxYearService;
            this.companyRunService = companyRunService;
            this.companySettingService = companySettingService;
            this.tenantProvider = tenantProvider;
            this.localizer = localizer;
        }

        public async Task<IActionResult> Index(long companyId)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var profile = this.HttpContext.GetSecurityProfile();
            return this.View(new CompanyFrequencyViewModel()
            {
                AddOptions = this.GetAddOptions(countryId),
                SelectedFrequencyId = this.tenantProvider.GetFrequencyId(),
                CountryId = countryId,
                CanEdit = profile.IsFullAccess(SystemAreas.CompanyFrequencies.Area, SystemAreas.CompanyFrequencies.Area)
            });
        }

        [HttpGet]
        public async Task<IActionResult> Edit(Tenant tenant)
        {
            if (tenant.FrequencyId == null)
            {
                return this.RedirectToAction("Index");
            }

            var vm = await this.GetViewModelAsync(tenant);

            return this.View(vm);
        }

        [HttpGet]
        public async Task<IActionResult> Create(Tenant tenant, PayslipFrequency? SelectFrequency = null)
        {
            if (SelectFrequency == null)
            {
                SelectFrequency = PayslipFrequency.Monthly;
            }

            var vm = await this.GetViewModelAsync(tenant, SelectFrequency);

            return this.View("Edit", vm);
        }

        private async Task<CompanyFrequencyViewModel> GetViewModelAsync(Tenant tenant, PayslipFrequency? frequencyType = null)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(tenant.CompanyId);
            var taxYear = await this.taxYearService.GetCountryTaxYearByDateAsync(countryId, DateTime.Today);
            var taxYearDates = await this.taxYearService.GetCountryMinMaxTaxDatesAsync(countryId);
            var profile = this.HttpContext.GetSecurityProfile();

            var workDaySetting = await this.companySettingService.IsActiveAsync(tenant.CompanyId, CompanySettingCode.PayRate.WorkDay);
            var openRuns = await this.companyRunService.GetCompanyRuns(tenant.FrequencyId ?? 0, RunStatus.Open).AnyAsync();
            var closedRuns = await this.companyRunService.GetCompanyRuns(tenant.FrequencyId ?? 0, RunStatus.Closed).AnyAsync();

            return new CompanyFrequencyViewModel()
            {
                FrequencyId = tenant.FrequencyId ?? 0,
                RunFrequency = (int?)frequencyType,
                TaxYearStartDate = taxYear.YearStartDate,
                CountryId = countryId,
                HasWorkforcePlanningModule = await this.companySettingService.IsActiveAsync(tenant.CompanyId, CompanySettingCode.Positions.JobManagement),
                ShowWorkingDays = workDaySetting,
                AddOptions = this.GetAddOptions(countryId),
                EnableAdditionalTab = PaySpaceConstants.CompaniesWithAdditionalFrequencyTab.Contains(tenant.CompanyId),
                CanEdit = profile.IsFullAccess(SystemAreas.CompanyFrequencies.Area, SystemAreas.CompanyFrequencies.Area),
                MinTaxDate = taxYearDates.Min,
                MaxTaxDate = taxYearDates.Max,
                HasOpenRuns = openRuns,
                HasClosedRuns = closedRuns
            };
        }

        private List<ButtonOption> GetAddOptions(long taxCountryId)
        {
            var addOptions = new List<ButtonOption>();

            if (taxCountryId == (long)TaxCountry.Singapore)
            {
                addOptions.Add(new ButtonOption { Text = this.localizer.GetString("lblMonthly"), Value = 2 });
            }
            else
            {
                addOptions.AddRange(new List<ButtonOption>
                {
                    new() { Text = this.localizer.GetString("lblFortnight"), Value = 3 },
                    new() { Text = this.localizer.GetString("lblMonthly"), Value = 2 },
                    new() { Text = this.localizer.GetString("lblWeekly"), Value = 1 }
                });
            }

            if (taxCountryId == (long)TaxCountry.Canada)
            {
                addOptions.Add(new ButtonOption { Text = this.localizer.GetString("lblSemiMonthly"), Value = 4 });
            }

            addOptions.Sort((a, b) => a.Text.CompareTo(b.Text));
            return addOptions;
        }
    }
}