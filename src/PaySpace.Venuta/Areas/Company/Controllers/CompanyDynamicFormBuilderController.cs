namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Abstractions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Model;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Messages;

    [Area("Company")]
    [DisplayName(SystemAreas.DynamicFormBuilder.Area)]
    public class CompanyDynamicFormBuilderController : Controller
    {
        private readonly ITenantProvider tenantProvider;
        private readonly ICompanyService companyService;
        private readonly IMenuBuilder menuBuilder;
        private readonly IMessageBus messageBus;
        private readonly IDynamicFormBuilderService dynamicFormBuilderService;
        private readonly IEmployeeService employeeService;

        public CompanyDynamicFormBuilderController(
            ITenantProvider tenantProvider,
            ICompanyService companyService,
            IMenuBuilder menuBuilder,
            IMessageBus messageBus,
            IDynamicFormBuilderService dynamicFormBuilderService,
            IEmployeeService employeeService)
        {
            this.tenantProvider = tenantProvider;
            this.companyService = companyService;
            this.menuBuilder = menuBuilder;
            this.messageBus = messageBus;
            this.dynamicFormBuilderService = dynamicFormBuilderService;
            this.employeeService = employeeService;
        }

        public async Task<IActionResult> Index()
        {
            var companyId = this.tenantProvider.GetCompanyId().Value;
            var frequencyId = this.tenantProvider.GetFrequencyId().Value;
            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);

            var vm = new DynamicFormBuilderPageLoadViewModel
            {
                GenerateEmployeeNumber = await this.companyService.ShouldGenerateEmployeeNumber(this.tenantProvider.GetCompanyId().Value),
                TaxCountryId = taxCountryId,
                BureauAddNewEmployeeDynamicFormId = await this.dynamicFormBuilderService.FindBureauAddNewEmployeeDynamicFormIdAsync(companyId, frequencyId, accessToken)
            };

            return this.View(vm);
        }

        // Lite companies are blocked on this company level menu, but dynamic form templates need access to the action to work propertly, so AllowAll.
        [AllowAll]
        public async Task<IActionResult> SuccessfulQuickAdd(long companyId, long employeeId)
        {
            // After successfully doing an employee Quick Add, we need to run the following
            // tasks before being redirected to the OnboardingNotifications controller.

            // Clearing the user menu cache, else the user will get an unauthorized exception when navigation to the new employee.
            await this.menuBuilder.ClearUserMenusCacheAsync(this.User.GetUserId(), this.User.GetUserType(), companyId);

            // Trigger CreateEmployeeProfileMessage, else the frequency appearing next to the user in the search box won't be set.
            await this.messageBus.PublishMessageAsync(new CreateEmployeeProfileMessage { EmployeeId = employeeId });

            var profile = this.HttpContext.GetSecurityProfile();
            string? returnUrl = null;
            var hasFullAccessOnRecurringComponentsPage = profile.IsFullAccess(SystemAreas.RecurringComponent.Area, SystemAreas.RecurringComponent.Area);

            if (hasFullAccessOnRecurringComponentsPage)
            {
                var frequencyId = await this.employeeService.GetEmployeeFrequencyIdAsync(employeeId);
                returnUrl = this.Url.Action("Index", "RecurringComponent", new { area = "Employees", CompanyId = companyId, EmployeeId = employeeId, FrequencyId = frequencyId });
            }

            return this.RedirectToAction("Index", "OnboardingNotifications", new { area = "Employees", processId = ProcessNotifications.OnBoarding, returnUrlOverwrite = returnUrl });
        }
    }
}