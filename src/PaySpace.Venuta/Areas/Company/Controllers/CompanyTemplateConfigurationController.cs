namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.ViewModels.Templates;

    [Area("Company")]
    [DisplayName(SystemAreas.TemplateConfiguration.CompanyArea)]
    public class CompanyTemplateConfigurationController : Controller
    {
        public IActionResult Index()
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.TemplateConfiguration.CompanyArea, SystemAreas.TemplateConfiguration.CompanyArea);

            var model = new TemplateConfigurationViewModel
            {
                ApiControllerName = "CompanyTemplateConfiguration",
                EditUrl = this.Url.Action(nameof(Edit)),
                AllowEdit = allowEdit,
                IsCompany = true
            };

            return this.View("Templates/TemplateConfiguration", model);
        }

        public IActionResult Edit(long? templateConfigurationId)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.TemplateConfiguration.CompanyArea, SystemAreas.TemplateConfiguration.CompanyArea);

            var viewModel = new TemplateConfigurationEditViewModel
            {
                BaseUrl = this.Url.Action(nameof(Index)),
                EditUrl = this.Url.Action(nameof(Edit), new { templateConfigurationId = (long?)null }),
                ApiControllerName = "CompanyTemplateConfiguration",
                TemplatesApiControllerName = "CompanyTemplate",
                ComponentVariablesApiControllerName = "CompanyComponentVariable",
                TemplateConfigurationId = templateConfigurationId.GetValueOrDefault(),
                AllowEdit = allowEdit,
                AllowOverride = true,
                IsCompany = true
            };

            return this.View("Templates/TemplateConfigurationEdit", viewModel);
        }
    }
}