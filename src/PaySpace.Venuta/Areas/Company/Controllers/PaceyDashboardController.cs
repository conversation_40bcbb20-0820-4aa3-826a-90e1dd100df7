namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Threading.Tasks;

    using DevExpress.DashboardCommon;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Configuration;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security.Identity;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.ViewModels;

    [DisplayName(SystemAreas.Pacey.Area)]
    [Area("Company")]
    public class PaceyDashboardController : Controller
    {
        private readonly IConfiguration configuration;
        private readonly IReportUrlResolver reportUrlResolver;
        private readonly TokenEndpointService tokenEndpointService;

        public PaceyDashboardController(IConfiguration configuration, IReportUrlResolver reportUrlResolver, TokenEndpointService tokenEndpointService)
        {
            this.configuration = configuration;
            this.reportUrlResolver = reportUrlResolver;
            this.tokenEndpointService = tokenEndpointService;
        }

        public async Task<IActionResult> Index(long companyId)
        {
            var reportsUrl = this.reportUrlResolver.Resolve(this.Request.Host);
            var minimalToken = await this.tokenEndpointService.ExchangeAsync(
                await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken),
                "nextgen.whatsapp",
                "minimal",
                this.HttpContext.RequestAborted);
            if (minimalToken.IsError)
            {
                throw new InvalidOperationException(minimalToken.Error);
            }

            var now = DateTime.Now;
            var vm = new PaceyDashboardViewModel
            {
                AccessToken = minimalToken.AccessToken,
                DashboardUri = new Uri(reportsUrl, "/dashboard"),
                DashboardInitialState = new DashboardState
                {
                    Parameters = new List<DashboardParameterState>
                    {
                        new ("companyId", companyId, typeof(long)),
                        new ("functionUrl", this.configuration["ClientSettings:PaceyUrl"], typeof(string)),
                        new ("functionKey", this.configuration["Pacey:FunctionsKey"], typeof(string)),
                        new ("reportsUrl", reportsUrl.ToString(), typeof(string)),
                        new ("accessToken", minimalToken.AccessToken, typeof(string))
                    },
                    Items = new List<DashboardItemState>
                    {
                        new ("monthFilter")
                        {
                            RangeFilterState = new RangeFilterState
                            {
                                Selection = new RangeFilterSelection(new DateTime(now.Year, now.Month, 1), now),
                            }
                        }
                    }
                }
            };

            return this.View(vm);
        }
    }
}