namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Options;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Controllers;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Dto;
    using PaySpace.Venuta.Data.Models.Dto.Bureau;
    using PaySpace.Venuta.Data.Models.Dto.Company;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Abstractions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Model;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.External;

    [Area("Company")]
    [Authorize(Policies.Frequency)]
    [DisplayName(SystemAreas.BulkUpload.Area)]
    public class BulkUploadController : Controller
    {
        private static readonly string[] SupportAdding =
        [
            nameof(EditPayslip),
            nameof(EmployeeEtiTakeOnDto).TrimDto(),
            "EmployeeComponent",
            nameof(EmployeePayslipTakeOn)
        ];

        private static readonly string[] DisabledGridTypes =
        [
            "EmployeeOnboarding",
            "EmployeeRecurringTemplate",
            "PaceyMessageTemplate",
            "EmployeeUpdate",
            "EditPayslipAdvanced",
            "EmployeeDependantQuickAdd",
            nameof(OrganizationUnitDto).TrimDto()
        ];

        private static readonly string[] ProtectedTypes = [nameof(CompanyGLDetail)];

        private readonly IHostEnvironment env;
        private readonly ClientSettings clientSettings;
        private readonly ICustomFieldService customFieldService;
        private readonly ICompanyService companyService;
        private readonly ICountryTaxYearService countryTaxYearService;
        private readonly ICompanyRunService companyRunService;
        private readonly IDynamicFormBuilderService dynamicFormBuilderService;

        private const int SingaporeTaxCountryId = 98;

        private readonly Func<string, bool> isPayslipTakeOn = entity => entity != null && entity.Equals(nameof(EmployeePayslipTakeOn), StringComparison.InvariantCultureIgnoreCase);

        public BulkUploadController(
            IHostEnvironment env,
            IOptions<ClientSettings> clientSettings,
            ICustomFieldService customFieldService,
            ICompanyService companyService,
            ICountryTaxYearService countryTaxYearService,
            ICompanyRunService companyRunService,
            IDynamicFormBuilderService dynamicFormBuilderService)
        {
            this.env = env;
            this.clientSettings = clientSettings.Value;
            this.customFieldService = customFieldService;
            this.companyService = companyService;
            this.countryTaxYearService = countryTaxYearService;
            this.companyRunService = companyRunService;
            this.dynamicFormBuilderService = dynamicFormBuilderService;
        }

        public async Task<IActionResult> Index(
            long companyId,
            long frequencyId,
            string entity,
            long? runId,
            string? screenArea,
            string? customFormCategoryCode,
            long? customFormScreenType,
            long? customFormType,
            long? companyLeaveSchemeId)
        {
            if (string.IsNullOrEmpty(entity) || string.IsNullOrEmpty(screenArea) || !await this.IsRunValidForEntityAsync(frequencyId, runId, entity))
            {
                return await this.RedirectWithDefaultAsync(companyId, frequencyId, entity, runId, screenArea);
            }

            if (this.ShouldRedirectToClassic(entity, out var screenCode))
            {
                return this.RedirectToClassic(companyId, frequencyId, runId, screenCode);
            }

            entity = await this.GetScreenEntity(entity, companyId, customFormCategoryCode);

            var apiEntityUrl = this.GetApiEntityUrl(screenArea, companyId, entity, customFormCategoryCode);

            var disabledTypes = await this.GetDisabledTypesAsync(frequencyId);
            var taxCountryId = this.companyService.GetTaxCountryId(companyId);
            string currentTaxYear = null;

            if (taxCountryId == SingaporeTaxCountryId)
            {
                currentTaxYear = await this.countryTaxYearService.GetCurrentTaxYearAsync(taxCountryId);
            }

            var profile = this.HttpContext.GetSecurityProfile();
            var vm = new ODataGridViewModel(
                companyId,
                frequencyId,
                this.clientSettings.GetODataApiUrl(companyId),
                this.clientSettings.GetMetadataApiUrl(companyId),
                apiEntityUrl)
            {
                Entity = entity,
                RunId = runId,
                IsSmallCompany = await this.companyService.IsSmallCompanyAsync(companyId),
                DisableRecurringOption = !this.env.IsEnvironment(EnvironmentNames.Uat) && !this.env.IsEnvironment(EnvironmentNames.Development),
                IsReadOnly = profile.IsReadOnly(SystemAreas.BulkUpload.Area, SystemAreas.BulkUpload.Area),
                OnlyEditable = ProtectedTypes.Contains(entity),
                CanAddEmployee = profile.IsFullAccess(SystemAreas.Profile.BasicCreate, SystemAreas.Profile.Keys.CreateEmployee),
                Area = screenArea,
                CustomFormCategory = customFormCategoryCode,
                CustomFormScreenType = customFormScreenType,
                SupportAdding = SupportAdding,
                DisabledGridTypes = DisabledGridTypes,
                ShowEmployeeTerminationWarning = await this.ShowEmployeeTerminationWarningAsync(companyId, entity),
                CompanyLeaveSchemeId = companyLeaveSchemeId,
                PostRunByRunOnly = this.isPayslipTakeOn(entity),
                DisabledEntities = disabledTypes,
                CustomFormType = customFormType,
                DynamicFormTemplates = await this.GetDynamicFormTemplates(companyId, frequencyId, entity),
                CurrentTaxYear = currentTaxYear
            };

            return this.View(vm);
        }

        private Uri GetApiEntityUrl(string screenArea, long companyId, string entity, string customFormCategoryCode)
        {
            if (screenArea.Equals("Bureau", StringComparison.InvariantCultureIgnoreCase))
            {
                return this.clientSettings.GetBureauEntityApiUrl(entity);
            }

            var apiEntityUrl = this.clientSettings.GetEntityApiUrl(companyId, entity);

            if (!string.IsNullOrWhiteSpace(customFormCategoryCode))
            {
                apiEntityUrl = new Uri($"{apiEntityUrl}/{customFormCategoryCode}");
            }

            return apiEntityUrl;
        }

        private RedirectToActionResult RedirectToClassic(long companyId, long frequencyId, long? runId, string screenCode)
        {
            // This is the nav link that is needed when navigating to the classic bulkupload screen
            const long navLinkId = 111;
            return this.RedirectToAction(
                nameof(ClassicController.Index),
                "Classic",
                new
                {
                    Area = string.Empty,
                    companyId,
                    frequencyId,
                    runId,
                    rid = navLinkId,
                    ScreenCode = screenCode
                });
        }

        private async Task<IActionResult> RedirectWithDefaultAsync(long companyId, long frequencyId, string entity, long? runId, string screenArea)
        {
            screenArea = string.IsNullOrEmpty(screenArea) ? "Employee" : screenArea;

            entity = !string.IsNullOrEmpty(entity) ? entity
                : screenArea == "Employee" ? nameof(EditPayslip)
                   : screenArea == "Company" ? nameof(OrganizationPositionDetailDto).TrimDto()
                        : nameof(BureauPublicHolidayDto).TrimDto();

            if (this.isPayslipTakeOn(entity))
            {
                var runs = await this.companyRunService.GetPayslipTakeOnRunsAsync(frequencyId);
                if (runId != null && !runs.Any(_ => _ == runId))
                {
                    runId = null;
                }

                if (runs.Count > 0)
                {
                    runId ??= runs.FirstOrDefault();
                }
            }
            else
            {
                runId ??= await this.companyRunService.GetCompanyRuns(frequencyId, true, false)
                    .OrderBy(r => r.PeriodStartDate)
                    .ThenBy(r => r.OrderNumber)
                    .Select(r => r.RunId)
                    .FirstOrDefaultAsync();
            }

            return this.RedirectToAction(nameof(this.Index), new { companyId, frequencyId, runId, entity, screenArea });
        }

        private async Task<bool> IsRunValidForEntityAsync(long frequencyId, long? runId, string entity)
        {
            if (this.isPayslipTakeOn(entity))
            {
                var runIds = await this.companyRunService.GetPayslipTakeOnRunsAsync(frequencyId);
                if (runIds.Count == 0 && runId == null)
                {
                    return true;
                }

                return runIds.Any(_ => _ == runId);
            }

            return runId != null;
        }

        private async Task<List<string>> GetDisabledTypesAsync(long frequencyId)
        {
            var disabled = new List<string>();

            var runs = await this.companyRunService.GetPayslipTakeOnRunsAsync(frequencyId);
            if (runs.Count == 0)
            {
                disabled.Add(nameof(EmployeePayslipTakeOn));
            }

            return disabled;
        }

        private bool ShouldRedirectToClassic(string entity, out string? screenCode)
        {
            if (entity == nameof(EmployeeActionTypeDto).TrimDto())
            {
                screenCode = "EmployeeActionTypes";
            }
            else
            {
                screenCode = null;
            }

            return screenCode is not null;
        }

        private async Task<string> GetScreenEntity(string entity, long companyId, string? customFormCategoryCode)
        {
            switch (entity)
            {
                case "CompanyRegion":
                    var query = await this.customFieldService.GetCustomFieldsAsync(companyId, nameof(OrganizationRegion), customFormCategoryCode);
                    if (query.Count > 0)
                    {
                        return "CompanyRegionHistory";
                    }

                    return entity;
                default:
                    return entity;
            }
        }

        private Task<bool> ShowEmployeeTerminationWarningAsync(long companyId, string entity)
        {
            return this.companyService.GetTaxCountryCodeAsync(companyId)
                                      .ContinueWith(_ => _.Result == Maddalena.CountryCode.GB.ToString() && entity == nameof(EmployeeTerminationDto).TrimDto());
        }

        private async Task<List<DynamicFormTemplate>> GetDynamicFormTemplates(long companyId, long frequencyId, string entity)
        {
            // DynamicFormTemplates are currently only needed for EmployeeOnboarding.
            if (entity != "EmployeeOnboarding")
            {
                return default;
            }

            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
            return await this.dynamicFormBuilderService.GetDynamicFormTemplatesAsync(companyId, frequencyId, accessToken, entity);
        }
    }
}