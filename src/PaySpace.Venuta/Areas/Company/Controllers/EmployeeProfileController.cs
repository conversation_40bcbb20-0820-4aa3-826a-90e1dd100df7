namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.ComponentModel;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Profile;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Authentication;
    using PaySpace.Venuta.Conventions;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Workflow.Activities;

    [Area("Company")]
    [DisplayName(SystemAreas.Profile.Basic)]
    public class EmployeeProfileController : EmployeeProfileBaseController
    {
        private readonly IProfileViewModelBuilder builder;
        private readonly IValidator<Employee> validator;
        private readonly IMapper mapper;
        private readonly IWorkflowActivityProcessor workflowActivityProcessor;
        private readonly IUserSessionService userSessionService;
        private readonly IManagerService managerService;
        private readonly IEmployeeProfileService employeeProfileService;
        private readonly IImageService imageService;
        private readonly IEmployeeAddressHelperService employeeAddressHelperService;
        private readonly IAddressService addressService;

        public EmployeeProfileController(
            IProfileViewModelBuilder builder,
            IValidator<Employee> validator,
            IMapper mapper,
            IWorkflowActivityProcessor workflowActivityProcessor,
            IUserSessionService userSessionService,
            IManagerService managerService,
            IEmployeeProfileService employeeProfileService,
            IImageService imageService,
            ICompanyService companyService,
            IDynamicFormBuilderService dynamicFormBuilderService,
            ICompanySettingService companySettingService,
            IEmployeeAddressHelperService addressHelperService,
            IAddressService addressService)
            : base(companyService, dynamicFormBuilderService, companySettingService)
        {
            this.builder = builder;
            this.validator = validator;
            this.mapper = mapper;
            this.workflowActivityProcessor = workflowActivityProcessor;
            this.userSessionService = userSessionService;
            this.managerService = managerService;
            this.employeeProfileService = employeeProfileService;
            this.imageService = imageService;
            this.employeeAddressHelperService = addressHelperService;
            this.addressService = addressService;
        }

        [Permission(SystemAreas.Profile.Basic, SystemAreas.Profile.Basic)]
        public async Task<IActionResult> Create(
            long companyId,
            long frequencyId,
            OnboardingTemplateType? templateType,
            string? dynamicFormId,
            string? dynamicFormName)
        {
            var vm = await this.builder.BuildCreateViewModelAsync(this.HttpContext.GetSecurityProfile(), companyId, this.User.IsInRole(UserTypeCodes.Employee));
            return await this.CreateView(vm, companyId, frequencyId, templateType, dynamicFormId, dynamicFormName);
        }

        [HttpPost]
        [CreateMessage]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(long companyId, EmployeeProfileViewModel model)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            model.IsFullAccess = profile.IsFullAccess(SystemAreas.Profile.Basic, SystemAreas.Profile.Keys.CustomFields);

            var employeeAddress = model.Address.Select(this.mapper.Map<Address>).ToList();
            employeeAddress = this.employeeAddressHelperService.UpdateEmployeeAddressEntitiesAsync(employeeAddress, this.mapper, model.EmployeeId).ToList();

            var employee = this.mapper.Map<Employee>(model);
            employee.Address = employeeAddress;
            employee.CompanyId = companyId;

            this.ModelState.Clear();

            this.addressService.Sanitize(employee.Address);
            this.addressService.SetDefaultValues(employee.Address);

            var result = await this.validator.ValidateAsync(employee, opt => opt.IncludeRuleSets(RuleSetNames.Create));
            result.AddToModelState(this.ModelState, null);

            if (this.ModelState.IsValid)
            {
                await this.employeeProfileService.AddAsync(employee, true);

                if (model.Image?.Length > 0)
                {
                    await this.imageService.StoreBlobAsync(StorageContainers.Images, "Employee", Convert.ToString(employee.EmployeeId), model.Image);
                }

                var session = await this.userSessionService.GetEmployeeUserSession(this.User, companyId, employee.EmployeeId);
                await this.managerService.SetUserSessionSettingsAsync(this.HttpContext, this.User, session);

                var nextStep = await this.workflowActivityProcessor.ProcessAddWorkflowApplicationAsync((int)WorkflowHeaderEnum.NewEmployee, this.User.GetUserId(), companyId, employee.EmployeeId);
                return this.RedirectToAction(
                    nextStep.Action,
                    nextStep.Controller,
                    new { nextStep.Area, companyId, employee.EmployeeId, session.FrequencyId });
            }

            var vm = await this.builder.BuildCreateViewModelAsync(this.HttpContext.GetSecurityProfile(), companyId, employee, this.User.IsInRole(UserTypeCodes.Employee));
            return this.View(vm);
        }
    }
}