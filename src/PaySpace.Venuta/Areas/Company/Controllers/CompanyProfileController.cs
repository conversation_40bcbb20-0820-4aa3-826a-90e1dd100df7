namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.ComponentModel;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Venuta.Areas.Company.ViewModelBuilder;
    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Authentication;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Infrastructure.Helpers;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Storage;

    [Area("Company")]
    [DisplayName(SystemAreas.Company.Area)]
    public class CompanyProfileController : Controller
    {
        private readonly ApplicationContext context;
        private readonly ICalcSchedulingService calcSchedulingService;
        private readonly ICompanyProfileViewModelBuilder builder;
        private readonly ICompanyService companyService;
        private readonly IImageService imageService;
        private readonly IMapper mapper;
        private readonly IManagerService managerService;
        private readonly IUserSessionService userSessionService;
        private readonly IValidator<Company> validator;
        private readonly IAddressService addressService;
        private readonly ICompanyDefaultsService companyDefaultsService;

        public CompanyProfileController(
            ApplicationContext context,
            ICalcSchedulingService calcSchedulingService,
            ICompanyProfileViewModelBuilder builder,
            ICompanyService companyService,
            IImageService imageService,
            IMapper mapper,
            IManagerService managerService,
            IUserSessionService userSessionService,
            IValidator<Company> validator,
            IAddressService addressService,
            ICompanyDefaultsService companyDefaultsService)
        {
            this.context = context;
            this.calcSchedulingService = calcSchedulingService;
            this.builder = builder;
            this.companyService = companyService;
            this.imageService = imageService;
            this.mapper = mapper;
            this.managerService = managerService;
            this.userSessionService = userSessionService;
            this.validator = validator;
            this.addressService = addressService;
            this.companyDefaultsService = companyDefaultsService;
        }

        public async Task<IActionResult> Index(long companyId)
        {
            var vm = await this.builder.BuildViewModelAsync(companyId, this.User);

            var profile = this.HttpContext.GetSecurityProfile();
            await this.builder.BuildViewModelDefaultsAsync(vm, companyId, this.User, profile, this.HttpContext);

            return this.View(nameof(this.Index), vm);
        }

        [UpdateMessage]
        [HttpPost]
        public async Task<IActionResult> Index(long companyId, CompanyProfileViewModel model)
        {
            if (model.CompanyId == 0 || model.CompanyId != companyId)
            {
                this.ModelState.Clear();
                return this.BadRequest();
            }

            model.Address.Add(model.PostalAddress);
            model.Address.Add(model.PhysicalAddress);
            model.UserType = Enum.Parse<UserType>(this.User.GetUserType());
            model.AgencyUrlOverride = model.AgencyUrlOverride?.Trim();

            await CompanyAddressHelper.UpdateCompanyAddressEntitiesAsync(model.Address, this, this.mapper, null);

            var company = await this.companyService.GetCompanyAsync(model.CompanyId);
            var entity = this.mapper.Map(model, company);
            this.mapper.Map(model.CompanyTheme, entity.CompanyTheme);

            await this.companyService.SetupCompanyGroupAndModulesAsync(entity, model.CompanyGroupName, model.IncludeAnalytics, model.PaymentModuleId, model.UserType);

            // We need to clear the model state because the Address is being validated before the AddressHelper is run.
            this.ModelState.Keys
                .Where(k => k.Contains("address", StringComparison.OrdinalIgnoreCase))
                .ToList()
                .ForEach(k => this.ModelState.Remove(k));

            this.addressService.Sanitize(entity.Address);
            this.addressService.SetDefaultValues(entity.Address);

            var validateResult = await this.validator.ValidateAsync(entity, opt => opt.IncludeRuleSets(RuleSetNames.Update));
            validateResult.AddToModelState(this.ModelState, null);

            this.companyDefaultsService.DefaultCompanyValues(company);

            if (!this.ModelState.IsValid)
            {
                var profile = this.HttpContext.GetSecurityProfile();
                await this.builder.BuildViewModelDefaultsAsync(model, model.CompanyId, this.User, profile, this.HttpContext);

                return this.View(model);
            }

            if (model.CompanyLogo?.Length > 0)
            {
                await this.imageService.StoreBlobAsync(StorageContainers.Images, "Company", Convert.ToString(model.CompanyId), model.CompanyLogo);
            }

            if (model.CompanyBackground?.Length > 0)
            {
                await this.imageService.StoreBlobAsync(StorageContainers.CompanyTheme, Convert.ToString(model.CompanyId), "login_bg.jpg", model.CompanyBackground);
            }

            if (model.UifSignature?.Length > 0)
            {
                await this.imageService.StoreBlobAsync(StorageContainers.Images, "Company/Signature", Convert.ToString(companyId), model.UifSignature);
            }

            await this.companyService.UpdateAsync(entity);
            await this.calcSchedulingService.RecalculateCompany(companyId);

            var isAgencyModified = this.context.IsFieldModified(entity, nameof(entity.AgencyId));
            if (isAgencyModified)
            {
                var session = await this.userSessionService.GetCompanyUserSession(this.User, companyId);
                session.AgencyId = entity.CompanyId;
                await this.managerService.SetUserSessionSettingsAsync(this.HttpContext, this.User, session);
            }

            return this.RedirectToAction(nameof(this.Index));
        }
    }
}