namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System;
    using System.ComponentModel;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Areas.Company.ViewModels.CompanyGL;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Results;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Reports;

    [Area("Company")]
    [DisplayName(SystemAreas.CompanyGLDetail.Area)]
    public class CompanyGLController : Controller
    {
        private const string ExcelMimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

        private readonly IReportsMessageService reportsMessageService;
        private readonly IClassicReportService classicReportService;
        private readonly ICompanyService companyService;
        private readonly ICompanyGlService companyGlService;

        public CompanyGLController(
            IReportsMessageService reportsMessageService,
            IClassicReportService classicReportService,
            ICompanyService companyService,
            ICompanyGlService companyGlService)
        {
            this.reportsMessageService = reportsMessageService;
            this.classicReportService = classicReportService;
            this.companyService = companyService;
            this.companyGlService = companyGlService;
        }

        public IActionResult Index(long companyId, long frequencyId)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.CompanyGL.Area, SystemAreas.CompanyGL.Area);

            var formModel = new CompanyGLGridViewModel
            {
                AllowEdit = allowEdit,
                CompanyId = companyId,
                FrequencyId = frequencyId,
                EditUrl = this.Url.Action(nameof(this.Edit)),
                ReportUrl = this.Url.Action(nameof(this.DownloadReport))
            };

            return this.View(formModel);
        }

        public IActionResult Edit(long companyId, long frequencyId, long? id = null, long? cloneGlId = null)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.CompanyGL.Area, SystemAreas.CompanyGL.Area);

            if (id == null)
            {
                var addFormModel = new CompanyGLCreateViewModel
                {
                    AllowEdit = allowEdit,
                    CompanyId = companyId,
                    FrequencyId = frequencyId,
                    CloneGLId = cloneGlId,
                    BaseUrl = this.Url.Action(nameof(this.Index)),
                    ComponentsUrl = this.GetComponentsUrl(companyId, frequencyId, cloneGlId),
                };

                return this.View("Create", addFormModel);
            }

            var editFormModel = new CompanyGLEditViewModel
            {
                AllowEdit = allowEdit,
                CompanyId = companyId,
                FrequencyId = frequencyId,
                CompanyGlId = id.Value,
                AddUrl = this.Url.Action(nameof(this.Edit), new { id = "" }),
                ComponentsUrl = this.GetComponentsUrl(companyId, frequencyId, id.Value),
                BaseUrl = this.Url.Action(nameof(this.Index)),
            };

            return this.View("Edit", editFormModel);
        }

        /// <summary>
        /// Downloads the General Ledger Parameters report for a specific company and frequency.
        /// If a specific GL Id is provided, it generates a report for that GL-
        /// otherwise, it generates a report for all GLs under the company.
        /// </summary>
        public async Task<IActionResult> DownloadReport(
            long companyId,
            long frequencyId,
            long? generalLedgerId = null)
        {
            // Build report hash for notification
            var userId = this.User.GetUserId();
            var reportHash = ExportReportMessage.GetHash(userId);

            // Notify user about the report generation
            const string ReportTitle = "General Ledger Parameters";
            await this.reportsMessageService.PublishNotificationMessage(userId, reportHash, ReportTitle);

            var runPeriod = await this.companyGlService.GetRunPeriodAsync(frequencyId) ??
                throw new InvalidOperationException("No valid run period found for the specified frequency.");

            // Choose the correct export path
            (var reportBytes, var filePrefix) = generalLedgerId is null
                ? await this.GenerateCompanyExportAsync(companyId, runPeriod)
                : await this.GenerateSingleGlExportAsync(companyId, frequencyId, generalLedgerId.Value, runPeriod);

            if (reportBytes == null || reportBytes.Length == 0)
            {
                return this.BadRequest("Failed to generate the report, or the report is empty.");
            }

            var fileName = $"{filePrefix} GL Export.xlsx";

            return new NoCacheFileResult(reportBytes, ExcelMimeType, fileName);
        }

        private async Task<(byte[] bytes, string filePrefix)> GenerateCompanyExportAsync(
            long companyId,
            CompanyRunPeriodResult runPeriod)
        {
            var bytes = await this.classicReportService.ExportCompanyGlReportAsync(
                companyId,
                runPeriod.PeriodEndDate,
                companyGLID: null);

            var companyName = await this.companyService.GetCompanyNameAsync(companyId);
            return (bytes, companyName);
        }

        private async Task<(byte[] bytes, string filePrefix)> GenerateSingleGlExportAsync(
            long companyId,
            long frequencyId,
            long generalLedgerId,
            CompanyRunPeriodResult runPeriod)
        {
            // Ensure the GL belongs to the requested company & frequency and grab its name
            var glName = await this.companyGlService.GetCompanyGLNameAsync(
                generalLedgerId,
                frequencyId) ??
                throw new InvalidOperationException("The specified General Ledger does not exist for the given company and frequency.");

            var bytes = await this.classicReportService.ExportCompanyGlReportAsync(
                companyId,
                runPeriod.PeriodEndDate,
                generalLedgerId);

            return (bytes, glName);
        }

        /// <summary>
        /// Generates the URL to retrieve component data based on the operation context.
        /// - For creating a new CompanyGL: returns initial available components.
        /// - For editing an existing CompanyGL: returns components already linked to the given GL ID.
        /// - For cloning: uses the GL ID of the source to retrieve components for cloning.
        /// </summary>
        /// <returns>A relative URL to fetch the appropriate list of components.</returns>
        private string GetComponentsUrl(long companyId, long frequencyId, long? glId)
        {
            // When glId is null: create mode – return initial available components.
            // When glId is provided: edit or clone mode – return linked components for the given GL.
            return (glId == null)
                ? this.Url.Action(
                    "GetInitialComponents", "CompanyGLApi",
                    new
                    {
                        CompanyId = companyId,
                        FrequencyId = frequencyId,
                        PayslipAction = PayslipAction.Allowance
                    })
                : this.Url.Action(
                    "GetCompanyGLDetails", "CompanyGLApi",
                    new
                    {
                        CompanyId = companyId,
                        FrequencyId = frequencyId,
                        Id = glId,
                        PayslipAction = PayslipAction.Allowance
                    });
        }
    }
}
