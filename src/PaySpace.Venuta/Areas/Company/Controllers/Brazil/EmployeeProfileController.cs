namespace PaySpace.Venuta.Areas.Company.Controllers.Brazil
{
    using System;
    using System.ComponentModel;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Authentication;
    using PaySpace.Venuta.Conventions;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Workflow.Activities;

    [Area("Company")]
    [DisplayName(SystemAreas.Profile.Basic)]
    public class EmployeeProfileController : EmployeeProfileBaseController
    {
        private readonly IHistoryProfileViewModelBuilder builder;
        private readonly IValidator<Employee> validator;
        private readonly IMapper mapper;
        private readonly IWorkflowActivityProcessor workflowActivityProcessor;
        private readonly IUserSessionService userSessionService;
        private readonly IManagerService managerService;
        private readonly IEmployeeHistoryService employeeHistoryService;
        private readonly IImageService imageService;
        private readonly IEmployeeAddressHelperService employeeAddressHelperService;

        public EmployeeProfileController(
            IHistoryProfileViewModelBuilder builder,
            IValidator<Employee> validator,
            IMapper mapper,
            IWorkflowActivityProcessor workflowActivityProcessor,
            IUserSessionService userSessionService,
            IManagerService managerService,
            IEmployeeHistoryService employeeHistoryService,
            IImageService imageService,
            ICompanyService companyService,
            IDynamicFormBuilderService dynamicFormBuilderService,
            ICompanySettingService companySettingService,
            IEmployeeAddressHelperService addressHelperService)
            : base(companyService, dynamicFormBuilderService, companySettingService)
        {
            this.builder = builder;
            this.validator = validator;
            this.mapper = mapper;
            this.workflowActivityProcessor = workflowActivityProcessor;
            this.userSessionService = userSessionService;
            this.managerService = managerService;
            this.employeeHistoryService = employeeHistoryService;
            this.imageService = imageService;
            this.employeeAddressHelperService = addressHelperService;
        }

        [EmployeeHistory]
        [Permission(SystemAreas.Profile.Basic, SystemAreas.Profile.Basic)]
        public async Task<IActionResult> Create(
            long companyId,
            long frequencyId,
            OnboardingTemplateType? templateType,
            string? dynamicFormId,
            string? dynamicFormName)
        {
            var vm = await this.builder.BuildCreateViewModel(this.HttpContext.GetSecurityProfile(), companyId, this.User.IsInRole(UserTypeCodes.Employee));
            return await this.CreateView(vm, companyId, frequencyId, templateType, dynamicFormId, dynamicFormName);
        }

        [EmployeeHistory]
        [HttpPost]
        [CreateMessage]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(long companyId, EmployeeProfileViewModel model)
        {
            var employeeAddress = model.Address.Select(this.mapper.Map<Address>).ToList();
            employeeAddress = this.employeeAddressHelperService.UpdateEmployeeAddressEntitiesAsync(employeeAddress, this.mapper, model.EmployeeId).ToList();

            var employee = this.mapper.Map<Employee>(model);
            employee.Address = employeeAddress;
            employee.CompanyId = companyId;

            this.ModelState.Clear();

            var result = await this.validator.ValidateAsync(employee, opt => opt.IncludeRuleSets(RuleSetNames.Create));
            result.AddToModelState(this.ModelState, null);

            if (this.ModelState.IsValid)
            {
                await this.employeeHistoryService.AddAsync(employee, true);

                if (model.Image?.Length > 0)
                {
                    await this.imageService.StoreBlobAsync(StorageContainers.Images, "Employee", Convert.ToString(employee.EmployeeId), model.Image);
                }

                var session = await this.userSessionService.GetEmployeeUserSession(this.User, companyId, employee.EmployeeId);
                await this.managerService.SetUserSessionSettingsAsync(this.HttpContext, this.User, session);

                var nextStep = await this.workflowActivityProcessor.ProcessAddWorkflowApplicationAsync((int)WorkflowHeaderEnum.NewEmployee, this.User.GetUserId(), companyId, employee.EmployeeId);
                return this.RedirectToAction(
                    nextStep.Action,
                    nextStep.Controller,
                    new { nextStep.Area, companyId, employee.EmployeeId, session.FrequencyId });
            }

            var vm = await this.builder.BuildCreateViewModel(this.HttpContext.GetSecurityProfile(), companyId, employee, this.User.IsInRole(UserTypeCodes.Employee));
            return this.View(vm);
        }
    }
}