namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Agency;
    using PaySpace.Venuta.Data.Models.Security;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Users.Abstractions;
    using PaySpace.Venuta.Modules.Users.Abstractions.Models;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.ViewModels.UserProfile;

    [Area("Company")]
    [DisplayName(SystemAreas.UserProfiles.Area)]
    public class CompanyUserProfileController : Controller
    {
        private readonly IAgencyService agencyService;
        private readonly ICompanyService companyService;
        private readonly ICompanySettingService companySettingService;
        private readonly ICompanyPaymentModuleService companyPaymentModuleService;
        private readonly IMapper mapper;
        private readonly IUserProfileService userProfileService;
        private readonly IAuthorizationService authorizationService;

        public CompanyUserProfileController(
            IAgencyService agencyService,
            ICompanyService companyService,
            ICompanySettingService companySettingService,
            ICompanyPaymentModuleService companyPaymentModuleService,
            IMapper mapper,
            IUserProfileService userProfileService,
            IAuthorizationService authorizationService)
        {
            this.agencyService = agencyService;
            this.companyService = companyService;
            this.companySettingService = companySettingService;
            this.companyPaymentModuleService = companyPaymentModuleService;
            this.mapper = mapper;
            this.userProfileService = userProfileService;
            this.authorizationService = authorizationService;
        }

        public async Task<IActionResult> Index(long companyId)
        {
            var profile = this.HttpContext.GetSecurityProfile();

            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var featureFlags = await this.GetFlagsAsync(companyId, agencyId);

            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            var completedContactTypes = await this.userProfileService.GetUserContactTypesForCompanyGroupAsync(companyGroupId);

            return this.View("UserProfile/Index", new UserProfileViewModel
            {
                ApiUrl = this.Url.Action("Get", "CompanyUserProfileApi"),
                DataUrl = this.Url.Action("GetSubAgencyDetails", "CompanyUserProfileApi"),
                SelectedContactTypeUrl = this.Url.Action("GetSelectedContactTypes", "CompanyUserProfileApi"),
                ReplicateUserUrl = this.Url.Action("ReplicateUser", "Admin", new { area = string.Empty }),
                UserTypeId = (int)UserType.Company,
                AllowEdit = profile.IsFullAccess(SystemAreas.UserProfiles.Area, SystemAreas.UserProfiles.Area),
                Flags = JsonConvert.SerializeObject(featureFlags),
                CompletedContactTypes = JsonConvert.SerializeObject(completedContactTypes),
                IsBureauUser = this.User.IsInRole(UserTypeCodes.Bureau),
                CanReplicateUsers = await this.authorizationService.AuthorizeAsync(this.User, Policies.Replicate)
                                                                   .ContinueWith(_ => _.Result.Succeeded),
                ShowPasswordField = await this.agencyService.GetPasswordSettingAsync(agencyId) ?? false,
                EditUrl = this.Url.Action(nameof(this.Edit))
            });
        }

        public async Task<IActionResult> Edit(long companyId, long? userId)
        {
            var user = new UserResult
            {
                UserStatus = UserStatus.Active,
                UserType = UserType.Company,
            };

            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);

            if (userId > 0)
            {
                var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);

                var userProfileAgencyId = agencyId;

                if ((this.User.IsInRole(Roles.TopLevel) || this.User.IsInRole(UserTypeCodes.Bureau)) && userProfileAgencyId == PaySpaceConstants.PaySpaceAgencyId)
                {
                    userProfileAgencyId = default;
                }

                var userProfiles = this.userProfileService.GetUsers(companyGroupId, companyId, userProfileAgencyId, null, null, null, null);

                user = await userProfiles.ProjectTo<UserResult>(this.mapper.ConfigurationProvider)
                    .SingleOrDefaultAsync(_ => _.UserId == userId);

                if (user == null)
                {
                    return this.NotFound();
                }
            }

            var profile = this.HttpContext.GetSecurityProfile();
            var featureFlags = await this.GetFlagsAsync(companyId, agencyId);

            return this.View("UserProfile/Edit", new UserProfileEditViewModel
            {
                User = user,
                ApiUrl = this.Url.Action("Get", "CompanyUserProfileApi"),
                DataUrl = this.Url.Action("GetSubAgencyDetails", "CompanyUserProfileApi"),
                BaseUrl = this.Url.Action(nameof(this.Index)),
                UserTypeId = (int)user.UserType,
                AllowEdit = profile.IsFullAccess(SystemAreas.UserProfiles.Area, SystemAreas.UserProfiles.Area),
                Flags = JsonConvert.SerializeObject(featureFlags),
                IsBureauUser = this.User.IsInRole(UserTypeCodes.Bureau),
                IsBureauScreen = false,
                ShowPasswordField = await this.agencyService.GetPasswordSettingAsync(agencyId) ?? false,
                IsNew = !userId.HasValue,
                UserId = userId
            });
        }

        private async Task<List<string>> GetFlagsAsync(long companyId, long agencyId)
        {
            var featureFlags = new List<string>();
            if (await this.companySettingService.IsActiveAsync(companyId, "JOBMod"))
            {
                featureFlags.Add("isBudgetUser");
                if (await this.companySettingService.IsActiveAsync(companyId, "LOCKDOWN"))
                {
                    featureFlags.Add("overrideLockDown");
                }
            }

            if (this.User.IsInRole(UserTypeCodes.Bureau))
            {
                featureFlags.Add("allowBureauSettings");
                if (await this.companyPaymentModuleService.HasPowerBiModuleAsync(companyId))
                {
                    featureFlags.Add("isPowerBIUser");
                }
            }

            if (this.User.IsInRole(Roles.TopLevel))
            {
                featureFlags.Add("isAgencyTopLevelUser");
            }

            if (await this.userProfileService.IsContactTypeDisabled(agencyId))
            {
                featureFlags.Add("disableContactTypes");
            }

            if (await this.agencyService.AllowUserAgencySubInvoiceDetailsAsync(agencyId))
            {
                featureFlags.Add("agencySubInvoiceDetails");
            }

            if (agencyId is not PaySpaceConstants.PaySpaceAgencyId)
            {
                featureFlags.Add("allowAgency");
            }

            return featureFlags;
        }
    }
}