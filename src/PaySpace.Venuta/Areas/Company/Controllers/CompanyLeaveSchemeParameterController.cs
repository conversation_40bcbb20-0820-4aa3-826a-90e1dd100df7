namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanyLeaveSchemes.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;

    [Area("Company")]
    [DisplayName(SystemAreas.CompanyLeaveSchemeParameter.Area)]
    public class CompanyLeaveSchemeParameterController : Controller
    {
        private readonly ICompanyLeaveSchemeParameterService leaveSchemeParameterService;
        private readonly ICompanyService companyService;

        public CompanyLeaveSchemeParameterController(ICompanyService companyService, ICompanyLeaveSchemeParameterService leaveSchemeParameterService)
        {
            this.companyService = companyService;
            this.leaveSchemeParameterService = leaveSchemeParameterService;
        }

        public async Task<IActionResult> Index()
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var companyId = this.HttpContext.GetCompanyId();
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId!.Value);
            var gradeSettings = await this.leaveSchemeParameterService.GetRelevantGradeSettingsAsync(companyId.Value);

            var vm = new CompanyLeaveSchemeParameterViewModel
            {
                CountryId = taxCountryId,
                CompanyId = companyId!.Value,
                IsGradeBasedAccrualSettingEnabled = gradeSettings.isGradeBasedAccrualEnabled,
                IsGradeBasedMaxBalanceSettingEnabled = gradeSettings.isGradeBasedMaxBalanceEnabled,
                AllowEdit = profile.IsFullAccess(SystemAreas.CompanyLeaveSchemeParameter.Area, SystemAreas.CompanyLeaveSchemeParameter.Area),
            };

            return this.View(vm);
        }
    }
}