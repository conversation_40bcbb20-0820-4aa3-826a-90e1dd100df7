namespace PaySpace.Venuta.Areas.Company.Controllers
{
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Areas.Company.ViewModels;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.DynamicFormBuilder;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    [Area("Company")]
    [DisplayName(SystemAreas.EOnboarding.Area)]
    public class EmployeeEOnboardingDetailsController : Controller
    {
        private readonly ICompanyService companyService;
        private readonly IDynamicFormBuilderService dynamicFormBuilderService;

        public EmployeeEOnboardingDetailsController(ICompanyService companyService, IDynamicFormBuilderService dynamicFormBuilderService)
        {
            this.companyService = companyService;
            this.dynamicFormBuilderService = dynamicFormBuilderService;
        }

        public async Task<IActionResult> Index(long companyId, long frequencyId)
        {
            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
            var availableDynamicFormsContainer = await this.dynamicFormBuilderService.GetAvailableDynamicFormForModuleAsync(companyId, frequencyId, accessToken, DynamicFormBuilderConstants.ModuleType.EOnboarding);
            var hasDynamicFormTemplate = availableDynamicFormsContainer.DynamicFormBuilderModels is not null && availableDynamicFormsContainer.DynamicFormBuilderModels.Count > 0;

            var isEOnboardingTemplateActive = hasDynamicFormTemplate &&
                availableDynamicFormsContainer.ContextLevel == DynamicFormBuilderContextLevel.Company;

            return this.View(new EOnboardingDetailsViewModel()
            {
                IsEOnboardingTemplateActive = isEOnboardingTemplateActive,
                DynamicFormIndexUrl = this.Url.Action("Index", "CompanyDynamicFormBuilder"),
                GenerateEmployeeNumber = await this.companyService.ShouldGenerateEmployeeNumberAsync(companyId)
            });
        }
    }
}
