@using PaySpace.Venuta.Areas.Company.ViewModels.CompanyGL
@model CompanyGLGridViewModel

@{
    ViewBag.Title = @Localizer.GetString(SystemAreas.CompanyGLDetail.Keys.GridPageHeader);
}

@section scripts {
    <script type="module" src="~/pages/company-gl-grid.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.CompanyGLDetail.Keys.GridPageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <card>
        <company-gl-grid allow-edit="@Model.AllowEdit"
                         company-id="@Model.CompanyId"
                         frequency-id="@Model.FrequencyId"
                         report-url="@Model.ReportUrl"
                         edit-url-base="@Model.EditUrl">
        </company-gl-grid>
    </card>
</page-content>