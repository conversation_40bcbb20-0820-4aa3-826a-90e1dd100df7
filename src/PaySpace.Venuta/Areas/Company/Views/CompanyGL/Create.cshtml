@using PaySpace.Venuta.Areas.Company.ViewModels.CompanyGL
@model CompanyGLCreateViewModel

@{
    ViewBag.Title = @Localizer.GetString(SystemAreas.CompanyGLDetail.Keys.CreatePageHeader);
    var isClone = Model.CloneGLId.HasValue;
}

@section scripts {
    <script type="module" src="~/pages/@(isClone ? "company-gl-clone.js" : "company-gl-add.js")" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.CompanyGLDetail.Keys.CreatePageHeader)">
    </page-header-toolbar>
</page-header>

<page-content>
    @if (isClone)
    {
        <company-gl-clone allow-edit="@Model.AllowEdit"
                          company-id="@Model.CompanyId"
                          frequency-id="@Model.FrequencyId"
                          clone-id="@Model.CloneGLId"
                          components-url="@Model.ComponentsUrl"
                          base-url="@Model.BaseUrl">
        </company-gl-clone>
    }
    else
    {
        <company-gl-add allow-edit="@Model.AllowEdit"
                        company-id="@Model.CompanyId"
                        frequency-id="@Model.FrequencyId"
                        components-url="@Model.ComponentsUrl"
                        base-url="@Model.BaseUrl">
        </company-gl-add>
    }
</page-content>
