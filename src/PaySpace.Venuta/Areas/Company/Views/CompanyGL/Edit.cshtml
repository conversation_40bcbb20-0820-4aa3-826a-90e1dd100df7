@using PaySpace.Venuta.Areas.Company.ViewModels.CompanyGL
@model CompanyGLEditViewModel

@{
    ViewBag.Title = @Localizer.GetString(SystemAreas.CompanyGLDetail.Keys.EditPageHeader);
}

@section scripts {
    <script type="module" src="~/pages/company-gl-form.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.CompanyGLDetail.Keys.EditPageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <company-gl-form allow-edit="@Model.AllowEdit"
                     company-id="@Model.CompanyId"
                     frequency-id="@Model.FrequencyId"
                     company-gl-id="@Model.CompanyGlId"
                     components-url="@Model.ComponentsUrl"
                     base-url="@Model.BaseUrl">
    </company-gl-form>
</page-content>