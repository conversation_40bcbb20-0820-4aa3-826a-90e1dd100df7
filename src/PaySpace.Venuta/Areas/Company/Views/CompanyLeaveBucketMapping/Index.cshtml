@model CompanyLeaveBucketMappingViewModel
@{
    ViewBag.Title = @Localizer.GetString(SystemAreas.CompanyLeaveBucketMapping.Keys.PageHeader);
}
@section scripts {
    <script type="module" src="~/pages/company-leave-bucket-mapping.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.CompanyLeaveBucketMapping.Keys.PageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <company-leave-bucket-mapping edit-url="@Model.EditUrl"></company-leave-bucket-mapping>
</page-content>