@model CompanyComponentViewModel
@{
    ViewBag.Title = @Localizer.GetString("lblPageHeader");
}

@section scripts {
    <script type="module" src="~/pages/company-components.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString("lblPageHeader")">
        <page-header-toolbar-content>
            <a data-ajax-method="post"
               data-ajax="true"
               data-ajax-url="@Url.Action("DownloadComponentTaxabilityReport", "PayrollComponentApi")"
               class="btn btn-outline-primary"
               title="@Localizer.GetString("lblDownloadReport")"
               asp-visible="@Model.ShowReportButton">
                <i class="far fa-file-excel"></i>
            </a>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <company-components default-run-id="@Model.RunId"
                        country-id="@Model.CountryId"
                        allow-edit="@Model.AllowEdit"
                        show-calc-exceptions="@Model.ShowCalcExceptions"
                        show-taxability-option="@Model.ShowTaxabilityOption"
                        payslip-actions="@Model.PayslipActions" />
</page-content>