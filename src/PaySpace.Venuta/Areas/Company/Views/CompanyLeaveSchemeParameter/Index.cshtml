@model CompanyLeaveSchemeParameterViewModel
@{
    ViewBag.Title = @Localizer.GetString(SystemAreas.CompanyLeaveSchemeParameter.Keys.PageHeader);
}

@section scripts {
    <script type="module" src="~/pages/company-leave-scheme-parameters.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.CompanyLeaveSchemeParameter.Keys.PageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail/>
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <card>
        <company-leave-scheme-parameters
            company-id="@Model.CompanyId"
            allow-edit="@Model.AllowEdit"
            country-id="@Model.CountryId"
            api-url="@Url.Action("Get", "CompanyLeaveSchemeParameterApi")"
            is-grade-based-accrual-enabled="@Model.IsGradeBasedAccrualSettingEnabled"
            is-grade-based-max-balance-enabled="@Model.IsGradeBasedMaxBalanceSettingEnabled">
        </company-leave-scheme-parameters>
    </card>
</page-content>