@using PaySpace.Venuta.Infrastructure.Extensions
@using PaySpace.Venuta.Services.Abstractions.Models
@using PaySpace.Venuta.TagHelpers
@using Newtonsoft.Json
@using Microsoft.AspNetCore.Mvc.TagHelpers

@model TableBuilderViewModel

@section scripts {
    <script src="~/js/pages/custom-forms.js" asp-append-version="true"></script>
    <script src="~/js/pages/company/effective-date-custom-fields.js" asp-append-version="true"></script>
    <script src="~/js/pages/company/table-builder/table-builder-custom-fields.js" asp-append-version="true"></script>
    <script src="~/js/pages/company/table-builder/table-builder.js" asp-append-version="true"></script>
    <script type="text/javascript">
            EffectiveDateCustomFields.App = CompanyTableBuilder.CustomFieldsApp;
    </script>
}

<script>
    EffectiveDateCustomFields = {
        Settings: {
            EntityGridLink: "#grid-table-builder",
            HistoryDeleteUrl: "@Url.Action("DeleteByEffectiveDate", "TableBuilderApi")",
            HistoryLoadUrl: "@Url.Action("GetHistory", "TableBuilderApi")"
        }
    };

    CompanyTableBuilder = {
        Settings: {
            CategoryId: @Model.CategoryId,
            Level: "@Model.TableBuilderLevel.ToString()",
            AllowEdit: @Html.Raw(JsonConvert.SerializeObject(Model.AllowEdit)),
            StabilityUrl: "@(Model.CanEditStability ? Url.Action("GetStabilities", "TableBuilderApi", new { Model.CategoryId }) : null)",
            StabilityRulesEditUrl: "@(Model.IsBureauUser ? Url.Action("Index", "StabilityRules", new { Area = "Bureau" }) : string.Empty)"
        }
    };
</script>

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.CompanyTableBuilder.Keys.PageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail/>
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <card>
        <div class="mb-10" asp-visible="@Model.IsAgencyUser">
            @(
            Html.DevExtreme()
                .Form<TableBuilderViewModel>()
                .FormData(Model)
                .ColCount(4)
                .OnFieldDataChanged(
                    @<script>
                        function (e) {
                            window.location = @Html.Url("Index", new { categoryId = Model.CategoryId.ToString(), level = new JS($"e.value") });
                        }
                    </script>)
                .Items(
                    i =>
                    {
                        i.AddSimple()
                            .DataField("tableBuilderLevel")
                            .Visible(Model.IsAgencyUser)
                            .Label(l => l.Text(Localizer.GetString("lblTableBuilderLevel")))
                            .EnumFor<TableBuilderLevel>();
                    }))
        </div>
        <div class="row" style="min-height: 500px">
            <ul class="vertical-tabs nav" role="tablist">
                @(
                Html.DevExtreme()
                    .List()
                    .DataSource(Model.Categories, "CategoryId")
                    .SelectionMode(ListSelectionMode.Single)
                    .DisplayExpr("name")
                    .OnContentReady(
                        @<text>
                                <script>
                                function(e) {
                                    if (e.component.option('items').length > 0) {
                                        $('#nav-link-' + '@Model.CategoryId').addClass('active');
                                    }
                                }
                            </script>
                            </text>)
                    .ItemTemplate(
                        @<text>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link" id="nav-link-<%= categoryId %>" data-bs-toggle="tab" href="javascript:void(0)" role="tab">
                                        <%- name %>
                                    </a>
                                </li>
                            </text>)
                    .OnItemClick(
                        @<text>
                                <script>
                                function(e) {
                                    window.location = @Html.Url("Index", new { categoryId = new JS("e.itemData.categoryId"), level = Model.TableBuilderLevel })
                                }
                            </script>
                            </text>)
                    .NoDataText(""))
            </ul>
            <div class="col">
                @if (Model.ShowPensionWarning)
                {
                    <alert-message type="secondary">
                        @Localizer.GetString("lblPensionDefaultWarning")
                    </alert-message>
                }

                @(
                Html.DevExtreme()
                    .DataGrid<TableBuilderResult>()
                    .ID("grid-table-builder")
                    .DataSource(
                        d => d.WebApi()
                            .Controller("TableBuilderApi")
                            .Key("id")
                            .LoadParams(new { Model.CategoryId, level = Model.TableBuilderLevel })
                            .LoadAction("Get")
                            .InsertAction("Post")
                            .UpdateAction("Put")
                            .DeleteAction("Delete")
                            .OnBeforeSend("CompanyTableBuilder.App.OnBeforeSend"))
                    .Editing(
                        editing =>
                        {
                            editing.Mode(GridEditMode.Form);
                            editing.AllowAdding(Model.AllowEdit);
                            editing.AllowUpdating(Model.AllowEdit);
                            editing.AllowDeleting(Model.AllowDelete);
                            editing.Form(_ =>
                            {
                                _.OnContentReady("CompanyTableBuilder.App.OnContentReady");
                            });
                        })
                    .RepaintChangesOnly(true)
                    .OnEditorPreparing("CompanyTableBuilder.App.OnEditorPreparing")
                    .OnRowUpdating("CompanyTableBuilder.App.OnRowUpdating")
                    .Option("defaultCustomFields", Model.DefaultCustomFields)
                    .Columns(
                        columns =>
                        {
                            columns.AddForJson(a => a.Name)
                                .SetCellValue("CompanyTableBuilder.CustomFieldsApp.NameSetCellValue")
                                .CellTemplate(
                                    @<text>
                                            <div>
                                                <%= value %>
                                                <% if (data.isDefault) { %>
                                                <span class="badge bg-light border text-dark ms-1">@Localizer.GetString(SystemAreas.CompanyTableBuilder.Keys.IsDefault)</span>
                                                <% } %>
                                            </div>
                                        </text>);
                            columns.AddForJson(a => a.IsDefault).Visible(false).FormItem(_ => _.Label(l => l.Visible(false)).Editor(editor => editor.CheckBox().Text(Localizer.GetString(SystemAreas.CompanyTableBuilder.Keys.SetAsDefault))));
                            columns.AddForJson(a => a.Code).SetCellValue("CompanyTableBuilder.CustomFieldsApp.CodeSetCellValue");
                            columns.Add()
                                .Caption(@Localizer.GetString("lblEffectiveDate"))
                                .Visible(false)
                                .DataField("effectiveDate")
                                .DataType(GridColumnDataType.Date)
                                .FormItem(fi => fi.Editor(e => e.DateBox().ReadOnly(Model.ReadOnly).ShowClearButton(false).DateSerializationFormat("yyyy-mm-dd"))
                                    .IsRequired(!Model.ReadOnly)
                                    .Visible(Model.Visible))
                                .SetCellValue($"function(newData, value, currentRowData) {{ EffectiveDateCustomFields.App.EffectiveDateSetCellValue(newData, value, currentRowData, '#grid-table-builder') }}");
                            columns.Add()
                                .Type(GridCommandColumnType.Buttons)
                                .Buttons(
                                    buttons =>
                                    {
                                        buttons.Add()
                                            .Visible(Model.CanEditStability)
                                            .Icon("fas fa-cog")
                                            .Hint(@Localizer.GetString(SystemAreas.CompanyTableBuilder.Keys.StabilityTooltip))
                                            .OnClick("CompanyTableBuilder.App.OnStabilityEdit");
                                        buttons.Add()
                                            .Icon("fas fa-eye")
                                            .Visible(!Model.AllowEdit)
                                            .OnClick(
                                                @<script>
                                                    function(e) {
                                                        e.component.editRow(e.row.rowIndex);
                                                    }
                                                </script>);
                                        buttons.Add().Name(GridColumnButtonName.Edit);
                                        buttons.Add().Name(GridColumnButtonName.Delete);
                                    });
                            columns.AddForJson(a => a.CustomFields)
                                .AddCustomFieldTemplate(typeof(TableBuilder), null, null, Model.CategoryId, true);
                            columns.Add()
                                .Visible(false)
                                .FormItem(
                                    fi =>
                                    {
                                        fi.ColSpan(2);
                                        fi.Label(l => l.ShowColon(false).Text(" "));
                                        fi.Template(new TemplateName("template-history"));
                                    });
                        })
                )
            </div>
        </div>
    </card>
    <div id="popup"></div>
</page-content>

@await Html.PartialAsync("_TemplateHistory", Model)