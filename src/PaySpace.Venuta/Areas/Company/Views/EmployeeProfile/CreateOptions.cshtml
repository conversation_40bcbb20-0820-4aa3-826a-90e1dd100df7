@using PaySpace.Venuta.Areas.Company.Controllers;
@model ProfileViewModel
@{
    ViewBag.Title = Localizer.GetString("AddNewEmployeePageHeader");
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString("AddNewEmployeePageHeader")"/>
</page-header>

<page-content>
    <div class="row g-2">
        @if (!Model.ExcludesAdvancedWorkflowFromQuickOnboarding)
        {
            <a asp-action="Create" asp-controller="EmployeeProfile"
                asp-route-companyId="@Model.CompanyId"
                asp-route-templateType="@OnboardingTemplateType.Advanced"
                class="col-md-3 col-lg-6">
                <div class="card">
                    <div class="card-body">
                        Advanced Workflow
                    </div>
                </div>
            </a>
        }

        @foreach (var dynamicForm in Model.DynamicForms)
        {
            <a asp-action="Create" asp-controller="EmployeeProfile"
                asp-route-dynamicFormName="@($"{Localizer.GetString("AddNewEmployeePageHeader")} - {dynamicForm.FormName}")"
                asp-route-dynamicFormId=@dynamicForm.DynamicFormId
                asp-route-companyId="@Model.CompanyId"
                asp-route-templateType="@OnboardingTemplateType.Basic"
                class="col-md-3 col-lg-6">
                <div class="card">
                    <div class="card-body">
                        @dynamicForm.FormName
                    </div>
                </div>
            </a>
        }
    </div>
</page-content>