@model ProfileViewModel
@{
    ViewBag.Title = Localizer.GetString("lblPageHeader");
}

@section scripts {
    <script>
        $("#form-employee-profile").submit(function () {
            var validationResult = $(this).dxValid();
            if (validationResult.isValid) {
                var img = $("#@Html.IdFor(_ => _.Employee.Image)");
                if (img.val() === "") {
                    // An image was not uploaded.
                    img.remove();
                }
            }
        });
    </script>
}

<script>
    AddEmployeeProfile = {
        Urls: {
            GetProvinces: "@Url.Action("GetProvinces", "Lookup", new { area = string.Empty })",
            GetStandardIndustryCodeSubs: "@Url.Action("GetStandardIndustryCodeSubs", "Lookup", new { area = string.Empty })",
            municipality: "@Url.Action("GetMunicipality", "Lookup", new { area = string.Empty })",
            addressStreetType: "@Url.Action("GetAddressStreetType", "Lookup", new { area = string.Empty })"
        },
        Settings: {
            SendEssRegistrationEmail: @Json.Serialize(Model.EmployeeProfileSettings.SendEssRegistrationEmail),
            taxCountryCode: '@Model.TaxCountryCode',
			taxCountryId: @Model.TaxCountryId
        }
    };
</script>

@await Component.InvokeAsync("WorkflowActivitySteps", new { isBeginning = true, employeeOrCompanyWorkFlow = "E" })

<page-header>
    <page-header-toolbar title="@Localizer.GetString("lblPageHeader")" />
</page-header>

<page-content>
    <alert-message type="warning" id="employeeLegalWorkAge" class="col-md-6 d-none">
        @Localizer.GetString("lblEmployeeLegalWorkAgeWarning", Model.Employee.TaxCountryLegalWorkAge)
    </alert-message>
    
    <form id="form-employee-profile" autocomplete="off" enctype="multipart/form-data" method="post">
        <div class="ps-tabs-container mh-100">
            @await Html.PartialAsync("_Form")
        </div>

        @if (Model.CanCreateEmployee)
        {
            <page-footer>
                <btn-update tc="btn-save" system-area="@SystemAreas.Profile.BasicCreate" />
            </page-footer>
        }
    </form>
</page-content>