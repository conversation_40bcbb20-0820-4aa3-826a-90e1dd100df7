@model ProfileViewModel

@(Html.DevExtreme().Form<EmployeeProfileViewModel>()
      .ID("form-address")
      .ElementAttr("data_tc", "form-address")
      .FormData(Model.Employee)
      .OnInitialized("AddEmployeeProfile.App.OnInitialized")
      .OnFieldDataChanged("AddEmployeeProfile.App.OnFieldDataChanged")
      .CustomizeItem("AddEmployeeProfile.App.CustomizeItem")
      .Items(items =>
      {
          items.AddGroup()
              .ColCountByScreen(_ => _.Lg(3).Md(2))
              .Caption(Localizer.GetString("lblPhysicalAddress"))
              .Items(groupItems =>
              {
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].UnitNumber)
                      .Visible(Model.ShowUnitNumber)
                      .VisibleIndex(0);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Complex)
                      .Visible(Model.ShowComplex)
                      .VisibleIndex(1);

                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].StreetNumber)
                      .Visible(Model.ShowStreetNumber);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressLine1)
                      .ValidationRules(_ => _.AddStringLength().Max(Model.EmployeeProfileSettings.MaxStreetNameLength)
                                           .Message(string.Format(Localizer.GetString("lblStreetNameMaxError"), Model.EmployeeProfileSettings.MaxStreetNameLength)))
                      .IsRequired(Model.RequireAddressLine1);

                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Block);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Entrance);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Staircase);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Floor);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Door);

                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressLine2);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressLine3)
                      .IsRequired(Model.RequireAddressLine3);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressCode)
                      .ValidationRules(_ => _.AddStringLength().Max(12)
                                           .Message(Localizer.GetString("errAddressCodeMaxLength")));
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].CountryId)
                      .EnumFor<EnumAddressCountry>();
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].ProvinceId);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressStreetTypeId)
                      .VisibleIndex(Model.TaxCountryCode == Maddalena.CountryCode.ES.ToString() ? 2 : 998);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[0].MunicipalityId)
                      .VisibleIndex(999);
              });

          items.AddGroup()
              .ColCountByScreen(_ => _.Lg(4).Md(2))
              .Caption(Localizer.GetString("lblPostalAddress"))
              .Items(groupItems =>
              {
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].SameAsPostal)
                      .Label(l => l.Visible(false))
                      .EditorWithMetadata(_ => _.CheckBox().Text(Localizer.GetString("lblSameAsPostal")))
                      .Visible(Model.ShowSameAsPostal)
                      .VisibleIndex(0);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].IsCareofAddress)
                      .Label(l => l.Visible(false))
                      .EditorWithMetadata(_ => _.CheckBox().Text(Localizer.GetString("lblIsCareofAddress")))
                      .Visible(Model.ShowCareOfAddress)
                      .VisibleIndex(1);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressType)
                      .EnumFor<EnumAddressType>(excludeValues: new[] { (int)AddressType.Physical })
                      .VisibleIndex(2);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].UnitNumber)
                      .Visible(Model.ShowUnitNumber)
                      .VisibleIndex(3);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Complex)
                      .Visible(Model.ShowComplex);

                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].StreetNumber)
                      .Visible(Model.ShowStreetNumber);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressLine1)
                      .ValidationRules(_ => _.AddStringLength().Max(Model.EmployeeProfileSettings.MaxStreetNameLength)
                                           .Message(string.Format(Localizer.GetString("lblPostalAddressNumberMaxError"), Model.EmployeeProfileSettings.MaxStreetNameLength)))
                      .IsRequired(Model.RequireAddressLine1);

                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Block);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Entrance);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Staircase);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Floor);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Door);

                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressLine2);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressLine3)
                      .IsRequired(Model.RequireAddressLine3);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressLine1);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressLine2);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].SpecialServices);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressLine3);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressCode)
                      .ValidationRules(_ => _.AddStringLength().Max(12)
                                           .Message(Localizer.GetString("errAddressCodeMaxLength")));
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressCode)
                      .ValidationRules(_ => _.AddStringLength().Max(12)
                                           .Message(Localizer.GetString("errAddressCodeMaxLength")));
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalCountryId)
                      .EnumFor<EnumAddressCountry>();
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalProvinceId);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].ProvinceId);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].CareOfIntermediary)
                      .IsRequired(true);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressStreetTypeId)
                      .VisibleIndex(Model.TaxCountryCode == Maddalena.CountryCode.ES.ToString() ? 4 : 998);
                  groupItems.AddSimpleWithMetadataFor(m => m.Address[1].MunicipalityId)
                      .VisibleIndex(999);
              });
      }))