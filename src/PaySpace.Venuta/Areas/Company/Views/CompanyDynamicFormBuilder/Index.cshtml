@model PaySpace.Venuta.Modules.DynamicFormBuilder.Model.DynamicFormBuilderPageLoadViewModel

@inject IStringLocalizerFactory StringLocalizerFactory
@{
    var localizer = StringLocalizerFactory.Create(SystemAreas.Subordinate.Area, null);
}

@section scripts
{
    <script type="module" src="~/lib/nextgen/component-lib/dynamic-form-builder.js" asp-append-version="true"></script>
    <script>
        const auditTrailsUrl = '@Html.Raw(Url.Action("DynamicFormBuilderAudit", "Modal", new { area = string.Empty, pageArea = "Company" }))';
        document.addEventListener("DOMContentLoaded", function () {
            const dfb = document.getElementsByTagName("dynamic-form-builder")[0];
            const modalButton = document.getElementById("btn-audit-trail-modal");
            modalButton.setAttribute("data-remote", auditTrailsUrl);
            dfb?.addEventListener("dynamicFormBuilderComponentChange", function (e) {
                const args = e.detail;
                if (args?.dynamicFormId) {
                    const remoteUrl = auditTrailsUrl + `&dynamicFormId=${args?.dynamicFormId}`;
                    modalButton.setAttribute("data-remote", remoteUrl);
                }
                else {
                    modalButton.setAttribute("data-remote", auditTrailsUrl);
                }
            });
        });
    </script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.CustomForms.ConfigKeys.PageHeader)">
        <page-header-toolbar-content>
            <button id="dfb-run-test" class="btn btn-outline-primary" style="display: none;">
                Run Test
            </button>
            <div>
                <button id="btn-audit-trail-modal"
                        class="audit-trail btn btn-outline-primary"
                        data-bs-target="#audit-modal"
                        data-load-always="true"
                        data-bs-toggle="modal"
                        title="@localizer.GetString("AuditTitle")">
                    <i class="far fa-list-alt"></i>
                </button>
            </div>
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    @if (string.IsNullOrEmpty(Model.BureauAddNewEmployeeDynamicFormId))
    {
        <alert-message type="secondary" class="col-md-4">
            @Localizer.GetString("lblNoBureauLevelCountryForm")
        </alert-message>
        <div class="py-2"></div>
    }
    <dynamic-form-builder context-level="Company" generate-employee-number="@Model.GenerateEmployeeNumber" bureau-dynamic-form-id="@Model.BureauAddNewEmployeeDynamicFormId" tax-country-id="@Model.TaxCountryId"></dynamic-form-builder>
</page-content>

<style>
    /* To get DxLabel templates to work on the Form Designer tab, we need to unset the pointer-events */
    #form-designer .dx-field-item-label {
        pointer-events: unset !important;
    }
</style>