@model EOnboardingDetailsViewModel

@{
    ViewBag.Title = Localizer.GetString(SystemAreas.EOnboarding.Keys.PageHeader);
}

@section scripts
{
    <script type="module" src="~/pages/e-onboarding.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.EOnboarding.Keys.PageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <e-onboarding
                dynamic-form-index-url="@Model.DynamicFormIndexUrl"
                is-eonboarding-template-active="@Model.IsEOnboardingTemplateActive"
                generate-employee-number="@Model.GenerateEmployeeNumber">
    </e-onboarding>
</page-content>