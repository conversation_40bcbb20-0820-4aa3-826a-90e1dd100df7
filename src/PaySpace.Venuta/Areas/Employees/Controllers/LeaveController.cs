namespace PaySpace.Venuta.Areas.Employees.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Globalization;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Data.ResponseModel;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;
    using Microsoft.Extensions.Logging;

    using Newtonsoft.Json;

    using PaySpace.Cache.Distributed;
    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Leave;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Conventions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Extensions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Data.Models.Workflow;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Exceptions;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Models;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.ViewModels;
    using PaySpace.Venuta.Workflow;
    using PaySpace.Venuta.Workflow.Handlers;
    using PaySpace.Venuta.Workflow.Services;

    using RedLockNet;

    [Area("Employees")]
    [DisplayName(SystemAreas.Leave.Application)]
    public class LeaveController : Controller
    {
        private readonly ApplicationContext context;
        private readonly ILeaveViewModelFactory factory;
        private readonly ILeaveWorkflowHandler workflowManager;
        private readonly IWorkflowService workflowService;
        private readonly IWorkflowApproverService workflowApproverService;
        private readonly IWorkflowProcessor workflowProcessor;
        private readonly IEmployeeLeaveService employeeLeaveService;
        private readonly IEmployeeLeaveCalculationService employeeLeaveCalculationService;
        private readonly IEmployeeLeaveConcessionService employeeLeaveConcessionService;
        private readonly IEmployeeLeaveSettingService employeeLeaveSettingService;
        private readonly ILeaveDisplayViewModelBuilder leaveDisplayViewModelBuilder;
        private readonly IValidator<LeaveViewModel> validator;
        private readonly IValidator<EmployeeLeaveAdjustment> leaveValidator;
        private readonly ICompanyRunService companyRunService;
        private readonly ICalculationWebApiClient calculationWebApiClient;
        private readonly ILogger<LeaveController> logger;
        private readonly IStringLocalizer<LeaveController> localizer;
        private readonly ITenantProvider tenantProvider;
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly IDistributedLockFactory lockFactory;
        private readonly IFileManagerStorageService fileManagerStorageService;

        public LeaveController(
            ApplicationContext context,
            ILeaveViewModelFactory factory,
            ILeaveWorkflowHandler workflowManager,
            IWorkflowService workflowService,
            IWorkflowApproverService workflowApproverService,
            IWorkflowProcessor workflowProcessor,
            IEmployeeLeaveService employeeLeaveService,
            IEmployeeLeaveCalculationService employeeLeaveCalculationService,
            IEmployeeLeaveConcessionService employeeLeaveConcessionService,
            IEmployeeLeaveSettingService employeeLeaveSettingService,
            ILeaveDisplayViewModelBuilder leaveDisplayViewModelBuilder,
            IValidator<LeaveViewModel> validator,
            IValidator<EmployeeLeaveAdjustment> leaveValidator,
            ICompanyRunService companyRunService,
            ICalculationWebApiClient calculationWebApiClient,
            ILogger<LeaveController> logger,
            IFileManagerStorageService fileManagerStorageService,
            IStringLocalizer<LeaveController> localizer,
            ITenantProvider tenantProvider,
            IEmploymentStatusService employmentStatusService,
            IDistributedLockFactory lockFactory)
        {
            this.context = context;
            this.factory = factory;
            this.workflowManager = workflowManager;
            this.workflowService = workflowService;
            this.workflowApproverService = workflowApproverService;
            this.workflowProcessor = workflowProcessor;
            this.employeeLeaveService = employeeLeaveService;
            this.employeeLeaveCalculationService = employeeLeaveCalculationService;
            this.employeeLeaveConcessionService = employeeLeaveConcessionService;
            this.employeeLeaveSettingService = employeeLeaveSettingService;
            this.leaveDisplayViewModelBuilder = leaveDisplayViewModelBuilder;
            this.validator = validator;
            this.leaveValidator = leaveValidator;
            this.companyRunService = companyRunService;
            this.calculationWebApiClient = calculationWebApiClient;
            this.logger = logger;
            this.localizer = localizer;
            this.tenantProvider = tenantProvider;
            this.employmentStatusService = employmentStatusService;
            this.lockFactory = lockFactory;
            this.fileManagerStorageService = fileManagerStorageService;
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/history")]
        public async Task<object> GetHistory(DataSourceLoadOptions loadOptions, Tenant tenant)
        {
            var history = this.employeeLeaveService.GetLeaveAdjustments(tenant.EmployeeId, !this.User.IsInRole(UserTypeCodes.Employee))
                .Where(_ => _.StartDate.HasValue && _.LeaveEntryType != LeaveEntryType.Adjustment);

            var result = await DataSourceLoader.LoadAsync(history, loadOptions);

            if (result.data.OfType<Group>().Any())
            {
                return result;
            }

            var data = result.data.Cast<EmployeeLeaveAdjustmentDetailResult>();
            var cancellations = await history.Where(_ => _.LeaveEntryType == LeaveEntryType.Cancellation).ToListAsync();

            result.data = data
                .Select(_ => new
                {
                    _.LeaveAdjustmentId,
                    _.PeriodStartDate,
                    _.Year,
                    _.RunDescription,
                    _.RunStatus,
                    _.LeaveDescription,
                    _.LeaveEntryType,
                    _.LeaveType,
                    _.Status,
                    _.ComponentEmployeeId,

                    // Because this is an anonymous object, the default datetime converters are not applied. #34501
                    StartDate = _.StartDate.HasValue ? _.StartDate.Value.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) : string.Empty,
                    EndDate = _.EndDate.HasValue ? _.EndDate.Value.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) : string.Empty,
                    _.TotalDays,
                    _.EmployeeId,
                    _.HistoricalConcessionId,
                    AllowCancel = this.CanCancel(
                        cancellations,
                        new EmployeeLeaveAdjustment
                        {
                            CompanyLeaveSetupId = _.CompanyLeaveSetupId,
                            LeaveAdjustmentId = _.LeaveAdjustmentId,
                            StartDate = _.StartDate,
                            EndDate = _.EndDate,
                            LeaveType = _.LeaveType,
                            Status = _.Status,
                            LeaveEntryType = _.LeaveEntryType,
                            TotalDays = _.TotalDays,
                            ComponentEmployeeId = _.ComponentEmployeeId
                        },
                        tenant).Result,
                    Url = this.Url.Action(nameof(this.View), new { _.LeaveAdjustmentId })
                });

            return result;
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/types")]
        public async Task<object> GetTypes(DataSourceLoadOptions loadOptions, Tenant tenant)
        {
            try
            {
                var leaveTypes = await this.employeeLeaveSettingService.GetLeaveTypesAsync(tenant.EmployeeId);
                var result = leaveTypes
                    .Where(_ => _.LeaveTypeId != (int)LeaveType.Special)
                    .OrderBy(_ => _.LeaveTypeDescription)
                    .Select(_ => new { value = _.LeaveTypeId, text = _.LeaveTypeDescription, companyLeaveSetupId = (long)_.LeaveTypeId });

                var i = (int)LeaveType.Special + 1;

                var specialBuckets = await this.employeeLeaveSettingService.GetBucketsAsync(tenant.EmployeeId, LeaveType.Special);
                result = result.Concat(specialBuckets
                    .OrderBy(_ => _.LeaveDescription)
                    .Select(_ => new { value = i++, text = _.LeaveDescription, companyLeaveSetupId = _.CompanyLeaveSetupId }));

                return DataSourceLoader.Load(result, loadOptions);
            }
            catch (InvalidOperationException)
            {
                return Array.Empty<(string value, string text)>();
            }
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/buckets")]
        public async Task<object> GetBuckets(DataSourceLoadOptions loadOptions, Tenant tenant, LeaveType leaveType, DateTime? effectiveDate)
        {
            try
            {
                var buckets = await this.employeeLeaveSettingService.GetBucketsAsync(tenant.EmployeeId, leaveType, effectiveDate);
                var result = buckets.OrderBy(_ => _.LeaveDescription).Select(_ => new { value = _.CompanyLeaveSetupId, text = _.LeaveDescription });

                return DataSourceLoader.Load(result, loadOptions);
            }
            catch (InvalidOperationException ex)
            {
                this.logger.LogWarning(ex, ex.Message);
                return Array.Empty<(string value, string text)>();
            }
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/reasons")]
        public async Task<object> GetReasons(DataSourceLoadOptions loadOptions, Tenant tenant, LeaveType leaveType, long? companyLeaveSetupId)
        {
            try
            {
                var reasons = await this.employeeLeaveSettingService.GetReasonsAsync(tenant.CompanyId, tenant.EmployeeId, leaveType, companyLeaveSetupId);
                var result = reasons.Select(_ => new { value = _.LeaveReasonId, text = _.LeaveReason }).OrderBy(_ => _.text);

                return await DataSourceLoader.LoadAsync(result, loadOptions);
            }
            catch (InvalidOperationException ex)
            {
                this.logger.LogWarning(ex, ex.Message);
                return Array.Empty<(string value, string text)>();
            }
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/runs")]
        public async Task<object> GetCompanyRuns(DataSourceLoadOptions loadOptions, Tenant tenant)
        {
            var employmentDate = await this.employmentStatusService.GetEmploymentDateAsync(tenant.EmployeeId);
            var result = this.companyRunService.GetCompanyRuns(tenant.FrequencyId!.Value)
                .Where(_ => _.Status != RunStatus.Closed && !_.TakeOnRun && _.PeriodStartDate >= employmentDate)
                .Select(_ => new
                {
                    CompanyRunId = _.RunId,
                    _.RunDescription,
                    _.OrderNumber,
                    _.PeriodEndDate
                });
            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/concession-dates")]
        public object GetConcessionDates(DataSourceLoadOptions loadOptions, Tenant tenant)
        {
            var localizedText = this.localizer.GetString("lblConcessionYear").Value;
            var result = this.employeeLeaveConcessionService.GetEmployeeHistoricalConcessions(tenant.EmployeeId)
                .Select(_ => new
                {
                    value = _.HistoricalConcessionId,
                    text = localizedText + ": " + _.ConcessionYearStartDate.ToShortDateString() + " - " + _.ConcessionYearEndDate.ToShortDateString()
                });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpPost("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/form/{leaveAdjustmentId?}")]
        public async Task<IActionResult> Form(Tenant tenant, long leaveAdjustmentId, EmployeeLeaveAdjustment model)
        {
            // Validation is not needed.
            this.ModelState.Clear();

            try
            {
                var builder = await this.factory.GetBuilderAsync(tenant, this.tenantProvider.GetTaxCountryCode());
                var vm = await builder.BuildViewModelAsync(model, this.User, this.HttpContext.GetSecurityProfile(), true);

                return this.PartialView("_Form", vm);
            }
            catch (NegativeLeaveException ex)
            {
                return this.BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                this.logger.LogWarning(ex, ex.Message);
                return this.BadRequest(ex.Message);
            }
        }

        [AllowAll]
        [HideSubordinate]
        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/view/")]
        public async Task<IActionResult> View(Tenant tenant, long leaveAdjustmentId, bool submitted, bool canceled)
        {
            var leaveAdjustment = await this.employeeLeaveService.FindByIdWithNavigationAsync(tenant, leaveAdjustmentId);
            if (leaveAdjustment == null)
            {
                return this.Forbid();
            }

            var leaveAdjustments = await this.employeeLeaveService.GetLeaveAdjustments(tenant.EmployeeId)
                .Where(_ => _.StartDate.HasValue)
                .ToListAsync();

            var vm = await this.leaveDisplayViewModelBuilder.BuildAsync(tenant.CompanyId, tenant.FrequencyId.Value, leaveAdjustment);
            vm.Workflow = new WorkflowStepsViewModel
            {
                Workflow = await this.workflowService.GetWorkflowAsync(WorkflowItem.Leave, leaveAdjustmentId, tenant.CompanyId),
                RelatedPrimaryKey = leaveAdjustmentId
            };

            vm.AllowCancel = await this.CanCancel(leaveAdjustments, leaveAdjustment, tenant);
            vm.Submitted = submitted;
            vm.Canceled = canceled;

            return this.View(vm);
        }

        public async Task<IActionResult> Index(Tenant tenant, LeaveType? type, DateTime? start, DateTime? end)
        {
            var leaveViewModel = await this.GetViewModelAsync(tenant, type, start, end, true);
            return this.View(leaveViewModel);
        }

        public async Task<IActionResult> History(Tenant tenant, LeaveType? type, DateTime? start, DateTime? end)
        {
            var leaveViewModel = await this.GetViewModelAsync(tenant, type, start, end, false);
            return this.View(nameof(this.Index), leaveViewModel);
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/edit")]
        public async Task<IActionResult> Edit(Tenant tenant, long leaveAdjustmentId)
        {
            var leaveAdjustment = await this.employeeLeaveService.FindByIdAsync(tenant, leaveAdjustmentId);
            if (leaveAdjustment == null)
            {
                return this.Forbid();
            }

            var runStatus = await this.companyRunService.GetRunStatusAsync(leaveAdjustment.CompanyRunId);
            if (runStatus == RunStatus.Closed && leaveAdjustment.Status != LeaveStatus.Waiting)
            {
                return this.Forbid();
            }

            leaveAdjustment.SellVacationDays = leaveAdjustment.TotalDaysSell.HasValue;
            leaveAdjustment.CompanyLeaveSetupId ??= -2;
            leaveAdjustment.SkipValidation = true;

            var builder = await this.factory.GetBuilderAsync(tenant, this.tenantProvider.GetTaxCountryCode());
            var vm = await builder.InitAsync(leaveAdjustment, this.User, this.HttpContext.GetSecurityProfile());

            if (!vm.AllowHours && leaveAdjustment.Hours > 0)
            {
                leaveAdjustment.Hours = 0;
                leaveAdjustment.Days = leaveAdjustment.TotalDays;
            }

            vm.SelectedLeaveType = (int)leaveAdjustment.LeaveType;
            return this.View("Index", vm);
        }

        [CreateMessage]
        [HttpPost("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api")]
        public async Task<IActionResult> Post(Tenant tenant, EmployeeLeaveAdjustment model, long? approverEmployeeId)
        {
            model.EmployeeId = tenant.EmployeeId;
            model.CompanyLeaveSetupId ??= -2;

            var isCalcRunning = await this.calculationWebApiClient.IsEmployeeCalculationInProgressAsync(tenant.EmployeeId, model.CompanyRunId);
            if (isCalcRunning)
            {
                this.ModelState.AddModelError(string.Empty, this.localizer.GetString(ErrorCodes.Leave.CalcIsRunning));
                return this.BadRequest();
            }

            if (model.LeaveType > LeaveType.Special)
            {
                model.LeaveType = LeaveType.Special;
            }

            // Setting the Signature property so that we can use it as part of the validation
            model.Signature = this.fileManagerStorageService.GetBlobUrl(this.User.GetUserId(), tenant.CompanyId, tenant.EmployeeId, this.fileManagerStorageService.GetTempBlobPath(this.User.GetSessionId(), StorageContainers.EmployeeLeaveAdjustments));

            return await this.TryCreateResourceAsync(
                () => this.SaveApplicationAsync(tenant, approverEmployeeId, model),
                model);
        }

        [UpdateMessage]
        [HttpPatch("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api")]
        public async Task<IActionResult> Patch(Tenant tenant, long leaveAdjustmentId, EmployeeLeaveAdjustment model, long? approverEmployeeId)
        {
            var leaveAdjustment = await this.employeeLeaveService.FindByIdAsync(tenant, leaveAdjustmentId);
            await this.TryUpdateModelAsync(leaveAdjustment);

            if (leaveAdjustment.CompanyLeaveSetupId == null)
            {
                leaveAdjustment.CompanyLeaveSetupId = -2;
            }

            if (leaveAdjustment.LeaveType > LeaveType.Special)
            {
                leaveAdjustment.LeaveType = LeaveType.Special;
            }

            leaveAdjustment.SkipValidation = true;

            var builder = await this.factory.GetBuilderAsync(tenant, this.tenantProvider.GetTaxCountryCode());

            // Setting the Signature property so that we can use it as part of the validation
            leaveAdjustment.Signature = this.fileManagerStorageService.GetBlobUrl(this.User.GetUserId(), tenant.CompanyId, tenant.EmployeeId, this.fileManagerStorageService.GetTempBlobPath(this.User.GetSessionId(), StorageContainers.EmployeeLeaveAdjustments));
            var vm = await builder.BuildViewModelAsync(leaveAdjustment, this.User, this.HttpContext.GetSecurityProfile(), false);

            leaveAdjustment.TotalDays = this.employeeLeaveService.CalculateTotalDays(leaveAdjustment, vm.PayRateHours, vm.AllowHours);

            leaveAdjustment.SkipValidation = true;
            var result = await this.validator.ValidateAsync(vm, _ => _.IncludeRuleSets("Application"));
            result.AddToModelState(this.ModelState, null);

            // Add basic validation for leave adjustment + custom fields
            result = await this.leaveValidator.ValidateAsync(vm.LeaveAdjustment, _ => _.IncludeRuleSets(RuleSetNames.Update));
            result.AddToModelState(this.ModelState, null);

            if (this.ModelState.IsValid)
            {
                var isStatusChanged = this.employeeLeaveService.IsLeaveStatusChanged(leaveAdjustment);
                await this.employeeLeaveService.UpdateAsync(this.User.GetUserId(), tenant.CompanyId, tenant.EmployeeId, leaveAdjustment);

                if (!leaveAdjustment.SkipWorkflow && leaveAdjustment.Status == LeaveStatus.Waiting)
                {
                    await this.workflowProcessor.InitializeWorkflowAsync(
                        this.Request.PathBase,
                        this.User,
                        vm.Workflow,
                        tenant.EmployeeId,
                        leaveAdjustmentId,
                        approverEmployeeId,
                        leaveAdjustment.Comments);
                }

                if (isStatusChanged)
                {
                    // Ensure the leave application is attached to a Scheme in a correct LeaveSetup, if updating to Approved
                    if (leaveAdjustment.CompanyLeaveSetupId > 0 && leaveAdjustment.Status == LeaveStatus.Approved)
                    {
                        var updatedCompanyLeaveSetupId = await this.employeeLeaveService.GetUpdatedLeaveSetupIdForExpiredSchemeApplicationAsync(
                            leaveAdjustment.EmployeeId,
                            tenant.CompanyId,
                            leaveAdjustment.CompanyRunId,
                            (long)leaveAdjustment.CompanyLeaveSetupId);

                        // Update the application to have the correct LeaveSetupId (if linked to an expired/incorrect LeaveSetup)
                        if (updatedCompanyLeaveSetupId != default)
                        {
                            // This ensures Calc saves it to the correct bucket
                            leaveAdjustment.CompanyLeaveSetupId = updatedCompanyLeaveSetupId;
                        }
                    }

                    var workflow = await this.workflowManager.FindAsync(tenant.CompanyId, tenant.EmployeeId, leaveAdjustment.LeaveType, leaveAdjustment.CompanyLeaveSetupId);
                    if (workflow != null)
                    {
                        var workflowActionType = leaveAdjustment.Status == LeaveStatus.Approved ? WorkflowActionType.Commit : WorkflowActionType.Reject;
                        var message = leaveAdjustment.Status == LeaveStatus.Approved ? "Application completed by admin." : "Application declined by admin.";
                        await this.workflowProcessor.TerminateWorkflowAsync(
                            this.User,
                            workflow,
                            workflowActionType,
                            leaveAdjustment.LeaveAdjustmentId,
                            message);
                    }
                }

                if (!leaveAdjustment.TotalDaysSell.HasValue)
                {
                    // If an application is updated to un-tick the sell-vacation days checkbox,
                    // check if an Adjustment has been made to deduct the leave-sold days and delete it.
                    await this.employeeLeaveService.DeleteLeaveSoldAdjustmentAsync(tenant.EmployeeId, leaveAdjustment);
                }

                await this.employeeLeaveCalculationService.CalculateLeaveBalanceAsync(tenant.CompanyId, tenant.EmployeeId, leaveAdjustment.CompanyRunId, CalcSource.EmployeeLeaveApplication);
                return this.Ok();
            }

            return this.BadRequest();
        }

        [HttpGet]
        [UpdateMessage]
        [Permission(SystemAreas.Leave.Application, SystemAreas.Leave.Keys.LeaveCancellation)]
        public async Task<string> Cancel(Tenant tenant, long leaveAdjustmentId, string comment)
        {
            var reverseAdjustmentId = await this.workflowManager.CancelLeaveAsync(
                this.Request.PathBase,
                this.User,
                tenant.CompanyId,
                tenant.FrequencyId!.Value,
                leaveAdjustmentId,
                comment);

            if (reverseAdjustmentId == leaveAdjustmentId)
            {
                return this.Url.Action(nameof(this.Index));
            }

            return this.Url.Action(
                nameof(this.View),
                new { leaveAdjustmentId = reverseAdjustmentId, canceled = true });
        }

        [DeleteMessage]
        [Permission(SystemAreas.Leave.Application, SystemAreas.Leave.Application)]
        [HttpDelete("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api")]
        public async Task<IActionResult> Delete(Tenant tenant, long key)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            if (!this.IsAdminUser(profile))
            {
                return this.Forbid();
            }

            var leaveAdjustment = await this.employeeLeaveService.FindByIdAsync(tenant, key);

            var workflow = await this.workflowManager.FindAsync(tenant.CompanyId, tenant.EmployeeId, leaveAdjustment.LeaveType, leaveAdjustment.CompanyLeaveSetupId);
            if (workflow != null)
            {
                await this.workflowProcessor.TerminateWorkflowAsync(
                    this.User,
                    workflow,
                    WorkflowActionType.Reject,
                    leaveAdjustment.LeaveAdjustmentId,
                    "Application Deleted by admin");
            }

            await this.employeeLeaveService.DeleteAsync(tenant.CompanyId, tenant.EmployeeId, leaveAdjustment);
            if (leaveAdjustment.Status == LeaveStatus.Approved)
            {
                await this.employeeLeaveCalculationService.CalculateLeaveBalanceAsync(tenant.CompanyId, tenant.EmployeeId, leaveAdjustment.CompanyRunId, CalcSource.EmployeeLeaveApplication);
            }

            return this.RedirectToAction(nameof(this.Index));
        }

        private async Task<bool> CanCancel(IEnumerable<EmployeeLeaveAdjustmentDetailResult> allLeaveAdjustments, EmployeeLeaveAdjustment leaveAdjustment, Tenant tenant)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            if (!profile.IsFullAccess(SystemAreas.Leave.Application, SystemAreas.Leave.Keys.LeaveCancellation))
            {
                return false;
            }

            if (!this.IsAdminUser(profile) && tenant.EmployeeId != this.User.GetEmployeeId())
            {
                return false;
            }

            if (await this.HasWorkflowAsync(tenant, leaveAdjustment.LeaveType, leaveAdjustment.CompanyLeaveSetupId))
            {
                return false;
            }

            if (leaveAdjustment.LeaveEntryType != LeaveEntryType.LeaveApplication || (leaveAdjustment.Status != LeaveStatus.Waiting && leaveAdjustment.Status != LeaveStatus.Approved))
            {
                return false;
            }

            if (allLeaveAdjustments.Any(_ => _.CancellationId == leaveAdjustment.LeaveAdjustmentId && (_.Status == LeaveStatus.Waiting || _.Status == LeaveStatus.Approved)))
            {
                return false;
            }

            if (leaveAdjustment.ComponentEmployeeId.HasValue)
            {
                return false;
            }

            // Copied from old system. Hide the cancel button if already canceled.
            var adjCancelChecks = allLeaveAdjustments.Count(
                _ => _.LeaveAdjustmentId != leaveAdjustment.LeaveAdjustmentId
                    && _.StartDate == leaveAdjustment.StartDate
                    && _.EndDate == leaveAdjustment.EndDate
                    && _.LeaveEntryType == LeaveEntryType.Cancellation
                    && _.LeaveType == leaveAdjustment.LeaveType
                    && _.Status != LeaveStatus.Declined
                    && _.TotalDays == leaveAdjustment.TotalDays);

            if (adjCancelChecks > 0)
            {
                var adj = allLeaveAdjustments.Count(
                    _ => _.StartDate == leaveAdjustment.StartDate
                        && _.EndDate == leaveAdjustment.EndDate
                        && _.LeaveEntryType == LeaveEntryType.LeaveApplication
                        && _.LeaveType == leaveAdjustment.LeaveType
                        && _.Status != LeaveStatus.Declined
                        && _.TotalDays == leaveAdjustment.TotalDays);

                if (adjCancelChecks == adj)
                {
                    return false;
                }
            }

            return true;
        }

        private bool IsAdminUser(ISecurityProfile profile)
        {
            return !this.User.IsInRole(UserTypeCodes.Employee) && profile.IsFullAccess(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Adjustment);
        }

        private async Task<bool> HasWorkflowAsync(Tenant tenant, LeaveType leaveType, long? companyLeaveSetupId)
        {
            return await this.workflowManager.FindAsync(tenant.CompanyId, tenant.EmployeeId, leaveType, companyLeaveSetupId) == null;
        }

        private async Task<LeaveViewModel> GetViewModelAsync(Tenant tenant, LeaveType? type, DateTime? start, DateTime? end, bool isApply)
        {
            var profile = this.HttpContext.GetSecurityProfile();

            var taxCountryCode = this.tenantProvider.GetTaxCountryCode();
            if (taxCountryCode == BrazilConstants.BrazilTaxCountryCode)
            {
                type = LeaveType.Annual;
            }

            if (type.HasValue)
            {
                var builder = await this.factory.GetBuilderAsync(tenant, taxCountryCode);
                var vm = await builder.BuildViewModelAsync(new EmployeeLeaveAdjustment { EmployeeId = tenant.EmployeeId, LeaveType = type.Value, StartDate = start, EndDate = end }, this.User, profile, true);
                vm.SelectedLeaveType = (int)type.Value;

                return vm;
            }

            return new LeaveViewModel
            {
                AllowEdit = profile.IsFullAccess(SystemAreas.Leave.Application, SystemAreas.Leave.Application),
                LeaveAdjustment = new EmployeeLeaveAdjustment { EmployeeId = tenant.EmployeeId },
                IsAdminUser = this.IsAdminUser(profile),
                CompanyId = tenant.CompanyId,
                EmployeeId = tenant.EmployeeId,
                IsApply = isApply,
                IsBrazilLeave = taxCountryCode == BrazilConstants.BrazilTaxCountryCode
            };
        }

        private async Task<IActionResult> TryCreateResourceAsync(Func<Task<IActionResult>> save, EmployeeLeaveAdjustment model)
        {
            var key = $"Employee:{model.EmployeeId}:LeaveApplication:{model.LeaveEntryType}:{model.LeaveType}:{model.CompanyLeaveSetupId}:{model.StartDate}:{model.EndDate}";

            try
            {
                return await this.lockFactory.TryUpdateWithLockAsync(key, save);
            }
            catch (LockTimedOutException)
            {
                throw new InvalidOperationException("Duplicate application detected");
            }
        }

        private async Task<IActionResult> SaveApplicationAsync(Tenant tenant, long? approverEmployeeId, EmployeeLeaveAdjustment leaveAdjustment)
        {
            var builder = await this.factory.GetBuilderAsync(tenant, this.tenantProvider.GetTaxCountryCode());
            var vm = await builder.InitAsync(leaveAdjustment, this.User, this.HttpContext.GetSecurityProfile());
            leaveAdjustment.TotalDays = this.employeeLeaveService.CalculateTotalDays(leaveAdjustment, vm.PayRateHours, vm.AllowHours);

            var result = await this.validator.ValidateAsync(vm, _ => _.IncludeRuleSets("Application"));
            result.AddToModelState(this.ModelState, null);

            // Add basic validation for leave adjustment + custom fields
            result = await this.leaveValidator.ValidateAsync(vm.LeaveAdjustment, _ => _.IncludeRuleSets(RuleSetNames.Create));
            result.AddToModelState(this.ModelState, null);

            if (this.ModelState.IsValid)
            {
                leaveAdjustment.EmployeeId = tenant.EmployeeId;
                if (!leaveAdjustment.SkipWorkflow && vm.Workflow == SimpleWorkflow.Instance &&
                    leaveAdjustment.Status == LeaveStatus.Waiting)
                {
                    var approvers = await this.workflowApproverService.GetAffectedEmployeesAsync(this.Request.PathBase, this.User, vm.Workflow, tenant.EmployeeId, leaveAdjustment.LeaveAdjustmentId, 1);
                    leaveAdjustment.ApprovalEmployeeId = approvers.Select(_ => _.EmployeeId).First();
                }

                using (var transaction = await this.context.CreateTransactionAsync())
                {
                    try
                    {
                        await this.employeeLeaveService.AddAsync(this.User.GetUserId(), tenant.CompanyId, tenant.EmployeeId, leaveAdjustment);
                        if (!leaveAdjustment.SkipWorkflow && leaveAdjustment.Status == LeaveStatus.Waiting)
                        {
                            await this.workflowProcessor.InitializeWorkflowAsync(
                                this.Request.PathBase,
                                this.User,
                                vm.Workflow,
                                tenant.EmployeeId,
                                leaveAdjustment.LeaveAdjustmentId,
                                approverEmployeeId,
                                leaveAdjustment.Comments);
                        }

                        await transaction.CommitAsync();
                        if (leaveAdjustment.Status == LeaveStatus.Approved)
                        {
                            await this.employeeLeaveCalculationService.CalculateLeaveBalanceAsync(tenant.CompanyId, tenant.EmployeeId, leaveAdjustment.CompanyRunId, CalcSource.EmployeeLeaveApplication);
                        }

                        return this.Ok(leaveAdjustment.LeaveAdjustmentId);
                    }
                    catch (ValidationException ex)
                    {
                        foreach (var error in ex.Errors)
                        {
                            this.ModelState.AddModelError(string.Empty, error.ErrorMessage);
                        }
                    }
                }
            }

            return this.BadRequest();
        }
    }
}