namespace PaySpace.Venuta.Areas.Employees.Controllers
{
    using System.ComponentModel;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Mvc;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Newtonsoft.Json;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Conventions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Abstractions.Messages;
    using PaySpace.Venuta.Messaging.Message;
    using PaySpace.Venuta.Messaging.Notifications;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Storage;

    [Area("Employees")]
    [DisplayName(SystemAreas.AssetRegister.Area)]
    public class AssetRegisterController : Controller
    {
        private readonly IEmployeeAssetService employeeAssetService;
        private readonly IFileManagerStorageService fileManagerStorageService;
        private readonly IMessageBus messageBus;

        public AssetRegisterController(
            IEmployeeAssetService employeeAssetService,
            IFileManagerStorageService fileManagerStorageService,
            IMessageBus messageBus)
        {
            this.employeeAssetService = employeeAssetService;
            this.fileManagerStorageService = fileManagerStorageService;
            this.messageBus = messageBus;
        }

        public IActionResult Index(Tenant tenant)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            return this.View(
                "Index",
                new AssetsRegisterViewModel
                {
                    CompanyId = tenant.CompanyId,
                    EmployeeId = tenant.EmployeeId,
                    AllowEdit = profile.IsFullAccess(SystemAreas.AssetRegister.Area, SystemAreas.AssetRegister.Keys.EmployeeAssetRegisterPage),
                    UserId = this.User.GetUserId()
                });
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api")]
        public async Task<object> Get(DataSourceLoadOptions loadOptions, Tenant tenant)
        {
            var history = this.employeeAssetService.GetEmployeeAssets(tenant)
                .OrderBy(_ => _.IssuedDate)
                .Select(_ => new AssetsRegisterListViewModel
                {
                    AssetId = _.AssetId,
                    Model = _.Model,
                    AssetStatus = _.AssetStatus,
                    EmployeeId = _.EmployeeId,
                    Notes = _.Notes,
                    Quantity = _.Quantity,
                    RandValue = _.RandValue,
                    ReturnDate = _.ReturnDate,
                    StockRoomNo = _.StockRoomNo,
                    Description = _.Description,
                    IssuedDate = _.IssuedDate,
                    SerialNo = _.SerialNo,
                    Attachments = default
                });

            var result = await DataSourceLoader.LoadAsync(history, loadOptions);
            foreach (var item in result.data.Cast<AssetsRegisterListViewModel>())
            {
                item.Attachments = await this.employeeAssetService.GetAttachmentsAsync(tenant, item.AssetId);
                item.DownloadAttachmentsUrl = this.Url.Action("ExportAttachments", new { area = "Employees", tenant.CompanyId, tenant.EmployeeId, tenant.FrequencyId.Value, assetId = item.AssetId });
            }

            return result;
        }

        [CreateMessage]
        [Permission(SystemAreas.AssetRegister.Area, SystemAreas.AssetRegister.Keys.EmployeeAssetRegisterPage)]
        [HttpPost("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api")]
        public async Task<IActionResult> Post(Tenant tenant, string values)
        {
            var asset = new EmployeeAsset();
            JsonConvert.PopulateObject(values, asset);

            asset.EmployeeId = tenant.EmployeeId;
            asset.Signature = this.fileManagerStorageService.GetBlobUrl(this.User.GetUserId(), tenant.CompanyId, tenant.EmployeeId, this.fileManagerStorageService.GetTempBlobPath(this.User.GetSessionId(), StorageContainers.EmployeeAsset));

            if (!this.TryValidateModel(asset))
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.employeeAssetService.AddAsync(tenant, asset);
            return this.Ok(asset);
        }

        [UpdateMessage]
        [Permission(SystemAreas.AssetRegister.Area, SystemAreas.AssetRegister.Keys.EmployeeAssetRegisterPage)]
        [HttpPut("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api")]
        public async Task<IActionResult> Put(Tenant tenant, long key, string values)
        {
            var asset = await this.employeeAssetService.FindByIdAsync(tenant, key);

            JsonConvert.PopulateObject(values, asset);
            asset.EmployeeId = tenant.EmployeeId;

            asset.Signature = this.fileManagerStorageService.GetBlobUrl(this.User.GetUserId(), tenant.CompanyId, tenant.EmployeeId, this.fileManagerStorageService.GetTempBlobPath(this.User.GetSessionId(), StorageContainers.EmployeeAsset));

            if (!this.TryValidateModel(asset))
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.employeeAssetService.UpdateAsync(tenant, asset);
            return this.Ok(asset);
        }

        [Permission(SystemAreas.AssetRegister.Area, SystemAreas.AssetRegister.Keys.EmployeeAssetRegisterPage)]
        [HttpDelete("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api")]
        public async Task<IActionResult> Delete(Tenant tenant, long key)
        {
            var asset = await this.employeeAssetService.FindByIdAsync(tenant, key);
            await this.employeeAssetService.DeleteAsync(tenant, asset);
            return this.Ok();
        }

        [AllowAll]
        public async Task ExportAttachments(Tenant tenant, long assetId)
        {
            await this.messageBus.PublishMessageAsync(new NotificationStatusMessage
            {
                UserId = this.User.GetUserId(),
                ProgressMessage = new QueuedMessage
                {
                    Id = assetId.ToString(),
                    Title = "Attachments",
                    Description = "Preparing your attachments"
                }
            });

            await this.messageBus.PublishMessageAsync(new ExportAttachmentMessage
            {
                UserId = this.User.GetUserId(),
                CompanyId = tenant.CompanyId,
                EmployeeId = tenant.EmployeeId,
                EntityKey = assetId,
                StorageContainer = StorageContainers.EmployeeAsset,
                FileName = "asset_attachments.zip"
            });
        }
    }
}