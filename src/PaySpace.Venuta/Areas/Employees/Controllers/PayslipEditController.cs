namespace PaySpace.Venuta.Areas.Employees.Controllers
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Globalization;
    using System.Security.Claims;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Data.ResponseModel;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;
    using Microsoft.Extensions.Options;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Calculation.Common.Http.Enums;

    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Common.Azure.TableStorage;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Message;
    using PaySpace.Venuta.Messaging.Notifications;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Components;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Tax;
    using PaySpace.Venuta.Validation.ModelBinders;

    [Area("Employees")]
    [Authorize(Policies.Frequency)]
    [DisplayName(SystemAreas.Payslip.Edit)]
    public class PayslipEditController : Controller
    {
        private readonly IStringLocalizer localizer;
        private readonly ClientSettings clientSettings;
        private readonly IEditPayslipViewModelBuilder builder;
        private readonly IPayslipService payslipService;
        private readonly ICompanyPayslipService companyPayslipService;
        private readonly ICompanyComponentService companyComponentService;
        private readonly ITaxBreakdownService taxBreakdownService;
        private readonly IBulkCaptureCodeService bulkCaptureCodeService;
        private readonly ICalculationWebApiClient calculationApiClient;
        private readonly EmployeeCalculationBreakDownRepository employeeCalculationBreakdownRepository;
        private readonly IMessageBus messageBus;
        private readonly IValidator<PayslipHeader> validator;
        private readonly IReportRetrievalService reportRetrievalService;
        private readonly IReportsMessageService reportsMessageService;

        private const string TaxBreakdownReportId = "TaxBreakdown";

        public PayslipEditController(
            IStringLocalizer<PayslipEditController> localizer,
            IOptions<ClientSettings> clientSettings,
            IEditPayslipViewModelBuilder builder,
            IPayslipService payslipService,
            ICompanyPayslipService companyPayslipService,
            ICompanyComponentService companyComponentService,
            ITaxBreakdownService taxBreakdownService,
            IBulkCaptureCodeService bulkCaptureCodeService,
            ICalculationWebApiClient calculationApiClient,
            EmployeeCalculationBreakDownRepository employeeCalculationBreakdownRepository,
            IMessageBus messageBus,
            IValidator<PayslipHeader> validator,
            IReportRetrievalService reportRetrievalService,
            IReportsMessageService reportsMessageService)
        {
            this.localizer = localizer;
            this.clientSettings = clientSettings.Value;
            this.builder = builder;
            this.payslipService = payslipService;
            this.companyPayslipService = companyPayslipService;
            this.companyComponentService = companyComponentService;
            this.taxBreakdownService = taxBreakdownService;
            this.bulkCaptureCodeService = bulkCaptureCodeService;
            this.calculationApiClient = calculationApiClient;
            this.employeeCalculationBreakdownRepository = employeeCalculationBreakdownRepository;
            this.messageBus = messageBus;
            this.validator = validator;
            this.reportRetrievalService = reportRetrievalService;
            this.reportsMessageService = reportsMessageService;
        }

        public async Task<IActionResult> Index(long companyId, long employeeId, long frequencyId, long? payslipId)
        {
            if (payslipId.HasValue)
            {
                var profile = this.HttpContext.GetSecurityProfile();

                var isValid = await this.payslipService.ValidatePayslipAsync(companyId, employeeId, new[] { payslipId.Value });
                if (isValid == false)
                {
                    var payslip = await this.payslipService.FindByIdAsync(profile, payslipId.Value, false, false, true);
                    if (payslip == null)
                    {
                        return await this.GetPayslipCreateError(companyId, employeeId, frequencyId);
                    }

                    try
                    {
                        var currentEmployeePayslipId = await this.builder.ValidateCurrentSelectedEmployeePayslipIdAsync(profile, companyId, employeeId, payslip.PayslipHeader.PayslipId, payslip.PayslipHeader.CompanyRunId);
                        if (currentEmployeePayslipId != payslipId)
                        {
                            return this.RedirectToAction("Index", new { companyId, employeeId, frequencyId, payslipId = currentEmployeePayslipId });
                        }
                    }
                    catch (EditPayslipViewModelBuilder.CurrentSelectedEmployeeException)
                    {
                        return this.RedirectToAction("Index", "Payslips");
                    }
                    catch (EditPayslipViewModelBuilder.ValidPayslipException)
                    {
                        return this.Forbid();
                    }
                }

                var vm = await this.builder.BuildAsync(profile, companyId, employeeId, frequencyId, payslipId.Value);
                return this.View(vm);
            }
            else
            {
                return this.RedirectToAction("Index", "Payslips");
            }
        }

        private async Task<IActionResult> GetPayslipCreateError(long companyId, long employeeId, long frequencyId)
        {
            await this.ErrorMessageAsync(this.localizer.GetString(ErrorCodes.Payslip.UnableToCreatePayslipDueToCompanySettings));

            return this.RedirectToAction(
                "Index",
                "Payslips",
                new
                {
                    CompanyId = companyId,
                    EmployeeId = employeeId,
                    FrequencyId = frequencyId
                });
        }

        public async Task<IActionResult> TaxBreakdown(long companyId, long employeeId, long runId)
        {
            var reportExists = await this.reportRetrievalService.CheckReportExistsAsync(TaxBreakdownReportId, companyId, this.User.GetUserId(), false, ReportContextLevel.BureauTaxCountry, CancellationToken.None);

            if (reportExists)
            {
                var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
                var userId = this.User.GetUserId();
                var reportUrl = GetReportUrl(companyId, employeeId, runId);
                var parameters = GetReportParameters(companyId, employeeId, runId);

                var reportResponse = await this.messageBus.RequestAsync<ExportCustomReportMessage, ReportResponse>(
                    new ExportCustomReportMessage
                    {
                        Background = false,
                        SendNotifications = false,
                        AccessToken = accessToken,
                        UserId = userId,
                        CompanyId = companyId,
                        ReportUrl = reportUrl,
                        Parameters = parameters,
                        Format = "html",
                        Culture = CultureInfo.CurrentCulture
                    },
                    this.HttpContext.RequestAborted);

                var reportHtml = Encoding.UTF8.GetString(await reportResponse!.Document!.Value);

                var reportDownloadUrl = this.Url.Action("ExportTaxBreakdown", new { companyId, employeeId, runId });
                return this.PartialView("_TaxBreakdownReport", new TaxBreakdownReportViewModel(reportHtml, reportDownloadUrl));
            }

            var taxCalculationBreakdown = await this.taxBreakdownService.GetTaxCalculationBreakdownAsync(employeeId, runId);

            return this.PartialView("_TaxBreakdown", new TaxCalculationBreakdownViewModel
            {
                TaxCalculationBreakdown = taxCalculationBreakdown,
            });
        }

        public async Task ExportTaxBreakdown(long companyId, long employeeId, long runId, long? payslipId)
        {
            var userId = this.User.GetUserId();
            var reportExists = await this.reportRetrievalService.CheckReportExistsAsync(TaxBreakdownReportId, companyId, userId, false, ReportContextLevel.BureauTaxCountry, CancellationToken.None);

            if (reportExists)
            {
                var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);
                var reportUrl = GetReportUrl(companyId, employeeId, runId);
                var parameters = GetReportParameters(companyId, employeeId, runId);
                var notificationHash = ExportReportMessage.GetHash(parameters);

                await this.reportsMessageService.PublishNotificationMessage(userId, notificationHash, "Taxbreakdown.pdf");
                await this.reportsMessageService.PublishExportReportMessage(userId, companyId, notificationHash, reportUrl, parameters, "pdf", this.Request.Host, accessToken!);
                return;
            }

            var context = new ExportTaxBreakdownMessage
            {
                EntityKey = runId,
                UserId = this.User.GetUserId(),
                CompanyId = companyId,
                EmployeeId = employeeId,
                RunId = runId,
                PayslipId = payslipId,
                Tenant = this.Request.Host.Value
            };

            await this.messageBus.PublishMessageAsync(new NotificationStatusMessage
            {
                UserId = this.User.GetUserId(),
                ProgressMessage = new QueuedMessage
                {
                    Id = context.NotificationId,
                    Title = "Tax Breakdown",
                    Description = $"Preparing Tax Calc Breakdown Extract"
                }
            });

            await this.messageBus.PublishMessageAsync(context);
        }

        public async Task<IActionResult> CalculationBreakdown(long companyId, long employeeId, long runId)
        {
            var breakdown = await this.employeeCalculationBreakdownRepository.GetEmployeeCalculationBreakDownAsync(employeeId, runId);
            return this.PartialView("_CalculationBreakdown", breakdown);
        }

        public IActionResult GetCostCentres(long runId)
        {
            return this.PartialView("_CostCentreSplit", runId);
        }

        public IActionResult LumpSumDirective(long payslipId)
        {
            return this.PartialView("_LumpSumDirective", new LumpSumDirectiveViewModel { PayslipId = payslipId });
        }

        [DeleteMessage]
        public async Task<IActionResult> DeletePayslip(long companyId, long employeeId, long payslipId, long frequencyId, long companyRunId)
        {
            // We don't need to get a payslip header from the context as we only need the employeeId and companyRunId for the validator
            var result = await this.validator.ValidateAsync(
                new PayslipHeader
                {
                    EmployeeId = employeeId,
                    CompanyRunId = companyRunId
                },
                _ => _.IncludeRuleSets(RuleSetNames.Delete));

            result.AddToModelState(this.ModelState, null);
            if (this.ModelState.IsValid)
            {
                await this.companyPayslipService.DeletePayslipAsync(companyId, employeeId, frequencyId, companyRunId, payslipId);
                await this.calculationApiClient.CalculateEmployeeAsync(employeeId, companyRunId, EnumRunOrigin.Web, CalcSource.Payslips.ToString());
            }

            return this.RedirectToAction("Index", "Payslips", new { companyId, employeeId, frequencyId });
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/component/select")]
        public IActionResult GetAddComponentForm(long companyId, long employeeId, long frequencyId, long runId, long payslipId, long componentId, PayslipAction payslipAction, [CommaArrayBinder] long[] componentIds)
        {
            return this.PartialView(
                "_Form",
                new OnceOffComponentSelectViewModel(companyId, employeeId, frequencyId, runId, this.clientSettings.GetODataApiUrl(companyId), this.clientSettings.GetMetadataApiUrl(companyId))
                {
                    PayslipAction = payslipAction,
                    ComponentCompanyId = componentId,
                    ComponentCompany = string.Empty,
                    ComponentId = null,
                    PayslipId = payslipId,
                    IgnoreComponentIds = componentIds,
                    EmployeeComponentApiUrl = this.Url.Action("GetCompanyComponent", "EditPayslip", new { employeeId, frequencyId, componentId, runId }),
                    RecordApiUrl = this.Url.Action("AddEmployeeComponent", "EditPayslip", new { employeeId, payslipId, runId, frequencyId }),
                });
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/api/component/edit")]
        public async Task<IActionResult> GetEditComponentForm(long companyId, long employeeId, long frequencyId, long runId, long payslipId, long componentId, long componentCompanyId, PayslipAction payslipAction)
        {
            // BulkCapture
            var costingFigures = await this.bulkCaptureCodeService.GetCostingTimeEntry(runId, componentId).ToListAsync();
            if (costingFigures.Count > 0)
            {
                return this.PartialView("_EditCostingComponentForm", new BulkCaptureCodeViewModel()
                {
                    EmployeeId = employeeId,
                    FrequencyId = frequencyId,
                    RunId = runId,
                    PayslipId = payslipId,
                    BulkCaptureCodes = costingFigures
                });
            }

            return this.PartialView(
                "_Form",
                new OnceOffComponentSelectViewModel(companyId, employeeId, frequencyId, runId, this.clientSettings.GetODataApiUrl(companyId), this.clientSettings.GetMetadataApiUrl(companyId))
                {
                    PayslipAction = payslipAction,
                    ComponentCompanyId = componentCompanyId,
                    ComponentCompany = await this.companyComponentService.GetComponentAliasDescriptionAsync(componentCompanyId),
                    ComponentId = componentId,
                    PayslipId = payslipId,
                    CompanyComponentApiUrl = this.Url.Action("GetCompanyComponent", "EditPayslip", new { employeeId, frequencyId, componentId = componentCompanyId, runId }),
                    EmployeeComponentApiUrl = this.Url.Action("GetEmployeeComponent", "EditPayslip", new { employeeId, frequencyId, payslipId, runId, ComponentId = componentId }),
                    RecordApiUrl = this.Url.Action("UpdateEmployeeComponent", "EditPayslip", new { employeeId, payslipId, runId, frequencyId }),
                });
        }

        [HttpGet("{companyId}/[area]/{employeeId}/[controller]/api/periods")]
        public LoadResult GetPeriods(DataSourceLoadOptions loadOptions, long companyId, long employeeId)
        {
            var periods = this.payslipService.GetPeriods(companyId, employeeId);

            return DataSourceLoader.Load(periods, loadOptions);
        }

        private static Dictionary<string, object> GetReportParameters(long companyId, long employeeId, long runId)
        {
            return new Dictionary<string, object>
            {
                {"CompanyId", companyId},
                {"EmployeeId", employeeId},
                {"RunId", runId}
            };
        }

        private static string GetReportUrl(long companyId, long employeeId, long runId)
        {
            return $"TaxBreakdown?CompanyId={companyId}&EmployeeId={employeeId}&RunId={runId}";
        }
    }
}