namespace PaySpace.Venuta.Areas.Employees.Controllers.Api
{
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Mvc;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using Newtonsoft.Json;

    using PaySpace.Calculation.Common.Http.Enums;
    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Validation.Annotations;

    [NavigationControllerName("PayslipEdit")]
    [Area("Employees")]
    public class EmployeeLumpSumApiController : ControllerBase
    {
        private readonly IValidator<EmployeeLumpSumResult> validator;
        private readonly IEmployeeLumpSumService employeeLumpSumService;
        private readonly ICalculationWebApiClient calculationApiClient;
        private readonly IMapper mapper;

        public EmployeeLumpSumApiController(IValidator<EmployeeLumpSumResult> validator, IEmployeeLumpSumService employeeLumpSumService, ICalculationWebApiClient calculationApiClient, IMapper mapper)
        {
            this.validator = validator;
            this.employeeLumpSumService = employeeLumpSumService;
            this.calculationApiClient = calculationApiClient;
            this.mapper = mapper;
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/{runId}/[controller]/api/taxcodes")]
        public async Task<object> GetTaxCodes(DataSourceLoadOptions loadOptions, long companyId, long frequencyId, long runId)
        {
            return DataSourceLoader.Load(await this.employeeLumpSumService.GetTaxCodesAsync(companyId, frequencyId, runId), loadOptions);
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/payslips/{payslipId}/[controller]/api/lumpsum")]
        public object Get(DataSourceLoadOptions loadOptions, long employeeId, long payslipId)
        {
            var employeeLumpSum = this.employeeLumpSumService
                .GetEmployeeLumpSum(employeeId)
                .Select(_ => new
                {
                    _.EmployeeLumpSumId,
                    _.DirectiveNumber,
                    _.DirectiveAmount,
                    _.DirectiveTax,
                    _.ReferenceNumber,
                    _.DirectiveIssuedDate,
                    _.PayslipId,
                    _.PayslipHeader.CompanyRun.RunDescription,
                    _.TaxFreeDirectiveAmount,
                    PeriodCode = $"{_.PayslipHeader.CompanyRun.PeriodCodeStartDate:MMMM - yyyy}",
                    TaxCode = _.ComponentCompany.OverridingTaxCodeId.HasValue
                            ? _.ComponentCompany.ComponentId + "-" + _.ComponentCompany.OverridingTaxCode.Code
                            : _.ComponentCompany.ComponentId + "-" + _.ComponentCompany.ComponentBureau.TaxCode.Code
                });

            return DataSourceLoader.Load(employeeLumpSum, loadOptions);
        }

        [UpdateMessage]
        [HttpPut("{companyId}/[area]/{employeeId}/{frequencyId}/payslips/{payslipId}/{runId}/[controller]/api/lumpsum")]
        public async Task<IActionResult> Put(long companyId, long payslipId, long runId, long frequencyId, long employeeId, string key, string values)
        {
            var lumpSumKey = JsonConvert.DeserializeObject<LumpSumKey>(key);
            var employeeLumpSum = await this.employeeLumpSumService.FindByIdAsync(lumpSumKey.EmployeeLumpSumId);
            JsonConvert.PopulateObject(values, employeeLumpSum);

            var model = this.mapper.Map<EmployeeLumpSumResult>(employeeLumpSum);
            model.RunId = runId;

            this.employeeLumpSumService.SetDefaultValues(employeeLumpSum);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));
            result.AddToModelState(this.ModelState, null);

            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.employeeLumpSumService.UpdateAsync(companyId, employeeId, runId, employeeLumpSum);
            await this.calculationApiClient.CalculateEmployeeAsync(employeeId, runId, EnumRunOrigin.Web, CalcSource.ApiEmployeeLumpSum.ToString());

            return this.Ok();
        }

        [CreateMessage]
        [HttpPost("{companyId}/[area]/{employeeId}/{frequencyId}/payslips/{payslipId}/{runId}/[controller]/api/lumpsum")]
        public async Task<IActionResult> Post(long companyId, long payslipId, long runId, long frequencyId, long employeeId, string values)
        {
            var model = new EmployeeLumpSumResult();
            JsonConvert.PopulateObject(values, model);

            model.EmployeeId = employeeId;
            model.PayslipId = payslipId;
            model.RunId = runId;

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));
            result.AddToModelState(this.ModelState, null);

            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.employeeLumpSumService.AddAsync(companyId, employeeId, frequencyId, runId, payslipId, model);
            await this.calculationApiClient.CalculateEmployeeAsync(employeeId, runId, EnumRunOrigin.Web, CalcSource.ApiEmployeeLumpSum.ToString());

            return this.Ok(model);
        }

        [DeleteMessage]
        [HttpDelete("{companyId}/[area]/{employeeId}/{frequencyId}/payslips/{payslipId}/{runId}/[controller]/api/lumpsum")]
        public async Task<IActionResult> Delete(long companyId, long employeeId, long frequencyId, long payslipId, long runId, string key)
        {
            var lumpSumKey = JsonConvert.DeserializeObject<LumpSumKey>(key);
            var result = await this.validator.ValidateAsync(new EmployeeLumpSumResult
            {
                EmployeeId = employeeId,
                RunId = runId,
                PayslipId = lumpSumKey.PayslipId,
                EmployeeLumpSumId = lumpSumKey.EmployeeLumpSumId,
                CurrentPayslipId = payslipId
            }, options => options.IncludeRuleSets(RuleSetNames.Delete));

            result.AddToModelState(this.ModelState, null);

            if (!this.ModelState.IsValid)
            {
                return this.BadRequest(this.ModelState.ToFullErrorString());
            }

            await this.employeeLumpSumService.DeleteAsync(companyId, employeeId, frequencyId, payslipId, lumpSumKey.PayslipId, runId, lumpSumKey.EmployeeLumpSumId);
            await this.calculationApiClient.CalculateEmployeeAsync(employeeId, runId, EnumRunOrigin.Web, CalcSource.ApiEmployeeLumpSum.ToString());

            return this.Ok();
        }

        public class LumpSumKey
        {
            public long EmployeeLumpSumId { get; set; }

            public long PayslipId { get; set; }
        }
    }
}