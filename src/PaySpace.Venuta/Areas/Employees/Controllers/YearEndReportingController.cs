namespace PaySpace.Venuta.Areas.Employees.Controllers
{
    using System;
    using System.ComponentModel;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees.SG;

    [Area("Employees")]
    [DisplayName(SystemAreas.YearEndReporting.Area)]
    public class YearEndReportingController : Controller
    {
        private readonly ICompanyService companyService;
        private readonly ICountryTaxYearService countryTaxYearService;
        private readonly ISingaporeEmployeeIR8AService singaporeEmployeeIR8AService;
        private readonly ISingaporeEmployeeAppendix8BService singaporeEmployeeAppendix8BService;

        public YearEndReportingController(
            ICompanyService companyService,
            ICountryTaxYearService countryTaxYearService,
            ISingaporeEmployeeIR8AService singaporeEmployeeIR8AService,
            ISingaporeEmployeeAppendix8BService singaporeEmployeeAppendix8BService)
        {
            this.companyService = companyService;
            this.countryTaxYearService = countryTaxYearService;
            this.singaporeEmployeeIR8AService = singaporeEmployeeIR8AService;
            this.singaporeEmployeeAppendix8BService = singaporeEmployeeAppendix8BService;
        }

        [HttpGet]
        public async Task<IActionResult> Index(long companyId, long employeeId, int? taxYearId, int? selectedIndex)
        {
            var taxCountryId = this.companyService.GetTaxCountryId(companyId);
            var currentTaxYearId = taxYearId ?? await this.countryTaxYearService.GetCurrentTaxYearIdAsync(taxCountryId);
            var canAddNewIR8ARecord = await this.singaporeEmployeeIR8AService.CanAddRecordAsync(currentTaxYearId, employeeId);
            var canAddNewAppendix8BRecord = this.singaporeEmployeeAppendix8BService.CanAddRecord(currentTaxYearId, employeeId);

            var allowEditForTaxYear = this.IsAllowEditForTaxYear(await this.countryTaxYearService.GetCountryTaxYearAsync(currentTaxYearId, taxCountryId));

            return this.View(new YearEndReportingViewModel
            {
                TaxYearId = currentTaxYearId,
                CompanyId = companyId,
                EmployeeId = employeeId,
                AllowNewIR8ARecord = canAddNewIR8ARecord,
                AllowNewAppendix8BRecord = canAddNewAppendix8BRecord,
                AllowEditForTaxYear = allowEditForTaxYear,
                TaxYearApiUrl = this.Url.Action("GetTaxYears", "YearEndReportingApi", new { area = "Employees", CompanyId = companyId, EmployeeId = employeeId }),
                IR8AEditUrl = this.Url.Action(nameof(this.IR8AEdit), new { area = "Employees", CompanyId = companyId, EmployeeId = employeeId }),
                EditAppendix8BUrl = this.Url.Action(nameof(this.EditAppendix8B), new { area = "Employees", CompanyId = companyId, EmployeeId = employeeId }),
                SelectIndexTab = selectedIndex ?? 0,
                AmendmentGuideLink = "https://www.iras.gov.sg/taxes/individual-income-tax/employers/auto-inclusion-scheme-(ais)-for-employment-income/amend-submitted-records"
            });
        }

        [HttpGet]
        public async Task<IActionResult> IR8AEdit(long companyId, long employeeId, string taxYear, bool allowEditForTaxYear, long? employeeIR8AId)
        {
            var taxCountryId = this.companyService.GetTaxCountryId(companyId);
            var currentTaxYearId = await this.countryTaxYearService.GetCountryTaxYearIdByDateAsync(taxCountryId, new DateTime(int.Parse(taxYear), 1, 1));

            return this.View(new EmployeeIR8AViewModel
            {
                EmployeeIR8AId = employeeIR8AId,
                TaxYear = taxYear,
                CompanyId = companyId,
                EmployeeId = employeeId,
                TaxYearId = currentTaxYearId,
                AllowEditForTaxYear = allowEditForTaxYear,
                IndexUrl = this.Url.Action(nameof(this.Index))
            });
        }

        [HttpGet]
        public IActionResult EditAppendix8B(long companyId, long employeeId, string taxYear, bool allowEditForTaxYear, long? employeeAppendix8BId, bool? copy)
        {
            return this.View("EditAppendix8B", new EmployeeAppendix8BViewModel
            {
                EmployeeAppendix8BId = employeeAppendix8BId,
                AllowEdit = true,
                BaseUrl = this.Url.Action(nameof(this.Index), new { area = "Employees", CompanyId = companyId, EmployeeId = employeeId, Selectedindex = 2 }),
                TaxYear = taxYear,
                Copy = copy ?? false,
                EmployeeId = employeeId,
                AllowNewAppendix8BRecord = allowEditForTaxYear,
                AllowEditForTaxYear = allowEditForTaxYear
            });
        }

        private bool IsAllowEditForTaxYear(CountryTaxYear taxYear)
        {
            var editableYear = DateTime.Now.Year;

            if (taxYear.YearStartDate.Year >= editableYear)
            {
                return true;
            }
            else
            {
                return taxYear.YearStartDate.Year < editableYear && taxYear.YearStartDate.Year >= editableYear - 4;
            }
        }
    }
}
