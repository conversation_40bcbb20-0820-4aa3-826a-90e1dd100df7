namespace PaySpace.Venuta.Areas.Employees.Controllers
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports;

    [Area("Employees")]
    [DisplayName(SystemAreas.Performance.Journal)]
    public class JournalController : Controller
    {
        private readonly string ReportTemplate = "Bureau/PerformanceJournal/PerformanceJournal.repx";
        private readonly IReportsMessageService reportsMessageService;
        private readonly IEmployeeService employeeService;

        public JournalController(IReportsMessageService reportsMessageService, IEmployeeService employeeService)
        {
            this.reportsMessageService = reportsMessageService;
            this.employeeService = employeeService;
        }

        public IActionResult Index(Tenant tenant)
        {
            var allowEdit = this.HttpContext.GetSecurityProfile().IsFullAccess(SystemAreas.Performance.Journal, SystemAreas.Performance.Journal);
            var vm = new JournalViewModel { CompanyId = tenant.CompanyId, EmployeeId = tenant.EmployeeId, FrequencyId = tenant.FrequencyId, AllowEdit = allowEdit };

            return this.View(vm);
        }

        [HttpGet("{companyId}/[area]/{employeeId}/{frequencyId}/[controller]/report")]
        public async Task<IActionResult> Download(long companyId, long employeeId)
        {
            var allowEdit = this.HttpContext.GetSecurityProfile().IsFullAccess(SystemAreas.Performance.Journal, SystemAreas.Performance.Journal);
            if (!allowEdit)
            {
                return this.Forbid();
            }

            var employeeNumber = await this.employeeService.GetEmployeeNumberAsync(employeeId);
            var userId = this.User.GetUserId();
            var reportParameters = new Dictionary<string, object>
            {
                { "EmpNumber", employeeNumber },
                { "CompanyId", companyId },
                { "InclTerminations", false},
                { "EffectiveDateTo", null},
                { "EffectiveDateFrom",null},
                { "UserId", userId},
                { "PositionId", null},
                { "OrgUnitId", null}
            };

            var reportHash = ExportReportMessage.GetHash(reportParameters);
            var reportUrl = $"{this.ReportTemplate}?CompanyId={companyId}";
            var accessToken = await this.HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken);

            await this.reportsMessageService.PublishNotificationMessage(
                userId,
                reportHash,
                "Performance Journal Report");

            await this.reportsMessageService.PublishExportReportMessage(
                userId,
                companyId,
                reportHash,
                reportUrl,
                reportParameters,
                "xlsx",
                null,
                accessToken);

            return this.Ok();
        }
    }
}