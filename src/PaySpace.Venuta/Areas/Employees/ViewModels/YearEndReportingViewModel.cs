namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    public class YearEndReportingViewModel
    {
        public long EmployeeId { get; set; }

        public long CompanyId { get; set; }

        public string IR8AEditUrl { get; set; }

        public string EditAppendix8BUrl { get; set; }

        public string TaxYearApiUrl { get; set; }

        public int? TaxYearId { get; set; }

        public bool AllowEditForTaxYear { get; set; }

        public bool AllowNewIR8ARecord { get; set; }

        public bool AllowNewAppendix8BRecord { get; set; }

        public int? SelectIndexTab { get; set; }

        public string AmendmentGuideLink { get; set; }
    }
}
