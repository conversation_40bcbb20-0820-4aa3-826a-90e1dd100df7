namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    using System;
    using System.ComponentModel.DataAnnotations;

    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [SecurityDisplayName(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Application)]
    public class LeaveAdjustmentViewModel
    {
        public long CompanyId { get; set; }

        public long EmployeeId { get; set; }

        public long LeaveAdjustmentId { get; set; }

        [Display(Name = "lblPeriod")]
        public long CompanyRunId { get; set; }

        [Display(Name = "lblBucket")]
        public long CompanyLeaveSetupId { get; set; }

        [Display(Name = "lblLeaveReason")]
        public long LeaveReasonId { get; set; }

        [Display(Name = "lblNoDays")]
        public double TotalDays { get; set; }

        [Display(Name = "lblLeaveType")]
        public long LeaveType { get; set; }

        public RunStatus RunStatus { get; set; }

        public bool HideOnLeaveApplication { get; set; }

        public bool OverrideBalance { get; set; }

        public bool IsReadOnly { get; set; }

        public bool IsLinkedToLeaveScheme { get; set; }

        [MaxLength(500)]
        [Display(Name = "lblComments")]
        public string Comments { get; set; }

        [MaxLength(50)]
        [Display(Name = "lblReference")]
        public string Reference { get; set; }

        public string RunDescription { get; set; }

        public bool IsAttachmentReadOnly { get; set; }

        public DateTime PayRateEffectiveDate { get; set; }

        [Display(Name = "lblHistoricConcession")]
        public long? HistoricalConcessionId { get; set; }

        public bool IsBrazilianCompany { get; set; }
    }
}