namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;

    using Newtonsoft.Json;

    using PaySpace.Data.Models.Converters;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.ViewModels;

    public record EmploymentActionType(string Text, int Value);

    [DisplayName(SystemAreas.EmploymentStatus.Area)]
    public class EmploymentStatusViewModel
    {
        // This is the nav link that is needed when navigating to classic and back to nextgen
        // for Terminations when an employee has subordinates.
        public long NavLinkId { get; } = 60413;

        public EmployeeEmploymentStatus EmploymentStatus { get; set; }

        public CustomFormViewModel CustomForm { get; set; }

        public List<CustomFormViewModel> CustomForms { get; set; }

        public string DefaultIdString { get; set; }

        public string CustomDate1Name { get; set; }

        public string CustomDate2Name { get; set; }

        public string OnboardingNotificationsUrl { get; set; }

        public long CompanyId { get; set; }

        public long FrequencyId { get; set; }

        public bool BulkCaptureRedirect { get; set; }

        public bool IsLatest { get; set; }

        public bool IsSouthAfrica { get; set; }

        public bool IsMauritius { get; set; }

        public bool IsUnitedKingdom { get; set; }

        public bool IsCanada { get; set; }

        public bool Terminated { get; set; }

        public bool ShowCustomDate1 { get; set; }

        public bool ShowCustomDate2 { get; set; }

        public bool RequireCustomDate1 { get; set; }

        public bool RequireCustomDate2 { get; set; }

        public bool RequireTaxReferenceNumber { get; set; }

        public bool ShowCasualWorkerCheck { get; set; }

        public bool ReadOnly { get; set; }

        public bool HasPendingInboxes { get; set; }

        public bool HasCustomFields { get; set; }

        public bool ClearEmploymentDates { get; set; }

        public bool EnableStability { get; set; }

        public IList<EmploymentActionType> EmploymentActionTypes { get; set; }

        public IList<EnumNaturePerson> NaturePersons { get; set; }

        public IList<EnumIdentityType> IdTypes { get; set; }

        public IList<EnumTaxStatus> TaxStatuses { get; set; }

        public IList<EnumDentalBenefit> DentalBenefits { get; set; }

        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        public DateTime? MaxEffectiveDate { get; set; }

        public IEnumerable<DateTime> DisabledTerminationDates { get; set; }

        public string ActiveJobWarningMessage { get; set; }

        public bool IsIreland { get; set; }
    }
}