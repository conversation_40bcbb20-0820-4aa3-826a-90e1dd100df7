namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using Maddalena;

    using Newtonsoft.Json;

    using PaySpace.Data.Models.Converters;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Validation.Annotations;

    [DisplayName(SystemAreas.Profile.Basic)]
    public class EmployeeProfileViewModel : CustomFieldEntity<EmployeeCustomFieldValue>, IEmployeeName
    {
        [CountryNotRequired(CountryCode.CA, CountryCode.IE, CountryCode.MY, CountryCode.IN)]
        [Display(Name = "lblTitle")]
        public int? TitleId { get; set; }

        [StringLength(100)]
        [Display(Name = "lblMiddleName")]
        public string EmpMiddleName { get; set; }

        [CountryNotRequired(CountryCode.CA, CountryCode.IE, CountryCode.MY, CountryCode.IN, CountryCode.AU)]
        [StringLength(5)]
        [Display(Name = "lblInitial")]
        public string EmpInitials { get; set; }

        [Display(Name = "lblHomeNumber")]
        public string HomeNumber { get; set; }

        [Display(Name = "lblWorkNumber")]
        public string WorkNumber { get; set; }

        [Display(Name = "lblCellNumber")]
        public string CellNumber { get; set; }

        [StringLength(6)]
        [Display(Name = "lblWorkEx")]
        public string WorkExtension { get; set; }

        [CountryRequired(CountryCode.IE)]
        [Display(Name = "lblLanguage")]
        public int? LanguageId { get; set; }

        [CountryNotRequired(CountryCode.IE)]
        [Display(Name = "lblGender")]
        public int? GenderId { get; set; }

        [CountryRequired(CountryCode.ZA, CountryCode.SG)]
        [Display(Name = "lblRace")]
        public int? RaceId { get; set; }

        [CountryNotRequired(CountryCode.IE)]
        [Display(Name = "lblNationality")]
        public int? NationalityId { get; set; }

        [CountryNotRequired(CountryCode.NA, CountryCode.CA, CountryCode.IE, CountryCode.MY, CountryCode.IN)]
        [Display(Name = "lblCitiz")]
        public int? CitizenshipId { get; set; }

        public bool? Disabled { get; set; }

        [Display(Name = "lblDisabledT")]
        public int? DisabledTypeId { get; set; }

        [DropDownList]
        [Display(Name = "lblUifexemption")]
        public int? UifExemptionId { get; set; }

        [DropDownList]
        [Display(Name = "lblSdlexemption")]
        public int? SdlExemptionId { get; set; }

        [Display(Name = "lblForeignNat")]
        public bool? ForeignNational { get; set; }

        [CountryRequired(CountryCode.MY)]
        [Display(Name = "lblMaritalStat")]
        public int? MaritalStatusId { get; set; }

        [StringLength(50)]
        [Display(Name = "lblMaidenName")]
        public string EmpMaidenName { get; set; }

        [Display(Name = "lblReportId")]
        public int? ReportId { get; set; }

        [CompanyDisplayName(Code = CompanySettingCode.BasicProfile.CustomFieldOne)]
        public string CustomFieldValue { get; set; }

        [CompanyDisplayName(Code = CompanySettingCode.BasicProfile.CustomFieldTwo)]
        public string CustomFieldValue2 { get; set; }

        [Display(Name = "lblEtiExempt")]
        public bool? EtiExempt { get; set; }

        [DropDownList]
        [Display(Name = "lblSubStandardIndustryCodeId")]
        public long? SubStandardIndustryCodeId { get; set; } = 0;

        [Display(Name = "lblEmergContName")]
        public string EmergencyContactName { get; set; }

        [Display(Name = "lblEmergContNumber")]
        public string EmergencyContactNumber { get; set; }

        [Display(Name = "lblEmergContAddress")]
        public string EmergencyContactAddress { get; set; }

        [Display(Name = "lblIsEmpRetired")]
        public bool? IsRetired { get; set; }

        [Display(Name = "txtimage")]
        public byte[] Image { get; set; }

        public IList<EmployeeAddressViewModel> Address { get; set; } = new List<EmployeeAddressViewModel>();

        [Display(Name = "lblStandardIndustryCodeHeader")]
        public int StandardIndustryCodeHeader { get; set; } = 0;

        public long EmployeeId { get; set; }

        public long CompanyId { get; set; }

        [Display(Name = "lblEmpNumber")]
        [CompanyRequired(CompanySettingCode.EmployeeNumberRequired)]
        public string? EmployeeNumber { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "lblFirstName")]
        public string? FirstName { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "lblLastName")]
        public string? LastName { get; set; }

        [Display(Name = "lblPrefName")]
        [Column("EmpPreferredName")]
        [CountryRequired(CountryCode.SG, CountryCode.MY, CountryCode.IN)]
        public string? PreferredName { get; set; }

        [Display(Name = "lblName")]
        public string? FullName { get; }

        public string? ShortName => (string.IsNullOrWhiteSpace(this.PreferredName) ? this.FirstName : this.PreferredName) + ". " + this.LastName?.Substring(0, 1);

        public string? Initials => !string.IsNullOrEmpty(this.FirstName) && string.IsNullOrEmpty(this.LastName) ? $"{this.FirstName[0]}{this.LastName[0]}".ToUpper() : null;

        [Display(Name = "lblEmailAdd")]
        public string? Email { get; set; }

        [Required]
        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        [Display(Name = "lblBirthdate")]
        public DateTime? Birthday { get; set; }

        public long? CompanyFrequencyId { get; set; }

        [DataType(DataType.Date)]
        [JsonConverter(typeof(DateConverter))]
        public DateTime? EffectiveDate { get; set; }

        public EmailAddressStatus CheckValidId { get; set; }

        public bool ShowDisabilityType { get; set; }

        public bool IsFullAccess { get; set; }

        public int? TaxCountryLegalWorkAge { get; set; }

        public bool ShowLegalWorkingAge { get; set; }
    }
}