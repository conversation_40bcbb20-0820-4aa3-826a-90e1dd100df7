namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    using System.Collections.Generic;
    using System.ComponentModel;

    using DevExtreme.AspNet.Mvc.Builders;

    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Model;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.ViewModels;

    public enum OnboardingTemplateType
    {
        Basic,
        Advanced
    }

    [DisplayName(SystemAreas.Profile.Basic)]
    public class ProfileViewModel : EmployeeProfileViewModelBase
    {
        public EmployeeProfileViewModel Employee { get; set; }

        public CustomFormViewModel CustomForm { get; set; }

        public List<CustomFormViewModel> CustomForms { get; set; }

        public EmployeeEmploymentStatus EmploymentStatus { get; set; }

        public EmployeeProfileSettings EmployeeProfileSettings { get; set; }

        public bool ShowExemptionsAndOthersPanel { get; set; }

        public bool CanDeleteEmployee { get; set; }

        public bool CanCreateEmployee { get; set; } = true;

        public bool HasEmployeeImage { get; set; }

        public bool ShowNoAddressWarning { get; set; }

        public string TaxCountryCode { get; set; }

        public long? EmployeeHistoryId { get; set; }

        public long CompanyId { get; set; }

        public bool CompanyGenerateEmployeeNumber { get; set; } = false;

        public bool IsCurrentlyInWorkflow { get; set; }

        public string RequiredCustomFormCode { get; set; }

        public string DynamicFormId { get; set; }

        public string DynamicFormName { get; set; }

        public bool ExcludesAdvancedWorkflowFromQuickOnboarding { get; set; }

        public List<DynamicFormBuilderModel> DynamicForms { get; set; }

        public int TaxCountryId { get; set; }
    }

    public record EmployeeProfileTabViewModel(FormTabBuilder<EmployeeProfileViewModel> Tab, ProfileViewModel ViewModel);
}