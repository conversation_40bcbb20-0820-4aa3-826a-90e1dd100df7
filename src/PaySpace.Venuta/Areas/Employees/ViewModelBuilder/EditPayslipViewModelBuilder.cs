namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Areas.Employees.Controllers;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Components;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Tax;

    public interface IEditPayslipViewModelBuilder
    {
        Task<PayslipEditViewModel> BuildAsync(ISecurityProfile profile, long companyId, long employeeId, long frequencyId, long payslipId);

        Task<long> ValidateCurrentSelectedEmployeePayslipIdAsync(ISecurityProfile profile, long companyId, long employeeId, long payslipId, long companyRunId);
    }

    public class EditPayslipViewModelBuilder : IEditPayslipViewModelBuilder
    {
        private readonly IStringLocalizer localizer;
        private readonly IPayslipService payslipService;
        private readonly IPayslipSettingsService payslipSettingsService;
        private readonly IPayRateService payRateService;
        private readonly ICompanyService companyService;
        private readonly ICompanyRunService companyRunService;
        private readonly ITaxCalculationErrorService taxCalculationErrorService;
        private readonly IBulkCaptureCodeService bulkCaptureCodeService;
        private readonly IReportRetrievalService reportRetrievalService;

        public EditPayslipViewModelBuilder(
            IStringLocalizer<PayslipEditController> localizer,
            IPayslipService payslipService,
            IPayslipSettingsService payslipSettingsService,
            IPayRateService payRateService,
            ICompanyService companyService,
            ICompanyRunService companyRunService,
            ITaxCalculationErrorService taxCalculationErrorService,
            IBulkCaptureCodeService bulkCaptureCodeService,
            IReportRetrievalService reportRetrievalService)
        {
            this.localizer = localizer;
            this.payslipService = payslipService;
            this.payslipSettingsService = payslipSettingsService;
            this.payRateService = payRateService;
            this.companyService = companyService;
            this.companyRunService = companyRunService;
            this.taxCalculationErrorService = taxCalculationErrorService;
            this.bulkCaptureCodeService = bulkCaptureCodeService;
            this.reportRetrievalService = reportRetrievalService;
        }

        public virtual async Task<PayslipEditViewModel> BuildAsync(ISecurityProfile profile, long companyId, long employeeId, long frequencyId, long payslipId)
        {
            var payslip = await this.payslipService.FindByIdAsync(profile, payslipId, false, false, true, false);
            var companyRunPeriod = await this.companyRunService.GetRunPeriodAsync(payslip.PayslipHeader.CompanyRunId);
            var isRunByTakeOnRun = await this.payslipService.IsRunByTakeOnRunAsync(payslip);
            var allowEdit = payslip.CompanyId == companyId && payslip.PayslipHeader.CompanyRun.CompanyFrequencyId == frequencyId && !isRunByTakeOnRun;
            var payslipNotificationCount = await this.taxCalculationErrorService.GetTaxCalculationError(employeeId, payslip.PayslipHeader.CompanyRunId, false, TaxErrorType.Both).CountAsync();
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var useBreakdownReport = await this.reportRetrievalService
                    .CheckReportExistsAsync("TaxBreakdown", companyId, profile.UserId, false, ReportContextLevel.BureauTaxCountry, CancellationToken.None);

            return new PayslipEditViewModel
            {
                CompanyId = companyId,
                EmployeeId = employeeId,
                PayslipEmployeeId = payslip.PayslipHeader.EmployeeId,
                FrequencyId = frequencyId,
                RunId = payslip.PayslipHeader.CompanyRunId,
                AlreadyBeenPaid = payslip.PayslipHeader.Paid ?? false,
                PayslipId = payslip.PayslipHeader.PayslipId,
                Payslip = payslip,
                CompanyRunPeriod = companyRunPeriod,
                AllowCreate = payslip.PayslipHeader.CompanyRun.Status == RunStatus.Open && profile.IsFullAccess(SystemAreas.Payslip.Edit, SystemAreas.Payslip.Edit) && allowEdit,
                AllowEdit = allowEdit,
                IsOpenRunByTakeOnRun = isRunByTakeOnRun && payslip.PayslipHeader.CompanyRun.Status == RunStatus.Open,
                DisplayPayslipDeleteButton = await this.payslipSettingsService.ShowDeletePayslipButtonAsync(profile, companyId, employeeId, payslip.PayslipHeader.CompanyRun) && allowEdit,
                DisplayCalculationBreakdownButton = profile.UserType is UserType.Bureau or UserType.Agency or UserType.Company,
                DisplayTaxCalculationBreakdownButton = useBreakdownReport || await this.payslipSettingsService.ShowTaxCalculationBreakdownButtonAsync(payslip.PayslipHeader.EmployeeId, payslip.PayslipHeader.CompanyRunId),
                DisplayTaxCalculationErrorButton = await this.payslipSettingsService.ShowTaxCalculationErrorButtonAsync(employeeId, companyRunPeriod.RunId, companyRunPeriod.Status) && allowEdit,
                DisplayEmployeeLumpSumButton = (payslip.PayslipHeader.CompanyRun.Status != RunStatus.Closed || payslip.PayslipHeader.CompanyRun.TakeOnRun)
                    && profile.IsFullAccess(SystemAreas.Payslip.Edit, SystemAreas.Payslip.Edit),
                DisplayCommentsButton = payslip.PayslipHeader.CompanyRun.Status == RunStatus.Open && profile.IsFullAccess(SystemAreas.Payslip.Edit, SystemAreas.Payslip.Edit) && allowEdit,
                EnableNetPayViewOption = payslip.PayslipHeader.CompanyRun.Status == RunStatus.Open && profile.IsFullAccess(SystemAreas.Payslip.Edit, SystemAreas.Payslip.Edit) && allowEdit,
                DownloadDataSource = await this.GetDownloadDataSourceAsync(companyId, employeeId, allowEdit),
                PayslipNotificationCount = payslipNotificationCount,
                DisplayCostCentreSplitButton = await this.bulkCaptureCodeService.HasCostCentreSplittingAsync(employeeId, companyRunPeriod.RunId),
                PayslipSummary = [new(companyRunPeriod.PeriodStartDate, companyRunPeriod.PeriodEndDate, payslip.PayslipHeader.Hours, payslip.PayslipHeader.HourlyRate, payslip.NetPay, null)],
                ExternalPayslipRef = payslip.PayslipHeader.ExternalPayslipRef,
                IsUnitedKingdom = taxCountryId == (int)TaxCountry.UnitedKingdom
            };
        }

        public async Task<long> ValidateCurrentSelectedEmployeePayslipIdAsync(ISecurityProfile profile, long companyId, long employeeId, long payslipId, long companyRunId)
        {
            var currentSelectedEmployeePayslipId = await this.payslipService.GetPayslips(companyId, employeeId)
                .Where(_ => _.CompanyRunId == companyRunId)
                .Select(_ => _.PayslipId)
                .SingleOrDefaultAsync();

            if (currentSelectedEmployeePayslipId == default)
            {
                throw new CurrentSelectedEmployeeException();
            }

            // reset the payslip data with the new payslipId from the same run period
            var payslip = await this.payslipService.FindByIdAsync(profile, currentSelectedEmployeePayslipId, false, false, true);
            var isValidPayslip = await this.payslipService.ValidatePayslipAsync(companyId, employeeId, new[] { payslip.PayslipHeader.PayslipId });
            if (isValidPayslip == false)
            {
                throw new ValidPayslipException();
            }

            return payslip.PayslipHeader.PayslipId;
        }

        private async Task<IEnumerable<string>> GetDownloadDataSourceAsync(long companyId, long employeeId, bool allowEdit)
        {
            var payrate = await this.payRateService.GetPayRateAsync(employeeId, DateTime.Today);
            var currencyId = await this.companyService.GetTaxCurrencyIdAsync(companyId);
            var displayInHomeCurrency = this.payslipSettingsService.ShowViewHomeCurrency(payrate?.CurrencyId, currencyId);

            return displayInHomeCurrency && allowEdit
                ? new string[] { this.localizer.GetString("DownloadHomeCurrency") }
                : Enumerable.Empty<string>();
        }

        public class CurrentSelectedEmployeeException : UnauthorizedAccessException
        {
        }

        public class ValidPayslipException : UnauthorizedAccessException
        {
        }
    }
}