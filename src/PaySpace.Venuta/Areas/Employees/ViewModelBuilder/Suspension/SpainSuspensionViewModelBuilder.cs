namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Suspension
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.ES)]
    public class SpainSuspensionViewModelBuilder : SuspensionViewModelBuilder
    {
        private readonly IStringLocalizer<EmployeeSuspension> localizer;
        private static readonly (string Text, TransactionType Value)[] TransactionTypes =
        {
            ("lblAbsenteeism", TransactionType.Absenteeism),
            ("lblTemporaryIncapacity", TransactionType.TemporaryIncapacity)
        };

        public SpainSuspensionViewModelBuilder(
            IEmployeeService employeeService,
            IEmploymentStatusService employmentStatusService,
            IStringLocalizer<EmployeeSuspension> localizer,
            IEnumService enumService,
            ICompanyService companyService)
            : base(employeeService, employmentStatusService, localizer, enumService, companyService)
        {
            this.localizer = localizer;
        }

        public override async Task<EmployeeSuspensionViewModel> BuildViewModelAsync(ISecurityProfile profile, Tenant tenant)
        {
            var vm = await base.BuildViewModelAsync(profile, tenant);
            vm.TransactionTypes = TransactionTypes
                .Select(_ => new { Text = this.localizer.GetString(_.Text), _.Value }).ToArray();
            return vm;
        }
    }
}