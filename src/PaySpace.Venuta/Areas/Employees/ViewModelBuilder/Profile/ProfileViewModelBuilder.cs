namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Profile
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Bureau;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.ViewModels;

    public interface IProfileViewModelBuilder
    {
        Task<ProfileViewModel> BuildViewModelAsync(ClaimsPrincipal user, ISecurityProfile profile, Tenant tenant);

        Task<ProfileViewModel> BuildViewModelAsync(ClaimsPrincipal user, ISecurityProfile profile, long companyId, Employee employee);

        Task<ProfileViewModel> BuildCreateViewModelAsync(ISecurityProfile profile, long companyId, bool employeeSelfService);

        Task<ProfileViewModel> BuildCreateViewModelAsync(ISecurityProfile profile, long companyId, Employee employee, bool employeeSelfService);
    }

    public class ProfileViewModelBuilder : IProfileViewModelBuilder
    {
        private readonly IStringLocalizer localizer;
        private readonly IEmployeeService employeeService;
        private readonly IEmployeeProfileService employeeProfileService;
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly IEmployeeProfileSettingService employeeProfileSettingService;
        private readonly IEmployeeSecurityService employeeSecurityService;
        private readonly ICustomFormService<EmployeeCustomForm> customFormService;
        private readonly IMapper mapper;
        private readonly IImageService imageService;
        private readonly ICompanyService companyService;
        private readonly ICompanyAddressService companyAddressService;
        private readonly IEmailAddressService emailAddressService;
        private readonly ICustomFieldService customFieldService;
        private readonly ITaxCountryService taxCountryService;
        private readonly ICompanyRunService companyRunService;
        private readonly ITenantProvider tenantProvider;

        public ProfileViewModelBuilder(
            IStringLocalizer<Employee> localizer,
            IEmployeeService employeeService,
            IEmployeeProfileService employeeProfileService,
            IEmploymentStatusService employmentStatusService,
            IEmployeeProfileSettingService employeeProfileSettingService,
            IEmployeeSecurityService employeeSecurityService,
            ICustomFormService<EmployeeCustomForm> customFormService,
            IMapper mapper,
            IImageService imageService,
            ICompanyService companyService,
            ICompanyAddressService companyAddressService,
            IEmailAddressService emailAddressService,
            ICustomFieldService customFieldService,
            ITaxCountryService taxCountryService,
            ICompanyRunService companyRunService,
            ITenantProvider tenantProvider)
        {
            this.localizer = localizer;
            this.employeeService = employeeService;
            this.employeeProfileService = employeeProfileService;
            this.employmentStatusService = employmentStatusService;
            this.employeeProfileSettingService = employeeProfileSettingService;
            this.employeeSecurityService = employeeSecurityService;
            this.customFormService = customFormService;
            this.mapper = mapper;
            this.imageService = imageService;
            this.companyService = companyService;
            this.companyAddressService = companyAddressService;
            this.emailAddressService = emailAddressService;
            this.customFieldService = customFieldService;
            this.taxCountryService = taxCountryService;
            this.companyRunService = companyRunService;
            this.tenantProvider = tenantProvider;
        }

        public virtual async Task<ProfileViewModel> BuildViewModelAsync(ClaimsPrincipal user, ISecurityProfile profile, Tenant tenant)
        {
            var employee = await this.employeeService.FindByIdWithAddressAsync(tenant.CompanyId, tenant.EmployeeId);
            return await this.BuildViewModelAsync(user, profile, tenant.CompanyId, employee);
        }

        public virtual async Task<ProfileViewModel> BuildViewModelAsync(ClaimsPrincipal user, ISecurityProfile profile, long companyId, Employee employee)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var enumTaxCountry = await this.taxCountryService.GetEnumTaxCountryAsync(taxCountryId);

            var employeeModel = this.mapper.Map<EmployeeProfileViewModel>(employee);

            employeeModel.ShowDisabilityType = true;
            employeeModel.CheckValidId = await this.emailAddressService.GetEmailAddressStatusAsync(employeeModel.Email);
            employeeModel.TaxCountryLegalWorkAge = enumTaxCountry?.EmployeeLegalWorkAge;
            employeeModel.ShowLegalWorkingAge = enumTaxCountry?.EmployeeLegalWorkAge != null && employee.Age < employeeModel.TaxCountryLegalWorkAge;

            var employeeSelfService = user.IsInRole(UserTypeCodes.Employee);

            this.SortAddresses(employeeModel);

            var customForms = await this.GetCustomFormsAsync(profile, taxCountryId, companyId, user);
            if (profile.IsDenied(SystemAreas.Profile.Basic, SystemAreas.CustomForms.Keys.Field))
            {
                customForms = [];
            }

            var employeeProfileSettings = await this.employeeProfileSettingService.GetEmployeeProfileSettingsAsync(profile, employee.CompanyId, employeeSelfService);
            var profileViewModel = new ProfileViewModel
            {
                Employee = employeeModel,
                EmploymentStatus = await this.employmentStatusService.GetStatusAsync(EmploymentStatusTypes.EmployeeId, employee.EmployeeId, DateTime.Today, true) ?? new EmployeeEmploymentStatus(),
                EmployeeProfileSettings = employeeProfileSettings,
                ShowExemptionsAndOthersPanel = this.ShowExemptionsAndOthersPanel(employeeProfileSettings, employeeSelfService),
                CanDeleteEmployee = !employeeSelfService && await this.employeeProfileService.CanDeleteAsync(employee.EmployeeId),
                HasEmployeeImage = await this.imageService.BlobExistsAsync(StorageContainers.Images, "Employee/Thumbnail", Convert.ToString(employee.EmployeeId)),
                TaxCountryCode = await this.companyService.GetTaxCountryCodeAsync(companyId),
                TaxCountryId = taxCountryId,
                IsCurrentlyInWorkflow = await this.employeeSecurityService.IsCurrentlyInWorkflowAsync(employee.EmployeeId),
                RequiredCustomFormCode = await this.GetRequiredCustomFormCodeAsync(customForms, companyId, employee.EmployeeId),
                CustomForms = customForms.Select(customForm => new CustomFormViewModel
                {
                    CompanyId = companyId,
                    CustomFormLevel = customForm.Level,
                    CustomFormCategory = $"{customForm.Code}_{(customForm.Level == CustomFormLevel.Bureau ? "B" : "C")}",
                    CustomFormScreenType = CustomFormScreenTypes.BasicProfile,
                    CustomFormScreenName = this.localizer.GetString(SystemAreas.Profile.Keys.PageHeader),
                    IsEmployee = true,
                    FormType = customForm.FormType,
                    HideEffectiveDate = customForm.HideEffectiveDate ?? false,
                    Required = customForm.Required,
                    FormName = customForm.FormName,
                    DisableEdit = profile.IsReadOnly(SystemAreas.Profile.Basic, SystemAreas.CustomForms.Keys.Field),
                    ShowPayrollCycle = customForm.ShowPayrollCycle,
                    CurrentRuns = this.companyRunService.GetCompanyRuns(this.tenantProvider.GetFrequencyId().Value, RunStatus.Open)
                        .Select(_ => _.CompanyFrequency.RunFrequencyId == (int)PayslipFrequency.Monthly
                            ? GetMonthlyFrequencyRunFormat(_)
                            : _.PeriodCode + "-" + _.PeriodEndDate.Month + "-" + _.PeriodEndDate.Day + "-" + _.OrderNumber)
                        .ToList()
                }).ToList()
            };

            return profileViewModel;
        }

        private async Task<string> GetRequiredCustomFormCodeAsync(List<CustomForm> customForms, long companyId, long employeeId)
        {
            foreach (var customForm in customForms.Where(_ => _.Required == true))
            {
                var hasEmployeeCustomForms = await this.customFormService
                    .GetWithCustomForms(new Tenant(companyId, employeeId), customForm.FormId, customForm.Level)
                    .AnyAsync();

                if (!hasEmployeeCustomForms)
                {
                    return $"{customForm.Code}_{(customForm.Level == CustomFormLevel.Bureau ? "B" : "C")}";
                }
            }

            return string.Empty;
        }

        public virtual async Task<ProfileViewModel> BuildCreateViewModelAsync(ISecurityProfile profile, long companyId, bool employeeSelfService)
        {
            var settings = await this.employeeProfileSettingService.GetEmployeeProfileSettingsAsync(profile, companyId, employeeSelfService);
            var company = await this.companyService.FindByIdAsync(companyId);
            var companyAddress = await this.companyAddressService.GetCompanyAddressAsync(companyId);

            // Getting the EnumCountry countryId as the implementation uses a different value from the taxCountryId
            var countryId = await this.companyService.GetCountryIdAsync(company.CompanyId);

            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var enumTaxCountry = await this.taxCountryService.GetEnumTaxCountryAsync(taxCountryId);

            var model = new ProfileViewModel
            {
                Employee = new EmployeeProfileViewModel
                {
                    CompanyId = companyId,
                    Address = this.AddAddress(companyAddress),
                    UifExemptionId = (int)UIFExemption.None, // the default value
                    SdlExemptionId = settings.ShowSdlExemption ? await this.companyService.GetDefaultSdlExemptionIdAsync(companyId) : null,
                    StandardIndustryCodeHeader = 0,
                    SubStandardIndustryCodeId = 0,
                    EtiExempt = company.EtiExempt,
                    CitizenshipId = countryId,
                    NationalityId = countryId,
                    ShowDisabilityType = true,
                    TaxCountryLegalWorkAge = enumTaxCountry?.EmployeeLegalWorkAge
                },
                EmployeeProfileSettings = settings,
                ShowExemptionsAndOthersPanel = this.ShowExemptionsAndOthersPanel(settings, employeeSelfService),
                TaxCountryCode = await this.companyService.GetTaxCountryCodeAsync(companyId),
                CompanyGenerateEmployeeNumber = await this.companyService.ShouldGenerateEmployeeNumber(companyId),
                CanCreateEmployee = profile.IsFullAccess(SystemAreas.Profile.BasicCreate, SystemAreas.Profile.BasicCreate),
                TaxCountryId = taxCountryId,
            };

            if (!settings.ShowEmployeeNumber)
            {
                model.Employee.EmployeeNumber = this.localizer.GetString("lblEmployeeNumberGenerated");
            }

            await this.SetCustomFieldDefaultsAsync(companyId, model.Employee.CustomFields);

            return model;
        }

        public virtual async Task<ProfileViewModel> BuildCreateViewModelAsync(ISecurityProfile profile, long companyId, Employee employee, bool employeeSelfService)
        {
            var employeeModel = this.mapper.Map<EmployeeProfileViewModel>(employee);
            employeeModel.CheckValidId = await this.emailAddressService.GetEmailAddressStatusAsync(employeeModel.Email);
            this.SortAddresses(employeeModel);

            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);

            return new ProfileViewModel
            {
                Employee = employeeModel,
                EmployeeProfileSettings = await this.employeeProfileSettingService.GetEmployeeProfileSettingsAsync(profile, companyId, employeeSelfService),
                TaxCountryCode = await this.companyService.GetTaxCountryCodeAsync(companyId),
                TaxCountryId = taxCountryId,
            };
        }

        protected virtual void SortAddresses(EmployeeProfileViewModel employeeModel)
        {
            if (employeeModel.Address.Count < 2)
            {
                if (employeeModel.Address.Count == 0)
                {
                    employeeModel.Address.Add(new EmployeeAddressViewModel
                    {
                        AddressType = AddressType.Physical,
                        EmployeeId = employeeModel.EmployeeId
                    });
                }

                employeeModel.Address.Add(new EmployeeAddressViewModel
                {
                    AddressType = AddressType.Postal,
                    EmployeeId = employeeModel.EmployeeId
                });
            }

            // Make Physical 1st address, the order is not guaranteed in the database
            employeeModel.Address = employeeModel.Address.OrderBy(a => a.AddressType).ToList();

            if (employeeModel.Address.Any(a => a.SameAsPostal == true))
            {
                // both addresses should be set to true
                foreach (var address in employeeModel.Address)
                {
                    address.SameAsPostal = true;
                }
            }
        }

        private bool ShowExemptionsAndOthersPanel(EmployeeProfileSettings employeeProfileSettings, bool employeeSelfService)
        {
            return !employeeSelfService
                   && (employeeProfileSettings.ShowSdlExemption
                       || employeeProfileSettings.ShowUIFExemption
                       || employeeProfileSettings.ShowCustomFieldOne
                       || employeeProfileSettings.ShowCustomFieldTwo
                       || employeeProfileSettings.ShowDefaultPayslip
                       || employeeProfileSettings.ShowSICCodeFields);
        }

        private async Task<List<CustomForm>> GetCustomFormsAsync(ISecurityProfile profile, long taxCountryId, long companyId, ClaimsPrincipal user)
        {
            return profile.IsDenied(SystemAreas.DynamicHistoricalInformation.Area, SystemAreas.DynamicHistoricalInformation.Area)
            ? []
            : await this.customFormService.GetCustomFormsAsync(taxCountryId, companyId, user.IsInRole(UserTypeCodes.Employee), user.GetUserId(), CustomFormScreenTypes.BasicProfile);
        }

        protected virtual IList<EmployeeAddressViewModel> AddAddress(Address? companyAddress = null)
        {
            return new List<EmployeeAddressViewModel>
            {
                new()
                {
                    CountryId = companyAddress?.CountryId,
                    PostalCountryId = companyAddress?.CountryId,
                    AddressType = AddressType.Physical
                },
                new()
                {
                    CountryId = companyAddress?.CountryId,
                    PostalCountryId = companyAddress?.CountryId,
                    AddressType = AddressType.Postal
                }
            };
        }

        protected async Task SetCustomFieldDefaultsAsync(long companyId, CustomFieldList<EmployeeCustomFieldValue> customFields)
        {
            var customFieldDefaults = await this.employeeProfileService.GetDefaultCustomFieldValuesAsync(companyId);
            var customFieldsConfigs = await this.customFieldService.GetCustomFieldsAsync(companyId, nameof(Employee), null);
            var customFieldsConfigsDictionary = customFieldsConfigs.Where(_ => _.CustomFieldType == "B").ToDictionary(x => x.CustomFieldId);

            foreach (var customFieldDefault in customFieldDefaults)
            {
                if (!customFieldsConfigsDictionary.TryGetValue(customFieldDefault.CustomFieldId, out var customFieldConfig))
                {
                    continue;
                }

                var current = customFields.FirstOrDefault(_ => _.CustomFieldId == customFieldDefault.CustomFieldId);
                if (current == null)
                {
                    customFields.Add(new EmployeeCustomFieldValue
                    {
                        CustomFieldId = customFieldDefault.CustomFieldId,
                        FieldValue = customFieldDefault.CustomFieldValue,
                        Code = customFieldDefault.CustomFieldOptionCode,
                        CustomFieldType = customFieldConfig.CustomFieldType
                    });
                }
                else if (string.IsNullOrEmpty(current.FieldValue))
                {
                    current.FieldValue = customFieldDefault.CustomFieldValue;
                    current.Code = customFieldDefault.CustomFieldOptionCode;
                }
            }
        }

        private static string GetMonthlyFrequencyRunFormat(CompanyRun companyRun)
        {
            return companyRun.RunType == RunType.Main ? companyRun.PeriodCode : companyRun.PeriodCode + "-" + companyRun.OrderNumber;
        }
    }
}