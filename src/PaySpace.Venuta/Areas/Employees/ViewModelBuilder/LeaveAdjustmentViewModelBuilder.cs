namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Security;

    public interface ILeaveAdjustmentViewModelBuilder
    {
        Task<LeaveAdjustmentViewModel> BuildViewModelAsync(Tenant tenant, ISecurityProfile profile);

        Task<LeaveAdjustmentViewModel> BuildViewModelAsync(long companyId, long employeeId,  long leaveAdjustmentId, ISecurityProfile profile);
    }

    public class LeaveAdjustmentViewModelBuilder : ILeaveAdjustmentViewModelBuilder
    {
        private readonly IMapper mapper;
        private readonly IEmployeeLeaveService employeeLeaveService;
        private readonly IPayRateService payRateService;
        private readonly ITenantProvider tenantProvider;

        public LeaveAdjustmentViewModelBuilder(
            IMapper mapper,
            IEmployeeLeaveService employeeLeaveService,
            IPayRateService payRateService,
            ITenantProvider tenantProvider)
        {
            this.mapper = mapper;
            this.employeeLeaveService = employeeLeaveService;
            this.payRateService = payRateService;
            this.tenantProvider = tenantProvider;
        }

        public Task<LeaveAdjustmentViewModel> BuildViewModelAsync(Tenant tenant, ISecurityProfile profile)
        {
            var vm = new LeaveAdjustmentViewModel();
            vm.CompanyId = tenant.CompanyId;
            vm.EmployeeId = tenant.EmployeeId;
            vm.PayRateEffectiveDate = this.payRateService.GetPayRatesByEmployeeId(tenant.EmployeeId)
                .OrderBy(_ => _.EffectiveDate)
                .Select(_ => _.EffectiveDate)
                .FirstOrDefault();

            return this.AddSecurityAsync(tenant.EmployeeId, profile, vm);
        }

        public async Task<LeaveAdjustmentViewModel> BuildViewModelAsync(long companyId, long employeeId, long leaveAdjustmentId, ISecurityProfile profile)
        {
            var vm = leaveAdjustmentId > 0
                ? await this.employeeLeaveService.GetLeaveAdjustments(employeeId, true).Where(_ => _.LeaveAdjustmentId == leaveAdjustmentId)
                    .ProjectTo<LeaveAdjustmentViewModel>(this.mapper.ConfigurationProvider)
                    .FirstOrDefaultAsync()
                : new LeaveAdjustmentViewModel { EmployeeId = employeeId };
            vm.CompanyId = companyId;
            await this.AddSecurityAsync(employeeId, profile, vm);
            return vm;
        }

        private async Task<LeaveAdjustmentViewModel> AddSecurityAsync(long employeeId, ISecurityProfile profile, LeaveAdjustmentViewModel vm)
        {
            vm.IsReadOnly = profile.IsReadOnly(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Adjustment);
            vm.IsAttachmentReadOnly = profile.IsReadOnly(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Keys.LeaveAdjustmentAttachments);
            vm.IsLinkedToLeaveScheme = await this.employeeLeaveService.IsLinkedToLeaveSchemeAsync(employeeId, DateTime.Today);
            vm.IsBrazilianCompany = this.tenantProvider.GetTaxCountryCode() == BrazilConstants.BrazilTaxCountryCode;

            return vm;
        }
    }
}
