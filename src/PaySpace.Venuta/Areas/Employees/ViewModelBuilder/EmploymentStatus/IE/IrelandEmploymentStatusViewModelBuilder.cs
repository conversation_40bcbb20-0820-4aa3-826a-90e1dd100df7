namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder.EmploymentStatus.IE
{
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    [CountryService(CountryCode.IE)]
    public class IrelandEmploymentStatusViewModelBuilder : EmploymentStatusViewModelBuilder
    {
        public IrelandEmploymentStatusViewModelBuilder(
            IStringLocalizer<EmployeeEmploymentStatus> localizer,
            ICompanyService companyService,
            ICompanyRunService companyRunService,
            ICustomFormService<EmployeeCustomForm> customFormService,
            ICustomFieldService customFieldService,
            IEnumService enumService,
            ICompanySettingService companySettingService,
            IEmploymentStatusService employmentStatusService,
            IEmployeeService employeeService,
            IEmployeePositionHierarchyService employeePositionHierarchyService,
            IEmployeeInboxService employeeInboxService,
            ICompanyCustomFormConfigService companyCustomFormConfigService,
            IBureauCustomFormConfigService bureauCustomFormConfigService,
            IEmployeeHistoryService employeeHistoryService,
            IEmployeeLeaveService employeeLeaveService)
            : base(
                localizer,
                companyService,
                companyRunService,
                customFormService,
                customFieldService,
                enumService,
                companySettingService,
                employmentStatusService,
                employeeService,
                employeePositionHierarchyService,
                employeeInboxService,
                companyCustomFormConfigService,
                bureauCustomFormConfigService,
                employeeHistoryService,
                employeeLeaveService)
        {
        }

        protected override void SetEmploymentStatus(EmploymentStatusViewModel model)
        {
            if (model.EmploymentStatus.TaxStatusId is null or -1)
            {
                model.EmploymentStatus.TaxStatusId = model.TaxStatuses
                    .Where(_ => _.TaxStatusCode == "Resident")
                    .Select(_ => _.TaxStatusId)
                    .SingleOrDefault();
            }

            model.EmploymentStatus.DirectorshipId ??= (int?)Directorship.None;
        }
    }
}