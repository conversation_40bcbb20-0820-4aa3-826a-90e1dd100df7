namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Leave
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Leave.Abstractions;

    public interface ILeaveViewModelFactory
    {
        Task<LeaveViewModelBuilder> GetBuilderAsync(Tenant tenant, string countryCode);
    }

    public class LeaveViewModelFactory : ILeaveViewModelFactory
    {
        private readonly IServiceProvider serviceProvider;
        private readonly IEmployeeLeaveService employeeLeaveService;

        public LeaveViewModelFactory(
            IServiceProvider serviceProvider,
            IEmployeeLeaveService employeeLeaveService)
        {
            this.serviceProvider = serviceProvider;
            this.employeeLeaveService = employeeLeaveService;
        }

        public async Task<LeaveViewModelBuilder> GetBuilderAsync(Tenant tenant, string countryCode)
        {
            var type = countryCode switch
            {
                "BR" => typeof(BrazilLeaveViewModelBuilder),
                _ => typeof(LeaveViewModelBuilder)
            };

            return (LeaveViewModelBuilder)ActivatorUtilities.CreateInstance(
                this.serviceProvider,
                type,
                tenant.CompanyId,
                tenant.EmployeeId,
                tenant.FrequencyId.Value,
                await this.GetLeaveDatesAsync(tenant.EmployeeId, LeaveStatus.Waiting),
                await this.GetLeaveDatesAsync(tenant.EmployeeId, LeaveStatus.Approved));
        }

        private async Task<IList<DateTime>> GetLeaveDatesAsync(long employeeId, LeaveStatus status)
        {
            var result = new List<DateTime>();

            var allLeaveAdjustments = await this.employeeLeaveService.GetLeaveAdjustments(employeeId)
               .Select(_ => new { _.LeaveAdjustmentId, _.StartDate, _.EndDate, _.LeaveEntryType, _.LeaveType, _.Status, _.TotalDays, _.CancellationId })
               .ToListAsync();

            var adjustmentsForStatus = await this.employeeLeaveService.GetLeaveAdjustments(employeeId)
                .Where(_ => _.ComponentEmployeeId == null)
                .Where(_ => _.LeaveEntryType == LeaveEntryType.LeaveApplication && _.Status == status)
                .Select(_ => new { _.LeaveAdjustmentId, _.StartDate, _.EndDate, _.LeaveEntryType, _.LeaveType, _.Status, _.TotalDays })
                .ToListAsync();

            foreach (var adjustment in adjustmentsForStatus)
            {
                if (allLeaveAdjustments.Any(_ => _.CancellationId == adjustment.LeaveAdjustmentId && _.Status == LeaveStatus.Approved))
                {
                    continue;
                }

                // Copied from old system.
                var adjCancelChecks = allLeaveAdjustments.Count(
                    _ => _.LeaveAdjustmentId != adjustment.LeaveAdjustmentId
                        && _.StartDate == adjustment.StartDate
                        && _.EndDate == adjustment.EndDate
                        && _.LeaveEntryType == LeaveEntryType.Cancellation
                        && _.LeaveType == adjustment.LeaveType
                        && _.Status == LeaveStatus.Approved
                        && _.TotalDays == adjustment.TotalDays);

                if (adjCancelChecks > 0)
                {
                    var adj = allLeaveAdjustments.Count(
                        _ => _.StartDate == adjustment.StartDate
                            && _.EndDate == adjustment.EndDate
                            && _.LeaveEntryType == LeaveEntryType.LeaveApplication
                            && _.LeaveType == adjustment.LeaveType
                            && _.Status == LeaveStatus.Approved
                            && _.TotalDays == adjustment.TotalDays);

                    if (adjCancelChecks == adj && adjustment.Status == LeaveStatus.Approved)
                    {
                        continue;
                    }
                }

                result.AddRange(DateHelper.GetDates(adjustment.StartDate.Value, adjustment.EndDate.Value));
            }

            return result;
        }
    }
}