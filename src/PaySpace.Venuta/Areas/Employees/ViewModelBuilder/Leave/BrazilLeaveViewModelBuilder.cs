namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Leave
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using AutoMapper;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Exceptions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.Workflow.Handlers;
    using PaySpace.Venuta.Workflow.Services;

    public class BrazilLeaveViewModelBuilder : LeaveViewModelBuilder
    {
        private readonly IStringLocalizer<EmployeeLeaveAdjustment> localizer;
        private readonly IMapper mapper;
        private readonly ICompanyRunService companyRunService;
        private readonly ICompanyLeaveService companyLeaveService;
        private readonly IEmployeeLeaveService employeeLeaveService;
        private readonly IEmployeeLeaveConcessionService employeeLeaveConcessionService;
        private readonly IEmployeeLeaveSettingService employeeLeaveSettingService;
        private readonly IEmployeeSuspensionService employeeSuspensionService;
        private readonly IBrazilEmployeeLeaveBalanceService brazilEmployeeLeaveBalanceService;
        private readonly IEmploymentStatusService employmentStatusService;
        private readonly IEmployeeLeaveValueService employeeLeaveValueService;
        private readonly long companyId;
        private readonly long employeeId;
        private readonly long frequencyId;

        public BrazilLeaveViewModelBuilder(
            IStringLocalizer<EmployeeLeaveAdjustment> localizer,
            IMapper mapper,
            ILeaveWorkflowHandler workflowManager,
            IWorkflowService workflowService,
            IWorkflowStatusService workflowStatusService,
            ICompanyRosterService companyRosterService,
            ICompanyRunService companyRunService,
            ICompanyLeaveService companyLeaveService,
            IBrazilEmployeeLeaveBalanceService employeeLeaveBalanceService,
            IEmploymentStatusService employmentStatusService,
            IEmployeeLeaveService employeeLeaveService,
            IEmployeeLeaveConcessionService employeeLeaveConcessionService,
            IEmployeeLeaveRunService employeeLeaveRunService,
            IEmployeeLeaveValueService employeeLeaveValueService,
            IEmployeeLeaveSettingService employeeLeaveSettingService,
            IEmployeeSuspensionService employeeSuspensionService,
            long companyId,
            long employeeId,
            long frequencyId,
            IList<DateTime> waitingDates,
            IList<DateTime> approvedDates)
            : base(
                localizer,
                workflowManager,
                workflowService,
                workflowStatusService,
                companyRosterService,
                employeeLeaveService,
                employeeLeaveRunService,
                employeeLeaveValueService,
                employeeLeaveSettingService,
                companyId,
                employeeId,
                frequencyId,
                waitingDates,
                approvedDates)
        {
            this.localizer = localizer;
            this.mapper = mapper;
            this.companyRunService = companyRunService;
            this.companyLeaveService = companyLeaveService;
            this.employeeLeaveService = employeeLeaveService;
            this.employeeLeaveConcessionService = employeeLeaveConcessionService;
            this.brazilEmployeeLeaveBalanceService = employeeLeaveBalanceService;
            this.employmentStatusService = employmentStatusService;
            this.employeeLeaveValueService = employeeLeaveValueService;
            this.employeeLeaveSettingService = employeeLeaveSettingService;
            this.employeeSuspensionService = employeeSuspensionService;
            this.companyId = companyId;
            this.employeeId = employeeId;
            this.frequencyId = frequencyId;
        }

        public override async Task<LeaveViewModel> InitAsync(
            EmployeeLeaveAdjustment leaveAdjustment,
            ClaimsPrincipal user,
            ISecurityProfile securityProfile)
        {
            var vm = await base.InitAsync(leaveAdjustment, user, securityProfile);
            vm.IsBrazilLeave = true;

            return vm;
        }

        public override async Task<LeaveViewModel> BuildViewModelAsync(
            EmployeeLeaveAdjustment leaveAdjustment,
            ClaimsPrincipal user,
            ISecurityProfile profile,
            bool init)
        {
            var isAdminUser = !user.IsInRole(UserTypeCodes.Employee) && profile.IsFullAccess(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Adjustment);

            if (leaveAdjustment.CompanyLeaveSetupId is null)
            {
                var companySchemeId = await this.employeeLeaveService.GetCompanySchemeIdAsync(this.employeeId, null, DateTime.Today)
                                      ?? throw new EmployeeLeaveSchemeException(this.localizer);

                leaveAdjustment.CompanyLeaveSetupId = await this.companyLeaveService.GetPriorityLeaveSetupIdAsync(
                    companySchemeId,
                    leaveAdjustment.LeaveType,
                    null,
                    DateTime.Today);
            }

            var settings = await this.employeeLeaveSettingService.GetLeaveSettingsAsync(
                this.companyId,
                this.employeeId,
                leaveAdjustment.LeaveType,
                leaveAdjustment.CompanyLeaveSetupId,
                leaveAdjustment.StartDate,
                isAdminUser);

            var sellDays = await this.employeeLeaveService.GetSellDays(this.employeeId, leaveAdjustment.LeaveAdjustmentId);
            var vm = new BrazilLeaveViewModel(settings)
            {
                LeaveAdjustment = leaveAdjustment
            };

            this.mapper.Map(await base.BuildViewModelAsync(leaveAdjustment, user, profile, init), vm);

            var historicalConcessionText = string.Empty;
            if (leaveAdjustment.LeaveType == LeaveType.Annual)
            {
                EmployeeHistoricConcession concession = null;
                if (leaveAdjustment.LeaveAdjustmentId == 0 && leaveAdjustment.CompanyRunId > 0)
                {
                    var periodEndDate = await this.companyRunService.GetPeriodEndDateAsync(leaveAdjustment.CompanyRunId);
                    concession = await this.employeeLeaveConcessionService.GetEmployeeHistoricalConcessionAsync(this.employeeId, periodEndDate);
                    if (concession != null)
                    {
                        leaveAdjustment.HistoricalConcessionId = concession.HistoricalConcessionId;
                    }
                }
                else if (leaveAdjustment.HistoricalConcessionId > 0)
                {
                    concession = await this.employeeLeaveConcessionService.GetEmployeeHistoricalConcessionByIdAsync(this.employeeId, leaveAdjustment.HistoricalConcessionId.Value);
                }

                if (concession != null)
                {
                    historicalConcessionText = $"{concession.ConcessionYearStartDate:d} - {concession.ConcessionYearEndDate:d}";
                    settings.EmployeeHistoricConcession = concession;
                }
            }

            vm.DisabledStartDates = await this.employeeLeaveValueService.GetDisabledStartDatesAsync(
                leaveAdjustment.LeaveType,
                leaveAdjustment.CompanyLeaveSetupId,
                leaveAdjustment.StartDate,
                this.companyId,
                this.employeeId,
                settings.OffDays);

            vm.DisabledStartDays = await this.employeeLeaveValueService.GetDisabledStartDaysAsync(this.companyId, this.employeeId, settings.OffDays);

            if (leaveAdjustment.LeaveType == LeaveType.Annual)
            {
                vm.ShowThirteenthCheque = (leaveAdjustment.LeaveAdjustmentId > 0 && leaveAdjustment.ThirteenCheque == true)
                                          || (leaveAdjustment.StartDate.HasValue
                                          && await this.employeeLeaveService.ShowThirteenthChequeAsync(leaveAdjustment.EmployeeId, this.companyId, leaveAdjustment.CompanyRunId));

                vm.ShowSellOption = sellDays.HasValue;
                if (leaveAdjustment.CompanyRunId > 0)
                {
                    vm.TotalDaysSell = sellDays ?? await this.brazilEmployeeLeaveBalanceService.GetDaysToSellAsync(
                    this.companyId,
                    this.employeeId,
                    this.frequencyId,
                    leaveAdjustment.CompanyLeaveSetupId,
                    leaveAdjustment.CompanyRunId,
                    settings.EmployeeHistoricConcession);

                    var canSellDays = await this.brazilEmployeeLeaveBalanceService.AllowVacationDaysSellAsync(
                        this.companyId,
                        this.employeeId,
                        leaveAdjustment.CompanyRunId,
                        leaveAdjustment.CompanyLeaveSetupId,
                        settings.EmployeeHistoricConcession);

                    vm.ShowSellOption = sellDays.HasValue || (leaveAdjustment.StartDate.HasValue && canSellDays);
                }

                vm.HistoricalConcession = historicalConcessionText;
            }

            vm.IsBrazilLeave = true;

            var disabledDates = await this.GetSuspensionDatesAsync();
            disabledDates.AddRange(await this.GetTerminationDatesAsync());
            disabledDates.AddRange(vm.Disabled);
            vm.Disabled = disabledDates.Distinct();

            return vm;
        }

        protected override async Task<LeaveBalance> GetLeaveBalanceAsync(EmployeeLeaveAdjustment leaveAdjustment, EmployeeLeaveSettings settings)
        {
            if (settings.EmployeeHistoricConcession is null)
            {
                return await base.GetLeaveBalanceAsync(leaveAdjustment, settings);
            }

            var runningBalance = (double)settings.EmployeeHistoricConcession.RunningLeaveBalance;
            return new LeaveBalance
            {
                ReflectInHours = settings.ReflectInHours,
                HoursPerDay = settings.PayRateHours,
                TimeSpan = settings.ReflectInHours ? TimeSpan.FromHours(runningBalance * settings.PayRateHours) : TimeSpan.FromDays(runningBalance)
            };
        }

        private async Task<List<DateTime>> GetSuspensionDatesAsync()
        {
            var dates = new List<DateTime>();
            var suspensionDates = await this.employeeSuspensionService
                .GetSuspensions(new Tenant(this.companyId, this.employeeId))
                .Select(_ => new { _.EffectiveDate, _.ReturnDate })
                .ToListAsync();

            foreach (var suspension in suspensionDates)
            {
                var suspensionEndDate = suspension.ReturnDate ?? suspension.EffectiveDate.AddMonths(24);
                dates.AddRange(DateHelper.GetDates(suspension.EffectiveDate, suspensionEndDate));
            }

            return dates;
        }

        private async Task<List<DateTime>> GetTerminationDatesAsync()
        {
            var dates = new List<DateTime>();
            var terminationDate = await this.employmentStatusService.GetLatestTerminationDateAsync(this.employeeId);
            if (terminationDate.HasValue)
            {
                dates.AddRange(DateHelper.GetDates(terminationDate.Value, terminationDate.Value.AddMonths(24)));
            }

            return dates;
        }
    }
}