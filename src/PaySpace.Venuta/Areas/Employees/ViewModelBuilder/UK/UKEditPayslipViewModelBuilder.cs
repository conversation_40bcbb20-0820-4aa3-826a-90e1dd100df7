namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder.UK
{
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Employees.Controllers;
    using PaySpace.Venuta.Areas.Employees.ViewModels;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Components;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval;
    using PaySpace.Venuta.Services.Tax;

    [CountryService(CountryCode.GB)]
    public sealed class UKEditPayslipViewModelBuilder : EditPayslipViewModelBuilder
    {
        private const string TaxCode = "TAXD_B";
        private const string FieldCode = "TCODE";

        private readonly ICustomFormService<EmployeeCustomForm> customFormService;
        private readonly ICustomFieldService customFieldService;

        public UKEditPayslipViewModelBuilder(IStringLocalizer<PayslipEditController> localizer,
            IPayslipService payslipService,
            IPayslipSettingsService payslipSettingsService,
            IPayRateService payRateService,
            ICompanyService companyService,
            ICompanyRunService companyRunService,
            ITaxCalculationErrorService taxCalculationErrorService,
            IBulkCaptureCodeService bulkCaptureCodeService,
            ICustomFormService<EmployeeCustomForm> customFormService,
            ICustomFieldService customFieldService,
            IReportRetrievalService reportRetrievalService)
            : base(
                 localizer,
                 payslipService,
                 payslipSettingsService,
                 payRateService,
                 companyService,
                 companyRunService,
                 taxCalculationErrorService,
                 bulkCaptureCodeService,
                 reportRetrievalService)
        {
            this.customFormService = customFormService;
            this.customFieldService = customFieldService;
        }

        public override async Task<PayslipEditViewModel> BuildAsync(ISecurityProfile profile, long companyId, long employeeId, long frequencyId, long payslipId)
        {
            var model = await base.BuildAsync(profile, companyId, employeeId, frequencyId, payslipId);

            if (model.PayslipSummary == null || model.PayslipSummary.Length == 0)
            {
                return model;
            }

            var taxCodeField = await this.customFieldService.GetCustomFieldAsync(companyId, "CustomForm", TaxCode, FieldCode);

            if (taxCodeField == null)
            {
                return model;
            }

            var taxCodeValue = await this.GetTaxFieldValue(companyId, employeeId, taxCodeField.CustomFormCategoryId.Value, taxCodeField.CustomFieldId);
            model.PayslipSummary[0] = model.PayslipSummary[0] with { taxCode = taxCodeValue };

            return model;
        }

        private async Task<string?> GetTaxFieldValue(long companyId, long employeeId, long categoryId, long fieldId)
        {
            var fieldValue = await this.customFormService
                .GetWithCustomForms(new Tenant(companyId, employeeId), categoryId, CustomFormLevel.Bureau)
                .OrderByDescending(_ => _.EffectiveDate)
                .Select(_ => _.CustomFields.FirstOrDefault(field => field.CustomFieldId == fieldId))
                .FirstOrDefaultAsync();

            return fieldValue?.FieldValue;
        }
    }
}
