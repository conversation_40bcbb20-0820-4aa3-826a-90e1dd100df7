namespace PaySpace.Venuta.Areas.Employees.ViewModelBuilder.UK
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using Maddalena;

    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Modules.Payslips.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Bureau;
    using PaySpace.Venuta.Services.Employees;
    using PaySpace.Venuta.ViewModels;

    [CountryService(CountryCode.GB)]
    public sealed class UKPayslipViewModelBuilder : PayslipViewModelBuilder
    {
        private readonly IPayRateService payRateService;
        private readonly ICompanyPayslipService companyPayslipService;

        public UKPayslipViewModelBuilder(
            IPayslipSettingsService payslipSettingsService,
            IPayRateService payRateService,
            ICompanyService companyService,
            ICompanyPayslipService companyPayslipService,
            IEmploymentStatusService employmentStatusService,
            ICountryTaxYearService countryTaxYearService,
            IEmployeeService employeeService,
            ITaxCountryService taxCountryService)
            : base(
                 payslipSettingsService,
                 payRateService,
                 companyService,
                 companyPayslipService,
                 employmentStatusService,
                 countryTaxYearService,
                 employeeService,
                 taxCountryService)
        {
            this.payRateService = payRateService;
            this.companyPayslipService = companyPayslipService;
        }

        public override async Task<PayslipsViewModel> BuildAsync(ISecurityProfile profile, long companyId, long employeeId, long frequencyId, bool isEmployeeLevelUser)
        {
            // Prevent manual payslip creation if the users earliest PayRate is later than any open run's periodRndDate
            var earliestPayRateDate = await this.payRateService.GetEarliestPayRateEffectiveDateAsync(employeeId, DateTime.MinValue);
            var runs = await this.companyPayslipService.GetEmptyPayslipRunsAsync(companyId, employeeId, frequencyId);
            var vm = await base.BuildAsync(profile, companyId, employeeId, frequencyId, isEmployeeLevelUser);

            vm.CannotCreatePayslipInThisTaxYear = false;
            vm.DisplayCreatePayslip = !isEmployeeLevelUser;
            vm.Periods = runs.Select(_ => new PayslipPeriodResult(_.RunId, _.RunDescription, earliestPayRateDate > _.PeriodEndDate)).ToList();

            return vm;
        }
    }
}
