@model JournalViewModel
@{
    ViewBag.Title = Localizer.GetString("lblPageHeader");
}

@await Html.PartialAsync("PerformanceJournal/_JournalGridScripts")

<page-header>
    <page-header-toolbar title="@Localizer.GetString("lblPageHeader")">
        <page-header-toolbar-content>
            @(Html.DevExtreme().Button()
            .Type(ButtonType.Default)
                            .ID("btnPJReportDownload")
                            .Visible(false)
                            .ElementAttr("data_tc", "btn-download")
                            .Icon("fas fa-download")
                            .StylingMode(ButtonStylingMode.Outlined)
                            .Text(Localizer.GetString("lblPerformanceJournalReport"))
                            .OnClick(
                                @<script>
                                    function() {
                                        const url = '@Url.Action("Download", "Journal", new {companyId = Model.CompanyId,employeeId = Model.EmployeeId,frequencyId = Model.FrequencyId,area = "Employees"})';
                                        utils.http.get(url);
                                    }
                                </script>))
            <vc:audit-trail/>
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <card>
        @await Html.PartialAsync("PerformanceJournal/_JournalGrid")
    </card>
</page-content>