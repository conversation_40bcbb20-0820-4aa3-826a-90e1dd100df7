@model EmployeeProfileTabViewModel
@{
    Model.Tab.Items(items =>
    {
        items.AddGroup()
            .ColCountByScreen(_ => _.Lg(3).Md(3).Sm(2))
            .Caption(Localizer.GetString("lblPhysicalAddress"))
            .Items(groupItems =>
            {
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].UnitNumber)
                    .Visible(Model.ViewModel.ShowUnitNumber)
                    .VisibleIndex(0);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Complex)
                    .Visible(Model.ViewModel.ShowComplex)
                    .VisibleIndex(1);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].StreetNumber)
                    .Visible(Model.ViewModel.ShowStreetNumber);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressLine1)
                    .ValidationRules(_ => _.AddStringLength().Max(Model.ViewModel.EmployeeProfileSettings.MaxStreetNameLength).Message(string.Format(Localizer.GetString("lblStreetNameMaxError"), Model.ViewModel.EmployeeProfileSettings.MaxStreetNameLength)))
                    .IsRequired(Model.ViewModel.RequireAddressLine1);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Block);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Entrance);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Staircase);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Floor);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].Door);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressLine2);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressLine3)
                    .IsRequired(Model.ViewModel.RequireAddressLine3);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressCode);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].CountryId)
                    .EnumFor<EnumAddressCountry>();
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].ProvinceId);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].AddressStreetTypeId)
                    .VisibleIndex(Model.ViewModel.TaxCountryCode == Maddalena.CountryCode.ES.ToString() ? 2 : 998);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[0].MunicipalityId)
                    .VisibleIndex(999);
            });

        items.AddGroup()
            .ColCountByScreen(_ => _.Lg(3).Md(3).Sm(2))
            .Caption(Localizer.GetString("lblPostalAddress"))
            .Items(groupItems =>
            {
                groupItems.AddGroup()
                         .ColCountByScreen(_ => _.Lg(3).Md(3))
                         .ColSpan(3)
                         .VisibleIndex(0)
                         .Items(items =>
                         {
                             items.AddSimpleWithMetadataFor(m => m.Address[1].SameAsPostal)
                                     .Label(l => l.Visible(false))
                                     .EditorWithMetadata(e => e.CheckBox().Text(Localizer.GetString("lblSameAsPostal")))
                                     .Visible(Model.ViewModel.ShowSameAsPostal);
                             items.AddSimpleWithMetadataFor(m => m.Address[1].IsCareofAddress)
                                     .Label(l => l.Visible(false))
                                     .EditorWithMetadata(e => e.CheckBox().Text(Localizer.GetString("lblIsCareofAddress")))
                                     .Visible(Model.ViewModel.ShowCareOfAddress);
                         });
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressType)
                    .EnumFor<EnumAddressType>(excludeValues: new[] { (int)AddressType.Physical }, false)
                    .VisibleIndex(1);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].UnitNumber)
                    .Visible(Model.ViewModel.ShowUnitNumber)
                    .VisibleIndex(2);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Complex)
                    .Visible(Model.ViewModel.ShowComplex)
                    .VisibleIndex(3);

                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].StreetNumber)
                    .Visible(Model.ViewModel.ShowStreetNumber);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressLine1)
                    .ValidationRules(_ => _.AddStringLength().Max(Model.ViewModel.EmployeeProfileSettings.MaxStreetNameLength).Message(string.Format(Localizer.GetString("lblPostalAddressNumberMaxError"), Model.ViewModel.EmployeeProfileSettings.MaxStreetNameLength)))
                    .IsRequired(Model.ViewModel.RequireAddressLine1);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Block);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Entrance);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Staircase);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Floor);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].Door);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressLine2);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressLine3)
                    .IsRequired(Model.ViewModel.RequireAddressLine3);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressLine1);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressLine2);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].SpecialServices);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressLine3);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalAddressCode);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressCode);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalCountryId)
                    .EnumFor<EnumAddressCountry>();
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].PostalProvinceId);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].ProvinceId);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].CareOfIntermediary)
                    .IsRequired(true);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].AddressStreetTypeId).VisibleIndex(Model.ViewModel.TaxCountryCode == Maddalena.CountryCode.ES.ToString() ? 4 : 998);
                groupItems.AddSimpleWithMetadataFor(m => m.Address[1].MunicipalityId)
                    .VisibleIndex(999);
            });
    });
}