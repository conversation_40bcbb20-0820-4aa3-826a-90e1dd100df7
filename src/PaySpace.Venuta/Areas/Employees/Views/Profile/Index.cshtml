@model ProfileViewModel
@{
    ViewBag.Title = "Profile";
}

@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Xsrf

<script>
    EmployeeProfile = {
        Settings: {
            Xsrf: "@Xsrf.GetAndStoreTokens(Context).RequestToken",
            taxCountryCode : '@Model.TaxCountryCode',
            taxCountryId : @Model.TaxCountryId,
            IsHistoricalRecord: @Json.Serialize(Model.EmployeeProfileSettings.IsHistoricalRecord)
        },
        Urls: {
            languages: "@Url.Action("GetLanguages", "Lookup", new { area = string.Empty })",
            gender: "@Url.Action("GetGender", "Lookup", new { area = string.Empty })",
            provinces: "@Url.Action("GetProvinces", "Lookup", new { area = string.Empty })",
            municipality: "@Url.Action("GetMunicipality", "Lookup", new { area = string.Empty })",
            addressStreetType: "@Url.Action("GetAddressStreetType", "Lookup", new { area = string.Empty })",
            standardIndustryCodeSubs: "@Url.Action("GetStandardIndustryCodeSubs", "Lookup", new { area = string.Empty })",
            updateEmailAddressValidity: "@Url.Action("UpdateEmailAddressValidity")",
            deleteImage: "@Url.Action("DeleteEmployeeImage", "Profile", new { employeeId = Model.Employee.EmployeeId })",
            pageUrl: "@Url.Action("Index", "Profile", new { employeeId = Model.Employee.EmployeeId })"
        }
    };

    function NewEmployeeHistory() {
        var baseUrl = "@Url.Action("Index", new { Model.Employee.EmployeeId, Model.Employee.CompanyId })";
        window.location.href = baseUrl + "/new";
    }
</script>

@section scripts {
	<script src="~/js/pages/countries.js" asp-append-version="true"></script>
	<script src="~/js/pages/employee-profile.js" asp-append-version="true"></script>
    <script type="module" src="~/pages/custom-form-grid.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString("lblPageHeader")">
        <page-header-toolbar-content>
            <button class="btn btn-outline-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasHistory" aria-controls="offcanvasHistory" asp-visible="@Model.EmployeeProfileSettings.ShowHistoryGrid">
                <i class="fas fa-history"></i>
                @Localizer.GetString("lblHistory")
            </button>

            <button asp-hide="Model.EmployeeProfileSettings.ReadOnly" securable-key="@SystemAreas.Profile.Basic" class="btn btn-outline-primary" data_tc="btn-new-employee-history" onclick="NewEmployeeHistory()" asp-visible="@Model.EmployeeProfileSettings.ShowAddNewButton">
                <i class="far fa-plus" aria-hidden="true"></i>
                @Localizer.GetString("lblNewEmployeeHistory")
            </button>
            <vc:audit-trail/>
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <alert-message type="danger" class="col-md-6" asp-visible="@Model.ShowNoAddressWarning || Model.Employee.ShowLegalWorkingAge">
        <div asp-visible="@Model.Employee.ShowLegalWorkingAge">
            @Localizer.GetString("lblEmployeeLegalWorkAgeWarning", Model.Employee.TaxCountryLegalWorkAge)
        </div>
        <div asp-visible="@Model.ShowNoAddressWarning">
            @Localizer.GetString("lblMissingAddress")
        </div>
    </alert-message>

    <form method="post" onsubmit="EmployeeProfile.App.OnSubmit(event)">
        @Html.Hidden("Address[0].AddressId", Model.Employee.Address[0].AddressId)
        @Html.Hidden("Address[0].AddressType", Model.Employee.Address[0].AddressType)
        @Html.Hidden("Address[0].EmployeeId", Model.Employee.Address[0].EmployeeId)

        @Html.Hidden("Address[1].AddressId", Model.Employee.Address[1].AddressId)
        @Html.Hidden("Address[1].EmployeeId", Model.Employee.Address[1].EmployeeId)

        <div asp-validation-summary="All"></div>
        <div id="validations-details"></div>
        <div id="validations-contact"></div>
        <div id="validations-address"></div>
        <div id="validations-exemptions"></div>
        <div class="ps-tabs-container">
            <div class="row">
                <div>
                    @(Html.DevExtreme().Form<EmployeeProfileViewModel>()
                        .ID("form-profile")
                        .FormData(Model.Employee)
                        .OnInitialized("EmployeeProfile.App.OnInitialized")
                        .CustomizeItem("EmployeeProfile.App.CustomizeItem")
                        .OnFieldDataChanged("EmployeeProfile.App.OnFieldDataChanged")
                        .ShowValidationSummary(false)
                        .Items(i =>
                        {
                            int defaultTabsCount = 4;
                            if (Model.ShowExemptionsAndOthersPanel)
                            {
                                defaultTabsCount += 1;
                            }

                            int selectedTabIndex = 0;
                            if (Model.IsCurrentlyInWorkflow && !string.IsNullOrEmpty(Model.RequiredCustomFormCode))
                            {
                                selectedTabIndex = Model.CustomForms.FindIndex(_ => _.CustomFormCategory == Model.RequiredCustomFormCode) + defaultTabsCount;
                            }

                            i.AddTabbed()
                                .Name("form-tabs")
                                .TabPanelOptions(builder => builder.ShowNavButtons(true)
                                .ScrollByContent(true)
                                .DeferRendering(false)
                                .Option("selectedIndex", selectedTabIndex)
                                .OnSelectionChanged("EmployeeProfile.App.OnSelectionChanged"))
                                .Tabs(async t =>
                                {
                                    await Html.PartialAsync("_Details", new EmployeeProfileTabViewModel(t.Add().Title(Localizer.GetString("tabEmployeeDetails")), Model));
                                    await Html.PartialAsync("_Personal", new EmployeeProfileTabViewModel(t.Add().Title(Localizer.GetString("tabPersonal")), Model));
                                    await Html.PartialAsync("_Contact", new EmployeeProfileTabViewModel(t.Add().Title(Localizer.GetString("tabContactDetails")), Model));
                                    await Html.PartialAsync("_Address", new EmployeeProfileTabViewModel(t.Add().Title(Localizer.GetString("tabAddresses")), Model));

                                    if (Model.ShowExemptionsAndOthersPanel)
                                    {
                                        await Html.PartialAsync("_ExemptionsAndOther", new EmployeeProfileTabViewModel(t.Add().Title(Localizer.GetString("tabExemptionsAndOther")), Model));
                                    }

                                    foreach (var customFormViewModel in Model.CustomForms)
                                    {
                                        t.Add()
                                            .Option("title", customFormViewModel.FormName)
                                            .Option("customFormCode", customFormViewModel.CustomFormCategory)
                                            .Option("hideUpdate", true)
                                            .Title(Localizer.GetString(customFormViewModel.FormName))
                                            .Badge(!customFormViewModel.Required.HasValue || !customFormViewModel.Required.Value ? "(optional)" : string.Empty)
                                            .Template(@<text>@await Html.PartialAsync("CustomForms/_CustomFormTabGrid", customFormViewModel)</text>);
                                    }
                                });
                        }))
                </div>
                <div id="history-overlay">
                     <offcanvas id="offcanvasHistory">
                        <offcanvas-header title="@Localizer.GetString("lblHistory")"></offcanvas-header>
                        <offcanvas-body>
                            @if (Model.EmployeeProfileSettings.ShowHistoryGrid)
                            {
                                @await Html.PartialAsync("_History")
                            }
                        </offcanvas-body>
                    </offcanvas>
                </div>
            </div>
        </div>
        <page-footer>
            @if (!Model.EmployeeProfileSettings.IsEssUser && Model.EmployeeHistoryId > 0 && !Model.EmployeeProfileSettings.ShowAddNewButton)
            {
                <a asp-action="EmployeeHistoryIndex" asp-route-employeeHistoryId="@Model.EmployeeHistoryId" data_tc="btn-cancel" text="Cancel"></a>
            }

            @if (Model.CanDeleteEmployee)
            {
                <a type="Danger" asp-action="Delete" data-navigate-confirm="@Localizer.GetString("deleteMessage")" text="Delete" data_tc="btn-delete"></a>
            }

            @if ((Model.EmployeeProfileSettings.IsHistoricalRecord && Model.EmployeeProfileSettings.CanEditHistoricalRecord && !Model.EmployeeProfileSettings.ReadOnly)
                 || (!Model.EmployeeProfileSettings.IsHistoricalRecord && !Model.EmployeeProfileSettings.ReadOnly))
            {
                <btn-update on-click="EmployeeProfile.App.OnUpdateClicked" id="btn-update" tc="btn-update"/>
            }
        </page-footer>
    </form>
</page-content>