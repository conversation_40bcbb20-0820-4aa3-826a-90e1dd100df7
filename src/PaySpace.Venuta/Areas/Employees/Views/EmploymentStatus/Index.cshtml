@model EmploymentStatusViewModel
@{
    ViewBag.Title = Localizer.GetString("lblPageHeader");
}

@section scripts {
    <country-script path="js/pages/employment-status" file="employment-status"></country-script>
    <script type="module" src="~/pages/custom-form-grid.js" asp-append-version="true"></script>
}

<script>
    EmploymentStatus = {
        Settings: {
            PreviouslyTerminated:@Json.Serialize(Model.Terminated),
            HasPendingInboxes:@Json.Serialize(Model.HasPendingInboxes),
            IsSouthAfrica: @Json.Serialize(Model.IsSouthAfrica),
            IsCanada: @Json.Serialize(Model.IsCanada),
            IsUnitedKingdom: @Json.Serialize(Model.IsUnitedKingdom),
            ClearEmploymentDates: @Json.Serialize(Model.ClearEmploymentDates),
            IdTypes: @Json.Serialize(Model.IdTypes),
            NaturePersons: @Json.Serialize(Model.NaturePersons),
            TaxStatuses: @Json.Serialize(Model.TaxStatuses),
            DentalBenefits: @Json.Serialize(Model.DentalBenefits),
            DentalBenefitId: "@(Model.EmploymentStatus.DentalBenefitId)",
            EmploymentStatusId: @Model.EmploymentStatus.EmploymentStatusId,
            DefaultIdString: "@Model.DefaultIdString",
            IdNumber: @Json.Serialize(Model.EmploymentStatus.IdNumber),
            EnableStability: @Json.Serialize(Model.EnableStability),
            IsIreland: @Json.Serialize(Model.IsIreland),
            Urls: {
                TerminationReasonsUrl: '@Url.Action("GetTerminationReasons", "EmploymentStatusApi")',
                CompanyRunsUrl: '@Url.Action("GetOpenRuns", "EmploymentStatusApi")',
                RecordUrl: '@Url.Action("Index")',
                GetBalanceUrl: '@Url.Action("GetLeaveBalance", "EmploymentStatusApi")',
                ValidateIdNumberUrl: "@Url.Action("ValidateIdNumber", "EmploymentStatusApi")",
                ValidateEmploymentStabilityUrl: "@Url.Action("ValidateEmploymentStability", "EmploymentStatusApi")",
                IsActiveLeavePresentOnTerminationUrl: "@Url.Action("IsActiveLeavePresentOnTermination","EmploymentStatusApi")",
                GetArrearsComponentsUrl: "@Url.Action("GetArrearsComponents", "EmploymentStatusApi")",
                SeveranceDaysUrl: '@Url.Action("GetSeveranceDays", "EmploymentStatusApi")',
            }
        }
    };
</script>

<page-header>
    <page-header-toolbar title="@Localizer.GetString("lblPageHeader")">
        <page-header-toolbar-content>
            @(Html.DevExtreme().Button()
                .Type(ButtonType.Default)
                .StylingMode(ButtonStylingMode.Outlined)
                .Icon("fas fa-download")
                .Text(Localizer.GetString("lblDownload"))
                .Visible(Model.IsUnitedKingdom && Model.Terminated)
                .OnClick(
                @<script>
                     function(e) {
                         $.ajax({
                         url: "@Url.Action("DownloadTerminationTaxCertificate")",
                             type: "GET"
                         })
                     }
                </script>))
            <vc:audit-trail/>
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    @(Html.DevExtreme().Form<EmploymentStatusViewModel>()
        .ID("form-employmentStatus")
        .FormData(Model)
        .Items(i =>
        {
            i.AddTabbed()
                .TabPanelOptions(_ => _.OnSelectionChanged("EmploymentStatus.App.OnSelectionChanged"))
                .Tabs(t =>
                {
                    t.Add().Template(new TemplateName("FormSection")).Title(Localizer.GetString("lblPageHeader"));

                    foreach (var customFormViewModel in Model.CustomForms)
                    {
                        t.Add()
                            .Option("hideSave", true)
                            .Title(Localizer.GetString(customFormViewModel.FormName))
                            .Template(@<text>@await Html.PartialAsync("CustomForms/_CustomFormTabGrid", customFormViewModel)</text>);
                    }
                });
        }))

    @await Html.PartialAsync("_Form")
    @if (!Model.IsUnitedKingdom)
    {
        @(Html.DevExtreme().Accordion()
            .Items(items =>
            {
                items.Add()
                    .Title(Localizer.GetString("lblHistory"))
                    .Template(@<text>@await Html.PartialAsync("_History")</text>);
            })
            .Collapsible(true)
            .Multiple(false)
        )
    }
</page-content>

@(Html.DevExtreme().Popup()
    .ID("validate-id-number-popup")
    .Width("auto")
    .Height("auto")
    .DragEnabled(false)
    .HideOnOutsideClick(true)
    .ContentTemplate(
        @<text>
            <span>
                @Localizer.GetString("lblDuplicateGroupEmployeeIdNumberWarning")
            </span>
            <ul class="list-unstyled ml-10">
                <% if(EmploymentStatus.App.DuplicateIds.length >0) { for (var i = 0; i < EmploymentStatus.App.DuplicateIds.length; i++) { %>
                <li>
                    <span><%= EmploymentStatus.App.DuplicateIds[i].employeeFullName %>,</span>
                    <span><%= EmploymentStatus.App.DuplicateIds[i].frequencyName %>,</span>
                    <span><%= EmploymentStatus.App.DuplicateIds[i].companyName %></span>
                </li>
                <% } }%>
            </ul>
         </text>)
    .ToolbarItems(items =>
    {
        items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Normal).Text(Localizer.GetString("btnCancel")).OnClick("EmploymentStatus.App.CancelSubmit")).Toolbar(Toolbar.Bottom);
        items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Default).Text(Localizer.GetString("btnContinue")).OnClick("EmploymentStatus.App.OnIdNumberValidationAccepted")).Toolbar(Toolbar.Bottom);
    }))

@(Html.DevExtreme().Popup()
    .ID("active-leave-applications-popup")
    .Width("auto")
    .Height("auto")
    .DragEnabled(false)
    .ShowTitle(false)
    .HideOnOutsideClick(true)
    .ContentTemplate(
        @<text>
            <span>
                 @Localizer.GetString("lblActiveLeaveApplicationsOnTerminationWarning")
            </span>
        </text>
    )
    .ToolbarItems(items =>
    {
        items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Default).Text(Localizer.GetString("btnYes")).OnClick("EmploymentStatus.App.onActiveLeaveApplicationsAccepted")).Toolbar(Toolbar.Bottom);
        items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Default).Text(Localizer.GetString("btnNo")).OnClick("EmploymentStatus.App.CancelSubmit")).Toolbar(Toolbar.Bottom);
    }))

@(Html.DevExtreme().Popup()
    .ID("arrears-components-popup")
    .Width("auto")
    .Height("auto")
    .DragEnabled(false)
    .ShowTitle(false)
    .HideOnOutsideClick(false)
    .ContentTemplate(
        @<text>
            <span>
                @Localizer.GetString("lblArrearsComponentsOnTerminationWarning")
            </span>
            <p>
                <ul>
                    <% if(EmploymentStatus.App.ArrearsComponents.length > 0) { for (var i = 0; i < EmploymentStatus.App.ArrearsComponents.length; i++) { %>
                        <li><%= EmploymentStatus.App.ArrearsComponents[i]%></li>
                    <% } }%>
                </ul>
            </p>
        </text>
    )
    .ToolbarItems(items =>
    {
        items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Normal).Text(Localizer.GetString("btnCancel")).OnClick("EmploymentStatus.App.CancelSubmit")).Toolbar(Toolbar.Bottom);
        items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Default).Text(Localizer.GetString("btnContinue")).OnClick("EmploymentStatus.App.OnArrearsComponentsTerminationAccepted")).Toolbar(Toolbar.Bottom);
    }))

@if (Model.EnableStability)
{
    @(Html.DevExtreme().Popup()
        .ID("validate-stability-popup")
        .Width("auto")
        .Height("auto")
        .DragEnabled(false)
        .HideOnOutsideClick(true)
        .ContentTemplate(
            @<text>
                <alert-message type="warning" class="mb-15">
                    <i class="fa fa-exclamation-triangle"></i> @Localizer.GetString("lblStabilityWarningHeading")
                </alert-message>
                <p>
                    <ul class="list-unstyled ml-10">
                        <% if(EmploymentStatus.App.StabilityWarnings.length > 0) { for (var i = 0; i < EmploymentStatus.App.StabilityWarnings.length; i++) { %>
                        <li><%= EmploymentStatus.App.StabilityWarnings[i]%></li>
                        <% } }%>
                    </ul>
                </p>
                <p>
                    @Localizer.GetString("lblStabilityWarningAreYouSure")
                </p>
             </text>)
        .ToolbarItems(items =>
        {
            items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Normal).Text(Localizer.GetString("btnCancel")).OnClick("EmploymentStatus.App.CancelSubmit")).Toolbar(Toolbar.Bottom);
            items.Add().Location(ToolbarItemLocation.Center).Widget(w => w.Button().Type(ButtonType.Default).Text(Localizer.GetString("btnContinue")).OnClick("EmploymentStatus.App.OnStabilityValidationAccepted")).Toolbar(Toolbar.Bottom);
        }))
}

@section modals {
    <modal id="terminated" asp-modal-show="@Model.BulkCaptureRedirect" asp-modal-backdrop="static">
        <modal-header>
            <modal-title title="@Localizer.GetString("lblTerminated")" />
        </modal-header>
        <modal-body>
            <p class="text-pre-line">@Localizer.GetString("lblRedirectInstructions")</p>
        </modal-body>
        <modal-footer>
            <a href="@Model.OnboardingNotificationsUrl" text="@Localizer.GetString("btnSkip")"></a>
            <a asp-controller="Classic" asp-action="Index" asp-area=""
                asp-route-frequencyId="@Model.FrequencyId"
                asp-route-companyId="@Model.CompanyId"
                asp-route-rid="@Model.NavLinkId"
                asp-route-employeeId="@Model.EmploymentStatus.EmployeeId"
                styling-mode="Contained"
                text="@Localizer.GetString("btnProceed")">
            </a>
        </modal-footer>
    </modal>
}