@model LumpSumDirectiveViewModel
@using PaySpace.Venuta.Modules.Payslips.Abstractions

@inject IStringLocalizerFactory StringLocalizerFactory
@{
    var localizer = StringLocalizerFactory.Create(SystemAreas.EmployeeLumpSum.Area, null);
}

<script>
    function allowUpdating(e) {
        return (e.row.data.payslipId == @Json.Serialize(Model.PayslipId));
    }

    function showDeleteIcon(e) {
        return allowUpdating(e);
    }

    function showReverseIcon(e) {
        return (e.row.data.payslipId != @Json.Serialize(Model.PayslipId));
    }

    function onEditorPreparing(e) {
        if (e.parentType === 'dataRow') {
            if (e.dataField == 'taxCode') {
                e.editorOptions.disabled = !e.row.isNewRow;
            }

            if (e.editorName == 'dxDateBox' && e.dataField === "directiveIssuedDate") {
                e.editorOptions.min = new Date(2021, 2);
            }
        }
    }

    function onRowUpdating(e) {
        // We need to ensure that the tax code is always sent to the server.
        if (!e.newData.taxCode) {
            e.newData.taxCode = e.oldData.taxCode;
        }
    }

    function onInitNewRow(e) {
        e.component.columnOption("runDescription", "formItem.visible", false);
        e.component.columnOption("periodCode", "formItem.visible", false);
    }

    function onEditingStart(e){
        e.component.columnOption("runDescription", "formItem.visible", false);
        e.component.columnOption("periodCode", "formItem.visible", false);
    }
</script>

<div class="modal-body">
    @(Html.DevExtreme().DataGrid<EmployeeLumpSumResult>()
        .ID("history-grid")
        .DataSource(_ => _.Mvc()
        .Controller("EmployeeLumpSumApi")
        .LoadAction("Get")
        .InsertAction("Post")
        .UpdateAction("Put")
        .DeleteAction("Delete")
        .Key("employeeLumpSumId", "payslipId"))
        .Editing(editing =>
        {
            editing.RefreshMode(GridEditRefreshMode.Full);
            editing.Mode(GridEditMode.Form);
            editing.AllowAdding(true);
            editing.AllowUpdating(true);
            editing.AllowDeleting(true);

            editing.Texts(x =>
            {
                x.ConfirmDeleteMessage(localizer.GetString("ConfirmDeleteMessage"));
            });
        })
        .OnEditorPreparing("onEditorPreparing")
        .OnRowUpdating("onRowUpdating")
        .OnInitNewRow("onInitNewRow")
        .OnEditingStart("onEditingStart")
        .Columns(columns =>
        {
            columns.AddForJson(m => m.DirectiveIssuedDate)
            .DataType(GridColumnDataType.Date)
            .CustomizeText(@<script>
                function(cellInfo){
                    return DateTimeFormatter(cellInfo.value)
                }
            </script>)
            .Alignment(HorizontalAlignment.Left)
            .Width(125);

            columns.AddForJson(m => m.RunDescription)
            .Caption("Run description")
            .Alignment(HorizontalAlignment.Left);

            columns.AddForJson(m => m.PeriodCode)
            .Caption("Period")
            .Alignment(HorizontalAlignment.Left)
            .Width(125);

            columns.AddForJson(_ => _.TaxCode)
            .Lookup(_ => _.DataSource(ds => ds.Mvc()
            .Controller("EmployeeLumpSumApi")
            .LoadAction("GetTaxCodes").Key("key"))
            .ValueExpr("key")
            .DisplayExpr("value"))
            .Alignment(HorizontalAlignment.Left);

            columns.AddForJson(m => m.DirectiveAmount)
            .Alignment(HorizontalAlignment.Left)
            .Width(125);

            columns.AddForJson(m => m.DirectiveTax)
            .Alignment(HorizontalAlignment.Left)
            .Width(125);

            columns.AddForJson(m => m.DirectiveNumber)
            .Alignment(HorizontalAlignment.Left)
            .Width(125);

            columns.AddForJson(m => m.ReferenceNumber)
            .ValidationRules(vr => vr.AddPattern().Message(localizer.GetString(SystemAreas.EmployeeLumpSum.Keys.ReferenceNumberOnlyNumeric)).Pattern("^[0-9]+$"))
            .Alignment(HorizontalAlignment.Left)
            .Width(125);

            columns.AddForJson(m => m.TaxFreeDirectiveAmount)
            .Alignment(HorizontalAlignment.Left)
            .Width(125);

            columns.AddForJson(m => m.PayslipId)
            .Visible(false)
            .FormItem(_ => _.Visible(false));

            columns.Add()
            .Type(GridCommandColumnType.Buttons)
            .Buttons(buttons =>
            {
                buttons.Add().Name(GridColumnButtonName.Edit).Visible(new JS("allowUpdating"));

                buttons.Add().Icon("trash").Hint(localizer.GetString("DeleteHint")).Name(GridColumnButtonName.Delete).Visible(new JS("showDeleteIcon"));
                buttons.Add().Icon("revert").Hint(localizer.GetString("ReverseHint")).Name(GridColumnButtonName.Delete).Visible(new JS("showReverseIcon"));
            });
        }))
</div>
