@model ChangeRequestViewModel

<card>
    @(Html.DevExtreme().DataGrid<EmployeeChangeRequest>()
            .DataSource(ds => ds.Mvc().Key("requestId")
            .LoadAction("GetHistory"))
            .RemoteOperations(true)
            .FilterRow(_ => _.Visible(true))
            .ColumnHidingEnabled(true)
            .Columns(columns =>
            {
                columns.AddForJson(_ => _.RequestTypeId)
            .CalculateSortValue("requestTypeName")
            .Lookup(l => l.DataSource(d => d.Mvc().Controller("ChangeRequest").Area("Employees").LoadAction("GetTypes")).DisplayExpr("text").ValueExpr("value"));
                columns.AddForJson(_ => _.EffectiveDate)
            .SortOrder(SortOrder.Desc)
            .HidingPriority(3);
                columns.AddFor<PERSON><PERSON>(_ => _.Status)
            .CalculateSortValue("statusDescription")
            .LookupForEnum(typeof(EnumChangeRequestStatus), excludeValues: new int[] { (int)ChangeRequestStatus.Saved })
            .CellTemplate(@<text>
        <% if (data.status == 2) { %>
        <span class="text-grey">
            <% } else if (data.status == 3) { %>
            <span class="text-success">
                <% } else { %>
                <span class="text-danger">
                    <% } %>
                    <span class="changerequest-status-<%= data.status %>">
                        <%- text %>
                    </span>
    </text>)
            .HidingPriority(2);
                columns.AddForJson(_ => _.RequestReference)
            .HidingPriority(1);
                columns.Add()
            .Alignment(HorizontalAlignment.Center)
            .CssClass("dx-command-edit dx-command-edit-with-icons")
            .CellTemplate(@<text><a class="fa fa-eye" href="<%= data.url %>"></a></text>);
            }))
</card>