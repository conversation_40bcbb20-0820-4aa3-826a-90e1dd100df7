@model ChangeRequestViewModel

<card title="Details">
    <div class="row m-0">
        <div class="p-0">
            <form id="form-change-request" autocomplete="off" method="post">
                <div class="col-md-4">
                    @Html.EditorFor(m => m.ChangeRequest.RequestTypeId, new { Action = "GetTypes" })
                </div>
                <div id="partial" class="d-flex flex-column gap-4">
                    @if (Model.ChangeRequest.RequestTypeId.HasValue)
                    {
                        <partial name="_Form" />
                    }
                </div>
            </form>
        </div>
    </div>
</card>