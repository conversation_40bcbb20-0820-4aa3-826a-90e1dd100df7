@model LeaveDefaultFormViewModel
@{
    Model.Configurator.AddGroup()
        .ColCount(new JS("Leave.getColCount()"))
        .Items(item =>
        {
            item.AddSimpleWithMetadataFor(_ => _.AutoGenerateRun);
        });

    Model.Configurator.AddGroup()
        .ColCount(new JS("Leave.getColCount()"))
        .Items(item =>
        {
            item.AddSimpleWithMetadataFor(_ => _.CompanyRunId)
                .EditorWithMetadata(e => e.Lookup()
                    .DataSource(ds => ds.Mvc()
                        .Controller("Leave")
                        .LoadAction("GetCompanyRuns")
                        .Key("companyRunId"))
                    .DataSourceOptions(dso => dso.Sort(s => s.AddSorting("PeriodEndDate").AddSorting("OrderNumber")))
                    .DisplayExpr("runDescription")
                    .ValueExpr("companyRunId"))
                    .IsRequired(true);
        });

    Model.Configurator.AddGroup()
        .ColCount(new JS("Leave.getColCount()"))
        .Items(item =>
        {
            item.AddSimpleWithMetadataFor(_ => _.Status)
            .EnumForSelectBox<EnumLeaveStatus>()
            .IsRequired(true);
        });

    Model.Configurator.AddSimpleWithMetadataFor(_ => _.SkipValidation)
        .Label(l => l.Visible(false))
        .EditorWithMetadata(e => e.CheckBox().Disabled(Model.ViewModel.LeaveAdjustment.LeaveAdjustmentId > 0).Text(Localizer.GetString("SkipValidation")));
    Model.Configurator.AddGroup()
        .ColCountByScreen(_ => _.Lg(3).Md(3))
        .Items(singleItems =>
        {
            singleItems.AddGroup()
            .Name("lblSkipLeaveValidationsWarning")
            .Items(singleItem =>
            {
                singleItem.AddSimple().Template(
                @<text>
                    <alert-message type="warning" title="@Localizer.GetString("lblSkipLeaveValidationsPart1")">
                        @Localizer.GetString("lblSkipLeaveValidationsPart2")
                    </alert-message>
                </text>);
            }).Visible(Model.ViewModel.LeaveAdjustment.SkipValidation);
        });
    Model.Configurator.AddSimpleWithMetadataFor(_ => _.SkipWorkflow)
        .Label(l => l.Visible(false))
        .EditorWithMetadata(e => e.CheckBox().Disabled(Model.ViewModel.LeaveAdjustment.LeaveAdjustmentId > 0).Text(Localizer.GetString("SkipWorkflow")));
}
