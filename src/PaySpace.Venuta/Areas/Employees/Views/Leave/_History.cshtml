@model LeaveViewModel
@(Html.DevExtreme().DataGrid<EmployeeLeaveAdjustment>()
    .DataSource(ds => ds.Mvc().Key("leaveAdjustmentId")
        .LoadAction("GetHistory")
        .DeleteAction("Delete"))
    .RemoteOperations(true)
    .Sorting(_ => _.ShowSortIndexes(false))
    .Editing(e => e
        .AllowUpdating(Model.IsAdminUser && !Model.ReadOnly)
        .AllowDeleting(Model.IsAdminUser && !Model.ReadOnly))
    .FilterRow(_ => _.Visible(true))
    .OnEditingStart("Leave.App.OnEditingStart")
    .OnRowRemoved("Leave.App.OnRowRemoved")
    .OnRowPrepared("Leave.App.OnRowPrepared")
    .OnCellPrepared("Leave.App.OnCellPrepared")
    .ColumnHidingEnabled(true)
    .ColumnAutoWidth(true)
    .Grouping(g => g.ExpandMode(GridGroupingExpandMode.RowClick))
    .Paging(p => p.Enabled(true).PageSize(10))
    .HoverStateEnabled(true)
    .OnRowClick("Leave.App.OnRowClick")
    .Columns(columns =>
        {
            columns.Add()
                .DataField("year")
                .Caption(Localizer.GetString("Year"))
                .SortOrder(SortOrder.Desc)
                .SortIndex(0)
                .GroupIndex(0);
            columns.Add()
                .DataField("periodStartDate")
                .DataType(GridColumnDataType.Date)
                .SortOrder(SortOrder.Desc)
                .SortIndex(1)
                .Visible(false);
            columns.LocalizeFor<CompanyRun>()
                .AddForJson(_ => _.RunDescription)
                .HidingPriority(5);
            columns.AddForJson(_ => _.LeaveEntryType)
                .LookupForEnum(typeof(EnumLeaveEntryType), excludeValues: new[] { (int)LeaveEntryType.Adjustment })
                .SortOrder(SortOrder.Asc)
                .SortIndex(3)
                .HidingPriority(2);
            columns.AddForJson(_ => _.HistoricalConcessionId)
                .Lookup(lookup => lookup
                    .DataSource(ds => ds.Mvc().Key("value").LoadAction("GetConcessionDates"))
                    .ValueExpr("value")
                    .DisplayExpr("text"))
                .HidingPriority(4)
                .Visible(Model.IsBrazilLeave);
            columns.AddForJson(_ => _.LeaveType)
                .Lookup(lookup => lookup
                    .DataSource(ds => ds.Mvc().Key("leaveTypeId").Controller("Leave").LoadAction("GetTypes"))
                    .ValueExpr("value")
                    .DisplayExpr("text"))
                .HidingPriority(3)
                .CellTemplate(
                @<text>
                    <%- text %>

                    <% if (text && data.leaveDescription) { %>
                    <br />
                    <% } %>

                    <% if (data.leaveDescription) { %>
                    <%- data.leaveDescription %>
                    <% } %>
                </text>);
            columns.AddForJson(_ => _.StartDate)
                .SortOrder(SortOrder.Desc)
                .SortIndex(2);
            columns.AddForJson(_ => _.EndDate)
                .HidingPriority(4);
            columns.AddForJson(_ => _.TotalDays)
                .Format(new JS("NumberFormatter"))
                .HidingPriority(1);
            columns.AddForJson(_ => _.Status)
                .LookupForEnum(typeof(EnumLeaveStatus))
                .CellTemplate(@<text>
                    <% if (data.status == 1) { %>
                        <span class="fs-6 badge bg-light border text-success">
                    <% } else if (data.status == 2) { %>
                        <span class="fs-6 badge bg-light border text-danger">
                    <% } else { %>
                        <span class="fs-6 badge bg-light border text-warning-emphasis">
                    <% } %>
                        <%- text %>
                    </span>
                </text>);
            columns.Add()
                .Alignment(HorizontalAlignment.Left)
                .CssClass("dx-command-edit dx-command-edit-with-icons")
                .Type(GridCommandColumnType.Buttons)
                .Buttons(b =>
                {
                    b.Add().Name(GridColumnButtonName.Edit)
                        .Visible(Model.IsAdminUser && !Model.ReadOnly);

                    b.Add().Name(GridColumnButtonName.Cancel).Icon("clearcircle")
                        .Visible(new JS("function(e) { return isButtonVisible(e) }"))
                        .OnClick("function(e) { LeaveCancellation.App.ShowCancellation(convertUrl(e)) }");

                    b.Add().Name(GridColumnButtonName.Delete)
                        .Visible(Model.IsAdminUser && !Model.ReadOnly);
                })
               .HidingPriority(6);
    })
)

<script type="text/javascript">
// Get conditional rendering flag
function isButtonVisible(e) {
    return e.row.data.allowCancel
}
// Get url from the row
function convertUrl(e) {
    return @Html.Url("cancel", new { leaveAdjustmentId = new JS("e.row.data.leaveAdjustmentId")});
}
</script>