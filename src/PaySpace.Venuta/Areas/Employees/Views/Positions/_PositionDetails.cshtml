@using PaySpace.Venuta.Infrastructure.Extensions
@model EmployeePositionViewModel

<div class="row">
    <div class="col-12">
        @(Html.DevExtreme().Form<EmployeePosition>()
            .FormData(Model.EmployeePosition)
            .ElementAttr("data_tc", "form-position")
            .ReadOnly(Model.Settings.IsReadOnly).ID("form-employeePosition")
            .ValidationGroup("EmployeePositionScreen")
            .CustomizeItem("EmployeePosition.App.CustomizeItem")
            .OnFieldDataChanged("EmployeePosition.App.OnFieldDataChanged")
            .Items(items =>
            {
                items.AddGroup()
                    .ColSpan(1)
                    .ColCount(3)
                    .Caption(Localizer.GetString("lblDetailsGroup"))
                     .Items(groupItems =>
                     {
                         groupItems.AddSimpleWithMetadataFor(_ => _.EffectiveDate)
                             .EditorWithMetadata(e => e.DateBox().Min(Model.MinimumEffectiveDate)).ColSpan(1);
                         groupItems.AddSimple().ColSpan(1);
                         groupItems.AddSimple().ColSpan(1);
                         groupItems.AddSimple().ColSpan(3)
                                   .Visible(Model.Settings.DisplayJob && Model.EmployeePosition.EmployeePositionId == default(long) && !Model.Settings.IsReadOnly && !Model.Settings.IsPageReadOnly)
                                   .Template(new TemplateName("Workforce"));
                         groupItems.AddSimple().ColSpan(3)
                                   .Template(new TemplateName("PositionDetails"));
                     });

                items.AddGroup()
                    .ColSpan(1)
                    .ColCount(3)
                    .Caption(Localizer["lblOrganizationGroup"].Value)
                    .Visible(Model.Settings.HasAdvancedPositionManagement && Model.OrganizationHierarchy?.Count > default(int))
                    .Template(new TemplateName("OrganizationUnit"));

                items.AddGroup().ColSpan(3)
                    .Caption(Localizer["lblReportingLine"].Value)
                    .Items(groupItems => {
                        groupItems.AddSimple().ColSpan(3).Template(new TemplateName("ReportingLine"));

                        groupItems.AddSimple().ColSpan(3).Template(
                            @<text>
                                @(Html.DevExtreme()
                                        .Accordion()
                                        .OnItemRendered("EmployeePosition.App.OnItemRendered")
                                        .OnItemTitleClick("EmployeePosition.App.OnItemTitleClick")
                                        .Collapsible(true)
                                        .DeferRendering(false)
                                        .SelectedIndex(-1)
                                        .Items(_ =>
                                        {
                                            _.Add().Title(Localizer.GetString("lblSection3"))
                                                    .Template(new TemplateName("AdditionalFields"));
                                        }))
                            </text>);
                    });

                items.AddSimpleFor(_ => _.CustomFields)
                    .AddCustomFieldTemplate(typeof(EmployeePosition), colSpan:3, customFieldsColCount:3);
            }))
    </div>
</div>