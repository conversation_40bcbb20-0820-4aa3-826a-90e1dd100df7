@model YearEndReportingViewModel

@{
    ViewBag.Title = Localizer.GetString(SystemAreas.YearEndReporting.Keys.PageHeader);
}

@section scripts
{
    <script type="module" src="~/pages/year-end-reporting.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.YearEndReporting.Keys.PageHeader)">
        <page-header-toolbar-content>
            @Html.DevExtreme().Button().Text(Localizer.GetString(SystemAreas.YearEndReporting.Keys.IRASAmendmentGuide)).Type(ButtonType.Default).StylingMode(ButtonStylingMode.Outlined).OnClick($"() => window.open('{Model.AmendmentGuideLink}', '_blank')")
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>
<page-content>
    <year-end-reporting
        company-id="@Model.CompanyId"
        employee-id="@Model.EmployeeId"
        ir8a-edit-url="@Model.IR8AEditUrl"
        tax-year-url="@Model.TaxYearApiUrl"
        tax-year-Id="@Model.TaxYearId"
        allow-edit-for-tax-year="@Model.AllowEditForTaxYear"
        edit-appendix8b-url="@Model.EditAppendix8BUrl"
        ir8-new-record-allowed="@Model.AllowNewIR8ARecord"
        allow-new-appendix8b-record="@Model.AllowNewAppendix8BRecord" 
        selected-index="@Model.SelectIndexTab">
    </year-end-reporting>
</page-content>