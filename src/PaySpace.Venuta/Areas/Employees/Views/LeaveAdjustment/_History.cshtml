@model LeaveAdjustmentViewModel

@(Html.DevExtreme().DataGrid<EmployeeLeaveAdjustment>()
    .ID("LeaveAdjustmentHistory")
    .DataSource(ds => ds.Mvc()
        .Key("leaveAdjustmentId")
        .LoadAction("GetHistory")
        .DeleteAction("Delete"))
    .RemoteOperations(true)
    .Sorting(_ => _.ShowSortIndexes(false))
    .FilterRow(_ => _.Visible(true))
    .ColumnHidingEnabled(true)
    .Editing(e => e.AllowDeleting(!Model.IsReadOnly).AllowUpdating(true))
    .Paging(p => p.Enabled(true).PageSize(4))
    .OnEditingStart("EmployeeLeaveAdjustment.App.OnEditingStart")
    .OnCellPrepared("EmployeeLeaveAdjustment.App.OnCellPrepared")
    .OnRowRemoved("EmployeeLeaveAdjustment.App.OnRowRemoved")
    .Columns(columns =>
    {
        columns.Add()
            .DataField("periodStartDate")
            .DataType(GridColumnDataType.Date)
            .SortOrder(SortOrder.Desc)
            .SortIndex(0)
            .Visible(false);
        columns.LocalizeFor<CompanyRun>()
            .AddForJson(_ => _.RunDescription)
            .HidingPriority(1);
        columns.AddForJson(_ => _.LeaveType)
            .Lookup(lookup => lookup
                .DataSource(ds => ds.Mvc().Key("leaveTypeId").LoadAction("GetTypes"))
                .ValueExpr("value")
                .DisplayExpr("text"))
            .AllowSorting(false)
            .HidingPriority(3)
            .CellTemplate(
            @<text>
                <%= text %>
                <% if (text && data.leaveDescription) { %>
                <br />
                <% } %>

                <% if (data.leaveDescription) { %>
                <%= data.leaveDescription %>
                <% } %>
            </text>);
        columns.AddForJson(_ => _.HistoricalConcessionId)
            .Lookup(lookup => lookup
                .DataSource(ds => ds.Mvc().Key("value")
                    .LoadAction("GetConcessionDates"))
                .ValueExpr("value")
                .DisplayExpr("text"))
            .AllowSorting(false)
            .Visible(Model.IsBrazilianCompany)
            .HidingPriority(3);
        columns.AddForJson(_ => _.TotalDays)
            .Format("#0.00###")
            .HidingPriority(2)
            .Width(100);
        columns.Add().Type(GridCommandColumnType.Buttons).Buttons(_ =>{
            _.Add().Name(GridColumnButtonName.Edit);
            _.Add().Name(GridColumnButtonName.Delete).Icon("fas fa-trash font-12");
        });
    })
)