@using PaySpace.Venuta.Modules.Leave.Abstractions.Models

@model LeaveBalanceViewModel
@{
    ViewBag.Title = Localizer.GetString("lblPageHeader");
}

@section scripts {
    <script src="~/js/pages/leave-balance.js" asp-append-version="true"></script>

    <style>
        .dx-group-row .dx-command-adaptive .dx-datagrid-adaptive-more {
            display: none;
        }
    </style>
}

<script>
    LeaveBalance = {
        Settings: {
            Annual: @Json.Serialize(EnumResourceManager.GetEnumDescription<EnumLeaveType>(CultureInfo.CurrentCulture, (int)LeaveType.Annual)),
            Special: @Json.Serialize(EnumResourceManager.GetEnumDescription<EnumLeaveType>(CultureInfo.CurrentCulture, (int)LeaveType.Special)),

            AllowLeaveValueAccess: @Json.Serialize(Model.AllowLeaveValueAccess),
            AllowLeaveValueCalculationAccess: @Json.Serialize(Model.AllowLeaveValueCalculationAccess),

            DaysPerPeriod: @Json.Serialize(Model.DaysPerPeriod),
            PayRateHours: @Json.Serialize(Model.PayrateHours)
        }
    }
</script>

<page-header>
    <page-header-toolbar title="@Localizer.GetString("lblPageHeader")">
        <page-header-toolbar-content>
            @(Html.DevExtreme().SelectBoxFor(_ => _.RunId)
                .DataSource(ds => ds.Mvc().LoadAction("GetRuns").Key("companyRunId"))
                .DataSourceOptions(ds => ds.Group("periodCodeStartDate", desc: true)
                    .Sort(s => s.AddSorting("periodCodeStartDate", desc: true)
                    .AddSorting("orderNumber", desc: true)))
                .ValueExpr("companyRunId")
                .DisplayExpr("runDescription")
                .ShowClearButton(false)
                .Option("type", "Navigator")
                .Grouped(true)
                .GroupTemplate(
                    @<text>
                        <span>
                            <%- DevExpress.localization.formatDate(new Date(items[0].periodCodeStartDate), "yMMMM") %>
                        </span>
                    </text>)
                .OnValueChanged(@<script>
                    function (e) {
                        window.location.href = @Html.Url("Index", new { RunId = new JS("e.value") });
                    }
                </script>))
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <div class="vstack gap-3">
        @if (!string.IsNullOrEmpty(Model.SchemeName))
        {
            <card class="fw-bold">
                <span class="text-primary">@Localizer.GetString("SchemeName"):</span>
                <span class="text-grey mr-15">@Model.SchemeName</span>

                <span class="text-primary">@Localizer.GetString("EffectiveDate"):</span>
                <span class="text-grey">@Model.EffectiveDate.ToString("d")</span>
            </card>
        }

        <card>
            @if (Model.RunId == null)
            {
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <alert-message type="warning" class="mt-15 text-wrap">
                            <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
                            @Localizer.GetString("RunNotFound")
                        </alert-message>
                    </div>
                </div>
            }
            @(Html.DevExtreme().DataGrid<EmployeeLeaveBalanceResult>()
                .ID("grid-balances")
                .DataSource(ds => ds.Mvc()
                    .Key("companyLeaveSetupId")
                    .LoadAction("GetBalances")
                    .LoadParams(new { Model.RunId }))
                .DataSourceOptions(ds => ds.PostProcess("LeaveBalance.App.PostProcess"))
                .FilterRow(_ => _.Visible(true))
                .Grouping(group => group.AutoExpandAll(false))
                .OnAdaptiveDetailRowPreparing("LeaveBalance.App.OnAdaptiveDetailRowPreparing")
                .OnCellPrepared("LeaveBalance.App.OnCellPrepared")
                .OnRowClick("LeaveBalance.App.OnRowClick")
                .Summary(summary =>
                {
                    summary.GroupItems(totals =>
                    {
                        totals.Add()
                            .Column("leaveType")
                            .SummaryType(SummaryType.Max);
                        totals.Add()
                            .Column("accrual")
                            .SummaryType(SummaryType.Sum)
                            .ValueFormat(new JS("NumberFormatter"))
                            .DisplayFormat("{0}")
                            .AlignByColumn(true);
                        totals.Add()
                            .Column("leaveBalance")
                            .SummaryType(SummaryType.Sum)
                            .ValueFormat(new JS("NumberFormatter"))
                            .DisplayFormat("{0}")
                            .AlignByColumn(true);
                        totals.Add()
                            .Column("leaveBalanceInDays")
                            .SummaryType(SummaryType.Sum)
                            .ValueFormat(new JS("NumberFormatter"))
                            .DisplayFormat("{0}")
                            .AlignByColumn(true);
                        totals.Add()
                            .Column("maxBalance")
                            .SummaryType(SummaryType.Max)
                            .ValueFormat(new JS("NumberFormatter"))
                            .DisplayFormat("{0}")
                            .AlignByColumn(true);
                    });
                })
                .SortByGroupSummaryInfo(_ => _.Add().GroupColumn("orderNumber").SummaryItem("leaveType"))
                .ColumnHidingEnabled(true)
                .ColumnAutoWidth(true)
                .Columns(columns =>
                {
                    columns.AddForJson(_ => _.OrderNumber)
                        .SortOrder(SortOrder.Asc)
                        .Visible(false);
                    columns.AddForJson(_ => _.LeaveTypeDescription)
                        .GroupCellTemplate(@<text><%= LeaveBalance.App.GetDescription(text, data) %></text>)
                        .GroupIndex(0);
                    columns.AddForJson(_ => _.LeaveDescription)
                        .CellTemplate(
                            @<text>
                                 <%- text %>
                                 <% if (data.bucketRules) { %>
                                 <p class="m-0"><small class="font-italic"><%= data.bucketRules %></small></p>
                                 <% } %>
                             </text>);
                    columns.AddForJson(_ => _.Accrual)
                        .Format(new JS("NumberFormatter"))
                        .HidingPriority(3)
                        .AllowFiltering(false);
                    columns.AddForJson(_ => _.LeaveBalance)
                        .Format(new JS("NumberFormatter"))
                        .AllowFiltering(false);
                    columns.AddForJson(_ => _.BalanceUnit)
                        .Caption("")
                        .AllowFiltering(false)
                        .AllowSorting(false);
                    columns.AddForJson(_ => _.LeaveBalanceInDays)
                        .Caption("")
                        .Format(new JS("NumberFormatter"))
                        .AllowFiltering(false)
                        .AllowSorting(false)
                        .Visible(false)
                        .Alignment(HorizontalAlignment.Left)
                        .CellTemplate(
                            @<text>
                                 <%- LeaveBalance.App.GetFormattedLeaveBalance(data) %>
                             </text>);
                    columns.AddForJson(_ => _.MaxBalance)
                        .Format(new JS("NumberFormatter"))
                        .CellTemplate(@<text></text>)
                        .AllowFiltering(false)
                        .HidingPriority(2);
                    columns.AddForJson(_ => _.LeaveCycleStartDate)
                        .AllowFiltering(false)
                        .HidingPriority(6)
                        .Visible(false);
                    columns.AddForJson(_ => _.DueToExpire)
                        .AllowFiltering(false)
                        .HidingPriority(5)
                        .Visible(false);
                    columns.AddForJson(_ => _.LeaveValue)
                        .AllowFiltering(false)
                        .Visible(false)
                        .CellTemplate(
                            @<text>
                                 <% if (data.displayLeaveValue) { %>
                                 <%- LeaveBalance.App.GetFormattedDecimalAsString(data.leaveValue) %>
                                 <% } %>
                             </text>);
                    columns.Add()
                        .Type(GridCommandColumnType.Buttons)
                        .Alignment(HorizontalAlignment.Left)
                        .Name("leaveValueCalculation")
                        .Visible(false)
                        .Width(50)
                        .Buttons(_ =>
                        {
                            _.Add()
                            .Visible(new JS("(e) => e.row.data.displayLeaveValueCalculation"))
                            .Icon("fas fa-calculator")
                            .Hint(Localizer.GetString("lblLeaveValueBreakdown"))
                            .OnClick(@<script>
                                            (e) => LeaveBalance.App.ShowLeaveValueCalculationPopUp(e.row.data.leaveBalance, e.row.data.leaveValue, e.row.data.reflectInHours, e.row.data.calculationType)
                                        </script>);
                        });
                }))
            <div class="pt-15">
                @(Html.DevExtreme().CheckBox()
                .Text(Localizer.GetString("lblLeaveDaysCheckbox"))
                .Value(false)
                .Visible(Model.ShowLeaveDaysOption)
                .OnValueChanged("LeaveBalance.App.OnCheckboxValueChanged"))
            </div>
        </card>
    </div>
</page-content>

@await Html.PartialAsync("_PopupLeaveBalanceBreakdown", Model)

@await Html.PartialAsync("_TemplateLeaveBalanceBreakdown", Model)

@await Html.PartialAsync("_PopupLeaveValueCalculation", Model)

@await Html.PartialAsync("_TemplateLeaveCalculationCustom", Model)

@await Html.PartialAsync("_TemplateLeaveCalculationDoNotCalcBcoe", Model)

@await Html.PartialAsync("_TemplateLeaveCalculationStandard", Model)