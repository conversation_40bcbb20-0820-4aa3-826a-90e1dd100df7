namespace PaySpace.Venuta.Areas.Agency.Controllers
{
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;

    using Newtonsoft.Json;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Users.Abstractions;
    using PaySpace.Venuta.Modules.Users.Abstractions.Models;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.ViewModels.UserProfile;

    [Area("Agency")]
    [DisplayName(SystemAreas.UserProfiles.Area)]
    public class AgencyUserProfileController : Controller
    {
        private readonly IAgencyService agencyService;
        private readonly ICompanyService companyService;
        private readonly IUserProfileService userProfileService;
        private readonly IMapper mapper;
        public AgencyUserProfileController(
            IAgencyService agencyService,
            ICompanyService companyService,
            IUserProfileService userProfileService,
            IMapper mapper)
        {
            this.agencyService = agencyService;
            this.companyService = companyService;
            this.userProfileService = userProfileService;
            this.mapper = mapper;
        }

        public async Task<IActionResult> Index(long companyId)
        {
            var profile = this.HttpContext.GetSecurityProfile();

            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var featureFlags = await this.GetFlagsAsync(agencyId);

            var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);
            var completedContactTypes = await this.userProfileService.GetUserContactTypesForCompanyGroupAsync(companyGroupId);

            return this.View("UserProfile/Index", new UserProfileViewModel
            {
                ApiUrl = this.Url.Action("Get", "AgencyUserProfileApi"),
                DataUrl = this.Url.Action("GetSubAgencyDetails", "AgencyUserProfileApi"),
                SelectedContactTypeUrl = this.Url.Action("GetSelectedContactTypes", "CompanyUserProfileApi", new { area = "Company" }),
                ReplicateUserUrl = this.Url.Action("ReplicateUser", "Admin", new { area = string.Empty }),
                UserTypeId = (int)UserType.Agency,
                IsAgencyScreen = true,
                AllowEdit = profile.IsFullAccess(SystemAreas.UserProfiles.Area, SystemAreas.UserProfiles.Area),
                Flags = JsonConvert.SerializeObject(featureFlags),
                CompletedContactTypes = JsonConvert.SerializeObject(completedContactTypes),
                IsBureauUser = this.User.IsInRole(UserTypeCodes.Bureau),
                ShowPasswordField = await this.agencyService.GetPasswordSettingAsync(agencyId) ?? false,
                EditUrl = this.Url.Action("Edit", "AgencyUserProfile")
            });
        }

        public async Task<IActionResult> Edit(long companyId, long? userId)
        {
            var user = new UserResult
            {
                UserStatus = UserStatus.Active,
                UserType = UserType.Agency,
            };

            var agencyId = await this.companyService.GetAgencyIdAsync(companyId);
            var userProfileAgencyId = agencyId;
            if (userId > 0)
            {
                var companyGroupId = await this.companyService.GetCompanyGroupIdAsync(companyId);

                if ((this.User.IsInRole(Roles.TopLevel) || this.User.IsInRole(UserTypeCodes.Bureau)) && userProfileAgencyId == PaySpaceConstants.PaySpaceAgencyId)
                {
                    userProfileAgencyId = default;
                }

                var userProfiles = this.userProfileService.GetUsers(companyGroupId, companyId, userProfileAgencyId, null, null, null, null);

                user = await userProfiles.ProjectTo<UserResult>(this.mapper.ConfigurationProvider)
                    .SingleOrDefaultAsync(_ => _.UserId == userId);

                if (user == null)
                {
                    return this.NotFound();
                }
            }

            var profile = this.HttpContext.GetSecurityProfile();
            var featureFlags = await this.GetFlagsAsync(agencyId);

            return this.View("UserProfile/Edit", new UserProfileEditViewModel
            {
                User = user,
                ApiUrl = this.Url.Action("Get", "AgencyUserProfileApi"),
                DataUrl = this.Url.Action("GetSubAgencyDetails", "CompanyUserProfileApi", new { area = "Agency" }),
                BaseUrl = this.Url.Action(nameof(this.Index)),
                UserTypeId = (int)user.UserType,
                AllowEdit = profile.IsFullAccess(SystemAreas.UserProfiles.Area, SystemAreas.UserProfiles.Area),
                Flags = JsonConvert.SerializeObject(featureFlags),
                IsBureauUser = this.User.IsInRole(UserTypeCodes.Bureau),
                IsBureauScreen = false,
                ShowPasswordField = await this.agencyService.GetPasswordSettingAsync(agencyId) ?? false,
                IsNew = !userId.HasValue,
                UserId = userId
            });
        }

        private async Task<List<string>> GetFlagsAsync(long agencyId)
        {
            var featureFlags = new List<string>();

            if (this.User.IsInRole(UserTypeCodes.Bureau))
            {
                featureFlags.Add("allowBureauSettings");
            }

            if (this.User.IsInRole(Roles.TopLevel))
            {
                featureFlags.Add("isAgencyTopLevelUser");
            }

            if (await this.userProfileService.IsContactTypeDisabled(agencyId))
            {
                featureFlags.Add("disableContactTypes");
            }

            if (await this.agencyService.AllowUserAgencySubInvoiceDetailsAsync(agencyId))
            {
                featureFlags.Add("agencySubInvoiceDetails");
            }

            if (agencyId is not PaySpaceConstants.PaySpaceAgencyId)
            {
                featureFlags.Add("allowAgency");
            }

            return featureFlags;
        }
    }
}
