namespace PaySpace.Venuta.Areas.Agency.Controllers
{
    using System.ComponentModel;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.ViewModels.Templates;

    [Area("Agency")]
    [DisplayName(SystemAreas.TemplateSetup.AgencyArea)]
    public class AgencyTemplateSetupController : Controller
    {
        public IActionResult Index()
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.TemplateSetup.AgencyArea, SystemAreas.TemplateSetup.AgencyArea);

            var model = new TemplateSetupViewModel
            {
                AllowEdit = allowEdit,
                TemplateApiControllerName = "AgencyTemplate",
                ComponentVariableApiControllerName = "AgencyComponentVariable"
            };

            return this.View("Templates/TemplateSetup", model);
        }
    }
}