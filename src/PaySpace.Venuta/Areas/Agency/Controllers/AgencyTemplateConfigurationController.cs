namespace PaySpace.Venuta.Areas.Agency.Controllers
{
    using System.ComponentModel;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.ViewModels.Templates;

    [Area("Agency")]
    [DisplayName(SystemAreas.TemplateConfiguration.AgencyArea)]
    public class AgencyTemplateConfigurationController : Controller
    {
        public IActionResult Index()
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.TemplateConfiguration.AgencyArea, SystemAreas.TemplateConfiguration.AgencyArea);

            return this.View("Templates/TemplateConfiguration", new TemplateConfigurationViewModel
            {
                ApiControllerName = "AgencyTemplateConfiguration",
                EditUrl = this.Url.Action(nameof(Edit)),
                AllowEdit = allowEdit
            });
        }

        public IActionResult Edit(long? templateConfigurationId)
        {
            var profile = this.HttpContext.GetSecurityProfile();
            var allowEdit = profile.IsFullAccess(SystemAreas.TemplateConfiguration.AgencyArea, SystemAreas.TemplateConfiguration.AgencyArea);

            var viewModel = new TemplateConfigurationEditViewModel
            {
                BaseUrl = this.Url.Action(nameof(Index)),
                EditUrl = this.Url.Action(nameof(Edit), new { templateConfigurationId = (long?)null }),
                ApiControllerName = "AgencyTemplateConfiguration",
                TemplatesApiControllerName = "AgencyTemplate",
                ComponentVariablesApiControllerName = "AgencyComponentVariable",
                TemplateConfigurationId = templateConfigurationId.GetValueOrDefault(),
                AllowEdit = allowEdit,
                IsAgency = true
            };

            return this.View("Templates/TemplateConfigurationEdit", viewModel);
        }
    }
}