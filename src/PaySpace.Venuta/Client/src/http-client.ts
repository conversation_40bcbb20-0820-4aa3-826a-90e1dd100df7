import env from "./endpoint-selector";
import session from "./session";

// Azure function key used for integration endpoints
export const integrationFunctionsKey = document.querySelector<HTMLMetaElement>("meta[name='ps:functionsKey']")!.content;

function getUrl(path: string) {
    // replace using regex gi = global replacement case-insensitive
    return path.replace(/{companyid}/gi, session.CompanyId?.toString() || "not-found")
        .replace(/{employeeid}/gi, session.EmployeeId?.toString() || "not-found")
        .replace(/{frequencyid}/gi, session.FrequencyId?.toString() || "not-found")
        .replace(/{countryid}/gi, session.CountryId?.toString() || "not-found");
}

export function getHeaders(companyRequest: boolean, customHeaders?: HeadersInit): HeadersInit {
    let baseHeaders = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.AccessToken}`,
        'X-IS-MSS': `${session.IsMSS}`,
        'X-IS-ESS': `${session.IsESS}`
    }

    if (!companyRequest)
    {
        baseHeaders = {...baseHeaders, ...{ 'X-EmployeeId': `${session.EmployeeId}` }}
    }

    return { ...baseHeaders, ...customHeaders };
}

async function get(path: string, req: RequestInit) {
    const response = await fetch(path, {
        ...{
            method: "get",
            headers: {
                "X-Requested-With": "XMLHttpRequest"
            }
        },
        ...req
    });

    if (response.status === 204) {
        return;
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
        return response.json();
    } else {
        return null
    }
}

async function getFile(path: string, fileName: string, req: RequestInit) {
    const response = await fetch(path, {
        ...{
            method: "get",
            headers: {
                "X-Requested-With": "XMLHttpRequest"
            }
        },
        ...req
    });

    if (response.status === 200) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');

        link.href = url;
        link.setAttribute('download', fileName);

        document.body.appendChild(link);
        link.click();

        document.body.removeChild(link);

        return true;
    } else {
        return false;
    }
}

async function post(path: string, req: RequestInit, body: Object) {
    const response = await fetch(path, {
        ...{
            method: "post",
            headers: {
                'Content-Type': 'application/json',
                "X-Requested-With": "XMLHttpRequest"
            },
            body: JSON.stringify(body)
        },
        ...req
    });

    return parseResponse(response);
}

async function patch(path: string, req: RequestInit, body: Object) {
    const response = await fetch(path, {
        ...{
            method: "patch",
            headers: {
                'Content-Type': 'application/json',
                "X-Requested-With": "XMLHttpRequest"
            },
            body: JSON.stringify(body)
        },
        ...req
    });

    return parseResponse(response);
}

async function put(path: string, req: RequestInit, body: Object) {
    const response = await fetch(path, {
        ...{
            method: "put",
            headers: {
                'Content-Type': 'application/json',
                "X-Requested-With": "XMLHttpRequest"
            },
            body: JSON.stringify(body)
        },
        ...req
    });

    return parseResponse(response);
}

async function del(path: string, req: RequestInit) {
    return await fetch(path, {
        ...{
            method: "delete",
            headers: {
                "X-Requested-With": "XMLHttpRequest"
            }
        },
        ...req
    });
}

function parseResponse(response: Response)
{
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
        return response.json();
    } else {
        return null
    }
}

export class LocalHttpClient {
    public getUrl(path: string) {
        if (path.startsWith("/nextgen")) {
            return getUrl(path);
        }

        return "/nextgen/" + getUrl(path).replace(/^\/+/, "");
    }

    private createRequest() {
        return {
            credentials: "include"
        } as RequestInit;
    }

    public get(path: string) {
        return get(this.getUrl(path), this.createRequest());
    }

    public post(path: string, body: Object) {
        return post(this.getUrl(path), this.createRequest(), body);
    }

    public patch(path: string, body: Object) {
        return patch(this.getUrl(path), this.createRequest(), body);
    }

    public put(path: string, body: Object) {
        return put(this.getUrl(path), this.createRequest(), body);
    }

    public del(path: string) {
        return del(this.getUrl(path), this.createRequest());
    }
}

export class ExternalHttpClient {
    private apiUrl: URL;

    constructor(apiUrl: URL = env.api_url) {
        this.apiUrl =  apiUrl;
    }

    private createRequest(customHeaders?: HeadersInit, companyRequest?: boolean) {
        return {
            headers: getHeaders(companyRequest === true, customHeaders)
        } as RequestInit;
    }

    public getUrl(path: string) {
        return new URL(getUrl(path), this.apiUrl).toString();
    }

    public get(path: string, headers?: HeadersInit, companyRequest?: boolean) {
        return get(this.getUrl(path), this.createRequest(headers, companyRequest));
    }

    public getFile(path: string, fileName: string, headers?: HeadersInit, companyRequest?: boolean) {
        return getFile(this.getUrl(path), fileName, this.createRequest(headers, companyRequest));
    }

    public post(path: string, body: Object, headers?: HeadersInit, companyRequest?: boolean) {
        return post(this.getUrl(path), this.createRequest(headers, companyRequest), body);
    }

    public patch(path: string, body: Object, headers?: HeadersInit, companyRequest?: boolean) {
        return patch(this.getUrl(path), this.createRequest(headers, companyRequest), body);
    }

    public put(path: string, body: Object,  headers?: HeadersInit, companyRequest?: boolean) {
        return put(this.getUrl(path), this.createRequest(headers, companyRequest), body);
    }

    public del(path: string, headers?: HeadersInit, companyRequest?: boolean) {
        return del(this.getUrl(path), this.createRequest(headers, companyRequest));
    }
}

export class IntegrationHttpClient {
    private readonly apiUrl: URL;
    private readonly withAuth: boolean;

    constructor(options?: { apiUrl?: URL; withAuth?: boolean }) {
        const rawUrl = options?.apiUrl ?? env.integration_api_url;

        this.apiUrl = typeof rawUrl === 'string' ? new URL(rawUrl) : rawUrl!;
        this.withAuth = options?.withAuth ?? true;
    }

    private createRequest(customHeaders?: HeadersInit, authOverride?: boolean): RequestInit {
        const includeAuth = authOverride ?? this.withAuth;

        return {
            headers: {
                'Content-Type': 'application/json',
                'x-functions-key': integrationFunctionsKey,
                ...(includeAuth && session.AccessToken ? { 'Authorization': `Bearer ${session.AccessToken}` } : {}),
                ...(customHeaders || {})
            }
        };
    }

    public getUrl(path: string): string {
        return new URL(getUrl(path).replace(/^\/+/, ""), this.apiUrl).toString();
    }

    public get(path: string, headers?: HeadersInit, authOverride?: boolean) {
        return get(this.getUrl(path), this.createRequest(headers, authOverride));
    }

    public getFile(path: string, fileName: string, headers?: HeadersInit, authOverride?: boolean) {
        return getFile(this.getUrl(path), fileName, this.createRequest(headers, authOverride));
    }

    public post(path: string, body: object, headers?: HeadersInit, authOverride?: boolean) {
        return post(this.getUrl(path), this.createRequest(headers, authOverride), body);
    }

    public patch(path: string, body: object, headers?: HeadersInit, authOverride?: boolean) {
        return patch(this.getUrl(path), this.createRequest(headers, authOverride), body);
    }

    public put(path: string, body: object, headers?: HeadersInit, authOverride?: boolean) {
        return put(this.getUrl(path), this.createRequest(headers, authOverride), body);
    }

    public del(path: string, headers?: HeadersInit, authOverride?: boolean) {
        return del(this.getUrl(path), this.createRequest(headers, authOverride));
    }
}