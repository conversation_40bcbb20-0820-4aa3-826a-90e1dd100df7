import { createStore, CustomStore, Options } from "devextreme-aspnet-data-nojquery";
import { integrationFunctionsKey, IntegrationHttpClient } from "./http-client";
import session from './session';

/**
 * Usage example:
 * const gridDataSource = new DataSource({
 *     store: integrationFactory.createStore({
 *         key: "id",
 *         loadUrl: `/company/${props.companyId}/ros/certificate`
 *     })
 * });
 */

// Temporary IntegrationFactory for DataSource usage, without needing to specify onBeforeSend with x-functions-key
class IntegrationFactory {
    private httpClient: IntegrationHttpClient;

    constructor() {
        this.httpClient = new IntegrationHttpClient();
    }

    public createStore(options: Options = {}): CustomStore {
        if (!options.loadUrl) {
            throw new Error("A URL for data loading is required.");
        }

        // Resolve the full URL using the IntegrationHttpClient
        options.loadUrl = this.httpClient.getUrl(options.loadUrl);

        // Capture any original user-provided onBeforeSend and errorHandler to chain them
        const originalUserOnBeforeSend = options.onBeforeSend;
        options.errorHandler = this.getCombinedErrorHandler(options.errorHandler);

        options.onBeforeSend = (operation, ajaxSettings) => {
            ajaxSettings.headers = ajaxSettings.headers || {};

            ajaxSettings.headers["x-functions-key"] = integrationFunctionsKey;
            if (session.AccessToken) {
                ajaxSettings.headers["Authorization"] = `Bearer ${session.AccessToken}`;
            }

            // Call the original user-provided onBeforeSend, if it exists
            if (originalUserOnBeforeSend) {
                originalUserOnBeforeSend(operation, ajaxSettings);
            }
        };

        return createStore(options);
    }

    // Ensures low complexity score
    private getCombinedErrorHandler(originalErrorHandler: Options["errorHandler"]): Options["errorHandler"] {
        return (err: any) => {
            if (originalErrorHandler) {
                originalErrorHandler(err);
            }

            // Factory's default error handling logic for Integration API responses
            if (err.errorDetails) {
                Notifications.closeMessage(window.Route.NotificationId);
                Notifications.showErrors(err.errorDetails.Message, window.Route.NotificationId);
            } else if (err.xhr && err.xhr.status) {
                const message = `API Error: ${err.xhr.status} ${err.xhr.statusText || "Unknown Error"}`;
                Notifications.showErrors([message], window.Route.NotificationId);
            } else {
                // Catch-all for other unexpected errors
                Notifications.showErrors(["An unknown error occurred while loading data."], window.Route.NotificationId);
            }
        };
    }
}

const integrationFactory = new IntegrationFactory();

export default integrationFactory;
export { integrationFactory };