/// <reference path="../../wwwroot/js/site/payspace.dx.defaults.ts" />

export {};

declare global {
    const Notifications: {
        showErrors(errors: string | string[], id?: string, title?: string): void;
        showWarning(description: string, id?: string, title?: string): void;
        showProgress(description: string, id?: string, title?: string): void;
        showSuccess(description: string, id?: string, title?: string): void;
        closeMessage(id: string): Promise<void>;
    };

    interface MenuItem {
        UniqueKey: string;
        Text: string;
        MenuStructure: number;
        NavigationLinkId: number;
        Target?: string;
        Url?: string;
        Icon?: string;
        Area?: string;
        Action?: string;
        Controller?: string;
        HasMenuOverride?: boolean;
        Lite?: boolean;
        Master?: boolean;
        Premier?: boolean;
        Visible?: boolean;
        Children?: MenuItem[];
    }

    interface Window {
        cachedMenus: MenuItem[];
        Auth: {
            AccessToken: string;
        };
        User: {
            UserId: number;
            UserType: string;
            SessionId: string;
            IsMSS: boolean;
            IsESS: boolean;
            Theme: string;
        };
        Route: {
            NotificationId: string;
            CompanyId?: number;
            EmployeeId?: number;
            FrequencyId?: number;
        };
    }

    declare module DevExpress.ui.dialog {
        export function confirm(
            messageHtml: string,
            title: string,
            showTitle: boolean
        ): DevExpress.core.utils.DxPromise<boolean>;
    }
}