import { getHeaders } from "./http-client";

type MockParams = Record<string, string | number | boolean>;
type MockHeaders = HeadersInit;

export type MockResponse<T> = {
    success: boolean;
    data?: T;
    message?: string;
    status?: number;
};

type MockResponseCallback<T> = (params: MockParams, headers: MockHeaders) => T;
type MockResponseData<T> = T | MockResponseCallback<T>;

export interface MockRequestOptions<T> {
    path: string;
    params?: MockParams;
    response?: MockResponseData<T>; // Optional if request is registered
    delay?: number;
    status?: number;
    message?: string;
    headers?: HeadersInit;
    companyRequest?: boolean;
}

type MockRegistryEntry<T> = Omit<MockRequestOptions<T>, "path">;

export class MockHttpClient {
    private registry: Record<string, MockRegistryEntry<any>> = {};

    public register<T>(path: string, config: MockRegistryEntry<T>) {
        this.registry[path] = config;
    }

    private createRequestHeaders(
        headers?: HeadersInit,
        companyRequest?: boolean
    ): HeadersInit {
        const base = getHeaders(companyRequest === true, headers);
        return {
            ...base,
            "X-Requested-With": "XMLHttpRequest",
        };
    }

    private matchPath(pathWithQuery: string): { entry: MockRegistryEntry<any>; pathParams: MockParams; queryParams: MockParams; } | null {
        const [pathname, queryString] = pathWithQuery.split("?");
        const queryParams = Object.fromEntries(new URLSearchParams(queryString || "").entries());

        for (const registeredPath in this.registry) {
            const regex = new RegExp(
                "^" + registeredPath.replace(/{(\w+)}/g, (_, key) => `(?<${key}>[^/]+)`) + "$"
            );

            const match = pathname.match(regex);
            if (match && match.groups) {
                const entry = this.registry[registeredPath];
                return {
                    entry,
                    pathParams: match.groups,
                    queryParams,
                };
            }
        }

        return null;
    }

    private async simulateRequest<T>(options: MockRequestOptions<T>): Promise<MockResponse<T>> {
        const match = this.matchPath(options.path);
        const {
            params = {},
            response,
            delay = 200,
            status = 200,
            message,
            headers,
            companyRequest,
        } = {
            ...(match?.entry ?? {}),
            ...options,
        };

        const allParams = {
            ...(match?.pathParams ?? {}),
            ...(match?.queryParams ?? {}),
            ...params
        };

        const allHeaders = this.createRequestHeaders(headers, companyRequest);

        return new Promise<MockResponse<T>>((resolve) => {
            setTimeout(() => {
                if (status && status >= 400) {
                    resolve({ success: false, status, message: message ?? `Mock error ${status}` });
                    return;
                }

                const data =
                    typeof response === "function"
                        ? (response as MockResponseCallback<T>)(allParams, allHeaders)
                        : response;

                resolve({ success: true, data });
            }, delay);
        });
    }

    public get<T>(options: MockRequestOptions<T>) {
        return this.simulateRequest(options);
    }

    public post<T>(options: MockRequestOptions<T>) {
        return this.simulateRequest(options);
    }

    public put<T>(options: MockRequestOptions<T>) {
        return this.simulateRequest(options);
    }

    public patch<T>(options: MockRequestOptions<T>) {
        return this.simulateRequest(options);
    }

    public delete<T>(options: MockRequestOptions<T>) {
        return this.simulateRequest(options);
    }

    public format<T>(response: MockResponse<T>): string {
        return JSON.stringify(response, null, 2);
    }
}