<template>
    <Card :title="formatMessage('CardTitle')">
        <DxDataGrid :data-source="dataSource"
                    date-serialization-format="yyyy-MM-dd"
                    :remote-operations="true"
                    :hover-state-enabled="true"
                    :column-auto-width="true"
                    :show-column-lines="false"
                    @init-new-row="onInitNewRow"
                    @row-inserting="(e: RowInsertingEvent) => dataGridFactory.onRowInserting(e)"
                    @row-inserted="(event: DxDataGridTypes.RowInsertedEvent) => dataGridFactory.onRowInserted(event)"
                    @row-updating="(e: DxDataGridTypes.RowUpdatingEvent) => dataGridFactory.onRowUpdating(e)"
                    @row-updated="(event: DxDataGridTypes.RowUpdatedEvent) => dataGridFactory.onRowUpdated(event)"
                    @row-removed="(event: DxDataGridTypes.RowRemovedEvent) => dataGridFactory.onRowRemoved(event)">
            <DxFilterRow :visible="true"></DxFilterRow>
            <DxEditing :allow-updating="allowEdit"
                       :allow-deleting="allowEdit"
                       :allow-adding="allowEdit"
                       mode="row">
            </DxEditing>
            <DxColumn v-bind="metadata.attr('EffectiveDate')"
                      data-type="date"
                      :calculate-filter-expression="calculateODataDateFilterExpression">
                <DxRequiredRule />
            </DxColumn>
            <DxColumn v-bind="metadata.attr('Name')">
                <DxRequiredRule />
            </DxColumn>
            <DxColumn v-bind="metadata.attr('Code')">
                <DxRequiredRule />
                <DxCustomRule :validation-callback="validateUniqueCode" :message="formatMessage('CodeDuplicate')" />
            </DxColumn>
            <DxColumn v-bind="metadata.attr('InactiveDate')"
                      data-type="date"
                      :calculate-filter-expression="calculateODataDateFilterExpression">
                <DxCustomRule :validation-callback="validateInactiveDate" :message="formatMessage('InactiveDateBeforeEffectiveDate')" />
            </DxColumn>
        </DxDataGrid>
    </Card>
</template>

<script lang="ts" setup>
    import { useMetadata } from "@/metadata";
    import { useLocalization } from "@/localization";
    import { formatMessage, formatDate } from "devextreme/localization";
    import { odataFactory } from "@/odata-extensions";
    import { calculateODataDateFilterExpression } from "@/helpers/date-filter-helper";
    import { DxDataGrid, DxColumn, DxFilterRow, DxEditing, DxCustomRule, DxRequiredRule, DxDataGridTypes } from "devextreme-vue/data-grid";
    import { ValidationCallbackData } from "devextreme/common";
    import Card from "@/components/ui/card.vue";
    import DataSource from "devextreme/data/data_source";
    import dataGridFactory from "@/datagrid-extensions";

    const props = defineProps({
        apiControllerName: {
            type: String,
            required: true
        },
        allowEdit: {
            type: Boolean,
            required: true
        }
    });

    await useLocalization("TemplateSetup", "System.Notification");

    const { ComponentVariableDto: metadata } = await useMetadata("ComponentVariableDto");

    const dataSource = new DataSource({
        store: odataFactory.createStore(props.apiControllerName, "ComponentVariableId")
    });

    function validateUniqueCode(e: ValidationCallbackData): boolean {
        return !dataSource.items?.().some(item =>
            item.ComponentVariableId !== e.data?.ComponentVariableId &&
            item.Code?.trim().toLowerCase() === e.value?.trim().toLowerCase()
        );
    }

    function validateInactiveDate(e: ValidationCallbackData): boolean {
        if (!e.value || !e.data?.EffectiveDate) {
            return true;
        }

        const inactiveDate = new Date(e.value);
        const effectiveDate = new Date(e.data.EffectiveDate);
        return inactiveDate >= effectiveDate;
    }

    function onInitNewRow(e: DxDataGridTypes.InitNewRowEvent) {
        e.data.EffectiveDate = formatDate(new Date(), "yyyy-MM-dd");
    }
</script>