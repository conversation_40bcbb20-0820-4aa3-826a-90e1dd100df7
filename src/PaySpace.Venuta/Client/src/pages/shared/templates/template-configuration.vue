<template>
    <Card>
        <DxDataGrid :data-source="dataSource"
                    :remote-operations="true"
                    :hover-state-enabled="true"
                    :column-auto-width="true"
                    :show-column-lines="false"
                    @editing-start="onEditingStart"
                    @cell-prepared="(e: DxDataGridTypes.CellPreparedEvent) => dataGridFactory.onCellPrepared(e, allowEdit)"
                    @row-click="(e: DxDataGridTypes.RowClickEvent) => dataGridFactory.onRowClick(e, allowEdit)"
                    @row-removed="(event: DxDataGridTypes.RowRemovedEvent) => dataGridFactory.onRowRemoved(event)">
            <DxToolbar>
                <DxItem>
                    <DxButton icon="add" stylingMode="contained" type="default" @click="addNewRow" :visible="allowEdit">
                    </DxButton>
                </DxItem>
            </DxToolbar>
            <DxFilterRow :visible="true"></DxFilterRow>
            <DxEditing :allow-adding="allowEdit"
                       :allow-updating="true"
                       :allow-deleting="allowEdit && canDeleteRow"
                       mode="row">
            </DxEditing>
            <DxColumn v-bind="metadata.attr('EffectiveDate')"
                      data-type="date"
                      :calculate-filter-expression="calculateODataDateFilterExpression">
            </DxColumn>
            <DxColumn v-bind="metadata.attr('TemplateCode')"
                      :caption="formatMessage('TemplateId')"
                      name="Template">
                <DxLookup :data-source="templatesLookup"
                          value-expr="Value"
                          display-expr="Description">
                </DxLookup>
            </DxColumn>
        </DxDataGrid>
    </Card>
</template>

<script setup lang="ts">
    import { useMetadata } from "@/metadata";
    import { useLocalization } from "@/localization";
    import { formatMessage } from "devextreme/localization";
    import { odataFactory } from "@/odata-extensions";
    import { calculateODataDateFilterExpression } from "@/helpers/date-filter-helper";
    import { DxButton } from "devextreme-vue/button";
    import { ClickEvent } from "devextreme/ui/button";
    import { DxDataGrid, DxColumn, DxEditing, DxItem, DxToolbar, DxFilterRow, DxLookup, DxDataGridTypes } from "devextreme-vue/data-grid";
    import DataSource from "devextreme/data/data_source";
    import Card from "@/components/ui/card.vue";
    import dataGridFactory from "@/datagrid-extensions";

    const props = defineProps({
        apiControllerName: {
            type: String,
            required: true
        },
        editUrl: {
            type: String,
            required: true
        },
        allowEdit: {
            type: Boolean,
            required: true
        },
        isCompany: {
            type: Boolean,
            required: true,
            default: false
        }
    });

    await useLocalization("TemplateConfiguration");

    const { TemplateConfigurationDto: metadata } = await useMetadata("TemplateConfigurationDto");

    const dataSource = new DataSource({
        store: odataFactory.createStore(props.apiControllerName, "TemplateConfigurationId")
    });

    const templatesLookup = odataFactory.createLookupStore("EmployeeTemplate", "Id");

    function addNewRow(e: ClickEvent) {
        e.cancel = true;
        window.location.href = props.editUrl;
    }

    function onEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        e.cancel = true;

        const id = e.data.TemplateConfigurationId;
        const editUrl = `${props.editUrl}/${id}`;

        // Redirect to the edit page
        window.location.href = editUrl;
    }

    function canDeleteRow(e: { row: DxDataGridTypes.Row }): boolean {
        if (props.isCompany) {
            return e.row.data?.CompanyId != null && e.row.data?.CompanyId > 0;
        }

        return true;
    }
</script>