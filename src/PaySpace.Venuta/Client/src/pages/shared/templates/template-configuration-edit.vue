<template>
    <DxForm :form-data="formData" ref="templateConfigurationForm">
        <DxGroupItem col-count="3">
            <DxSimpleItem v-bind="templateConfigurationMetadata.attr('EffectiveDate')"
                          editor-type="dxDateBox"
                          :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }"
                          :is-required="true">
            </DxSimpleItem>
            <DxSimpleItem v-if="!showTemplateConfigDropdown"
                          v-bind="templateConfigurationMetadata.attr('TemplateCode')"
                          :is-required="true">
                <!--Template dropdown (Agency/Company Level)-->
                <DxDropDownBox v-model:value="formData.TemplateCode"
                               v-model:opened="isTreeBoxOpened"
                               :label="formatMessage('TemplateId')"
                               :data-source="templatesDataSource"
                               :read-only="templateConfigurationId > 0"
                               value-expr="Value"
                               :display-expr="formatTemplateName">
                    <template #content>
                        <DxTreeView :data-source="templatesDataSource"
                                    :select-by-click="true"
                                    :search-enabled="true"
                                    :expand-all-enabled="true"
                                    selection-mode="single"
                                    data-structure="plain"
                                    parent-id-expr="ParentCode"
                                    key-expr="Value"
                                    :display-expr="formatTemplateName"
                                    @item-click="onTemplateClick()"
                                    @item-selection-changed="onTemplateSelect($event)">
                        </DxTreeView>
                    </template>
                </DxDropDownBox>
            </DxSimpleItem>
            <DxSimpleItem v-if="showTemplateConfigDropdown"
                          v-bind="templateConfigurationMetadata.attr('TemplateConfigurationId')"
                          :is-required="true">
                <!--Template configuration dropdown (Employee Level)-->
                <DxDropDownBox v-model:value="formData.TemplateConfigurationId"
                               v-model:opened="isTreeBoxOpened"
                               :label="formatMessage('PageHeader')"
                               :data-source="templateConfigurationDataSource"
                               value-expr="TemplateConfigurationId"
                               display-expr="TemplateCode">
                    <DxTreeView :data-source="templateConfigurationDataSource"
                                :select-by-click="true"
                                :search-enabled="true"
                                :expand-all-enabled="true"
                                selection-mode="single"
                                data-structure="plain"
                                key-expr="TemplateConfigurationId"
                                display-expr="TemplateCode"
                                @item-click="onTemplateClick()"
                                @item-selection-changed="onTemplateConfigSelect($event)">
                    </DxTreeView>
                </DxDropDownBox>
            </DxSimpleItem>
        </DxGroupItem>

        <!--Component Variables-->
        <DxGroupItem col-count="3" :caption="formatMessage('ComponentVariables')">
            <DxDataGrid :data-source="formData.ComponentVariableValues || []"
                        :key-expr="null"
                        :remote-operations="false"
                        :hover-state-enabled="true"
                        :column-auto-width="false"
                        :show-column-lines="false"
                        @editing-start="onEditingStart"
                        @editor-preparing="onEditorPreparing"
                        @row-inserting="(e: DxDataGridTypes.RowInsertingEvent) => dataGridFactory.onRowInserting(e)"
                        @row-updating="(e: DxDataGridTypes.RowUpdatingEvent) => dataGridFactory.onRowUpdating(e)">
                <DxEditing :allow-adding="allowEdit"
                           :allow-updating="allowEdit && canEditRow"
                           :allow-deleting="allowEdit && canDeleteRow"
                           mode="row">
                </DxEditing>
                <DxColumn v-bind="componentVariableValueMetadata.attr('ComponentVariableCode')">
                    <DxRequiredRule />
                    <DxCustomRule :validation-callback="validateComponentVariableCode" :message="formatMessage('CodeDuplicate')" />
                    <DxGridLookup :display-expr="formatComponentVariable"
                                  :data-source="componentVariableLookup"
                                  value-expr="Value" />
                </DxColumn>
                <DxColumn v-bind="componentVariableValueMetadata.attr('IsOverride')"
                          :editor-options="{ readOnly: true }"
                          :visible="allowOverride && (isCompany ? formData.CompanyId === 0 : true)"
                          data-type="boolean">
                </DxColumn>
                <DxColumn v-bind="componentVariableValueMetadata.attr('Value')"
                          data-type="number">
                    <DxRequiredRule />
                </DxColumn>
            </DxDataGrid>
        </DxGroupItem>

        <!--History section -->
        <DxGroupItem :caption="formatMessage('History')" v-if="isEmployee && templateConfigurationId > 0">
            <DxItem>
                <DxDataGrid :data-source="historyDataSource"
                            :remote-operations="true"
                            :column-auto-width="true"
                            @row-removed="onHistoryRowRemoved"
                            @editing-start="onHistoryEditingStart">
                    <DxFilterRow :visible="true" />
                    <DxEditing :allow-adding="allowEdit"
                               :allow-updating="allowEdit && canEditHistoryRow"
                               :allow-deleting="allowEdit"
                               mode="row">
                    </DxEditing>
                    <DxColumn v-bind="templateConfigurationMetadata.attr('EffectiveDate')"
                              :calculate-filter-expression="calculateODataDateFilterExpression"
                              data-type="date"
                              sort-order="desc">
                    </DxColumn>
                    <DxColumn v-bind="templateConfigurationMetadata.attr('TemplateCode')">
                        <DxRequiredRule />
                        <DxGridLookup :data-source="templatesLookup"
                                      display-expr="Description"
                                      value-expr="Value" />
                    </DxColumn>
                </DxDataGrid>
            </DxItem>
        </DxGroupItem>
    </DxForm>

    <PageFooter>
        <DxButton type="default" :text="formatMessage('Cancel')" styling-mode="outlined" @click="redirectToIndex" />
        <DxButton type="default"
                  :text="formatMessage('Update')"
                  :disable="true"
                  :visible="allowEdit"
                  styling-mode="default"
                  @click="onSave" />
    </PageFooter>
</template>

<script lang="ts" setup>
    import { ref } from "vue";
    import { useMetadata } from "@/metadata";
    import { useLocalization } from "@/localization";
    import { formatMessage, formatDate } from "devextreme/localization";
    import { odataFactory } from "@/odata-extensions";
    import { calculateODataDateFilterExpression } from "@/helpers/date-filter-helper";
    import { ODataFormService } from "@/services/odata-form-service";
    import { DxButton, DxDataGrid, DxForm } from "devextreme-vue";
    import { DxColumn, DxEditing, DxLookup as DxGridLookup, DxFilterRow, DxRequiredRule, DxCustomRule, DxDataGridTypes } from "devextreme-vue/data-grid";
    import { DxGroupItem, DxItem, DxSimpleItem } from "devextreme-vue/form";
    import { DxDropDownBox } from "devextreme-vue/drop-down-box";
    import { DxTreeView, DxTreeViewTypes } from "devextreme-vue/tree-view";
    import { DxButtonTypes } from "devextreme-vue/cjs/button";
    import { ValidationCallbackData } from "devextreme/common";
    import DataSource from "devextreme/data/data_source";
    import PageFooter from "@/components/ui/page-footer.vue";
    import dataGridFactory from "@/datagrid-extensions";

    await useLocalization("TemplateConfiguration", "General");

    const { TemplateConfigurationDto: templateConfigurationMetadata, ComponentVariableValueDto: componentVariableValueMetadata } = await useMetadata("TemplateConfigurationDto", "ComponentVariableValueDto");

    const props = defineProps({
        apiControllerName: {
            type: String,
            required: true
        },
        templatesApiControllerName: {
            type: String,
            required: true
        },
        componentVariablesApiControllerName: {
            type: String,
            required: true
        },
        templateConfigurationId: {
            type: Number,
            required: true
        },
        baseUrl: {
            type: String,
            required: true
        },
        allowEdit: {
            type: Boolean,
            required: true
        },
        allowOverride: {
            type: Boolean,
            required: true
        },
        isAgency: {
            type: Boolean,
            required: true,
            default: false
        },
        isCompany: {
            type: Boolean,
            required: true,
            default: false
        },
        isEmployee: {
            type: Boolean,
            required: true,
            default: false
        }
    });

    const oDataFormService = new ODataFormService(props.apiControllerName);

    const isTreeBoxOpened = ref(false);

    const formData = ref({
        TemplateConfigurationId: null,
        TemplateCode: "",
        TemplateId: 0,
        EffectiveDate: formatDate(new Date(), "yyyy-MM-dd"),
        ComponentVariableValues: []
    });

    const templateConfigurationForm = ref<{ instance: dxForm } | null>(null);

    // Computed variables
    const showTemplateConfigDropdown = props.isEmployee && props.templateConfigurationId == 0;

    // Lookups
    const componentVariableLookup = odataFactory.createLookupStore(props.componentVariablesApiControllerName, "ComponentVariableId");
    const templatesLookup = odataFactory.createLookupStore("EmployeeTemplate", "Id");

    // Data sources
    const templateConfigurationDataSource = new DataSource({
        store: odataFactory.createStore("CompanyTemplateConfiguration", "TemplateConfigurationId"),
        filter: ["EffectiveDate", "<=", new Date()]
    });

    const templatesDataSource = new DataSource({
        store: props.templateConfigurationId > 0 ? templatesLookup : odataFactory.createLookupStore(props.templatesApiControllerName, "TemplateId")
    });

    const historyDataSource = new DataSource({
        store: odataFactory.createStore(props.apiControllerName, "TemplateConfigurationId")
    });

    if (props.templateConfigurationId > 0) {
        // fetch form data if its not create
        await reloadTemplateForm(props.templateConfigurationId);
    }

    async function reloadTemplateForm(id: number) {
        formData.value = await oDataFormService.getDataAsync(id);
    }

    function redirectToIndex(id: number = 0): void {
        const url = id > 0 ? `${props.baseUrl}/${id}` : props.baseUrl;
        window.location.href = url;
    }

    async function onSave(e: DxButtonTypes.ClickEvent) {
        e.component.option("disabled", true);

        const form = templateConfigurationForm.value.instance;

        const validationResults = form.validate();

        // Manually check required fields if using custom editors
        if (!showTemplateConfigDropdown && !formData.value.TemplateCode) {
            Notifications.showErrors(formatMessage('TemplateRequired'), window.Route.NotificationId);
            e.component.option("disabled", false);
            return;
        }

        if (showTemplateConfigDropdown && !formData.value.TemplateConfigurationId) {
            Notifications.showErrors(formatMessage('TemplateRequired'), window.Route.NotificationId);
            e.component.option("disabled", false);
            return;
        }

        if (validationResults.isValid) {
            const isEdit = props.templateConfigurationId > 0;

            const result = isEdit
                ? await oDataFormService.putDataAsync(formData.value.TemplateConfigurationId, formData.value)
                : await oDataFormService.postDataAsync(formData.value);

            if (result) {
                redirectToIndex();
            }
        }
        else {
            let errorMessages = validationResults.brokenRules!.map((err: any) => err.message);
            Notifications.showErrors(errorMessages, window.Route.NotificationId);
        }

        e.component.option("disabled", false);
    }

    function onTemplateClick(e: DxTreeViewTypes.ItemClickEvent) {
        isTreeBoxOpened.value = false;
    }

    function onTemplateSelect(e: DxTreeViewTypes.ItemSelectionChangedEvent) {
        formData.value.TemplateConfigurationId = 0;
        formData.value.TemplateId = e.itemData.Id;
        formData.value.TemplateCode = e.itemData.Value;
    }

    // For employee level only
    async function onTemplateConfigSelect(e: any) {
        formData.value.TemplateConfigurationId = e.itemData.TemplateConfigurationId;
        formData.value.TemplateCode = e.itemData.TemplateCode;
        formData.value.ComponentVariableValues = e.itemData.ComponentVariableValues;

        // Set all IsOverride to false
        // This is on create, so there should be no overrides yet on employee level
        formData.value.ComponentVariableValues.forEach((item: any) => {
            item.IsOverride = false;
        });
    }

    function onEditorPreparing(e: DxDataGridTypes.EditorPreparingEvent) {
        if (e.parentType !== 'dataRow') {
            return;
        }

        if (e.dataField === 'ComponentVariableCode') {
            if (e.row?.data?.ComponentVariableValueId != null) {
                e.editorOptions.readOnly = true;
            }
        }
    }

    function onEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        if (props.isEmployee && (e.data.AgencyId || e.data.CompanyId)) {
            e.data.IsOverride = true;
        }

        if (props.isCompany && e.data.AgencyId) {
            e.data.IsOverride = true;
        }
    }

    function formatTemplateName(item: any) {
        if (!item) {
            return '';
        }

        return `${item.Description} (${item.Value})`;
    }

    function formatComponentVariable(item: any) {
        if (!item) {
            return "";
        }

        return `${item.Description} (${item.Value})`;
    }

    function validateComponentVariableCode(e: ValidationCallbackData) {
        return !(formData.value.ComponentVariableValues).some(row =>
            row.ComponentVariableValueId !== e.data?.ComponentVariableValueId &&
            row.ComponentVariableCode?.trim().toLowerCase() === e.value?.trim().toLowerCase()
        );
    }

    // Employee level
    // The edit button should not be visible on the currently loaded history record
    function canEditHistoryRow(e: { row: DxDataGridTypes.Row }): boolean {
        return e.row.data?.TemplateConfigurationId != formData.value.TemplateConfigurationId;
    }

    function canEditRow(e: { row: DxDataGridTypes.Row }): boolean {
        // Security
        if (props.allowEdit === false) {
            return false;
        }

        // Row has been created client side
        if (e.row.data?.ComponentVariableValueId == null || e.row.data?.ComponentVariableValueId === 0) {
            return true;
        }

        // Always allow edit for override
        if (e.row.data?.IsOverride === false) {
            return true;
        }

        if (props.isEmployee) {
            return e.row.data?.EmployeeId != null;
        }

        if (props.isCompany) {
            return e.row.data?.CompanyId != null;
        }

        if (props.isAgency) {
            return e.row.data?.AgencyId != null;
        }
    }

    function canDeleteRow(e: { row: DxDataGridTypes.Row }): boolean {
        // Row has been created client side
        if (e.row.data?.ComponentVariableValueId == null || e.row.data?.ComponentVariableValueId === 0) {
            return true;
        }

        if (props.isEmployee) {
            return e.row.data?.EmployeeId != null;
        }

        if (props.isCompany) {
            return e.row.data?.CompanyId != null;
        }

        if (props.isAgency) {
            return e.row.data?.AgencyId != null;
        }
    }

    async function onHistoryEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        e.cancel = true;
        redirectToIndex(e.data.TemplateConfigurationId);
    }

    function onHistoryRowRemoved(e: DxDataGridTypes.RowRemovedEvent) {
        dataGridFactory.onRowRemoved(e);

        // Load the latest template in editing mode if exists
        redirectToIndex();
    }
</script>