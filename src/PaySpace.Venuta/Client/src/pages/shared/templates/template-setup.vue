<template>
    <Card>
        <DxTreeList parent-id-expr="ParentTemplateId"
                    date-serialization-format="yyyy-MM-dd"
                    :data-source="dataSource"
                    :hover-state-enabled="true"
                    :show-column-lines="false"
                    :remote-operations="true"
                    :column-auto-width="true"
                    :error-row-enabled="false"
                    :has-items-expr="hasItemsExpr"
                    :auto-expand-all="true"
                    @editor-preparing="onEditorPreparing"
                    @init-new-row="onInitNewRow"
                    @row-inserting="(e: DxTreeListTypes.RowInsertingEvent) => dataGridFactory.onRowInserting(e)"
                    @row-inserted="(event: DxTreeListTypes.RowInsertedEvent) => dataGridFactory.onRowInserted(event)"
                    @row-updating="(e: DxTreeListTypes.RowUpdatingEvent) => dataGridFactory.onRowUpdating(e)"
                    @row-updated="(event: DxTreeListTypes.RowUpdatedEvent) => dataGridFactory.onRowUpdated(event)"
                    @row-removed="(event: DxTreeListTypes.RowRemovedEvent) => dataGridFactory.onRowRemoved(event)">
            <DxToolbar>
                <DxItem name="addRowButton" :options="{ stylingMode: 'contained', type: 'default' }" :visible="allowEdit">
                </DxItem>
            </DxToolbar>
            <DxFilterRow :visible="true"></DxFilterRow>
            <DxEditing :allow-updating="allowEdit"
                       :allow-deleting="allowEdit"
                       :allow-adding="({ row })=> allowEdit && !row.data.ParentTemplateId"
                       mode="row">
            </DxEditing>
            <DxColumn v-bind="metadata.attr('EffectiveDate')"
                      data-type="date"
                      :calculate-filter-expression="calculateODataDateFilterExpression">
                <DxRequiredRule />
            </DxColumn>
            <DxColumn v-bind="metadata.attr('Name')">
                <DxRequiredRule />
            </DxColumn>
            <DxColumn v-bind="metadata.attr('Code')">
                <DxRequiredRule />
                <DxCustomRule :validation-callback="validateUniqueCode" :message="formatMessage('CodeDuplicate')" />
            </DxColumn>
        </DxTreeList>
    </Card>
</template>

<script lang="ts" setup>
    import { useMetadata } from "@/metadata";
    import { useLocalization } from "@/localization";
    import { formatDate, formatMessage } from "devextreme/localization";
    import { odataFactory } from "@/odata-extensions";
    import { calculateODataDateFilterExpression } from "@/helpers/date-filter-helper";
    import { DxTreeList, DxColumn, DxFilterRow, DxEditing, DxToolbar, DxItem, DxCustomRule, DxRequiredRule, DxTreeListTypes } from "devextreme-vue/tree-list";
    import { ValidationCallbackData } from "devextreme/common";
    import DataSource from "devextreme/data/data_source";
    import Card from "@/components/ui/card.vue";
    import dataGridFactory from "@/datagrid-extensions";

    const props = defineProps({
        apiControllerName: {
            type: String,
            required: true
        },
        allowEdit: {
            type: Boolean,
            required: true
        }
    });

    await useLocalization("TemplateSetup");

    const { TemplateDto: metadata } = await useMetadata("TemplateDto");

    const dataSource = new DataSource({
        store: odataFactory.createStore(props.apiControllerName, "TemplateId")
    });

    function validateUniqueCode(e: ValidationCallbackData): boolean {
        return !dataSource.items?.().some(item =>
            item.TemplateId !== e.data?.TemplateId &&
            item.Code?.trim().toLowerCase() === e.value?.trim().toLowerCase()
        );
    }

    function onEditorPreparing(e: DxDataGridTypes.EditorPreparingEvent) {
        if (e.parentType !== 'dataRow') return;

        const isSubTemplate = !!e.row?.data?.ParentTemplateId;

        if (e.dataField === 'EffectiveDate' && isSubTemplate) {
            e.editorOptions.disabled = true;
        }
    }

    function onInitNewRow(e: DxTreeListTypes.InitNewRowEvent) {
        if (e.data.ParentTemplateId) {
            const parentItem = dataSource.items()?.find(t => t.key === e.data.ParentTemplateId);
            e.data.ParentTemplateCode = parentItem.data.Code;

            if (parentItem?.data?.EffectiveDate) {
                e.data.EffectiveDate = parentItem.data.EffectiveDate;
            }
        }
        else {
            e.data.EffectiveDate = formatDate(new Date(), "yyyy-MM-dd");
        }
    }

    // Only show expand arrow for parent (level 1) rows
    function hasItemsExpr(row: any) {
        return !row.ParentTemplateId;
    }
</script>