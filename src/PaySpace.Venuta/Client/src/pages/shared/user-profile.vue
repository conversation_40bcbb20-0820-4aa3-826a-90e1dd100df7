<template>
    <Offcanvas ref="offcanvasSubAgencyRef" @save="() => agencySubDetailsRef.save()" type="form" :title="formatMessage('lblOffcanvasSubAgencyLabel')" :visible="subAgencyUserId > 0">
        <template v-slot:body v-if="subAgencyUserId > 0">
            <agency-sub-details ref="agencySubDetailsRef" :data-url="dataUrl" :user-id="subAgencyUserId" :country-id="1" :key="subAgencyUserId"/>
        </template>
    </Offcanvas>

    <Offcanvas v-if="userFlags.includes('allowBureauSettings')" id="offcanvasUserSearch" :title="formatMessage('lblUserSearch')" :visible="subAgencyUserId > 0">
        <template v-slot:body>
            <global-user-search :base-url="baseUrl"/>
        </template>
    </Offcanvas>

    <div class="alert alert-danger d-flex flex-row justify-content-between" v-if="allowContactTypes && completedContactTypes.length < contactTypes.length">
        {{ formatMessage('lblAllocateAllContactType', contactTypes.length-completedContactTypes.length, contactTypes.length) }}
        <div>
            <a class="alert-link" id="btnViewDetails">
                {{ formatMessage('lblBtnViewIncompleteContacts')}}
            </a>
            <DxPopover :width="200"
                        target="#btnViewDetails"
                        show-event="click"
                        position="bottom">
                <p class="p-5 fw-bold"> {{ formatMessage('lblIncompleteContacts', contactTypes.length-completedContactTypes.length, contactTypes.length) }} </p>
                <p class="p-5" v-for="contact in contactTypes">
                    <span>
                        <i class="fa-solid fa-circle-check text-success" v-if="completedContactTypes.find(item => item === ContactType[contact])" />
                        <i class="fa-solid fa-circle-exclamation text-danger" v-else="completedContactTypes.find(item => item === ContactType[contact])" />
                    </span> {{ formatMessage(contact) }}
                </p>
            </DxPopover>
        </div>
    </div>
    <DxDataGrid
        ref="userGrid"
        :key="selectedUserType"
        :data-source="gridDataSource"
        :remote-operations="true"
        :hover-state-enabled="true"
        :column-auto-width="true"
        :column-chooser="{ enabled: true }"
        @initialized="initialized"
        @editor-preparing="onEditorPreparing"
        @editing-start="onEditingStart"
        @row-click="onRowClick"
        @cell-prepared="onCellPrepared"
        @option-changed="optionChanged"
        @exporting="onExporting">

        <template #securityGroupFilterEditorComponentTemplate="{ data: conditionInfo }">
            <DxFormLookup
                :data-source="createStore({
                    key: 'value',
                    loadUrl: `${props.baseUrl}/security-role`,
                })"
                display-expr='text'
                value-expr='value'
                width="200"
                :value="conditionInfo.value"
                @value-changed="(e: ValueChangedEvent) => filterEditorValueChanged(conditionInfo, e)"
                :show-clear-button=true />
        </template>

        <template #companyFrequencyFilterEditorComponentTemplate="{ data: conditionInfo }">
            <DxFormLookup
                :data-source="createStore({
                    key: 'value',
                    loadUrl: `${props.baseUrl}/company-frequencies`,
                })"
                display-expr='text'
                value-expr='value'
                width="200"
                :value="conditionInfo.value"
                @value-changed="(e: ValueChangedEvent) => filterEditorValueChanged(conditionInfo, e)"
                :show-clear-button=true />
        </template>

        <template #companyFilterEditorComponentTemplate="{ data: conditionInfo }">
            <DxFormLookup
                :data-source="createStore({
                    key: 'value',
                    loadUrl: `${props.baseUrl}/agency-companies`,
                })"
                display-expr='text'
                value-expr='value'
                width="200"
                :value="conditionInfo.value"
                @value-changed="(e: ValueChangedEvent) => filterEditorValueChanged(conditionInfo, e)"
                :show-clear-button=true />
        </template>

        <DxFilterBuilder>
            <DxCustomOperation
                :calculate-filter-expression="listFilterExpression"
                name="hasContactType"
                :caption="formatMessage('lblHasContactType')"
                icon="check"
                :hasValue="true"
                :customize-text="listFilterCustomizeText"
                v-if="allowContactTypes"
            />
            <DxCustomOperation
                :calculate-filter-expression="listFilterExpression"
                name="hasSecurityGroup"
                :caption="formatMessage('lblHasSecurityGroup')"
                icon="check"
                :customize-text="listFilterCustomizeText"
                editor-template="securityGroupFilterEditorComponentTemplate"
            />
            <DxCustomOperation
                :calculate-filter-expression="listFilterExpression"
                name="hasCompanyFrequency"
                :caption="formatMessage('lblHasCompanyFrequency')"
                icon="check"
                :customize-text="listFilterCustomizeText"
                editor-template="companyFrequencyFilterEditorComponentTemplate"
            />
            <DxCustomOperation
                :calculate-filter-expression="listFilterExpression"
                name="hasCompany"
                :caption="formatMessage('lblHasCompany')"
                icon="check"
                :customize-text="listFilterCustomizeText"
                editor-template="companyFilterEditorComponentTemplate"
            />
        </DxFilterBuilder>
        <DxExport :enabled="true"/>
        <DxPaging :page-size="10"/>
        <DxFilterPanel :visible="true"/>
        <DxFilterRow :visible="true"/>
        <DxEditing
            :use-icons="true"
            :allow-adding="false"
            :allow-updating="allowEdit"
            :allow-deleting="false">
        </DxEditing>

        <DxColumn
            data-field="userType"
            v-model:filter-value="selectedUserType"
            :caption="formatMessage('lblUserType')"
            v-if="!isBureauScreen">
            <DxLookup :data-source="userTypeEditorDataSource" display-expr="text" value-expr="value"/>
        </DxColumn>
        <DxColumn
            data-field="employeeNumber"
            :caption="formatMessage('EmployeeNumber')"
            v-if="!isBureauScreen"></DxColumn>
        <DxColumn data-field="firstName" :caption="formatMessage('lblFirstName')">
        </DxColumn>
        <DxColumn
            data-field="lastName"
            :caption="formatMessage('lblLastName')"
            sort-order="asc"
            :sort-index="2">
        </DxColumn>
        <DxColumn
            data-field="email"
            edit-cell-template="emailTemplate"
            :caption="formatMessage('lblEmail')">
        </DxColumn>
        <DxColumn
            data-field="userStatus"
            :caption="formatMessage('lblStatus')"
            sort-order="asc"
            :sort-index="1"
            editor-type="dxLookup">
            <DxLookup
                :data-source="createStore({ key: 'value', loadUrl: `${props.baseUrl}/user-status` })"
                display-expr="text"
                value-expr="value" />
        </DxColumn>
        <DxColumn
            data-field="jobTitle"
            :caption="formatMessage('lblJobTitle')"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="mobileNumber"
            :caption="formatMessage('lblContactNumber')"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="loginDate"
            :caption="formatMessage('lblLoginDate')"
            :allow-editing="false"
            data-type="date"/>
        <DxColumn data-field="userCompanyContactTypes"
            :caption="formatMessage('lblContactType')"
            :filterOperations="['hasContactType']"
            :allow-sorting="false"
            v-if="!isBureauScreen && allowContactTypes">
            <DxLookup
                :data-source="createStore({
                    key: 'value',
                    loadUrl: `${props.baseUrl}/contact-type`
                })"
                display-expr='text'
                value-expr='value' />
        </DxColumn>
        <DxColumn
            data-field="securityGroupUsers"
            :caption="formatMessage('lblSecurityRoleAccess')"
            :filterOperations="['hasSecurityGroup']"
            :visible="false"
            :show-in-column-chooser="false">
            <DxLookup
                :data-source="createStore({
                    key: 'value',
                    loadUrl: `${props.baseUrl}/security-role`,
                })"
                display-expr='text'
                value-expr='value' />
        </DxColumn>
        <DxColumn
            data-field="userCompanyLinks"
            :caption="formatMessage('lblCompanyFrequency')"
            :filterOperations="['hasCompanyFrequency']"
            :visible="false"
            :show-in-column-chooser="false"
            v-if="!isBureauScreen">
                <DxLookup
                    :data-source="createStore({
                        key: 'value',
                        loadUrl: `${props.baseUrl}/company-frequencies`,
                    })"
                    display-expr='text'
                    value-expr='value' />
        </DxColumn>
        <DxColumn
            data-field="userAgencyLinks"
            :caption="formatMessage('lblCompanyAccess')"
            :filterOperations = "['hasCompany']"
            :visible="false"
            v-if="userFlags.includes('allowAgency')">
            <DxLookup
                :data-source="createStore({
                    key: 'value',
                    loadUrl: `${props.baseUrl}/agency-companies`
                })"
                display-expr='text'
                value-expr='value' />
        </DxColumn>
        <DxColumn
            data-field="bureauAdmin"
            :caption="formatMessage('lblBureauAdmin')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="allocateContact"
            :caption="formatMessage('lblAllocateContact')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="doesNotRequireSystemAccess"
            :caption="formatMessage('lblSystemAccess')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="canEditHistoricalRecords"
            :caption="formatMessage('lblHistoricalRecords')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="analyticsUser"
            :caption="formatMessage('lblAnalytics')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="isCloudRoomUser"
            :caption="formatMessage('lblCloudRoom')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="isBudgetUser"
            :caption="formatMessage('lblBudgetUser')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false"
            :allow-filtering="userFlags.includes('isBudgetUser')" />
        <DxColumn
            data-field="isAgencyTopLevelUser"
            :caption="formatMessage('lblIsAgencyTop')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false" />
        <DxColumn
            data-field="isPowerBiUser"
            :caption="formatMessage('lblPowerBI')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false"
            :allow-filtering="userFlags.includes('isPowerBIUser')" />
        <DxColumn
            data-field="isOrgChartUser"
            :caption="formatMessage('lblOrgChart')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false"
            :allow-filtering="userFlags.includes('allowBureauSettings')" />
        <DxColumn
            data-field="overrideLockDown"
            :caption="formatMessage('lblOverrideLockDown')"
            data-type="boolean"
            :visible="false"
            :show-in-column-chooser="false"
            :allow-filtering="userFlags.includes('overrideLockDown')" />
        <DxColumn
            data-field="mfaLevel"
            :caption="formatMessage('lblMfaLevel')"
            data-type="dxLookup"
            :visible="false"
            :show-in-column-chooser="false">
            <DxLookup
                :data-source="[
                    { text: formatMessage('lblMfaOptionNotSet'), value: -1 },
                    { text: formatMessage('lblMfaOptionNone'), value: 0 },
                    { text: formatMessage('lblMfaOptionAuth'), value: 1 },
                    { text: formatMessage('lblMfaOptionSMS'), value: 2 }]"
                display-expr='text'
                value-expr='value' />
        </DxColumn>
        <DxColumn
                data-field="isTrainingUser"
                :caption="formatMessage('lblAllowTrainingCompanyReset')"
                data-type="boolean"
                v-if="isBureauScreen"
                :visible="false" />

            <DxColumn
                type="buttons"
                :show-in-column-chooser="false">
                <DxGridButton name="edit" />
                <DxGridButton :hint="formatMessage('lblOffcanvasSubAgencyLabel')"
                              :visible="(e) => e.row.data.userType === UserTypeNumber.Agency && userFlags.includes('agencySubInvoiceDetails')"
                              :on-click="clickSubAgency"
                              icon="hierarchy" />
                <DxGridButton :hint="formatMessage('lblReplicateUser')"
                              :visible="canReplicateUser"
                              :on-click="replicateUser"
                              icon="group" />
            </DxColumn>

        <DxToolbar>
            <DxItem location="after" v-if="allowEdit && selectedUserType != UserTypeNumber.Employee">
                <DxButton icon="add" @click="addNewRow" stylingMode="contained" type="default" />
            </DxItem>
            <DxItem name="exportButton"></DxItem>
            <DxItem name="columnChooserButton" location="after" />
        </DxToolbar>

    </DxDataGrid>
</template>

<script setup lang="ts">
    import agencySubDetails from "@/components/agency-sub-details.vue";
    import globalUserSearch from "@/components/global-user-search.vue";
    import dataGridFactory from '@/datagrid-extensions';
    import { ContactType, UserTypeNumber } from "@/enums";
    import { useLocalization } from "@/localization";
    import { createStore } from 'devextreme-aspnet-data-nojquery';
    import { DxButton } from "devextreme-vue/button";
    import { DxColumn, DxCustomOperation, DxDataGrid, DxEditing, DxExport, DxFilterBuilder, DxFilterPanel, DxFilterRow, DxButton as DxGridButton, DxItem, DxLookup, DxPaging, DxToolbar, DxDataGridTypes } from 'devextreme-vue/data-grid';
    import { DxLookup as DxFormLookup } from 'devextreme-vue/lookup';
    import { DxPopover } from 'devextreme-vue/popover';
    import DataSource from "devextreme/data/data_source";
    import { exportDataGrid } from "devextreme/excel_exporter";
    import { formatMessage } from 'devextreme/localization';
    import dxDataGrid, { ColumnButtonClickEvent } from "devextreme/ui/data_grid";
    import Lookup, { Properties as DxLookupOptions } from "devextreme/ui/lookup";
    import { Workbook } from 'exceljs';
    import { saveAs } from 'file-saver-es';
    import { computed, ref } from "vue";
    import Offcanvas from "@/components/ui/offcanvas.vue";
    import type { ValueChangedEvent } from "devextreme/ui/lookup";

    interface User {
        userType: UserTypeNumber;
        userStatus: number;
        bureauAdmin: boolean;
        allocateContact: boolean;
        doesNotRequireSystemAccess: boolean;
        canEditHistoricalRecords: boolean;
        analyticsUser: boolean;
        isCloudRoomUser: boolean;
        isBudgetUser: boolean;
        isAgencyTopLevelUser: boolean;
        isPowerBIUser: boolean;
        isOrgChartUser: boolean;
        overrideLockDown: boolean;
        isTrainingUser: boolean;
        employeeNumber: string;
    }

    interface ListFilters {
        [key: string]: string | number | boolean | null;
    }

    const props = defineProps({
        baseUrl: {
            type: String,
            required: true
        },
        dataUrl: {
            type: String,
            required: true
        },
        editUrl: {
            type: String,
            required: true
        },
        replicateUserUrl: {
            type: String,
            required: true
        },
        selectedContactTypeUrl: {
            type: String,
            required: true
        },
        userTypeId: {
            type: Number,
            required: true
        },
        allowEdit: {
            type: Boolean,
            required: true
        },
        flags: {
            type: String,
            required: true
        },
        contactTypes: {
            type: String,
            required: true
        },
        showPasswordField: {
            type: Boolean,
            required: true
        },
        isBureauScreen: {
            type: Boolean,
            required: true
        },
        canReplicateUsers: {
            type: Boolean,
            required: true
        }
    });

    const contactTypes = Object.keys(ContactType).filter((item) => {
        return isNaN(Number(item));
    });

    await useLocalization('User', 'General');

    Lookup.defaultOptions({
        options: {
            cleanSearchOnOpening: false,
            wrapItemText: true,
            showClearButton: false
        } as DxLookupOptions
    });

    const selectedUserType = ref<UserTypeNumber>(props.userTypeId);
    const userFlags: Array<string> = props.flags ? JSON.parse(props.flags) : new Array<string>;
    const completedContactTypes = ref<Array<number>>(props.contactTypes ? JSON.parse(props.contactTypes) : new Array<number>);
    const userGrid = ref<{ instance: dxDataGrid }>();
    const agencySubDetailsRef = ref();
    const offcanvasSubAgencyRef = ref();
    const listFilters = ref<ListFilters>({});

    let subAgencyUserId = ref<number>(0);

    const gridDataSource = computed(() => new DataSource({
        store: createStore({
            key: 'userId',
            loadUrl: props.baseUrl,
            insertUrl: props.baseUrl,
            updateUrl: props.baseUrl,
            loadParams: {
                userType: selectedUserType.value,
                ...listFilters.value
            }
        })
    }));

    const userTypeEditorDataSource = computed(() => createStore({
        key: 'value',
        loadUrl: `${props.baseUrl}/user-type`,
    }));

    const allowContactTypes = computed(() => selectedUserType.value === UserTypeNumber.Agency || !userFlags.includes('disableContactTypes'));

    let userTypeDataSource = createStore({
        key: 'value',
        loadUrl: `${props.baseUrl}/user-type`
    });

    function listFilterExpression (filterValue: any, field:any) {
        listFilters.value[field.dataField] = filterValue;
    }

    function listFilterCustomizeText (e:any) {
        let value = e.field?.lookup?.items?.find((item: any) => item.value == e.value);
        if (value?.text) {
            return  value?.text;
        }

        return e.value;
    }

    function filterEditorValueChanged(conditionInfo: any, e: ValueChangedEvent) {
        conditionInfo.setValue(e.value)
    }

    function optionChanged(e: { name: string, value: [] }) {
        if (e.name == "filterValue" && e.value == null) {
            Object.keys(listFilters.value).forEach((key) => delete listFilters.value[key]);
        }

        if (e.name == "filterValue" && e.value) {
            let filters = e.value?.flatMap(_ => _);
            Object.keys(listFilters.value).forEach((key) => {
                if (!filters.find(_ => _ === key)) {
                    delete listFilters.value[key];
                }
            });
        }
    }

    function userTypeChanged(e: { value: UserTypeNumber }) {
        selectedUserType.value = e.value;
    }

    function clickSubAgency(e: ColumnButtonClickEvent): void {
        subAgencyUserId.value = e.row?.data.userId;
        offcanvasSubAgencyRef.value.open();
    }

    function initialized(e: { component: any }) {
        if (selectedUserType.value === UserTypeNumber.Bureau) {
            e.component.columnOption("userCompanyContactTypes", "visible", false);
        }
    }

    function onRowClick(e: DxDataGridTypes.RowClickEvent) {
        dataGridFactory.onRowClick(e, props.allowEdit);
    }

    function addNewRow(e: { data: User }) {
        window.location.href = `${props.editUrl}`;
    }

    function onEditingStart(e: { key: number, component: dxDataGrid, data: User }) {
        e.cancel = true;
        window.location.href = `${props.editUrl}?userType=${e.data.userType}&userId=${e.key}`;
    }

    function onCellPrepared(e: DxDataGridTypes.CellPreparedEvent) {
        dataGridFactory.onCellPrepared(e, props.allowEdit);
    }

    async function onEditorPreparing(e: DxDataGridTypes.EditorPreparingEvent) {
        if (e.parentType === 'filterRow' && e.dataField === "userCompanyContactTypes") {
            e.editorOptions = { disabled: true };
        }
    }

    async function contactTypeCellTemplate(container: any, e: any) {
        const text = (e.value || []).map((contactType: any) => e.column.lookup.calculateCellValue(contactType)).join(', ');
        container.textContent = text;
        container.title = text;
    }

    function onExporting(e: DxDataGridTypes.ExportingEvent) {
        const hiddenColumnsToExport: string[] = [];
        if (props.isBureauScreen) {
            hiddenColumnsToExport.push('bureauAdmin');
        }
        if (!props.isBureauScreen && selectedUserType.value === UserTypeNumber.Company) {
            hiddenColumnsToExport.push('doesNotRequireSystemAccess');
        }
        if (!props.isBureauScreen && [UserTypeNumber.Agency, UserTypeNumber.Company].includes(selectedUserType.value)) {
            hiddenColumnsToExport.push('canEditHistoricalRecords');
        }
        if (props.isBureauScreen || [UserTypeNumber.Agency, UserTypeNumber.Company].includes(selectedUserType.value)) {
            hiddenColumnsToExport.push('analyticsUser');
            hiddenColumnsToExport.push('isCloudRoomUser');
        }
        if (!props.isBureauScreen && selectedUserType.value === UserTypeNumber.Agency) {
            hiddenColumnsToExport.push('isAgencyTopLevelUser');
        }
        if (!props.isBureauScreen && [UserTypeNumber.Agency, UserTypeNumber.Company].includes(selectedUserType.value)) {
            hiddenColumnsToExport.push('isOrgChartUser');
        }
        if (props.isBureauScreen || selectedUserType.value === UserTypeNumber.Agency) {
            hiddenColumnsToExport.push('mfaLevel');
        }
        if (props.isBureauScreen) {
            hiddenColumnsToExport.push('isTrainingUser');
        }

        hiddenColumnsToExport.forEach(c => {
            e.component.columnOption(c, "visible", true);
        });

        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('Users');

        exportDataGrid({
            component: e.component,
            worksheet,
            autoFilterEnabled: true,
        }).then(() => {
            workbook.xlsx.writeBuffer().then((buffer) => {
                saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'UsersExtract.xlsx');
            });
        })
        .then(() => {
            hiddenColumnsToExport.forEach(c => {
                e.component.columnOption(c, "visible", false);
            });
        });

        e.cancel = true;
    }

    function replicateUser(e: ColumnButtonClickEvent) {
        const userEmail = e.row?.data.email;
        if (userEmail) {
            window.location.href = `${props.replicateUserUrl}?email=${encodeURIComponent(userEmail)}`;
        }
    }

    function canReplicateUser() {
        // button should be hidden on bureau screen
        if (props.isBureauScreen) {
            return false;
        }

        // button should be hidden for all users in PROD
        if (document.querySelector('meta[name="ps:env"]')?.content === "production") {
            return false;
        }

        return props.canReplicateUsers;
    }
</script>

<style scoped>
    .dx-texteditor.dx-editor-outlined {
        border: none;
    }
</style>