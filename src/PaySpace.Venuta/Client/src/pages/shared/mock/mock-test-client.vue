<template>
    <card>
        <!-- Action Buttons -->
        <div class="d-flex flex-wrap gap-2 mb-4">
            <button @click="fetchEmployees" class="btn btn-primary">GET</button>
            <button @click="createEmployee" class="btn btn-success">POST</button>
            <button @click="updateEmployee" class="btn btn-warning text-dark">PUT</button>
            <button @click="deleteEmployee" class="btn btn-danger">DELETE</button>
        </div>

        <!-- Response Output -->
        <div class="mb-4">
            <div v-if="loading" class="text-muted mb-2">Loading response...</div>
            <pre v-if="result" class="bg-light border rounded p-3 text-body small">{{ result }}</pre>
        </div>

        <!-- Employee List -->
        <div>
            <h2 class="h5 mb-3">Employee RPN List</h2>
            <div v-if="isLoadingEmployees" class="text-muted">Loading employees...</div>
            <div v-else-if="error" class="text-danger">{{ error }}</div>
            <ul v-else class="list-group">
                <li v-for="employee in employees" :key="employee.id" class="list-group-item">
                    {{ employee.employeeName }}
                    <div v-if="employee.ppsNumber" class="text-muted small ms-2 mt-1">
                        PPS Number: {{ employee.ppsNumber }}
                    </div>
                </li>
            </ul>
        </div>
    </card>
</template>

<script setup lang="ts">
    import { ref } from "vue";
    import mockClient, { EmployeeRPN } from "./mock-setup";

    const props = defineProps({
        companyId: {
            type: Number,
            required: true,
        },
    });

    const employees = ref<EmployeeRPN[]>([]);
    const isLoadingEmployees = ref(false);
    const loading = ref(false);
    const result = ref<string | null>(null);
    const error = ref<string | null>(null);

    async function fetchEmployees() {
        isLoadingEmployees.value = true;
        result.value = null;
        error.value = null;

        try {
            const res = await mockClient.get<EmployeeRPN[]>({
                path: `company/${props.companyId}/employee-rpn/list?order=desc&page=2`,
            });

            result.value = mockClient.format(res);

            if (res.success && res.data) {
                employees.value = res.data;
            } else {
                error.value = res.message ?? "Failed to fetch employees";
            }
        } catch {
            error.value = "Network or unexpected error";
        } finally {
            isLoadingEmployees.value = false;
        }
    }

    async function createEmployee() {
        const res = await mockClient.post<EmployeeRPN>({
            path: `company/${props.companyId}/employee-rpn`,
        });

        result.value = mockClient.format(res);
    }

    async function updateEmployee() {
        const res = await mockClient.patch<EmployeeRPN>({
            path: `company/${props.companyId}/employee-rpn/{employeeId}`,
            params: { employeeId: 3 },
            response: {
                id: 3,
                employeeName: "Patched Employee",
                ppsNumber: "1234567A",
            },
            delay: 1000,
        });

        result.value = mockClient.format(res);
    }

    async function deleteEmployee() {
        const res = await mockClient.delete<{ id: number }>({
            path: `company/${props.companyId}/employee-rpn/{employeeid}`,
            params: { employeeid: 3 },
            response: { id: 3 },
        });

        result.value = mockClient.format(res);
    }
</script>