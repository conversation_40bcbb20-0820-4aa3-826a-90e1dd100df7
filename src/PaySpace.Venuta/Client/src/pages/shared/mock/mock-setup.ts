import { MockHttpClient } from "../../../mock-http-client";

export const mockClient = new MockHttpClient();

// Interface
export interface EmployeeRPN {
    id: number;
    employeeName: string;
    ppsNumber?: string;
}

// Register GET (list)
mockClient.register<EmployeeRPN[]>("company/{companyId}/employee-rpn/list", {
    response: (params) => {
        // Access params.companyId, params.order, params.page, etc.
        return [
            { id: 1, employeeName: "Alice", ppsNumber: "1234567A" },
            { id: 2, employeeName: "Bob", ppsNumber: "9876543B" }
        ];
    },
    delay: 300
});

// Register POST
mockClient.register<EmployeeRPN>("company/{companyId}/employee-rpn", {
    response: {
        id: 3,
        employeeName: "New Employee",
        ppsNumber: "3333333C"
    },
    delay: 200
});

export default mockClient;