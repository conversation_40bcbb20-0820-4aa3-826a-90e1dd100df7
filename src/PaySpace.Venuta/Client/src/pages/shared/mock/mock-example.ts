import { MockHttpClient } from "../../../mock-http-client";

// Usage examples:
// Interface
interface EmployeeRPN {
    id: number;
    employeeName: string;
    ppsNumber?: string;
}

const mockClient = new MockHttpClient();

// Register a mock for later usage
mockClient.register("/employee-rpn/list", {
    response: [
        { id: 1, employeeName: "Alice" },
        { id: 2, employeeName: "Bob" },
    ],
    delay: 100,
});

// GET example
export async function getAsync() {
    const res = await mockClient.get<EmployeeRPN>({
        path: "/employee-rpn/{employeeId}",
        params: { employeeId: 5 },
        response: (params) => ({
            id: +params.employeeId,
            employeeName: `User ${params.employeeId}`,
            ppsNumber: "1234567A",
        }),
    });

    return mockClient.format(res);
}

// POST example
export async function postAsync() {
    const res = await mockClient.post<EmployeeRPN>({
        path: "/employee-rpn",
        response: {
            id: 1001,
            employeeName: "New Employee",
            ppsNumber: "1234567A",
        },
    });

    return mockClient.format(res);
}

// PUT example
export async function putAsync() {
    const res = await mockClient.put<EmployeeRPN>({
        path: "/employee-rpn/{employeeId}",
        params: { employeeId: 1001 },
        response: {
            id: 1001,
            employeeName: "Updated Employee",
            ppsNumber: "1234567A",
        },
    });

    return mockClient.format(res);
}

// PATCH example
export async function patchAsync() {
    const res = await mockClient.patch<EmployeeRPN>({
        path: "/employee-rpn/{employeeId}",
        params: { employeeId: 1001 },
        response: {
            id: 1001,
            employeeName: "Patched Employee",
            ppsNumber: "1234567A",
        },
    });

    return mockClient.format(res);
}

// DELETE example
export async function deleteAsync() {
    const res = await mockClient.delete<{ id: number }>({
        path: "/employee-rpn/{employeeId}",
        params: { employeeId: 1001 },
        response: { id: 1001 },
    });

    return mockClient.format(res);
}

// GET example from pre-registered entry (only path needed)
export async function getListAsync() {
    const res = await mockClient.get<EmployeeRPN[]>({ path: "/employee-rpn/list", response: [] });
    return mockClient.format(res);
}