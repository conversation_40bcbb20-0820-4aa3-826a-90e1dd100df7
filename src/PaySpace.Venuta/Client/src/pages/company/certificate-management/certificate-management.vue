<template>
    <AlertMessage
        v-if="isEmptyGrid"
        :title="formatMessage('msgNoCertificates')"
        :message="formatMessage('msgNoCertificatesDesc')" />
    <DxDataGrid
        :data-source="gridDataSource"
        :hover-state-enabled="true"
        :show-column-lines="false"
        :column-auto-width="true"
        :remote-operations="true"
        :editing="{
            allowAdding: false,
            allowUpdating: allowEdit,
            allowDeleting: allowEdit
        }">
        <DxFilterRow :visible="true" />
        <DxToolbar>
            <DxItem location="after">
                <DxButton
                    icon="add"
                    type="default"
                    @click="handleAddClick"
                    :disabled="!allowEdit"
                    :visible="allowEdit" />
            </DxItem>
        </DxToolbar>

        <DxColumn data-field="certificateName" :caption="formatMessage('Name')" data-type="string" />

        <!-- Dates are formatted according to spec mockup -->
        <DxColumn
            data-field="validFrom"
            data-type="datetime"
            :editor-options="dateEditorOptions"
            :caption="formatMessage('ValidFrom')"
            :calculate-filter-expression="calculateValidFromFilterExpression" />
        <DxColumn
            data-field="validUntil"
            data-type="datetime"
            :editor-options="dateEditorOptions"
            :caption="formatMessage('ValidUntil')"
            :calculate-filter-expression="calculateValidUntilFilterExpression" />

        <DxColumn
            data-field="certificateType"
            data-type="string"
            :caption="formatMessage('CertificateType')">
            <DxLookup
                :data-source=certificateTypeLookup
                value-expr="value"
                display-expr="text" />
        </DxColumn>

        <DxColumn
            data-field="status"
            data-type="string"
            cell-template="statusTemplate"
            :caption="formatMessage('CertificateStatus')">
            <DxLookup
                :data-source=certificateStatusLookup
                value-expr="value"
                display-expr="text" />
        </DxColumn>

        <DxColumn
            data-field="default"
            data-type="boolean"
            cell-template="isDefaultIcon"
            :caption="formatMessage('IsDefault')" />

        <DxColumn type="buttons" alignment="right" width="auto">
            <DxGridButton name="edit" :hint="formatMessage('Edit')" :visible="allowEdit" @click="handleEditClick" />
        </DxColumn>

        <template #statusTemplate="{ data }">
            <span :class="getStatusBadgeClass(data)">
                {{ data.text }}
            </span>
        </template>

        <template #isDefaultIcon="{ data }">
            <span v-if="data.value" class="dx-icon-check" />
        </template>
    </DxDataGrid>

    <Offcanvas
        ref="offcanvasRef"
        @closed="certificateFormData = null"
        :title="isNew ? formatMessage('UploadCertificate') : formatMessage('EditCertificate')">
        <template #body>
            <Suspense>
                <CertificateManagementForm
                    :offcanvas-data="certificateFormData"
                    :v-if="certificateFormData"
                    ref="certificateFormRef"
                    @updated="detailsSaved"
                    :company-id="companyId"
                    :allow-edit="allowEdit"
                    :api-url="apiUrl"
                    :is-new="isNew" />
            </Suspense>
        </template>
    </Offcanvas>
</template>

<script setup lang="ts">
    import {
        DxItem,
        DxColumn,
        DxLookup,
        DxToolbar,
        DxDataGrid,
        DxFilterRow,
        DxButton as DxGridButton
    } from "devextreme-vue/data-grid";
    import { nextTick, ref } from "vue";
    import { useLocalization } from "@/localization";
    import { DxButton } from "devextreme-vue/button";
    import Offcanvas from "@components/ui/offcanvas.vue";
    import DataSource from "devextreme/data/data_source";
    import { IntegrationHttpClient } from "@/http-client";
    import { formatMessage } from "devextreme/localization";
    import AlertMessage from "@components/alert-message.vue";
    import { integrationFactory } from "@/integration-extensions";
    import type { CellClickEvent } from "devextreme/ui/data_grid";
    import CertificateManagementForm from "./certificate-management-form.vue";
    import { calculateDateTimeFilterExpression } from "@/helpers/date-filter-helper";
    import {
        rosCertificateEndpoints,
        certificateStatusLookup,
        certificateTypeLookup,
        CertificateStatus,
        CertificateType
    } from "./certificate-management.types";

    const props = defineProps({
        apiUrl: { type: String, required: true },
        companyId: { type: Number, required: true },
        allowEdit: { type: Boolean, required: true }
    });

    const integrationClient = new IntegrationHttpClient();
    await useLocalization("Company.CertificateManagement", "System.Notification", "General");

    const offcanvasRef = ref();
    const isNew = ref(false);
    const certificateFormData = ref();
    const certificateFormRef = ref();

    // Reactive flag to track if the grid is empty for alert messages
    const isEmptyGrid = ref(true);

    const gridDataSource = new DataSource({
        store: integrationFactory.createStore({
            key: "id",
            loadUrl: rosCertificateEndpoints(props.companyId).getAllCertificates,
            onLoaded: result => {
                if (result.length) isEmptyGrid.value = false;
            }
        })
    });

    const dateEditorOptions = {
        showClearButton: true,
        openOnFieldClick: true
    };

    // --- Event Handlers ---
    async function handleAddClick(e: CellClickEvent) {
        window.ShowLoadPanel();
        isNew.value = true;

        // Initialize form data for a new certificate
        certificateFormData.value = {
            certificateType: CertificateType.Employer,
            status: CertificateStatus.Pending,
            agentTain: null,
            password: null,
            default: false,
            certificate: [] // init file property as an empty array
        };

        window.HideLoadPanel();
        await openOffcanvas();
    }

    async function handleEditClick(e: CellClickEvent) {
        window.ShowLoadPanel();
        isNew.value = false;

        // Fetch details for the certificate being updated
        const certificateId = e.row.data.id;
        if (certificateId) {
            const getCertificateEndpoint = rosCertificateEndpoints(props.companyId, certificateId).getCertificateById;
            certificateFormData.value = await integrationClient.get(getCertificateEndpoint!);
        }

        window.HideLoadPanel();
        await openOffcanvas();
    }

    function detailsSaved() {
        offcanvasRef.value.close();
        gridDataSource.reload();
    }

    async function openOffcanvas() {
        await nextTick();
        if (offcanvasRef.value) {
            offcanvasRef.value.open();
        } else {
            console.error("Offcanvas ref is not available.");
        }
    }

    // --- Custom Methods ---
    const calculateValidFromFilterExpression = (filterValue: any, _: string) => {
        // For validFrom: show certificates that start on or after the filter date
        return calculateDateTimeFilterExpression(filterValue, "validFrom", ">=");
    };

    const calculateValidUntilFilterExpression = (filterValue: any, _: string) => {
        // For validUntil: show certificates that expire on or before the filter date
        return calculateDateTimeFilterExpression(filterValue, "validUntil", "<=");
    };

    // Certificate Status badge styling
    const getStatusBadgeClass = (item: any): string => {
        const baseClass = "fs-6 badge bg-light border";
        const statusValue: CertificateStatus = item.value;
        switch (statusValue) {
            case CertificateStatus.Pending:
                return `${baseClass} text-warning-emphasis`;
            case CertificateStatus.Active:
                return `${baseClass} text-primary`;
            case CertificateStatus.Expired:
                return `${baseClass} text-danger`;
            default:
                return `${baseClass} text-primary`;
        }
    };
</script>