<template>
    <AlertMessage
        v-if="isEmptyGrid"
        :title="formatMessage('msgNoCertificates')"
        :message="formatMessage('msgNoCertificatesDesc')" />
    <DxDataGrid
        :data-source="gridDataSource"
        :hover-state-enabled="true"
        :show-column-lines="false"
        :column-auto-width="true"
        :remote-operations="true"
        :editing="{
            allowAdding: false,
            allowUpdating: allowEdit,
            allowDeleting: allowEdit
        }">
        <DxFilterRow :visible="true" />
        <DxToolbar>
            <DxItem location="after">
                <DxButton
                    icon="add"
                    type="default"
                    @click="handleAddClick"
                    :disabled="!allowEdit"
                    :visible="allowEdit" />
            </DxItem>
        </DxToolbar>

        <DxColumn data-field="certificateName" :caption="formatMessage('Name')" data-type="string" />

        <!-- Dates are formatted according to spec mockup -->
        <DxColumn
            data-field="validFrom"
            data-type="date"
            :customize-text="formatDateTime"
            :calculate-filter-expression="calculateValidFromFilterExpression"
            :caption="formatMessage('ValidFrom')"
            :editor-options="{
                showClearButton: true,
                openOnFieldClick: true,
                dateSerializationFormat: 'yyyy-MM-ddTHH:mm:ssZ'
            }" />
        <DxColumn
            data-field="validUntil"
            data-type="date"
            :customize-text="formatDateTime"
            :calculate-filter-expression="calculateValidUntilFilterExpression"
            :caption="formatMessage('ValidUntil')"
            :editor-options="{
                showClearButton: true,
                openOnFieldClick: true,
                dateSerializationFormat: 'yyyy-MM-ddTHH:mm:ssZ'
            }" />

        <DxColumn
            data-field="certificateType"
            data-type="string"
            :caption="formatMessage('CertificateType')">
            <DxLookup
                :data-source=certificateTypeLookup
                value-expr="value"
                display-expr="text" />
        </DxColumn>

        <DxColumn
            data-field="status"
            data-type="string"
            cell-template="statusTemplate"
            :caption="formatMessage('CertificateStatus')">
            <DxLookup
                :data-source=certificateStatusLookup
                value-expr="value"
                display-expr="text" />
        </DxColumn>

        <DxColumn
            data-field="default"
            data-type="boolean"
            cell-template="isDefaultIcon"
            :caption="formatMessage('IsDefault')" />

        <DxColumn type="buttons" alignment="right" width="auto">
            <DxGridButton name="edit" :hint="formatMessage('Edit')" :visible="allowEdit" @click="handleEditClick" />
        </DxColumn>

        <template #statusTemplate="{ data }">
            <span :class="getStatusBadgeClass(data)">
                {{ data.text }}
            </span>
        </template>

        <template #isDefaultIcon="{ data }">
            <span v-if="data.value" class="dx-icon-check" />
        </template>
    </DxDataGrid>

    <Offcanvas
        ref="offcanvasRef"
        @closed="certificateFormData = null"
        :title="isNew ? formatMessage('UploadCertificate') : formatMessage('EditCertificate')">
        <template #body>
            <Suspense>
                <CertificateManagementForm
                    :offcanvas-data="certificateFormData"
                    :v-if="certificateFormData"
                    ref="certificateFormRef"
                    @updated="detailsSaved"
                    :company-id="companyId"
                    :allow-edit="allowEdit"
                    :api-url="apiUrl"
                    :is-new="isNew" />
            </Suspense>
        </template>
    </Offcanvas>
</template>

<script setup lang="ts">
    import {
        DxItem,
        DxColumn,
        DxLookup,
        DxToolbar,
        DxDataGrid,
        DxFilterRow,
        DxButton as DxGridButton
    } from "devextreme-vue/data-grid";
    import { nextTick, ref } from "vue";
    import { useLocalization } from "@/localization";
    import { DxButton } from "devextreme-vue/button";
    import Offcanvas from "@components/ui/offcanvas.vue";
    import DataSource from "devextreme/data/data_source";
    import { IntegrationHttpClient } from "@/http-client";
    import AlertMessage from "@components/alert-message.vue";
    import { integrationFactory } from "@/integration-extensions";
    import type { CellClickEvent } from "devextreme/ui/data_grid";
    import { formatMessage, locale } from "devextreme/localization";
    import CertificateManagementForm from "./certificate-management-form.vue";
    import {
        rosCertificateEndpoints,
        certificateStatusLookup,
        certificateTypeLookup,
        CertificateStatus,
        CertificateType
    } from "./certificate-management.types";

    const props = defineProps({
        apiUrl: { type: String, required: true },
        companyId: { type: Number, required: true },
        allowEdit: { type: Boolean, required: true }
    });

    const integrationClient = new IntegrationHttpClient();
    await useLocalization("Company.CertificateManagement", "System.Notification", "General");

    const offcanvasRef = ref();
    const isNew = ref(false);
    const certificateFormData = ref();
    const certificateFormRef = ref();

    // Reactive flag to track if the grid is empty for alert messages
    const isEmptyGrid = ref(true);

    const gridDataSource = new DataSource({
        store: integrationFactory.createStore({
            key: "id",
            loadUrl: rosCertificateEndpoints(props.companyId).getAllCertificates,
            onLoaded: result => {
                if (result.length) isEmptyGrid.value = false;
            }
        })
    });

    const dateTimeFormatOptions: Intl.DateTimeFormatOptions = {
        day: "2-digit",
        month: "long", // full month-name
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false // 24-hour format
    };

    // --- Event Handlers ---
    async function handleAddClick(e: CellClickEvent) {
        window.ShowLoadPanel();
        isNew.value = true;

        // Initialize form data for a new certificate
        certificateFormData.value = {
            certificateType: CertificateType.Employer,
            status: CertificateStatus.Pending,
            agentTain: null,
            password: null,
            default: false,
            certificate: null // init file property
        };

        window.HideLoadPanel();
        await openOffcanvas();
    }

    async function handleEditClick(e: CellClickEvent) {
        window.ShowLoadPanel();
        isNew.value = false;

        // Fetch details for the certificate being updated
        const certificateId = e.row.data.id;
        if (certificateId) {
            const getCertificateEndpoint = rosCertificateEndpoints(props.companyId, certificateId).getCertificateById;
            certificateFormData.value = await integrationClient.get(getCertificateEndpoint!);
            console.log(certificateFormData.value);
        }

        window.HideLoadPanel();
        await openOffcanvas();
    }

    function detailsSaved() {
        offcanvasRef.value.close();
        gridDataSource.reload();
    }

    async function openOffcanvas() {
        await nextTick();
        if (offcanvasRef.value) {
            offcanvasRef.value.open();
        } else {
            console.error("Offcanvas ref is not available.");
        }
    }

    // --- Custom Methods ---
    const formatDateTime = (e: { value: Date | string | null; valueText: string }) => {
        const dateValue = e.value;
        if (!dateValue) {
            return "";
        }

        const date = new Date(dateValue);

        // If date is not valid after conversion, return original value
        if (isNaN(date.getTime())) {
            return String(dateValue);
        }

        return new Intl.DateTimeFormat(locale(), dateTimeFormatOptions).format(date);
    };

    // Date filter expressions - handle date-only comparisons for datetime fields
    const calculateValidFromFilterExpression = (filterValue: any, selectedFilterOperation: string) => {
        if (selectedFilterOperation === "between" && Array.isArray(filterValue)) {
            // For date range, use start of first day to end of last day
            const startDate = new Date(filterValue[0]);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(filterValue[1]);
            endDate.setHours(23, 59, 59, 999);
            return [["validFrom", ">=", startDate.toISOString()], "and", ["validFrom", "<=", endDate.toISOString()]];
        } else if (selectedFilterOperation === "=") {
            // For equals, check if the date falls within the same day
            const inputDate = new Date(filterValue);
            const startOfDay = new Date(inputDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(inputDate);
            endOfDay.setHours(23, 59, 59, 999);
            return [["validFrom", ">=", startOfDay.toISOString()], "and", ["validFrom", "<=", endOfDay.toISOString()]];
        } else {
            // For other operations (>, <, >=, <=), use the date as-is
            const date = new Date(filterValue).toISOString();
            return [["validFrom", selectedFilterOperation, date]];
        }
    };

    const calculateValidUntilFilterExpression = (filterValue: any, selectedFilterOperation: string) => {
        if (selectedFilterOperation === "between" && Array.isArray(filterValue)) {
            // For date range, use start of first day to end of last day
            const startDate = new Date(filterValue[0]);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(filterValue[1]);
            endDate.setHours(23, 59, 59, 999);
            return [["validUntil", ">=", startDate.toISOString()], "and", ["validUntil", "<=", endDate.toISOString()]];
        } else if (selectedFilterOperation === "=") {
            // For equals, check if the date falls within the same day
            const inputDate = new Date(filterValue);
            const startOfDay = new Date(inputDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(inputDate);
            endOfDay.setHours(23, 59, 59, 999);
            return [["validUntil", ">=", startOfDay.toISOString()], "and", ["validUntil", "<=", endOfDay.toISOString()]];
        } else {
            // For other operations (>, <, >=, <=), use the date as-is
            const date = new Date(filterValue).toISOString();
            return [["validUntil", selectedFilterOperation, date]];
        }
    };

    // Certificate Status badge styling
    const getStatusBadgeClass = (item: any): string => {
        const baseClass = "fs-6 badge bg-light border";
        const statusValue: CertificateStatus = item.value;
        switch (statusValue) {
            case CertificateStatus.Pending:
                return `${baseClass} text-warning-emphasis`;
            case CertificateStatus.Active:
                return `${baseClass} text-primary`;
            case CertificateStatus.Expired:
                return `${baseClass} text-danger`;
            default:
                return `${baseClass} text-primary`;
        }
    };
</script>