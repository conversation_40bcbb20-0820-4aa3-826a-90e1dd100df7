<template>
    <Card>
        <form
            :action="uploadUrl"
            method="POST"
            data-ajax="true"
            data-ajax-failure="OnFailure"
            data-ajax-success="OnSuccess"
            enctype="multipart/form-data">
            <!-- Keep the uploader OUTSIDE of DxForm to prevent re-creation -->
            <div class="mb-3" v-if="isNew">
                <label class="form-label">{{ formatMessage('SelectFile') }}</label>
                <DxFileUploader
                    name="certificate"
                    accept=".p12"
                    upload-mode="useForm"
                    :allowed-file-extensions="['.p12']"
                    :show-file-list="true"
                    :on-value-changed="onFileChanged" />
            </div>

            <DxForm :form-data="formData" :col-count="1" v-if="isNew">

                <DxItem data-field="certificateName" editor-type="dxTextBox">
                    <DxLabel :text="formatMessage('CertificateName')" />
                    <DxRequiredRule :message="formatMessage('errCertificateNameRequired')" />
                    <DxStringLengthRule :max="150" :message="formatMessage('errCertificateNameLength')" />
                </DxItem>

                <DxItem
                    data-field="certificateType"
                    editor-type="dxSelectBox"
                    :is-required="isNew"
                    :editor-options="{
                        dataSource: certificateTypeLookup,
                        displayExpr: 'text',
                        valueExpr: 'value'
                    }">
                    <DxLabel :text="formatMessage('CertificateType')" />
                    <DxRequiredRule :message="formatMessage('errCertificateTypeRequired')" />
                </DxItem>

                <!-- Only visible & required for Agents -->
                <DxItem
                    data-field="agentTain"
                    editor-type="dxTextBox"
                    :is-required="formData?.certificateType == CertificateType.Agent"
                    :visible="isNew && formData?.certificateType == CertificateType.Agent"
                    :disabled="!isNew && formData?.certificateType == CertificateType.Agent">
                    <DxLabel :text="formatMessage('TAIN')" />
                    <DxRequiredRule :message="formatMessage('errTAINRequired')" />
                </DxItem>

                <DxItem
                    data-field="password"
                    editor-type="dxTextBox"
                    :editor-options="passwordEditorOptions"
                    :is-required="isNew">
                    <DxLabel :text="formatMessage('CertificatePassword')" />
                    <DxRequiredRule :message="formatMessage('errCertificatePasswordRequired')" />
                </DxItem>

                <DxItem
                    data-field="default"
                    editor-type="dxCheckBox"
                    :label="{ visible: false }"
                    :editor-options="{ text: formatMessage('IsDefaultCertificate') }" />

            </DxForm>
            <div class="d-flex justify-content-end gap-2 pt-3">
                <DxButton
                    :text="formatMessage('ValidateFile')"
                    type="default"
                    styling-mode="contained"
                    :use-submit-behavior="true"
                    :disabled="!isFileSelected" />
            </div>
        </form>

        <DxForm :form-data="formData" :col-count="1" v-if="!isNew">
            <DxItem data-field="certificateName" editor-type="dxTextBox">
                <DxLabel :text="formatMessage('CertificateName')" />
                <DxStringLengthRule :max="150" :message="formatMessage('errCertificateNameLength')" />
            </DxItem>

            <DxItem
                data-field="certificateType"
                editor-type="dxSelectBox"
                :is-required="isNew"
                :disabled="!isNew"
                :editor-options="{
                    dataSource: certificateTypeLookup,
                    displayExpr: 'text',
                    valueExpr: 'value'
                }">
                <DxLabel :text="formatMessage('CertificateType')" />
            </DxItem>

            <!-- Only visible & required for Agents -->
            <DxItem
                data-field="agentTain"
                :visible="formData?.certificateType == CertificateType.Agent"
                :is-required="formData?.certificateType == CertificateType.Agent"
                :disabled="!isNew && formData?.certificateType == CertificateType.Agent">
                <DxLabel :text="formatMessage('TAIN')" />
            </DxItem>

            <DxItem
                data-field="default"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('IsDefaultCertificate') }" />

            <DxItem
                data-field="issuedBy"
                :label="{ text: formatMessage('IssuedBy') }"
                :disabled="true" />

            <DxItem
                data-field="issuedTo"
                :label="{ text: formatMessage('IssuedTo') }"
                :disabled="true" />

            <DxItem
                data-field="validFrom"
                :label="{ text: formatMessage('ValidFrom') }"
                :disabled="true" />

            <DxItem
                data-field="validUntil"
                :label="{ text: formatMessage('ValidUntil') }"
                :disabled="true" />

            <DxItem
                data-field="serialNumber"
                :label="{ text: formatMessage('SerialNumber') }"
                :disabled="true" />

            <DxItem
                data-field="thumbPrint"
                :label="{ text: formatMessage('Thumbprint') }"
                :disabled="true" />

            <DxItem
                data-field="status"
                :label="{ text: formatMessage('CertificateStatus') }"
                :disabled="true" />

            <DxButtonItem
                :button-options="{
                    text: formatMessage('lblSubmit'),
                    type: 'default',
                    onClick: (e: DxButtonTypes.ClickEvent) => update(e)
                }"/>
        </DxForm>
    </Card>
</template>

<script setup lang="ts">
    import Card from "@components/ui/card.vue";
    import { ref, watch, computed } from "vue";
    import { DxButton, DxButtonTypes } from "devextreme-vue/button";
    import { formatMessage } from "devextreme/localization";
    import { IntegrationHttpClient, integrationFunctionsKey } from "@/http-client";
    import { DxStringLengthRule, DxRequiredRule, DxLabel, DxForm, DxItem, DxButtonItem } from "devextreme-vue/form";
    import {
        rosCertificateEndpoints,
        certificateTypeLookup,
        CertificateType
    } from "./certificate-management.types";

    const props = defineProps({
        isNew: { type: Boolean, required: true },
        apiUrl: { type: String, required: true },
        companyId: { type: Number, required: true },
        allowEdit: { type: Boolean, required: true },
        offcanvasData: { type: Object, required: true }
    });

    defineExpose({ update });

    const integrationClient = new IntegrationHttpClient();
    const emit = defineEmits<{ (e: "updated"): void }>();

    const uploadUrl =
        "https://func-uat-in1-ie-navigator-san.azurewebsites.net" +
        rosCertificateEndpoints(props.companyId).uploadCertificate +
        "?code=" +
        integrationFunctionsKey;

    // --- Refs ---
    const formData = ref();
    const isFileSelected = ref(false);
    const passwordMode = ref("password");

    watch(
        () => props.offcanvasData,
        newValue => {
            if (newValue) {
                formData.value = { ...newValue };
            }
        },
        { immediate: true, deep: true }
    );

    // Allows user to toggle password hide/text
    const passwordEditorOptions = computed(() => ({
        mode: passwordMode.value,
        buttons: [
            {
                name: "passwordToggle",
                location: "after",
                options: {
                    icon: passwordMode.value === "password" ? "eyeopen" : "eyeclose",
                    type: "default",
                    stylingMode: "text",
                    onClick: () => {
                        passwordMode.value = passwordMode.value === "password" ? "text" : "password";
                    }
                }
            }
        ]
    }));

    (window as any).OnFailure = OnFailure;
    function OnFailure(xhr: any) {
        Notifications.showErrors([xhr.responseJSON.error], window.Route.NotificationId);
    }

    (window as any).OnSuccess = OnSuccess;
    function OnSuccess() {
        Notifications.showSuccess(formatMessage("Create.Success"), window.Route.NotificationId);
        emit("updated");
    }

    // --- Event Handlers ---
    async function update(e: DxButtonTypes.ClickEvent) {
        e.component.option("disabled", true);

        const patchData = {
            certificateName: formData.value.certificateName,
            default: formData.value.default,
            agentTain: formData.value.certificateType == CertificateType.Agent ? formData.value.agentTain : null
        };

        const response = await integrationClient.patch(
            rosCertificateEndpoints(props.companyId, formData.value.id).patchCertificate,
            patchData
        );

        // On success, the response is null
        if (response && response.status !== 200) {
            const errorMessage = response.error || response.detail;
            Notifications.showErrors([errorMessage], window.Route.NotificationId);
            return;
        } else {
            Notifications.showSuccess(formatMessage("Update.Success"), window.Route.NotificationId);
        }

        emit("updated");
        e.component.option("disabled", false);
    }
</script>