<template>
    <Card>
        <form
            :action="uploadUrl"
            method="POST"
            data-ajax="true"
            data-ajax-failure="OnFailure"
            data-ajax-success="OnSuccess"
            enctype="multipart/form-data">
            <DxForm :form-data="formData" :col-count="1" v-if="isNew">
                <DxItem template="fileUploaderTemplate">
                    <DxLabel :text="formatMessage('SelectFile')" />
                </DxItem>

                <template #fileUploaderTemplate>
                    <input
                        type="file"
                        name="certificate"
                        accept=".p12"
                        ref="fileInputRef"
                        @change="onFileSelected"
                        style="display: none;" />
                    <div class="dx-fileuploader dx-fileuploader-empty">
                        <div class="dx-fileuploader-content">
                            <div class="dx-fileuploader-input-wrapper">
                                <div class="dx-fileuploader-input-container">
                                    <div class="dx-fileuploader-input-label" @click="triggerFileSelect">
                                        <span v-if="!selectedFileName">{{ formatMessage('SelectFile') }} or Drop a file here</span>
                                        <span v-else>{{ selectedFileName }} <small class="text-muted">({{ formatFileSize(selectedFileSize) }})</small></span>
                                    </div>
                                    <div class="dx-fileuploader-input-label" v-if="!selectedFileName">
                                        <span class="dx-fileuploader-button dx-button dx-button-normal dx-button-mode-contained dx-widget" @click="triggerFileSelect">
                                            <div class="dx-button-content">
                                                <span class="dx-button-text">{{ formatMessage('SelectFile') }}</span>
                                            </div>
                                        </span>
                                    </div>
                                    <div v-if="selectedFileName" class="mt-2">
                                        <span class="badge bg-success">Ready to upload</span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" @click="clearFile">
                                            <i class="dx-icon dx-icon-close"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <DxItem data-field="certificateName" editor-type="dxTextBox">
                    <DxLabel :text="formatMessage('CertificateName')" />
                    <DxRequiredRule :message="formatMessage('errCertificateNameRequired')" />
                    <DxStringLengthRule :max="150" :message="formatMessage('errCertificateNameLength')" />
                </DxItem>

                <DxItem
                    data-field="certificateType"
                    editor-type="dxSelectBox"
                    :is-required="isNew"
                    :editor-options="{
                        dataSource: certificateTypeLookup,
                        displayExpr: 'text',
                        valueExpr: 'value'
                    }">
                    <DxLabel :text="formatMessage('CertificateType')" />
                    <DxRequiredRule :message="formatMessage('errCertificateTypeRequired')" />
                </DxItem>

                <!-- Only visible & required for Agents -->
                <DxItem
                    data-field="agentTain"
                    editor-type="dxTextBox"
                    :is-required="formData?.certificateType == CertificateType.Agent"
                    :visible="isNew && formData?.certificateType == CertificateType.Agent"
                    :disabled="!isNew && formData?.certificateType == CertificateType.Agent">
                    <DxLabel :text="formatMessage('TAIN')" />
                    <DxRequiredRule :message="formatMessage('errTAINRequired')" />
                </DxItem>

                <DxItem
                    data-field="password"
                    editor-type="dxTextBox"
                    :editor-options="passwordEditorOptions"
                    :is-required="isNew">
                    <DxLabel :text="formatMessage('CertificatePassword')" />
                    <DxRequiredRule :message="formatMessage('errCertificatePasswordRequired')" />
                </DxItem>

                <DxItem
                    data-field="default"
                    editor-type="dxCheckBox"
                    :label="{ visible: false }"
                    :editor-options="{ text: formatMessage('IsDefaultCertificate') }" />

                <DxButtonItem
                    :button-options="{
                        text: formatMessage('ValidateFile'),
                        type: 'default',
                        useSubmitBehavior: true,
                        disabled: !isFileSelected
                    }" />
            </DxForm>
        </form>

        <DxForm :form-data="formData" :col-count="1" v-if="!isNew">
            <DxItem data-field="certificateName" editor-type="dxTextBox">
                <DxLabel :text="formatMessage('CertificateName')" />
                <DxStringLengthRule :max="150" :message="formatMessage('errCertificateNameLength')" />
            </DxItem>

            <DxItem
                data-field="certificateType"
                editor-type="dxSelectBox"
                :is-required="isNew"
                :disabled="!isNew"
                :editor-options="{
                    dataSource: certificateTypeLookup,
                    displayExpr: 'text',
                    valueExpr: 'value'
                }">
                <DxLabel :text="formatMessage('CertificateType')" />
            </DxItem>

            <!-- Only visible & required for Agents -->
            <DxItem
                data-field="agentTain"
                :visible="formData?.certificateType == CertificateType.Agent"
                :is-required="formData?.certificateType == CertificateType.Agent"
                :disabled="!isNew && formData?.certificateType == CertificateType.Agent">
                <DxLabel :text="formatMessage('TAIN')" />
            </DxItem>

            <DxItem
                data-field="default"
                editor-type="dxCheckBox"
                :label="{ visible: false }"
                :editor-options="{ text: formatMessage('IsDefaultCertificate') }" />

            <DxItem
                data-field="issuedBy"
                :label="{ text: formatMessage('IssuedBy') }"
                :disabled="true" />

            <DxItem
                data-field="issuedTo"
                :label="{ text: formatMessage('IssuedTo') }"
                :disabled="true" />

            <DxItem
                data-field="validFrom"
                :label="{ text: formatMessage('ValidFrom') }"
                :disabled="true" />

            <DxItem
                data-field="validUntil"
                :label="{ text: formatMessage('ValidUntil') }"
                :disabled="true" />

            <DxItem
                data-field="serialNumber"
                :label="{ text: formatMessage('SerialNumber') }"
                :disabled="true" />

            <DxItem
                data-field="thumbPrint"
                :label="{ text: formatMessage('Thumbprint') }"
                :disabled="true" />

            <DxItem
                data-field="status"
                :label="{ text: formatMessage('CertificateStatus') }"
                :disabled="true" />

            <DxButtonItem
                :button-options="{
                    text: formatMessage('lblSubmit'),
                    type: 'default',
                    onClick: (e: DxButtonTypes.ClickEvent) => update(e)
                }"/>
        </DxForm>
    </Card>
</template>

<script setup lang="ts">
    import Card from "@components/ui/card.vue";
    import { ref, watch, computed } from "vue";
    import { DxButtonTypes } from "devextreme-vue/button";
    import { formatMessage } from "devextreme/localization";
    import { IntegrationHttpClient, integrationFunctionsKey } from "@/http-client";
    import { DxStringLengthRule, DxRequiredRule, DxLabel, DxForm, DxItem, DxButtonItem } from "devextreme-vue/form";
    import {
        rosCertificateEndpoints,
        certificateTypeLookup,
        CertificateType
    } from "./certificate-management.types";

    const props = defineProps({
        isNew: { type: Boolean, required: true },
        apiUrl: { type: String, required: true },
        companyId: { type: Number, required: true },
        allowEdit: { type: Boolean, required: true },
        offcanvasData: { type: Object, required: true }
    });

    defineExpose({ update });

    const integrationClient = new IntegrationHttpClient();
    const emit = defineEmits<{ (e: "updated"): void }>();

    const uploadUrl =
        "https://func-uat-in1-ie-navigator-san.azurewebsites.net" +
        rosCertificateEndpoints(props.companyId).uploadCertificate +
        "?code=" +
        integrationFunctionsKey;

    // --- Refs ---
    const formData = ref();
    const isFileSelected = ref(false);
    const passwordMode = ref("password");
    const fileInputRef = ref();
    const selectedFileName = ref("");
    const selectedFileSize = ref(0);

    watch(
        () => props.offcanvasData,
        newValue => {
            if (newValue) {
                formData.value = { ...newValue };
            }
        },
        { immediate: true, deep: true }
    );

    // Allows user to toggle password hide/text
    const passwordEditorOptions = computed(() => ({
        mode: passwordMode.value,
        buttons: [
            {
                name: "passwordToggle",
                location: "after",
                options: {
                    icon: passwordMode.value === "password" ? "eyeopen" : "eyeclose",
                    type: "default",
                    stylingMode: "text",
                    onClick: () => {
                        passwordMode.value = passwordMode.value === "password" ? "text" : "password";
                    }
                }
            }
        ]
    }));

    (window as any).OnFailure = OnFailure;
    function OnFailure(xhr: any) {
        Notifications.showErrors([xhr.responseJSON.error], window.Route.NotificationId);
    }

    (window as any).OnSuccess = OnSuccess;
    function OnSuccess() {
        Notifications.showSuccess(formatMessage("Create.Success"), window.Route.NotificationId);
        emit("updated");
    }

    // --- File handling methods ---
    function triggerFileSelect() {
        fileInputRef.value?.click();
    }

    function onFileSelected(event: Event) {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (file) {
            // Validate file extension
            if (!file.name.toLowerCase().endsWith('.p12')) {
                Notifications.showErrors(['Only .p12 files are allowed'], window.Route.NotificationId);
                clearFile();
                return;
            }

            selectedFileName.value = file.name;
            selectedFileSize.value = file.size;
            isFileSelected.value = true;
        } else {
            clearFile();
        }
    }

    function clearFile() {
        if (fileInputRef.value) {
            fileInputRef.value.value = '';
        }
        selectedFileName.value = '';
        selectedFileSize.value = 0;
        isFileSelected.value = false;
    }

    function formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // --- Event Handlers ---
    async function update(e: DxButtonTypes.ClickEvent) {
        e.component.option("disabled", true);

        const patchData = {
            certificateName: formData.value.certificateName,
            default: formData.value.default,
            agentTain: formData.value.certificateType == CertificateType.Agent ? formData.value.agentTain : null
        };

        const response = await integrationClient.patch(
            rosCertificateEndpoints(props.companyId, formData.value.id).patchCertificate,
            patchData
        );

        // On success, the response is null
        if (response && response.status !== 200) {
            const errorMessage = response.error || response.detail;
            Notifications.showErrors([errorMessage], window.Route.NotificationId);
            return;
        } else {
            Notifications.showSuccess(formatMessage("Update.Success"), window.Route.NotificationId);
        }

        emit("updated");
        e.component.option("disabled", false);
    }
</script>

<style scoped>
.dx-fileuploader {
    border: 1px dashed #d3d3d3;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    background-color: #fafafa;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.dx-fileuploader:hover {
    border-color: #007acc;
    background-color: #f0f8ff;
}

.dx-fileuploader-input-label {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.dx-fileuploader-button {
    margin-top: 10px;
}
</style>