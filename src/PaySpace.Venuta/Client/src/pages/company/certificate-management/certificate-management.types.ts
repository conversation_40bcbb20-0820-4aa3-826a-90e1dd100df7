export enum CertificateStatus {
    Active = 'Active',
    Pending = 'Pending',
    Expired = 'Expired'
}
export enum CertificateType {
    Employer = 'Employer',
    Agent = 'Agent'
}

export const certificateTypeLookup = [
    { text: CertificateType.Employer, value: CertificateType.Employer },
    { text: CertificateType.Agent,    value: CertificateType.Agent }
];

export const certificateStatusLookup = [
    { text: CertificateStatus.Active, value: CertificateStatus.Active },
    { text: CertificateStatus.Pending, value: CertificateStatus.Pending },
    { text: CertificateStatus.Expired, value: CertificateStatus.Expired }
]

export const rosCertificateEndpoints = (companyId: string | number, certificateId?: string) => {
    return {
        getCertificateById: certificateId ? `/company/${companyId}/ros/certificate/${certificateId}` : null,
        getAllCertificates: `/company/${companyId}/ros/certificate`,
        verifyCertificate: `/company/${companyId}/certificate/verify`,
        uploadCertificate: `/company/${companyId}/ros/certificate`,
        patchCertificate: `/company/${companyId}/ros/certificate/${certificateId}`
    };
};

export function calculateROSDateFilterExpression(filterValue: any, dataField: string) {
    // For date filtering, check if the datetime falls within the same day
    const inputDate = new Date(filterValue);

    const startOfDay = new Date(inputDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(inputDate);
    endOfDay.setHours(23, 59, 59, 999);

    return [
        [dataField, ">=", startOfDay.toISOString()],
        "and",
        [dataField, "<=", endOfDay.toISOString()]
    ];
}