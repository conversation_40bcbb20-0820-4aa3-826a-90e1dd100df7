<template>
    <DxDataGrid ref="dataGrid"
                :data-source="gridDataSource"
                :allow-column-reordering="true"
                :hover-state-enabled="true"
                :show-column-lines="false"
                :remote-operations="true"
                :column-auto-width="true"
                :column-chooser="{ enabled: true }"
                @cell-click="onCellClick"
                @editing-start="onEditingStart"
                @row-removed="onRowRemoved">
        <DxFilterRow :visible="true"></DxFilterRow>
        <DxEditing :allow-deleting="true"
                   :allow-adding="false"
                   :allow-updating="true"
                   :use-icons="true"
                   mode="row">
        </DxEditing>

        <DxColumn data-field="frequencyName"
                  :caption="formatMessage('FrequencyName')"
                  :sort-index="2"
                  :fixed="true"
                  sort-order="asc">
        </DxColumn>

        <DxColumn data-field="runFrequency"
                  :caption="formatMessage('RunFrequency')"
                  :fixed="true">
            <DxLookup :data-source="payslipFrequencyDataSource" value-expr="Value" display-expr="Text"/>
        </DxColumn>

        <DxColumn data-field="taxCertificatePrefix"
                  :caption="formatMessage('TaxCertificatePrefix')"
                  :visible="enableTaxPrefix"
                  :show-in-column-chooser="enableTaxPrefix"
                  alignment="right">
        </DxColumn>

        <DxColumn data-field="hoursPerDay"
                  :caption="formatMessage('HoursPerDay')"
                  :customize-text="formatNumber">
        </DxColumn>

        <DxColumn data-field="daysPerPeriod"
                  :caption="formatMessage('DaysPerPeriod')"
                  :customize-text="formatNumber">
        </DxColumn>

        <DxColumn data-field="payDay"
                  allow-filter="false"
                  :caption="formatMessage('PayDay')"
                  :calculate-cell-value="calculatePayDayText">
        </DxColumn>
        <DxColumn data-field="inactiveFromDate"
                  :caption="formatMessage('lblInactiveFromDate')"
                  sort-order="asc"
                  :sort-index="1">
        </DxColumn>
        <DxColumn type="buttons" :fixed="true" :show-in-column-chooser="false">
            <DxButton name="edit" />
            <DxButton name="delete" :visible="deleteEnabled"/>
        </DxColumn>

        <DxPaging :page-size="15">
        </DxPaging>
    </DxDataGrid>
</template>

<script lang="ts">
    import { defineComponent } from "vue";
    import { DxDataGrid, DxColumn, DxPaging, DxFilterRow, DxLookup, DxEditing, DxButton, DxDataGridTypes } from "devextreme-vue/data-grid";
    import { formatMessage } from "devextreme/localization";
    import { createStore } from "devextreme-aspnet-data-nojquery";

    import enumService from "@/services/enum-service";
    import { RunFrequency } from "@/enums";
    import dataGridFactory from '@/datagrid-extensions';
    import { useLocalization } from "@/localization";

    export default defineComponent({
        props: {
            apiEndpoint: String,
            apiDeleteEndpoint: String,
            baseUrl: String,
            selectedFrequencyId: {
                type: Number,
                required: true,
                default: 0
            },
            countryId: {
                type: Number,
                required: true,
                default: 0
            },
            canEdit: {
                type: Boolean,
                required: true,
                default: false
            }
        },
        async setup(props) {
            await useLocalization("Payroll.CompanyFrequencies");
            const payslipFrequencyDataSource = enumService.getPayslipFrequencies(props.countryId);
            const paydayDataSource = enumService.getPaydays(props.countryId);
            const enableTaxPrefix = enumService.isSouthAfricaOrNamibia(props.countryId)

            const gridDataSource = createStore({
                key: 'companyFrequencyId',
                loadUrl: props.apiEndpoint,
                deleteUrl: props.apiDeleteEndpoint
            });

            function formatNumber(data: any) {
                return new Intl.NumberFormat('en-US', {
                    minimumIntegerDigits: 1,
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 10
                }).format(data.value || 0);
            }

            function onEditingStart(e: DxDataGridTypes.EditingStartEvent) {
                e.cancel = true;
                window.location.href = `${ props.baseUrl }/edit/${ e.key }`;
            }

            function onCellClick(e: DxDataGridTypes.CellClickEvent) {
                if (e.rowType === 'data' && e.column.name !== 'buttons' && e.column.type !== 'adaptive') {
                    dataGridFactory.onRowClick(e, props.canEdit);
                }
            }

            function calculatePayDayText(data: any) {
                let payday = null;
                if (data.runFrequency === RunFrequency.Weekly) {
                    payday = enumService.getWeeklyPayDates(props.countryId).find(opt => {
                        return opt.Value === data.payDayId;
                    });
                }
                if (data.runFrequency === RunFrequency.Monthly) {
                    payday = enumService.getPaydays(props.countryId).find(opt => {
                        return opt.Value === data.payDayId;
                    });
                }
                if (data.runFrequency === RunFrequency.Fortnight) {
                    payday = enumService.getFortnightPayDates(props.countryId).find(opt => {
                        return opt.Value === data.payDayId;
                    });
                }
                if (data.runFrequency === RunFrequency.SemiMonthly) {
                    payday = enumService.getSemiMonthlySecondCyclePayDays(props.countryId).find(opt => opt.Value === data.payDayId);
                }
                return payday?.Text;
            }

            function deleteEnabled(e: { row: { data: { companyFrequencyId: number } } }) {
                return !(!props.canEdit || e.row.data.companyFrequencyId == props.selectedFrequencyId);
            }

            function onRowRemoved() {
                window.location.reload();
            }

            return {
                gridDataSource,
                payslipFrequencyDataSource,
                paydayDataSource,
                enableTaxPrefix,
                formatNumber,
                calculatePayDayText,
                onEditingStart,
                onCellClick,
                deleteEnabled,
                onRowRemoved
            };
        },
        methods: { formatMessage },
        components: { DxButton, DxEditing, DxLookup, DxDataGrid, DxColumn, DxPaging, DxFilterRow }
    });
</script>