<template>
    <DxTabPanel tabs-position="left" :items="OTHER_DROPDOWN_ENTITIES">
        <template #title="{ data }">
            <div class="dx-tab-text text-nowrap align-items-start">
                {{ formatMessage(data.entity) }}
            </div>
        </template>
        <template #item="{ data }">
            <OtherDropdownsGrid :entity="data.entity" :entity-key="data.entityKey" />
        </template>
    </DxTabPanel>
</template>
<script setup lang="ts">
    import { useLocalization } from "@/localization";
    import DxTabPanel from "devextreme-vue/tab-panel";
    import { formatMessage } from "devextreme/localization";
    import OtherDropdownsGrid from "./components/other-dropdowns-grid.vue";
    import { useEdmParser } from "./composables/edmParser";
    import { OTHER_DROPDOWN_ENTITIES } from "./constants";

    const { fetchAndParse } = useEdmParser();
    await Promise.all([useLocalization("OtherDropdowns", "General"), fetchAndParse()]);
</script>
