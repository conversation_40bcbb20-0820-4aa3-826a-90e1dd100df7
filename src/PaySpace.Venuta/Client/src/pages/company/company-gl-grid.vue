<template>
    <DxDataGrid :data-source="dataSource">
        <DxToolbar>
            <DxItem location="after">
                <DxButton icon="download"
                          @click="downloadAll"
                          stylingMode="contained"
                          type="default"
                          class="mr-4"/>

                <DxButton v-if="allowEdit"
                          icon="add"
                          @click="addNewEntry"
                          stylingMode="contained"
                          type="default" />
            </DxItem>
        </DxToolbar>
        <DxFilterRow :visible="true" />

        <DxEditing :allow-adding="allowEdit" :allow-updating="allowEdit" :allow-deleting="allowEdit" />

        <DxColumn v-bind="metadata.gl.attr('HeaderName')" data-type="string" />

        <DxColumn v-bind="metadata.gl.attr('EffectiveDate')" data-type="date" />

        <DxColumn type="buttons">
            <DxColumnButton v-if="allowEdit" name="copy" icon="copy" @click="onCopyClick"/>
            <DxColumnButton :visible="allowEdit" name="edit" @click="onEditClick" />
            <DxColumnButton :visible="allowEdit" name="delete" />
            <DxColumnButton icon="download" @click="onDownloadClick"/>
        </DxColumn>

    </DxDataGrid>
</template>

<script setup lang="ts">
    import { useLocalization } from "@/localization";
    import {
        DxDataGrid,
        DxColumn,
        DxEditing,
        DxButton as DxColumnButton,
        DxFilterRow,
        DxToolbar,
        DxItem,
        DxDataGridTypes
    } from "devextreme-vue/data-grid";
    import { DxButton } from "devextreme-vue/button";

    import odataFactory from "@/odata-extensions";
    import DataSource from "devextreme/data/data_source";
    import { useMetadata } from "@/metadata";

    await useLocalization("CompanyGLDetail");

    const { CompanyGLDto: gl } = await useMetadata("CompanyGLDto");

    const metadata = { gl };

    const props = defineProps<{
        allowEdit: {
            type: boolean,
            required: true
        },
        companyId: {
            type: number;
            required: true;
        };
        frequencyId: {
            type: number;
            required: true;
        };
        editUrlBase: {
            type: string;
            required: true;
        };
        reportUrl: {
            type: string;
            required: true;
        };
    }>();

    const dataSource = new DataSource({
        store: odataFactory.createStore("CompanyGL", "CompanyGlId", undefined, request => {
            request.params = request.params || {};
            request.params["frequency"] = props.frequencyId;
        })
    });

    function onEditClick(e: DxDataGridTypes.ColumnButtonClickEvent) {
        e.event?.preventDefault();

        const row = e.row?.data;

        if (row?.CompanyGlId !== null) {
            window.location.href = `${props.editUrlBase}/${row.CompanyGlId}`;
        }
    }

    function onCopyClick(e: DxDataGridTypes.ColumnButtonClickEvent) {
        e.event?.preventDefault();

        const row = e.row?.data;

        if (row?.CompanyGlId !== null) {
            window.location.href = `${props.editUrlBase}?cloneGLId=${row.CompanyGlId}`;
        }
    }

    function onDownloadClick(e: DxDataGridTypes.ColumnButtonClickEvent) {
        e.event?.preventDefault();

        const row = e.row?.data;

        if (row?.CompanyGlId !== null) {
            window.location.href = `${props.reportUrl}?generalLedgerId=${row.CompanyGlId}`;
        }
    }

    function addNewEntry(e: DxDataGridTypes.RowInsertingEvent) {
        e.cancel = true;
        window.location.href = `${props.editUrlBase}`;
    }

    async function downloadAll() {
        if (props.reportUrl !== null) {
            window.open(props.reportUrl, '_blank', 'noopener,noreferrer');
        }
    }
</script>
