<template>
    <DxForm id="companyGLForm" :form-data="formData" label-location="top">
        <DxGroupItem :caption="formatMessage('lblDetails')" css-class="cglp-details-section" :col-count="3">
            <DxSimpleItem v-bind="metadata.gl.attr('HeaderName')" editor-type="dxTextBox">
                <DxRequiredRule />
            </DxSimpleItem>
            <DxSimpleItem v-bind="metadata.gl.attr('EffectiveDate')"
                          editor-type="dxDateBox"
                          data-type="date"
                          display-format="shortdate">
                <DxRequiredRule />
            </DxSimpleItem>
            <DxSimpleItem data-field="includeInactive" editor-type="dxSwitch">
                <DxLabel :text="formatMessage('lblIncludeInactiveComponents')" />
            </DxSimpleItem>
            <DxSimpleItem :col-span="3">
                <DxAccordion :selected-index="null"
                             :collapsible="true"
                             :multiple="false"
                             @option-changed="onAccordionOptionChanged">
                    <DxItem v-for="section in sections" :key="section.title" :title="formatMessage(section.title)">
                        <DxDataGrid :data-source="section.dataSource" @row-prepared="rowPrepared">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row"
                                       :allow-updating="allowEdit"
                                       :allow-deleting="false"
                                       :allow-adding="false"
                                       refresh-mode="repaint" />
                            <DxColumn v-bind="metadata.glDetails.attr('ComponentCompany')"
                                      :allow-editing="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('TaxCode')"
                                      data-type="number"
                                      :allow-editing="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('ComponentCode')"
                                      :allow-editing="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('GLAccountNumber')"
                                      data-type="string"
                                      :allow-editing="allowEdit">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glDetails.attr('GLContraAccountNumber')"
                                      data-type="string"
                                      :allow-editing="allowEdit">
                                <DxRequiredRule />
                            </DxColumn>
                        </DxDataGrid>
                    </DxItem>
                    <DxItem :title="formatMessage('lblFixedGLEntries')">
                        <DxDataGrid :data-source="fixedDataSource" @row-inserting="e => (e.data.id = nextFixedId++)">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row" :allow-updating="allowEdit" :allow-deleting="allowEdit" :allow-adding="allowEdit" />
                            <DxColumn v-bind="metadata.glFixed.attr('ComponentDescription')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glFixed.attr('GLAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glFixed.attr('GLContraAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="{...metadata.glFixed.attr('GLAmount'), dataType: 'number'}">
                                <DxRequiredRule />
                            </DxColumn>
                        </DxDataGrid>
                    </DxItem>
                    <DxItem :title="formatMessage('lblInterAccountGLEntries')">
                        <DxDataGrid :data-source="interAccountDataSource"
                                    @row-inserting="e => (e.data.id = nextInterAccountId++)">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row" :allow-updating="allowEdit" :allow-deleting="allowEdit" :allow-adding="allowEdit" />

                            <DxColumn v-bind="metadata.glInterAcc.attr('InterGLAccount')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glInterAcc.attr('GLAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glInterAcc.attr('GLContraAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                        </DxDataGrid>
                    </DxItem>
                </DxAccordion>
            </DxSimpleItem>
        </DxGroupItem>
    </DxForm>
    <PageFooter>
        <DxButton type="default" styling-mode="outlined" :text="formatMessage('lblCancel')" @click="onCancel" />

        <DxButton type="default"
                  v-if="allowEdit"
                  :text="formatMessage('lblSave')"
                  use-submit-behavior="true"
                  @click="onSubmit"></DxButton>
    </PageFooter>
</template>

<script setup lang="ts">
    import { formatMessage } from "devextreme/localization";
    import { LocalHttpClient } from "@/http-client";
    import { useLocalization } from "@/localization";
    import odataFactory from "@/odata-extensions";

    import { watch, ref, reactive, defineProps, toRaw } from "vue";
    import { DxForm, DxGroupItem, DxSimpleItem, DxLabel, DxRequiredRule } from "devextreme-vue/form";

    import { DxDataGrid, DxColumn, DxFilterRow, DxEditing, DxDataGridTypes } from "devextreme-vue/data-grid";
    import CustomStore from "devextreme/data/custom_store";
    import DataSource from "devextreme/data/data_source";
    import ArrayStore from "devextreme/data/array_store";
    import { DxButton } from "devextreme-vue/button";
    import PageFooter from "@/components/ui/page-footer.vue";
    import { DxAccordion, DxItem } from "devextreme-vue/accordion";
    import { PayslipAction } from "@/enums";
    import { useMetadata } from "@/metadata";

    await useLocalization("CompanyGLDetail", "General", "System.Notification");

    const httpClient = new LocalHttpClient();
    const formStore = new DataSource({
        store: odataFactory.createStore(
            "CompanyGL",
            "CompanyGlId",
            undefined,
            (req: any) => {
                req.headers["X-FrequencyId"] = props.frequencyId.toString();
            }
        )
    });

    const props = defineProps<{
        allowEdit: {
            type: boolean,
            required: true
        },
        companyId: {
            type: number;
            required: true;
        };
        frequencyId: {
            type: number;
            required: true;
        };
        baseUrl: {
            type: string;
            required: true;
        };
        componentsUrl: {
            type: string;
            required: true;
        };
    }>();

    const {
        CompanyGLDto: gl,
        CompanyGLDetailDto: glDetails,
        CompanyGLExtraDto: glFixed,
        CompanyGLInterAccountDto: glInterAcc
    } = await useMetadata(
        "CompanyGLDto",
        "CompanyGLDetailDto",
        "CompanyGLExtraDto",
        "CompanyGLInterAccountDto"
    );

    const metadata = {
        gl,
        glDetails,
        glFixed,
        glInterAcc
    };

    const openedIndex = ref(null);

    // Form formData
    const formData = reactive({
        HeaderName: "",
        EffectiveDate: null as Date | null,
        includeInactive: false
    });

    interface ModifiedComponent {
        ComponentCompany: string;
        GLAccountNumber: string;
        GLContraAccountNumber: string;
    }

    interface FixedEntries {
        id: number;
        ComponentDescription: string;
        GLAccountNo: string;
        GLContraAccountNo: string;
        GLAmount: decimal;
        EffectiveDate: Date;
    }

    interface InterAccountEntries {
        id: number;
        InterGLAccount: string;
        GLAccountNo: string;
        GLContraAccountNo: string;
    }

    // reactive registries
    const modifiedDetails = reactive<Record<number, ModifiedComponent>>({});

    const nextFixedId = ref(1);
    const fixedEntries = reactive<FixedEntries[]>([]);

    const nextInterAccountId = ref(1);
    const interAccountEntries = reactive<InterAccountEntries[]>([]);

    function onCancel() {
        window.location.href = props.baseUrl;
    }

    function onAccordionOptionChanged(e: any) {
        if (e.name === "selectedIndex") {
            openedIndex.value = e.value;
        }
    }

    /* Components accordion sections */
    const sections = reactive([
        { title: "lblAllowances", dataSource: createComponentDataSource(PayslipAction.Allowance) },
        { title: "lblDeductions", dataSource: createComponentDataSource(PayslipAction.Deduction) },
        { title: "lblPersonals", dataSource: createComponentDataSource(PayslipAction.Personal) },
        { title: "lblFringeBenefits", dataSource: createComponentDataSource(PayslipAction.Fringe) },
        { title: "lblCompanyContributions", dataSource: createComponentDataSource(PayslipAction.ComCont) },
        { title: "lblNotes", dataSource: createComponentDataSource(PayslipAction.Note) },
        { title: "lblTotals", dataSource: createComponentDataSource(PayslipAction.Totals) }
    ]);

    /* Components data source */
    function createComponentDataSource(payslipAction: PayslipAction) {
        const componentsUrl = props.componentsUrl.replace(/\/[^/]*$/, `/${payslipAction}`);

        const store = new CustomStore({
            key: "ComponentCompany",
            async load() {
                const response = await httpClient.get(`${componentsUrl}?includeInactive=${formData.includeInactive}`);

                // Mapping to the right case
                return response.data.map((item: any) => ({
                    ComponentCompany: item.componentCompany,
                    TaxCode: item.taxCode,
                    ComponentCode: item.componentCode,
                    GLAccountNumber: item.glAccountNumber,
                    GLContraAccountNumber: item.glContraAccountNumber,
                    IsActive: item.isActive
                }));
            },
            update: (key, values) => {

                modifiedDetails[key] = {
                    ComponentCompany: key,
                    GLAccountNumber: values.GLAccountNumber,
                    GLContraAccountNumber: values.GLContraAccountNumber
                };

                return Promise.resolve(values);
            }
        });

        return new DataSource({
            store,
            key: "ComponentCompany"
        });
    }

    const fixedDataSource = new DataSource({
        store: new ArrayStore({
            data: fixedEntries,
            key: "id"
        })
    });

    const interAccountDataSource = new DataSource({
        store: new ArrayStore({
            data: interAccountEntries,
            key: "id"
        })
    });

    watch(
        () => formData.includeInactive,
        () => {
            if (openedIndex.value >= 0) {
                sections.forEach(section => {
                    if (section.dataSource.isLoaded()) {
                        section.dataSource.reload();
                    }
                });
            }
        }
    );

    async function onSubmit() {
        const detailsArray = Object.values(toRaw(modifiedDetails));
        const fixedEntriesArray = toRaw(fixedEntries).map(entry => ({
            ...entry,
            EffectiveDate: formData.EffectiveDate,
        }));
        const interAccountEntriesArray = toRaw(interAccountEntries);

        const payload = {
            HeaderName: formData.HeaderName,
            EffectiveDate: formData.EffectiveDate,
            CompanyGLDetails: detailsArray,
            CompanyGLExtras: fixedEntriesArray,
            CompanyGLInterAccounts: interAccountEntriesArray,
        };

        await formStore.store().insert(payload);

        Notifications.showSuccess(formatMessage("Create.Success"), window.Route.NotificationId);
        window.location.href = `${props.baseUrl}`;
    }
         
    function rowPrepared(e: DxDataGridTypes.RowPreparedEvent) {
        if (e.rowType !== "data" || e.data.IsActive) return;

        e.rowElement?.classList.add("alert", "alert-danger");
    }
</script>
