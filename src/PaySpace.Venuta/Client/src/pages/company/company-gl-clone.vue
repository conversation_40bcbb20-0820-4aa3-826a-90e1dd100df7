<template>
    <DxForm id="companyGLForm" :form-data="formData" label-location="top">
        <DxGroupItem :caption="formatMessage('lblDetails')" css-class="cglp-details-section" :col-count="3">
            <DxSimpleItem v-bind="metadata.gl.attr('HeaderName')" editor-type="dxTextBox">
                <DxRequiredRule />
            </DxSimpleItem>
            <DxSimpleItem v-bind="metadata.gl.attr('EffectiveDate')"
                          editor-type="dxDateBox"
                          data-type="date"
                          display-format="shortdate">
                <DxRequiredRule />
            </DxSimpleItem>
            <DxSimpleItem data-field="includeInactive" editor-type="dxSwitch">
                <DxLabel :text="formatMessage('lblIncludeInactiveComponents')" />
            </DxSimpleItem>
            <DxSimpleItem :col-span="3">
                <DxAccordion
                    :selected-index="null"
                    :collapsible="true"
                    :multiple="false"
                    @option-changed="onAccordionOptionChanged">
                    <DxItem v-for="section in sections" :key="section.title" :title="formatMessage(section.title)">
                        <DxDataGrid :data-source="section.dataSource" @row-prepared="rowPrepared">
                            <DxFilterRow :visible="true" />
                            <DxEditing
                                mode="row"
                                :allow-updating="allowEdit"
                                :allow-deleting="false"
                                :allow-adding="false"
                                refresh-mode="repaint" />
                            <DxColumn
                                v-bind="metadata.glDetails.attr('ComponentCompany')"
                                :allow-editing="false" />
                            <DxColumn
                                v-bind="metadata.glDetails.attr('TaxCode')"
                                data-type="number"
                                :allow-editing="false" />
                            <DxColumn
                                v-bind="metadata.glDetails.attr('ComponentCode')"
                                :allow-editing="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('GLAccountNumber')"
                                      data-type="string"
                                      :allow-editing="allowEdit">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glDetails.attr('GLContraAccountNumber')"
                                      data-type="string"
                                      :allow-editing="allowEdit">
                                <DxRequiredRule />
                            </DxColumn>
                        </DxDataGrid>
                    </DxItem>
                    <DxItem :title="formatMessage('lblFixedGLEntries')">
                        <DxDataGrid :data-source="fixedDataSource" @row-inserting="e => (e.data.id = nextFixedId++)">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row" :allow-updating="allowEdit" :allow-deleting="allowEdit" :allow-adding="allowEdit" />
                            <DxColumn v-bind="metadata.glFixed.attr('ComponentDescription')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glFixed.attr('GLAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glFixed.attr('GLContraAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glFixed.attr('GLAmount')">
                                <DxRequiredRule />
                            </DxColumn>
                        </DxDataGrid>
                    </DxItem>
                    <DxItem :title="formatMessage('lblInterAccountGLEntries')">
                        <DxDataGrid
                            :data-source="interAccountDataSource"
                            @row-inserting="e => (e.data.id = nextInterAccountId++)">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row" :allow-updating="allowEdit" :allow-deleting="allowEdit" :allow-adding="allowEdit" />

                            <DxColumn v-bind="metadata.glInterAcc.attr('InterGLAccount')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glInterAcc.attr('GLAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                            <DxColumn v-bind="metadata.glInterAcc.attr('GLContraAccountNo')">
                                <DxRequiredRule />
                            </DxColumn>
                        </DxDataGrid>
                    </DxItem>
                </DxAccordion>
            </DxSimpleItem>
        </DxGroupItem>
    </DxForm>
    <PageFooter>
        <DxButton type="default" styling-mode="outlined" :text="formatMessage('lblCancel')" @click="onCancel" />

        <DxButton type="default"
            v-if="allowEdit"
            :text="formatMessage('lblSave')"
            use-submit-behavior="true"
            @click="onSubmit"></DxButton>
    </PageFooter>

    <DxLoadPanel
        :position="{ for: '#companyGLForm' }"
        :visible="showLoading"
        :show-indicator="true"
        :show-pane="true"
        :shading="true"
        :hide-on-outside-click="false"
        shadingColor="rgba(255,255,255, .7)" />
</template>

<script setup lang="ts">
    import { formatMessage } from "devextreme/localization";
    import { useLocalization } from "@/localization";
    import { LocalHttpClient } from "@/http-client";
    import { ODataFormService } from "@/services/odata-form-service";
    import odataFactory from "@/odata-extensions";

    import { watch, ref, reactive, defineProps, toRaw, onMounted } from "vue";
    import { DxForm, DxGroupItem, DxSimpleItem, DxLabel, DxRequiredRule } from "devextreme-vue/form";

    import { DxDataGrid, DxColumn, DxFilterRow, DxEditing, DxDataGridTypes } from "devextreme-vue/data-grid";
    import { DxLoadPanel } from "devextreme-vue/load-panel";
    import DataSource from "devextreme/data/data_source";
    import ArrayStore from "devextreme/data/array_store";
    import { DxButton } from "devextreme-vue/button";
    import PageFooter from "@/components/ui/page-footer.vue";
    import { DxAccordion, DxItem } from "devextreme-vue/accordion";
    import { PayslipAction } from "@/enums";
    import { useMetadata } from "@/metadata";

    const localHttpClient = new LocalHttpClient();

    await useLocalization("CompanyGLDetail");


    const props = defineProps<{
        allowEdit: {
            type: boolean,
            required: true
        },
        companyId: {
            type: number;
            required: true;
        };
        frequencyId: {
            type: number;
            required: true;
        };
        cloneId: {
            type: number;
            required: false;
        };
        baseUrl: {
            type: string;
            required: true;
        };
        componentsUrl: {
            type: string;
            required: true;
        };
    }>();

    const {
        CompanyGLDto: gl,
        CompanyGLDetailDto: glDetails,
        CompanyGLExtraDto: glFixed,
        CompanyGLInterAccountDto: glInterAcc
    } = await useMetadata(
        "CompanyGLDto",
        "CompanyGLDetailDto",
        "CompanyGLExtraDto",
        "CompanyGLInterAccountDto"
    );

    const metadata = {
        gl,
        glDetails,
        glFixed,
        glInterAcc
    };

    const showLoading = ref(false);
    const openedIndex = ref(0);

    // Form formData
    const formData = reactive({
        HeaderName: "",
        EffectiveDate: null as Date | null,
        includeInactive: false
    });

    interface ModifiedComponent {
        id: number;
        ComponentCompany: string;
        GLAccountNumber: string;
        GLContraAccountNumber: string;
    }

    interface FixedEntries {
        id: number;
        ComponentDescription: string;
        GLAccountNo: string;
        GLContraAccountNo: string;
        GLAmount: decimal;
        EffectiveDate: Date;
        CompanyFrequencyId: number;
    }

    interface InterAccountEntries {
        id: number;
        InterGLAccount: string;
        GLAccountNo: string;
        GLContraAccountNo: string;
    }

    // reactive registries
    const modifiedDetails = reactive<Record<number, ModifiedComponent>>({});

    let nextFixedId = ref(1);
    let nextInterAccountId = ref(1);

    const interAccountDataSource = ref(null);
    const fixedDataSource = ref(null);

    onMounted(async () => {
        // Load GL to clone from
        const formStore = new DataSource({
            store: odataFactory.createStore(
                "CompanyGL",
                "CompanyGlId",
                undefined,
                (req: any) => {
                    req.headers["X-FrequencyId"] = props.frequencyId.toString();
                }
            )
        });

        const store = formStore.store();
        const gl = await store.byKey(Number(props.cloneId));

        if (gl != null){
            formData.EffectiveDate = gl.EffectiveDate
        }

        // Load child entries
        fixedDataSource.value = await loadFixedEntries(gl.HeaderName);
        interAccountDataSource.value = await loadInterAccountEntries(gl.HeaderName);
    });

    function onCancel() {
        window.location.href = props.baseUrl;
    }

    function onAccordionOptionChanged(e: any) {
        if (e.name === "selectedIndex") {
            openedIndex.value = e.value;
        }
    }

    /* Components accordion sections */
    const sections = reactive([
        { title: "lblAllowances", dataSource: createComponentDataSource(PayslipAction.Allowance) },
        { title: "lblDeductions", dataSource: createComponentDataSource(PayslipAction.Deduction) },
        { title: "lblPersonals", dataSource: createComponentDataSource(PayslipAction.Personal) },
        { title: "lblFringeBenefits", dataSource: createComponentDataSource(PayslipAction.Fringe) },
        { title: "lblCompanyContributions", dataSource: createComponentDataSource(PayslipAction.ComCont) },
        { title: "lblNotes", dataSource: createComponentDataSource(PayslipAction.Note) },
        { title: "lblTotals", dataSource: createComponentDataSource(PayslipAction.Totals) }
    ]);

    /* Components data source */
    function createComponentDataSource(payslipAction: PayslipAction) {
        const componentsUrl = props.componentsUrl.replace(/\/[^/]*$/, `/${payslipAction}`);

        const customLoad = async () => {
            let url = `${componentsUrl}`;

            const includeInactive = formData.includeInactive;

            if (includeInactive != null && includeInactive !== '') {
                const separator = url.includes('?') ? '&' : '?';
                url += `${separator}includeInactive=${includeInactive}`;
            }

            const response = await localHttpClient.get(url);

            return response.data.map((item: any) => ({
                ComponentCompany: item.componentCompany,
                TaxCode: item.taxCode,
                ComponentCode: item.componentCode,
                GLAccountNumber: item.glAccountNumber,
                GLContraAccountNumber: item.glContraAccountNumber,
                IsActive: item.isActive
            }));
        };

        return new DataSource({
            key: "ComponentCompany",
            load: customLoad,
            update: (key, values) => {
                modifiedDetails[key] = {
                    ComponentCompany: key,
                    GLAccountNumber: values.GLAccountNumber,
                    GLContraAccountNumber: values.GLContraAccountNumber
                };
                return Promise.resolve(values);
            }
        });
    }

    async function loadFixedEntries(headerName: string): Promise<DataSource> {

        const sourceFixedDataSource = new DataSource({
            store: odataFactory.createStore(
                "CompanyGLExtra",
                "CompanyExtraGLId",
                undefined,
                (req: any) => {
                    req.headers["X-FrequencyId"] = props.frequencyId.toString();
                }
            ),
            filter: ["GeneralLedgerHeader", "=", headerName]
        });

        // Load entries from the OData endpoint
        const loadedFixedData = await sourceFixedDataSource.load();

        // Map the loaded entries into the fixedEntries array format
        const fixedEntries = loadedFixedData.map(item => ({
            id: nextFixedId.value++, // Generate unique row ID
            ComponentDescription: item.ComponentDescription ?? "",
            GLAccountNo: item.GLAccountNo ?? "",
            GLContraAccountNo: item.GLContraAccountNo ?? "",
            GLAmount: item.GLAmount ?? 0,
            EffectiveDate: item.EffectiveDate,
        } as FixedEntries));

        // Return a new in-memory DataSource based on the mapped entries
        return new DataSource({
            store: new ArrayStore({
                data: fixedEntries,
                key: "id"
            })
        });
    }

    async function loadInterAccountEntries(headerName: string): Promise<DataSource> {
        const sourceInterAccDataSource = new DataSource({
            store: odataFactory.createStore(
                "CompanyGLInterAccount",
                "CompanyGlInterAccId",
                undefined,
                (req: any) => {
                    req.headers["X-FrequencyId"] = props.frequencyId.toString();
                }
            ),
            filter: ["GeneralLedgerHeader", "=", headerName]
        });

        const loadedInterAccData = await sourceInterAccDataSource.load();

        // Map the loaded inter-account entries into the local data format
        const interAccountEntries = loadedInterAccData.map(item => ({
            id: nextInterAccountId.value++, // Generate unique row ID
            InterGLAccount: item.InterGLAccount,
            GLAccountNo: item.GLAccountNo,
            GLContraAccountNo: item.GLContraAccountNo
        } as InterAccountEntries));

        // Return a new in-memory DataSource containing the mapped entries
        return new DataSource({
            store: new ArrayStore({
                data: interAccountEntries,
                key: "id"
            })
        });
    }

    watch(
        () => formData.includeInactive,
        () => {
            if (openedIndex.value >= 0) {
                sections.forEach(section => {
                    if (section.dataSource.isLoaded()) {
                        section.dataSource.reload();
                    }
                });
            }
        }
    );

    async function onSubmit() {
        showLoading.value = true;

        const fixedEntriesArray = await fixedDataSource.value.store().load();
        const interAccountEntriesArray = await interAccountDataSource.value.store().load();

        const payload = {
            HeaderName: formData.HeaderName,
            EffectiveDate: formData.EffectiveDate,
            CompanyGLDetails: Object.values(toRaw(modifiedDetails)),
            CompanyGLExtras: fixedEntriesArray.map(e => ({
                ...toRaw(e),
                EffectiveDate: formData.EffectiveDate,
            })),
            CompanyGLInterAccounts: interAccountEntriesArray.map(e => toRaw(e))
        };

        // Call oData
        const oDataFormService = new ODataFormService(`CompanyGL/Clone/${props.cloneId}`);

        const headers: HeadersInit = {
            "X-FrequencyId": props.frequencyId.toString()
        };

        await oDataFormService.postDataAsync(payload, headers);

        Notifications.showSuccess(formatMessage("Create.Success"), window.Route.NotificationId);
        window.location.href = `${props.baseUrl}`;
    }

    function rowPrepared(e: DxDataGridTypes.RowPreparedEvent) {
        if (e.rowType !== "data" || e.data.IsActive) return;

        e.rowElement?.classList.add("alert", "alert-danger");
    }
</script>
