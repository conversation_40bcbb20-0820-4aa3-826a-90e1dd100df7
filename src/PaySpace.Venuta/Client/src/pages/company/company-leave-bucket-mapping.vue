<template>
    <Card>
        <DxDataGrid
            :data-source="dataSource"
            :hover-state-enabled="true"
            :show-column-lines="false"
            :column-auto-width="true"
            :sorting="{ mode: 'multiple' }"
            :editing="{
                allowUpdating: true,
                allowAdding: true,
                allowDeleting: true
            }"
            @editing-start="onEditingStart">
            <DxToolbar>
                <DxItem
                    location="after"
                    widget="dxButton"
                    :options="{
                        icon: 'add',
                        onClick: addRow
                    }" />
            </DxToolbar>
            <DxFilterRow :visible="true" />

            <DxColumn v-bind="metadata.attr('FromLeaveScheme')" />
            <DxColumn v-bind="metadata.attr('ToLeaveScheme')" />
        </DxDataGrid>
    </Card>
</template>
<script setup lang="ts">
    import Card from "@/components/ui/card.vue";
    import { DxDataGrid, DxColumn, DxFilterRow, DxToolbar, DxItem, DxDataGridTypes } from "devextreme-vue/data-grid";
    import { useMetadata } from "@/metadata";
    import { odataFactory } from "@/odata-extensions";
    import DataSource from "devextreme/data/data_source";
    import { useLocalization } from "@/localization";

    const props = defineProps({
        editUrl: {
            type: String,
            required: true
        }
    });
    
    await useLocalization("Company.LeaveBucketMapping", "General", "System.Notification");
    
    const { CompanyLeaveBucketMappingDto: metadata } = await useMetadata("CompanyLeaveBucketMappingDto");

    const dataSource = new DataSource({
        store: odataFactory.createStore("CompanyLeaveBucketMapping", "LeaveBucketMappingId"),
    });

    const addRow = () => (window.location.href = props.editUrl);

    function onEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        e.cancel = true;
        window.location.href = `${props.editUrl}/${e.key}`;
    }
</script>