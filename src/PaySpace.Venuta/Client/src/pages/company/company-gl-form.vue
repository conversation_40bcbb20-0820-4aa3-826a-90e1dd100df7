<template>
    <DxForm id="companyGLForm" :form-data="formData" label-location="top">
        <DxGroupItem :caption="formatMessage('lblDetails')" :col-count="3">
            <DxSimpleItem v-bind="metadata.gl.attr('HeaderName')"
                          editor-type="dxTextBox">
                <DxRequiredRule />
            </DxSimpleItem>
            <DxSimpleItem v-bind="metadata.gl.attr('EffectiveDate')"
                          data-field="EffectiveDate"
                          editor-type="dxDateBox"
                          data-type="date"
                          display-format="shortdate">
                <DxRequiredRule />
            </DxSimpleItem>

            <DxSimpleItem data-field="includeInactive"
                          editor-type="dxSwitch">
                <DxLabel :text="formatMessage('lblIncludeInactiveComponents')" />
            </DxSimpleItem>

            <DxSimpleItem :col-span="3">
                <DxAccordion :selected-index="null" :collapsible="true" @option-changed="onAccordionOptionChanged">
                    <DxItem v-for="section in sections" :key="section.title" :title="formatMessage(section.title)">
                        <DxDataGrid :data-source="section.dataSource" @row-prepared="rowPrepared">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row"
                                       :allow-updating="allowEdit"
                                       :allow-deleting="false"
                                       :allow-adding="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('ComponentCompany')"
                                      :allow-editing="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('TaxCode')"
                                      data-type="number"
                                      :allow-editing="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('ComponentCode')"
                                      :allow-editing="false" />
                            <DxColumn v-bind="metadata.glDetails.attr('GLAccountNumber')"
                                      :allow-editing="allowEdit"/>
                            <DxColumn v-bind="metadata.glDetails.attr('GLContraAccountNumber')"
                                      :allow-editing="allowEdit"/>
                        </DxDataGrid>
                    </DxItem>
                    <DxItem :title="formatMessage('lblFixedGLEntries')">
                        <DxDataGrid :data-source="fixedDataSource">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row" :allow-updating="allowEdit" :allow-deleting="allowEdit" :allow-adding="allowEdit" />
                            <DxColumn v-bind="metadata.glFixed.attr('ComponentDescription')" />
                            <DxColumn v-bind="metadata.glFixed.attr('GLAccountNumber')" />
                            <DxColumn v-bind="metadata.glFixed.attr('GLAccountNo')" />
                            <DxColumn v-bind="metadata.glFixed.attr('GLContraAccountNo')" />
                            <DxColumn v-bind="{...metadata.glFixed.attr('GLAmount'), dataType: 'number'}" />
                        </DxDataGrid>
                    </DxItem>
                    <DxItem :title="formatMessage('lblInterAccountGLEntries')">
                        <DxDataGrid :data-source="interAccountDataSource">
                            <DxFilterRow :visible="true" />
                            <DxEditing mode="row" :allow-updating="allowEdit" :allow-deleting="allowEdit" :allow-adding="allowEdit" />
                            <DxColumn v-bind="metadata.glInterAcc.attr('InterGLAccount')"
                                      data-field="InterGLAccount" />
                            <DxColumn v-bind="metadata.glInterAcc.attr('GLAccountNo')"
                                      data-field="GLAccountNo" />
                            <DxColumn v-bind="metadata.glInterAcc.attr('GLContraAccountNo')"
                                      data-field="GLContraAccountNo" />
                        </DxDataGrid>
                    </DxItem>
                </DxAccordion>
            </DxSimpleItem>
        </DxGroupItem>
    </DxForm>
    <PageFooter>
        <DxButton type="default" styling-mode="outlined" :text="formatMessage('lblCancel')" @click="onCancel" />

        <DxButton type="default"
            v-if="allowEdit"
            :text="formatMessage('lblSave')"
            use-submit-behaviour="true"
            @click="onSubmit"></DxButton>
    </PageFooter>
</template>

<script setup lang="ts">
    import { formatMessage } from "devextreme/localization";
    import { LocalHttpClient } from "@/http-client";
    import { useLocalization } from "@/localization";

    import { watch, ref, defineProps, onMounted } from "vue";
    import { DxForm, DxGroupItem, DxSimpleItem, DxLabel, DxRequiredRule } from "devextreme-vue/form";

    import { DxDataGrid, DxColumn, DxFilterRow, DxEditing, DxDataGridTypes } from "devextreme-vue/data-grid";

    import odataFactory from "@/odata-extensions";
    import { DxButton } from "devextreme-vue/button";
    import PageFooter from "@/components/ui/page-footer.vue";
    import { DxAccordion, DxItem } from "devextreme-vue/accordion";
    import DataSource from "devextreme/data/data_source";
    import { PayslipAction } from "@/enums";
    import { useMetadata } from "@/metadata";

    const httpClient = new LocalHttpClient();
    await useLocalization("CompanyGLDetail", "General", "System.Notification");

    const {
        CompanyGLDto: gl,
        CompanyGLDetailDto: glDetails,
        CompanyGLExtraDto: glFixed,
        CompanyGLInterAccountDto: glInterAcc
    } = await useMetadata(
        "CompanyGLDto",
        "CompanyGLDetailDto",
        "CompanyGLExtraDto",
        "CompanyGLInterAccountDto"
    );

    const metadata = {
        gl,
        glDetails,
        glFixed,
        glInterAcc
    };

    const props = defineProps<{
        allowEdit: {
            type: boolean,
            required: true
        },
        companyId: {
            type: number;
            required: true;
        };
        frequencyId: {
            type: number;
            required: true;
        };
        companyGlId: {
            type: number;
        };
        baseUrl: {
            type: string;
            required: true;
        };
        componentsUrl: {
            type: string;
            required: true;
        };
    }>();

    const openedIndex = ref(0);
    const formData = ref({});

    const formStore = new DataSource({
        store: odataFactory.createStore(
            "CompanyGL",
            "CompanyGlId",
            undefined,
            (req: any) => {
                req.headers["X-FrequencyId"] = props.frequencyId.toString();
            }
        )
    });

    /* Components accordion sections */
    const sections = [
        { title: "lblAllowances", dataSource: createComponentDataSource(PayslipAction.Allowance) },
        { title: "lblDeductions", dataSource: createComponentDataSource(PayslipAction.Deduction) },
        {
            title: "lblCompanyContributions",
            dataSource: createComponentDataSource(PayslipAction.ComCont),
            contentReady: false
        },
        {
            title: "lblFringeBenefits",
            dataSource: createComponentDataSource(PayslipAction.Fringe),
            contentReady: false
        },
        { title: "lblPersonals", dataSource: createComponentDataSource(PayslipAction.Personal) },
        { title: "lblNotes", dataSource: createComponentDataSource(PayslipAction.Note) },
        { title: "lblTotals", dataSource: createComponentDataSource(PayslipAction.Totals) }
    ];

    onMounted(async () => {
        const store = formStore.store();
        formData.value = await store.byKey(Number(props.companyGlId));

        // Load GL Inter Account entries
        const headerName = formData.value.HeaderName;

        interAccountDataSource.value = new DataSource({
            store: odataFactory.createStore(
                "CompanyGLInterAccount",
                "CompanyGlInterAccId",
                undefined,
                (req: any) => {
                    req.headers["X-FrequencyId"] = props.frequencyId.toString();

                    if (req.method === "POST") {
                        req.payload.EffectiveDate = formData.value.EffectiveDate;
                        req.payload.GeneralLedgerHeader = headerName;
                    }
                }
            ),
            filter: ["GeneralLedgerHeader", "=", headerName]
        });

        // Load GL Extra entries
        fixedDataSource.value = new DataSource({
            store: odataFactory.createStore(
                "CompanyGLExtra",
                "CompanyExtraGLId",
                undefined,
                (req: any) => {
                    req.headers["X-FrequencyId"] = props.frequencyId.toString();

                    if (req.method === "POST") {
                        req.payload.EffectiveDate = formData.value.EffectiveDate;
                        req.payload.GeneralLedgerHeader = headerName;
                    }
                }
            ),
            filter: ["GeneralLedgerHeader", "=", headerName]
        });
    });

    async function onSubmit() {
        await formStore.store().update(Number(props.companyGlId), formData.value);
        Notifications.showSuccess(formatMessage("Update.Success"), window.Route.NotificationId);
    }

    function onCancel() {
        window.location.href = props.baseUrl;
    }

    function onAccordionOptionChanged(e: any) {
        if (e.name === "selectedIndex") {
            openedIndex.value = e.value;
        }
    }

    /* Components data source */
    function createComponentDataSource(payslipAction: PayslipAction) {
        const componentsUrl = props.componentsUrl.replace(/\/[^/]*$/, `/${payslipAction}`);

        const store = odataFactory.createStore(
            "CompanyGLDetail",
            "CompanyGLDetailId",
            undefined,
            (req: any) => {
                req.headers["X-FrequencyId"] = props.frequencyId.toString();
            }
        );

        const customLoad = async () => {
            let url = `${componentsUrl}`;

            const includeInactive = formData.value.includeInactive;

            if (includeInactive != null && includeInactive !== '') {
                const separator = url.includes('?') ? '&' : '?';
                url += `${separator}includeInactive=${includeInactive}`;
            }

            const response = await httpClient.get(url);

            return response.data.map((item: any) => ({
                CompanyGlDetailId: item.companyGLDetailId,
                ComponentCompany: item.componentCompany,
                TaxCode: item.taxCode,
                ComponentCode: item.componentCode,
                GLAccountNumber: item.glAccountNumber,
                GLContraAccountNumber: item.glContraAccountNumber,
                IsActive: item.isActive
            }));
        };

        return new DataSource({
            key: "CompanyGlDetailId",
            load: customLoad,
            update: (key, values) => store.update(key, values)
        });
    }

    const interAccountDataSource = ref(null);
    const fixedDataSource = ref(null);

    watch(
        () => formData.value.includeInactive,
        () => {
            if (openedIndex.value >= 0) {
                // Reload data source for each section that has contentReady set to true
                sections.forEach(section => {
                    if (section.dataSource.isLoaded()) {
                        section.dataSource.reload();
                    }
                });
                if (fixedDataSource.value.isLoaded()) {
                    fixedDataSource.value.reload();
                }
                if (interAccountDataSource.value.isLoaded()) {
                    interAccountDataSource.value.reload();
                }
            }
        }
    );

    function rowPrepared(e: DxDataGridTypes.RowPreparedEvent) {
        if (e.rowType !== "data" || e.data.IsActive) return;

        e.rowElement?.classList.add("alert", "alert-danger");
    }
</script>
