<template>
    <Card title="Details">
        <DxForm :form-data="formData" ref="formRef" :col-count="2" label-location="top">
            <DxSimpleItem
                v-if="companyLeaveSchemes"
                editor-type="dxSelectBox"
                :editor-options="fromLeaveSchemeEditorOptions"
                data-field="FromLeaveScheme">
                <DxRequiredRule />
            </DxSimpleItem>

            <DxSimpleItem
                editor-type="dxSelectBox"
                v-if="companyLeaveSchemes"
                :editor-options="toLeaveSchemeEditorOptions"
                data-field="ToLeaveScheme">
                <DxRequiredRule />
                <DxCustomRule
                    :validation-callback="e => formData.FromLeaveScheme !== e.value"
                    :message="formatMessage('lblFromToMatchMessage')" />
            </DxSimpleItem>
        </DxForm>

        <DxDataGrid
            :data-source="formData.LeaveBucketMappingDetails"
            :hover-state-enabled="true"
            :show-column-lines="false"
            :column-auto-width="true"
            :sorting="{ mode: 'multiple' }"
            :editing="{
                mode: 'row',
                allowUpdating: true
            }">
            <DxFilterRow :visible="true" />

            <DxColumn
                data-field="FromLeaveBucket"
                :allow-editing="false"
                :caption="formatMessage('lblLeaveDescription')"
                width="50%" />
            <DxColumn
                data-field="ToLeaveBucket"
                :caption="formatMessage('lblMapLeaveTo')"
                editor-type="dxSelectBox"
                :editor-options="mapToEditorOptions"
                width="50%" />
        </DxDataGrid>
    </Card>

    <PageFooter>
        <DxButton
            :text="formatMessage('lblCancel')"
            @click="goToIndexPage"
            styling-mode="outlined"
            type="default" />
        <DxButton :text="formatMessage('lblSave')" @click="save" type="default" />
    </PageFooter>
</template>
<script setup lang="ts">
    import PageFooter from "@/components/ui/page-footer.vue";
    import { DxButton } from "devextreme-vue/button";
    import Card from "@/components/ui/card.vue";
    import { ref, computed } from "vue";
    import { DxForm, DxSimpleItem } from "devextreme-vue/form";
    import { DxDataGrid, DxColumn, DxFilterRow, DxRequiredRule, DxCustomRule } from "devextreme-vue/data-grid";
    import { ExternalHttpClient } from "@/http-client";
    import DataSource from "devextreme/data/data_source";
    import { formatMessage } from "devextreme/localization";
    import { ODataFormService } from "@/services/odata-form-service";
    import { useLocalization } from "@/localization";
    import { odataFactory } from "@/odata-extensions";
    import type { ClickEvent } from "devextreme/ui/button";

    interface LeaveBucketMappingDetail {
        FromLeaveBucket: string;
        ToLeaveBucket: string;
        LeaveBucketMappingDetailId: number;
    }

    interface LeaveBucketMappingFormData {
        FromLeaveScheme: string;
        ToLeaveScheme: string;
        LeaveBucketMappingDetails: LeaveBucketMappingDetail[];
    }

    const props = defineProps({
        leaveBucketMappingId: {
            type: Number,
            required: true
        },
        editUrl: {
            type: String,
            required: true
        },
        isNewLeaveBucketMapping: {
            type: Boolean,
            default: false,
            required: false
        }
    });

    await useLocalization("Company.LeaveBucketMapping", "General", "System.Notification");

    const externalHttpClient = new ExternalHttpClient();
    const oDataFormService = new ODataFormService("CompanyLeaveBucketMapping");
    const isNewEntry = !(props.leaveBucketMappingId && props.leaveBucketMappingId > 0);
    const isEditable = props.leaveBucketMappingId ? false : true;

    const formRef = ref();
    const formData = ref<LeaveBucketMappingFormData>(
        isEditable
            ? {
                  FromLeaveScheme: "",
                  ToLeaveScheme: "",
                  LeaveBucketMappingDetails: []
              }
            : await oDataFormService.getDataAsync(props.leaveBucketMappingId, isNewEntry)
    );

    const mapLeaveToLookup = ref<Array<{ Value: string; Description: string }>>([]);

    const mapToEditorOptions = computed(() => ({
        dataSource: [...mapLeaveToLookup.value],
        valueExpr: "Value",
        displayExpr: "Description"
    }));

    const companyLeaveSchemes = new DataSource({
        store: odataFactory.createStore("CompanyLeaveScheme", "CompanyLeaveSchemeId"),
        sort: ["SchemeName"]
    });

    const options = {
        dataSource: companyLeaveSchemes,
        valueExpr: "SchemeName",
        displayExpr: "SchemeName",
        required: true,
        searchEnabled: true,
        readOnly: !isEditable,
        disabled: !isEditable
    };

    let leaveBucketMappingDetails: LeaveBucketMappingDetail[] = [{
        FromLeaveBucket: "",
        ToLeaveBucket: "",
        LeaveBucketMappingDetailId: 0
    }];
    const fromLeaveSchemeEditorOptions = computed(() => ({
        ...options,
        onValueChanged: async (e: { value: string }) => {
            const response = await externalHttpClient.get(
                `/odata/v2.0/${window.Route.CompanyId}/lookup/CompanyLeaveScheme/${e.value}`
            );

            formData.value.LeaveBucketMappingDetails = leaveBucketMappingDetails = response.value.map((item: { Value: string }) => ({
                FromLeaveBucket: item.Value,
                ToLeaveBucket: "",
                LeaveBucketMappingDetailId: 0
            }));
        },
        onInitialized: async () => {
            if (!isEditable) {
                const response = await externalHttpClient.get(
                    `/odata/v2.0/${window.Route.CompanyId}/lookup/CompanyLeaveScheme/${formData.value.FromLeaveScheme}`
                );

                const mappingMap = Object.fromEntries(
                    formData.value.LeaveBucketMappingDetails.map(m => [
                        m.FromLeaveBucket,
                        {
                            ToLeaveBucket: m.ToLeaveBucket,
                            LeaveBucketMappingDetailId: m.LeaveBucketMappingDetailId
                        }
                    ])
                );
                formData.value.LeaveBucketMappingDetails = response.value.map((bucket: { Value: string }) => {
                    const mapping = mappingMap[bucket.Value] || {};
                    return {
                        FromLeaveBucket: bucket.Value,
                        ToLeaveBucket: mapping.ToLeaveBucket || "",
                        LeaveBucketMappingDetailId: mapping.LeaveBucketMappingDetailId || 0
                    };
                });
            }
        }
    }));

    const toLeaveSchemeEditorOptions = computed(() => ({
        ...options,
        onValueChanged: async (e: { value: string }) => {
            const response = await externalHttpClient.get(
                `/odata/v2.0/${window.Route.CompanyId}/lookup/CompanyLeaveScheme/${e.value}`
            );
            mapLeaveToLookup.value = response.value;
            // Reset mapping grid
            formData.value.LeaveBucketMappingDetails = JSON.parse(JSON.stringify(leaveBucketMappingDetails));
        },
        onInitialized: async () => {
            if (!isEditable) {
                const response = await externalHttpClient.get(
                    `/odata/v2.0/${window.Route.CompanyId}/lookup/CompanyLeaveScheme/${formData.value.ToLeaveScheme}`
                );
                mapLeaveToLookup.value = response.value;
            }
        }
    }));

    async function save(e: ClickEvent) {
        e.component.option("disabled", true);

        let success;
        const validationResults = formRef.value?.instance.validate();

        let data = { ...formData.value };

        data.LeaveBucketMappingDetails = data.LeaveBucketMappingDetails.filter(
            (detail: LeaveBucketMappingDetail) => detail.ToLeaveBucket && detail.ToLeaveBucket.trim() !== ""
        );

        if (!data.LeaveBucketMappingDetails.length) {
            Notifications.showErrors(
                formatMessage("lblLeaveBucketMappingDetailsRequired"),
                window.Route.NotificationId
            );
            e.component.option("disabled", false);
            return;
        }

        if (!validationResults?.isValid) {
            const errorMessages = validationResults?.brokenRules!.map((err: { message: string }) => err.message);
            if (errorMessages) {
                Notifications.showErrors(errorMessages.Message, window.Route.NotificationId);
            }
        } else {
            if (props.leaveBucketMappingId && !isEditable) {
                success = await oDataFormService.putDataAsync(props.leaveBucketMappingId, data);
            } else {
                success = await oDataFormService.postDataAsync(data);
            }

            if (success) {
                goToIndexPage();
            }
        }

        e.component.option("disabled", false);
    }

    const goToIndexPage = () => (window.location.href = props.editUrl);
</script>
