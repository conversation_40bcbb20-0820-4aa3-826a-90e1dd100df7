<template>
    <DxForm id="componentForm" :form-data="formData">
        <DxGroupItem :caption="formatMessage('lblComponentFilters')">
            <DxGroupItem :col-count-by-screen="{md: 2, lg: 3}">
                <DxItem dataField="RunId" editorType="dxLookup" :label="{ text: formatMessage('RunId') }" :col-span="1"
                        :editorOptions="{ dataSource: runDs, displayExpr: 'runDescription', valueExpr: 'runId' }" />
            </DxGroupItem>
            <DxItem dataField="ShowInactive" editorType="dxCheckBox" :editor-options="{ text: formatMessage('ShowInactive') }" :label="{ visible: false }" />
        </DxGroupItem>
        <DxItem>
            <DxAccordion :data-source="payslipActionsList" :collapsible="true" :multiple="false"
                         :selected-items="selectedItems"
                         :include-inactive="formData.ShowInactive">
                <template #title="{ data }">
                    {{ data.ActionDescription }}
                </template>
                <template #item="{ data }">
                    <company-component-grid :run-id="formData.RunId"
                                            :payslip-action="data.PayslipAction"
                                            :include-inactive="formData.ShowInactive"
                                            :show-calc-exceptions="showCalcExceptions"
                                            :is="selectedItems"
                                            :country-id="countryId"
                                            @sub-code-data-loaded="subCodeDataLoaded" />
                </template>
            </DxAccordion>
        </DxItem>
    </DxForm>
    <div id="component-sub-code-modal" class="modal"
         data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title"> {{ componentCalcExceptionModel.ComponentName }}</div>
                </div>
                <component-sub-codes ref="subCode" :key="componentCalcExceptionModel.ComponentId"
                                     :allow-edit="allowEdit"
                                     :component-id="componentCalcExceptionModel.ComponentId"
                                     :component-name="componentCalcExceptionModel.ComponentName"
                                     :show-taxability-option="showTaxabilityOption" :country-id="countryId"
                                     :component-calc-exception-id="componentCalcExceptionModel.ComponentCalcExceptionId" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { LocalHttpClient } from "@/http-client";
    import { useLocalization } from "@/localization";
    import CompanyComponentGrid, { ComponentCalcExceptionModel } from "@components/company-component-grid.vue";
    import ComponentSubCodes from "@components/component-sub-codes.vue";
    import { createStore } from 'devextreme-aspnet-data-nojquery';
    import { DxAccordion } from "devextreme-vue/accordion";
    import { DxForm, DxGroupItem, DxItem } from 'devextreme-vue/form';
    import { formatMessage } from 'devextreme/localization';
    import { ref } from "vue";
    import { useTenant } from "@/composables/tenant";

    const props = defineProps({
        defaultRunId: {
            type: Number,
            required: true
        },
        countryId: {
            type: Number,
            required: true
        },
        allowEdit: {
            type: Boolean,
            required: true
        },
        showCalcExceptions: {
            type: Boolean,
            required: true
        },
        showTaxabilityOption: {
            type: Boolean,
            required: true
        },
        payslipActions: {
            type: String,
            required: true
        }
    });

    await useLocalization('Payroll.Components.Company');

    const { companyId, frequencyId } = useTenant();

    const subCode = ref<bootstrap.Modal | null>(null);
    const payslipActionsList = JSON.parse(props.payslipActions);

    const runDs = createStore({
        key: "runId",
        loadUrl: new LocalHttpClient().getUrl(`company/${companyId}/frequency/${frequencyId}/payroll-components/api/runs`)
    });
    const formData = {
        RunId: props.defaultRunId,
        ShowInactive: false
    };

    let selectedItems = [payslipActionsList[0]];
    const componentCalcExceptionModel = ref<ComponentCalcExceptionModel>(new ComponentCalcExceptionModel());

    async function subCodeDataLoaded(e: ComponentCalcExceptionModel) {
        if (e.ComponentId != componentCalcExceptionModel.value.ComponentId) {
            componentCalcExceptionModel.value = e;
        }

        if (subCode.value) {
            const element = document.getElementById('component-sub-code-modal');
            if (element) {
                window.HideLoadPanel();
                subCode.value = new window.bootstrap.Modal(element, { backdrop: 'static', keyboard: false });
                subCode.value.show();
            }
        }
    }
</script>