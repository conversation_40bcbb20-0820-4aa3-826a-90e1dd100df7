<template>
    <DxDataGrid
        :data-source="gridDataSource"
        :allow-column-reordering="true"
        :column-hiding-enabled="true"
        :hover-state-enabled="true"
        :show-column-lines="false"
        :remote-operations="true"
        :column-auto-width="true"
        @row-inserting="onRowInserting"
        @editing-start="onEditingStart"
        @init-new-row="initNewRow"
        @editor-preparing="onEditorPreparing"
        @row-updating="(e: DxDataGridTypes.RowUpdatingEvent) => $emit('row-updating', e)"
        @row-removing="(e: DxDataGridTypes.RowRemovingEvent) => $emit('row-removing', e)"
        @cell-prepared="(e: DxDataGridTypes.CellPreparedEvent) => $emit('cell-prepared', e)"
        @row-click="(e: DxDataGridTypes.RowClickEvent) => $emit('row-click', e)">
        <DxGrouping :auto-expand-all="false" />
        <DxFilterRow :visible="true" />
        <DxEditing :allow-updating="allowEdit" :allow-deleting="allowEdit" :allow-adding="allowEdit" mode="form">
            <DxForm :col-count="3" />
        </DxEditing>

        <DxColumn
            data-field="holidayDate"
            :caption="formatMessage('HolidayDate')"
            sort-order="desc"
            data-type="date"
            :sort-index="0"
            :calculate-filter-expression="calculateDateFilterExpressions"
            :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }">
            <DxFormat type="shortDate" />
            <DxRequiredRule />
        </DxColumn>

        <DxColumn data-field="holidayDescription" :caption="formatMessage('HolidayDescription')">
            <DxRequiredRule />
            <DxStringLengthRule :max="50" :message="formatMessage('errMaxLength50')" />
        </DxColumn>

        <!-- DxLookup's -->
        <DxColumn
            data-field="publicHolidayLevel"
            :caption="formatMessage('PublicHolidayLevel')"
            sort-order="asc"
            editor-type="dxSelectBox"
            :value="selectedLevel"
            :set-cell-value="holidayLevelValueChanged"
            :lookup="{
                dataSource: createStore({
                    key: 'value',
                    loadUrl: `${baseUrl}/public-holiday-level`,
                    ...(isBureau ? { loadParams: { [props.idExpr]: selectedIdentifier } } : {})
                }),
                displayExpr: 'text',
                valueExpr: 'value'
            }">
            <DxRequiredRule />
        </DxColumn>

        <DxColumn
            data-field="publicHolidayProvinces"
            :caption="formatMessage('Province')"
            :cell-template="defaultCellTemplateMapper"
            edit-cell-template="provinceEditor"
            :allow-sorting="false"
            editor-type="dxLookup">
            <DxRequiredRule
                :v-if="selectedLevel === HolidayLevel.Provincial"
                :message="formatMessage('errProvinceRequired')" />
            <DxLookup
                :data-source="
                    createStore({
                        key: 'value',
                        loadUrl: `${baseUrl}/provinces`,
                        loadParams: { iso3DigitCode: selectedCountryCode, ...(!isBureau ? { isBrazil } : {}) }
                    })
                "
                display-expr="text"
                value-expr="value" />
            <DxFormItem :visible="selectedLevel === HolidayLevel.Provincial" />
        </DxColumn>

        <!-- display if it's not bureau -->
        <DxColumn
            v-if="!isBureau"
            data-field="publicHolidayMunicipalities"
            :caption="formatMessage('Municipality')"
            :cell-template="defaultCellTemplateMapper"
            edit-cell-template="municipalityEditor"
            :allow-sorting="false"
            :allow-filtering="false"
            editor-type="dxLookup"
            :visible="isBrazil">
            <DxRequiredRule :v-if="selectedLevel === 4" />
            <DxLookup
                :data-source="createStore({ key: 'value', loadUrl: `${baseUrl}/municipalities` })"
                display-expr="text"
                value-expr="value" />

            <DxFormItem :visible="selectedLevel === HolidayLevel.Municipality" />
        </DxColumn>

        <!-- display if it's not bureau -->
        <DxColumn
            v-if="!isBureau"
            data-field="publicHolidayCategories"
            :caption="formatMessage('Category')"
            :cell-template="defaultCellTemplateMapper"
            edit-cell-template="categoryEditor"
            :allow-sorting="false"
            editor-type="dxLookup">
            <DxRequiredRule :v-if="selectedLevel === 3" />
            <DxLookup
                :data-source="createStore({ key: 'value', loadUrl: `${baseUrl}/categories` })"
                display-expr="text"
                value-expr="value" />
            <DxFormItem :visible="selectedLevel === HolidayLevel.Categorical" />
        </DxColumn>

        <DxColumn data-field="Year" :caption="formatMessage('Year')" :group-index="0" sort-order="desc">
            <DxFormItem :visible="false" />
        </DxColumn>

        <!-- Hidden field to allow filter panel to filter by company holidays -->
        <DxColumn data-field="isCompanyHoliday" :visible="false">
            <DxFormItem :visible="false" />
        </DxColumn>

        <!-- TagBox Templates -->
        <template #provinceEditor="{ data: cellInfo }">
            <DxTagBox
                value-expr="value"
                display-expr="text"
                :data-source="
                    createStore({
                        key: 'value',
                        loadUrl: `${baseUrl}/provinces`,
                        loadParams: { iso3DigitCode: selectedCountryCode, ...(!isBureau ? { isBrazil } : {}) }
                    })
                "
                :show-selection-controls="true"
                :show-clear-button="true"
                :search-enabled="true"
                :value="cellInfo.value"
                :label="formatMessage('Province')"
                :on-value-changed="(e: any) => onTagBoxValueChanged(e.value, cellInfo)" />
        </template>
        <template #categoryEditor="{ data: cellInfo }">
            <DxTagBox
                value-expr="value"
                display-expr="text"
                :data-source="
                    createStore({
                        key: 'value',
                        loadUrl: `${baseUrl}/categories`,
                        loadParams: { companyId: selectedIdentifier }
                    })
                "
                :show-selection-controls="true"
                :show-clear-button="true"
                :search-enabled="true"
                :value="cellInfo.value"
                :label="formatMessage('Category')"
                :on-value-changed="(e: any) => onTagBoxValueChanged(e.value, cellInfo)" />
        </template>
        <template #municipalityEditor="{ data: cellInfo }">
            <DxTagBox
                value-expr="value"
                display-expr="text"
                :data-source="municipalityDataSource"
                :show-selection-controls="true"
                :show-clear-button="true"
                :search-enabled="true"
                :value="cellInfo.value"
                :label="formatMessage('Municipality')"
                :on-value-changed="
                    (e: any) => {
                        onTagBoxValueChanged(e.value, cellInfo);
                    }
                " />
        </template>

        <template #countryTemplate>
            <CountryComponent
                :tax-country-id="selectedIdentifier"
                @country-changed="onCountrySelected"></CountryComponent>
        </template>

        <DxToolbar>
            <DxItem name="addRowButton"></DxItem>
            <DxItem location="before" template="countryTemplate" caption="Tax Country" v-if="isBureau" />
        </DxToolbar>

        <DxPaging :page-size="20" />
    </DxDataGrid>
</template>

<script setup lang="ts">
    import CountryComponent from "@/components/country-component.ce.vue";
    import dataGridFactory from "@/datagrid-extensions";
    import { HolidayLevel } from "@/enums";
    import { calculateDateFilterExpression } from "@/helpers/date-filter-helper";
    import { useLocalization } from "@/localization";
    import { createStore } from "devextreme-aspnet-data-nojquery";
    import { DxLookupTypes } from "devextreme-vue/cjs/lookup";
    import {
        DxColumn,
        DxDataGrid,
        DxDataGridTypes,
        DxEditing,
        DxFilterRow,
        DxForm,
        DxFormat,
        DxFormItem,
        DxGrouping,
        DxItem,
        DxLookup,
        DxPaging,
        DxToolbar
    } from "devextreme-vue/data-grid";
    import { DxRequiredRule } from "devextreme-vue/form";
    import DxTagBox from "devextreme-vue/tag-box";
    import { DxStringLengthRule } from "devextreme-vue/validator";
    import DataSource from "devextreme/data/data_source";
    import { formatMessage } from "devextreme/localization";
    import Lookup from "devextreme/ui/lookup";
    import { computed, ref } from "vue";

    const props = defineProps({
        baseUrl: { type: String, required: true },
        keyExpr: { type: String, required: true },
        idExpr: { type: String, required: true },
        countryCode: { type: String, required: true },
        area: { type: String, required: true },
        //Identifier is an alias for either taxCountryId or CompanyId.
        identifier: { type: Number, required: true },
        allowEdit: { type: Boolean, required: true },
        isBureau: Boolean,
        isBrazil: Boolean
    });

    defineEmits<{
        (e: "row-click", event: DxDataGridTypes.RowClickEvent): void;
        (e: "row-updating", event: DxDataGridTypes.RowUpdatingEvent): void;
        (e: "row-removing", event: DxDataGridTypes.RowRemovingEvent): void;
        (e: "cell-prepared", event: DxDataGridTypes.CellPreparedEvent): void;
    }>();

    await useLocalization(props.area);
    Lookup.defaultOptions({
        options: {
            showClearButton: false
        }
    });

    // default the selected holiday level to National: 1
    const selectedLevel = ref<number>(1);
    const selectedIdentifier = ref<number>(props.identifier);
    const selectedCountryCode = ref<string>(props.countryCode);
    const publicHolidayProvinces = ref<Array<string>>([]);
    const publicHolidayCategories = ref<Array<string>>([]);

    /** Data Sources **/
    const gridDataSource = computed(
        () =>
            new DataSource({
                store: createStore({
                    key: props.keyExpr,
                    loadUrl: props.baseUrl,
                    insertUrl: props.baseUrl,
                    updateUrl: props.baseUrl,
                    deleteUrl: props.baseUrl,
                    loadParams: {
                        [props.idExpr]: selectedIdentifier.value,
                        publicHolidayProvinces: publicHolidayProvinces.value,
                        publicHolidayCategories: publicHolidayCategories.value
                    }
                })
            })
    );

    const municipalityDataSource = props.isBureau
        ? null
        : new DataSource({
              store: createStore({
                  key: "value",
                  loadUrl: `${props.baseUrl}/municipalities`
              }),
              paginate: true,
              pageSize: 20
          });

    function calculateDateFilterExpressions(filterValue: any, selectedFilterOperation: string) {
        return calculateDateFilterExpression(filterValue, selectedFilterOperation, "holidayDate");
    }

    function holidayLevelValueChanged(this: { defaultSetCellValue: Function }, newData: object, value: number) {
        // used to hide/display the lookups depending on holiday level selected
        selectedLevel.value = value;
        this.defaultSetCellValue(newData, value);
    }

    function defaultCellTemplateMapper(container: any, e: any) {
        // map the selected dropdown-tags into a text such that it will display on the relevant grid-column
        const text = (e.value || []).map((province: any) => e.column.lookup.calculateCellValue(province)).join(", ");
        container.textContent = text;
        container.title = text;
    }

    function onCountrySelected(selectedItem: { value: number; iso3code: string }) {
        if (selectedItem && selectedItem.value) {
            // update the countryId so the grid data-source can refresh to show the correct countries data
            selectedIdentifier.value = selectedItem.value;

            // update the Province list to match the selected country
            selectedCountryCode.value = selectedItem.iso3code;
        }
    }

    function onTagBoxValueChanged(value: Array<number>, cellInfo: DxDataGridTypes.ColumnEditCellTemplateData) {
        cellInfo.setValue(value);
    }

    /** Grid Event Handlers **/
    function initNewRow(e: { data: any }) {
        selectedLevel.value = 1;
        e.data[props.idExpr] = selectedIdentifier.value;
        e.data.publicHolidayLevel = selectedLevel.value;
    }

    function onEditingStart(e: { key: number; data: any }) {
        selectedLevel.value = e.data.publicHolidayLevel;
    }

    function onRowInserting(e: any) {
        dataGridFactory.onRowInserting(e);
    }

    function onEditorPreparing(e: DxDataGridTypes.EditorPreparingEvent) {
        if (e.dataField === "publicHolidayProvinces" && e.parentType === "filterRow") {
            e.editorOptions.value = publicHolidayProvinces.value[0];
            e.editorOptions.showClearButton = true;
            e.editorOptions.onValueChanged = (event: DxLookupTypes.ValueChangedEvent) => {
                publicHolidayProvinces.value = [event.value];
            };
        }

        if (e.dataField === "publicHolidayCategories" && e.parentType === "filterRow") {
            e.editorOptions.value = publicHolidayCategories.value[0];
            e.editorOptions.showClearButton = true;
            e.editorOptions.onValueChanged = (event: DxLookupTypes.ValueChangedEvent) => {
                publicHolidayCategories.value = [event.value];
            };
        }
    }
</script>
