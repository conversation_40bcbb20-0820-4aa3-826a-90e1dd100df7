<template>
    <DxForm
        ref="formRef"
        v-model:form-data="formData"
        label-location="top"
        :col-count="1"
        :show-colon-after-label="true">
        <!-- Accommodation Details section -->
        <DxGroupItem :caption="formatMessage('lblAccommodationDetails')" :col-count="3">
            <DxSimpleItem
                v-bind="metadata.attr('TaxYear')"
                editor-type="dxSelectBox"
                :editor-options="taxYearOptions" />
            <DxSimpleItem
                v-bind="metadata.attr('YearEndAccommodationType')"
                editor-type="dxSelectBox"
                :editor-options="accommodationOptions">
                <DxRequiredRule />
            </DxSimpleItem>
        </DxGroupItem>

        <!-- Place of Residence provided by Employer section -->
        <DxGroupItem
            :caption="formatMessage('lblPlaceOfResidenceProvidedByEmployer')"
            :col-count="3"
            :visible="isResidenceSection">
            <DxSimpleItem
                v-bind="metadata.attr('AddressLine1')"
                editor-type="dxTextBox"
                :editor-options="{ maxLength: 30 }">
                <DxStringLengthRule :max="30" :message="formatMessage('errAddressLine1Length')" />
            </DxSimpleItem>
            <DxSimpleItem
                v-bind="metadata.attr('AddressLine2')"
                editor-type="dxTextBox"
                :editor-options="{ maxLength: 30 }">
                <DxStringLengthRule :max="30" :message="formatMessage('errAddressLine2Length')" />
            </DxSimpleItem>
            <DxSimpleItem
                v-bind="metadata.attr('AddressLine3')"
                editor-type="dxTextBox"
                :editor-options="{ maxLength: 30 }">
                <DxStringLengthRule :max="30" :message="formatMessage('errAddressLine3Length')" />
            </DxSimpleItem>
            <DxSimpleItem
                v-bind="metadata.attr('OccupationPeriodStartDate')"
                editor-type="dxDateBox"
                :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }"></DxSimpleItem>
            <DxSimpleItem
                v-bind="metadata.attr('OccupationPeriodEndDate')"
                editor-type="dxDateBox"
                :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }">
                <DxCustomRule
                    :validation-callback="validateEndDate"
                    :message="formatMessage('errOccupationPeriodEndDateBeforeStart')" />
            </DxSimpleItem>
            <DxSimpleItem
                v-bind="metadata.attr('NumberOfDays')"
                editor-type="dxNumberBox"
                :editor-options="{ readOnly: true }" />
            <DxSimpleItem
                v-bind="metadata.attr('NumberOfEmployeesSharing')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }">
                <DxRangeRule :max="99" :message="formatMessage('errNumberOfEmployeesSharing')" />
            </DxSimpleItem>
        </DxGroupItem>
        <!-- Accommodation and related benefits section -->
        <DxGroupItem
            :caption="formatMessage('lblAccomodationAndRelatedBenefitsProvidedByEmployer')"
            :col-count="3"
            :visible="isResidenceSection">
            <DxSimpleItem
                v-bind="metadata.attr('AnnualValuePremises')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }" />
            <DxSimpleItem
                v-bind="metadata.attr('FurnitureFittingOption')"
                editor-type="dxSelectBox"
                :editor-options="furnitureOptionsEnum"
                :is-required="isFurnitureOptionVisible" />
            <DxSimpleItem
                v-bind="metadata.attr('ValueFurnitureFitting')"
                editor-type="dxNumberBox"
                :editor-options="{ readOnly: true }" />
            <DxSimpleItem
                v-bind="metadata.attr('RentPaidToLandlord')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }"
                :is-required="isRentPaidToLandlordVisible" />
            <DxSimpleItem
                v-bind="metadata.attr('TaxableValuePlaceOfResidence')"
                editor-type="dxNumberBox"
                :editor-options="{ readOnly: true }" />
            <DxSimpleItem
                v-bind="metadata.attr('RentPaidByEmployee')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }" />
            <DxSimpleItem
                v-bind="metadata.attr('TotalTaxableValuePlaceOfResidence')"
                editor-type="dxNumberBox"
                :editor-options="{ readOnly: true }" />
            <DxSimpleItem
                v-bind="metadata.attr('UtilitiesCosts')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }" />
            <DxSimpleItem
                v-bind="metadata.attr('DriverCosts')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }" />
            <DxSimpleItem
                v-bind="metadata.attr('ServantCosts')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }" />
            <DxSimpleItem
                v-bind="metadata.attr('TaxableValueUtilitiesHousekeeping')"
                editor-type="dxNumberBox"
                :editor-options="{ readOnly: true }" />
        </DxGroupItem>
        <!-- Hotel Accommodation -->
        <DxGroupItem :caption="formatMessage('lblHotelAccommodationProvided')" :col-count="3" :visible="isHotelSection">
            <DxSimpleItem
                v-bind="metadata.attr('CostHotelAccommodation')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }" />
            <DxSimpleItem
                v-bind="metadata.attr('HotelAmountPaidByEmployee')"
                editor-type="dxNumberBox"
                :editor-options="{ min: 0 }" />
            <DxSimpleItem
                v-bind="metadata.attr('TaxableValueHotelAccommodation')"
                editor-type="dxNumberBox"
                :editor-options="{ readOnly: true }" />
        </DxGroupItem>
    </DxForm>
    <PageFooter>
        <DxButton text="Cancel" type="default" styling-mode="outlined" @click="onCancel" />
        <DxButton
            v-if="!metadata.isReadOnly() && allowEditForTaxYear"
            :text="formatMessage('lblSave')"
            :disabled="isSaving"
            @click="onSubmit"
            type="default" />
    </PageFooter>
</template>

<script setup lang="ts">
    import { computed, ref, watch } from "vue";

    import DxButton from "devextreme-vue/button";
    import { DxForm, DxGroupItem, DxSimpleItem } from "devextreme-vue/form";
    import { DxCustomRule, DxRangeRule, DxRequiredRule, DxStringLengthRule } from "devextreme-vue/validator";

    import PageFooter from "@/components/ui/page-footer.vue";
    import { useLocalization } from "@/localization";
    import { useMetadata } from "@/metadata";
    import { odataFactory } from "@/odata-extensions";
    import { ODataFormService } from "@/services/odata-form-service";
    import { formatMessage } from "devextreme/localization";

    interface EmployeeAppendix8AFormData {
        EmployeeAppendix8AId?: number;
        EmployeeId: number;
        CompanyId: number;
        TaxYear: string;
        TaxYearId: number;
        YearEndAccommodationType: string;
        OccupationPeriodStartDate?: Date | string;
        OccupationPeriodEndDate?: Date | string;
        NumberOfDays?: number;
        AddressLine1?: string;
        AddressLine2?: string;
        AddressLine3?: string;
        NumberOfEmployeesSharing?: number;
        AnnualValuePremises?: number;
        FurnitureFittingOption?: string; // 'P' or 'F'
        ValueFurnitureFitting?: number;
        RentPaidToLandlord?: number;
        TaxableValuePlaceOfResidence?: number;
        RentPaidByEmployee?: number;
        TotalTaxableValuePlaceOfResidence?: number;
        UtilitiesCosts?: number;
        DriverCosts?: number;
        ServantCosts?: number;
        TaxableValueUtilitiesHousekeeping?: number;
        CostHotelAccommodation?: number;
        HotelAmountPaidByEmployee?: number;
        TaxableValueHotelAccommodation?: number;
        TotalAccommodationBenefit?: number;
    }

    function validateEndDate(e: any) {
        if (!formData.value?.OccupationPeriodStartDate || !e.value) return true;
        return new Date(e.value) >= new Date(formData.value.OccupationPeriodStartDate);
    }

    const props = defineProps({
        employeeAppendix8aId: { type: Number, default: null },
        taxYear: { type: String, required: true },
        taxYearId: { type: Number, required: true },
        companyId: { type: Number, required: true },
        employeeId: { type: Number, required: true },
        apiBaseUrl: { type: String, required: true },
        returnUrl: { type: String, required: true },
        copy: { type: Boolean, default: false },
        allowNewResidenceProvidedByEmployerRecord: { type: Boolean, required: true },
        allowNewHotelAccomodationRecord: { type: Boolean, required: true },
        allowEditForTaxYear: { type: Boolean, required: true }
    });

    const { EmployeeAppendix8ADto: metadata } = await useMetadata("EmployeeAppendix8ADto");

    await useLocalization("General", "YearEndReporting", "System.Notification");

    const isNewEntry = !(props.employeeAppendix8aId && props.employeeAppendix8aId > 0);

    const furnitureOptionsEnum = {
        dataSource: odataFactory.createLookupStore("FurnitureFittingOption", "Value"),
        displayExpr: "Description",
        valueExpr: "Value"
    };

    enum YearEndAccommadationType {
        ResidenceProvidedByEmployer = "ResidenceProvidedByEmployer",
        HotelAccomodation = "HotelAccomodation"
    }

    const accommodationOptions = computed(() => {
        return {
            dataSource: {
                store: odataFactory.createLookupStore("YearEndAccommodationType", "Value"),
                filter: (item: any) => {
                    if (item.Value === YearEndAccommadationType.ResidenceProvidedByEmployer)
                        return props.allowNewResidenceProvidedByEmployerRecord;
                    if (item.Value === YearEndAccommadationType.HotelAccomodation)
                        return props.allowNewHotelAccomodationRecord;
                    return false;
                }
            },
            displayExpr: "Description",
            valueExpr: "Value"
        };
    });

    function getDefaultAccommodationType(): string | null {
        if (props.allowNewResidenceProvidedByEmployerRecord && !props.allowNewHotelAccomodationRecord)
            return YearEndAccommadationType.ResidenceProvidedByEmployer;
        if (!props.allowNewResidenceProvidedByEmployerRecord && props.allowNewHotelAccomodationRecord)
            return YearEndAccommadationType.HotelAccomodation;

        return null;
    }

    const taxYearOptions = {
        dataSource: odataFactory.createLookupStore("TaxYear", "Value"),
        displayExpr: "Description",
        valueExpr: "Value",
        disabled: true,
        readonly: true,
        value: props.taxYear
    };

    const formRef = ref();
    const isSaving = ref(false);

    const formDefaults: EmployeeAppendix8AFormData = {
        EmployeeId: props.employeeId,
        CompanyId: props.companyId,
        TaxYear: props.taxYear,
        TaxYearId: props.taxYearId,
        ...(props.employeeAppendix8aId && { EmployeeAppendix8AId: props.employeeAppendix8aId })
    };

    const formData = ref<EmployeeAppendix8AFormData>();

    const oDataFormService = new ODataFormService("EmployeeAppendix8A");

    formData.value = isNewEntry
        ? { ...formDefaults }
        : await oDataFormService.getDataAsync(props.employeeAppendix8aId, false);

    const isEditMode = computed(() => !isNewEntry);

    if (isNewEntry && !formData.value?.YearEndAccommodationType) {
        formData.value.YearEndAccommodationType = getDefaultAccommodationType();
    }

    // Clean reset using existing formDefaults pattern
    const resetToDefaults = (): void => {
        if (!formData.value) return;

        // Reset to clean state: keep core fields, clear all accommodation-specific fields
        const cleanState: EmployeeAppendix8AFormData = {
            ...formDefaults,
            YearEndAccommodationType: formData.value.YearEndAccommodationType
        };

        formData.value = { ...cleanState };
    };

    watch(
        () => formData.value?.YearEndAccommodationType,
        (newType: number | null, oldType: number | null) => {
            // Only reset when actually switching types (not on initial load)
            if (!oldType || newType === oldType) return;

            // Reset all accommodation fields to clean state, preserving current type selection
            resetToDefaults();

            calculateTaxableValues();
        }
    );
    const isResidenceSection = computed(
        () => formData.value?.YearEndAccommodationType === YearEndAccommadationType.ResidenceProvidedByEmployer
    );
    const isHotelSection = computed(
        () => formData.value?.YearEndAccommodationType === YearEndAccommadationType.HotelAccomodation
    );

    const isFurnitureOptionVisible = computed(
        () => !!formData.value?.AnnualValuePremises && formData.value.AnnualValuePremises > 0
    );
    const isRentPaidToLandlordVisible = computed(
        () => !formData.value?.AnnualValuePremises || formData.value.AnnualValuePremises <= 0
    );

    watch(
        [() => formData.value?.OccupationPeriodStartDate, () => formData.value?.OccupationPeriodEndDate],
        ([start, end]) => {
            if (start && end) {
                const startDate = new Date(start);
                const endDate = new Date(end);

                if (startDate <= endDate) {
                    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
                    formData.value.NumberOfDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                } else {
                    formData.value.NumberOfDays = undefined;
                }
            } else {
                formData.value.NumberOfDays = undefined;
            }
        }
    );

    function calcFurnitureValue(av?: number, option?: string) {
        if (!av || av <= 0 || !option) return undefined;

        let perc = 0;
        if (option === "P") perc = 0.4;
        else if (option === "F") perc = 0.5;

        return Math.round(av * perc * 100) / 100;
    }

    watch([() => formData.value?.AnnualValuePremises, () => formData.value?.FurnitureFittingOption], ([av, option]) => {
        if (!formData.value) return;

        formData.value.ValueFurnitureFitting = calcFurnitureValue(av, option);
        calculateTaxableValues();
    });

    // Auto-clear conflicting fields to prevent mutual exclusivity validation errors
    watch(
        () => formData.value?.AnnualValuePremises,
        newValue => {
            // If user enters Annual Value, clear Rent Paid to Landlord
            if (newValue && newValue > 0 && formData.value) {
                // Use null to ensure field is explicitly cleared in backend payload
                formData.value.RentPaidToLandlord = null;
            }
        }
    );

    watch(
        () => formData.value?.RentPaidToLandlord,
        newValue => {
            // If user enters Rent Paid to Landlord, clear Annual Value and related fields
            if (newValue && newValue > 0 && formData.value?.AnnualValuePremises && formData.value) {
                // Use null to ensure fields are explicitly cleared in payload
                formData.value.AnnualValuePremises = null;
                formData.value.FurnitureFittingOption = null;
                formData.value.ValueFurnitureFitting = null;
            }
        }
    );

    watch(
        () => [
            formData.value?.RentPaidToLandlord,
            formData.value?.RentPaidByEmployee,
            formData.value?.UtilitiesCosts,
            formData.value?.DriverCosts,
            formData.value?.ServantCosts,
            formData.value?.CostHotelAccommodation,
            formData.value?.HotelAmountPaidByEmployee,
            formData.value?.ValueFurnitureFitting
        ],
        calculateTaxableValues
    );

    function calculateTaxableValues() {
        if (!formData.value) return;

        const av = formData.value.AnnualValuePremises ?? 0;
        const vff = formData.value.ValueFurnitureFitting ?? 0;
        const rpll = formData.value.RentPaidToLandlord ?? 0;
        formData.value.TaxableValuePlaceOfResidence = (av > 0 ? av + vff : 0) + (av <= 0 ? rpll : 0);

        const tvpr = formData.value.TaxableValuePlaceOfResidence ?? 0;
        const rpee = formData.value.RentPaidByEmployee ?? 0;
        formData.value.TotalTaxableValuePlaceOfResidence = Math.max(0, tvpr - rpee);

        const util = formData.value.UtilitiesCosts ?? 0;
        const driver = formData.value.DriverCosts ?? 0;
        const servant = formData.value.ServantCosts ?? 0;
        formData.value.TaxableValueUtilitiesHousekeeping = util + driver + servant;

        const costHotel = formData.value.CostHotelAccommodation ?? 0;
        const hotelPaidByEE = formData.value.HotelAmountPaidByEmployee ?? 0;
        formData.value.TaxableValueHotelAccommodation = Math.max(0, costHotel - hotelPaidByEE);

        if (
            formData.value.TotalTaxableValuePlaceOfResidence !== null &&
            formData.value.TaxableValueUtilitiesHousekeeping !== null &&
            formData.value.TaxableValueHotelAccommodation !== null
        ) {
            formData.value.TotalAccommodationBenefit =
                (formData.value.TotalTaxableValuePlaceOfResidence || 0) +
                (formData.value.TaxableValueUtilitiesHousekeeping || 0) +
                (formData.value.TaxableValueHotelAccommodation || 0);
        }
    }

    async function onSubmit() {
        if (isSaving.value) return; // Prevent double clicks

        isSaving.value = true; // Disable save button

        try {
            const form = formRef.value?.instance;
            const { isValid, brokenRules = [] } = form?.validate() || {};

            if (!isValid) {
                const message = brokenRules.map((r: any) => r.message).join("\n") || "Validation failed";
                Notifications.showErrors(message, window.Route.NotificationId);
                return;
            }

            const id = props.employeeAppendix8aId ? Number(props.employeeAppendix8aId) : null;
            const data = formData.value;

            let ok: boolean;
            if (!isNewEntry && !props.copy) {
                ok = await oDataFormService.putDataAsync(id!, data);
            } else {
                if (props.copy) {
                    data.EmployeeAppendix8AId = undefined;
                }
                ok = await oDataFormService.postDataAsync(data);
            }

            if (ok) {
                window.location.href = props.returnUrl;
            }
        } finally {
            isSaving.value = false; // Re-enable save button
        }
    }

    function onCancel() {
        window.location.href = props.returnUrl;
    }
</script>