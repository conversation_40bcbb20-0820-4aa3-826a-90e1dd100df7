<template>
    <Card>
        <AlertMessage :message="formatMessage('maxRecordMessage')" variant="warning" v-show="!canAddNewRecord" />
        <DxDataGrid
            :data-source="dataSource"
            :key-expr="primaryKey"
            :word-wrap-enabled="true"
            @editing-start="onEditingStart"
            @editor-preparing="onEditorPreparing"
            @row-removed="dataGridFactory.onRowRemoved">
            <DxEditing
                :allow-deleting="!metadata.isReadOnly() && allowEditForTaxYear"
                :allow-updating="!metadata.isReadOnly()" />

            <DxFilterRow :visible="true" />
            <DxColumn v-bind="metadata.attr('TaxYear')" v-model:filter-value="selectedTaxYear" data-type="string">
                <DxLookup :data-source="taxYearDataSource" displayExpr="text" valueExpr="text"></DxLookup>
            </DxColumn>

            <DxColumn v-bind="metadata.attr('OccupationPeriodStartDate')" />
            <DxColumn v-bind="metadata.attr('OccupationPeriodEndDate')" />
            <DxColumn v-bind="metadata.attr('NumberOfDays')" />
            <DxColumn v-bind="metadata.attr('AccommodationNumber')" alignment="center" />
            <DxColumn v-bind="metadata.attr('TotalTaxableValuePlaceOfResidence')" />
            <DxColumn v-bind="metadata.attr('TaxableValueUtilitiesHousekeeping')" />
            <DxColumn v-bind="metadata.attr('TaxableValueHotelAccommodation')" />
            <DxColumn v-bind="metadata.attr('TotalAccommodationBenefit')" />

            <DxColumn type="buttons">
                <DxGridButton :disabled="e => metadata.isReadOnly() || !allowEditForTaxYear || isCopyDisabled(e.row.data)"
                              icon="copy"
                              @click="copyRow" />
                <DxGridButton name="edit" :disabled="!allowEditForTaxYear" />
                <DxGridButton name="delete" :disabled="!allowEditForTaxYear" />
            </DxColumn>

            <DxSummary :calculate-custom-summary="calculateSummary">
                <DxTotalItem
                    column="TotalTaxableValuePlaceOfResidence"
                    summary-type="sum"
                    value-format="#,##0.00"
                    display-format="{0}"
                    :show-in-column="'TotalTaxableValuePlaceOfResidence'" />
                <DxTotalItem
                    column="TaxableValueUtilitiesHousekeeping"
                    summary-type="sum"
                    value-format="#,##0.00"
                    display-format="{0}"
                    :show-in-column="'TaxableValueUtilitiesHousekeeping'" />
                <DxTotalItem
                    column="TaxableValueHotelAccommodation"
                    summary-type="sum"
                    value-format="#,##0.00"
                    display-format="{0}"
                    :show-in-column="'TaxableValueHotelAccommodation'" />
                <DxTotalItem
                    column="TotalAccommodationBenefit"
                    summary-type="sum"
                    value-format="#,##0.00"
                    display-format="{0}"
                    :show-in-column="'TotalAccommodationBenefit'" />
                <DxTotalItem
                    name="TotalLabel"
                    summary-type="custom"
                    display-format="Total"
                    :show-in-column="'OccupationPeriodStartDate'" />
            </DxSummary>
            <DxToolbar>
                <DxItem location="after">
                    <DxButton
                        v-if="!metadata.isReadOnly()"
                        :disabled="!canAddNewRecord"
                        icon="add"
                        @click="onAddClick"
                        stylingMode="contained"
                        type="default" />
                </DxItem>
            </DxToolbar>
        </DxDataGrid>
    </Card>
</template>

<script setup lang="ts">
    import type { PropType } from "vue";
    import { ref, watch } from "vue";

    import AlertMessage from "@/components/alert-message.vue";
    import Card from "@/components/ui/card.vue";
    import dataGridFactory from "@/datagrid-extensions";
    import { IMetadata } from "@/metadata";
    import { odataFactory } from "@/odata-extensions";
    import { DxButton } from "devextreme-vue/button";
    import {
        DxColumn,
        DxDataGrid,
        DxDataGridTypes,
        DxEditing,
        DxFilterRow,
        DxButton as DxGridButton,
        DxItem,
        DxLookup,
        DxSummary,
        DxToolbar,
        DxTotalItem
    } from "devextreme-vue/data-grid";
    import DataSource from "devextreme/data/data_source";
    import { formatMessage } from "devextreme/localization";

    const emit = defineEmits<(e: "taxYearChanged", year: string) => void>();

    const props = defineProps({
        taxYearId: { type: Number, required: true },
        taxYear: { type: String, required: true },
        companyId: { type: Number, required: true },
        allowNewAppendix8aRecord: { type: Boolean, required: true },
        editUrl: { type: String, required: true },
        employeeId: { type: Number, required: true },
        apiUrl: { type: String, required: false },
        metadata: { type: Object as PropType<IMetadata>, required: true },
        allowEditForTaxYear: { type: Boolean, required: true },
        taxYearDataSource: { type: Array, required: true }
    });

    const primaryKey = "EmployeeAppendix8AId";
    const selectedTaxYear = ref(props.taxYear);
    const maximumRecord = 11;
    const maximumResidenceRecord = 10;

    enum YearEndAccommadationType {
        ResidenceProvidedByEmployer = "ResidenceProvidedByEmployer",
        HotelAccomodation = "HotelAccomodation"
    }

    const store = odataFactory.createStore("EmployeeAppendix8A", "EmployeeAppendix8AId");
    const dataSource = new DataSource({
        store
    });

    const canAddNewRecord = ref(props.allowNewAppendix8aRecord);
    store.on("removed", () => (canAddNewRecord.value = true));

    function onAddClick() {
        window.location.href =
            props.editUrl +
            `?TaxYearId=${props.taxYearId}` +
            `&TaxYear=${props.taxYear}`;
    }

    function copyRow(e: DxDataGridTypes.ColumnButtonClickEvent) {
        window.location.href =
            props.editUrl +
            `?TaxYearId=${props.taxYearId}` +
            `&TaxYear=${props.taxYear}` +
            `&EmployeeAppendix8AId=${e.row?.data.EmployeeAppendix8AId}` +
            `&Copy=true`;
    }

    function onEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        e.cancel = true;
        if (props.editUrl) {
            window.location.href =
                props.editUrl +
                `?TaxYearId=${props.taxYearId}` +
                `&TaxYear=${props.taxYear}` +
                `&EmployeeAppendix8AId=${e.key}`;
        }
    }

    function calculateSummary(options: any) {
        if (options.name === "TotalLabel" && options.summaryProcess === "finalize") {
            options.totalValue = "Total";
        }
    }

    // Watch for user changes to the filter value
    watch(selectedTaxYear, newTaxYear => {
        if (newTaxYear && newTaxYear !== props.taxYear) {
            emit("taxYearChanged", newTaxYear);
            store.totalCount({ filter: ["TaxYear", newTaxYear] }).then(result => (canAddNewRecord.value = result < maximumRecord));
        }
    });

    watch(
        () => props.taxYear,
        newTaxYear => {
            if (selectedTaxYear.value !== newTaxYear) {
                selectedTaxYear.value = newTaxYear;
            }
        },
        { immediate: true }
    );

    function onEditorPreparing(e: DxDataGridTypes.EditorPreparingEvent) {
        // https://supportcenter.devexpress.com/ticket/details/t1107347/datagrid-how-to-remove-the-all-item-in-a-lookup-column-s-filter-row-editor
        if (e.parentType === "filterRow" && e.dataField === "TaxYear") {
            // Remove the (All) option on the dropdown filter as user should never see all tax years at once.
            e.editorOptions.dataSource.postProcess = undefined;
        }
    }

    function isCopyDisabled(rowData: any): boolean {
        const accommodationType = rowData?.YearEndAccommodationType;

        if (accommodationType === YearEndAccommadationType.HotelAccomodation) {
            return true;
        }

        if (accommodationType === YearEndAccommadationType.ResidenceProvidedByEmployer) {
            const allData = dataSource.items() || [];
            const residenceRecordsCount = allData.filter(
                (item: any) => item.YearEndAccommodationType === YearEndAccommadationType.ResidenceProvidedByEmployer
            ).length;
            return residenceRecordsCount >= maximumResidenceRecord;
        }

        return false;
    }

</script>
