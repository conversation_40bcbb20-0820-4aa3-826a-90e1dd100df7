<template>
    <DxDataGrid :data-source="dataSource"
                :show-column-lines="false"
                @editorPreparing="onEditorPreparing"
                @contentReady="onFormReady"
                @cell-prepared="(event: CellPreparedEvent) => dataGridFactory.onCellPrepared(event, false)"
                @row-click="(event: RowClickEvent) => dataGridFactory.onRowClick(event, false)"
                @row-inserted="(event: RowInsertedEvent) => dataGridFactory.onRowInserted(event)"
                @row-removed="(event: RowRemovedEvent) => dataGridFactory.onRowRemoved(event)"
                @row-updated="(event: RowUpdatedEvent) => dataGridFactory.onRowUpdated(event)">
        <DxFilterRow :visible="true" />
        <DxEditing :allow-adding="false"
                    :allow-updating="true"
                    :allow-deleting="false"
                    mode="form">
            <DxForm :col-count="3">
                <DxGroupItem :col-count="3" :col-span="3" :caption="formatMessage('TaxationInformation')">
                    <DxSimpleItem data-field="incomeTaxCalculationBasis" />
                    <DxSimpleItem data-field="lptToBeDeducted" />
                </DxGroupItem>
                <DxGroupItem :col-count="3" :col-span="3" :caption="formatMessage('PayeSummary')">
                    <DxSimpleItem data-field="yearlyTaxCredit" />
                    <DxSimpleItem data-field="yearlyRate1CutOff" />
                    <DxSimpleItem data-field="exclusionOrder" editor-type="dxCheckBox"
                        :label="{ visible: false }" :editor-options="{ text: formatMessage('ExclusionOrder') }" />
                    <DxSimpleItem data-field="payForIncomeTaxToDate" />
                    <DxSimpleItem data-field="incomeTaxDeductedToDate" />
                    <DxSimpleItem data-field="taxRate1Percent" />
                    <DxSimpleItem data-field="taxRate2Percent" />
                </DxGroupItem>
                <DxGroupItem :col-count="3" :col-span="3" :caption="formatMessage('UscSummary')">
                    <DxSimpleItem data-field="yearlyUscRate1CutOff" />
                    <DxSimpleItem data-field="yearlyUscRate2CutOff" />
                    <DxSimpleItem data-field="yearlyUscRate3CutOff" />
                    <DxSimpleItem data-field="yearlyUscRate4CutOff" />
                    <DxSimpleItem data-field="payForUscToDate" />
                    <DxSimpleItem data-field="uscStatus" editor-type="dxSelectBox"
                        :label="{ visible: false }"
                        :editor-options="{
                            items: [{ value: true, text: formatMessage('UscStatusOrdinary') }, { value: false, text: formatMessage('UscStatusExempt') }],
                            valueExpr: 'value',
                            displayExpr: 'text',
                            placeholder: '',
                            readOnly: true
                        }" />
                    <DxSimpleItem data-field="uscDeductedToDate" />
                    <DxSimpleItem data-field="uscRate1Percent" />
                    <DxSimpleItem data-field="uscRate2Percent" />
                    <DxSimpleItem data-field="uscRate3Percent" />
                    <DxSimpleItem data-field="uscRate4Percent" />
                </DxGroupItem>
                <DxGroupItem :col-count="3" :col-span="3" :caption="formatMessage('General')">
                    <DxSimpleItem data-field="rpnNumber" />
                    <DxSimpleItem data-field="rpnEmploymentId" />
                    <DxSimpleItem data-field="employerReference" />
                    <DxSimpleItem data-field="employeePpsn" />
                    <DxSimpleItem data-field="previousEmployeePpsn" />
                    <DxSimpleItem data-field="employeeIsExemptFromPrsiInIreland" editor-type="dxCheckBox"
                        :label="{ visible: false }" :editor-options="{ text: formatMessage('EmployeeIsExemptFromPrsiInIreland') }" />
                    <DxSimpleItem data-field="rpnIssueDate" />
                    <DxSimpleItem data-field="rpnFirstName" />
                    <DxSimpleItem data-field="rpnFamilyName" />
                    <DxSimpleItem data-field="rpnEffectiveDate" />
                    <DxSimpleItem data-field="endDate" />
                    <DxSimpleItem data-field="prsiClassAndSubclass" />
                </DxGroupItem>
                <DxSimpleItem data-field="statePensionContribution" editor-type="dxCheckBox"
                    :label="{ visible: false }" :editor-options="{ text: formatMessage('StatePensionContribution') }" />
            </DxForm>
        </DxEditing>

        <DxColumn :caption="formatMessage('EffectiveDate')" data-type="date" data-field="effectiveDate" />
        <DxColumn :caption="formatMessage('RpnNumber')" data-field="rpnNumber" />
        <DxColumn :caption="formatMessage('RpnIssueDate')" data-type="date" data-field="rpnIssueDate" />
        <DxColumn :caption="formatMessage('EmployeePpsn')" data-field="employeePpsn" />
        <DxColumn :caption="formatMessage('IncomeTaxCalculationBasis')" data-field="incomeTaxCalculationBasis" />

        <DxColumn :caption="formatMessage('LptToBeDeducted')" data-field="lptToBeDeducted" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('YearlyTaxCredit')" data-field="yearlyTaxCredit" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('YearlyRate1CutOff')" data-field="yearlyRate1CutOff" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('ExclusionOrder')" data-field="exclusionOrder" data-type="boolean" :visible="false" />
        <DxColumn :caption="formatMessage('PayForIncomeTaxToDate')" data-field="payForIncomeTaxToDate" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('IncomeTaxDeductedToDate')" data-field="incomeTaxDeductedToDate" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('YearlyUscRate1CutOff')" data-field="yearlyUscRate1CutOff" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('YearlyUscRate2CutOff')" data-field="yearlyUscRate2CutOff" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('YearlyUscRate3CutOff')" data-field="yearlyUscRate3CutOff" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('YearlyUscRate4CutOff')" data-field="yearlyUscRate4CutOff" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('PayForUscToDate')" data-field="payForUscToDate" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('UscDeductedToDate')" data-field="uscDeductedToDate" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('UscStatus')" data-field="uscStatus" data-type="boolean" :visible="false" />
        <DxColumn :caption="formatMessage('RpnEmploymentId')" data-field="rpnEmploymentId" :visible="false" />
        <DxColumn :caption="formatMessage('EmployerReference')" data-field="employerReference" :visible="false" />
        <DxColumn :caption="formatMessage('PreviousEmployeePpsn')" data-field="previousEmployeePpsn" :visible="false" />
        <DxColumn :caption="formatMessage('EmployeeIsExemptFromPrsiInIreland')" data-field="employeeIsExemptFromPrsiInIreland" data-type="boolean" :visible="false" />
        <DxColumn :caption="formatMessage('RpnFirstName')" data-field="rpnFirstName" :visible="false" />
        <DxColumn :caption="formatMessage('RpnFamilyName')" data-field="rpnFamilyName" :visible="false" />
        <DxColumn :caption="formatMessage('RpnEffectiveDate')" data-type="date" data-field="rpnEffectiveDate" :visible="false" />
        <DxColumn :caption="formatMessage('EndDate')" data-type="date" data-field="endDate" :visible="false" />
        <DxColumn :caption="formatMessage('PrsiClassAndSubclass')" data-field="prsiClassAndSubclass" :visible="false" />
        <DxColumn :caption="formatMessage('TaxRate1Percent')" data-field="taxRate1Percent" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('TaxRate2Percent')" data-field="taxRate2Percent" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('UscRate1Percent')" data-field="uscRate1Percent" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('UscRate2Percent')" data-field="uscRate2Percent" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('UscRate3Percent')" data-field="uscRate3Percent" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('UscRate4Percent')" data-field="uscRate4Percent" data-type="number" :visible="false" />
        <DxColumn :caption="formatMessage('StatePensionContribution')" data-field="statePensionContribution" data-type="boolean" :visible="false" />

        <DxColumn type="buttons">
            <DxButton icon="download" :hint="formatMessage('lblDownloadIconTooltip')" @click="onDownloadClick" />
            <DxButton name="edit" icon="eyeopen" :hint="formatMessage('lblViewIconTooltip')" />
        </DxColumn>
        <DxPaging :page-size="10" />
    </DxDataGrid>
</template>

<script lang="ts" setup>
import dataGridFactory from "@/datagrid-extensions";
import { IntegrationHttpClient } from "@/http-client";
import integrationFactory from "@/integration-extensions";
import { useLocalization } from "@/localization";
import {
    DxButton,
    DxColumn,
    DxDataGrid,
    DxEditing,
    DxFilterRow,
    DxForm,
    DxPaging
} from "devextreme-vue/data-grid";
import { DxGroupItem, DxSimpleItem } from "devextreme-vue/form";
import DataSource from "devextreme/data/data_source";
import { formatMessage } from "devextreme/localization";
import {
    CellPreparedEvent,
    ColumnButtonClickEvent,
    EditorPreparingEvent,
    RowClickEvent,
    RowInsertedEvent,
    RowRemovedEvent,
    RowUpdatedEvent
} from "devextreme/ui/data_grid";
import { ContentReadyEvent } from "devextreme/ui/form";
import { saveAs } from "file-saver";
import JSZip from "jszip";

type GetRpnAttachmentsResponse = {
    files: RpnFileInfo[];
}

type RpnFileInfo = {
    blobStorageUrl: string;
    fileName: string;
    extension: string;
};

const props = defineProps({
    systemArea: { type: String, required: true },
    companyId: { type: Number, required: true },
    employeeId: { type: Number, required: true }
});

await useLocalization(props.systemArea);

const httpClient = new IntegrationHttpClient();
const dataSource = new DataSource({
    store: integrationFactory.createStore({
        key: "id",
        loadUrl: `/company/${props.companyId}/rpn/employee/${props.employeeId}`
    })
});

function onEditorPreparing(e: EditorPreparingEvent): void {
    if (e.parentType === "dataRow" && e.dataField) {
        e.editorOptions.disabled = true;
    }
}

function onFormReady(e: ContentReadyEvent) {
    // first button in the container is the Save button
    const saveBtn = e.element.querySelector('.dx-datagrid-form-buttons-container .dx-button');
    if (saveBtn instanceof HTMLElement) {
        saveBtn.style.display = 'none';
    }
}

async function onDownloadClick(e: ColumnButtonClickEvent) {
    const requestId = e.row?.key;

    try {
        const result: GetRpnAttachmentsResponse = await httpClient.get(`/company/${props.companyId}/rpn/${requestId}/attachments`);
        const blobs = result.files;

        // download URLs retuned from the attachments service and zip them
        const zip = new JSZip();
        for (const blob of blobs) {
            zip.file(blob.fileName + "." + blob.extension, getBlob(blob.blobStorageUrl));
        }

        // generate the zip file and trigger download
        const content = await zip.generateAsync({ type: "blob" });
        saveAs(content, "rpn.zip");
    }
    catch (error) {
        Notifications.showErrors("Error downloading attachments");
    }
}

async function getBlob(blobUri: string) {
    const response = await fetch(blobUri);
    return await response.blob();
}

</script>