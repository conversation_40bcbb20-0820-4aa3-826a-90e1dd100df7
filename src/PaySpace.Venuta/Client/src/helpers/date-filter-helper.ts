import { DxDataGridTypes } from "devextreme-vue/cjs/data-grid";
import { EdmLiteral } from "devextreme/data/odata/utils";
import { formatDate } from "devextreme/localization";

type ODataDateFilterValue = (Date | string) | [Date, Date] | [string, string];

// https://supportcenter.devexpress.com/ticket/details/t939530/filter-builder-and-date-field#:~:text=If%20you%20need%20to%20serialize,ss%22%20as%20described%20at%20dateSerializationFormat
export function calculateDateFilterExpression(filterValue: any, selectedFilterOperation: string, dataField: string) {
    let filterExpression: any = [];
    if (selectedFilterOperation === "between" && Array.isArray(filterValue)) {
        const startDate = formatDate(new Date(filterValue[0]), "yyyy/MM/dd");
        const endDate = formatDate(new Date(filterValue[1]), "yyyy/MM/dd");
        filterExpression = [[dataField, ">=", startDate], "and", [dataField, "<=", endDate]];
    } else {
        const date = formatDate(new Date(filterValue), "yyyy/MM/dd");
        filterExpression = [[dataField, selectedFilterOperation || "=", date]];
    }

    return filterExpression;
}

export function calculateDateTimeFilterExpression(filterValue: any, dataField: string, selectedFilterOperation?: string) {
    if (selectedFilterOperation === "between" && Array.isArray(filterValue)) {
        // For date range, use exact datetime values
        const startDate = new Date(filterValue[0]).toISOString();
        const endDate = new Date(filterValue[1]).toISOString();
        return [[dataField, ">=", startDate], "and", [dataField, "<=", endDate]];
    } else if (selectedFilterOperation && selectedFilterOperation !== "=") {
        // For other operations (>, <, >=, <=), use exact datetime
        const exactDateTime = new Date(filterValue).toISOString();
        return [[dataField, selectedFilterOperation, exactDateTime]];
    } else {
        // For equals or no operation specified, use day range (default behavior)
        const inputDate = new Date(filterValue);

        const startOfDay = new Date(inputDate);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(inputDate);
        endOfDay.setHours(23, 59, 59, 999);

        return [
            [dataField, ">=", startOfDay.toISOString()],
            "and",
            [dataField, "<=", endOfDay.toISOString()]
        ];
    }
}

export function calculateODataDateFilterExpression(
    this: DxDataGridTypes.Column,
    filterValue: ODataDateFilterValue,
    selectedFilterOperation: string | null,
    target: string
) {
    if (selectedFilterOperation === "between" && Array.isArray(filterValue)) {
        const startDate = new EdmLiteral(formatDate(new Date(filterValue[0]), "yyyy-MM-dd"));
        const endDate = new EdmLiteral(formatDate(new Date(filterValue[1]), "yyyy-MM-dd"));

        return [[`date(${this.dataField})`, ">=", startDate], "and", [`date(${this.dataField})`, "<=", endDate]];
    }

    return [
        `date(${this.dataField})`,
        selectedFilterOperation,
        new EdmLiteral(formatDate(new Date(filterValue as Date | string), "yyyy-MM-dd"))
    ];
}