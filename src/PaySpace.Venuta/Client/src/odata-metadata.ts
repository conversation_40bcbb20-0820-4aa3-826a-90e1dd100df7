import { ExternalHttpClient } from "@/http-client";
import odataFactory from "@/odata-extensions";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edm<PERSON><PERSON><PERSON>, IEdmProperty, MetadataParser } from "@nextgen/nextgen-metadata";
import { EdmLiteral } from "devextreme/data/odata/utils";
import { EditorPreparingEvent } from "devextreme/ui/data_grid";
import dxForm, { SimpleItem } from "devextreme/ui/form";
import { ExtendEdmPropertyFormatter, IExtendedEdmProperty } from "./services/advanced-settings/extend-edm-property-formatter";
import session from './session';

export default class DxFormEdmParser extends EdmParser {
    private metadata?: EdmModel;

    constructor() {
        super({
            formatters: [
                new ExtendEdmPropertyFormatter()
            ]
        });
    }

    public async FetchAndParse() {
        const meta = await new MetadataParser().fetch(new ExternalHttpClient().getUrl(`{companyId}/$metadata`), session.AccessToken);
        this.metadata = this.parse(meta);
    }

    public async GetFormItems(entity: string, isEdit: boolean, entityPrefix: string = '') {
        const model = this.metadata?.Entities[entity];

        if (model == null) {
            return null;
        }

        return model.filter((property: IEdmProperty) => property.Name != property.EdmEntity?.Key && property.EditorType)
            .map((property: IEdmProperty) => {
                const prop = property as IExtendedEdmProperty;
                return {
                    dataField: entityPrefix ? entityPrefix + '.' + property.Name : property.Name,
                    label: { text: property.Caption },
                    editorType: property.EditorType,
                    isRequired: property.Required,
                    editorOptions: this.GetEditorOptions(prop, isEdit, entityPrefix),
                    visibleIndex: prop.Order,
                    visible: prop.Visible
                } as DevExpress.ui.dxForm.Item;
            });
    }

    public async GetColumns(entity: string, entityPrefix: string = '', excludedColumns?: Array<string>) {
        const model = this.metadata?.Entities[entity];

        if (!excludedColumns?.length) {
            excludedColumns = [];
        }

        if (model == null) {
            return null;
        }

        return model.filter((property: IEdmProperty) => property.EditorType
            && excludedColumns.indexOf(entityPrefix ? entityPrefix + '.' + property.Name : property.Name) < 0)
            .map((property: IEdmProperty) => {
                const prop = property as IExtendedEdmProperty;
                let column = {
                    dataField: entityPrefix ? entityPrefix + '.' + property.Name : property.Name,
                    dataType: property.DataType,
                    caption: property.Caption,
                    editorType: property.EditorType,
                    isRequired: property.Required,
                    editorOptions: this.GetEditorOptions(prop, false, entityPrefix),
                    visibleIndex: prop.Order,
                    visible: property.Name == property.EdmEntity?.Key ? false : prop.Visible,
                    sortIndex: prop.SortIndex,
                    sortOrder: prop.SortOrder
                } as DevExpress.ui.dxDataGrid.Column;

                if (column.dataType == "date") {
                    column.calculateFilterExpression = (filterValue, selectedFilterOperation) => {
                        return this.calculateFilterExpression(column.dataField!, filterValue, selectedFilterOperation);
                   }

                   column.filterOperations = <any>["=", "<", ">", "<=", ">=", "between"];
                }

                if (prop.EditorType == "dxLookup")
                {
                    column.lookup = this.GetLookup(prop, false, entityPrefix);
                }

                return column;
            });
    }

    // This is a helpher to support cascading lookups on grids
    public onEditorPreparing(col: EditorPreparingEvent) {
        if (col.parentType !== 'dataRow' || !col.dataField || col.editorName !== 'dxLookup') {
            return;
        }

        if (!col.editorOptions?.parentLookup) // Only parent lookups should force a repaint
        {
            Object.assign(col.editorOptions, {
                onValueChanged: (e: { value: any }) => {
                    col.setValue(e.value);
                    col.component.refresh();
                }
            });

            return;
        }

        const propValue = col.component.cellValue(col.row?.rowIndex ?? 0, col.editorOptions.parentProperty);

        if (propValue) {
            const parentValueLookup = `${col.editorOptions.parentLookup}/${propValue}`;
            Object.assign(col.editorOptions, {
                dataSource: odataFactory.createLookupStore(parentValueLookup, "Value")
            });
        }
    }

    // This is a helpher to support cascading lookups on forms
    public customizeItem(item: SimpleItem, form: dxForm) {
        if (item.editorType !== 'dxLookup') {
            return;
        }

        if (!item.editorOptions?.parentLookup) { // Only parent lookups should force a repaint
            Object.assign(item.editorOptions, {
                onValueChanged: (e: { value: any }) => {
                    form.repaint();
                }
            });

            return;
        }

        const propValue = this.getLeafPropertyValue(form.option('formData'), item.editorOptions.parentProperty);

        if (propValue) {
            const parentValueLookup = `${item.editorOptions.parentLookup}/${propValue}`;
            Object.assign(item.editorOptions, {
                dataSource: odataFactory.createLookupStore(parentValueLookup, "Value")
            });
        }
    }

    // Flattens an object to get the leaf property value defined by a path
    // of dot-delimited property names (e.g. 'Parent.Child.Property')
    public getLeafPropertyValue(obj: any, path: string, delim: string = '.') {
        return path.split(delim).reduce((o, k) => o ? o[k] : o, obj)
    }

    private GetEditorOptions(property: IExtendedEdmProperty, isEdit: boolean, entityPrefix: string) {
        const options = {};

        if (property.EditorType === "dxLookup") {
            Object.assign(options, {
                displayExpr: "Description",
                valueExpr: "Value"
            });

            const entityLookupTokens = property.Lookup.split('/');

            if (entityLookupTokens.length > 1) {
                const parentLookup = entityLookupTokens[0];
                const parentProperty = entityLookupTokens[1].replace('{', '').replace('}', '');

                Object.assign(options, {
                    parentLookup: parentLookup,
                    parentProperty: entityPrefix ? entityPrefix + '.' + parentProperty : parentProperty
                });
            } else {
                Object.assign(options, {
                    dataSource: odataFactory.createLookupStore(property.Lookup, "Value")
                });
            }
        }

        if (property.EditorType === "dxDateBox") {
            Object.assign(options, {
                type: property.DataType,
                dateSerializationFormat: "yyyy/MM/dd",
                showClearButton: !property.Required
            });
        }

        Object.assign(options, {
            disabled: property.ReadOnly || (isEdit && !property.Editable)
        });

        return options;
    }

    private GetLookup(property: IExtendedEdmProperty, isEdit: boolean, entityPrefix: string) {
        if (property.EditorType !== "dxLookup") {
            return undefined;
        }

        return this.GetEditorOptions(property, isEdit, entityPrefix);
    }

    private calculateFilterExpression(dataField: string, filterValue: any, selectedFilterOperation: string | null): any {
        if (selectedFilterOperation === "between" && Array.isArray(filterValue)) {
            const startDate = new EdmLiteral(this.formatDate(filterValue[0]));
            const endDate = new EdmLiteral(this.formatDate(filterValue[1]));

            return [
                ["date(" + dataField + ")", ">=", startDate],
                "and",
                ["date(" + dataField + ")", "<=", endDate]
            ];
        }

        return ["date(" + dataField + ")", selectedFilterOperation, new EdmLiteral(this.formatDate(filterValue))];
    };

    private formatDate(date: string | Date): string {
        return DevExpress.localization.formatDate(new Date(date), "yyyy-MM-dd");
    }
}