import ODataStore from 'devextreme/data/odata/store';
import { ExternalHttpClient } from './http-client';
import session from './session';

class OdataFactory {
    public createStore(entity: string, key: string, getAllPath?: string, onBeforeSend?: Function) {
        let url = new ExternalHttpClient().getUrl(`{companyId}/${entity}`);

        return this.createStoreInternal(url, entity, key, (request: any) => {
            if ((request.method === "get") && (getAllPath)) {
                request.url = new ExternalHttpClient().getUrl(`{companyId}/${entity}/${getAllPath }`);
            }

            if (onBeforeSend)
            {
                onBeforeSend(request);
            }
        });
    }

    public createLookupStore(entity: string, key: string, getAllPath?: string, onBeforeSend?: Function) {
        let url = new ExternalHttpClient().getUrl(`{companyId}/Lookup/${entity}`);
        return this.createStoreInternal(url, entity, key, onBeforeSend);
    }

    public createCountryLookupStore(entity: string, key: string, entityPath: string, getAllPath?: string, onBeforeSend?: Function) {
        let url = new ExternalHttpClient().getUrl(entityPath);
        return this.createStoreInternal(url, entity, key, onBeforeSend);
    }

    private createStoreInternal(url: string, entity: string, key: string, onBeforeSend?: Function) {
        return new ODataStore({
            key,
            url: url,
            beforeSend(request) {
                request.headers = {
                    'Authorization': `Bearer ${session.AccessToken}`,
                    'X-EmployeeId': session.EmployeeId,
                    'X-IS-MSS': `${session.IsMSS}`,
                    'X-IS-ESS': `${session.IsESS}`
                };

                if (session.FrequencyId) {
                    request.headers['X-FrequencyId'] = session.FrequencyId;
                }

                if (onBeforeSend)
                {
                    onBeforeSend(request);
                }
            },
            errorHandler: async (err: any) => {
                if (err.errorDetails) {
                    await Notifications.closeMessage(window.Route.NotificationId);
                    Notifications.showErrors(err.errorDetails.Message, window.Route.NotificationId);
                }
            },
        });
    }
}

class OdataCascadeFactory {
    public createStore(key: string, parent: string, child: string) {
        return new ODataStore({
            key,
            url: new ExternalHttpClient().getUrl(`{companyId}/Lookup/${parent}`),
            beforeSend(request) {
                request.headers = {
                    'Authorization': `Bearer ${session.AccessToken}`,
                    'X-IS-MSS': `${session.IsMSS}`,
                    'X-IS-ESS': `${session.IsESS}`
                };

                if (request.params['$filter']) {
                    let value = "";
                    if (request.params['$filter'].includes('contains')) {
                        value = request.params['$filter'].slice(request.params['$filter'].indexOf('Value eq '))
                            .replace('Value eq', '')
                            .replace(/'/g, '')
                            .replace(')', '')
                            .trim();
                        request.params['$filter'] = request.params['$filter'].slice(1, request.params['$filter'].indexOf(' ') - 1);
                    } else {
                        value = request.params['$filter'].replace('Value eq', '').replace(/'/g, '').trim();
                        request.params['$filter'] = undefined;
                    }

                    request.url = new ExternalHttpClient().getUrl(`{companyId}/Lookup/${child}/${value}`);
                }
            },
            errorHandler: async (err: any) => {
                if (err.errorDetails) {
                    await Notifications.closeMessage(window.Route.NotificationId);
                    Notifications.showErrors(err.errorDetails.Message, window.Route.NotificationId);
                }
            },
        });
    }
}

const odataFactory = new OdataFactory();
const odataCascadeFactory = new OdataCascadeFactory();

export default odataFactory;
export { odataCascadeFactory, odataFactory };