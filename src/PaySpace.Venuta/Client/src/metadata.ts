export interface IMetadata {
    attr(name: string): IAttribute,
    isReadOnly(name?: string): boolean,
    isVisible(name: string): boolean;
    isRequired(name: string): boolean;
    displayName(name: string): string;
    description(name: string): string;
    maximum(name?: string): number | undefined;
    minimum(name?: string): number | undefined;
}

interface IAttribute {
    dataField: string,
    helpText: string,
    formItem: {
        helpText: string
    },
    label: {
        text: string
    },
    caption: string,
    visible: boolean,
    isRequired: boolean,
    editorOptions: {
        min?: number,
        max?: number,
        format?: string
    }
}

function convertToPascalCase(name: string): string {
    if (!name) {
        return name;
    }

    return name.charAt(0).toUpperCase() + name.slice(1);
}

function getPropertyMetadata(model: any, property: string) {
    if (!model) {
        throw new Error('Model is undefined');
    }

    const propertyMetadata = model["Properties"][convertToPascalCase(property)];
    if (!propertyMetadata) {
        return `No metadata for property: ${property}`;
    }

    return propertyMetadata;
}

function isVisible(model: any, property: string): boolean {
    if (!model) {
        throw new Error('Model is undefined');
    }

    const propertyMetadata = model["Properties"][convertToPascalCase(property)];
    if (!propertyMetadata) {
        return false;
    }

    return true;
}

function isReadOnly(model: any, property?: string): boolean {
    if (!model) {
        throw new Error('Model is undefined');
    }

    // Class level check, if it is read-only then all other fields will be read-only
    if (!property && model["IsReadOnly"] == true) {
        return true;
    }

    const propertyMetadata = getPropertyMetadata(model, convertToPascalCase(property!));
    return propertyMetadata["IsReadOnly"];
}

function isRequired(model: any, property: string): boolean {
    const propertyMetadata = getPropertyMetadata(model, convertToPascalCase(property));
    return propertyMetadata["IsRequired"];
}

function displayName(model: any, property: string): string {
    const propertyMetadata = getPropertyMetadata(model, convertToPascalCase(property));
    return propertyMetadata["DisplayName"];
}

function description(model: any, property: string): string {
    const propertyMetadata = getPropertyMetadata(model, convertToPascalCase(property));
    return propertyMetadata["Description"];
}

function minimum(model: any, property?: string): number | undefined {
    if (!property) {
        return undefined;
    }

    const propertyMetadata = getPropertyMetadata(model, convertToPascalCase(property!));
    return propertyMetadata["Range"]?.Minimum;
}

function maximum(model: any, property?: string): number | undefined {
    if (!property) {
        return undefined;
    }

    const propertyMetadata = getPropertyMetadata(model, convertToPascalCase(property!));
    return propertyMetadata["Range"]?.Maximum;
}

function format(model: any, property?: string): string | undefined {
    const propertyMetadata = getPropertyMetadata(model, convertToPascalCase(property!));
    if (propertyMetadata["Type"] === "Int32") {
        return "#";
    }

    return undefined;
}

export async function useMetadata(...params: any[]): Promise<Record<string, IMetadata>> {
    const modelTypes = params.join(',');
    const response = await fetch(`/nextgen/api/metadata/${window.User.Country}?lang=${document.documentElement.lang}&userId=${window.User.UserId}&modelTypes=${modelTypes}`);
    let data = await response.json();

    let outputProps: any = {};
    params.forEach(param => {
        const model = data[param];
        if (model) {
            outputProps[param] = {
                isVisible: (name: string) => isVisible(model, name),
                isReadOnly: (name?: string) => isReadOnly(model, name),
                isRequired: (name: string) => isRequired(model, name),
                displayName: (name: string) => displayName(model, name),
                description: (name: string) => description(model, name),
                attr: (name: string): IAttribute => {
                    return {
                        dataField: name,
                        helpText: description(model, name),
                        formItem: {
                            helpText: description(model, name)
                        },
                        label: {
                            text: displayName(model, name)
                        },
                        caption: displayName(model, name),
                        visible: isVisible(model, name),
                        isRequired: isRequired(model, name),
                        editorOptions: {
                            min: minimum(model, name),
                            max: maximum(model, name),
                            format: format(model, name)
                        }
                    }
                },
                value: model,
            };
        }
    });

    return outputProps;
}