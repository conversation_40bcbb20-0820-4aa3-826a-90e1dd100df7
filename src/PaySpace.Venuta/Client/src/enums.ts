export enum UserType {
    Employee = "Employee",
    Company = "Company",
    Agency = "Agency",
    Bureau = "Bureau"
}

export enum UserTypeNumber {
    Bureau = 1,
    Company = 2,
    Employee = 3,
    Agency = 5
}

export enum EmailAddressStatus {
    Unverified = 0,
    Valid = 1,
    Invalid = 2,
    Uninitialized = 3
}

export enum ContactType {
    Primary = 1,
    PayRoll = 2,
    Hr = 3,
    Contract = 4,
    Billing = 5,
    Technical = 6
}

export enum LeaveType {
    All,
    Annual,
    Sick,
    FamilyResponsibility,
    Study,
    Special
}

export enum CustomFieldScreenType {
    BureauCustomFields = "BureauCustomFields",
    CompanyCustomFields = "CompanyCustomFields",
    TableBuilderCustomFields = "TableBuilderCustomFields",
    CustomFormCustomFields = "CustomFormCustomFields"
}

export enum Area {
    Company = "Company",
    Employee = "Employee"
}

export enum EditorType {
    date = "date",
    number = "number",
    radiogroup = "radio-group",
    text = "text",
    checkbox = "chk",
    select = "select",
    textarea = "textarea",
    lookup = "lookup",
    repository = "repository",
    attachment = "attachment",
    childlibrary = "child-library"
}

export enum PensionLetterStatus {
    Pending = 1,
    Sent = 2,
    Deleted = 3,
    Sending = 4
}

export enum RunFrequency {
    Weekly = 1,
    Monthly = 2,
    Fortnight = 3,
    SemiMonthly = 4
}

export enum TaxCountry {
    SouthAfrica = 1,
    Namibia = 27,
    Brazil = 76,
    UK = 92,
    Canada = 96,
    AU = 97,
    SG = 98,
    ES = 157,
    UKShell = 7,
    IE = 175
}

export enum AddressCountry {
    SG = 264
}

export enum CustomFieldForms {
    EmployeeTraining = 8,
    ClaimComponent = 11,
    EmployeeProject = 15,
    EmployeeLeaveAdjustment = 18,
    EmployeeAttachments = 4,
    ComponentCalculationExceptions = 24
}

export enum RoleType {
    Admin = 1,
    Bureau = 2,
    Employee = 3,
    Manager = 4
}

export enum CustomFormLevel {
    Company = 1,
    Bureau = 2
}

export enum CustomFormType {
    HG = 1,
    LIB = 2,
    CR = 3,
    CLIB = 4
}

export enum MenuStructure {
    MenuEmployee = 0,
    MenuOnOffBoarding = 1,
    MenuBulkUpload = 2,
    MenuReport = 3,
    MenuPayrollCycle = 4,
    MenuIntegrations = 5,
    MenuOther = 6,
    MenuCompany = 7
}

export enum Permission {
    Allow = 1,
    Deny = 2,
    ReadOnly = 3
}

export enum HolidayLevel {
    National = 1,
    Provincial = 2,
    Categorical = 3,
    Municipality = 4
}

export enum RosterType {
    ShiftDate = 1,
    ShiftPattern = 2
}

export enum CustomFieldFormAreas {
    CompanyDetails = 1,
    CompanyAddress = 2,
    CompanyThemes = 3,
    CompanyGeneral = 4,
    CompanyContact = 5
}

export enum RosterScheduleView {
    Detail = 1,
    Calendar = 2
}

export enum RunStatus {
    Closed = 1,
    Open = 2,
    Future = 3
}