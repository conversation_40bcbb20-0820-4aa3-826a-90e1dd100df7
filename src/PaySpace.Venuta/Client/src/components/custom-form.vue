<template>
    <DxDataGrid
        ref="dataGridRef"
        v-if="isLocalizationLoaded"
        :id="`custom-form-grid-${customFormCode}`"
        :data-source="dataSource"
        key-expr="CustomFormId"
        :column-auto-width="true"
        :repaint-changes-only="true"
        @saving="onSaving"
        @init-new-row="onNewRowInit"
        @initialized="onInitialized"
        @editorPreparing="onEditorPreparing"
        @editing-start="onFormEditingStart"
        :required="isRequired"
        :label="label"
        :code="customFormCode">
        <DxPaging :page-size="15" />
        <DxFilterRow :visible="true" />
        <DxEditing :mode="editMode" :allow-updating="!readOnly" :allow-deleting="!readOnly" :allow-adding="!readOnly">
            <DxForm v-if="!isChild" :customize-item="customizeItem" :form-data="editingFormData" :col-count="1" label-location="top" :option="{ customFieldVue: true }">
                <DxGroupItem :col-count="3">
                    <DxItem data-field="EffectiveDate" v-if="showEffectiveDateColumn" :disabled="isClosedRun" :is-required="showEffectiveDateColumn" />
                    <DxItem data-field="CompanyRun"
                            v-if="showPayrollCycle"
                            editorType="dxLookup"
                            :editorOptions="{ dataSource: companyRunDataSource, displayExpr: 'Description', valueExpr: 'Value' }" />
                    <DxItem data-field="Description" v-if="showRepositoryFields">
                        <DxRequiredRule :message="formatMessage('lblDescriptionRequired')" />
                    </DxItem>
                    <DxItem data-field="Code" v-if="showRepositoryFields">
                        <DxRequiredRule :message="formatMessage('lblCodeRequired')" />
                    </DxItem>
                    <DxItem data-field="InactiveDate" v-if="showRepositoryFields" />
                </DxGroupItem>
                <DxGroupItem :col-span="3">
                    <DxItem data-field="CustomFields" :disabled="isClosedRun" />
                </DxGroupItem>
                <DxGroupItem :col-span="3" :visible="showRepositoryFields && !!editingFormData">
                    <DxAccordion :collapsible="true" :deferRendering="false" :selectedItems="selectedItems">
                        <DxAccordionItem :title="'History'">
                            <template #default>
                                <DxDataGrid
                                    :data-source="historyDataSource"
                                    key-expr="CustomFormId"
                                    @editing-start="onHistoryEditingStart"
                                    :column-auto-width="true"
                                    :repaint-changes-only="true">
                                    <DxEditing :mode="form" :allow-updating="!readOnly" :allow-deleting="!readOnly"></DxEditing>
                                    <DxColumn data-field="EffectiveDate"
                                            data-type="date"
                                            sort-order="desc"
                                            :caption="formatMessage('lblEffectiveDate')"></DxColumn>
                                    <DxColumn data-field="Description"
                                            :caption="formatMessage('lblDescription')"></DxColumn>
                                    <DxColumn data-field="Code"
                                            v-if="showRepositoryFields"
                                            :caption="formatMessage('lblCode')"></DxColumn>
                                    <DxColumn data-field="InactiveDate"
                                            data-type="date"
                                            v-if="showRepositoryFields"
                                            :caption="formatMessage('lblInactiveDate')"></DxColumn>
                                    <DxColumn type="buttons">
                                        <DxGridButton name="add" icon="copy" @click="OnHistoryCloneIconClick" :visible="e => e.row.data.CustomFormId !== editingFormData.CustomFormId && IsCloneIconVisible" />
                                        <DxGridButton name="edit" :visible="isHistoryEditing" />
                                        <DxGridButton name="delete" :visible="isHistoryEditing" />
                                    </DxColumn>
                                </DxDataGrid>
                            </template>
                        </DxAccordionItem>
                    </DxAccordion>
                </DxGroupItem>
            </DxForm>
        </DxEditing>
        <DxScrolling show-scrollbar="always" />
        <DxColumn
            :visible-index="1"
            data-field="EffectiveDate"
            data-type="date"
            v-if="showEffectiveDateColumn"
            sort-order="desc"
            :calculate-filter-expression="calculateODataDateFilterExpression"
            :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }"
            :caption="formatMessage('lblEffectiveDate')"></DxColumn>
        <DxColumn
            data-field="CompanyRun"
            :visible="false"
            :caption="formatMessage('lblRunId')"></DxColumn>
        <DxColumn :visible="false" data-field="ParentCustomFormId" v-if="isChild && !clonedKey"></DxColumn>
        <DxColumn :visible-index="2"
                  data-field="Description"
                  v-if="showRepositoryFields"
                  :caption="formatMessage('lblDescription')"></DxColumn>
        <DxColumn :visible-index="3"
                  data-field="Code"
                  v-if="showRepositoryFields"
                  :caption="formatMessage('lblCode')"></DxColumn>
        <DxColumn :visible-index="4"
                  data-field="InactiveDate"
                  :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }"
                  :calculate-filter-expression="calculateODataDateFilterExpression"
                  data-type="date"
                  v-if="showRepositoryFields"
                  :caption="formatMessage('lblInactiveDate')"></DxColumn>
        <DxColumn data-field="ProjectCode" v-if="projectCode" :visible="false" />
        <DxColumn data-field="CustomFields" :visible="false" edit-cell-template="custom-fields-template">
            <DxFormItem :label="{ visible: false }" />
        </DxColumn>
        <DxColumn type="buttons">
            <DxGridButton name="add" icon="copy" @click="OnCloneIconClick" :visible="!readOnly && IsCloneIconVisible" />
            <DxGridButton v-if="isChild" icon="edit" @click="onEditingStart" />
            <DxGridButton v-else name="edit" />
            <DxGridButton name="delete" />
        </DxColumn>
        <DxToolbar v-if="isChild">
            <DxToolbarItem>
                <DxButton type="default" styling-mode="contained" @click="onAddNewRow" icon="add" />
            </DxToolbarItem>
        </DxToolbar>
        <template #custom-fields-template="{ data }">
            <CustomGridFields :entity="entity"
                              :isBasicFormat="false"
                              :args="data"
                              :custom-form-category-code="customFormCode" />
        </template>
    </DxDataGrid>
    <Teleport to="body">
        <Offcanvas
            ref="offCanvasRef"
            type="form"
            v-if="isChild"
            :title="label"
            @closed="() => (customFormGridFormData = null)"
            @save="() => customFormGridFormRef.save()">
            <template #body>
                <CustomFormGridForm
                    ref="customFormGridFormRef"
                    v-if="showCustomFormGridForm"
                    @update="onUpdate"
                    :read-only="readOnly"
                    :show-repository-fields="showRepositoryFields"
                    :entity="entity"
                    :args="customFormGridFormData"
                    :custom-form-category-code="customFormCode" />
            </template>
        </Offcanvas>
    </Teleport>
</template>

<script setup lang="ts">
    import { onMounted, ref, computed } from "vue";

    import CustomGridFields from "../components/custom-grid-fields.ce.vue";

    import {
        DxColumn,
        DxDataGrid,
        DxDataGridTypes,
        DxEditing,
        DxForm,
        DxFormItem,
        DxButton as DxGridButton,
        DxPaging,
        DxScrolling,
        DxToolbar,
        DxItem as DxToolbarItem,
        DxFilterRow
    } from "devextreme-vue/data-grid";
    import { DxGroupItem, DxItem, DxRequiredRule } from "devextreme-vue/form";
    import { DxAccordion, DxItem as DxAccordionItem } from "devextreme-vue/accordion";

    import { CustomFieldValue } from "../models/custom-fields/custom-field-value";
    import customFieldsService from "../services/custom-fields/custom-fields-service";
    import fieldParser, { CustomFieldMetadata } from "../services/custom-fields/field-parser";

    import { CustomFormType, RunStatus } from "@/enums";
    import { useLocalization } from "@/localization";
    import odataFactory from "@/odata-extensions";
    import DataSource from "devextreme/data/data_source";
    import { formatMessage } from "devextreme/localization";

    import { useOffCanvasMode } from "@/composables/custom-form/offcanvas-mode";
    import { ExternalHttpClient } from "@/http-client";
    import DxButton from "devextreme-vue/button";
    import dxDataGrid from "devextreme/ui/data_grid";
    import CustomFormGridForm from "./custom-form-grid-form.vue";
    import Offcanvas from "./ui/offcanvas.vue";
    import { calculateODataDateFilterExpression } from "@/helpers/date-filter-helper";

    const props = withDefaults(defineProps<ICustomForm>(), { editMode: "form" });

    interface ICustomForm {
        customFormCode: string;
        customFormLevel: number;
        customFormType: CustomFormType;
        isEmployeeLevel: boolean;
        hideEffectiveDate: boolean;
        readOnly: boolean;
        editMode: DxDataGridTypes.GridsEditMode;
        projectCode?: string;
        label?: string;
        parentCustomFormId?: number;
        isChild?: boolean;
        clonedKey?: number;
        isRequired?: boolean;
        currentRuns: string[];
        showPayrollCycle?: boolean;
    }

    const isLocalizationLoaded = ref<boolean>(false);
    const dataGridRef = ref<{ instance: dxDataGrid } | undefined>();
    const customFormGridFormRef = ref();
    const isClosedRun = ref<boolean>(false);
    const editingFormData = ref();
    const { offCanvasRef, customFormGridFormData, showCustomFormGridForm, onAddNewRow, onUpdate, onEditingStart } =
        useOffCanvasMode(dataGridRef, props.parentCustomFormId);

    const entity = "CustomForm";
    const customFormEntity = props.isEmployeeLevel ? "EmployeeCustomForm" : "CompanyCustomForm";
    const externalHttpClient = new ExternalHttpClient();

    const companyRunStore = odataFactory.createLookupStore("CompanyRun", "Value");
    const runFilter: any = ['RunStatus', '=', RunStatus.Open];
    let defaultRun;

    onMounted(async () => {
        await useLocalization("CustomForms", "General", "System.Notification");
        isLocalizationLoaded.value = true;

        if(props.showPayrollCycle) {
            [{ Value: defaultRun }] = await companyRunStore.load({
                filter: runFilter,
                take: 1,
                select: "Value",
                sort: [{ selector: "PeriodStartDate", desc: true }]
            })
        }
    });

    const showEffectiveDateColumn = props.hideEffectiveDate === false;
    const showRepositoryFields = props.customFormType === CustomFormType.CR;

    let dsFilter: any = undefined;
    if (props.projectCode) {
        dsFilter = ["ProjectCode", "=", props.projectCode];
    }

    if (props.isChild) {
        // filter by 0 when the parent does not exist so the list does not include any other records
        const parentFilter = ["ParentCustomFormId", "=", props.parentCustomFormId ?? 0];
        if (dsFilter?.length > 0) {
            dsFilter = [dsFilter, "and", parentFilter];
        } else {
            dsFilter = parentFilter;
        }
    }
    const ENTITY_URL = `${customFormEntity}/${props.customFormCode}`;

    const odataStore = odataFactory.createStore(ENTITY_URL, "CustomFormId", "all");
    const selectedItems = ref([]);
    const historyDataSource = computed(() => {
        if (editingFormData.value?.Code) {
            return getChildDataSource(editingFormData.value.Code);
        }
        return [];
    });

    let dataSource: DataSource;
    if (props.customFormType === CustomFormType.CR) {
        dataSource = new DataSource({
            load: async () => {
                // Combine your existing dynamic filters (dsFilter) with the load request
                const rawData = await odataStore.load({
                    filter: dsFilter
                });

                return getNearestEffectiveDatePerCode(rawData);
            },
            remove: (rowData) => { return odataStore.remove(rowData.CustomFormId); },
            insert: (rowData) => { return odataStore.insert(rowData); },
            update: (oldRowData, newRowData) => { return odataStore.update(oldRowData.CustomFormId, newRowData); }
        });
    } else {
        dataSource = new DataSource({
            store: odataStore,
            filter: dsFilter
        });
    }

    const companyOpenRunDataSource = new DataSource({
        store: companyRunStore,
        filter: runFilter,
        sort: [{ selector: "PeriodStartDate", desc: true }]
    });

    const companyRunDataSource = new DataSource({
        store: odataFactory.createLookupStore("CompanyRun", "Value")
    });

    function getChildDataSource(code) {
        return new DataSource({
            store: odataStore,
            filter: ['Code', '=', code]
        });
    }

    let clonedItem: any;

    function onFormEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        if(showRepositoryFields){
            editingFormData.value = { ...e.data }; // clone to avoid direct mutation
        }
    }

    function onHistoryEditingStart(e: DxDataGridTypes.EditingStartEvent) {
        selectedItems.value = [1];
        e.cancel = true;
        clonedItem = {...e.data};
        clonedItem.isExistingEntry = true;
        editingFormData.value = clonedItem;
        dataGridRef.value?.instance.cancelEditData();
        dataGridRef.value?.instance.addRow();
        dataGridRef.value?.instance.deselectAll();
        dataGridRef.value?.instance.refresh(true);
        e.event?.preventDefault();
    }

    const OnCloneIconClick = (e: DxDataGridTypes.ColumnButtonClickEvent): void => {
        clonedItem = $.extend({}, e.row?.data, { CustomFormId: 0, clonedKey: e.row?.key });

        if(showRepositoryFields){
            editingFormData.value = clonedItem;
        }
        dataGridRef.value?.instance.cancelEditData();
        e.component.addRow();
        e.component.deselectAll();

        e.component.refresh(true);
        e.event?.preventDefault();
    };

    const OnHistoryCloneIconClick = (e: DxDataGridTypes.ColumnButtonClickEvent): void => {
        selectedItems.value = [1];
        clonedItem = $.extend({}, e.row?.data, { CustomFormId: 0, clonedKey: e.row?.key });
        editingFormData.value = clonedItem;
        dataGridRef.value?.instance.cancelEditData();
        dataGridRef.value?.instance.addRow();
        dataGridRef.value?.instance.deselectAll();
        dataGridRef.value?.instance.refresh(true);
        e.event?.preventDefault();
    };

    function onNewRowInit(e: DxDataGridTypes.InitNewRowEvent) {
        e.data.CustomFormCategoryCode = props.customFormCode;
        e.data.ProjectCode = props.projectCode;
        e.data.CustomFields = [];
        if(props.showPayrollCycle) {
            e.data.CompanyRun = defaultRun;
        }
        const grid = e.component;
        if (clonedItem) {
            grid.beginUpdate();
            e.data = clonedItem;
            clonedItem = null;
            grid.endUpdate();
        } else {
            editingFormData.value = null;
        }
    }

    const IsCloneIconVisible = (e: { row: { isEditing: boolean}}) => !e.row.isEditing;

    const isHistoryEditing = (e: DxDataGridTypes.EditingStartEvent) => {
        return e.row?.data?.CustomFormId !== editingFormData.value.CustomFormId || e.row?.data?.CustomFormId === 0;
    };

    function fieldLookup(config: CustomFieldMetadata): object {
        if (config.editorType !== "dxLookup") {
            return {};
        }

        return config.editorOptions;
    }

    function customizeItem(item: SimpleItem) {
        if (item.dataField === 'CompanyRun') {
            if (props.showPayrollCycle) {
                let grid = dataGridRef.value.instance;
                let editRowKey = grid.option('editing.editRowKey');
                let rowIndex = grid.getRowIndexByKey(editRowKey);
                let value = grid.cellValue(rowIndex, "CompanyRun")

                // --- is the value of an empty companyRun on the grid
                if (value && value != '---' && !props.currentRuns.includes(value)) {
                    item.disabled = true;
                }
            }
        }
    }

    function onEditorPreparing(e: EditorPreparingEvent) {
        if (e.dataField === "CompanyRun") {
            if (props.showPayrollCycle) {
                let grid = dataGridRef.value.instance;
                let editRowKey = grid.option('editing.editRowKey');
                let rowIndex = grid.getRowIndexByKey(editRowKey);
                let value = grid.cellValue(rowIndex, "CompanyRun");

                // --- is the value of an empty companyRun on the grid
                if (value && value != '---' && !props.currentRuns.includes(value)) {
                    isClosedRun.value = true;
                    e.editorOptions.dataSource = companyRunDataSource;
                }
                else {
                    isClosedRun.value = false;
                    e.editorOptions.dataSource = companyOpenRunDataSource;
                }
            }
        };
    }

    function onInitialized(e: DxDataGridTypes.InitializedEvent) {
        customFieldsService
            .GetConfiguration(entity, undefined, props.customFormCode)
            .then(config => {
                return config.value.map(_ => fieldParser.Parse(false, entity, undefined, _));
            })
            .then(result => {
                return fieldParser.OrderMetadata(false, new Array<CustomFieldValue>(), result);
            })
            .then(fieldConfig => {
                fieldConfig
                    .filter(x => x.showOnGrid)
                    .forEach(config => {
                        if (showRepositoryFields) {
                            config.visibleIndex! += 3;
                        }

                        e.component?.addColumn({
                            dataField: config.dataField!,
                            dataType: config.editorType as DxDataGridTypes.DataType,
                            caption: config.label?.text,
                            visibleIndex: ++config.visibleIndex!,
                            allowEditing: false,
                            allowFiltering: false,
                            formItem: {
                                visible: false
                            }
                        });
                    });

                // Display the first valid custom field if the effective date is hidden and there are no visible fields
                if (props.hideEffectiveDate && !hasVisibleFields(e.component!)) {
                    const firstField = fieldConfig.filter(conf => conf.editorType).at(0);

                    if (firstField) {
                        e.component?.addColumn({
                            dataField: firstField.dataField!,
                            dataType: firstField.editorType as DxDataGridTypes.DataType,
                            caption: firstField.label?.text,
                            visibleIndex: 0,
                            allowEditing: false,
                            allowFiltering: false,
                            formItem: {
                                visible: false
                            }
                        });
                    }
                }

                e.component?.columnOption("buttons", "visibleIndex", 999);
            });

        if (props.isChild && props.clonedKey) loadDataForClonedChildCustomForm(e);
    }

    const hasVisibleFields = (component: dxDataGrid): boolean => {
        return component.getVisibleColumns().some((col: DxDataGridTypes.Column) => col.type !== "buttons");
    };

    const loadDataForClonedChildCustomForm = (e: DxDataGridTypes.InitializedEvent) => {
        e.component?.beginCustomLoading("load-cloned-data");

        const url = externalHttpClient.getUrl(
            `{companyId}/${ENTITY_URL}?$filter=ParentCustomFormId eq ${props.clonedKey}`
        );
        const result = externalHttpClient.get(url);

        result.then(async ({ value: clonedData }) => {
            for (const clonedDataItem of clonedData) {
                await dataGridRef.value?.instance.addRow();
                const newRowIndex: DxDataGridTypes.Row[] = dataGridRef.value?.instance.getVisibleRows()[0].rowIndex;

                Object.keys(clonedDataItem).forEach((fieldName: string) => {
                    dataGridRef.value?.instance.cellValue(newRowIndex, fieldName, clonedDataItem[fieldName]);
                });
            }

            e.component?.endCustomLoading();
        });
    };

    const onSaving = (e: DxDataGridTypes.SavingEvent) => {
        const changes = e.changes[0];
        if (props.isChild || changes?.type === "remove") return;

        e.cancel = true;

        const errors: string[] = [];
        const childInstances = getChildLibraryInstances();
        childInstances
            .filter(instance => instance.option("required") === true)
            .forEach(instance => {
                const removedRowKeys = getRemovedRowCount(instance);
                if (hasNoVisibleRows(instance) && allRowsRemoved(instance, removedRowKeys)) {
                    const label = (instance.option("label") ?? "") as string;
                    errors.push(formatMessage("lblChildCustomFormRequired", label));
                }
            });

        if (errors.length > 0) {
            Notifications.showErrors(errors);
            return;
        }

        if(showRepositoryFields && changes.data.isExistingEntry){
            changes.key = changes.data;
            changes.type = "update";
            delete changes.data.isExistingEntry;
        }

        if (changes?.type === "update") {
            dataSource
                .store()
                .update(changes.key, changes.data)
                .then(() => {
                    Notifications.showSuccess(formatMessage("Update.Success"));
                    saveEditDataForChildCustomForms(childInstances);
                });
        } else if (changes?.type === "insert") {
            dataSource
                .store()
                .insert(changes.data)
                .then(result => {
                    Notifications.showSuccess(formatMessage("Create.Success"));
                    saveEditDataForChildCustomForms(childInstances, result.CustomFormId);
                });
        }

        const saveEditDataForChildCustomForms = (childInstances: dxDataGrid[], parentCustomFormId?: number) => {
            const saveEditDataPromises = childInstances.map(gridInstance => {
                const gridStore = gridInstance.getDataSource().store();
                //On insert after saving parent custom form use the returned customFormId when saving child custom form.
                gridStore.on("inserting", (values: { ParentCustomFormId: number | undefined }) => {
                    if (!values.ParentCustomFormId) {
                        values.ParentCustomFormId = parentCustomFormId;
                    }
                });

                return gridInstance.saveEditData();
            });

            Promise.all(saveEditDataPromises).then(() => {
                //Cancel edit data is to close edit mode on the form after saving data manually.
                dataGridRef.value?.instance.cancelEditData();
                dataGridRef.value?.instance.refresh();
            });
        };

        function getChildLibraryInstances(): Array<dxDataGrid> {
            const childCustomFormsGrids = e.element.querySelectorAll("[id*=custom-form-grid-]");
            const childCustomFormsGridInstances = childCustomFormsGrids.entries().map(([_, element]) => {
                return dxDataGrid.getInstance(element) as dxDataGrid;
            });

            return childCustomFormsGridInstances.toArray();
        }
    };

    function allRowsRemoved(instance: dxDataGrid, removedRowKeys: number) {
        return instance.getDataSource().totalCount() === removedRowKeys;
    }

    function hasNoVisibleRows(instance: dxDataGrid) {
        return instance.getVisibleRows().filter((r: any) => r.removed !== true).length === 0;
    }

    function getRemovedRowCount(dataGrid: dxDataGrid) {
        const changes = (dataGrid.option("editing.changes") as Array<{ type: string }>) ?? [];
        return changes.filter((change: { type: string }) => change.type === "remove").length;
    }

    function getNearestEffectiveDatePerCode(data: Array<{ EffectiveDate: string; Code: string }>) {
        const today = new Date().toISOString().split('T')[0]; // '2025-05-21'

        const grouped: Record<string, { EffectiveDate: string; Code: string }[]> = {};

        // Group rows by code
        for (const row of data) {
            if (!grouped[row.Code]) {
                grouped[row.Code] = [];
            }
            grouped[row.Code].push(row);
        }

        const result: Array<{ EffectiveDate: string; Code: string }> = [];

        for (const code in grouped) {
            const rows = grouped[code];

            const pastOrToday = rows
                .filter(r => r.EffectiveDate <= today)
                .sort((a, b) => b.EffectiveDate.localeCompare(a.EffectiveDate)); // most recent past first

            if (pastOrToday.length > 0) {
                result.push(pastOrToday[0]);
                continue;
            }

            const future = rows
                .filter(r => r.EffectiveDate > today)
                .sort((a, b) => a.EffectiveDate.localeCompare(b.EffectiveDate)); // soonest future

            if (future.length > 0) {
                result.push(future[0]);
            }
        }

        return result;
    }

</script>