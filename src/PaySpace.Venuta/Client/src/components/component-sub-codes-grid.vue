<template>
    <DxDataGrid ref="grid" :column-auto-width="true" :data-source="dataSource" @toolbarPreparing="onToolbarPreparing"
                key-expr="ComponentCalcExceptionDetailId">
        <DxEditing mode="batch" :allow-adding="allowEdit" :allow-deleting="allowEdit" :allow-updating="allowEdit"
                   :confirm-delete="false"
                   :use-icons="true" />
        <DxColumn data-field="BureauSubCodeId" editor-type="dxSelectBox" :caption="formatMessage('BureauSubCodeId')">
            <DxLookup :dataSource="lookupStore('bureau-sub-codes')" display-expr="Text" value-expr="Value"
                      :show-clear-button="false " />
            <DxRequiredRule :message="formatMessage('validation-required-formatted', formatMessage('BureauSubCodeId'))" />
        </DxColumn>
        <DxColumn data-field="ComponentBureauId" editor-type="dxSelectBox" :caption="formatMessage('ComponentBureauId')">
            <DxLookup :data-source="lookupStore('bureau-income-base')" display-expr="Text" value-expr="Value"
                      :show-clear-button="false" />
            <DxRequiredRule :message="formatMessage('validation-required-formatted', formatMessage('ComponentBureauId'))" />
        </DxColumn>
        <DxColumn data-field="BureauTaxabilityOptionId" editor-type="dxSelectBox" :caption="formatMessage('BureauTaxabilityOptionId')" :visible="showTaxabilityOption">
            <DxLookup :data-source="taxabilityOptionDataSource" display-expr="text" value-expr="value"
                      allow-clearing="true" />
        </DxColumn>
    </DxDataGrid>
</template>

<script lang="ts">
    import lookupFactory from '@/lookup-extensions';
    import { createStore } from 'devextreme-aspnet-data-nojquery';
    import { DxButton, DxColumn, DxDataGrid, DxEditing, DxLookup, DxRequiredRule } from 'devextreme-vue/data-grid';
    import { formatMessage } from 'devextreme/localization';
    import { defineComponent, ref } from "vue";
    import { LocalHttpClient } from "../http-client";

    export class SubCodeException {
        ComponentCalcExceptionDetailId!: number;
        ComponentBureauId!: number;
        BureauSubCodeId!: number;
        BureauTaxabilityOptionId?: number;
    }

    export default defineComponent({
        components: {
            DxDataGrid,
            DxColumn,
            DxButton,
            DxEditing,
            DxRequiredRule,
            DxLookup
        },
        props: {
            componentId: {
                type: Number,
                required: true
            },
            dataSource: {
                type: Array<SubCodeException>,
                required: false
            },
            baseUrl: {
                type: String,
                required: true
            },
            allowEdit: {
                type: Boolean,
                required: true
            },
            countryId: {
                type: Number,
                required: true
            },
            showTaxabilityOption: {
                type: Boolean,
                required: true
            },
        },
        setup(props) {
            const grid = ref();

            const taxabilityOptionDataSource = lookupFactory.createCustomStore("bureau-taxability-options", props.countryId.toString());

            function lookupStore(endpoint: string) {
                return createStore({
                    key: "value",
                    loadUrl: new LocalHttpClient().getUrl(`${props.baseUrl}/${endpoint}`)
                });
            }

            function onToolbarPreparing(e: any) {
                PaySpace.ui.DxDataGrid.onToolbarPreparing(e);
                e.toolbarOptions.items.forEach((item: { name: String, visible: Boolean }) => {
                    if (item.name == "saveButton" || item.name == "revertButton") {
                        item.visible = false;
                    }
                });
            }

            return {
                grid,
                taxabilityOptionDataSource,
                onToolbarPreparing,
                formatMessage,
                lookupStore
            }
        }
    })
</script>