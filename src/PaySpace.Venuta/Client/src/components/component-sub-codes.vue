<template>
    <div class="modal-body">
        <DxForm
            ref="subCodeForm"
            :col-count="2"
            :form-data="formData"
            label-location="top"
            :read-only="!allowEdit"
            :key="formData?.ComponentCalcExceptionId"
            validation-group="customfields">
            <DxGroupItem :col-span="2">
                <DxItem
                    data-field="EffectiveDate"
                    editor-type="dxDateBox"
                    data-type="date"
                    display-format="shortdate"
                    :label="{ text: formatMessage('EffectiveDate') }"
                    :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }">
                    <DxRequiredRule
                        :message="formatMessage('validation-required-formatted', formatMessage('EffectiveDate'))" />
                </DxItem>
            </DxGroupItem>
            <DxItem :col-span="2">
                <p>
                    {{ formatMessage("lblDescription1") }}
                    <b>{{ componentName }}</b>
                    {{ formatMessage("lblDescription2") }}
                </p>
                <component-sub-codes-grid
                    ref="subCodeGrid"
                    :component-id="componentId"
                    :data-source="formData?.ComponentCalcExceptionDetails"
                    :base-url="dataUrl"
                    :show-taxability-option="showTaxabilityOption"
                    :country-id="countryId"
                    :allow-edit="allowEdit" />
            </DxItem>
            <DxItem data-field="CustomFields" template="custom-fields-template" :col-span="2">
                <DxLabel :visible="false" />
            </DxItem>

            <template #custom-fields-template="{ data }">
                <custom-form-fields
                    entity="ComponentCalcException"
                    :read-only="!allowEdit"
                    :args="data"
                    :is-basic-format="true" />
            </template>

            <DxItem :col-span="2">
                <DxAccordion :data-source="[{}]" :collapsible="true" :multiple="false">
                    <template #title>
                        {{ formatMessage("lblHistory") }}
                    </template>
                    <template #item>
                        <DxDataGrid
                            :column-auto-width="true"
                            :data-source="historyDs"
                            @editing-start="onEditingStart"
                            @row-removed="onRowRemoved">
                            <DxEditing
                                mode="row"
                                :allow-updating="allowEditing"
                                :allow-deleting="allowEdit"
                                :confirm-delete="true"
                                :use-icons="true" />
                            <DxColumn
                                data-field="EffectiveDate"
                                data-type="date"
                                display-format="shortdate"
                                :editor-options="{ dateSerializationFormat: 'yyyy-MM-dd' }"
                                :caption="formatMessage('EffectiveDate')" />
                        </DxDataGrid>
                    </template>
                </DxAccordion>
            </DxItem>
        </DxForm>
    </div>
    <div class="modal-footer">
        <DxButton v-if="allowEdit" :text="formatMessage('lblSubmit')" @click="submitPage(subCodeId)" type="default" />
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            {{ formatMessage("lblCancel") }}
        </button>
    </div>
</template>

<script lang="ts" setup>
    import { createStore } from "devextreme-aspnet-data-nojquery";
    import { DxAccordion } from "devextreme-vue/accordion";
    import { DxButton } from "devextreme-vue/button";
    import { DxColumn, DxDataGrid, DxEditing } from "devextreme-vue/data-grid";
    import { DxDateBox } from "devextreme-vue/date-box";
    import { DxForm, DxGroupItem, DxItem, DxLabel, DxRequiredRule } from "devextreme-vue/form";
    import { formatMessage } from "devextreme/localization";
    import dxForm from "devextreme/ui/form";
    import ValidationEngine from "devextreme/ui/validation_engine";
    import { ref, onMounted } from "vue";
    import { LocalHttpClient } from "../http-client";
    import { useLocalization } from "@/localization";
    import { CustomFieldValue } from "../models/custom-fields/custom-field-value";
    import ComponentSubCodesGrid, { SubCodeException } from "./component-sub-codes-grid.vue";
    import CustomFormFields from "./custom-form-fields.ce.vue";

    class SubCodeViewModel {
        constructor() {
            this.ComponentCalcExceptionDetails = new Array<SubCodeException>();
            this.CustomFields = new Array<CustomFieldValue>();
        }

        ComponentCalcExceptionId!: number;
        EffectiveDate!: Date;
        ComponentCalcExceptionDetails!: Array<SubCodeException>;
        CustomFields: Array<CustomFieldValue>;
    }

    const props = defineProps({
        componentId: {
            type: Number,
            required: true
        },
        componentName: {
            type: String,
            required: true
        },
        componentCalcExceptionId: {
            type: Number,
            required: true
        },
        allowEdit: {
            type: Boolean,
            required: true
        },
        countryId: {
            type: Number,
            required: true
        },
        showTaxabilityOption: {
            type: Boolean,
            required: true
        }
    });

    await useLocalization("Payroll.Components.Company.Sub.Code");

    const httpClient = new LocalHttpClient();
    const dataUrl = `company/{companyId}/frequency/{frequencyId}/payroll-sub-code/api/${props.componentId}`;
    const formData = ref(new SubCodeViewModel());
    const subCodeId = ref<number>(props.componentCalcExceptionId);
    const subCodeForm = ref<{ instance: dxForm }>();
    const subCodeGrid = ref();

    const historyDs = createStore({
        key: "ComponentCalcExceptionId",
        loadUrl: httpClient.getUrl(dataUrl + "/history"),
        deleteUrl: httpClient.getUrl(dataUrl)
    });

    function getSubCode(componentCalcExceptionId: number) {
        formData.value = new SubCodeViewModel();
        subCodeForm.value?.instance.repaint();

        httpClient
            .get(`${dataUrl}?componentCalcExceptionId=${componentCalcExceptionId}`)
            .then(res => {
                formData.value = res;
                subCodeForm.value?.instance.repaint();
                subCodeId.value = componentCalcExceptionId;
            })
            .finally(() => {
                window.HideLoadPanel();
            });
    }

    async function submitPage(componentCalcExceptionId: number) {
        let gridInstance = subCodeGrid.value.grid.instance;
        if (gridInstance.hasEditData()) {
            await gridInstance.saveEditData();
        }

        const validationResults = ValidationEngine.validateGroup("customfields");

        let data = { ...formData.value } as SubCodeViewModel;
        data?.ComponentCalcExceptionDetails.forEach(detail => {
            if (isNaN(detail.ComponentCalcExceptionDetailId)) {
                detail.ComponentCalcExceptionDetailId = 0;
            }
        });

        if (validationResults.isValid) {
            componentCalcExceptionId > 0
                ? submit(`${dataUrl}/${componentCalcExceptionId}`, data)
                : submit(`${dataUrl}`, data);
        } else {
            Notifications.closeMessage(window.Route.NotificationId);
            Notifications.showErrors(
                validationResults.brokenRules!.map((err: any) => err.message),
                window.Route.NotificationId
            );
        }
    }

    function onEditingStart(e: { data: { ComponentCalcExceptionId: number }; cancel: boolean }) {
        e.cancel = true;
        getSubCode(e.data.ComponentCalcExceptionId);
    }

    function onRowRemoved(e: { key: number }) {
        if (e.key == subCodeId.value) {
            subCodeId.value = 0;
            formData.value = new SubCodeViewModel();
            subCodeForm.value?.instance.repaint();
        }
    }

    function allowEditing(e: { row: { data: { ComponentCalcExceptionId: number } } }) {
        return props.allowEdit && e.row.data.ComponentCalcExceptionId != subCodeId.value;
    }

    function submit(url: string, data: SubCodeViewModel): Promise<void> | undefined {
        window.ShowLoadPanel();
        return new LocalHttpClient()
            .post(url, JSON.stringify(data))
            .then(resp => {
                if (!isNaN(resp)) {
                    subCodeId.value = resp;
                    formData.value.ComponentCalcExceptionId = resp;
                    subCodeForm.value?.instance.repaint();
                }
            })
            .finally(function () {
                window.HideLoadPanel();
            });
    }

    onMounted(() => getSubCode(props.componentCalcExceptionId));
</script>
