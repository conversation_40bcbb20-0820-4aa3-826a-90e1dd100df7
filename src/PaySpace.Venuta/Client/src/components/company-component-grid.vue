<template>
    <DxDataGrid :data-source="ds" :column-auto-width="true">
        <DxColumn
            data-field="aliasDescription"
            cell-template="descriptionTemplate"
            :caption="formatMessage('aliasDescription')" />
        <DxColumn data-field="taxCode" cell-template="taxCodeTemplate" :caption="formatMessage('taxCode')" />
        <DxColumn
            data-field="active"
            cell-template="statusTemplate"
            data-type="boolean"
            :caption="formatMessage('active')">
            <DxFormItem :label="{ visible: false }" :editor-options="{ text: formatMessage('active') }" />
        </DxColumn>
        <DxColumn
            data-field="hasSubCodes"
            data-type="boolean"
            :caption="formatMessage('lblHasSubCodes')"
            cell-template="tickTemplate" />
        <DxColumn type="buttons">
            <DxButton
                :visible="showSubCodeButton"
                :title="formatMessage('lblSubCode')"
                @click="showSubCode"
                icon="fa-solid fa-calculator" />
        </DxColumn>

        <template #descriptionTemplate="{ data }">
            <span :title="data.data.componentDescription">{{ data.value }}</span>
        </template>

        <template #tickTemplate="{ data }">
            <em v-if="data.value" class="fas fa-check table-tick"></em>
        </template>

        <template #taxCodeTemplate="{ data }">
            <span
                :title="formatMessage('lblOverrideTooltip', data.data.originalTaxCode, data.data.taxCodeDescription)"
                v-if="data.data.originalTaxCode !== data.data.taxCode"
                class="text-danger">
                {{ data.value }}
            </span>
            <span :title="data.data.taxCodeDescription" v-else>{{ data.value }}</span>
        </template>

        <template #inPackageTemplate="{ data }">
            <em v-if="data.value" class="fas fa-check table-tick"></em>
        </template>

        <template #statusTemplate="{ data: cellData }">
            <span v-if="cellData.value == true">{{ formatMessage("lblActive") }}</span>
            <span v-else>{{ formatMessage("lblInactive") }}</span>
        </template>
    </DxDataGrid>
</template>

<script lang="ts">
    import { createStore } from "devextreme-aspnet-data-nojquery";
    import { DxButton, DxColumn, DxDataGrid, DxFormItem } from "devextreme-vue/data-grid";
    import { formatMessage } from "devextreme/localization";
    import { defineComponent } from "vue";
    import { LocalHttpClient } from "../http-client";
    import ComponentSubCodes from "./component-sub-codes.vue";
    import { useTenant } from "@/composables/tenant";

    export class ComponentCalcExceptionModel {
        constructor() {
            this.ComponentId = 0;
            this.ComponentName = "";
            this.ComponentCalcExceptionId = 0;
        }

        ComponentId: number;
        ComponentName: string;
        ComponentCalcExceptionId: number;
    }

    export default defineComponent({
        components: {
            DxDataGrid,
            DxColumn,
            DxButton,
            DxFormItem,
            ComponentSubCodes
        },
        props: {
            runId: {
                type: Number,
                required: true
            },
            payslipAction: {
                type: Number,
                required: true
            },
            includeInactive: {
                type: Boolean,
                required: true
            },
            showCalcExceptions: {
                type: Boolean,
                required: true
            },
            countryId: {
                type: Number,
                required: true
            }
        },
        setup(props) {

           const { companyId, frequencyId } = useTenant();

            let localHttpClient = new LocalHttpClient();
            let url = localHttpClient.getUrl(
                `company/${companyId}/frequency/${frequencyId}/payroll-components/api/${props.runId}/${props.payslipAction}`
            );
            if (props.includeInactive) {
                url = url + "?includeInactive=true";
            }

            return {
                ds: createStore({
                    key: "componentId",
                    loadUrl: url
                }),
                exceptionsPageUrl(componentId: number): string {
                    return localHttpClient.getUrl(
                        `company/${companyId}/frequency/${frequencyId}/payroll-component-sub-code/${componentId}`
                    );
                },
                formatMessage,
                subCodeModel: new ComponentCalcExceptionModel(),
                showSubCodeButton: (e: { row: { data: { disallowCompanyExemption: boolean } } }) => {
                    return props.showCalcExceptions && !e.row.data.disallowCompanyExemption;
                }
            };
        },
        methods: {
            showSubCode(e: { row: { data: { componentId: number } } }) {
                window.ShowLoadPanel();
                new LocalHttpClient()
                    .get(
                        `company/{companyId}/frequency/{frequencyId}/payroll-sub-code/api/${e.row.data.componentId}/sub-code-name`
                    )
                    .then((res: ComponentCalcExceptionModel) => {
                        this.subCodeModel = res;
                        this.$emit("subCodeDataLoaded", this.subCodeModel);
                    });

                e.event.preventDefault();
            }
        }
    });
</script>
