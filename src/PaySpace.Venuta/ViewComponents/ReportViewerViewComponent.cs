namespace PaySpace.Venuta.ViewComponents
{
    using System;
    using System.Collections.Generic;

    using Microsoft.AspNetCore.Mvc;

    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.ViewModels;

    public class ReportViewerViewComponent : ViewComponent
    {
        private readonly IReportUrlResolver reportUrlResolver;

        public ReportViewerViewComponent(IReportUrlResolver reportUrlResolver)
        {
            this.reportUrlResolver = reportUrlResolver;
        }

        public IViewComponentResult Invoke(
            string name,
            string parameters,
            bool showParameters,
            Dictionary<string, ReportViewParameterEditor> editorOptions = null)
        {
            return this.View(new ReportViewerViewModel
            {
                HandlerUrl = new Uri(this.reportUrlResolver.Resolve(this.Request.Host), "/DXXRDV"),
                ReportName = name,
                Parameters = parameters,
                ShowParameters = showParameters,
                EditorOptions = editorOptions
            });
        }
    }
}

namespace DevExpress.AspNetCore
{
    using System;
    using System.Reflection;

    using DevExpress.AspNetCore.Reporting.WebDocumentViewer;

    [Obsolete("Log a support ticket why a remote source requires retrieving the report locally")]
    public static class WebDocumentViewerBuilderExtentions
    {
        public static WebDocumentViewerBuilder ReportUrl(this WebDocumentViewerBuilder builder, string reportUrl)
        {
            var reportUrlProperty = typeof(WebDocumentViewerBuilder).GetProperty("ReportUrl", BindingFlags.Instance | BindingFlags.NonPublic);
            reportUrlProperty.SetValue(builder, reportUrl);

            return builder;
        }
    }
}