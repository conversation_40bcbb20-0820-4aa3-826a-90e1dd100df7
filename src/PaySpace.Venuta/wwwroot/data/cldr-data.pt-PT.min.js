var CldrData=[{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},likelySubtags:{aa:"aa-Latn-ET",aai:"aai-Latn-ZZ",aak:"aak-Latn-ZZ",aau:"aau-Latn-ZZ",ab:"ab-Cyrl-GE",abi:"abi-Latn-ZZ",abq:"abq-Cyrl-ZZ",abr:"abr-Latn-GH",abt:"abt-Latn-ZZ",aby:"aby-Latn-ZZ",acd:"acd-Latn-ZZ",ace:"ace-Latn-ID",ach:"ach-Latn-UG",ada:"ada-Latn-GH",ade:"ade-Latn-ZZ",adj:"adj-Latn-ZZ",adp:"adp-Tibt-BT",ady:"ady-Cyrl-RU",adz:"adz-Latn-ZZ",ae:"ae-Avst-IR",aeb:"aeb-Arab-TN",aey:"aey-Latn-ZZ",af:"af-Latn-ZA",agc:"agc-Latn-ZZ",agd:"agd-Latn-ZZ",agg:"agg-Latn-ZZ",agm:"agm-Latn-ZZ",ago:"ago-Latn-ZZ",agq:"agq-Latn-CM",aha:"aha-Latn-ZZ",ahl:"ahl-Latn-ZZ",aho:"aho-Ahom-IN",ajg:"ajg-Latn-ZZ",ak:"ak-Latn-GH",akk:"akk-Xsux-IQ",ala:"ala-Latn-ZZ",ali:"ali-Latn-ZZ",aln:"aln-Latn-XK",alt:"alt-Cyrl-RU",am:"am-Ethi-ET",amm:"amm-Latn-ZZ",amn:"amn-Latn-ZZ",amo:"amo-Latn-NG",amp:"amp-Latn-ZZ",an:"an-Latn-ES",anc:"anc-Latn-ZZ",ank:"ank-Latn-ZZ",ann:"ann-Latn-ZZ",any:"any-Latn-ZZ",aoj:"aoj-Latn-ZZ",aom:"aom-Latn-ZZ",aoz:"aoz-Latn-ID",apc:"apc-Arab-ZZ",apd:"apd-Arab-TG",ape:"ape-Latn-ZZ",apr:"apr-Latn-ZZ",aps:"aps-Latn-ZZ",apz:"apz-Latn-ZZ",ar:"ar-Arab-EG",arc:"arc-Armi-IR","arc-Nbat":"arc-Nbat-JO","arc-Palm":"arc-Palm-SY",arh:"arh-Latn-ZZ",arn:"arn-Latn-CL",aro:"aro-Latn-BO",arq:"arq-Arab-DZ",ars:"ars-Arab-SA",ary:"ary-Arab-MA",arz:"arz-Arab-EG",as:"as-Beng-IN",asa:"asa-Latn-TZ",ase:"ase-Sgnw-US",asg:"asg-Latn-ZZ",aso:"aso-Latn-ZZ",ast:"ast-Latn-ES",ata:"ata-Latn-ZZ",atg:"atg-Latn-ZZ",atj:"atj-Latn-CA",auy:"auy-Latn-ZZ",av:"av-Cyrl-RU",avl:"avl-Arab-ZZ",avn:"avn-Latn-ZZ",avt:"avt-Latn-ZZ",avu:"avu-Latn-ZZ",awa:"awa-Deva-IN",awb:"awb-Latn-ZZ",awo:"awo-Latn-ZZ",awx:"awx-Latn-ZZ",ay:"ay-Latn-BO",ayb:"ayb-Latn-ZZ",az:"az-Latn-AZ","az-Arab":"az-Arab-IR","az-IQ":"az-Arab-IQ","az-IR":"az-Arab-IR","az-RU":"az-Cyrl-RU",ba:"ba-Cyrl-RU",bal:"bal-Arab-PK",ban:"ban-Latn-ID",bap:"bap-Deva-NP",bar:"bar-Latn-AT",bas:"bas-Latn-CM",bav:"bav-Latn-ZZ",bax:"bax-Bamu-CM",bba:"bba-Latn-ZZ",bbb:"bbb-Latn-ZZ",bbc:"bbc-Latn-ID",bbd:"bbd-Latn-ZZ",bbj:"bbj-Latn-CM",bbp:"bbp-Latn-ZZ",bbr:"bbr-Latn-ZZ",bcf:"bcf-Latn-ZZ",bch:"bch-Latn-ZZ",bci:"bci-Latn-CI",bcm:"bcm-Latn-ZZ",bcn:"bcn-Latn-ZZ",bco:"bco-Latn-ZZ",bcq:"bcq-Ethi-ZZ",bcu:"bcu-Latn-ZZ",bdd:"bdd-Latn-ZZ",be:"be-Cyrl-BY",bef:"bef-Latn-ZZ",beh:"beh-Latn-ZZ",bej:"bej-Arab-SD",bem:"bem-Latn-ZM",bet:"bet-Latn-ZZ",bew:"bew-Latn-ID",bex:"bex-Latn-ZZ",bez:"bez-Latn-TZ",bfd:"bfd-Latn-CM",bfq:"bfq-Taml-IN",bft:"bft-Arab-PK",bfy:"bfy-Deva-IN",bg:"bg-Cyrl-BG",bgc:"bgc-Deva-IN",bgn:"bgn-Arab-PK",bgx:"bgx-Grek-TR",bhb:"bhb-Deva-IN",bhg:"bhg-Latn-ZZ",bhi:"bhi-Deva-IN",bhl:"bhl-Latn-ZZ",bho:"bho-Deva-IN",bhy:"bhy-Latn-ZZ",bi:"bi-Latn-VU",bib:"bib-Latn-ZZ",big:"big-Latn-ZZ",bik:"bik-Latn-PH",bim:"bim-Latn-ZZ",bin:"bin-Latn-NG",bio:"bio-Latn-ZZ",biq:"biq-Latn-ZZ",bjh:"bjh-Latn-ZZ",bji:"bji-Ethi-ZZ",bjj:"bjj-Deva-IN",bjn:"bjn-Latn-ID",bjo:"bjo-Latn-ZZ",bjr:"bjr-Latn-ZZ",bjt:"bjt-Latn-SN",bjz:"bjz-Latn-ZZ",bkc:"bkc-Latn-ZZ",bkm:"bkm-Latn-CM",bkq:"bkq-Latn-ZZ",bku:"bku-Latn-PH",bkv:"bkv-Latn-ZZ",blt:"blt-Tavt-VN",bm:"bm-Latn-ML",bmh:"bmh-Latn-ZZ",bmk:"bmk-Latn-ZZ",bmq:"bmq-Latn-ML",bmu:"bmu-Latn-ZZ",bn:"bn-Beng-BD",bng:"bng-Latn-ZZ",bnm:"bnm-Latn-ZZ",bnp:"bnp-Latn-ZZ",bo:"bo-Tibt-CN",boj:"boj-Latn-ZZ",bom:"bom-Latn-ZZ",bon:"bon-Latn-ZZ",bpy:"bpy-Beng-IN",bqc:"bqc-Latn-ZZ",bqi:"bqi-Arab-IR",bqp:"bqp-Latn-ZZ",bqv:"bqv-Latn-CI",br:"br-Latn-FR",bra:"bra-Deva-IN",brh:"brh-Arab-PK",brx:"brx-Deva-IN",brz:"brz-Latn-ZZ",bs:"bs-Latn-BA",bsj:"bsj-Latn-ZZ",bsq:"bsq-Bass-LR",bss:"bss-Latn-CM",bst:"bst-Ethi-ZZ",bto:"bto-Latn-PH",btt:"btt-Latn-ZZ",btv:"btv-Deva-PK",bua:"bua-Cyrl-RU",buc:"buc-Latn-YT",bud:"bud-Latn-ZZ",bug:"bug-Latn-ID",buk:"buk-Latn-ZZ",bum:"bum-Latn-CM",buo:"buo-Latn-ZZ",bus:"bus-Latn-ZZ",buu:"buu-Latn-ZZ",bvb:"bvb-Latn-GQ",bwd:"bwd-Latn-ZZ",bwr:"bwr-Latn-ZZ",bxh:"bxh-Latn-ZZ",bye:"bye-Latn-ZZ",byn:"byn-Ethi-ER",byr:"byr-Latn-ZZ",bys:"bys-Latn-ZZ",byv:"byv-Latn-CM",byx:"byx-Latn-ZZ",bza:"bza-Latn-ZZ",bze:"bze-Latn-ML",bzf:"bzf-Latn-ZZ",bzh:"bzh-Latn-ZZ",bzw:"bzw-Latn-ZZ",ca:"ca-Latn-ES",can:"can-Latn-ZZ",cbj:"cbj-Latn-ZZ",cch:"cch-Latn-NG",ccp:"ccp-Cakm-BD",ce:"ce-Cyrl-RU",ceb:"ceb-Latn-PH",cfa:"cfa-Latn-ZZ",cgg:"cgg-Latn-UG",ch:"ch-Latn-GU",chk:"chk-Latn-FM",chm:"chm-Cyrl-RU",cho:"cho-Latn-US",chp:"chp-Latn-CA",chr:"chr-Cher-US",cic:"cic-Latn-US",cja:"cja-Arab-KH",cjm:"cjm-Cham-VN",cjv:"cjv-Latn-ZZ",ckb:"ckb-Arab-IQ",ckl:"ckl-Latn-ZZ",cko:"cko-Latn-ZZ",cky:"cky-Latn-ZZ",cla:"cla-Latn-ZZ",cme:"cme-Latn-ZZ",cmg:"cmg-Soyo-MN",co:"co-Latn-FR",cop:"cop-Copt-EG",cps:"cps-Latn-PH",cr:"cr-Cans-CA",crh:"crh-Cyrl-UA",crj:"crj-Cans-CA",crk:"crk-Cans-CA",crl:"crl-Cans-CA",crm:"crm-Cans-CA",crs:"crs-Latn-SC",cs:"cs-Latn-CZ",csb:"csb-Latn-PL",csw:"csw-Cans-CA",ctd:"ctd-Pauc-MM",cu:"cu-Cyrl-RU","cu-Glag":"cu-Glag-BG",cv:"cv-Cyrl-RU",cy:"cy-Latn-GB",da:"da-Latn-DK",dad:"dad-Latn-ZZ",daf:"daf-Latn-ZZ",dag:"dag-Latn-ZZ",dah:"dah-Latn-ZZ",dak:"dak-Latn-US",dar:"dar-Cyrl-RU",dav:"dav-Latn-KE",dbd:"dbd-Latn-ZZ",dbq:"dbq-Latn-ZZ",dcc:"dcc-Arab-IN",ddn:"ddn-Latn-ZZ",de:"de-Latn-DE",ded:"ded-Latn-ZZ",den:"den-Latn-CA",dga:"dga-Latn-ZZ",dgh:"dgh-Latn-ZZ",dgi:"dgi-Latn-ZZ",dgl:"dgl-Arab-ZZ",dgr:"dgr-Latn-CA",dgz:"dgz-Latn-ZZ",dia:"dia-Latn-ZZ",dje:"dje-Latn-NE",dnj:"dnj-Latn-CI",dob:"dob-Latn-ZZ",doi:"doi-Arab-IN",dop:"dop-Latn-ZZ",dow:"dow-Latn-ZZ",drh:"drh-Mong-CN",dri:"dri-Latn-ZZ",drs:"drs-Ethi-ZZ",dsb:"dsb-Latn-DE",dtm:"dtm-Latn-ML",dtp:"dtp-Latn-MY",dts:"dts-Latn-ZZ",dty:"dty-Deva-NP",dua:"dua-Latn-CM",duc:"duc-Latn-ZZ",dud:"dud-Latn-ZZ",dug:"dug-Latn-ZZ",dv:"dv-Thaa-MV",dva:"dva-Latn-ZZ",dww:"dww-Latn-ZZ",dyo:"dyo-Latn-SN",dyu:"dyu-Latn-BF",dz:"dz-Tibt-BT",dzg:"dzg-Latn-ZZ",ebu:"ebu-Latn-KE",ee:"ee-Latn-GH",efi:"efi-Latn-NG",egl:"egl-Latn-IT",egy:"egy-Egyp-EG",eka:"eka-Latn-ZZ",eky:"eky-Kali-MM",el:"el-Grek-GR",ema:"ema-Latn-ZZ",emi:"emi-Latn-ZZ",en:"en-Latn-US","en-Shaw":"en-Shaw-GB",enn:"enn-Latn-ZZ",enq:"enq-Latn-ZZ",eo:"eo-Latn-001",eri:"eri-Latn-ZZ",es:"es-Latn-ES",esg:"esg-Gonm-IN",esu:"esu-Latn-US",et:"et-Latn-EE",etr:"etr-Latn-ZZ",ett:"ett-Ital-IT",etu:"etu-Latn-ZZ",etx:"etx-Latn-ZZ",eu:"eu-Latn-ES",ewo:"ewo-Latn-CM",ext:"ext-Latn-ES",fa:"fa-Arab-IR",faa:"faa-Latn-ZZ",fab:"fab-Latn-ZZ",fag:"fag-Latn-ZZ",fai:"fai-Latn-ZZ",fan:"fan-Latn-GQ",ff:"ff-Latn-SN","ff-Adlm":"ff-Adlm-GN",ffi:"ffi-Latn-ZZ",ffm:"ffm-Latn-ML",fi:"fi-Latn-FI",fia:"fia-Arab-SD",fil:"fil-Latn-PH",fit:"fit-Latn-SE",fj:"fj-Latn-FJ",flr:"flr-Latn-ZZ",fmp:"fmp-Latn-ZZ",fo:"fo-Latn-FO",fod:"fod-Latn-ZZ",fon:"fon-Latn-BJ",for:"for-Latn-ZZ",fpe:"fpe-Latn-ZZ",fqs:"fqs-Latn-ZZ",fr:"fr-Latn-FR",frc:"frc-Latn-US",frp:"frp-Latn-FR",frr:"frr-Latn-DE",frs:"frs-Latn-DE",fub:"fub-Arab-CM",fud:"fud-Latn-WF",fue:"fue-Latn-ZZ",fuf:"fuf-Latn-GN",fuh:"fuh-Latn-ZZ",fuq:"fuq-Latn-NE",fur:"fur-Latn-IT",fuv:"fuv-Latn-NG",fuy:"fuy-Latn-ZZ",fvr:"fvr-Latn-SD",fy:"fy-Latn-NL",ga:"ga-Latn-IE",gaa:"gaa-Latn-GH",gaf:"gaf-Latn-ZZ",gag:"gag-Latn-MD",gah:"gah-Latn-ZZ",gaj:"gaj-Latn-ZZ",gam:"gam-Latn-ZZ",gan:"gan-Hans-CN",gaw:"gaw-Latn-ZZ",gay:"gay-Latn-ID",gba:"gba-Latn-ZZ",gbf:"gbf-Latn-ZZ",gbm:"gbm-Deva-IN",gby:"gby-Latn-ZZ",gbz:"gbz-Arab-IR",gcr:"gcr-Latn-GF",gd:"gd-Latn-GB",gde:"gde-Latn-ZZ",gdn:"gdn-Latn-ZZ",gdr:"gdr-Latn-ZZ",geb:"geb-Latn-ZZ",gej:"gej-Latn-ZZ",gel:"gel-Latn-ZZ",gez:"gez-Ethi-ET",gfk:"gfk-Latn-ZZ",ggn:"ggn-Deva-NP",ghs:"ghs-Latn-ZZ",gil:"gil-Latn-KI",gim:"gim-Latn-ZZ",gjk:"gjk-Arab-PK",gjn:"gjn-Latn-ZZ",gju:"gju-Arab-PK",gkn:"gkn-Latn-ZZ",gkp:"gkp-Latn-ZZ",gl:"gl-Latn-ES",glk:"glk-Arab-IR",gmm:"gmm-Latn-ZZ",gmv:"gmv-Ethi-ZZ",gn:"gn-Latn-PY",gnd:"gnd-Latn-ZZ",gng:"gng-Latn-ZZ",god:"god-Latn-ZZ",gof:"gof-Ethi-ZZ",goi:"goi-Latn-ZZ",gom:"gom-Deva-IN",gon:"gon-Telu-IN",gor:"gor-Latn-ID",gos:"gos-Latn-NL",got:"got-Goth-UA",grb:"grb-Latn-ZZ",grc:"grc-Cprt-CY","grc-Linb":"grc-Linb-GR",grt:"grt-Beng-IN",grw:"grw-Latn-ZZ",gsw:"gsw-Latn-CH",gu:"gu-Gujr-IN",gub:"gub-Latn-BR",guc:"guc-Latn-CO",gud:"gud-Latn-ZZ",gur:"gur-Latn-GH",guw:"guw-Latn-ZZ",gux:"gux-Latn-ZZ",guz:"guz-Latn-KE",gv:"gv-Latn-IM",gvf:"gvf-Latn-ZZ",gvr:"gvr-Deva-NP",gvs:"gvs-Latn-ZZ",gwc:"gwc-Arab-ZZ",gwi:"gwi-Latn-CA",gwt:"gwt-Arab-ZZ",gyi:"gyi-Latn-ZZ",ha:"ha-Latn-NG","ha-CM":"ha-Arab-CM","ha-SD":"ha-Arab-SD",hag:"hag-Latn-ZZ",hak:"hak-Hans-CN",ham:"ham-Latn-ZZ",haw:"haw-Latn-US",haz:"haz-Arab-AF",hbb:"hbb-Latn-ZZ",hdy:"hdy-Ethi-ZZ",he:"he-Hebr-IL",hhy:"hhy-Latn-ZZ",hi:"hi-Deva-IN",hia:"hia-Latn-ZZ",hif:"hif-Latn-FJ",hig:"hig-Latn-ZZ",hih:"hih-Latn-ZZ",hil:"hil-Latn-PH",hla:"hla-Latn-ZZ",hlu:"hlu-Hluw-TR",hmd:"hmd-Plrd-CN",hmt:"hmt-Latn-ZZ",hnd:"hnd-Arab-PK",hne:"hne-Deva-IN",hnj:"hnj-Hmng-LA",hnn:"hnn-Latn-PH",hno:"hno-Arab-PK",ho:"ho-Latn-PG",hoc:"hoc-Deva-IN",hoj:"hoj-Deva-IN",hot:"hot-Latn-ZZ",hr:"hr-Latn-HR",hsb:"hsb-Latn-DE",hsn:"hsn-Hans-CN",ht:"ht-Latn-HT",hu:"hu-Latn-HU",hui:"hui-Latn-ZZ",hy:"hy-Armn-AM",hz:"hz-Latn-NA",ia:"ia-Latn-001",ian:"ian-Latn-ZZ",iar:"iar-Latn-ZZ",iba:"iba-Latn-MY",ibb:"ibb-Latn-NG",iby:"iby-Latn-ZZ",ica:"ica-Latn-ZZ",ich:"ich-Latn-ZZ",id:"id-Latn-ID",idd:"idd-Latn-ZZ",idi:"idi-Latn-ZZ",idu:"idu-Latn-ZZ",ife:"ife-Latn-TG",ig:"ig-Latn-NG",igb:"igb-Latn-ZZ",ige:"ige-Latn-ZZ",ii:"ii-Yiii-CN",ijj:"ijj-Latn-ZZ",ik:"ik-Latn-US",ikk:"ikk-Latn-ZZ",ikt:"ikt-Latn-CA",ikw:"ikw-Latn-ZZ",ikx:"ikx-Latn-ZZ",ilo:"ilo-Latn-PH",imo:"imo-Latn-ZZ",in:"in-Latn-ID",inh:"inh-Cyrl-RU",io:"io-Latn-001",iou:"iou-Latn-ZZ",iri:"iri-Latn-ZZ",is:"is-Latn-IS",it:"it-Latn-IT",iu:"iu-Cans-CA",iw:"iw-Hebr-IL",iwm:"iwm-Latn-ZZ",iws:"iws-Latn-ZZ",izh:"izh-Latn-RU",izi:"izi-Latn-ZZ",ja:"ja-Jpan-JP",jab:"jab-Latn-ZZ",jam:"jam-Latn-JM",jbo:"jbo-Latn-001",jbu:"jbu-Latn-ZZ",jen:"jen-Latn-ZZ",jgk:"jgk-Latn-ZZ",jgo:"jgo-Latn-CM",ji:"ji-Hebr-UA",jib:"jib-Latn-ZZ",jmc:"jmc-Latn-TZ",jml:"jml-Deva-NP",jra:"jra-Latn-ZZ",jut:"jut-Latn-DK",jv:"jv-Latn-ID",jw:"jw-Latn-ID",ka:"ka-Geor-GE",kaa:"kaa-Cyrl-UZ",kab:"kab-Latn-DZ",kac:"kac-Latn-MM",kad:"kad-Latn-ZZ",kai:"kai-Latn-ZZ",kaj:"kaj-Latn-NG",kam:"kam-Latn-KE",kao:"kao-Latn-ML",kbd:"kbd-Cyrl-RU",kbm:"kbm-Latn-ZZ",kbp:"kbp-Latn-ZZ",kbq:"kbq-Latn-ZZ",kbx:"kbx-Latn-ZZ",kby:"kby-Arab-NE",kcg:"kcg-Latn-NG",kck:"kck-Latn-ZW",kcl:"kcl-Latn-ZZ",kct:"kct-Latn-ZZ",kde:"kde-Latn-TZ",kdh:"kdh-Arab-TG",kdl:"kdl-Latn-ZZ",kdt:"kdt-Thai-TH",kea:"kea-Latn-CV",ken:"ken-Latn-CM",kez:"kez-Latn-ZZ",kfo:"kfo-Latn-CI",kfr:"kfr-Deva-IN",kfy:"kfy-Deva-IN",kg:"kg-Latn-CD",kge:"kge-Latn-ID",kgf:"kgf-Latn-ZZ",kgp:"kgp-Latn-BR",kha:"kha-Latn-IN",khb:"khb-Talu-CN",khn:"khn-Deva-IN",khq:"khq-Latn-ML",khs:"khs-Latn-ZZ",kht:"kht-Mymr-IN",khw:"khw-Arab-PK",khz:"khz-Latn-ZZ",ki:"ki-Latn-KE",kij:"kij-Latn-ZZ",kiu:"kiu-Latn-TR",kiw:"kiw-Latn-ZZ",kj:"kj-Latn-NA",kjd:"kjd-Latn-ZZ",kjg:"kjg-Laoo-LA",kjs:"kjs-Latn-ZZ",kjy:"kjy-Latn-ZZ",kk:"kk-Cyrl-KZ","kk-AF":"kk-Arab-AF","kk-Arab":"kk-Arab-CN","kk-CN":"kk-Arab-CN","kk-IR":"kk-Arab-IR","kk-MN":"kk-Arab-MN",kkc:"kkc-Latn-ZZ",kkj:"kkj-Latn-CM",kl:"kl-Latn-GL",kln:"kln-Latn-KE",klq:"klq-Latn-ZZ",klt:"klt-Latn-ZZ",klx:"klx-Latn-ZZ",km:"km-Khmr-KH",kmb:"kmb-Latn-AO",kmh:"kmh-Latn-ZZ",kmo:"kmo-Latn-ZZ",kms:"kms-Latn-ZZ",kmu:"kmu-Latn-ZZ",kmw:"kmw-Latn-ZZ",kn:"kn-Knda-IN",knf:"knf-Latn-GW",knp:"knp-Latn-ZZ",ko:"ko-Kore-KR",koi:"koi-Cyrl-RU",kok:"kok-Deva-IN",kol:"kol-Latn-ZZ",kos:"kos-Latn-FM",koz:"koz-Latn-ZZ",kpe:"kpe-Latn-LR",kpf:"kpf-Latn-ZZ",kpo:"kpo-Latn-ZZ",kpr:"kpr-Latn-ZZ",kpx:"kpx-Latn-ZZ",kqb:"kqb-Latn-ZZ",kqf:"kqf-Latn-ZZ",kqs:"kqs-Latn-ZZ",kqy:"kqy-Ethi-ZZ",kr:"kr-Latn-ZZ",krc:"krc-Cyrl-RU",kri:"kri-Latn-SL",krj:"krj-Latn-PH",krl:"krl-Latn-RU",krs:"krs-Latn-ZZ",kru:"kru-Deva-IN",ks:"ks-Arab-IN",ksb:"ksb-Latn-TZ",ksd:"ksd-Latn-ZZ",ksf:"ksf-Latn-CM",ksh:"ksh-Latn-DE",ksj:"ksj-Latn-ZZ",ksr:"ksr-Latn-ZZ",ktb:"ktb-Ethi-ZZ",ktm:"ktm-Latn-ZZ",kto:"kto-Latn-ZZ",ktr:"ktr-Latn-MY",ku:"ku-Latn-TR","ku-Arab":"ku-Arab-IQ","ku-LB":"ku-Arab-LB",kub:"kub-Latn-ZZ",kud:"kud-Latn-ZZ",kue:"kue-Latn-ZZ",kuj:"kuj-Latn-ZZ",kum:"kum-Cyrl-RU",kun:"kun-Latn-ZZ",kup:"kup-Latn-ZZ",kus:"kus-Latn-ZZ",kv:"kv-Cyrl-RU",kvg:"kvg-Latn-ZZ",kvr:"kvr-Latn-ID",kvx:"kvx-Arab-PK",kw:"kw-Latn-GB",kwj:"kwj-Latn-ZZ",kwo:"kwo-Latn-ZZ",kwq:"kwq-Latn-ZZ",kxa:"kxa-Latn-ZZ",kxc:"kxc-Ethi-ZZ",kxe:"kxe-Latn-ZZ",kxm:"kxm-Thai-TH",kxp:"kxp-Arab-PK",kxw:"kxw-Latn-ZZ",kxz:"kxz-Latn-ZZ",ky:"ky-Cyrl-KG","ky-Arab":"ky-Arab-CN","ky-CN":"ky-Arab-CN","ky-Latn":"ky-Latn-TR","ky-TR":"ky-Latn-TR",kye:"kye-Latn-ZZ",kyx:"kyx-Latn-ZZ",kzj:"kzj-Latn-MY",kzr:"kzr-Latn-ZZ",kzt:"kzt-Latn-MY",la:"la-Latn-VA",lab:"lab-Lina-GR",lad:"lad-Hebr-IL",lag:"lag-Latn-TZ",lah:"lah-Arab-PK",laj:"laj-Latn-UG",las:"las-Latn-ZZ",lb:"lb-Latn-LU",lbe:"lbe-Cyrl-RU",lbu:"lbu-Latn-ZZ",lbw:"lbw-Latn-ID",lcm:"lcm-Latn-ZZ",lcp:"lcp-Thai-CN",ldb:"ldb-Latn-ZZ",led:"led-Latn-ZZ",lee:"lee-Latn-ZZ",lem:"lem-Latn-ZZ",lep:"lep-Lepc-IN",leq:"leq-Latn-ZZ",leu:"leu-Latn-ZZ",lez:"lez-Cyrl-RU",lg:"lg-Latn-UG",lgg:"lgg-Latn-ZZ",li:"li-Latn-NL",lia:"lia-Latn-ZZ",lid:"lid-Latn-ZZ",lif:"lif-Deva-NP","lif-Limb":"lif-Limb-IN",lig:"lig-Latn-ZZ",lih:"lih-Latn-ZZ",lij:"lij-Latn-IT",lis:"lis-Lisu-CN",ljp:"ljp-Latn-ID",lki:"lki-Arab-IR",lkt:"lkt-Latn-US",lle:"lle-Latn-ZZ",lln:"lln-Latn-ZZ",lmn:"lmn-Telu-IN",lmo:"lmo-Latn-IT",lmp:"lmp-Latn-ZZ",ln:"ln-Latn-CD",lns:"lns-Latn-ZZ",lnu:"lnu-Latn-ZZ",lo:"lo-Laoo-LA",loj:"loj-Latn-ZZ",lok:"lok-Latn-ZZ",lol:"lol-Latn-CD",lor:"lor-Latn-ZZ",los:"los-Latn-ZZ",loz:"loz-Latn-ZM",lrc:"lrc-Arab-IR",lt:"lt-Latn-LT",ltg:"ltg-Latn-LV",lu:"lu-Latn-CD",lua:"lua-Latn-CD",luo:"luo-Latn-KE",luy:"luy-Latn-KE",luz:"luz-Arab-IR",lv:"lv-Latn-LV",lwl:"lwl-Thai-TH",lzh:"lzh-Hans-CN",lzz:"lzz-Latn-TR",mad:"mad-Latn-ID",maf:"maf-Latn-CM",mag:"mag-Deva-IN",mai:"mai-Deva-IN",mak:"mak-Latn-ID",man:"man-Latn-GM","man-GN":"man-Nkoo-GN","man-Nkoo":"man-Nkoo-GN",mas:"mas-Latn-KE",maw:"maw-Latn-ZZ",maz:"maz-Latn-MX",mbh:"mbh-Latn-ZZ",mbo:"mbo-Latn-ZZ",mbq:"mbq-Latn-ZZ",mbu:"mbu-Latn-ZZ",mbw:"mbw-Latn-ZZ",mci:"mci-Latn-ZZ",mcp:"mcp-Latn-ZZ",mcq:"mcq-Latn-ZZ",mcr:"mcr-Latn-ZZ",mcu:"mcu-Latn-ZZ",mda:"mda-Latn-ZZ",mde:"mde-Arab-ZZ",mdf:"mdf-Cyrl-RU",mdh:"mdh-Latn-PH",mdj:"mdj-Latn-ZZ",mdr:"mdr-Latn-ID",mdx:"mdx-Ethi-ZZ",med:"med-Latn-ZZ",mee:"mee-Latn-ZZ",mek:"mek-Latn-ZZ",men:"men-Latn-SL",mer:"mer-Latn-KE",met:"met-Latn-ZZ",meu:"meu-Latn-ZZ",mfa:"mfa-Arab-TH",mfe:"mfe-Latn-MU",mfn:"mfn-Latn-ZZ",mfo:"mfo-Latn-ZZ",mfq:"mfq-Latn-ZZ",mg:"mg-Latn-MG",mgh:"mgh-Latn-MZ",mgl:"mgl-Latn-ZZ",mgo:"mgo-Latn-CM",mgp:"mgp-Deva-NP",mgy:"mgy-Latn-TZ",mh:"mh-Latn-MH",mhi:"mhi-Latn-ZZ",mhl:"mhl-Latn-ZZ",mi:"mi-Latn-NZ",mif:"mif-Latn-ZZ",min:"min-Latn-ID",mis:"mis-Hatr-IQ","mis-Medf":"mis-Medf-NG",miw:"miw-Latn-ZZ",mk:"mk-Cyrl-MK",mki:"mki-Arab-ZZ",mkl:"mkl-Latn-ZZ",mkp:"mkp-Latn-ZZ",mkw:"mkw-Latn-ZZ",ml:"ml-Mlym-IN",mle:"mle-Latn-ZZ",mlp:"mlp-Latn-ZZ",mls:"mls-Latn-SD",mmo:"mmo-Latn-ZZ",mmu:"mmu-Latn-ZZ",mmx:"mmx-Latn-ZZ",mn:"mn-Cyrl-MN","mn-CN":"mn-Mong-CN","mn-Mong":"mn-Mong-CN",mna:"mna-Latn-ZZ",mnf:"mnf-Latn-ZZ",mni:"mni-Beng-IN",mnw:"mnw-Mymr-MM",mo:"mo-Latn-RO",moa:"moa-Latn-ZZ",moe:"moe-Latn-CA",moh:"moh-Latn-CA",mos:"mos-Latn-BF",mox:"mox-Latn-ZZ",mpp:"mpp-Latn-ZZ",mps:"mps-Latn-ZZ",mpt:"mpt-Latn-ZZ",mpx:"mpx-Latn-ZZ",mql:"mql-Latn-ZZ",mr:"mr-Deva-IN",mrd:"mrd-Deva-NP",mrj:"mrj-Cyrl-RU",mro:"mro-Mroo-BD",ms:"ms-Latn-MY","ms-CC":"ms-Arab-CC","ms-ID":"ms-Arab-ID",mt:"mt-Latn-MT",mtc:"mtc-Latn-ZZ",mtf:"mtf-Latn-ZZ",mti:"mti-Latn-ZZ",mtr:"mtr-Deva-IN",mua:"mua-Latn-CM",mur:"mur-Latn-ZZ",mus:"mus-Latn-US",mva:"mva-Latn-ZZ",mvn:"mvn-Latn-ZZ",mvy:"mvy-Arab-PK",mwk:"mwk-Latn-ML",mwr:"mwr-Deva-IN",mwv:"mwv-Latn-ID",mww:"mww-Hmnp-US",mxc:"mxc-Latn-ZW",mxm:"mxm-Latn-ZZ",my:"my-Mymr-MM",myk:"myk-Latn-ZZ",mym:"mym-Ethi-ZZ",myv:"myv-Cyrl-RU",myw:"myw-Latn-ZZ",myx:"myx-Latn-UG",myz:"myz-Mand-IR",mzk:"mzk-Latn-ZZ",mzm:"mzm-Latn-ZZ",mzn:"mzn-Arab-IR",mzp:"mzp-Latn-ZZ",mzw:"mzw-Latn-ZZ",mzz:"mzz-Latn-ZZ",na:"na-Latn-NR",nac:"nac-Latn-ZZ",naf:"naf-Latn-ZZ",nak:"nak-Latn-ZZ",nan:"nan-Hans-CN",nap:"nap-Latn-IT",naq:"naq-Latn-NA",nas:"nas-Latn-ZZ",nb:"nb-Latn-NO",nca:"nca-Latn-ZZ",nce:"nce-Latn-ZZ",ncf:"ncf-Latn-ZZ",nch:"nch-Latn-MX",nco:"nco-Latn-ZZ",ncu:"ncu-Latn-ZZ",nd:"nd-Latn-ZW",ndc:"ndc-Latn-MZ",nds:"nds-Latn-DE",ne:"ne-Deva-NP",neb:"neb-Latn-ZZ",new:"new-Deva-NP",nex:"nex-Latn-ZZ",nfr:"nfr-Latn-ZZ",ng:"ng-Latn-NA",nga:"nga-Latn-ZZ",ngb:"ngb-Latn-ZZ",ngl:"ngl-Latn-MZ",nhb:"nhb-Latn-ZZ",nhe:"nhe-Latn-MX",nhw:"nhw-Latn-MX",nif:"nif-Latn-ZZ",nii:"nii-Latn-ZZ",nij:"nij-Latn-ID",nin:"nin-Latn-ZZ",niu:"niu-Latn-NU",niy:"niy-Latn-ZZ",niz:"niz-Latn-ZZ",njo:"njo-Latn-IN",nkg:"nkg-Latn-ZZ",nko:"nko-Latn-ZZ",nl:"nl-Latn-NL",nmg:"nmg-Latn-CM",nmz:"nmz-Latn-ZZ",nn:"nn-Latn-NO",nnf:"nnf-Latn-ZZ",nnh:"nnh-Latn-CM",nnk:"nnk-Latn-ZZ",nnm:"nnm-Latn-ZZ",nnp:"nnp-Wcho-IN",no:"no-Latn-NO",nod:"nod-Lana-TH",noe:"noe-Deva-IN",non:"non-Runr-SE",nop:"nop-Latn-ZZ",nou:"nou-Latn-ZZ",nqo:"nqo-Nkoo-GN",nr:"nr-Latn-ZA",nrb:"nrb-Latn-ZZ",nsk:"nsk-Cans-CA",nsn:"nsn-Latn-ZZ",nso:"nso-Latn-ZA",nss:"nss-Latn-ZZ",ntm:"ntm-Latn-ZZ",ntr:"ntr-Latn-ZZ",nui:"nui-Latn-ZZ",nup:"nup-Latn-ZZ",nus:"nus-Latn-SS",nuv:"nuv-Latn-ZZ",nux:"nux-Latn-ZZ",nv:"nv-Latn-US",nwb:"nwb-Latn-ZZ",nxq:"nxq-Latn-CN",nxr:"nxr-Latn-ZZ",ny:"ny-Latn-MW",nym:"nym-Latn-TZ",nyn:"nyn-Latn-UG",nzi:"nzi-Latn-GH",oc:"oc-Latn-FR",ogc:"ogc-Latn-ZZ",okr:"okr-Latn-ZZ",okv:"okv-Latn-ZZ",om:"om-Latn-ET",ong:"ong-Latn-ZZ",onn:"onn-Latn-ZZ",ons:"ons-Latn-ZZ",opm:"opm-Latn-ZZ",or:"or-Orya-IN",oro:"oro-Latn-ZZ",oru:"oru-Arab-ZZ",os:"os-Cyrl-GE",osa:"osa-Osge-US",ota:"ota-Arab-ZZ",otk:"otk-Orkh-MN",ozm:"ozm-Latn-ZZ",pa:"pa-Guru-IN","pa-Arab":"pa-Arab-PK","pa-PK":"pa-Arab-PK",pag:"pag-Latn-PH",pal:"pal-Phli-IR","pal-Phlp":"pal-Phlp-CN",pam:"pam-Latn-PH",pap:"pap-Latn-AW",pau:"pau-Latn-PW",pbi:"pbi-Latn-ZZ",pcd:"pcd-Latn-FR",pcm:"pcm-Latn-NG",pdc:"pdc-Latn-US",pdt:"pdt-Latn-CA",ped:"ped-Latn-ZZ",peo:"peo-Xpeo-IR",pex:"pex-Latn-ZZ",pfl:"pfl-Latn-DE",phl:"phl-Arab-ZZ",phn:"phn-Phnx-LB",pil:"pil-Latn-ZZ",pip:"pip-Latn-ZZ",pka:"pka-Brah-IN",pko:"pko-Latn-KE",pl:"pl-Latn-PL",pla:"pla-Latn-ZZ",pms:"pms-Latn-IT",png:"png-Latn-ZZ",pnn:"pnn-Latn-ZZ",pnt:"pnt-Grek-GR",pon:"pon-Latn-FM",ppa:"ppa-Deva-IN",ppo:"ppo-Latn-ZZ",pra:"pra-Khar-PK",prd:"prd-Arab-IR",prg:"prg-Latn-001",ps:"ps-Arab-AF",pss:"pss-Latn-ZZ",pt:"pt-Latn-BR",ptp:"ptp-Latn-ZZ",puu:"puu-Latn-GA",pwa:"pwa-Latn-ZZ",qu:"qu-Latn-PE",quc:"quc-Latn-GT",qug:"qug-Latn-EC",rai:"rai-Latn-ZZ",raj:"raj-Deva-IN",rao:"rao-Latn-ZZ",rcf:"rcf-Latn-RE",rej:"rej-Latn-ID",rel:"rel-Latn-ZZ",res:"res-Latn-ZZ",rgn:"rgn-Latn-IT",rhg:"rhg-Arab-MM",ria:"ria-Latn-IN",rif:"rif-Tfng-MA","rif-NL":"rif-Latn-NL",rjs:"rjs-Deva-NP",rkt:"rkt-Beng-BD",rm:"rm-Latn-CH",rmf:"rmf-Latn-FI",rmo:"rmo-Latn-CH",rmt:"rmt-Arab-IR",rmu:"rmu-Latn-SE",rn:"rn-Latn-BI",rna:"rna-Latn-ZZ",rng:"rng-Latn-MZ",ro:"ro-Latn-RO",rob:"rob-Latn-ID",rof:"rof-Latn-TZ",roo:"roo-Latn-ZZ",rro:"rro-Latn-ZZ",rtm:"rtm-Latn-FJ",ru:"ru-Cyrl-RU",rue:"rue-Cyrl-UA",rug:"rug-Latn-SB",rw:"rw-Latn-RW",rwk:"rwk-Latn-TZ",rwo:"rwo-Latn-ZZ",ryu:"ryu-Kana-JP",sa:"sa-Deva-IN",saf:"saf-Latn-GH",sah:"sah-Cyrl-RU",saq:"saq-Latn-KE",sas:"sas-Latn-ID",sat:"sat-Latn-IN",sav:"sav-Latn-SN",saz:"saz-Saur-IN",sba:"sba-Latn-ZZ",sbe:"sbe-Latn-ZZ",sbp:"sbp-Latn-TZ",sc:"sc-Latn-IT",sck:"sck-Deva-IN",scl:"scl-Arab-ZZ",scn:"scn-Latn-IT",sco:"sco-Latn-GB",scs:"scs-Latn-CA",sd:"sd-Arab-PK","sd-Deva":"sd-Deva-IN","sd-Khoj":"sd-Khoj-IN","sd-Sind":"sd-Sind-IN",sdc:"sdc-Latn-IT",sdh:"sdh-Arab-IR",se:"se-Latn-NO",sef:"sef-Latn-CI",seh:"seh-Latn-MZ",sei:"sei-Latn-MX",ses:"ses-Latn-ML",sg:"sg-Latn-CF",sga:"sga-Ogam-IE",sgs:"sgs-Latn-LT",sgw:"sgw-Ethi-ZZ",sgz:"sgz-Latn-ZZ",shi:"shi-Tfng-MA",shk:"shk-Latn-ZZ",shn:"shn-Mymr-MM",shu:"shu-Arab-ZZ",si:"si-Sinh-LK",sid:"sid-Latn-ET",sig:"sig-Latn-ZZ",sil:"sil-Latn-ZZ",sim:"sim-Latn-ZZ",sjr:"sjr-Latn-ZZ",sk:"sk-Latn-SK",skc:"skc-Latn-ZZ",skr:"skr-Arab-PK",sks:"sks-Latn-ZZ",sl:"sl-Latn-SI",sld:"sld-Latn-ZZ",sli:"sli-Latn-PL",sll:"sll-Latn-ZZ",sly:"sly-Latn-ID",sm:"sm-Latn-WS",sma:"sma-Latn-SE",smj:"smj-Latn-SE",smn:"smn-Latn-FI",smp:"smp-Samr-IL",smq:"smq-Latn-ZZ",sms:"sms-Latn-FI",sn:"sn-Latn-ZW",snc:"snc-Latn-ZZ",snk:"snk-Latn-ML",snp:"snp-Latn-ZZ",snx:"snx-Latn-ZZ",sny:"sny-Latn-ZZ",so:"so-Latn-SO",sog:"sog-Sogd-UZ",sok:"sok-Latn-ZZ",soq:"soq-Latn-ZZ",sou:"sou-Thai-TH",soy:"soy-Latn-ZZ",spd:"spd-Latn-ZZ",spl:"spl-Latn-ZZ",sps:"sps-Latn-ZZ",sq:"sq-Latn-AL",sr:"sr-Cyrl-RS","sr-ME":"sr-Latn-ME","sr-RO":"sr-Latn-RO","sr-RU":"sr-Latn-RU","sr-TR":"sr-Latn-TR",srb:"srb-Sora-IN",srn:"srn-Latn-SR",srr:"srr-Latn-SN",srx:"srx-Deva-IN",ss:"ss-Latn-ZA",ssd:"ssd-Latn-ZZ",ssg:"ssg-Latn-ZZ",ssy:"ssy-Latn-ER",st:"st-Latn-ZA",stk:"stk-Latn-ZZ",stq:"stq-Latn-DE",su:"su-Latn-ID",sua:"sua-Latn-ZZ",sue:"sue-Latn-ZZ",suk:"suk-Latn-TZ",sur:"sur-Latn-ZZ",sus:"sus-Latn-GN",sv:"sv-Latn-SE",sw:"sw-Latn-TZ",swb:"swb-Arab-YT",swc:"swc-Latn-CD",swg:"swg-Latn-DE",swp:"swp-Latn-ZZ",swv:"swv-Deva-IN",sxn:"sxn-Latn-ID",sxw:"sxw-Latn-ZZ",syl:"syl-Beng-BD",syr:"syr-Syrc-IQ",szl:"szl-Latn-PL",ta:"ta-Taml-IN",taj:"taj-Deva-NP",tal:"tal-Latn-ZZ",tan:"tan-Latn-ZZ",taq:"taq-Latn-ZZ",tbc:"tbc-Latn-ZZ",tbd:"tbd-Latn-ZZ",tbf:"tbf-Latn-ZZ",tbg:"tbg-Latn-ZZ",tbo:"tbo-Latn-ZZ",tbw:"tbw-Latn-PH",tbz:"tbz-Latn-ZZ",tci:"tci-Latn-ZZ",tcy:"tcy-Knda-IN",tdd:"tdd-Tale-CN",tdg:"tdg-Deva-NP",tdh:"tdh-Deva-NP",tdu:"tdu-Latn-MY",te:"te-Telu-IN",ted:"ted-Latn-ZZ",tem:"tem-Latn-SL",teo:"teo-Latn-UG",tet:"tet-Latn-TL",tfi:"tfi-Latn-ZZ",tg:"tg-Cyrl-TJ","tg-Arab":"tg-Arab-PK","tg-PK":"tg-Arab-PK",tgc:"tgc-Latn-ZZ",tgo:"tgo-Latn-ZZ",tgu:"tgu-Latn-ZZ",th:"th-Thai-TH",thl:"thl-Deva-NP",thq:"thq-Deva-NP",thr:"thr-Deva-NP",ti:"ti-Ethi-ET",tif:"tif-Latn-ZZ",tig:"tig-Ethi-ER",tik:"tik-Latn-ZZ",tim:"tim-Latn-ZZ",tio:"tio-Latn-ZZ",tiv:"tiv-Latn-NG",tk:"tk-Latn-TM",tkl:"tkl-Latn-TK",tkr:"tkr-Latn-AZ",tkt:"tkt-Deva-NP",tl:"tl-Latn-PH",tlf:"tlf-Latn-ZZ",tlx:"tlx-Latn-ZZ",tly:"tly-Latn-AZ",tmh:"tmh-Latn-NE",tmy:"tmy-Latn-ZZ",tn:"tn-Latn-ZA",tnh:"tnh-Latn-ZZ",to:"to-Latn-TO",tof:"tof-Latn-ZZ",tog:"tog-Latn-MW",toq:"toq-Latn-ZZ",tpi:"tpi-Latn-PG",tpm:"tpm-Latn-ZZ",tpz:"tpz-Latn-ZZ",tqo:"tqo-Latn-ZZ",tr:"tr-Latn-TR",tru:"tru-Latn-TR",trv:"trv-Latn-TW",trw:"trw-Arab-ZZ",ts:"ts-Latn-ZA",tsd:"tsd-Grek-GR",tsf:"tsf-Deva-NP",tsg:"tsg-Latn-PH",tsj:"tsj-Tibt-BT",tsw:"tsw-Latn-ZZ",tt:"tt-Cyrl-RU",ttd:"ttd-Latn-ZZ",tte:"tte-Latn-ZZ",ttj:"ttj-Latn-UG",ttr:"ttr-Latn-ZZ",tts:"tts-Thai-TH",ttt:"ttt-Latn-AZ",tuh:"tuh-Latn-ZZ",tul:"tul-Latn-ZZ",tum:"tum-Latn-MW",tuq:"tuq-Latn-ZZ",tvd:"tvd-Latn-ZZ",tvl:"tvl-Latn-TV",tvu:"tvu-Latn-ZZ",twh:"twh-Latn-ZZ",twq:"twq-Latn-NE",txg:"txg-Tang-CN",ty:"ty-Latn-PF",tya:"tya-Latn-ZZ",tyv:"tyv-Cyrl-RU",tzm:"tzm-Latn-MA",ubu:"ubu-Latn-ZZ",udm:"udm-Cyrl-RU",ug:"ug-Arab-CN","ug-Cyrl":"ug-Cyrl-KZ","ug-KZ":"ug-Cyrl-KZ","ug-MN":"ug-Cyrl-MN",uga:"uga-Ugar-SY",uk:"uk-Cyrl-UA",uli:"uli-Latn-FM",umb:"umb-Latn-AO",und:"en-Latn-US","und-002":"en-Latn-NG","und-003":"en-Latn-US","und-005":"pt-Latn-BR","und-009":"en-Latn-AU","und-011":"en-Latn-NG","und-013":"es-Latn-MX","und-014":"sw-Latn-TZ","und-015":"ar-Arab-EG","und-017":"sw-Latn-CD","und-018":"en-Latn-ZA","und-019":"en-Latn-US","und-021":"en-Latn-US","und-029":"es-Latn-CU","und-030":"zh-Hans-CN","und-034":"hi-Deva-IN","und-035":"id-Latn-ID","und-039":"it-Latn-IT","und-053":"en-Latn-AU","und-054":"en-Latn-PG","und-057":"en-Latn-GU","und-061":"sm-Latn-WS","und-142":"zh-Hans-CN","und-143":"uz-Latn-UZ","und-145":"ar-Arab-SA","und-150":"ru-Cyrl-RU","und-151":"ru-Cyrl-RU","und-154":"en-Latn-GB","und-155":"de-Latn-DE","und-202":"en-Latn-NG","und-419":"es-Latn-419","und-AD":"ca-Latn-AD","und-Adlm":"ff-Adlm-GN","und-AE":"ar-Arab-AE","und-AF":"fa-Arab-AF","und-Aghb":"lez-Aghb-RU","und-Ahom":"aho-Ahom-IN","und-AL":"sq-Latn-AL","und-AM":"hy-Armn-AM","und-AO":"pt-Latn-AO","und-AQ":"und-Latn-AQ","und-AR":"es-Latn-AR","und-Arab":"ar-Arab-EG","und-Arab-CC":"ms-Arab-CC","und-Arab-CN":"ug-Arab-CN","und-Arab-GB":"ks-Arab-GB","und-Arab-ID":"ms-Arab-ID","und-Arab-IN":"ur-Arab-IN","und-Arab-KH":"cja-Arab-KH","und-Arab-MM":"rhg-Arab-MM","und-Arab-MN":"kk-Arab-MN","und-Arab-MU":"ur-Arab-MU","und-Arab-NG":"ha-Arab-NG","und-Arab-PK":"ur-Arab-PK","und-Arab-TG":"apd-Arab-TG","und-Arab-TH":"mfa-Arab-TH","und-Arab-TJ":"fa-Arab-TJ","und-Arab-TR":"az-Arab-TR","und-Arab-YT":"swb-Arab-YT","und-Armi":"arc-Armi-IR","und-Armn":"hy-Armn-AM","und-AS":"sm-Latn-AS","und-AT":"de-Latn-AT","und-Avst":"ae-Avst-IR","und-AW":"nl-Latn-AW","und-AX":"sv-Latn-AX","und-AZ":"az-Latn-AZ","und-BA":"bs-Latn-BA","und-Bali":"ban-Bali-ID","und-Bamu":"bax-Bamu-CM","und-Bass":"bsq-Bass-LR","und-Batk":"bbc-Batk-ID","und-BD":"bn-Beng-BD","und-BE":"nl-Latn-BE","und-Beng":"bn-Beng-BD","und-BF":"fr-Latn-BF","und-BG":"bg-Cyrl-BG","und-BH":"ar-Arab-BH","und-Bhks":"sa-Bhks-IN","und-BI":"rn-Latn-BI","und-BJ":"fr-Latn-BJ","und-BL":"fr-Latn-BL","und-BN":"ms-Latn-BN","und-BO":"es-Latn-BO","und-Bopo":"zh-Bopo-TW","und-BQ":"pap-Latn-BQ","und-BR":"pt-Latn-BR","und-Brah":"pka-Brah-IN","und-Brai":"fr-Brai-FR","und-BT":"dz-Tibt-BT","und-Bugi":"bug-Bugi-ID","und-Buhd":"bku-Buhd-PH","und-BV":"und-Latn-BV","und-BY":"be-Cyrl-BY","und-Cakm":"ccp-Cakm-BD","und-Cans":"cr-Cans-CA","und-Cari":"xcr-Cari-TR","und-CD":"sw-Latn-CD","und-CF":"fr-Latn-CF","und-CG":"fr-Latn-CG","und-CH":"de-Latn-CH","und-Cham":"cjm-Cham-VN","und-Cher":"chr-Cher-US","und-CI":"fr-Latn-CI","und-CL":"es-Latn-CL","und-CM":"fr-Latn-CM","und-CN":"zh-Hans-CN","und-CO":"es-Latn-CO","und-Copt":"cop-Copt-EG","und-CP":"und-Latn-CP","und-Cprt":"grc-Cprt-CY","und-CR":"es-Latn-CR","und-CU":"es-Latn-CU","und-CV":"pt-Latn-CV","und-CW":"pap-Latn-CW","und-CY":"el-Grek-CY","und-Cyrl":"ru-Cyrl-RU","und-Cyrl-AL":"mk-Cyrl-AL","und-Cyrl-BA":"sr-Cyrl-BA","und-Cyrl-GE":"ab-Cyrl-GE","und-Cyrl-GR":"mk-Cyrl-GR","und-Cyrl-MD":"uk-Cyrl-MD","und-Cyrl-RO":"bg-Cyrl-RO","und-Cyrl-SK":"uk-Cyrl-SK","und-Cyrl-TR":"kbd-Cyrl-TR","und-Cyrl-XK":"sr-Cyrl-XK","und-CZ":"cs-Latn-CZ","und-DE":"de-Latn-DE","und-Deva":"hi-Deva-IN","und-Deva-BT":"ne-Deva-BT","und-Deva-FJ":"hif-Deva-FJ","und-Deva-MU":"bho-Deva-MU","und-Deva-PK":"btv-Deva-PK","und-DJ":"aa-Latn-DJ","und-DK":"da-Latn-DK","und-DO":"es-Latn-DO","und-Dogr":"doi-Dogr-IN","und-Dupl":"fr-Dupl-FR","und-DZ":"ar-Arab-DZ","und-EA":"es-Latn-EA","und-EC":"es-Latn-EC","und-EE":"et-Latn-EE","und-EG":"ar-Arab-EG","und-Egyp":"egy-Egyp-EG","und-EH":"ar-Arab-EH","und-Elba":"sq-Elba-AL","und-Elym":"arc-Elym-IR","und-ER":"ti-Ethi-ER","und-ES":"es-Latn-ES","und-ET":"am-Ethi-ET","und-Ethi":"am-Ethi-ET","und-EU":"en-Latn-GB","und-EZ":"de-Latn-EZ","und-FI":"fi-Latn-FI","und-FO":"fo-Latn-FO","und-FR":"fr-Latn-FR","und-GA":"fr-Latn-GA","und-GE":"ka-Geor-GE","und-Geor":"ka-Geor-GE","und-GF":"fr-Latn-GF","und-GH":"ak-Latn-GH","und-GL":"kl-Latn-GL","und-Glag":"cu-Glag-BG","und-GN":"fr-Latn-GN","und-Gong":"wsg-Gong-IN","und-Gonm":"esg-Gonm-IN","und-Goth":"got-Goth-UA","und-GP":"fr-Latn-GP","und-GQ":"es-Latn-GQ","und-GR":"el-Grek-GR","und-Gran":"sa-Gran-IN","und-Grek":"el-Grek-GR","und-Grek-TR":"bgx-Grek-TR","und-GS":"und-Latn-GS","und-GT":"es-Latn-GT","und-Gujr":"gu-Gujr-IN","und-Guru":"pa-Guru-IN","und-GW":"pt-Latn-GW","und-Hanb":"zh-Hanb-TW","und-Hang":"ko-Hang-KR","und-Hani":"zh-Hani-CN","und-Hano":"hnn-Hano-PH","und-Hans":"zh-Hans-CN","und-Hant":"zh-Hant-TW","und-Hatr":"mis-Hatr-IQ","und-Hebr":"he-Hebr-IL","und-Hebr-CA":"yi-Hebr-CA","und-Hebr-GB":"yi-Hebr-GB","und-Hebr-SE":"yi-Hebr-SE","und-Hebr-UA":"yi-Hebr-UA","und-Hebr-US":"yi-Hebr-US","und-Hira":"ja-Hira-JP","und-HK":"zh-Hant-HK","und-Hluw":"hlu-Hluw-TR","und-HM":"und-Latn-HM","und-Hmng":"hnj-Hmng-LA","und-Hmnp":"mww-Hmnp-US","und-HN":"es-Latn-HN","und-HR":"hr-Latn-HR","und-HT":"ht-Latn-HT","und-HU":"hu-Latn-HU","und-Hung":"hu-Hung-HU","und-IC":"es-Latn-IC","und-ID":"id-Latn-ID","und-IL":"he-Hebr-IL","und-IN":"hi-Deva-IN","und-IQ":"ar-Arab-IQ","und-IR":"fa-Arab-IR","und-IS":"is-Latn-IS","und-IT":"it-Latn-IT","und-Ital":"ett-Ital-IT","und-Jamo":"ko-Jamo-KR","und-Java":"jv-Java-ID","und-JO":"ar-Arab-JO","und-JP":"ja-Jpan-JP","und-Jpan":"ja-Jpan-JP","und-Kali":"eky-Kali-MM","und-Kana":"ja-Kana-JP","und-KE":"sw-Latn-KE","und-KG":"ky-Cyrl-KG","und-KH":"km-Khmr-KH","und-Khar":"pra-Khar-PK","und-Khmr":"km-Khmr-KH","und-Khoj":"sd-Khoj-IN","und-KM":"ar-Arab-KM","und-Knda":"kn-Knda-IN","und-Kore":"ko-Kore-KR","und-KP":"ko-Kore-KP","und-KR":"ko-Kore-KR","und-Kthi":"bho-Kthi-IN","und-KW":"ar-Arab-KW","und-KZ":"ru-Cyrl-KZ","und-LA":"lo-Laoo-LA","und-Lana":"nod-Lana-TH","und-Laoo":"lo-Laoo-LA","und-Latn-AF":"tk-Latn-AF","und-Latn-AM":"ku-Latn-AM","und-Latn-CN":"za-Latn-CN","und-Latn-CY":"tr-Latn-CY","und-Latn-DZ":"fr-Latn-DZ","und-Latn-ET":"en-Latn-ET","und-Latn-GE":"ku-Latn-GE","und-Latn-IR":"tk-Latn-IR","und-Latn-KM":"fr-Latn-KM","und-Latn-MA":"fr-Latn-MA","und-Latn-MK":"sq-Latn-MK","und-Latn-MM":"kac-Latn-MM","und-Latn-MO":"pt-Latn-MO","und-Latn-MR":"fr-Latn-MR","und-Latn-RU":"krl-Latn-RU","und-Latn-SY":"fr-Latn-SY","und-Latn-TN":"fr-Latn-TN","und-Latn-TW":"trv-Latn-TW","und-Latn-UA":"pl-Latn-UA","und-LB":"ar-Arab-LB","und-Lepc":"lep-Lepc-IN","und-LI":"de-Latn-LI","und-Limb":"lif-Limb-IN","und-Lina":"lab-Lina-GR","und-Linb":"grc-Linb-GR","und-Lisu":"lis-Lisu-CN","und-LK":"si-Sinh-LK","und-LS":"st-Latn-LS","und-LT":"lt-Latn-LT","und-LU":"fr-Latn-LU","und-LV":"lv-Latn-LV","und-LY":"ar-Arab-LY","und-Lyci":"xlc-Lyci-TR","und-Lydi":"xld-Lydi-TR","und-MA":"ar-Arab-MA","und-Mahj":"hi-Mahj-IN","und-Maka":"mak-Maka-ID","und-Mand":"myz-Mand-IR","und-Mani":"xmn-Mani-CN","und-Marc":"bo-Marc-CN","und-MC":"fr-Latn-MC","und-MD":"ro-Latn-MD","und-ME":"sr-Latn-ME","und-Medf":"mis-Medf-NG","und-Mend":"men-Mend-SL","und-Merc":"xmr-Merc-SD","und-Mero":"xmr-Mero-SD","und-MF":"fr-Latn-MF","und-MG":"mg-Latn-MG","und-MK":"mk-Cyrl-MK","und-ML":"bm-Latn-ML","und-Mlym":"ml-Mlym-IN","und-MM":"my-Mymr-MM","und-MN":"mn-Cyrl-MN","und-MO":"zh-Hant-MO","und-Modi":"mr-Modi-IN","und-Mong":"mn-Mong-CN","und-MQ":"fr-Latn-MQ","und-MR":"ar-Arab-MR","und-Mroo":"mro-Mroo-BD","und-MT":"mt-Latn-MT","und-Mtei":"mni-Mtei-IN","und-MU":"mfe-Latn-MU","und-Mult":"skr-Mult-PK","und-MV":"dv-Thaa-MV","und-MX":"es-Latn-MX","und-MY":"ms-Latn-MY","und-Mymr":"my-Mymr-MM","und-Mymr-IN":"kht-Mymr-IN","und-Mymr-TH":"mnw-Mymr-TH","und-MZ":"pt-Latn-MZ","und-NA":"af-Latn-NA","und-Nand":"sa-Nand-IN","und-Narb":"xna-Narb-SA","und-Nbat":"arc-Nbat-JO","und-NC":"fr-Latn-NC","und-NE":"ha-Latn-NE","und-Newa":"new-Newa-NP","und-NI":"es-Latn-NI","und-Nkoo":"man-Nkoo-GN","und-NL":"nl-Latn-NL","und-NO":"nb-Latn-NO","und-NP":"ne-Deva-NP","und-Nshu":"zhx-Nshu-CN","und-Ogam":"sga-Ogam-IE","und-Olck":"sat-Olck-IN","und-OM":"ar-Arab-OM","und-Orkh":"otk-Orkh-MN","und-Orya":"or-Orya-IN","und-Osge":"osa-Osge-US","und-Osma":"so-Osma-SO","und-PA":"es-Latn-PA","und-Palm":"arc-Palm-SY","und-Pauc":"ctd-Pauc-MM","und-PE":"es-Latn-PE","und-Perm":"kv-Perm-RU","und-PF":"fr-Latn-PF","und-PG":"tpi-Latn-PG","und-PH":"fil-Latn-PH","und-Phag":"lzh-Phag-CN","und-Phli":"pal-Phli-IR","und-Phlp":"pal-Phlp-CN","und-Phnx":"phn-Phnx-LB","und-PK":"ur-Arab-PK","und-PL":"pl-Latn-PL","und-Plrd":"hmd-Plrd-CN","und-PM":"fr-Latn-PM","und-PR":"es-Latn-PR","und-Prti":"xpr-Prti-IR","und-PS":"ar-Arab-PS","und-PT":"pt-Latn-PT","und-PW":"pau-Latn-PW","und-PY":"gn-Latn-PY","und-QA":"ar-Arab-QA","und-QO":"en-Latn-DG","und-RE":"fr-Latn-RE","und-Rjng":"rej-Rjng-ID","und-RO":"ro-Latn-RO","und-Rohg":"rhg-Rohg-MM","und-RS":"sr-Cyrl-RS","und-RU":"ru-Cyrl-RU","und-Runr":"non-Runr-SE","und-RW":"rw-Latn-RW","und-SA":"ar-Arab-SA","und-Samr":"smp-Samr-IL","und-Sarb":"xsa-Sarb-YE","und-Saur":"saz-Saur-IN","und-SC":"fr-Latn-SC","und-SD":"ar-Arab-SD","und-SE":"sv-Latn-SE","und-Sgnw":"ase-Sgnw-US","und-Shaw":"en-Shaw-GB","und-Shrd":"sa-Shrd-IN","und-SI":"sl-Latn-SI","und-Sidd":"sa-Sidd-IN","und-Sind":"sd-Sind-IN","und-Sinh":"si-Sinh-LK","und-SJ":"nb-Latn-SJ","und-SK":"sk-Latn-SK","und-SM":"it-Latn-SM","und-SN":"fr-Latn-SN","und-SO":"so-Latn-SO","und-Sogd":"sog-Sogd-UZ","und-Sogo":"sog-Sogo-UZ","und-Sora":"srb-Sora-IN","und-Soyo":"cmg-Soyo-MN","und-SR":"nl-Latn-SR","und-ST":"pt-Latn-ST","und-Sund":"su-Sund-ID","und-SV":"es-Latn-SV","und-SY":"ar-Arab-SY","und-Sylo":"syl-Sylo-BD","und-Syrc":"syr-Syrc-IQ","und-Tagb":"tbw-Tagb-PH","und-Takr":"doi-Takr-IN","und-Tale":"tdd-Tale-CN","und-Talu":"khb-Talu-CN","und-Taml":"ta-Taml-IN","und-Tang":"txg-Tang-CN","und-Tavt":"blt-Tavt-VN","und-TD":"fr-Latn-TD","und-Telu":"te-Telu-IN","und-TF":"fr-Latn-TF","und-Tfng":"zgh-Tfng-MA","und-TG":"fr-Latn-TG","und-Tglg":"fil-Tglg-PH","und-TH":"th-Thai-TH","und-Thaa":"dv-Thaa-MV","und-Thai":"th-Thai-TH","und-Thai-CN":"lcp-Thai-CN","und-Thai-KH":"kdt-Thai-KH","und-Thai-LA":"kdt-Thai-LA","und-Tibt":"bo-Tibt-CN","und-Tirh":"mai-Tirh-IN","und-TJ":"tg-Cyrl-TJ","und-TK":"tkl-Latn-TK","und-TL":"pt-Latn-TL","und-TM":"tk-Latn-TM","und-TN":"ar-Arab-TN","und-TO":"to-Latn-TO","und-TR":"tr-Latn-TR","und-TV":"tvl-Latn-TV","und-TW":"zh-Hant-TW","und-TZ":"sw-Latn-TZ","und-UA":"uk-Cyrl-UA","und-UG":"sw-Latn-UG","und-Ugar":"uga-Ugar-SY","und-UY":"es-Latn-UY","und-UZ":"uz-Latn-UZ","und-VA":"it-Latn-VA","und-Vaii":"vai-Vaii-LR","und-VE":"es-Latn-VE","und-VN":"vi-Latn-VN","und-VU":"bi-Latn-VU","und-Wara":"hoc-Wara-IN","und-Wcho":"nnp-Wcho-IN","und-WF":"fr-Latn-WF","und-WS":"sm-Latn-WS","und-XK":"sq-Latn-XK","und-Xpeo":"peo-Xpeo-IR","und-Xsux":"akk-Xsux-IQ","und-YE":"ar-Arab-YE","und-Yiii":"ii-Yiii-CN","und-YT":"fr-Latn-YT","und-Zanb":"cmg-Zanb-MN","und-ZW":"sn-Latn-ZW",unr:"unr-Beng-IN","unr-Deva":"unr-Deva-NP","unr-NP":"unr-Deva-NP",unx:"unx-Beng-IN",uok:"uok-Latn-ZZ",ur:"ur-Arab-PK",uri:"uri-Latn-ZZ",urt:"urt-Latn-ZZ",urw:"urw-Latn-ZZ",usa:"usa-Latn-ZZ",utr:"utr-Latn-ZZ",uvh:"uvh-Latn-ZZ",uvl:"uvl-Latn-ZZ",uz:"uz-Latn-UZ","uz-AF":"uz-Arab-AF","uz-Arab":"uz-Arab-AF","uz-CN":"uz-Cyrl-CN",vag:"vag-Latn-ZZ",vai:"vai-Vaii-LR",van:"van-Latn-ZZ",ve:"ve-Latn-ZA",vec:"vec-Latn-IT",vep:"vep-Latn-RU",vi:"vi-Latn-VN",vic:"vic-Latn-SX",viv:"viv-Latn-ZZ",vls:"vls-Latn-BE",vmf:"vmf-Latn-DE",vmw:"vmw-Latn-MZ",vo:"vo-Latn-001",vot:"vot-Latn-RU",vro:"vro-Latn-EE",vun:"vun-Latn-TZ",vut:"vut-Latn-ZZ",wa:"wa-Latn-BE",wae:"wae-Latn-CH",waj:"waj-Latn-ZZ",wal:"wal-Ethi-ET",wan:"wan-Latn-ZZ",war:"war-Latn-PH",wbp:"wbp-Latn-AU",wbq:"wbq-Telu-IN",wbr:"wbr-Deva-IN",wci:"wci-Latn-ZZ",wer:"wer-Latn-ZZ",wgi:"wgi-Latn-ZZ",whg:"whg-Latn-ZZ",wib:"wib-Latn-ZZ",wiu:"wiu-Latn-ZZ",wiv:"wiv-Latn-ZZ",wja:"wja-Latn-ZZ",wji:"wji-Latn-ZZ",wls:"wls-Latn-WF",wmo:"wmo-Latn-ZZ",wnc:"wnc-Latn-ZZ",wni:"wni-Arab-KM",wnu:"wnu-Latn-ZZ",wo:"wo-Latn-SN",wob:"wob-Latn-ZZ",wos:"wos-Latn-ZZ",wrs:"wrs-Latn-ZZ",wsg:"wsg-Gong-IN",wsk:"wsk-Latn-ZZ",wtm:"wtm-Deva-IN",wuu:"wuu-Hans-CN",wuv:"wuv-Latn-ZZ",wwa:"wwa-Latn-ZZ",xav:"xav-Latn-BR",xbi:"xbi-Latn-ZZ",xcr:"xcr-Cari-TR",xes:"xes-Latn-ZZ",xh:"xh-Latn-ZA",xla:"xla-Latn-ZZ",xlc:"xlc-Lyci-TR",xld:"xld-Lydi-TR",xmf:"xmf-Geor-GE",xmn:"xmn-Mani-CN",xmr:"xmr-Merc-SD",xna:"xna-Narb-SA",xnr:"xnr-Deva-IN",xog:"xog-Latn-UG",xon:"xon-Latn-ZZ",xpr:"xpr-Prti-IR",xrb:"xrb-Latn-ZZ",xsa:"xsa-Sarb-YE",xsi:"xsi-Latn-ZZ",xsm:"xsm-Latn-ZZ",xsr:"xsr-Deva-NP",xwe:"xwe-Latn-ZZ",yam:"yam-Latn-ZZ",yao:"yao-Latn-MZ",yap:"yap-Latn-FM",yas:"yas-Latn-ZZ",yat:"yat-Latn-ZZ",yav:"yav-Latn-CM",yay:"yay-Latn-ZZ",yaz:"yaz-Latn-ZZ",yba:"yba-Latn-ZZ",ybb:"ybb-Latn-CM",yby:"yby-Latn-ZZ",yer:"yer-Latn-ZZ",ygr:"ygr-Latn-ZZ",ygw:"ygw-Latn-ZZ",yi:"yi-Hebr-001",yko:"yko-Latn-ZZ",yle:"yle-Latn-ZZ",ylg:"ylg-Latn-ZZ",yll:"yll-Latn-ZZ",yml:"yml-Latn-ZZ",yo:"yo-Latn-NG",yon:"yon-Latn-ZZ",yrb:"yrb-Latn-ZZ",yre:"yre-Latn-ZZ",yrl:"yrl-Latn-BR",yss:"yss-Latn-ZZ",yua:"yua-Latn-MX",yue:"yue-Hant-HK","yue-CN":"yue-Hans-CN","yue-Hans":"yue-Hans-CN",yuj:"yuj-Latn-ZZ",yut:"yut-Latn-ZZ",yuw:"yuw-Latn-ZZ",za:"za-Latn-CN",zag:"zag-Latn-SD",zdj:"zdj-Arab-KM",zea:"zea-Latn-NL",zgh:"zgh-Tfng-MA",zh:"zh-Hans-CN","zh-AU":"zh-Hant-AU","zh-BN":"zh-Hant-BN","zh-Bopo":"zh-Bopo-TW","zh-GB":"zh-Hant-GB","zh-GF":"zh-Hant-GF","zh-Hanb":"zh-Hanb-TW","zh-Hant":"zh-Hant-TW","zh-HK":"zh-Hant-HK","zh-ID":"zh-Hant-ID","zh-MO":"zh-Hant-MO","zh-MY":"zh-Hant-MY","zh-PA":"zh-Hant-PA","zh-PF":"zh-Hant-PF","zh-PH":"zh-Hant-PH","zh-SR":"zh-Hant-SR","zh-TH":"zh-Hant-TH","zh-TW":"zh-Hant-TW","zh-US":"zh-Hant-US","zh-VN":"zh-Hant-VN",zhx:"zhx-Nshu-CN",zia:"zia-Latn-ZZ",zlm:"zlm-Latn-TG",zmi:"zmi-Latn-MY",zne:"zne-Latn-ZZ",zu:"zu-Latn-ZA",zza:"zza-Latn-TR"}}},{main:{"pt-PT":{identity:{version:{_cldrVersion:"36"},language:"pt",territory:"PT"},numbers:{defaultNumberingSystem:"latn",otherNumberingSystems:{native:"latn"},minimumGroupingDigits:"2","symbols-numberSystem-latn":{decimal:",",group:" ",list:";",percentSign:"%",plusSign:"+",minusSign:"-",exponential:"E",superscriptingExponent:"×",perMille:"‰",infinity:"∞",nan:"NaN",timeSeparator:":"},"decimalFormats-numberSystem-latn":{standard:"#,##0.###",long:{decimalFormat:{"1000-count-one":"0 mil","1000-count-other":"0 mil","10000-count-one":"00 mil","10000-count-other":"00 mil","100000-count-one":"000 mil","100000-count-other":"000 mil","1000000-count-one":"0 milhão","1000000-count-other":"0 milhões","********-count-one":"00 milhões","********-count-other":"00 milhões","1********-count-one":"000 milhões","1********-count-other":"000 milhões","1********0-count-one":"0 mil milhões","1********0-count-other":"0 mil milhões","1********00-count-one":"00 mil milhões","1********00-count-other":"00 mil milhões","1********000-count-one":"000 mil milhões","1********000-count-other":"000 mil milhões","1********0000-count-one":"0 bilião","1********0000-count-other":"0 biliões","1********00000-count-one":"00 biliões","1********00000-count-other":"00 biliões","***************-count-one":"000 biliões","***************-count-other":"000 biliões"}},short:{decimalFormat:{"1000-count-one":"0 mil","1000-count-other":"0 mil","10000-count-one":"00 mil","10000-count-other":"00 mil","100000-count-one":"000 mil","100000-count-other":"000 mil","1000000-count-one":"0 M","1000000-count-other":"0 M","********-count-one":"00 M","********-count-other":"00 M","1********-count-one":"000 M","1********-count-other":"000 M","1********0-count-one":"0 mM","1********0-count-other":"0 mM","1********00-count-one":"00 mM","1********00-count-other":"00 mM","1********000-count-one":"000 mM","1********000-count-other":"000 mM","1********0000-count-one":"0 Bi","1********0000-count-other":"0 Bi","1********00000-count-one":"00 Bi","1********00000-count-other":"00 Bi","***************-count-one":"000 Bi","***************-count-other":"000 Bi"}}},"scientificFormats-numberSystem-latn":{standard:"#E0"},"percentFormats-numberSystem-latn":{standard:"#,##0%"},"currencyFormats-numberSystem-latn":{currencySpacing:{beforeCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "},afterCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "}},standard:"#,##0.00 ¤",accounting:"#,##0.00 ¤;(#,##0.00 ¤)",short:{standard:{"1000-count-one":"0 mil ¤","1000-count-other":"0 mil ¤","10000-count-one":"00 mil ¤","10000-count-other":"00 mil ¤","100000-count-one":"000 mil ¤","100000-count-other":"000 mil ¤","1000000-count-one":"0 M ¤","1000000-count-other":"0 M ¤","********-count-one":"00 M ¤","********-count-other":"00 M ¤","1********-count-one":"000 M ¤","1********-count-other":"000 M ¤","1********0-count-one":"0 mM ¤","1********0-count-other":"0 mM ¤","1********00-count-one":"00 mM ¤","1********00-count-other":"00 mM ¤","1********000-count-one":"000 mM ¤","1********000-count-other":"000 mM ¤","1********0000-count-one":"0 B ¤","1********0000-count-other":"0 B ¤","1********00000-count-one":"00 B ¤","1********00000-count-other":"00 B ¤","***************-count-one":"000 B ¤","***************-count-other":"000 B ¤"}},"unitPattern-count-one":"{0} {1}","unitPattern-count-other":"{0} {1}"},"miscPatterns-numberSystem-latn":{approximately:"~{0}",atLeast:"+{0}",atMost:"≤{0}",range:"{0} - {1}"},minimalPairs:{"pluralMinimalPairs-count-one":"{0} dia","pluralMinimalPairs-count-other":"{0} dias",other:"{0}.º lugar"}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},numberingSystems:{adlm:{_digits:"𞥐𞥑𞥒𞥓𞥔𞥕𞥖𞥗𞥘𞥙",_type:"numeric"},ahom:{_digits:"𑜰𑜱𑜲𑜳𑜴𑜵𑜶𑜷𑜸𑜹",_type:"numeric"},arab:{_digits:"٠١٢٣٤٥٦٧٨٩",_type:"numeric"},arabext:{_digits:"۰۱۲۳۴۵۶۷۸۹",_type:"numeric"},armn:{_rules:"armenian-upper",_type:"algorithmic"},armnlow:{_rules:"armenian-lower",_type:"algorithmic"},bali:{_digits:"᭐᭑᭒᭓᭔᭕᭖᭗᭘᭙",_type:"numeric"},beng:{_digits:"০১২৩৪৫৬৭৮৯",_type:"numeric"},bhks:{_digits:"𑱐𑱑𑱒𑱓𑱔𑱕𑱖𑱗𑱘𑱙",_type:"numeric"},brah:{_digits:"𑁦𑁧𑁨𑁩𑁪𑁫𑁬𑁭𑁮𑁯",_type:"numeric"},cakm:{_digits:"𑄶𑄷𑄸𑄹𑄺𑄻𑄼𑄽𑄾𑄿",_type:"numeric"},cham:{_digits:"꩐꩑꩒꩓꩔꩕꩖꩗꩘꩙",_type:"numeric"},cyrl:{_rules:"cyrillic-lower",_type:"algorithmic"},deva:{_digits:"०१२३४५६७८९",_type:"numeric"},ethi:{_rules:"ethiopic",_type:"algorithmic"},fullwide:{_digits:"０１２３４５６７８９",_type:"numeric"},geor:{_rules:"georgian",_type:"algorithmic"},gong:{_digits:"𑶠𑶡𑶢𑶣𑶤𑶥𑶦𑶧𑶨𑶩",_type:"numeric"},gonm:{_digits:"𑵐𑵑𑵒𑵓𑵔𑵕𑵖𑵗𑵘𑵙",_type:"numeric"},grek:{_rules:"greek-upper",_type:"algorithmic"},greklow:{_rules:"greek-lower",_type:"algorithmic"},gujr:{_digits:"૦૧૨૩૪૫૬૭૮૯",_type:"numeric"},guru:{_digits:"੦੧੨੩੪੫੬੭੮੯",_type:"numeric"},hanidays:{_rules:"zh/SpelloutRules/spellout-numbering-days",_type:"algorithmic"},hanidec:{_digits:"〇一二三四五六七八九",_type:"numeric"},hans:{_rules:"zh/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hansfin:{_rules:"zh/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hant:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hantfin:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hebr:{_rules:"hebrew",_type:"algorithmic"},hmng:{_digits:"𖭐𖭑𖭒𖭓𖭔𖭕𖭖𖭗𖭘𖭙",_type:"numeric"},hmnp:{_digits:"𞅀𞅁𞅂𞅃𞅄𞅅𞅆𞅇𞅈𞅉",_type:"numeric"},java:{_digits:"꧐꧑꧒꧓꧔꧕꧖꧗꧘꧙",_type:"numeric"},jpan:{_rules:"ja/SpelloutRules/spellout-cardinal",_type:"algorithmic"},jpanfin:{_rules:"ja/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},jpanyear:{_rules:"ja/SpelloutRules/spellout-numbering-year-latn",_type:"algorithmic"},kali:{_digits:"꤀꤁꤂꤃꤄꤅꤆꤇꤈꤉",_type:"numeric"},khmr:{_digits:"០១២៣៤៥៦៧៨៩",_type:"numeric"},knda:{_digits:"೦೧೨೩೪೫೬೭೮೯",_type:"numeric"},lana:{_digits:"᪀᪁᪂᪃᪄᪅᪆᪇᪈᪉",_type:"numeric"},lanatham:{_digits:"᪐᪑᪒᪓᪔᪕᪖᪗᪘᪙",_type:"numeric"},laoo:{_digits:"໐໑໒໓໔໕໖໗໘໙",_type:"numeric"},latn:{_digits:"0123456789",_type:"numeric"},lepc:{_digits:"᱀᱁᱂᱃᱄᱅᱆᱇᱈᱉",_type:"numeric"},limb:{_digits:"᥆᥇᥈᥉᥊᥋᥌᥍᥎᥏",_type:"numeric"},mathbold:{_digits:"𝟎𝟏𝟐𝟑𝟒𝟓𝟔𝟕𝟖𝟗",_type:"numeric"},mathdbl:{_digits:"𝟘𝟙𝟚𝟛𝟜𝟝𝟞𝟟𝟠𝟡",_type:"numeric"},mathmono:{_digits:"𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿",_type:"numeric"},mathsanb:{_digits:"𝟬𝟭𝟮𝟯𝟰𝟱𝟲𝟳𝟴𝟵",_type:"numeric"},mathsans:{_digits:"𝟢𝟣𝟤𝟥𝟦𝟧𝟨𝟩𝟪𝟫",_type:"numeric"},mlym:{_digits:"൦൧൨൩൪൫൬൭൮൯",_type:"numeric"},modi:{_digits:"𑙐𑙑𑙒𑙓𑙔𑙕𑙖𑙗𑙘𑙙",_type:"numeric"},mong:{_digits:"᠐᠑᠒᠓᠔᠕᠖᠗᠘᠙",_type:"numeric"},mroo:{_digits:"𖩠𖩡𖩢𖩣𖩤𖩥𖩦𖩧𖩨𖩩",_type:"numeric"},mtei:{_digits:"꯰꯱꯲꯳꯴꯵꯶꯷꯸꯹",_type:"numeric"},mymr:{_digits:"၀၁၂၃၄၅၆၇၈၉",_type:"numeric"},mymrshan:{_digits:"႐႑႒႓႔႕႖႗႘႙",_type:"numeric"},mymrtlng:{_digits:"꧰꧱꧲꧳꧴꧵꧶꧷꧸꧹",_type:"numeric"},newa:{_digits:"𑑐𑑑𑑒𑑓𑑔𑑕𑑖𑑗𑑘𑑙",_type:"numeric"},nkoo:{_digits:"߀߁߂߃߄߅߆߇߈߉",_type:"numeric"},olck:{_digits:"᱐᱑᱒᱓᱔᱕᱖᱗᱘᱙",_type:"numeric"},orya:{_digits:"୦୧୨୩୪୫୬୭୮୯",_type:"numeric"},osma:{_digits:"𐒠𐒡𐒢𐒣𐒤𐒥𐒦𐒧𐒨𐒩",_type:"numeric"},rohg:{_digits:"𐴰𐴱𐴲𐴳𐴴𐴵𐴶𐴷𐴸𐴹",_type:"numeric"},roman:{_rules:"roman-upper",_type:"algorithmic"},romanlow:{_rules:"roman-lower",_type:"algorithmic"},saur:{_digits:"꣐꣑꣒꣓꣔꣕꣖꣗꣘꣙",_type:"numeric"},shrd:{_digits:"𑇐𑇑𑇒𑇓𑇔𑇕𑇖𑇗𑇘𑇙",_type:"numeric"},sind:{_digits:"𑋰𑋱𑋲𑋳𑋴𑋵𑋶𑋷𑋸𑋹",_type:"numeric"},sinh:{_digits:"෦෧෨෩෪෫෬෭෮෯",_type:"numeric"},sora:{_digits:"𑃰𑃱𑃲𑃳𑃴𑃵𑃶𑃷𑃸𑃹",_type:"numeric"},sund:{_digits:"᮰᮱᮲᮳᮴᮵᮶᮷᮸᮹",_type:"numeric"},takr:{_digits:"𑛀𑛁𑛂𑛃𑛄𑛅𑛆𑛇𑛈𑛉",_type:"numeric"},talu:{_digits:"᧐᧑᧒᧓᧔᧕᧖᧗᧘᧙",_type:"numeric"},taml:{_rules:"tamil",_type:"algorithmic"},tamldec:{_digits:"௦௧௨௩௪௫௬௭௮௯",_type:"numeric"},telu:{_digits:"౦౧౨౩౪౫౬౭౮౯",_type:"numeric"},thai:{_digits:"๐๑๒๓๔๕๖๗๘๙",_type:"numeric"},tibt:{_digits:"༠༡༢༣༤༥༦༧༨༩",_type:"numeric"},tirh:{_digits:"𑓐𑓑𑓒𑓓𑓔𑓕𑓖𑓗𑓘𑓙",_type:"numeric"},vaii:{_digits:"꘠꘡꘢꘣꘤꘥꘦꘧꘨꘩",_type:"numeric"},wara:{_digits:"𑣠𑣡𑣢𑣣𑣤𑣥𑣦𑣧𑣨𑣩",_type:"numeric"},wcho:{_digits:"𞋰𞋱𞋲𞋳𞋴𞋵𞋶𞋷𞋸𞋹",_type:"numeric"}}}},{main:{"pt-PT":{identity:{version:{_cldrVersion:"36"},language:"pt",territory:"PT"},numbers:{currencies:{ADP:{displayName:"Peseta de Andorra","displayName-count-one":"Peseta de Andorra","displayName-count-other":"Pesetas de Andorra",symbol:"ADP"},AED:{displayName:"dirham dos Emirados Árabes Unidos","displayName-count-one":"dirham dos Emirados Árabes Unidos","displayName-count-other":"sdirham dos Emirados Árabes Unidos",symbol:"AED"},AFA:{displayName:"Afeghani (1927–2002)","displayName-count-one":"Afegane do Afeganistão (AFA)","displayName-count-other":"Afeganes do Afeganistão (AFA)",symbol:"AFA"},AFN:{displayName:"afegâni afegão","displayName-count-one":"afegâni afegão","displayName-count-other":"afegânis afegãos",symbol:"AFN"},ALK:{displayName:"Lek Albanês (1946–1965)","displayName-count-one":"Lek Albanês (1946–1965)","displayName-count-other":"Leks Albaneses (1946–1965)",symbol:"ALK"},ALL:{displayName:"lek albanês","displayName-count-one":"lek albanês","displayName-count-other":"leks albaneses",symbol:"ALL"},AMD:{displayName:"dram arménio","displayName-count-one":"dram arménio","displayName-count-other":"drams arménios",symbol:"AMD"},ANG:{displayName:"florim das Antilhas Holandesas","displayName-count-one":"florim das Antilhas Holandesas","displayName-count-other":"florins das Antilhas Holandesas",symbol:"ANG"},AOA:{displayName:"kwanza angolano","displayName-count-one":"kwanza angolano","displayName-count-other":"kwanzas angolanos",symbol:"AOA","symbol-alt-narrow":"Kz"},AOK:{displayName:"Cuanza angolano (1977–1990)","displayName-count-one":"Kwanza angolano (AOK)","displayName-count-other":"Kwanzas angolanos (AOK)",symbol:"AOK"},AON:{displayName:"Novo cuanza angolano (1990–2000)","displayName-count-one":"Novo kwanza angolano (AON)","displayName-count-other":"Novos kwanzas angolanos (AON)",symbol:"AON"},AOR:{displayName:"Cuanza angolano reajustado (1995–1999)","displayName-count-one":"Kwanza angolano reajustado (AOR)","displayName-count-other":"Kwanzas angolanos reajustados (AOR)",symbol:"AOR"},ARA:{displayName:"Austral argentino","displayName-count-one":"Austral argentino","displayName-count-other":"Austrais argentinos",symbol:"ARA"},ARL:{displayName:"Peso lei argentino (1970–1983)","displayName-count-one":"Peso lei argentino (1970–1983)","displayName-count-other":"Pesos lei argentinos (1970–1983)",symbol:"ARL"},ARM:{displayName:"Peso argentino (1881–1970)","displayName-count-one":"Peso argentino (1881–1970)","displayName-count-other":"Pesos argentinos (1881–1970)",symbol:"ARM"},ARP:{displayName:"Peso argentino (1983–1985)","displayName-count-one":"Peso argentino (1983–1985)","displayName-count-other":"Pesos argentinos (1983–1985)",symbol:"ARP"},ARS:{displayName:"peso argentino","displayName-count-one":"peso argentino","displayName-count-other":"pesos argentinos",symbol:"ARS","symbol-alt-narrow":"$"},ATS:{displayName:"Xelim austríaco","displayName-count-one":"Schilling australiano","displayName-count-other":"Schillings australianos",symbol:"ATS"},AUD:{displayName:"dólar australiano","displayName-count-one":"dólar australiano","displayName-count-other":"dólares australianos",symbol:"AU$","symbol-alt-narrow":"$"},AWG:{displayName:"florim de Aruba","displayName-count-one":"florim de Aruba","displayName-count-other":"florins de Aruba",symbol:"AWG"},AZM:{displayName:"Manat azerbaijano (1993–2006)","displayName-count-one":"Manat do Azeibaijão (1993–2006)","displayName-count-other":"Manats do Azeibaijão (1993–2006)",symbol:"AZM"},AZN:{displayName:"manat azeri","displayName-count-one":"manat azeri","displayName-count-other":"manats azeris",symbol:"AZN"},BAD:{displayName:"Dinar da Bósnia-Herzegóvina","displayName-count-one":"Dinar da Bósnia Herzegovina","displayName-count-other":"Dinares da Bósnia Herzegovina",symbol:"BAD"},BAM:{displayName:"marco bósnio-herzegóvino conversível","displayName-count-one":"marco bósnio-herzegóvino conversível","displayName-count-other":"marcos bósnio-herzegóvinos conversíveis",symbol:"BAM","symbol-alt-narrow":"KM"},BAN:{displayName:"Novo dinar da Bósnia-Herzegovina (1994–1997)","displayName-count-one":"Novo dinar da Bósnia-Herzegovina","displayName-count-other":"Novos dinares da Bósnia-Herzegovina",symbol:"BAN"},BBD:{displayName:"dólar barbadense","displayName-count-one":"dólar barbadense","displayName-count-other":"dólares barbadenses",symbol:"BBD","symbol-alt-narrow":"$"},BDT:{displayName:"taka bengali","displayName-count-one":"taka bengali","displayName-count-other":"takas bengalis",symbol:"BDT","symbol-alt-narrow":"৳"},BEC:{displayName:"Franco belga (convertível)","displayName-count-one":"Franco belga (conversível)","displayName-count-other":"Francos belgas (conversíveis)",symbol:"BEC"},BEF:{displayName:"Franco belga","displayName-count-one":"Franco belga","displayName-count-other":"Francos belgas",symbol:"BEF"},BEL:{displayName:"Franco belga (financeiro)","displayName-count-one":"Franco belga (financeiro)","displayName-count-other":"Francos belgas (financeiros)",symbol:"BEL"},BGL:{displayName:"Lev forte búlgaro","displayName-count-one":"Lev forte búlgaro","displayName-count-other":"Levs fortes búlgaros",symbol:"BGL"},BGM:{displayName:"Lev socialista búlgaro","displayName-count-one":"Lev socialista búlgaro","displayName-count-other":"Levs socialistas búlgaros",symbol:"BGM"},BGN:{displayName:"lev búlgaro","displayName-count-one":"lev búlgaro","displayName-count-other":"levs búlgaros",symbol:"BGN"},BGO:{displayName:"Lev búlgaro (1879–1952)","displayName-count-one":"Lev búlgaro (1879–1952)","displayName-count-other":"Levs búlgaros (1879–1952)",symbol:"BGO"},BHD:{displayName:"dinar baremita","displayName-count-one":"dinar baremita","displayName-count-other":"dinares baremitas",symbol:"BHD"},BIF:{displayName:"franco burundiano","displayName-count-one":"franco burundiano","displayName-count-other":"francos burundianos",symbol:"BIF"},BMD:{displayName:"dólar bermudense","displayName-count-one":"dólar bermudense","displayName-count-other":"dólares bermudense",symbol:"BMD","symbol-alt-narrow":"$"},BND:{displayName:"dólar bruneano","displayName-count-one":"dólar bruneano","displayName-count-other":"dólares bruneanos",symbol:"BND","symbol-alt-narrow":"$"},BOB:{displayName:"boliviano","displayName-count-one":"boliviano","displayName-count-other":"bolivianos",symbol:"BOB","symbol-alt-narrow":"Bs"},BOL:{displayName:"Boliviano (1863–1963)","displayName-count-one":"Boliviano (1863–1963)","displayName-count-other":"Bolivianos (1863–1963)",symbol:"BOL"},BOP:{displayName:"Peso boliviano","displayName-count-one":"Peso boliviano","displayName-count-other":"Pesos bolivianos",symbol:"BOP"},BOV:{displayName:"Mvdol boliviano","displayName-count-one":"Mvdol boliviano","displayName-count-other":"Mvdols bolivianos",symbol:"BOV"},BRB:{displayName:"Cruzeiro novo brasileiro (1967–1986)","displayName-count-one":"Cruzeiro novo brasileiro (BRB)","displayName-count-other":"Cruzeiros novos brasileiros (BRB)",symbol:"BRB"},BRC:{displayName:"Cruzado brasileiro (1986–1989)","displayName-count-one":"Cruzado brasileiro","displayName-count-other":"Cruzados brasileiros",symbol:"BRC"},BRE:{displayName:"Cruzeiro brasileiro (1990–1993)","displayName-count-one":"Cruzeiro brasileiro (BRE)","displayName-count-other":"Cruzeiros brasileiros (BRE)",symbol:"BRE"},BRL:{displayName:"real brasileiro","displayName-count-one":"real brasileiro","displayName-count-other":"reais brasileiros",symbol:"R$","symbol-alt-narrow":"R$"},BRN:{displayName:"Cruzado novo brasileiro (1989–1990)","displayName-count-one":"Cruzado novo brasileiro","displayName-count-other":"Cruzados novos brasileiros",symbol:"BRN"},BRR:{displayName:"Cruzeiro brasileiro (1993–1994)","displayName-count-one":"Cruzeiro brasileiro","displayName-count-other":"Cruzeiros brasileiros",symbol:"BRR"},BRZ:{displayName:"Cruzeiro brasileiro (1942–1967)","displayName-count-one":"Cruzeiro brasileiro antigo","displayName-count-other":"Cruzeiros brasileiros antigos",symbol:"BRZ"},BSD:{displayName:"dólar das Bahamas","displayName-count-one":"dólar das Bahamas","displayName-count-other":"dólares das Bahamas",symbol:"BSD","symbol-alt-narrow":"$"},BTN:{displayName:"ngultrum butanês","displayName-count-one":"ngultrum butanês","displayName-count-other":"ngultrumes butaneses",symbol:"BTN"},BUK:{displayName:"Kyat birmanês","displayName-count-one":"Kyat burmês","displayName-count-other":"Kyats burmeses",symbol:"BUK"},BWP:{displayName:"pula de Botswana","displayName-count-one":"pula de Botswana","displayName-count-other":"pulas de Botswana",symbol:"BWP","symbol-alt-narrow":"P"},BYB:{displayName:"Rublo novo bielorusso (1994–1999)","displayName-count-one":"Novo rublo bielorusso (BYB)","displayName-count-other":"Novos rublos bielorussos (BYB)",symbol:"BYB"},BYN:{displayName:"rublo bielorrusso","displayName-count-one":"rublo bielorrusso","displayName-count-other":"rublos bielorrussos",symbol:"BYN","symbol-alt-narrow":"р."},BYR:{displayName:"Rublo bielorrusso (2000–2016)","displayName-count-one":"Rublo bielorrusso (2000–2016)","displayName-count-other":"Rublos bielorrussos (2000–2016)",symbol:"BYR"},BZD:{displayName:"dólar belizense","displayName-count-one":"dólar belizense","displayName-count-other":"dólares belizense",symbol:"BZD","symbol-alt-narrow":"$"},CAD:{displayName:"dólar canadiano","displayName-count-one":"dólar canadiano","displayName-count-other":"dólares canadianos",symbol:"CA$","symbol-alt-narrow":"$"},CDF:{displayName:"franco congolês","displayName-count-one":"franco congolês","displayName-count-other":"francos congoleses",symbol:"CDF"},CHE:{displayName:"Euro WIR","displayName-count-one":"Euro WIR","displayName-count-other":"Euros WIR",symbol:"CHE"},CHF:{displayName:"franco suíço","displayName-count-one":"franco suíço","displayName-count-other":"francos suíços",symbol:"CHF"},CHW:{displayName:"Franco WIR","displayName-count-one":"Franco WIR","displayName-count-other":"Francos WIR",symbol:"CHW"},CLE:{displayName:"Escudo chileno","displayName-count-one":"Escudo chileno","displayName-count-other":"Escudos chilenos",symbol:"CLE"},CLF:{displayName:"Unidades de Fomento chilenas","displayName-count-one":"Unidade de fomento chilena","displayName-count-other":"Unidades de fomento chilenas",symbol:"CLF"},CLP:{displayName:"peso chileno","displayName-count-one":"peso chileno","displayName-count-other":"pesos chilenos",symbol:"CLP","symbol-alt-narrow":"$"},CNH:{displayName:"yuan offshore","displayName-count-one":"yuan offshore","displayName-count-other":"yuans offshore",symbol:"CNH"},CNX:{displayName:"Dólar do Banco Popular da China","displayName-count-one":"Dólar do Banco Popular da China","displayName-count-other":"Dólares do Banco Popular da China",symbol:"CNX"},CNY:{displayName:"yuan","displayName-count-one":"yuan","displayName-count-other":"yuans",symbol:"CN¥","symbol-alt-narrow":"¥"},COP:{displayName:"peso colombiano","displayName-count-one":"peso colombiano","displayName-count-other":"pesos colombianos",symbol:"COP","symbol-alt-narrow":"$"},COU:{displayName:"Unidade de Valor Real","displayName-count-one":"Unidade de valor real","displayName-count-other":"Unidades de valor real",symbol:"COU"},CRC:{displayName:"colon costa-riquenho","displayName-count-one":"colon costa-riquenho","displayName-count-other":"colons costa-riquenho",symbol:"CRC","symbol-alt-narrow":"₡"},CSD:{displayName:"Dinar sérvio (2002–2006)","displayName-count-one":"Dinar antigo da Sérvia","displayName-count-other":"Dinares antigos da Sérvia",symbol:"CSD"},CSK:{displayName:"Coroa Forte checoslovaca","displayName-count-one":"Coroa forte tchecoslovaca","displayName-count-other":"Coroas fortes tchecoslovacas",symbol:"CSK"},CUC:{displayName:"peso cubano conversível","displayName-count-one":"peso cubano conversível","displayName-count-other":"pesos cubanos conversíveis",symbol:"CUC","symbol-alt-narrow":"$"},CUP:{displayName:"peso cubano","displayName-count-one":"peso cubano","displayName-count-other":"pesos cubanos",symbol:"CUP","symbol-alt-narrow":"$"},CVE:{displayName:"escudo cabo-verdiano","displayName-count-one":"escudo cabo-verdiano","displayName-count-other":"escudos cabo-verdianos",symbol:"CVE"},CYP:{displayName:"Libra de Chipre","displayName-count-one":"Libra cipriota","displayName-count-other":"Libras cipriotas",symbol:"CYP"},CZK:{displayName:"coroa checa","displayName-count-one":"coroa checa","displayName-count-other":"coroas checas",symbol:"CZK","symbol-alt-narrow":"Kč"},DDM:{displayName:"Ostmark da Alemanha Oriental","displayName-count-one":"Marco da Alemanha Oriental","displayName-count-other":"Marcos da Alemanha Oriental",symbol:"DDM"},DEM:{displayName:"Marco alemão","displayName-count-one":"Marco alemão","displayName-count-other":"Marcos alemães",symbol:"DEM"},DJF:{displayName:"franco jibutiano","displayName-count-one":"franco jibutiano","displayName-count-other":"francos jibutianos",symbol:"DJF"},DKK:{displayName:"coroa dinamarquesa","displayName-count-one":"coroa dinamarquesa","displayName-count-other":"coroas dinamarquesas",symbol:"DKK","symbol-alt-narrow":"kr"},DOP:{displayName:"peso dominicano","displayName-count-one":"peso dominicano","displayName-count-other":"pesos dominicanos",symbol:"DOP","symbol-alt-narrow":"$"},DZD:{displayName:"dinar argelino","displayName-count-one":"dinar argelino","displayName-count-other":"dinares argelinos",symbol:"DZD"},ECS:{displayName:"Sucre equatoriano","displayName-count-one":"Sucre equatoriano","displayName-count-other":"Sucres equatorianos",symbol:"ECS"},ECV:{displayName:"Unidad de Valor Constante (UVC) do Equador","displayName-count-one":"Unidade de valor constante equatoriana (UVC)","displayName-count-other":"Unidades de valor constante equatorianas (UVC)",symbol:"ECV"},EEK:{displayName:"Coroa estoniana","displayName-count-one":"Coroa estoniana","displayName-count-other":"Coroas estonianas",symbol:"EEK"},EGP:{displayName:"libra egípcia","displayName-count-one":"libra egípcia","displayName-count-other":"libras egípcias",symbol:"EGP","symbol-alt-narrow":"E£"},ERN:{displayName:"nakfa eritreia","displayName-count-one":"nakfa eritreia","displayName-count-other":"nakfas eritreias",symbol:"ERN"},ESA:{displayName:"Peseta espanhola (conta A)","displayName-count-one":"Peseta espanhola (conta A)","displayName-count-other":"Pesetas espanholas (conta A)",symbol:"ESA"},ESB:{displayName:"Peseta espanhola (conta conversível)","displayName-count-one":"Peseta espanhola (conta conversível)","displayName-count-other":"Pesetas espanholas (conta conversível)",symbol:"ESB"},ESP:{displayName:"Peseta espanhola","displayName-count-one":"Peseta espanhola","displayName-count-other":"Pesetas espanholas",symbol:"ESP","symbol-alt-narrow":"₧"},ETB:{displayName:"birr etíope","displayName-count-one":"birr etíope","displayName-count-other":"birres etíopes",symbol:"ETB"},EUR:{displayName:"euro","displayName-count-one":"euro","displayName-count-other":"euros",symbol:"€","symbol-alt-narrow":"€"},FIM:{displayName:"Marca finlandesa","displayName-count-one":"Marco finlandês","displayName-count-other":"Marcos finlandeses",symbol:"FIM"},FJD:{displayName:"dólar fijiano","displayName-count-one":"dólar fijiano","displayName-count-other":"dólares fijianos",symbol:"FJD","symbol-alt-narrow":"$"},FKP:{displayName:"libra das Ilhas Falkland","displayName-count-one":"libra das Ilhas Falkland","displayName-count-other":"libras das Ilhas Falkland",symbol:"FKP","symbol-alt-narrow":"£"},FRF:{displayName:"Franco francês","displayName-count-one":"Franco francês","displayName-count-other":"Francos franceses",symbol:"FRF"},GBP:{displayName:"libra esterlina britânica","displayName-count-one":"libra esterlina britânica","displayName-count-other":"libras esterlinas britânicas",symbol:"£","symbol-alt-narrow":"£"},GEK:{displayName:"Cupom Lari georgiano","displayName-count-one":"Kupon larit da Geórgia","displayName-count-other":"Kupon larits da Geórgia",symbol:"GEK"},GEL:{displayName:"lari georgiano","displayName-count-one":"lari georgiano","displayName-count-other":"laris georgianos",symbol:"GEL","symbol-alt-narrow":"₾","symbol-alt-variant":"₾"},GHC:{displayName:"Cedi de Gana (1979–2007)","displayName-count-one":"Cedi de Gana (1979–2007)","displayName-count-other":"Cedis de Gana (1979–2007)",symbol:"GHC"},GHS:{displayName:"cedi ganês","displayName-count-one":"cedi ganês","displayName-count-other":"cedis ganeses",symbol:"GHS"},GIP:{displayName:"libra de Gibraltar","displayName-count-one":"libra de Gibraltar","displayName-count-other":"libras de Gibraltar",symbol:"GIP","symbol-alt-narrow":"£"},GMD:{displayName:"dalasi gambiano","displayName-count-one":"dalasi gambiano","displayName-count-other":"dalasis gambianos",symbol:"GMD"},GNF:{displayName:"franco guineense","displayName-count-one":"franco guineense","displayName-count-other":"francos guineenses",symbol:"GNF","symbol-alt-narrow":"FG"},GNS:{displayName:"Syli da Guiné","displayName-count-one":"Syli guineano","displayName-count-other":"Sylis guineanos",symbol:"GNS"},GQE:{displayName:"Ekwele da Guiné Equatorial","displayName-count-one":"Ekwele da Guiné Equatorial","displayName-count-other":"Ekweles da Guiné Equatorial",symbol:"GQE"},GRD:{displayName:"Dracma grego","displayName-count-one":"Dracma grego","displayName-count-other":"Dracmas gregos",symbol:"GRD"},GTQ:{displayName:"quetzal da Guatemala","displayName-count-one":"quetzal da Guatemala","displayName-count-other":"quetzales da Guatemala",symbol:"GTQ","symbol-alt-narrow":"Q"},GWE:{displayName:"Escudo da Guiné Portuguesa","displayName-count-one":"Escudo da Guiné Portuguesa","displayName-count-other":"Escudos da Guinéa Portuguesa",symbol:"GWE"},GWP:{displayName:"Peso da Guiné-Bissau","displayName-count-one":"Peso de Guiné-Bissau","displayName-count-other":"Pesos de Guiné-Bissau",symbol:"GWP"},GYD:{displayName:"dólar da Guiana","displayName-count-one":"dólar da Guiana","displayName-count-other":"dólares da Guiana",symbol:"GYD","symbol-alt-narrow":"$"},HKD:{displayName:"dólar de Hong Kong","displayName-count-one":"dólar de Hong Kong","displayName-count-other":"dólares de Hong Kong",symbol:"HK$","symbol-alt-narrow":"$"},HNL:{displayName:"lempira das Honduras","displayName-count-one":"lempira das Honduras","displayName-count-other":"lempiras das Honduras",symbol:"HNL","symbol-alt-narrow":"L"},HRD:{displayName:"Dinar croata","displayName-count-one":"Dinar croata","displayName-count-other":"Dinares croatas",symbol:"HRD"},HRK:{displayName:"kuna croata","displayName-count-one":"kuna croata","displayName-count-other":"kunas croatas",symbol:"HRK","symbol-alt-narrow":"kn"},HTG:{displayName:"gourde haitiano","displayName-count-one":"gourde haitiano","displayName-count-other":"gourdes haitianos",symbol:"HTG"},HUF:{displayName:"forint húngaro","displayName-count-one":"forint húngaro","displayName-count-other":"forints húngaros",symbol:"HUF","symbol-alt-narrow":"Ft"},IDR:{displayName:"rupia indonésia","displayName-count-one":"rupia indonésia","displayName-count-other":"rupias indonésias",symbol:"IDR","symbol-alt-narrow":"Rp"},IEP:{displayName:"Libra irlandesa","displayName-count-one":"Libra irlandesa","displayName-count-other":"Libras irlandesas",symbol:"IEP"},ILP:{displayName:"Libra israelita","displayName-count-one":"Libra israelita","displayName-count-other":"Libras israelitas",symbol:"ILP"},ILR:{displayName:"Sheqel antigo israelita","displayName-count-one":"Sheqel antigo israelita","displayName-count-other":"Sheqels antigos israelitas",symbol:"ILR"},ILS:{displayName:"sheqel novo israelita","displayName-count-one":"sheqel novo israelita","displayName-count-other":"sheqels novos israelitas",symbol:"₪","symbol-alt-narrow":"₪"},INR:{displayName:"rupia indiana","displayName-count-one":"rupia indiana","displayName-count-other":"rupias indianas",symbol:"₹","symbol-alt-narrow":"₹"},IQD:{displayName:"dinar iraquiano","displayName-count-one":"dinar iraquiano","displayName-count-other":"dinares iraquianos",symbol:"IQD"},IRR:{displayName:"rial iraniano","displayName-count-one":"rial iraniano","displayName-count-other":"riais iranianos",symbol:"IRR"},ISJ:{displayName:"Coroa antiga islandesa","displayName-count-one":"Coroa antiga islandesa","displayName-count-other":"Coroas antigas islandesas",symbol:"ISJ"},ISK:{displayName:"coroa islandesa","displayName-count-one":"coroa islandesa","displayName-count-other":"coroas islandesas",symbol:"ISK","symbol-alt-narrow":"kr"},ITL:{displayName:"Lira italiana","displayName-count-one":"Lira italiana","displayName-count-other":"Liras italianas",symbol:"ITL"},JMD:{displayName:"dólar jamaicano","displayName-count-one":"dólar jamaicano","displayName-count-other":"dólares jamaicanos",symbol:"JMD","symbol-alt-narrow":"$"},JOD:{displayName:"dinar jordaniano","displayName-count-one":"dinar jordaniano","displayName-count-other":"dinares jordanianos",symbol:"JOD"},JPY:{displayName:"iene japonês","displayName-count-one":"iene japonês","displayName-count-other":"ienes japoneses",symbol:"JP¥","symbol-alt-narrow":"¥"},KES:{displayName:"xelim queniano","displayName-count-one":"xelim queniano","displayName-count-other":"xelins quenianos",symbol:"KES"},KGS:{displayName:"som quirguiz","displayName-count-one":"som quirguiz","displayName-count-other":"somes quirguizes",symbol:"KGS"},KHR:{displayName:"riel cambojano","displayName-count-one":"riel cambojano","displayName-count-other":"rieles cambojanos",symbol:"KHR","symbol-alt-narrow":"៛"},KMF:{displayName:"franco comoriano","displayName-count-one":"franco comoriano","displayName-count-other":"francos comorianos",symbol:"KMF","symbol-alt-narrow":"CF"},KPW:{displayName:"won norte-coreano","displayName-count-one":"won norte-coreano","displayName-count-other":"wons norte-coreanos",symbol:"KPW","symbol-alt-narrow":"₩"},KRH:{displayName:"Hwan da Coreia do Sul (1953–1962)","displayName-count-one":"Hwan da Coreia do Sul","displayName-count-other":"Hwans da Coreia do Sul",symbol:"KRH"},KRO:{displayName:"Won da Coreia do Sul (1945–1953)","displayName-count-one":"Won antigo da Coreia do Sul","displayName-count-other":"Wons antigos da Coreia do Sul",symbol:"KRO"},KRW:{displayName:"won sul-coreano","displayName-count-one":"won sul-coreano","displayName-count-other":"wons sul-coreano",symbol:"₩","symbol-alt-narrow":"₩"},KWD:{displayName:"dinar kuwaitiano","displayName-count-one":"dinar kuwaitiano","displayName-count-other":"dinares kuwaitianos",symbol:"KWD"},KYD:{displayName:"dólar das Ilhas Caimão","displayName-count-one":"dólar das Ilhas Caimão","displayName-count-other":"dólares das Ilhas Caimão",symbol:"KYD","symbol-alt-narrow":"$"},KZT:{displayName:"tenge cazaque","displayName-count-one":"tenge cazaque","displayName-count-other":"tenges cazaques",symbol:"KZT","symbol-alt-narrow":"₸"},LAK:{displayName:"kip laosiano","displayName-count-one":"kip laosiano","displayName-count-other":"kips laosianos",symbol:"LAK","symbol-alt-narrow":"₭"},LBP:{displayName:"libra libanesa","displayName-count-one":"libra libanesa","displayName-count-other":"libras libanesas",symbol:"LBP","symbol-alt-narrow":"L£"},LKR:{displayName:"rupia do Sri Lanka","displayName-count-one":"rupia do Sri Lanka","displayName-count-other":"rupias do Sri Lanka",symbol:"LKR","symbol-alt-narrow":"Rs"},LRD:{displayName:"dólar liberiano","displayName-count-one":"dólar liberiano","displayName-count-other":"dólares liberianos",symbol:"LRD","symbol-alt-narrow":"$"},LSL:{displayName:"Loti do Lesoto","displayName-count-one":"Loti do Lesoto","displayName-count-other":"Lotis do Lesoto",symbol:"LSL"},LTL:{displayName:"Litas da Lituânia","displayName-count-one":"Litas da Lituânia","displayName-count-other":"Litas da Lituânia",symbol:"LTL","symbol-alt-narrow":"Lt"},LTT:{displayName:"Talonas lituano","displayName-count-one":"Talonas lituanas","displayName-count-other":"Talonases lituanas",symbol:"LTT"},LUC:{displayName:"Franco conversível de Luxemburgo","displayName-count-one":"Franco conversível de Luxemburgo","displayName-count-other":"Francos conversíveis de Luxemburgo",symbol:"LUC"},LUF:{displayName:"Franco luxemburguês","displayName-count-one":"Franco de Luxemburgo","displayName-count-other":"Francos de Luxemburgo",symbol:"LUF"},LUL:{displayName:"Franco financeiro de Luxemburgo","displayName-count-one":"Franco financeiro de Luxemburgo","displayName-count-other":"Francos financeiros de Luxemburgo",symbol:"LUL"},LVL:{displayName:"Lats da Letónia","displayName-count-one":"Lats da Letónia","displayName-count-other":"Lats da Letónia",symbol:"LVL","symbol-alt-narrow":"Ls"},LVR:{displayName:"Rublo letão","displayName-count-one":"Rublo da Letônia","displayName-count-other":"Rublos da Letônia",symbol:"LVR"},LYD:{displayName:"dinar líbio","displayName-count-one":"dinar líbio","displayName-count-other":"dinares líbios",symbol:"LYD"},MAD:{displayName:"dirham marroquino","displayName-count-one":"dirham marroquino","displayName-count-other":"dirhams marroquinos",symbol:"MAD"},MAF:{displayName:"Franco marroquino","displayName-count-one":"Franco marroquino","displayName-count-other":"Francos marroquinos",symbol:"MAF"},MCF:{displayName:"Franco monegasco","displayName-count-one":"Franco monegasco","displayName-count-other":"Francos monegascos",symbol:"MCF"},MDC:{displayName:"Cupon moldávio","displayName-count-one":"Cupon moldávio","displayName-count-other":"Cupon moldávio",symbol:"MDC"},MDL:{displayName:"leu moldavo","displayName-count-one":"leu moldavo","displayName-count-other":"leus moldavos",symbol:"MDL"},MGA:{displayName:"ariari malgaxe","displayName-count-one":"ariari malgaxe","displayName-count-other":"ariaris malgaxes",symbol:"MGA","symbol-alt-narrow":"Ar"},MGF:{displayName:"Franco de Madagascar","displayName-count-one":"Franco de Madagascar","displayName-count-other":"Francos de Madagascar",symbol:"MGF"},MKD:{displayName:"dinar macedónio","displayName-count-one":"dinar macedónio","displayName-count-other":"dinares macedónios",symbol:"MKD"},MKN:{displayName:"Dinar macedônio (1992–1993)","displayName-count-one":"Dinar macedônio (1992–1993)","displayName-count-other":"Dinares macedônios (1992–1993)",symbol:"MKN"},MLF:{displayName:"Franco de Mali","displayName-count-one":"Franco de Mali","displayName-count-other":"Francos de Mali",symbol:"MLF"},MMK:{displayName:"kyat de Mianmar","displayName-count-one":"kyat de Mianmar","displayName-count-other":"kyats de Mianmar",symbol:"MMK","symbol-alt-narrow":"K"},MNT:{displayName:"tugrik mongol","displayName-count-one":"tugrik mongol","displayName-count-other":"tugriks mongóis",symbol:"MNT","symbol-alt-narrow":"₮"},MOP:{displayName:"pataca macaense","displayName-count-one":"pataca macaense","displayName-count-other":"patacas macaenses",symbol:"MOP"},MRO:{displayName:"ouguiya mauritana (1973–2017)","displayName-count-one":"ouguiya mauritana (1973–2017)","displayName-count-other":"ouguiyas mauritanas (1973–2017)",symbol:"MRO"},MRU:{displayName:"ouguiya mauritana","displayName-count-one":"ouguiya mauritana","displayName-count-other":"ouguiyas mauritanas",symbol:"MRU"},MTL:{displayName:"Lira maltesa","displayName-count-one":"Lira Maltesa","displayName-count-other":"Liras maltesas",symbol:"MTL"},MTP:{displayName:"Libra maltesa","displayName-count-one":"Libra maltesa","displayName-count-other":"Libras maltesas",symbol:"MTP"},MUR:{displayName:"rupia mauriciana","displayName-count-one":"rupia mauriciana","displayName-count-other":"rupias mauricianas",symbol:"MUR","symbol-alt-narrow":"Rs"},MVP:{displayName:"MVP",symbol:"MVP"},MVR:{displayName:"rupia maldivana","displayName-count-one":"rupia maldivana","displayName-count-other":"rupias maldivanas",symbol:"MVR"},MWK:{displayName:"kwacha malauiano","displayName-count-one":"kwacha malauiano","displayName-count-other":"kwachas malauianos",symbol:"MWK"},MXN:{displayName:"peso mexicano","displayName-count-one":"peso mexicano","displayName-count-other":"pesos mexicanos",symbol:"MX$","symbol-alt-narrow":"$"},MXP:{displayName:"Peso Plata mexicano (1861–1992)","displayName-count-one":"Peso de prata mexicano (1861–1992)","displayName-count-other":"Pesos de prata mexicanos (1861–1992)",symbol:"MXP"},MXV:{displayName:"Unidad de Inversion (UDI) mexicana","displayName-count-one":"Unidade de investimento mexicana (UDI)","displayName-count-other":"Unidades de investimento mexicanas (UDI)",symbol:"MXV"},MYR:{displayName:"ringgit malaio","displayName-count-one":"ringgit malaio","displayName-count-other":"ringgits malaios",symbol:"MYR","symbol-alt-narrow":"RM"},MZE:{displayName:"Escudo de Moçambique","displayName-count-one":"Escudo de Moçambique","displayName-count-other":"Escudos de Moçambique",symbol:"MZE"},MZM:{displayName:"Metical de Moçambique (1980–2006)","displayName-count-one":"Metical antigo de Moçambique","displayName-count-other":"Meticales antigos de Moçambique",symbol:"MZM"},MZN:{displayName:"metical moçambicano","displayName-count-one":"metical moçambicano","displayName-count-other":"meticais moçambicanos",symbol:"MZN"},NAD:{displayName:"dólar namibiano","displayName-count-one":"dólar namibiano","displayName-count-other":"dólares namibianos",symbol:"NAD","symbol-alt-narrow":"$"},NGN:{displayName:"naira nigeriana","displayName-count-one":"naira nigeriana","displayName-count-other":"nairas nigerianas",symbol:"NGN","symbol-alt-narrow":"₦"},NIC:{displayName:"Córdoba nicaraguano (1988–1991)","displayName-count-one":"Córdoba nicaraguano (1988–1991)","displayName-count-other":"Córdobas nicaraguano (1988–1991)",symbol:"NIC"},NIO:{displayName:"córdoba nicaraguano","displayName-count-one":"córdoba nicaraguano","displayName-count-other":"córdobas nicaraguanos",symbol:"NIO","symbol-alt-narrow":"C$"},NLG:{displayName:"Florim holandês","displayName-count-one":"Florim holandês","displayName-count-other":"Florins holandeses",symbol:"NLG"},NOK:{displayName:"coroa norueguesa","displayName-count-one":"coroa norueguesa","displayName-count-other":"coroas norueguesas",symbol:"NOK","symbol-alt-narrow":"kr"},NPR:{displayName:"rupia nepalesa","displayName-count-one":"rupia nepalesa","displayName-count-other":"rupias nepalesas",symbol:"NPR","symbol-alt-narrow":"Rs"},NZD:{displayName:"dólar neozelandês","displayName-count-one":"dólar neozelandês","displayName-count-other":"dólares neozelandeses",symbol:"NZ$","symbol-alt-narrow":"$"},OMR:{displayName:"rial omanense","displayName-count-one":"rial omanense","displayName-count-other":"riais omanenses",symbol:"OMR"},PAB:{displayName:"balboa do Panamá","displayName-count-one":"balboa do Panamá","displayName-count-other":"balboas do Panamá",symbol:"PAB"},PEI:{displayName:"Inti peruano","displayName-count-one":"Inti peruano","displayName-count-other":"Intis peruanos",symbol:"PEI"},PEN:{displayName:"sol peruano","displayName-count-one":"sol peruano","displayName-count-other":"sóis peruanos",symbol:"PEN"},PES:{displayName:"Sol peruano (1863–1965)","displayName-count-one":"Sol peruano (1863–1965)","displayName-count-other":"Soles peruanos (1863–1965)",symbol:"PES"},PGK:{displayName:"kina papuásia","displayName-count-one":"kina papuásia","displayName-count-other":"kinas papuásias",symbol:"PGK"},PHP:{displayName:"peso filipino","displayName-count-one":"peso filipino","displayName-count-other":"pesos filipinos",symbol:"PHP","symbol-alt-narrow":"₱"},PKR:{displayName:"rupia paquistanesa","displayName-count-one":"rupia paquistanesa","displayName-count-other":"rupias paquistanesas",symbol:"PKR","symbol-alt-narrow":"Rs"},PLN:{displayName:"zloti polaco","displayName-count-one":"zloti polaco","displayName-count-other":"zlotis polacos",symbol:"PLN","symbol-alt-narrow":"zł"},PLZ:{displayName:"Zloti polonês (1950–1995)","displayName-count-one":"Zloti polonês (1950–1995)","displayName-count-other":"Zlotis poloneses (1950–1995)",symbol:"PLZ"},PTE:{displayName:"escudo português","displayName-count-one":"escudo português","displayName-count-other":"escudos portugueses",symbol:"​",decimal:"$",group:","},PYG:{displayName:"guarani paraguaio","displayName-count-one":"guarani paraguaio","displayName-count-other":"guaranis paraguaios",symbol:"PYG","symbol-alt-narrow":"₲"},QAR:{displayName:"rial catarense","displayName-count-one":"rial catarense","displayName-count-other":"riais catarenses",symbol:"QAR"},RHD:{displayName:"Dólar rodesiano","displayName-count-one":"Dólar da Rodésia","displayName-count-other":"Dólares da Rodésia",symbol:"RHD"},ROL:{displayName:"Leu romeno (1952–2006)","displayName-count-one":"Leu antigo da Romênia","displayName-count-other":"Leus antigos da Romênia",symbol:"ROL"},RON:{displayName:"leu romeno","displayName-count-one":"leu romeno","displayName-count-other":"leus romenos",symbol:"RON","symbol-alt-narrow":"L"},RSD:{displayName:"dinar sérvio","displayName-count-one":"dinar sérvio","displayName-count-other":"dinares sérvios",symbol:"RSD"},RUB:{displayName:"rublo russo","displayName-count-one":"rublo russo","displayName-count-other":"rublos russos",symbol:"RUB","symbol-alt-narrow":"₽"},RUR:{displayName:"Rublo russo (1991–1998)","displayName-count-one":"Rublo russo (1991–1998)","displayName-count-other":"Rublos russos (1991–1998)",symbol:"RUR","symbol-alt-narrow":"р."},RWF:{displayName:"franco ruandês","displayName-count-one":"franco ruandês","displayName-count-other":"francos ruandeses",symbol:"RWF","symbol-alt-narrow":"RF"},SAR:{displayName:"rial saudita","displayName-count-one":"rial saudita","displayName-count-other":"riais sauditas",symbol:"SAR"},SBD:{displayName:"dólar das Ilhas Salomão","displayName-count-one":"dólar das Ilhas Salomão","displayName-count-other":"dólares das Ilhas Salomão",symbol:"SBD","symbol-alt-narrow":"$"},SCR:{displayName:"rupia seichelense","displayName-count-one":"rupia seichelense","displayName-count-other":"rupias seichelenses",symbol:"SCR"},SDD:{displayName:"Dinar sudanês (1992–2007)","displayName-count-one":"Dinar antigo do Sudão","displayName-count-other":"Dinares antigos do Sudão",symbol:"SDD"},SDG:{displayName:"libra sudanesa","displayName-count-one":"libra sudanesa","displayName-count-other":"libras sudanesas",symbol:"SDG"},SDP:{displayName:"Libra sudanesa (1957–1998)","displayName-count-one":"Libra antiga sudanesa","displayName-count-other":"Libras antigas sudanesas",symbol:"SDP"},SEK:{displayName:"coroa sueca","displayName-count-one":"coroa sueca","displayName-count-other":"coroas suecas",symbol:"SEK","symbol-alt-narrow":"kr"},SGD:{displayName:"dólar singapuriano","displayName-count-one":"dólar singapuriano","displayName-count-other":"dólares singapurianos",symbol:"SGD","symbol-alt-narrow":"$"},SHP:{displayName:"libra santa-helenense","displayName-count-one":"libra santa-helenense","displayName-count-other":"libras santa-helenenses",symbol:"SHP","symbol-alt-narrow":"£"},SIT:{displayName:"Tolar Bons esloveno","displayName-count-one":"Tolar da Eslovênia","displayName-count-other":"Tolares da Eslovênia",symbol:"SIT"},SKK:{displayName:"Coroa eslovaca","displayName-count-one":"Coroa eslovaca","displayName-count-other":"Coroas eslovacas",symbol:"SKK"},SLL:{displayName:"leone de Serra Leoa","displayName-count-one":"leone de Serra Leoa","displayName-count-other":"leones de Serra Leoa",symbol:"SLL"},SOS:{displayName:"xelim somali","displayName-count-one":"xelim somali","displayName-count-other":"xelins somalis",symbol:"SOS"},SRD:{displayName:"dólar do Suriname","displayName-count-one":"dólar do Suriname","displayName-count-other":"dólares do Suriname",symbol:"SRD","symbol-alt-narrow":"$"},SRG:{displayName:"Florim do Suriname","displayName-count-one":"Florim do Suriname","displayName-count-other":"Florins do Suriname",symbol:"SRG"},SSP:{displayName:"libra sul-sudanesa","displayName-count-one":"libra sul-sudanesa","displayName-count-other":"libras sul-sudanesas",symbol:"SSP","symbol-alt-narrow":"£"},STD:{displayName:"Dobra de São Tomé e Príncipe (1977–2017)","displayName-count-one":"Dobra de São Tomé e Príncipe (1977–2017)","displayName-count-other":"Dobras de São Tomé e Príncipe (1977–2017)",symbol:"STD"},STN:{displayName:"dobra de São Tomé e Príncipe","displayName-count-one":"dobra de São Tomé e Príncipe","displayName-count-other":"dobras de São Tomé e Príncipe",symbol:"STN","symbol-alt-narrow":"Db"},SUR:{displayName:"Rublo soviético","displayName-count-one":"Rublo soviético","displayName-count-other":"Rublos soviéticos",symbol:"SUR"},SVC:{displayName:"Colom salvadorenho","displayName-count-one":"Colon de El Salvador","displayName-count-other":"Colons de El Salvador",symbol:"SVC"},SYP:{displayName:"libra síria","displayName-count-one":"libra síria","displayName-count-other":"libras sírias",symbol:"SYP","symbol-alt-narrow":"£"},SZL:{displayName:"lilangeni suázi","displayName-count-one":"lilangeni suázi","displayName-count-other":"lilangenis suázis",symbol:"SZL"},THB:{displayName:"baht tailandês","displayName-count-one":"baht tailandês","displayName-count-other":"bahts tailandeses",symbol:"฿","symbol-alt-narrow":"฿"},TJR:{displayName:"Rublo do Tadjiquistão","displayName-count-one":"Rublo do Tajaquistão","displayName-count-other":"Rublos do Tajaquistão",symbol:"TJR"},TJS:{displayName:"somoni tajique","displayName-count-one":"somoni tajique","displayName-count-other":"somonis tajiques",symbol:"TJS"},TMM:{displayName:"Manat do Turcomenistão (1993–2009)","displayName-count-one":"Manat do Turcomenistão (1993–2009)","displayName-count-other":"Manats do Turcomenistão (1993–2009)",symbol:"TMM"},TMT:{displayName:"manat turcomeno","displayName-count-one":"manat turcomeno","displayName-count-other":"manats turcomenos",symbol:"TMT"},TND:{displayName:"dinar tunisino","displayName-count-one":"dinar tunisino","displayName-count-other":"dinares tunisinos",symbol:"TND"},TOP:{displayName:"paʻanga tonganesa","displayName-count-one":"paʻanga tonganesa","displayName-count-other":"paʻangas tonganesas",symbol:"TOP","symbol-alt-narrow":"T$"},TPE:{displayName:"Escudo timorense","displayName-count-one":"Escudo do Timor","displayName-count-other":"Escudos do Timor",symbol:"TPE"},TRL:{displayName:"Lira turca (1922–2005)","displayName-count-one":"Lira turca antiga","displayName-count-other":"Liras turcas antigas",symbol:"TRL"},TRY:{displayName:"lira turca","displayName-count-one":"lira turca","displayName-count-other":"liras turcas",symbol:"TRY","symbol-alt-narrow":"₺","symbol-alt-variant":"TL"},TTD:{displayName:"dólar de Trindade e Tobago","displayName-count-one":"dólar de Trindade e Tobago","displayName-count-other":"dólares de Trindade e Tobago",symbol:"TTD","symbol-alt-narrow":"$"},TWD:{displayName:"novo dólar taiwanês","displayName-count-one":"novo dólar taiwanês","displayName-count-other":"novos dólares taiwaneses",symbol:"NT$","symbol-alt-narrow":"NT$"},TZS:{displayName:"xelim tanzaniano","displayName-count-one":"xelim tanzaniano","displayName-count-other":"xelins tanzanianos",symbol:"TZS"},UAH:{displayName:"hryvnia ucraniano","displayName-count-one":"hryvnia ucraniano","displayName-count-other":"hryvnias ucranianos",symbol:"UAH","symbol-alt-narrow":"₴"},UAK:{displayName:"Karbovanetz ucraniano","displayName-count-one":"Karbovanetz da Ucrânia","displayName-count-other":"Karbovanetzs da Ucrânia",symbol:"UAK"},UGS:{displayName:"Xelim ugandense (1966–1987)","displayName-count-one":"Shilling de Uganda (1966–1987)","displayName-count-other":"Shillings de Uganda (1966–1987)",symbol:"UGS"},UGX:{displayName:"xelim ugandense","displayName-count-one":"xelim ugandense","displayName-count-other":"xelins ugandenses",symbol:"UGX"},USD:{displayName:"dólar dos Estados Unidos","displayName-count-one":"dólar dos Estados Unidos","displayName-count-other":"dólares dos Estados Unidos",symbol:"US$","symbol-alt-narrow":"$"},USN:{displayName:"Dólar norte-americano (Dia seguinte)","displayName-count-one":"Dólar americano (dia seguinte)","displayName-count-other":"Dólares americanos (dia seguinte)",symbol:"USN"},USS:{displayName:"Dólar norte-americano (Mesmo dia)","displayName-count-one":"Dólar americano (mesmo dia)","displayName-count-other":"Dólares americanos (mesmo dia)",symbol:"USS"},UYI:{displayName:"Peso uruguaio en unidades indexadas","displayName-count-one":"Peso uruguaio em unidades indexadas","displayName-count-other":"Pesos uruguaios em unidades indexadas",symbol:"UYI"},UYP:{displayName:"Peso uruguaio (1975–1993)","displayName-count-one":"Peso uruguaio (1975–1993)","displayName-count-other":"Pesos uruguaios (1975–1993)",symbol:"UYP"},UYU:{displayName:"peso uruguaio","displayName-count-one":"peso uruguaio","displayName-count-other":"pesos uruguaios",symbol:"UYU","symbol-alt-narrow":"$"},UYW:{displayName:"UYW",symbol:"UYW"},UZS:{displayName:"som uzbeque","displayName-count-one":"som uzbeque","displayName-count-other":"somes uzbeques",symbol:"UZS"},VEB:{displayName:"Bolívar venezuelano (1871–2008)","displayName-count-one":"Bolívar venezuelano (1871–2008)","displayName-count-other":"Bolívares venezuelanos (1871–2008)",symbol:"VEB"},VEF:{displayName:"bolívar (2008–2018)","displayName-count-one":"bolívar (2008–2018)","displayName-count-other":"bolívares (2008–2018)",symbol:"VEF","symbol-alt-narrow":"Bs"},VES:{displayName:"bolívar","displayName-count-one":"bolívar","displayName-count-other":"bolívares",symbol:"VES"},VND:{displayName:"dong vietnamita","displayName-count-one":"dong vietnamita","displayName-count-other":"dongs vietnamitas",symbol:"₫","symbol-alt-narrow":"₫"},VNN:{displayName:"Dong vietnamita (1978–1985)","displayName-count-one":"Dong vietnamita (1978–1985)","displayName-count-other":"Dong vietnamita (1978–1985)",symbol:"VNN"},VUV:{displayName:"vatu de Vanuatu","displayName-count-one":"vatu de Vanuatu","displayName-count-other":"vatus de Vanuatu",symbol:"VUV"},WST:{displayName:"tala samoano","displayName-count-one":"tala samoano","displayName-count-other":"talas samoanos",symbol:"WST"},XAF:{displayName:"franco CFA (BEAC)","displayName-count-one":"franco CFA (BEAC)","displayName-count-other":"francos CFA (BEAC)",symbol:"FCFA"},XAG:{displayName:"Prata","displayName-count-one":"Prata","displayName-count-other":"Pratas",symbol:"XAG"},XAU:{displayName:"Ouro","displayName-count-one":"Ouro","displayName-count-other":"Ouros",symbol:"XAU"},XBA:{displayName:"Unidade Composta Europeia","displayName-count-one":"Unidade de composição europeia","displayName-count-other":"Unidades de composição europeias",symbol:"XBA"},XBB:{displayName:"Unidade Monetária Europeia","displayName-count-one":"Unidade monetária europeia","displayName-count-other":"Unidades monetárias europeias",symbol:"XBB"},XBC:{displayName:"Unidade de Conta Europeia (XBC)","displayName-count-one":"Unidade europeia de conta (XBC)","displayName-count-other":"Unidades europeias de conta (XBC)",symbol:"XBC"},XBD:{displayName:"Unidade de Conta Europeia (XBD)","displayName-count-one":"Unidade europeia de conta (XBD)","displayName-count-other":"Unidades europeias de conta (XBD)",symbol:"XBD"},XCD:{displayName:"dólar das Caraíbas Orientais","displayName-count-one":"dólar das Caraíbas Orientais","displayName-count-other":"dólares das Caraíbas Orientais",symbol:"EC$","symbol-alt-narrow":"$"},XDR:{displayName:"Direitos Especiais de Giro","displayName-count-one":"Direitos de desenho especiais","displayName-count-other":"Direitos de desenho especiais",symbol:"XDR"},XEU:{displayName:"Unidade de Moeda Europeia","displayName-count-one":"Unidade de moeda europeia","displayName-count-other":"Unidades de moedas europeias",symbol:"XEU"},XFO:{displayName:"Franco-ouro francês","displayName-count-one":"Franco de ouro francês","displayName-count-other":"Francos de ouro franceses",symbol:"XFO"},XFU:{displayName:"Franco UIC francês","displayName-count-one":"Franco UIC francês","displayName-count-other":"Francos UIC franceses",symbol:"XFU"},XOF:{displayName:"franco CFA (BCEAO)","displayName-count-one":"franco CFA (BCEAO)","displayName-count-other":"francos CFA (BCEAO)",symbol:"CFA"},XPD:{displayName:"Paládio","displayName-count-one":"Paládio","displayName-count-other":"Paládios",symbol:"XPD"},XPF:{displayName:"franco CFP","displayName-count-one":"franco CFP","displayName-count-other":"francos CFP",symbol:"CFPF"},XPT:{displayName:"Platina","displayName-count-one":"Platina","displayName-count-other":"Platinas",symbol:"XPT"},XRE:{displayName:"Fundos RINET","displayName-count-one":"Fundos RINET","displayName-count-other":"Fundos RINET",symbol:"XRE"},XSU:{displayName:"XSU",symbol:"XSU"},XTS:{displayName:"Código de Moeda de Teste","displayName-count-one":"Código de moeda de teste","displayName-count-other":"Códigos de moeda de teste",symbol:"XTS"},XUA:{displayName:"XUA",symbol:"XUA"},XXX:{displayName:"moeda desconhecida","displayName-count-one":"(moeda desconhecida)","displayName-count-other":"(moedas desconhecidas)",symbol:"¤"},YDD:{displayName:"Dinar iemenita","displayName-count-one":"Dinar do Iêmen","displayName-count-other":"Dinares do Iêmen",symbol:"YDD"},YER:{displayName:"rial iemenita","displayName-count-one":"rial iemenita","displayName-count-other":"riais iemenitas",symbol:"YER"},YUD:{displayName:"Dinar forte iugoslavo (1966–1990)","displayName-count-one":"Dinar forte iugoslavo","displayName-count-other":"Dinares fortes iugoslavos",symbol:"YUD"},YUM:{displayName:"Dinar noviy iugoslavo (1994–2002)","displayName-count-one":"Dinar noviy da Iugoslávia","displayName-count-other":"Dinares noviy da Iugoslávia",symbol:"YUM"},YUN:{displayName:"Dinar conversível iugoslavo (1990–1992)","displayName-count-one":"Dinar conversível da Iugoslávia","displayName-count-other":"Dinares conversíveis da Iugoslávia",symbol:"YUN"},YUR:{displayName:"Dinar reformado iugoslavo (1992–1993)","displayName-count-one":"Dinar iugoslavo reformado","displayName-count-other":"Dinares iugoslavos reformados",symbol:"YUR"},ZAL:{displayName:"Rand sul-africano (financeiro)","displayName-count-one":"Rand da África do Sul (financeiro)","displayName-count-other":"Rands da África do Sul (financeiro)",symbol:"ZAL"},ZAR:{displayName:"rand sul-africano","displayName-count-one":"rand sul-africano","displayName-count-other":"rands sul-africanos",symbol:"ZAR","symbol-alt-narrow":"R"},ZMK:{displayName:"Kwacha zambiano (1968–2012)","displayName-count-one":"Kwacha zambiano (1968–2012)","displayName-count-other":"Kwachas zambianos (1968–2012)",symbol:"ZMK"},ZMW:{displayName:"kwacha zambiano","displayName-count-one":"kwacha zambiano","displayName-count-other":"kwachas zambianos",symbol:"ZMW","symbol-alt-narrow":"ZK"},ZRN:{displayName:"Zaire Novo zairense (1993–1998)","displayName-count-one":"Novo zaire do Zaire","displayName-count-other":"Novos zaires do Zaire",symbol:"ZRN"},ZRZ:{displayName:"Zaire zairense (1971–1993)","displayName-count-one":"Zaire do Zaire","displayName-count-other":"Zaires do Zaire",symbol:"ZRZ"},ZWD:{displayName:"Dólar do Zimbábue (1980–2008)","displayName-count-one":"Dólar do Zimbábue","displayName-count-other":"Dólares do Zimbábue",symbol:"ZWD"},ZWL:{displayName:"Dólar do Zimbábue (2009)","displayName-count-one":"Dólar do Zimbábue (2009)","displayName-count-other":"Dólares do Zimbábue (2009)",symbol:"ZWL"},ZWR:{displayName:"Dólar do Zimbábue (2008)","displayName-count-one":"Dólar do Zimbábue (2008)","displayName-count-other":"Dólares do Zimbábue (2008)",symbol:"ZWR"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},currencyData:{fractions:{ADP:{_rounding:"0",_digits:"0"},AFN:{_rounding:"0",_digits:"0"},ALL:{_rounding:"0",_digits:"0"},AMD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},BHD:{_rounding:"0",_digits:"3"},BIF:{_rounding:"0",_digits:"0"},BYN:{_rounding:"0",_digits:"2"},BYR:{_rounding:"0",_digits:"0"},CAD:{_rounding:"0",_digits:"2",_cashRounding:"5"},CHF:{_rounding:"0",_digits:"2",_cashRounding:"5"},CLF:{_rounding:"0",_digits:"4"},CLP:{_rounding:"0",_digits:"0"},COP:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CRC:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CZK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},DEFAULT:{_rounding:"0",_digits:"2"},DJF:{_rounding:"0",_digits:"0"},DKK:{_rounding:"0",_digits:"2",_cashRounding:"50"},ESP:{_rounding:"0",_digits:"0"},GNF:{_rounding:"0",_digits:"0"},GYD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},HUF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IDR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IQD:{_rounding:"0",_digits:"0"},IRR:{_rounding:"0",_digits:"0"},ISK:{_rounding:"0",_digits:"0"},ITL:{_rounding:"0",_digits:"0"},JOD:{_rounding:"0",_digits:"3"},JPY:{_rounding:"0",_digits:"0"},KMF:{_rounding:"0",_digits:"0"},KPW:{_rounding:"0",_digits:"0"},KRW:{_rounding:"0",_digits:"0"},KWD:{_rounding:"0",_digits:"3"},LAK:{_rounding:"0",_digits:"0"},LBP:{_rounding:"0",_digits:"0"},LUF:{_rounding:"0",_digits:"0"},LYD:{_rounding:"0",_digits:"3"},MGA:{_rounding:"0",_digits:"0"},MGF:{_rounding:"0",_digits:"0"},MMK:{_rounding:"0",_digits:"0"},MNT:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},MRO:{_rounding:"0",_digits:"0"},MUR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},NOK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},OMR:{_rounding:"0",_digits:"3"},PKR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},PYG:{_rounding:"0",_digits:"0"},RSD:{_rounding:"0",_digits:"0"},RWF:{_rounding:"0",_digits:"0"},SEK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},SLL:{_rounding:"0",_digits:"0"},SOS:{_rounding:"0",_digits:"0"},STD:{_rounding:"0",_digits:"0"},SYP:{_rounding:"0",_digits:"0"},TMM:{_rounding:"0",_digits:"0"},TND:{_rounding:"0",_digits:"3"},TRL:{_rounding:"0",_digits:"0"},TWD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},TZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},UGX:{_rounding:"0",_digits:"0"},UYI:{_rounding:"0",_digits:"0"},UYW:{_rounding:"0",_digits:"4"},UZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VEF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VND:{_rounding:"0",_digits:"0"},VUV:{_rounding:"0",_digits:"0"},XAF:{_rounding:"0",_digits:"0"},XOF:{_rounding:"0",_digits:"0"},XPF:{_rounding:"0",_digits:"0"},YER:{_rounding:"0",_digits:"0"},ZMK:{_rounding:"0",_digits:"0"},ZWD:{_rounding:"0",_digits:"0"}},region:{AC:[{SHP:{_from:"1976-01-01"}}],AD:[{ESP:{_from:"1873-01-01",_to:"2002-02-28"}},{ADP:{_from:"1936-01-01",_to:"2001-12-31"}},{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],AE:[{AED:{_from:"1973-05-19"}}],AF:[{AFA:{_from:"1927-03-14",_to:"2002-12-31"}},{AFN:{_from:"2002-10-07"}}],AG:[{XCD:{_from:"1965-10-06"}}],AI:[{XCD:{_from:"1965-10-06"}}],AL:[{ALK:{_from:"1946-11-01",_to:"1965-08-16"}},{ALL:{_from:"1965-08-16"}}],AM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-22"}},{AMD:{_from:"1993-11-22"}}],AO:[{AOK:{_from:"1977-01-08",_to:"1991-03-01"}},{AON:{_from:"1990-09-25",_to:"2000-02-01"}},{AOR:{_from:"1995-07-01",_to:"2000-02-01"}},{AOA:{_from:"1999-12-13"}}],AQ:[{XXX:{_tender:"false"}}],AR:[{ARM:{_from:"1881-11-05",_to:"1970-01-01"}},{ARL:{_from:"1970-01-01",_to:"1983-06-01"}},{ARP:{_from:"1983-06-01",_to:"1985-06-14"}},{ARA:{_from:"1985-06-14",_to:"1992-01-01"}},{ARS:{_from:"1992-01-01"}}],AS:[{USD:{_from:"1904-07-16"}}],AT:[{ATS:{_from:"1947-12-04",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],AU:[{AUD:{_from:"1966-02-14"}}],AW:[{ANG:{_from:"1940-05-10",_to:"1986-01-01"}},{AWG:{_from:"1986-01-01"}}],AX:[{EUR:{_from:"1999-01-01"}}],AZ:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-01-01"}},{AZM:{_from:"1993-11-22",_to:"2006-12-31"}},{AZN:{_from:"2006-01-01"}}],BA:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-01"}},{YUR:{_from:"1992-07-01",_to:"1993-10-01"}},{BAD:{_from:"1992-07-01",_to:"1994-08-15"}},{BAN:{_from:"1994-08-15",_to:"1997-07-01"}},{BAM:{_from:"1995-01-01"}}],BB:[{XCD:{_from:"1965-10-06",_to:"1973-12-03"}},{BBD:{_from:"1973-12-03"}}],BD:[{INR:{_from:"1835-08-17",_to:"1948-04-01"}},{PKR:{_from:"1948-04-01",_to:"1972-01-01"}},{BDT:{_from:"1972-01-01"}}],BE:[{NLG:{_from:"1816-12-15",_to:"1831-02-07"}},{BEF:{_from:"1831-02-07",_to:"2002-02-28"}},{BEC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{BEL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],BF:[{XOF:{_from:"1984-08-04"}}],BG:[{BGO:{_from:"1879-07-08",_to:"1952-05-12"}},{BGM:{_from:"1952-05-12",_to:"1962-01-01"}},{BGL:{_from:"1962-01-01",_to:"1999-07-05"}},{BGN:{_from:"1999-07-05"}}],BH:[{BHD:{_from:"1965-10-16"}}],BI:[{BIF:{_from:"1964-05-19"}}],BJ:[{XOF:{_from:"1975-11-30"}}],BL:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],BM:[{BMD:{_from:"1970-02-06"}}],BN:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{BND:{_from:"1967-06-12"}}],BO:[{BOV:{_tender:"false"}},{BOL:{_from:"1863-06-23",_to:"1963-01-01"}},{BOP:{_from:"1963-01-01",_to:"1986-12-31"}},{BOB:{_from:"1987-01-01"}}],BQ:[{ANG:{_from:"2010-10-10",_to:"2011-01-01"}},{USD:{_from:"2011-01-01"}}],BR:[{BRZ:{_from:"1942-11-01",_to:"1967-02-13"}},{BRB:{_from:"1967-02-13",_to:"1986-02-28"}},{BRC:{_from:"1986-02-28",_to:"1989-01-15"}},{BRN:{_from:"1989-01-15",_to:"1990-03-16"}},{BRE:{_from:"1990-03-16",_to:"1993-08-01"}},{BRR:{_from:"1993-08-01",_to:"1994-07-01"}},{BRL:{_from:"1994-07-01"}}],BS:[{BSD:{_from:"1966-05-25"}}],BT:[{INR:{_from:"1907-01-01"}},{BTN:{_from:"1974-04-16"}}],BU:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}}],BV:[{NOK:{_from:"1905-06-07"}}],BW:[{ZAR:{_from:"1961-02-14",_to:"1976-08-23"}},{BWP:{_from:"1976-08-23"}}],BY:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-11-08"}},{BYB:{_from:"1994-08-01",_to:"2000-12-31"}},{BYR:{_from:"2000-01-01",_to:"2017-01-01"}},{BYN:{_from:"2016-07-01"}}],BZ:[{BZD:{_from:"1974-01-01"}}],CA:[{CAD:{_from:"1858-01-01"}}],CC:[{AUD:{_from:"1966-02-14"}}],CD:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-01"}},{CDF:{_from:"1998-07-01"}}],CF:[{XAF:{_from:"1993-01-01"}}],CG:[{XAF:{_from:"1993-01-01"}}],CH:[{CHE:{_tender:"false"}},{CHW:{_tender:"false"}},{CHF:{_from:"1799-03-17"}}],CI:[{XOF:{_from:"1958-12-04"}}],CK:[{NZD:{_from:"1967-07-10"}}],CL:[{CLF:{_tender:"false"}},{CLE:{_from:"1960-01-01",_to:"1975-09-29"}},{CLP:{_from:"1975-09-29"}}],CM:[{XAF:{_from:"1973-04-01"}}],CN:[{CNY:{_from:"1953-03-01"}},{CNX:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{CNH:{_tender:"false",_from:"2010-07-19"}}],CO:[{COU:{_tender:"false"}},{COP:{_from:"1905-01-01"}}],CP:[{XXX:{_tender:"false"}}],CR:[{CRC:{_from:"1896-10-26"}}],CS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-06-03"}},{EUR:{_from:"2003-02-04",_to:"2006-06-03"}}],CU:[{CUP:{_from:"1859-01-01"}},{USD:{_from:"1899-01-01",_to:"1959-01-01"}},{CUC:{_from:"1994-01-01"}}],CV:[{PTE:{_from:"1911-05-22",_to:"1975-07-05"}},{CVE:{_from:"1914-01-01"}}],CW:[{ANG:{_from:"2010-10-10"}}],CX:[{AUD:{_from:"1966-02-14"}}],CY:[{CYP:{_from:"1914-09-10",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],CZ:[{CSK:{_from:"1953-06-01",_to:"1993-03-01"}},{CZK:{_from:"1993-01-01"}}],DD:[{DDM:{_from:"1948-07-20",_to:"1990-10-02"}}],DE:[{DEM:{_from:"1948-06-20",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],DG:[{USD:{_from:"1965-11-08"}}],DJ:[{DJF:{_from:"1977-06-27"}}],DK:[{DKK:{_from:"1873-05-27"}}],DM:[{XCD:{_from:"1965-10-06"}}],DO:[{USD:{_from:"1905-06-21",_to:"1947-10-01"}},{DOP:{_from:"1947-10-01"}}],DZ:[{DZD:{_from:"1964-04-01"}}],EA:[{EUR:{_from:"1999-01-01"}}],EC:[{ECS:{_from:"1884-04-01",_to:"2000-10-02"}},{ECV:{_tender:"false",_from:"1993-05-23",_to:"2000-01-09"}},{USD:{_from:"2000-10-02"}}],EE:[{SUR:{_from:"1961-01-01",_to:"1992-06-20"}},{EEK:{_from:"1992-06-21",_to:"2010-12-31"}},{EUR:{_from:"2011-01-01"}}],EG:[{EGP:{_from:"1885-11-14"}}],EH:[{MAD:{_from:"1976-02-26"}}],ER:[{ETB:{_from:"1993-05-24",_to:"1997-11-08"}},{ERN:{_from:"1997-11-08"}}],ES:[{ESP:{_from:"1868-10-19",_to:"2002-02-28"}},{ESB:{_tender:"false",_from:"1975-01-01",_to:"1994-12-31"}},{ESA:{_tender:"false",_from:"1978-01-01",_to:"1981-12-31"}},{EUR:{_from:"1999-01-01"}}],ET:[{ETB:{_from:"1976-09-15"}}],EU:[{XEU:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{EUR:{_from:"1999-01-01"}}],FI:[{FIM:{_from:"1963-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],FJ:[{FJD:{_from:"1969-01-13"}}],FK:[{FKP:{_from:"1901-01-01"}}],FM:[{JPY:{_from:"1914-10-03",_to:"1944-01-01"}},{USD:{_from:"1944-01-01"}}],FO:[{DKK:{_from:"1948-01-01"}}],FR:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GA:[{XAF:{_from:"1993-01-01"}}],GB:[{GBP:{_from:"1694-07-27"}}],GD:[{XCD:{_from:"1967-02-27"}}],GE:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-06-11"}},{GEK:{_from:"1993-04-05",_to:"1995-09-25"}},{GEL:{_from:"1995-09-23"}}],GF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GG:[{GBP:{_from:"1830-01-01"}}],GH:[{GHC:{_from:"1979-03-09",_to:"2007-12-31"}},{GHS:{_from:"2007-07-03"}}],GI:[{GIP:{_from:"1713-01-01"}}],GL:[{DKK:{_from:"1873-05-27"}}],GM:[{GMD:{_from:"1971-07-01"}}],GN:[{GNS:{_from:"1972-10-02",_to:"1986-01-06"}},{GNF:{_from:"1986-01-06"}}],GP:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GQ:[{GQE:{_from:"1975-07-07",_to:"1986-06-01"}},{XAF:{_from:"1993-01-01"}}],GR:[{GRD:{_from:"1954-05-01",_to:"2002-02-28"}},{EUR:{_from:"2001-01-01"}}],GS:[{GBP:{_from:"1908-01-01"}}],GT:[{GTQ:{_from:"1925-05-27"}}],GU:[{USD:{_from:"1944-08-21"}}],GW:[{GWE:{_from:"1914-01-01",_to:"1976-02-28"}},{GWP:{_from:"1976-02-28",_to:"1997-03-31"}},{XOF:{_from:"1997-03-31"}}],GY:[{GYD:{_from:"1966-05-26"}}],HK:[{HKD:{_from:"1895-02-02"}}],HM:[{AUD:{_from:"1967-02-16"}}],HN:[{HNL:{_from:"1926-04-03"}}],HR:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1991-12-23"}},{HRD:{_from:"1991-12-23",_to:"1995-01-01"}},{HRK:{_from:"1994-05-30"}}],HT:[{HTG:{_from:"1872-08-26"}},{USD:{_from:"1915-01-01"}}],HU:[{HUF:{_from:"1946-07-23"}}],IC:[{EUR:{_from:"1999-01-01"}}],ID:[{IDR:{_from:"1965-12-13"}}],IE:[{GBP:{_from:"1800-01-01",_to:"1922-01-01"}},{IEP:{_from:"1922-01-01",_to:"2002-02-09"}},{EUR:{_from:"1999-01-01"}}],IL:[{ILP:{_from:"1948-08-16",_to:"1980-02-22"}},{ILR:{_from:"1980-02-22",_to:"1985-09-04"}},{ILS:{_from:"1985-09-04"}}],IM:[{GBP:{_from:"1840-01-03"}}],IN:[{INR:{_from:"1835-08-17"}}],IO:[{USD:{_from:"1965-11-08"}}],IQ:[{EGP:{_from:"1920-11-11",_to:"1931-04-19"}},{INR:{_from:"1920-11-11",_to:"1931-04-19"}},{IQD:{_from:"1931-04-19"}}],IR:[{IRR:{_from:"1932-05-13"}}],IS:[{DKK:{_from:"1873-05-27",_to:"1918-12-01"}},{ISJ:{_from:"1918-12-01",_to:"1981-01-01"}},{ISK:{_from:"1981-01-01"}}],IT:[{ITL:{_from:"1862-08-24",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],JE:[{GBP:{_from:"1837-01-01"}}],JM:[{JMD:{_from:"1969-09-08"}}],JO:[{JOD:{_from:"1950-07-01"}}],JP:[{JPY:{_from:"1871-06-01"}}],KE:[{KES:{_from:"1966-09-14"}}],KG:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-05-10"}},{KGS:{_from:"1993-05-10"}}],KH:[{KHR:{_from:"1980-03-20"}}],KI:[{AUD:{_from:"1966-02-14"}}],KM:[{KMF:{_from:"1975-07-06"}}],KN:[{XCD:{_from:"1965-10-06"}}],KP:[{KPW:{_from:"1959-04-17"}}],KR:[{KRO:{_from:"1945-08-15",_to:"1953-02-15"}},{KRH:{_from:"1953-02-15",_to:"1962-06-10"}},{KRW:{_from:"1962-06-10"}}],KW:[{KWD:{_from:"1961-04-01"}}],KY:[{JMD:{_from:"1969-09-08",_to:"1971-01-01"}},{KYD:{_from:"1971-01-01"}}],KZ:[{KZT:{_from:"1993-11-05"}}],LA:[{LAK:{_from:"1979-12-10"}}],LB:[{LBP:{_from:"1948-02-02"}}],LC:[{XCD:{_from:"1965-10-06"}}],LI:[{CHF:{_from:"1921-02-01"}}],LK:[{LKR:{_from:"1978-05-22"}}],LR:[{LRD:{_from:"1944-01-01"}}],LS:[{ZAR:{_from:"1961-02-14"}},{LSL:{_from:"1980-01-22"}}],LT:[{SUR:{_from:"1961-01-01",_to:"1992-10-01"}},{LTT:{_from:"1992-10-01",_to:"1993-06-25"}},{LTL:{_from:"1993-06-25",_to:"2014-12-31"}},{EUR:{_from:"2015-01-01"}}],LU:[{LUF:{_from:"1944-09-04",_to:"2002-02-28"}},{LUC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{LUL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],LV:[{SUR:{_from:"1961-01-01",_to:"1992-07-20"}},{LVR:{_from:"1992-05-07",_to:"1993-10-17"}},{LVL:{_from:"1993-06-28",_to:"2013-12-31"}},{EUR:{_from:"2014-01-01"}}],LY:[{LYD:{_from:"1971-09-01"}}],MA:[{MAF:{_from:"1881-01-01",_to:"1959-10-17"}},{MAD:{_from:"1959-10-17"}}],MC:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{MCF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MD:[{MDC:{_from:"1992-06-01",_to:"1993-11-29"}},{MDL:{_from:"1993-11-29"}}],ME:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{DEM:{_from:"1999-10-02",_to:"2002-05-15"}},{EUR:{_from:"2002-01-01"}}],MF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MG:[{MGF:{_from:"1963-07-01",_to:"2004-12-31"}},{MGA:{_from:"1983-11-01"}}],MH:[{USD:{_from:"1944-01-01"}}],MK:[{MKN:{_from:"1992-04-26",_to:"1993-05-20"}},{MKD:{_from:"1993-05-20"}}],ML:[{XOF:{_from:"1958-11-24",_to:"1962-07-02"}},{MLF:{_from:"1962-07-02",_to:"1984-08-31"}},{XOF:{_from:"1984-06-01"}}],MM:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}},{MMK:{_from:"1989-06-18"}}],MN:[{MNT:{_from:"1915-03-01"}}],MO:[{MOP:{_from:"1901-01-01"}}],MP:[{USD:{_from:"1944-01-01"}}],MQ:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MR:[{XOF:{_from:"1958-11-28",_to:"1973-06-29"}},{MRO:{_from:"1973-06-29",_to:"2018-06-30"}},{MRU:{_from:"2018-01-01"}}],MS:[{XCD:{_from:"1967-02-27"}}],MT:[{MTP:{_from:"1914-08-13",_to:"1968-06-07"}},{MTL:{_from:"1968-06-07",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],MU:[{MUR:{_from:"1934-04-01"}}],MV:[{MVP:{_from:"1947-01-01",_to:"1981-07-01"}},{MVR:{_from:"1981-07-01"}}],MW:[{MWK:{_from:"1971-02-15"}}],MX:[{MXV:{_tender:"false"}},{MXP:{_from:"1822-01-01",_to:"1992-12-31"}},{MXN:{_from:"1993-01-01"}}],MY:[{MYR:{_from:"1963-09-16"}}],MZ:[{MZE:{_from:"1975-06-25",_to:"1980-06-16"}},{MZM:{_from:"1980-06-16",_to:"2006-12-31"}},{MZN:{_from:"2006-07-01"}}],NA:[{ZAR:{_from:"1961-02-14"}},{NAD:{_from:"1993-01-01"}}],NC:[{XPF:{_from:"1985-01-01"}}],NE:[{XOF:{_from:"1958-12-19"}}],NF:[{AUD:{_from:"1966-02-14"}}],NG:[{NGN:{_from:"1973-01-01"}}],NI:[{NIC:{_from:"1988-02-15",_to:"1991-04-30"}},{NIO:{_from:"1991-04-30"}}],NL:[{NLG:{_from:"1813-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],NO:[{SEK:{_from:"1873-05-27",_to:"1905-06-07"}},{NOK:{_from:"1905-06-07"}}],NP:[{INR:{_from:"1870-01-01",_to:"1966-10-17"}},{NPR:{_from:"1933-01-01"}}],NR:[{AUD:{_from:"1966-02-14"}}],NU:[{NZD:{_from:"1967-07-10"}}],NZ:[{NZD:{_from:"1967-07-10"}}],OM:[{OMR:{_from:"1972-11-11"}}],PA:[{PAB:{_from:"1903-11-04"}},{USD:{_from:"1903-11-18"}}],PE:[{PES:{_from:"1863-02-14",_to:"1985-02-01"}},{PEI:{_from:"1985-02-01",_to:"1991-07-01"}},{PEN:{_from:"1991-07-01"}}],PF:[{XPF:{_from:"1945-12-26"}}],PG:[{AUD:{_from:"1966-02-14",_to:"1975-09-16"}},{PGK:{_from:"1975-09-16"}}],PH:[{PHP:{_from:"1946-07-04"}}],PK:[{INR:{_from:"1835-08-17",_to:"1947-08-15"}},{PKR:{_from:"1948-04-01"}}],PL:[{PLZ:{_from:"1950-10-28",_to:"1994-12-31"}},{PLN:{_from:"1995-01-01"}}],PM:[{FRF:{_from:"1972-12-21",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],PN:[{NZD:{_from:"1969-01-13"}}],PR:[{ESP:{_from:"1800-01-01",_to:"1898-12-10"}},{USD:{_from:"1898-12-10"}}],PS:[{JOD:{_from:"1950-07-01",_to:"1967-06-01"}},{ILP:{_from:"1967-06-01",_to:"1980-02-22"}},{ILS:{_from:"1985-09-04"}},{JOD:{_from:"1996-02-12"}}],PT:[{PTE:{_from:"1911-05-22",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],PW:[{USD:{_from:"1944-01-01"}}],PY:[{PYG:{_from:"1943-11-01"}}],QA:[{QAR:{_from:"1973-05-19"}}],RE:[{FRF:{_from:"1975-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],RO:[{ROL:{_from:"1952-01-28",_to:"2006-12-31"}},{RON:{_from:"2005-07-01"}}],RS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-10-25"}},{RSD:{_from:"2006-10-25"}}],RU:[{RUR:{_from:"1991-12-25",_to:"1998-12-31"}},{RUB:{_from:"1999-01-01"}}],RW:[{RWF:{_from:"1964-05-19"}}],SA:[{SAR:{_from:"1952-10-22"}}],SB:[{AUD:{_from:"1966-02-14",_to:"1978-06-30"}},{SBD:{_from:"1977-10-24"}}],SC:[{SCR:{_from:"1903-11-01"}}],SD:[{EGP:{_from:"1889-01-19",_to:"1958-01-01"}},{GBP:{_from:"1889-01-19",_to:"1958-01-01"}},{SDP:{_from:"1957-04-08",_to:"1998-06-01"}},{SDD:{_from:"1992-06-08",_to:"2007-06-30"}},{SDG:{_from:"2007-01-10"}}],SE:[{SEK:{_from:"1873-05-27"}}],SG:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{SGD:{_from:"1967-06-12"}}],SH:[{SHP:{_from:"1917-02-15"}}],SI:[{SIT:{_from:"1992-10-07",_to:"2007-01-14"}},{EUR:{_from:"2007-01-01"}}],SJ:[{NOK:{_from:"1905-06-07"}}],SK:[{CSK:{_from:"1953-06-01",_to:"1992-12-31"}},{SKK:{_from:"1992-12-31",_to:"2009-01-01"}},{EUR:{_from:"2009-01-01"}}],SL:[{GBP:{_from:"1808-11-30",_to:"1966-02-04"}},{SLL:{_from:"1964-08-04"}}],SM:[{ITL:{_from:"1865-12-23",_to:"2001-02-28"}},{EUR:{_from:"1999-01-01"}}],SN:[{XOF:{_from:"1959-04-04"}}],SO:[{SOS:{_from:"1960-07-01"}}],SR:[{NLG:{_from:"1815-11-20",_to:"1940-05-10"}},{SRG:{_from:"1940-05-10",_to:"2003-12-31"}},{SRD:{_from:"2004-01-01"}}],SS:[{SDG:{_from:"2007-01-10",_to:"2011-09-01"}},{SSP:{_from:"2011-07-18"}}],ST:[{STD:{_from:"1977-09-08",_to:"2017-12-31"}},{STN:{_from:"2018-01-01"}}],SU:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}}],SV:[{SVC:{_from:"1919-11-11",_to:"2001-01-01"}},{USD:{_from:"2001-01-01"}}],SX:[{ANG:{_from:"2010-10-10"}}],SY:[{SYP:{_from:"1948-01-01"}}],SZ:[{SZL:{_from:"1974-09-06"}}],TA:[{GBP:{_from:"1938-01-12"}}],TC:[{USD:{_from:"1969-09-08"}}],TD:[{XAF:{_from:"1993-01-01"}}],TF:[{FRF:{_from:"1959-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],TG:[{XOF:{_from:"1958-11-28"}}],TH:[{THB:{_from:"1928-04-15"}}],TJ:[{RUR:{_from:"1991-12-25",_to:"1995-05-10"}},{TJR:{_from:"1995-05-10",_to:"2000-10-25"}},{TJS:{_from:"2000-10-26"}}],TK:[{NZD:{_from:"1967-07-10"}}],TL:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}},{USD:{_from:"1999-10-20"}}],TM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-01"}},{TMM:{_from:"1993-11-01",_to:"2009-01-01"}},{TMT:{_from:"2009-01-01"}}],TN:[{TND:{_from:"1958-11-01"}}],TO:[{TOP:{_from:"1966-02-14"}}],TP:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}}],TR:[{TRL:{_from:"1922-11-01",_to:"2005-12-31"}},{TRY:{_from:"2005-01-01"}}],TT:[{TTD:{_from:"1964-01-01"}}],TV:[{AUD:{_from:"1966-02-14"}}],TW:[{TWD:{_from:"1949-06-15"}}],TZ:[{TZS:{_from:"1966-06-14"}}],UA:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1992-11-13"}},{UAK:{_from:"1992-11-13",_to:"1993-10-17"}},{UAH:{_from:"1996-09-02"}}],UG:[{UGS:{_from:"1966-08-15",_to:"1987-05-15"}},{UGX:{_from:"1987-05-15"}}],UM:[{USD:{_from:"1944-01-01"}}],US:[{USN:{_tender:"false"}},{USS:{_tender:"false",_to:"2014-03-01"}},{USD:{_from:"1792-01-01"}}],UY:[{UYI:{_tender:"false"}},{UYW:{_tender:"false"}},{UYP:{_from:"1975-07-01",_to:"1993-03-01"}},{UYU:{_from:"1993-03-01"}}],UZ:[{UZS:{_from:"1994-07-01"}}],VA:[{ITL:{_from:"1870-10-19",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],VC:[{XCD:{_from:"1965-10-06"}}],VE:[{VEB:{_from:"1871-05-11",_to:"2008-06-30"}},{VEF:{_from:"2008-01-01",_to:"2018-08-20"}},{VES:{_from:"2018-08-20"}}],VG:[{USD:{_from:"1833-01-01"}},{GBP:{_from:"1833-01-01",_to:"1959-01-01"}}],VI:[{USD:{_from:"1837-01-01"}}],VN:[{VNN:{_from:"1978-05-03",_to:"1985-09-14"}},{VND:{_from:"1985-09-14"}}],VU:[{VUV:{_from:"1981-01-01"}}],WF:[{XPF:{_from:"1961-07-30"}}],WS:[{WST:{_from:"1967-07-10"}}],XK:[{YUM:{_from:"1994-01-24",_to:"1999-09-30"}},{DEM:{_from:"1999-09-01",_to:"2002-03-09"}},{EUR:{_from:"2002-01-01"}}],YD:[{YDD:{_from:"1965-04-01",_to:"1996-01-01"}}],YE:[{YER:{_from:"1990-05-22"}}],YT:[{KMF:{_from:"1975-01-01",_to:"1976-02-23"}},{FRF:{_from:"1976-02-23",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],YU:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-24"}},{YUM:{_from:"1994-01-24",_to:"2002-05-15"}}],ZA:[{ZAR:{_from:"1961-02-14"}},{ZAL:{_tender:"false",_from:"1985-09-01",_to:"1995-03-13"}}],ZM:[{ZMK:{_from:"1968-01-16",_to:"2013-01-01"}},{ZMW:{_from:"2013-01-01"}}],ZR:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-31"}}],ZW:[{RHD:{_from:"1970-02-17",_to:"1980-04-18"}},{ZWD:{_from:"1980-04-18",_to:"2008-08-01"}},{ZWR:{_from:"2008-08-01",_to:"2009-02-02"}},{ZWL:{_from:"2009-02-02",_to:"2009-04-12"}},{USD:{_from:"2009-04-12"}}],ZZ:[{XAG:{_tender:"false"}},{XAU:{_tender:"false"}},{XBA:{_tender:"false"}},{XBB:{_tender:"false"}},{XBC:{_tender:"false"}},{XBD:{_tender:"false"}},{XDR:{_tender:"false"}},{XPD:{_tender:"false"}},{XPT:{_tender:"false"}},{XSU:{_tender:"false"}},{XTS:{_tender:"false"}},{XUA:{_tender:"false"}},{XXX:{_tender:"false"}},{XRE:{_tender:"false",_to:"1999-11-30"}},{XFU:{_tender:"false",_to:"2013-11-30"}},{XFO:{_tender:"false",_from:"1930-01-01",_to:"2003-04-01"}}]}}}},{main:{"pt-PT":{identity:{version:{_cldrVersion:"36"},language:"pt",territory:"PT"},dates:{calendars:{gregorian:{months:{format:{abbreviated:{1:"jan.",2:"fev.",3:"mar.",4:"abr.",5:"mai.",6:"jun.",7:"jul.",8:"ago.",9:"set.",10:"out.",11:"nov.",12:"dez."},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"janeiro",2:"fevereiro",3:"março",4:"abril",5:"maio",6:"junho",7:"julho",8:"agosto",9:"setembro",10:"outubro",11:"novembro",12:"dezembro"}},"stand-alone":{abbreviated:{1:"jan.",2:"fev.",3:"mar.",4:"abr.",5:"mai.",6:"jun.",7:"jul.",8:"ago.",9:"set.",10:"out.",11:"nov.",12:"dez."},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"janeiro",2:"fevereiro",3:"março",4:"abril",5:"maio",6:"junho",7:"julho",8:"agosto",9:"setembro",10:"outubro",11:"novembro",12:"dezembro"}}},days:{format:{abbreviated:{sun:"domingo",mon:"segunda",tue:"terça",wed:"quarta",thu:"quinta",fri:"sexta",sat:"sábado"},narrow:{sun:"D",mon:"S",tue:"T",wed:"Q",thu:"Q",fri:"S",sat:"S"},short:{sun:"domingo",mon:"segunda",tue:"terça",wed:"quarta",thu:"quinta",fri:"sexta",sat:"sábado"},wide:{sun:"domingo",mon:"segunda-feira",tue:"terça-feira",wed:"quarta-feira",thu:"quinta-feira",fri:"sexta-feira",sat:"sábado"}},"stand-alone":{abbreviated:{sun:"domingo",mon:"segunda",tue:"terça",wed:"quarta",thu:"quinta",fri:"sexta",sat:"sábado"},narrow:{sun:"D",mon:"S",tue:"T",wed:"Q",thu:"Q",fri:"S",sat:"S"},short:{sun:"domingo",mon:"segunda",tue:"terça",wed:"quarta",thu:"quinta",fri:"sexta",sat:"sábado"},wide:{sun:"domingo",mon:"segunda-feira",tue:"terça-feira",wed:"quarta-feira",thu:"quinta-feira",fri:"sexta-feira",sat:"sábado"}}},quarters:{format:{abbreviated:{1:"T1",2:"T2",3:"T3",4:"T4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1.º trimestre",2:"2.º trimestre",3:"3.º trimestre",4:"4.º trimestre"}},"stand-alone":{abbreviated:{1:"T1",2:"T2",3:"T3",4:"T4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1.º trimestre",2:"2.º trimestre",3:"3.º trimestre",4:"4.º trimestre"}}},dayPeriods:{format:{abbreviated:{midnight:"meia-noite",am:"a.m.",noon:"meio-dia",pm:"p.m.",morning1:"da manhã",afternoon1:"da tarde",evening1:"da noite",night1:"da madrugada"},narrow:{midnight:"meia-noite",am:"a.m.",noon:"meio-dia",pm:"p.m.",morning1:"manhã",afternoon1:"tarde",evening1:"noite",night1:"madrugada"},wide:{midnight:"meia-noite",am:"da manhã",noon:"meio-dia",pm:"da tarde",morning1:"da manhã",afternoon1:"da tarde",evening1:"da noite",night1:"da madrugada"}},"stand-alone":{abbreviated:{midnight:"meia-noite",am:"a.m.",noon:"meio-dia",pm:"p.m.",morning1:"manhã",afternoon1:"tarde",evening1:"noite",night1:"madrugada"},narrow:{midnight:"meia-noite",am:"a.m.",noon:"meio-dia",pm:"p.m.",morning1:"manhã",afternoon1:"tarde",evening1:"noite",night1:"madrugada"},wide:{midnight:"meia-noite",am:"manhã",noon:"meio-dia",pm:"tarde",morning1:"manhã",afternoon1:"tarde",evening1:"noite",night1:"madrugada"}}},eras:{eraNames:{0:"antes de Cristo",1:"depois de Cristo","0-alt-variant":"antes da Era Comum","1-alt-variant":"Era Comum"},eraAbbr:{0:"a.C.",1:"d.C.","0-alt-variant":"a.E.C.","1-alt-variant":"E.C."},eraNarrow:{0:"a.C.",1:"d.C.","0-alt-variant":"a.E.C.","1-alt-variant":"E.C."}},dateFormats:{full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"dd/MM/y",short:"dd/MM/yy"},timeFormats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},dateTimeFormats:{full:"{1} 'às' {0}",long:"{1} 'às' {0}",medium:"{1}, {0}",short:"{1}, {0}",availableFormats:{Bh:"h B",Bhm:"h:mm B",Bhms:"h:mm:ss B",d:"d",E:"ccc",EBhm:"E h:mm B",EBhms:"E h:mm:ss B",Ed:"E, d",Ehm:"E, h:mm a",EHm:"E, HH:mm",Ehms:"E, h:mm:ss a",EHms:"E, HH:mm:ss",Gy:"y G",GyMMM:"MMM 'de' y G",GyMMMd:"d 'de' MMM 'de' y G",GyMMMEd:"E, d 'de' MMM 'de' y G",h:"h a",H:"HH",hm:"h:mm a",Hm:"HH:mm",hms:"h:mm:ss a",Hms:"HH:mm:ss",hmsv:"h:mm:ss a v",Hmsv:"HH:mm:ss v",hmv:"h:mm a v",Hmv:"HH:mm v",M:"L",Md:"dd/MM",MEd:"E, dd/MM",MMdd:"dd/MM",MMM:"LLL",MMMd:"d/MM",MMMEd:"E, d/MM",MMMMd:"d 'de' MMMM",MMMMEd:"ccc, d 'de' MMMM","MMMMW-count-one":"W.'ª' 'semana' 'de' MMMM","MMMMW-count-other":"W.'ª' 'semana' 'de' MMMM",ms:"mm:ss",y:"y",yM:"MM/y",yMd:"dd/MM/y",yMEd:"E, dd/MM/y",yMM:"MM/y",yMMM:"MM/y",yMMMd:"d/MM/y",yMMMEd:"E, d/MM/y",yMMMEEEEd:"EEEE, d/MM/y",yMMMM:"MMMM 'de' y",yMMMMd:"d 'de' MMMM 'de' y",yMMMMEd:"ccc, d 'de' MMMM 'de' y",yQQQ:"QQQQ 'de' y",yQQQQ:"QQQQ 'de' y","yw-count-one":"w.'ª' 'semana' 'de' Y","yw-count-other":"w.'ª' 'semana' 'de' Y"},appendItems:{Day:"{0} ({2}: {1})","Day-Of-Week":"{0} {1}",Era:"{1} {0}",Hour:"{0} ({2}: {1})",Minute:"{0} ({2}: {1})",Month:"{0} ({2}: {1})",Quarter:"{0} ({2}: {1})",Second:"{0} ({2}: {1})",Timezone:"{0} {1}",Week:"{0} ({2}: {1})",Year:"{1} {0}"},intervalFormats:{intervalFormatFallback:"{0} - {1}",Bh:{B:"h B – h B",h:"h – h B"},Bhm:{B:"h:mm B – h:mm B",h:"h:mm – h:mm B",m:"h:mm – h:mm B"},d:{d:"d–d"},Gy:{G:"y G – y G",y:"y – y G"},GyM:{G:"M/y GGGGG – M/y GGGGG",M:"M/y – M/y GGGGG",y:"M/y – M/y GGGGG"},GyMd:{d:"d/M/y – d/M/y GGGGG",G:"d/M/y GGGGG – d/M/y GGGGG",M:"d/M/y – d/M/y GGGGG",y:"d/M/y – d/M/y GGGGG"},GyMEd:{d:"E, d/M/y – E, d/M/y GGGGG",G:"E, d/M/y GGGGG – E, d/M/y GGGGG",M:"E, d/M/y – E, d/M/y GGGGG",y:"E, d/M/y – E, d/M/y GGGGG"},GyMMM:{G:"MMM y G – MMM y G",M:"MMM – MMM y G",y:"MMM y – MMM y G"},GyMMMd:{d:"d – d MMM y G",G:"d MMM y G – d MMM y G",M:"d MMM – d MMM y G",y:"d MMM y – d MMM y G"},GyMMMEd:{d:"E, d MMM – E, d MMM y G",G:"E, d MMM y G – E, d MMM y G",M:"E, d MMM – E, d MMM y G",y:"E, d MMM y – E, d MMM y G"},h:{a:"h a – h a",h:"h–h a"},H:{H:"HH–HH"},hm:{a:"h:mm a – h:mm a",h:"h:mm – h:mm a",m:"h:mm – h:mm a"},Hm:{H:"HH:mm – HH:mm",m:"HH:mm – HH:mm"},hmv:{a:"h:mm a – h:mm a v",h:"h:mm – h:mm a v",m:"h:mm – h:mm a v"},Hmv:{H:"HH:mm – HH:mm v",m:"HH:mm – HH:mm v"},hv:{a:"h a – h a v",h:"h–h a v"},Hv:{H:"HH – HH v"},M:{M:"M–M"},Md:{d:"dd/MM – dd/MM",M:"dd/MM – dd/MM"},MEd:{d:"ccc, dd/MM – ccc, dd/MM",M:"ccc, dd/MM – ccc, dd/MM"},MMM:{M:"MMM–MMM"},MMMd:{d:"d–d 'de' MMM",M:"d 'de' MMM – d 'de' MMM"},MMMEd:{d:"ccc, dd/MM – ccc, dd/MM",M:"ccc, dd/MM – ccc, dd/MM"},MMMMEd:{d:"ccc, d 'de' MMMM – ccc, d 'de' MMMM",M:"ccc, d 'de' MMMM – ccc, d 'de' MMMM"},y:{y:"y–y"},yM:{M:"MM/y – MM/y",y:"MM/y – MM/y"},yMd:{d:"dd/MM/y – dd/MM/y",M:"dd/MM/y – dd/MM/y",y:"dd/MM/y – dd/MM/y"},yMEd:{d:"ccc, dd/MM/y – ccc, dd/MM/y",M:"ccc, dd/MM/y – ccc, dd/MM/y",y:"ccc, dd/MM/y – ccc, dd/MM/y"},yMMM:{M:"MMM–MMM 'de' y",y:"MMM 'de' y – MMM 'de' y"},yMMMd:{d:"d–d 'de' MMM 'de' y",M:"d 'de' MMM – d 'de' MMM 'de' y",y:"d 'de' MMM 'de' y – d 'de' MMM 'de' y"},yMMMEd:{d:"E, dd/MM – E, dd/MM/y",M:"E, d 'de' MMM – E, d 'de' MMM 'de' y",y:"E, d 'de' MMM 'de' y – E, d 'de' MMM 'de' y"},yMMMM:{M:"MMMM – MMMM 'de' y",y:"MMMM 'de' y – MMMM 'de' y"},yMMMMEd:{d:"E, d 'de' MMMM – E, d 'de' MMMM 'de' y",M:"E, d 'de' MMMM – E, d 'de' MMMM 'de' y",y:"E, d 'de' MMMM 'de' y – E, d 'de' MMMM 'de' y"}}}}}}}}},{main:{"pt-PT":{identity:{version:{_cldrVersion:"36"},language:"pt",territory:"PT"},dates:{fields:{era:{displayName:"era"},"era-short":{displayName:"era"},"era-narrow":{displayName:"era"},year:{displayName:"ano","relative-type--1":"ano passado","relative-type-0":"este ano","relative-type-1":"próximo ano","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} ano","relativeTimePattern-count-other":"dentro de {0} anos"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} ano","relativeTimePattern-count-other":"há {0} anos"}},"year-short":{displayName:"ano","relative-type--1":"ano passado","relative-type-0":"este ano","relative-type-1":"próximo ano","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} ano","relativeTimePattern-count-other":"dentro de {0} anos"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} ano","relativeTimePattern-count-other":"há {0} anos"}},"year-narrow":{displayName:"ano","relative-type--1":"ano passado","relative-type-0":"este ano","relative-type-1":"próximo ano","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} ano","relativeTimePattern-count-other":"+{0} anos"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} ano","relativeTimePattern-count-other":"-{0} anos"}},quarter:{displayName:"trimestre","relative-type--1":"trimestre passado","relative-type-0":"este trimestre","relative-type-1":"próximo trimestre","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} trimestre","relativeTimePattern-count-other":"dentro de {0} trimestres"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} trimestre","relativeTimePattern-count-other":"há {0} trimestres"}},"quarter-short":{displayName:"trim.","relative-type--1":"trim. passado","relative-type-0":"este trim.","relative-type-1":"próximo trim.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} trim.","relativeTimePattern-count-other":"dentro de {0} trim."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} trim.","relativeTimePattern-count-other":"há {0} trim."}},"quarter-narrow":{displayName:"trim.","relative-type--1":"trim. passado","relative-type-0":"este trim.","relative-type-1":"próximo trim.","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} trim.","relativeTimePattern-count-other":"+{0} trim."},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} trim.","relativeTimePattern-count-other":"-{0} trim."}},month:{displayName:"mês","relative-type--1":"mês passado","relative-type-0":"este mês","relative-type-1":"próximo mês","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} mês","relativeTimePattern-count-other":"dentro de {0} meses"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} mês","relativeTimePattern-count-other":"há {0} meses"}},"month-short":{displayName:"mês","relative-type--1":"mês passado","relative-type-0":"este mês","relative-type-1":"próximo mês","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} mês","relativeTimePattern-count-other":"dentro de {0} meses"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} mês","relativeTimePattern-count-other":"há {0} meses"}},"month-narrow":{displayName:"mês","relative-type--1":"mês passado","relative-type-0":"este mês","relative-type-1":"próximo mês","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} mês","relativeTimePattern-count-other":"+{0} meses"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} mês","relativeTimePattern-count-other":"-{0} meses"}},week:{displayName:"semana","relative-type--1":"semana passada","relative-type-0":"esta semana","relative-type-1":"próxima semana","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} semana","relativeTimePattern-count-other":"dentro de {0} semanas"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} semana","relativeTimePattern-count-other":"há {0} semanas"},relativePeriod:"a semana de {0}"},"week-short":{displayName:"sem.","relative-type--1":"semana passada","relative-type-0":"esta semana","relative-type-1":"próxima semana","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} sem.","relativeTimePattern-count-other":"dentro de {0} sem."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} sem.","relativeTimePattern-count-other":"há {0} sem."},relativePeriod:"a sem. de {0}"},"week-narrow":{displayName:"sem.","relative-type--1":"semana passada","relative-type-0":"esta semana","relative-type-1":"próxima semana","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} sem.","relativeTimePattern-count-other":"+{0} sem."},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} sem.","relativeTimePattern-count-other":"-{0} sem."},relativePeriod:"a semana de {0}"},weekOfMonth:{displayName:"semana do mês"},"weekOfMonth-short":{displayName:"sem. do mês"},"weekOfMonth-narrow":{displayName:"sem. do mês"},day:{displayName:"dia","relative-type--2":"anteontem","relative-type--1":"ontem","relative-type-0":"hoje","relative-type-1":"amanhã","relative-type-2":"depois de amanhã","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} dia","relativeTimePattern-count-other":"dentro de {0} dias"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} dia","relativeTimePattern-count-other":"há {0} dias"}},"day-short":{displayName:"dia","relative-type--2":"anteontem","relative-type--1":"ontem","relative-type-0":"hoje","relative-type-1":"amanhã","relative-type-2":"depois de amanhã","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} dia","relativeTimePattern-count-other":"dentro de {0} dias"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} dia","relativeTimePattern-count-other":"há {0} dias"}},"day-narrow":{displayName:"dia","relative-type--2":"anteontem","relative-type--1":"ontem","relative-type-0":"hoje","relative-type-1":"amanhã","relative-type-2":"depois de amanhã","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} dia","relativeTimePattern-count-other":"+{0} dias"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} dias","relativeTimePattern-count-other":"há {0} dias"}},dayOfYear:{displayName:"dia do ano"},"dayOfYear-short":{displayName:"dia do ano"},"dayOfYear-narrow":{displayName:"dia do ano"},weekday:{displayName:"dia da semana"},"weekday-short":{displayName:"dia da semana"},"weekday-narrow":{displayName:"dia da semana"},weekdayOfMonth:{displayName:"dia da semana do mês"},"weekdayOfMonth-short":{displayName:"dia da sem. do mês"},"weekdayOfMonth-narrow":{displayName:"dia da sem. do mês"},sun:{"relative-type--1":"domingo passado","relative-type-0":"este domingo","relative-type-1":"próximo domingo","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} domingo","relativeTimePattern-count-other":"dentro de {0} domingos"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} domingo","relativeTimePattern-count-other":"há {0} domingos"}},"sun-short":{"relative-type--1":"domingo passado","relative-type-0":"este domingo","relative-type-1":"próximo domingo","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} domingo","relativeTimePattern-count-other":"dentro de {0} domingos"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} dom.","relativeTimePattern-count-other":"há {0} dom."}},"sun-narrow":{"relative-type--1":"dom. passado","relative-type-0":"este dom.","relative-type-1":"próximo dom.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} dom.","relativeTimePattern-count-other":"dentro de {0} dom."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} dom.","relativeTimePattern-count-other":"há {0} dom."}},mon:{"relative-type--1":"segunda-feira passada","relative-type-0":"esta segunda-feira","relative-type-1":"próxima segunda-feira","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} segunda-feira","relativeTimePattern-count-other":"dentro de {0} segundas-feiras"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} segunda-feira","relativeTimePattern-count-other":"há {0} segundas-feiras"}},"mon-short":{"relative-type--1":"segunda passada","relative-type-0":"esta segunda","relative-type-1":"próxima segunda","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} segunda","relativeTimePattern-count-other":"dentro de {0} segundas"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} segunda","relativeTimePattern-count-other":"há {0} segundas"}},"mon-narrow":{"relative-type--1":"seg. passada","relative-type-0":"esta seg.","relative-type-1":"próxima seg.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} seg.","relativeTimePattern-count-other":"dentro de {0} seg."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} seg.","relativeTimePattern-count-other":"há {0} seg."}},tue:{"relative-type--1":"terça-feira passada","relative-type-0":"esta terça-feira","relative-type-1":"próxima terça-feira","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} terça-feira","relativeTimePattern-count-other":"dentro de {0} terças-feiras"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} terça-feira","relativeTimePattern-count-other":"há {0} terças-feiras"}},"tue-short":{"relative-type--1":"terça passada","relative-type-0":"esta terça","relative-type-1":"próxima terça","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} terça","relativeTimePattern-count-other":"dentro de {0} terças"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} terça","relativeTimePattern-count-other":"há {0} terças"}},"tue-narrow":{"relative-type--1":"ter. passada","relative-type-0":"esta ter.","relative-type-1":"próxima ter.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} ter.","relativeTimePattern-count-other":"dentro de {0} ter."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} ter.","relativeTimePattern-count-other":"há {0} ter."}},wed:{"relative-type--1":"quarta-feira passada","relative-type-0":"esta quarta-feira","relative-type-1":"próxima quarta-feira","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} quarta-feira","relativeTimePattern-count-other":"dentro de {0} quartas-feiras"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} quarta-feira","relativeTimePattern-count-other":"há {0} quartas-feiras"}},"wed-short":{"relative-type--1":"quarta passada","relative-type-0":"esta quarta","relative-type-1":"próxima quarta","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} quarta","relativeTimePattern-count-other":"dentro de {0} quartas"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} quarta","relativeTimePattern-count-other":"há {0} quartas"}},"wed-narrow":{"relative-type--1":"qua. passada","relative-type-0":"esta qua.","relative-type-1":"próxima qua.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} qua.","relativeTimePattern-count-other":"dentro de {0} qua."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} qua.","relativeTimePattern-count-other":"há {0} qua."}},thu:{"relative-type--1":"quinta-feira passada","relative-type-0":"esta quinta-feira","relative-type-1":"próxima quinta-feira","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} quinta-feira","relativeTimePattern-count-other":"dentro de {0} quintas-feiras"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} quinta-feira","relativeTimePattern-count-other":"há {0} quintas-feiras"}},"thu-short":{"relative-type--1":"quinta passada","relative-type-0":"esta quinta","relative-type-1":"próxima quinta","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} quinta","relativeTimePattern-count-other":"dentro de {0} quintas"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} quinta","relativeTimePattern-count-other":"há {0} quintas"}},"thu-narrow":{"relative-type--1":"qui. passada","relative-type-0":"esta qui.","relative-type-1":"próxima qui.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} qui.","relativeTimePattern-count-other":"dentro de {0} qui."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} qui.","relativeTimePattern-count-other":"há {0} qui."}},fri:{"relative-type--1":"sexta-feira passada","relative-type-0":"esta sexta-feira","relative-type-1":"próxima sexta-feira","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} sexta-feira","relativeTimePattern-count-other":"dentro de {0} sextas-feiras"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} sexta-feira","relativeTimePattern-count-other":"há {0} sextas-feiras"}},"fri-short":{"relative-type--1":"sexta passada","relative-type-0":"esta sexta","relative-type-1":"próxima sexta","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} sexta","relativeTimePattern-count-other":"dentro de {0} sextas"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} sexta","relativeTimePattern-count-other":"há {0} sextas"}},"fri-narrow":{"relative-type--1":"sex. passada","relative-type-0":"esta sex.","relative-type-1":"próxima sex.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} sex.","relativeTimePattern-count-other":"dentro de {0} sex."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} sex.","relativeTimePattern-count-other":"há {0} sex."}},sat:{"relative-type--1":"sábado passado","relative-type-0":"este sábado","relative-type-1":"próximo sábado","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} sábado","relativeTimePattern-count-other":"dentro de {0} sábados"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} sábado","relativeTimePattern-count-other":"há {0} sábados"}},"sat-short":{"relative-type--1":"sábado passado","relative-type-0":"este sábado","relative-type-1":"próximo sábado","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} sábado","relativeTimePattern-count-other":"dentro de {0} sábados"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} sábado","relativeTimePattern-count-other":"há {0} sábados"}},"sat-narrow":{"relative-type--1":"sáb. passado","relative-type-0":"este sáb.","relative-type-1":"próximo sáb.","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} sáb.","relativeTimePattern-count-other":"dentro de {0} sáb."},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} sáb.","relativeTimePattern-count-other":"há {0} sáb."}},"dayperiod-short":{displayName:"am/pm"},dayperiod:{displayName:"am/pm"},"dayperiod-narrow":{displayName:"am/pm"},hour:{displayName:"hora","relative-type-0":"esta hora","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} hora","relativeTimePattern-count-other":"dentro de {0} horas"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} hora","relativeTimePattern-count-other":"há {0} horas"}},"hour-short":{displayName:"h","relative-type-0":"esta hora","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} h","relativeTimePattern-count-other":"dentro de {0} h"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} h","relativeTimePattern-count-other":"há {0} h"}},"hour-narrow":{displayName:"h","relative-type-0":"esta hora","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} h","relativeTimePattern-count-other":"+{0} h"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} h","relativeTimePattern-count-other":"-{0} h"}},minute:{displayName:"minuto","relative-type-0":"este minuto","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} minuto","relativeTimePattern-count-other":"dentro de {0} minutos"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} minuto","relativeTimePattern-count-other":"há {0} minutos"}},"minute-short":{displayName:"min","relative-type-0":"este minuto","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} min","relativeTimePattern-count-other":"dentro de {0} min"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} min","relativeTimePattern-count-other":"há {0} min"}},"minute-narrow":{displayName:"min","relative-type-0":"este minuto","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} min","relativeTimePattern-count-other":"+{0} min"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} min","relativeTimePattern-count-other":"-{0} min"}},second:{displayName:"segundo","relative-type-0":"agora","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} segundo","relativeTimePattern-count-other":"dentro de {0} segundos"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} segundo","relativeTimePattern-count-other":"há {0} segundos"}},"second-short":{displayName:"s","relative-type-0":"agora","relativeTime-type-future":{"relativeTimePattern-count-one":"dentro de {0} s","relativeTimePattern-count-other":"dentro de {0} s"},"relativeTime-type-past":{"relativeTimePattern-count-one":"há {0} s","relativeTimePattern-count-other":"há {0} s"}},"second-narrow":{displayName:"s","relative-type-0":"agora","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} s","relativeTimePattern-count-other":"+{0} s"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} s","relativeTimePattern-count-other":"-{0} s"}},zone:{displayName:"fuso horário"},"zone-short":{displayName:"fuso horário"},"zone-narrow":{displayName:"fuso horário"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},"plurals-type-cardinal":{af:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ak:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},am:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},an:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ar:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ars:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},as:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},asa:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ast:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},az:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},be:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..4 and n % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 2.0, 3.0, 4.0, 22.0, 23.0, 24.0, 32.0, 33.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 10 = 0 or n % 10 = 5..9 or n % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":"   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …"},bem:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bez:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bho:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bm:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},br:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11,71,91 @integer 1, 21, 31, 41, 51, 61, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 81.0, 101.0, 1001.0, …","pluralRule-count-two":"n % 10 = 2 and n % 100 != 12,72,92 @integer 2, 22, 32, 42, 52, 62, 82, 102, 1002, … @decimal 2.0, 22.0, 32.0, 42.0, 52.0, 62.0, 82.0, 102.0, 1002.0, …","pluralRule-count-few":"n % 10 = 3..4,9 and n % 100 != 10..19,70..79,90..99 @integer 3, 4, 9, 23, 24, 29, 33, 34, 39, 43, 44, 49, 103, 1003, … @decimal 3.0, 4.0, 9.0, 23.0, 24.0, 29.0, 33.0, 34.0, 103.0, 1003.0, …","pluralRule-count-many":"n != 0 and n % 1000000 = 0 @integer 1000000, … @decimal 1000000.0, 1000000.00, 1000000.000, …","pluralRule-count-other":" @integer 0, 5~8, 10~20, 100, 1000, 10000, 100000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, …"},brx:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bs:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ca:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ce:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ceb:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},cgg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},chr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ckb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},cs:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},cy:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3 @integer 3 @decimal 3.0, 3.00, 3.000, 3.0000","pluralRule-count-many":"n = 6 @integer 6 @decimal 6.0, 6.00, 6.000, 6.0000","pluralRule-count-other":" @integer 4, 5, 7~20, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},da:{"pluralRule-count-one":"n = 1 or t != 0 and i = 0,1 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0~3.4, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},de:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dv:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dz:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ee:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},el:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},en:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},es:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},et:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fa:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ff:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fil:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},fo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fr:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fur:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fy:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ga:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3..6 @integer 3~6 @decimal 3.0, 4.0, 5.0, 6.0, 3.00, 4.00, 5.00, 6.00, 3.000, 4.000, 5.000, 6.000, 3.0000, 4.0000, 5.0000, 6.0000","pluralRule-count-many":"n = 7..10 @integer 7~10 @decimal 7.0, 8.0, 9.0, 10.0, 7.00, 8.00, 9.00, 10.00, 7.000, 8.000, 9.000, 10.000, 7.0000, 8.0000, 9.0000, 10.0000","pluralRule-count-other":" @integer 0, 11~25, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gd:{"pluralRule-count-one":"n = 1,11 @integer 1, 11 @decimal 1.0, 11.0, 1.00, 11.00, 1.000, 11.000, 1.0000","pluralRule-count-two":"n = 2,12 @integer 2, 12 @decimal 2.0, 12.0, 2.00, 12.00, 2.000, 12.000, 2.0000","pluralRule-count-few":"n = 3..10,13..19 @integer 3~10, 13~19 @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 3.00","pluralRule-count-other":" @integer 0, 20~34, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gsw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},guw:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gv:{"pluralRule-count-one":"v = 0 and i % 10 = 1 @integer 1, 11, 21, 31, 41, 51, 61, 71, 101, 1001, …","pluralRule-count-two":"v = 0 and i % 10 = 2 @integer 2, 12, 22, 32, 42, 52, 62, 72, 102, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 0,20,40,60,80 @integer 0, 20, 40, 60, 80, 100, 120, 140, 1000, 10000, 100000, 1000000, …","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 3~10, 13~19, 23, 103, 1003, …"},ha:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},haw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},he:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hy:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ia:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},id:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ig:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ii:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},in:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},io:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},is:{"pluralRule-count-one":"t = 0 and i % 10 = 1 and i % 100 != 11 or t != 0 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1~1.6, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},it:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ja:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jbo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ji:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jmc:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jv:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jw:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ka:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kab:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kaj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kcg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kde:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kea:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kkj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kl:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},km:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ko:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ks:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksh:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ku:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kw:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n % 100 = 2,22,42,62,82 or n % 1000 = 0 and n % 100000 = 1000..20000,40000,60000,80000 or n != 0 and n % 1000000 = 100000 @integer 2, 22, 42, 62, 82, 102, 122, 142, 1000, 10000, 100000, … @decimal 2.0, 22.0, 42.0, 62.0, 82.0, 102.0, 122.0, 142.0, 1000.0, 10000.0, 100000.0, …","pluralRule-count-few":"n % 100 = 3,23,43,63,83 @integer 3, 23, 43, 63, 83, 103, 123, 143, 1003, … @decimal 3.0, 23.0, 43.0, 63.0, 83.0, 103.0, 123.0, 143.0, 1003.0, …","pluralRule-count-many":"n != 1 and n % 100 = 1,21,41,61,81 @integer 21, 41, 61, 81, 101, 121, 141, 161, 1001, … @decimal 21.0, 41.0, 61.0, 81.0, 101.0, 121.0, 141.0, 161.0, 1001.0, …","pluralRule-count-other":" @integer 4~19, 100, 1004, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.1, 1000000.0, …"},ky:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lag:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"i = 0,1 and n != 0 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lkt:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ln:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lt:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11..19 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..9 and n % 100 != 11..19 @integer 2~9, 22~29, 102, 1002, … @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 22.0, 102.0, 1002.0, …","pluralRule-count-many":"f != 0   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lv:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},mas:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mg:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.2~1.0, 1.2~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ml:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mo:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},mr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ms:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mt:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-few":"n = 0 or n % 100 = 2..10 @integer 0, 2~10, 102~107, 1002, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 100 = 11..19 @integer 11~19, 111~117, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},my:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nah:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},naq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ne:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nnh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},no:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nqo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nso:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ny:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nyn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},om:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},or:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},os:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},osa:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pap:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i != 1 and i % 10 = 0..1 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 12..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},prg:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},ps:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pt:{"pluralRule-count-one":"i = 0..1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},"pt-PT":{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rm:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ro:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},rof:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},root:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ru:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rwk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sah:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},saq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sc:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},scn:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sdh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},se:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},seh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ses:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sg:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sh:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},shi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-few":"n = 2..10 @integer 2~10 @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 8.00","pluralRule-count-other":" @integer 11~26, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~1.9, 2.1~2.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},si:{"pluralRule-count-one":"n = 0,1 or i = 0 and f = 1 @integer 0, 1 @decimal 0.0, 0.1, 1.0, 0.00, 0.01, 1.00, 0.000, 0.001, 1.000, 0.0000, 0.0001, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.2~0.9, 1.1~1.8, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sk:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sl:{"pluralRule-count-one":"v = 0 and i % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, …","pluralRule-count-two":"v = 0 and i % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or v != 0 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sma:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smi:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sms:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},so:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ss:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ssy:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},st:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},su:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sv:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},syr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ta:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},te:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},teo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},th:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ti:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tig:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tl:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},tn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},to:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ts:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tzm:{"pluralRule-count-one":"n = 0..1 or n = 11..99 @integer 0, 1, 11~24 @decimal 0.0, 1.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0","pluralRule-count-other":" @integer 2~10, 100~106, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ug:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ur:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uz:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ve:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vi:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vun:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wae:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xog:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yue:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zh:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"}}}},{main:{"pt-PT":{identity:{version:{_cldrVersion:"36"},language:"pt",territory:"PT"},units:{long:{per:{compoundUnitPattern:"{0} por {1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"força G","unitPattern-count-one":"{0} força G","unitPattern-count-other":"{0} força G"},"acceleration-meter-per-second-squared":{displayName:"metros por segundo quadrado","unitPattern-count-one":"{0} metro por segundo quadrado","unitPattern-count-other":"{0} metros por segundo quadrado"},"angle-revolution":{displayName:"revolução","unitPattern-count-one":"{0} revolução","unitPattern-count-other":"{0} revoluções"},"angle-radian":{displayName:"radianos","unitPattern-count-one":"{0} radiano","unitPattern-count-other":"{0} radianos"},"angle-degree":{displayName:"graus","unitPattern-count-one":"{0} grau","unitPattern-count-other":"{0} graus"},"angle-arc-minute":{displayName:"minutos de arco","unitPattern-count-one":"{0} minuto de arco","unitPattern-count-other":"{0} minutos de arco"},"angle-arc-second":{displayName:"segundos de arco","unitPattern-count-one":"{0} segundo de arco","unitPattern-count-other":"{0} segundos de arco"},"area-square-kilometer":{displayName:"quilómetros quadrados","unitPattern-count-one":"{0} quilómetro quadrado","unitPattern-count-other":"{0} quilómetros quadrados",perUnitPattern:"{0} por quilómetro quadrado"},"area-hectare":{displayName:"hectares","unitPattern-count-one":"{0} hectare","unitPattern-count-other":"{0} hectares"},"area-square-meter":{displayName:"metros quadrados","unitPattern-count-one":"{0} metro quadrado","unitPattern-count-other":"{0} metros quadrados",perUnitPattern:"{0} por metro quadrado"},"area-square-centimeter":{displayName:"centímetros quadrados","unitPattern-count-one":"{0} centímetro quadrado","unitPattern-count-other":"{0} centímetros quadrados",perUnitPattern:"{0} por centímetro quadrado"},"area-square-mile":{displayName:"milhas quadradas","unitPattern-count-one":"{0} milha quadrada","unitPattern-count-other":"{0} milhas quadradas",perUnitPattern:"{0} por milha quadrada"},"area-acre":{displayName:"acres","unitPattern-count-one":"{0} acre","unitPattern-count-other":"{0} acres"},"area-square-yard":{displayName:"jardas quadradas","unitPattern-count-one":"{0} jarda quadrada","unitPattern-count-other":"{0} jardas quadradas"},"area-square-foot":{displayName:"pés quadrados","unitPattern-count-one":"{0} pé quadrado","unitPattern-count-other":"{0} pés quadrados"},"area-square-inch":{displayName:"polegadas quadradas","unitPattern-count-one":"{0} polegada quadrada","unitPattern-count-other":"{0} polegadas quadradas",perUnitPattern:"{0} por polegada quadrada"},"area-dunam":{displayName:"dunans","unitPattern-count-one":"{0} dunam","unitPattern-count-other":"{0} dunans"},"concentr-karat":{displayName:"quilates","unitPattern-count-one":"{0} quilate","unitPattern-count-other":"{0} quilates"},"concentr-milligram-per-deciliter":{displayName:"miligramas por decilitro","unitPattern-count-one":"{0} miligrama por decilitro","unitPattern-count-other":"{0} miligramas por decilitro"},"concentr-millimole-per-liter":{displayName:"milimoles por litro","unitPattern-count-one":"{0} milimole por litro","unitPattern-count-other":"{0} milimoles por litro"},"concentr-part-per-million":{displayName:"partes por milhão","unitPattern-count-one":"{0} parte por milhão","unitPattern-count-other":"{0} partes por milhão"},"concentr-percent":{displayName:"por cento","unitPattern-count-one":"{0} por cento","unitPattern-count-other":"{0} por cento"},"concentr-permille":{displayName:"por mil","unitPattern-count-one":"{0} por mil","unitPattern-count-other":"{0} por mil"},"concentr-permyriad":{displayName:"ponto base","unitPattern-count-one":"{0} ponto base","unitPattern-count-other":"{0} pontos base"},"concentr-mole":{displayName:"mols","unitPattern-count-one":"{0} mol","unitPattern-count-other":"{0} mols"},"consumption-liter-per-kilometer":{displayName:"litros por quilómetro","unitPattern-count-one":"{0} litro por quilómetro","unitPattern-count-other":"{0} litros por quilómetro"},"consumption-liter-per-100kilometers":{displayName:"litros por 100 quilómetros","unitPattern-count-one":"{0} litro por 100 quilómetros","unitPattern-count-other":"{0} litros por 100 quilómetros"},"consumption-mile-per-gallon":{displayName:"milhas por galão","unitPattern-count-one":"{0} milha por galão","unitPattern-count-other":"{0} milhas por galão"},"consumption-mile-per-gallon-imperial":{displayName:"milhas por galão imperial","unitPattern-count-one":"{0} milha por galão imperial","unitPattern-count-other":"{0} milhas por galão imperial"},"digital-petabyte":{displayName:"petabytes","unitPattern-count-one":"{0} petabyte","unitPattern-count-other":"{0} petabytes"},"digital-terabyte":{displayName:"terabytes","unitPattern-count-one":"{0} terabyte","unitPattern-count-other":"{0} terabytes"},"digital-terabit":{displayName:"terabits","unitPattern-count-one":"{0} terabit","unitPattern-count-other":"{0} terabits"},"digital-gigabyte":{displayName:"gigabytes","unitPattern-count-one":"{0} gigabyte","unitPattern-count-other":"{0} gigabytes"},"digital-gigabit":{displayName:"gigabits","unitPattern-count-one":"{0} gigabit","unitPattern-count-other":"{0} gigabits"},"digital-megabyte":{displayName:"megabytes","unitPattern-count-one":"{0} megabyte","unitPattern-count-other":"{0} megabytes"},"digital-megabit":{displayName:"megabits","unitPattern-count-one":"{0} megabit","unitPattern-count-other":"{0} megabits"},"digital-kilobyte":{displayName:"kilobytes","unitPattern-count-one":"{0} kilobyte","unitPattern-count-other":"{0} kilobytes"},"digital-kilobit":{displayName:"kilobits","unitPattern-count-one":"{0} kilobit","unitPattern-count-other":"{0} kilobits"},"digital-byte":{displayName:"bytes","unitPattern-count-one":"{0} byte","unitPattern-count-other":"{0} bytes"},"digital-bit":{displayName:"bits","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bits"},"duration-century":{displayName:"séculos","unitPattern-count-one":"{0} século","unitPattern-count-other":"{0} séculos"},"duration-decade":{displayName:"décadas","unitPattern-count-one":"{0} década","unitPattern-count-other":"{0} décadas"},"duration-year":{displayName:"anos","unitPattern-count-one":"{0} ano","unitPattern-count-other":"{0} anos",perUnitPattern:"{0} por ano"},"duration-month":{displayName:"meses","unitPattern-count-one":"{0} mês","unitPattern-count-other":"{0} meses",perUnitPattern:"{0} por mês"},"duration-week":{displayName:"semanas","unitPattern-count-one":"{0} semana","unitPattern-count-other":"{0} semanas",perUnitPattern:"{0} por semana"},"duration-day":{displayName:"dias","unitPattern-count-one":"{0} dia","unitPattern-count-other":"{0} dias",perUnitPattern:"{0} por dia"},"duration-hour":{displayName:"horas","unitPattern-count-one":"{0} hora","unitPattern-count-other":"{0} horas",perUnitPattern:"{0}/h"},"duration-minute":{displayName:"minutos","unitPattern-count-one":"{0} minuto","unitPattern-count-other":"{0} minutos",perUnitPattern:"{0} por minuto"},"duration-second":{displayName:"segundos","unitPattern-count-one":"{0} segundo","unitPattern-count-other":"{0} segundos",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"milissegundos","unitPattern-count-one":"{0} milissegundo","unitPattern-count-other":"{0} milissegundos"},"duration-microsecond":{displayName:"microssegundos","unitPattern-count-one":"{0} microssegundo","unitPattern-count-other":"{0} microssegundos"},"duration-nanosecond":{displayName:"nanossegundos","unitPattern-count-one":"{0} nanossegundo","unitPattern-count-other":"{0} nanossegundos"},"electric-ampere":{displayName:"amperes","unitPattern-count-one":"{0} ampere","unitPattern-count-other":"{0} amperes"},"electric-milliampere":{displayName:"miliamperes","unitPattern-count-one":"{0} miliampere","unitPattern-count-other":"{0} miliamperes"},"electric-ohm":{displayName:"ohms","unitPattern-count-one":"{0} ohm","unitPattern-count-other":"{0} ohms"},"electric-volt":{displayName:"volts","unitPattern-count-one":"{0} volt","unitPattern-count-other":"{0} volts"},"energy-kilocalorie":{displayName:"quilocalorias","unitPattern-count-one":"{0} quilocaloria","unitPattern-count-other":"{0} quilocalorias"},"energy-calorie":{displayName:"calorias","unitPattern-count-one":"{0} caloria","unitPattern-count-other":"{0} calorias"},"energy-foodcalorie":{displayName:"quilocalorias","unitPattern-count-one":"{0} quilocaloria","unitPattern-count-other":"{0} quilocalorias"},"energy-kilojoule":{displayName:"quilojoules","unitPattern-count-one":"{0} quilojoule","unitPattern-count-other":"{0} quilojoules"},"energy-joule":{displayName:"joules","unitPattern-count-one":"{0} joule","unitPattern-count-other":"{0} joules"},"energy-kilowatt-hour":{displayName:"quilowatts-hora","unitPattern-count-one":"{0} quilowatt-hora","unitPattern-count-other":"{0} quilowatts-hora"},"energy-electronvolt":{displayName:"eletrões-volts","unitPattern-count-one":"{0} eletrão-volt","unitPattern-count-other":"{0} eletrões-volts"},"energy-british-thermal-unit":{displayName:"unidades térmicas britânicas","unitPattern-count-one":"{0} unidade térmica britânica","unitPattern-count-other":"{0} unidades térmicas britânicas"},"energy-therm-us":{displayName:"unidades térmicas norte-americanas","unitPattern-count-one":"{0} unidade térmica norte-americana","unitPattern-count-other":"{0} unidades térmicas norte-americanas"},"force-pound-force":{displayName:"libras de força","unitPattern-count-one":"{0} libra de força","unitPattern-count-other":"{0} libras de força"},"force-newton":{displayName:"newtons","unitPattern-count-one":"{0} newton","unitPattern-count-other":"{0} newtons"},"frequency-gigahertz":{displayName:"gigahertz","unitPattern-count-one":"{0} gigahertz","unitPattern-count-other":"{0} gigahertz"},"frequency-megahertz":{displayName:"megahertz","unitPattern-count-one":"{0} megahertz","unitPattern-count-other":"{0} megahertz"},"frequency-kilohertz":{displayName:"kilohertz","unitPattern-count-one":"{0} kilohertz","unitPattern-count-other":"{0} kilohertz"},"frequency-hertz":{displayName:"hertz","unitPattern-count-one":"{0} hertz","unitPattern-count-other":"{0} hertz"},"graphics-em":{displayName:"em tipográfico","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"píxel","unitPattern-count-one":"{0} píxel","unitPattern-count-other":"{0} píxeis"},"graphics-megapixel":{displayName:"megapíxeis","unitPattern-count-one":"{0} megapíxel","unitPattern-count-other":"{0} megapíxeis"},"graphics-pixel-per-centimeter":{displayName:"píxeis por centímetro","unitPattern-count-one":"{0} píxel por centímetro","unitPattern-count-other":"{0} píxeis por centímetro"},"graphics-pixel-per-inch":{displayName:"píxeis por polegada","unitPattern-count-one":"{0} píxel por polegada","unitPattern-count-other":"{0} píxeis por polegada"},"graphics-dot-per-centimeter":{displayName:"pontos por centímetro","unitPattern-count-one":"{0} ponto por centímetro","unitPattern-count-other":"{0} pontos por centímetro"},"graphics-dot-per-inch":{displayName:"pontos por polegada","unitPattern-count-one":"{0} ponto por polegada","unitPattern-count-other":"{0} pontos por polegada"},"length-kilometer":{displayName:"quilómetros","unitPattern-count-one":"{0} quilómetro","unitPattern-count-other":"{0} quilómetros",perUnitPattern:"{0} por quilómetro"},"length-meter":{displayName:"metros","unitPattern-count-one":"{0} metro","unitPattern-count-other":"{0} metros",perUnitPattern:"{0} por metro"},"length-decimeter":{displayName:"decímetros","unitPattern-count-one":"{0} decímetro","unitPattern-count-other":"{0} decímetros"},"length-centimeter":{displayName:"centímetros","unitPattern-count-one":"{0} centímetro","unitPattern-count-other":"{0} centímetros",perUnitPattern:"{0} por centímetro"},"length-millimeter":{displayName:"milímetros","unitPattern-count-one":"{0} milímetro","unitPattern-count-other":"{0} milímetros"},"length-micrometer":{displayName:"micrómetros","unitPattern-count-one":"{0} micrómetro","unitPattern-count-other":"{0} micrómetros"},"length-nanometer":{displayName:"nanómetros","unitPattern-count-one":"{0} nanómetro","unitPattern-count-other":"{0} nanómetros"},"length-picometer":{displayName:"picómetros","unitPattern-count-one":"{0} picómetro","unitPattern-count-other":"{0} picómetros"},"length-mile":{displayName:"milhas","unitPattern-count-one":"{0} milha","unitPattern-count-other":"{0} milhas"},"length-yard":{displayName:"jardas","unitPattern-count-one":"{0} jarda","unitPattern-count-other":"{0} jardas"},"length-foot":{displayName:"pés","unitPattern-count-one":"{0} pé","unitPattern-count-other":"{0} pés",perUnitPattern:"{0} por pé"},"length-inch":{displayName:"polegadas","unitPattern-count-one":"{0} polegada","unitPattern-count-other":"{0} polegadas",perUnitPattern:"{0} por polegada"},"length-parsec":{displayName:"parsecs","unitPattern-count-one":"{0} parsec","unitPattern-count-other":"{0} parsecs"},"length-light-year":{displayName:"anos-luz","unitPattern-count-one":"{0} ano-luz","unitPattern-count-other":"{0} anos-luz"},"length-astronomical-unit":{displayName:"unidades astronómicas","unitPattern-count-one":"{0} unidade astronómica","unitPattern-count-other":"{0} unidades astronómicas"},"length-furlong":{displayName:"furlongs","unitPattern-count-one":"{0} furlong","unitPattern-count-other":"{0} furlongs"},"length-fathom":{displayName:"braças","unitPattern-count-one":"{0} braça","unitPattern-count-other":"{0} braças"},"length-nautical-mile":{displayName:"milhas náuticas","unitPattern-count-one":"{0} milha náutica","unitPattern-count-other":"{0} milhas náuticas"},"length-mile-scandinavian":{displayName:"milha escandinava","unitPattern-count-one":"{0} milha escandinava","unitPattern-count-other":"{0} milhas escandinavas"},"length-point":{displayName:"pontos","unitPattern-count-one":"{0} ponto","unitPattern-count-other":"{0} pontos"},"length-solar-radius":{displayName:"raios solares","unitPattern-count-one":"{0} raio solar","unitPattern-count-other":"{0} raios solares"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lux","unitPattern-count-other":"{0} lux"},"light-solar-luminosity":{displayName:"luminosidades solares","unitPattern-count-one":"{0} luminosidade solar","unitPattern-count-other":"{0} luminosidades solares"},"mass-metric-ton":{displayName:"toneladas métricas","unitPattern-count-one":"{0} tonelada métrica","unitPattern-count-other":"{0} toneladas métricas"},"mass-kilogram":{displayName:"quilogramas","unitPattern-count-one":"{0} quilograma","unitPattern-count-other":"{0} quilogramas",perUnitPattern:"{0} por quilograma"},"mass-gram":{displayName:"gramas","unitPattern-count-one":"{0} grama","unitPattern-count-other":"{0} gramas",perUnitPattern:"{0} por grama"},"mass-milligram":{displayName:"miligramas","unitPattern-count-one":"{0} miligrama","unitPattern-count-other":"{0} miligramas"},"mass-microgram":{displayName:"microgramas","unitPattern-count-one":"{0} micrograma","unitPattern-count-other":"{0} microgramas"},"mass-ton":{displayName:"toneladas","unitPattern-count-one":"{0} tonelada","unitPattern-count-other":"{0} toneladas"},"mass-stone":{displayName:"stones","unitPattern-count-one":"{0} stone","unitPattern-count-other":"{0} stones"},"mass-pound":{displayName:"libras","unitPattern-count-one":"{0} libra","unitPattern-count-other":"{0} libras",perUnitPattern:"{0} por libra"},"mass-ounce":{displayName:"onças","unitPattern-count-one":"{0} onça","unitPattern-count-other":"{0} onças",perUnitPattern:"{0} por onça"},"mass-ounce-troy":{displayName:"onças troy","unitPattern-count-one":"{0} onça troy","unitPattern-count-other":"{0} onças troy"},"mass-carat":{displayName:"quilates","unitPattern-count-one":"{0} quilate","unitPattern-count-other":"{0} quilates"},"mass-dalton":{displayName:"daltons","unitPattern-count-one":"{0} dalton","unitPattern-count-other":"{0} daltons"},"mass-earth-mass":{displayName:"massa terrestre","unitPattern-count-one":"{0} massa da Terra","unitPattern-count-other":"{0} massas da Terra"},"mass-solar-mass":{displayName:"massas solares","unitPattern-count-one":"{0} massa solar","unitPattern-count-other":"{0} massas solares"},"power-gigawatt":{displayName:"gigawatts","unitPattern-count-one":"{0} gigawatt","unitPattern-count-other":"{0} gigawatts"},"power-megawatt":{displayName:"megawatts","unitPattern-count-one":"{0} megawatt","unitPattern-count-other":"{0} megawatts"},"power-kilowatt":{displayName:"quilowatts","unitPattern-count-one":"{0} quilowatt","unitPattern-count-other":"{0} quilowatts"},"power-watt":{displayName:"watts","unitPattern-count-one":"{0} watt","unitPattern-count-other":"{0} watts"},"power-milliwatt":{displayName:"miliwatts","unitPattern-count-one":"{0} miliwatt","unitPattern-count-other":"{0} miliwatts"},"power-horsepower":{displayName:"cavalos-vapor","unitPattern-count-one":"{0} cavalo-vapor","unitPattern-count-other":"{0} cavalos-vapor"},"pressure-millimeter-of-mercury":{displayName:"milímetros de mercúrio","unitPattern-count-one":"{0} milímetro de mercúrio","unitPattern-count-other":"{0} milímetros de mercúrio"},"pressure-pound-per-square-inch":{displayName:"psi","unitPattern-count-one":"{0} psi","unitPattern-count-other":"{0} psi"},"pressure-inch-hg":{displayName:"polegadas de mercúrio","unitPattern-count-one":"{0} polegada de mercúrio","unitPattern-count-other":"{0} polegadas de mercúrio"},"pressure-bar":{displayName:"bars","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"milibares","unitPattern-count-one":"{0} milibar","unitPattern-count-other":"{0} milibares"},"pressure-atmosphere":{displayName:"atmosferas","unitPattern-count-one":"{0} atmosfera","unitPattern-count-other":"{0} atmosferas"},"pressure-pascal":{displayName:"pascais","unitPattern-count-one":"{0} pascal","unitPattern-count-other":"{0} pascais"},"pressure-hectopascal":{displayName:"hectopascais","unitPattern-count-one":"{0} hectopascal","unitPattern-count-other":"{0} hectopascais"},"pressure-kilopascal":{displayName:"quilopascais","unitPattern-count-one":"{0} quilopascal","unitPattern-count-other":"{0} quilopascais"},"pressure-megapascal":{displayName:"megapascais","unitPattern-count-one":"{0} megapascal","unitPattern-count-other":"{0} megapascais"},"speed-kilometer-per-hour":{displayName:"quilómetros por hora","unitPattern-count-one":"{0} quilómetro por hora","unitPattern-count-other":"{0} quilómetros por hora"},"speed-meter-per-second":{displayName:"metros por segundo","unitPattern-count-one":"{0} metro por segundo","unitPattern-count-other":"{0} metros por segundo"},"speed-mile-per-hour":{displayName:"milhas por hora","unitPattern-count-one":"{0} milha por hora","unitPattern-count-other":"{0} milhas por hora"},"speed-knot":{displayName:"nó","unitPattern-count-one":"{0} nó","unitPattern-count-other":"{0} nós"},"temperature-generic":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"graus Celsius","unitPattern-count-one":"{0} grau Celsius","unitPattern-count-other":"{0} graus Celsius"},"temperature-fahrenheit":{displayName:"graus Fahrenheit","unitPattern-count-one":"{0} grau Fahrenheit","unitPattern-count-other":"{0} graus Fahrenheit"},"temperature-kelvin":{displayName:"kelvins","unitPattern-count-one":"{0} kelvin","unitPattern-count-other":"{0} kelvins"},"torque-pound-foot":{displayName:"pés-libra","unitPattern-count-one":"{0} pé-libra","unitPattern-count-other":"{0} pés-libra"},"torque-newton-meter":{displayName:"newton-metros","unitPattern-count-one":"{0} newton-metro","unitPattern-count-other":"{0} newton-metros"},"volume-cubic-kilometer":{displayName:"quilómetros cúbicos","unitPattern-count-one":"{0} quilómetro cúbico","unitPattern-count-other":"{0} quilómetros cúbicos"},"volume-cubic-meter":{displayName:"metros cúbicos","unitPattern-count-one":"{0} metro cúbico","unitPattern-count-other":"{0} metros cúbicos",perUnitPattern:"{0} por metro cúbico"},"volume-cubic-centimeter":{displayName:"centímetros cúbicos","unitPattern-count-one":"{0} centímetro cúbico","unitPattern-count-other":"{0} centímetros cúbicos",perUnitPattern:"{0} por centímetro cúbico"},"volume-cubic-mile":{displayName:"milhas cúbicas","unitPattern-count-one":"{0} milha cúbica","unitPattern-count-other":"{0} milhas cúbicas"},"volume-cubic-yard":{displayName:"jardas cúbicas","unitPattern-count-one":"{0} jarda cúbica","unitPattern-count-other":"{0} jardas cúbicas"},"volume-cubic-foot":{displayName:"pés cúbicos","unitPattern-count-one":"{0} pé cúbico","unitPattern-count-other":"{0} pés cúbicos"},"volume-cubic-inch":{displayName:"polegadas cúbicas","unitPattern-count-one":"{0} polegada cúbica","unitPattern-count-other":"{0} polegadas cúbicas"},"volume-megaliter":{displayName:"megalitros","unitPattern-count-one":"{0} megalitro","unitPattern-count-other":"{0} megalitros"},"volume-hectoliter":{displayName:"hectolitros","unitPattern-count-one":"{0} hectolitro","unitPattern-count-other":"{0} hectolitros"},"volume-liter":{displayName:"litros","unitPattern-count-one":"{0} litro","unitPattern-count-other":"{0} litros",perUnitPattern:"{0} por litro"},"volume-deciliter":{displayName:"decilitros","unitPattern-count-one":"{0} decilitro","unitPattern-count-other":"{0} decilitros"},"volume-centiliter":{displayName:"centilitros","unitPattern-count-one":"{0} centilitro","unitPattern-count-other":"{0} centilitros"},"volume-milliliter":{displayName:"mililitros","unitPattern-count-one":"{0} mililitro","unitPattern-count-other":"{0} mililitros"},"volume-pint-metric":{displayName:"pints métricos","unitPattern-count-one":"{0} pint métrico","unitPattern-count-other":"{0} pints métricos"},"volume-cup-metric":{displayName:"chávenas métricas","unitPattern-count-one":"{0} chávena métrica","unitPattern-count-other":"{0} chávenas métricas"},"volume-acre-foot":{displayName:"acre-pés","unitPattern-count-one":"{0} acre-pé","unitPattern-count-other":"{0} acre-pés"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"galões","unitPattern-count-one":"{0} galão","unitPattern-count-other":"{0} galões",perUnitPattern:"{0} por galão"},"volume-gallon-imperial":{displayName:"galões imperiais","unitPattern-count-one":"{0} galão imperial","unitPattern-count-other":"{0} galões imperiais",perUnitPattern:"{0} por galão imperial"},"volume-quart":{displayName:"quartos","unitPattern-count-one":"{0} quarto","unitPattern-count-other":"{0} quartos"},"volume-pint":{displayName:"pints","unitPattern-count-one":"{0} pint","unitPattern-count-other":"{0} pints"},"volume-cup":{displayName:"chávenas","unitPattern-count-one":"{0} chávena","unitPattern-count-other":"{0} chávenas"},"volume-fluid-ounce":{displayName:"onças fluidas","unitPattern-count-one":"{0} onça fluida","unitPattern-count-other":"{0} onças fluidas"},"volume-fluid-ounce-imperial":{displayName:"onças fluidas imperiais","unitPattern-count-one":"{0} onça fluida imperial","unitPattern-count-other":"{0} onças fluidas imperiais"},"volume-tablespoon":{displayName:"colheres de sopa","unitPattern-count-one":"{0} colher de sopa","unitPattern-count-other":"{0} colheres de sopa"},"volume-teaspoon":{displayName:"colheres de chá","unitPattern-count-one":"{0} colher de chá","unitPattern-count-other":"{0} colheres de chá"},"volume-barrel":{displayName:"barris","unitPattern-count-one":"{0} barril","unitPattern-count-other":"{0} barris"},coordinateUnit:{displayName:"direção cardeal",east:"{0} este",north:"{0} norte",south:"{0} sul",west:"{0} Oeste"}},short:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"força G","unitPattern-count-one":"{0} G","unitPattern-count-other":"{0} G"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-one":"{0} m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"rev","unitPattern-count-other":"{0} rev"},"angle-radian":{displayName:"radianos","unitPattern-count-one":"{0} rad","unitPattern-count-other":"{0} rad"},"angle-degree":{displayName:"graus","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"minutos de arco","unitPattern-count-one":"{0} arcmin","unitPattern-count-other":"{0} arcmins"},"angle-arc-second":{displayName:"segundos de arco","unitPattern-count-one":"{0} arcseg","unitPattern-count-other":"{0} arcsegs"},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0} km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"hectares","unitPattern-count-one":"{0} ha","unitPattern-count-other":"{0} ha"},"area-square-meter":{displayName:"m²","unitPattern-count-one":"{0} m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0} cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"mi²","unitPattern-count-one":"{0} mi²","unitPattern-count-other":"{0} mi²",perUnitPattern:"{0}/mi²"},"area-acre":{displayName:"acres","unitPattern-count-one":"{0} acre","unitPattern-count-other":"{0} acres"},"area-square-yard":{displayName:"yd²","unitPattern-count-one":"{0} yd²","unitPattern-count-other":"{0} yd²"},"area-square-foot":{displayName:"pés quadrados","unitPattern-count-one":"{0} ft²","unitPattern-count-other":"{0} ft²"},"area-square-inch":{displayName:"in²","unitPattern-count-one":"{0} in²","unitPattern-count-other":"{0} in²",perUnitPattern:"{0}/in²"},"area-dunam":{displayName:"dunans","unitPattern-count-one":"{0} dunam","unitPattern-count-other":"{0} dunans"},"concentr-karat":{displayName:"quilates","unitPattern-count-one":"{0} kt","unitPattern-count-other":"{0} kt"},"concentr-milligram-per-deciliter":{displayName:"mg/dl","unitPattern-count-one":"{0} mg/dl","unitPattern-count-other":"{0} mg/dl"},"concentr-millimole-per-liter":{displayName:"milimole/litro","unitPattern-count-one":"{0} mmol/l","unitPattern-count-other":"{0} mmol/l"},"concentr-part-per-million":{displayName:"partes/milhão","unitPattern-count-other":"{0} ppm"},"concentr-percent":{displayName:"por cento","unitPattern-count-one":"{0}%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"por mil","unitPattern-count-one":"{0}‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"ponto base","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"litros/km","unitPattern-count-one":"{0} l/km","unitPattern-count-other":"{0} l/km"},"consumption-liter-per-100kilometers":{displayName:"l/100km","unitPattern-count-one":"{0} l/100km","unitPattern-count-other":"{0} l/100km"},"consumption-mile-per-gallon":{displayName:"milhas/galão","unitPattern-count-one":"{0} mpg","unitPattern-count-other":"{0} mpg"},"consumption-mile-per-gallon-imperial":{displayName:"milhas/gal imp.","unitPattern-count-one":"{0} mpg imp.","unitPattern-count-other":"{0} mpg imp."},"digital-petabyte":{displayName:"PByte","unitPattern-count-one":"{0} PB","unitPattern-count-other":"{0} PB"},"digital-terabyte":{displayName:"TByte","unitPattern-count-one":"{0} TB","unitPattern-count-other":"{0} TB"},"digital-terabit":{displayName:"Tbit","unitPattern-count-one":"{0} Tb","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"GByte","unitPattern-count-one":"{0} GB","unitPattern-count-other":"{0} GB"},"digital-gigabit":{displayName:"Gbit","unitPattern-count-one":"{0} Gb","unitPattern-count-other":"{0} Gb"},"digital-megabyte":{displayName:"MByte","unitPattern-count-one":"{0} MB","unitPattern-count-other":"{0} MB"},"digital-megabit":{displayName:"Mbit","unitPattern-count-one":"{0} Mb","unitPattern-count-other":"{0} Mb"},"digital-kilobyte":{displayName:"kByte","unitPattern-count-one":"{0} kB","unitPattern-count-other":"{0} kB"},"digital-kilobit":{displayName:"kbit","unitPattern-count-one":"{0} kb","unitPattern-count-other":"{0} kb"},"digital-byte":{displayName:"byte","unitPattern-count-one":"{0} byte","unitPattern-count-other":"{0} byte"},"digital-bit":{displayName:"bit","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bit"},"duration-century":{displayName:"séc.","unitPattern-count-one":"{0} séc.","unitPattern-count-other":"{0} sécs."},"duration-decade":{displayName:"déc.","unitPattern-count-one":"{0} déc.","unitPattern-count-other":"{0} déc."},"duration-year":{displayName:"anos","unitPattern-count-one":"{0} ano","unitPattern-count-other":"{0} anos",perUnitPattern:"{0}/ano"},"duration-month":{displayName:"meses","unitPattern-count-one":"{0} mês","unitPattern-count-other":"{0} meses",perUnitPattern:"{0}/mês"},"duration-week":{displayName:"semanas","unitPattern-count-one":"{0} sem.","unitPattern-count-other":"{0} sem.",perUnitPattern:"{0}/sem."},"duration-day":{displayName:"dias","unitPattern-count-one":"{0} dia","unitPattern-count-other":"{0} dias",perUnitPattern:"{0}/dia"},"duration-hour":{displayName:"horas","unitPattern-count-one":"{0} h","unitPattern-count-other":"{0} h",perUnitPattern:"{0}/h"},"duration-minute":{displayName:"minutos","unitPattern-count-one":"{0} min","unitPattern-count-other":"{0} min",perUnitPattern:"{0}/min"},"duration-second":{displayName:"s","unitPattern-count-one":"{0} s","unitPattern-count-other":"{0} s",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"milissegundos","unitPattern-count-one":"{0} ms","unitPattern-count-other":"{0} ms"},"duration-microsecond":{displayName:"μs","unitPattern-count-one":"{0} μs","unitPattern-count-other":"{0} μs"},"duration-nanosecond":{displayName:"ns","unitPattern-count-one":"{0} ns","unitPattern-count-other":"{0} ns"},"electric-ampere":{displayName:"amps","unitPattern-count-one":"{0} A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"miliamps","unitPattern-count-one":"{0} mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"ohms","unitPattern-count-one":"{0} Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"volts","unitPattern-count-one":"{0} V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-calorie":{displayName:"cal","unitPattern-count-one":"{0} cal","unitPattern-count-other":"{0} cal"},"energy-foodcalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-kilojoule":{displayName:"quilojoule","unitPattern-count-one":"{0} kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"joules","unitPattern-count-one":"{0} J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kW-hora","unitPattern-count-one":"{0} kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"eletrão-volt","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0} BTU","unitPattern-count-other":"{0} BTU"},"energy-therm-us":{displayName:"thm EUA","unitPattern-count-one":"{0} thm EUA","unitPattern-count-other":"{0} thm EUA"},"force-pound-force":{displayName:"libra-força","unitPattern-count-other":"{0} lbf"},"force-newton":{displayName:"newton","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0} GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0} MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0} kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0} Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"píxeis","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"megapíxeis","unitPattern-count-other":"{0} MP"},"graphics-pixel-per-centimeter":{displayName:"ppcm","unitPattern-count-other":"{0} ppcm"},"graphics-pixel-per-inch":{displayName:"ppi","unitPattern-count-other":"{0} ppi"},"graphics-dot-per-centimeter":{displayName:"dpcm","unitPattern-count-other":"{0} dpcm"},"graphics-dot-per-inch":{displayName:"ppp","unitPattern-count-one":"{0} ppp","unitPattern-count-other":"{0} ppp"},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0} km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"metros","unitPattern-count-one":"{0} m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0} dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0} cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0} mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µm","unitPattern-count-one":"{0} µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0} nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0} pm","unitPattern-count-other":"{0} pm"},"length-mile":{displayName:"milhas","unitPattern-count-one":"{0} milha","unitPattern-count-other":"{0} milhas"},"length-yard":{displayName:"jardas","unitPattern-count-one":"{0} yd","unitPattern-count-other":"{0} yd"},"length-foot":{displayName:"pés","unitPattern-count-one":"{0} pé","unitPattern-count-other":"{0} pés",perUnitPattern:"{0}/pé"},"length-inch":{displayName:"polegadas","unitPattern-count-one":"{0} pol.","unitPattern-count-other":"{0} pol.",perUnitPattern:"{0}/pol."},"length-parsec":{displayName:"pc","unitPattern-count-one":"{0} pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"anos-luz","unitPattern-count-one":"{0} ano-luz","unitPattern-count-other":"{0} anos-luz"},"length-astronomical-unit":{displayName:"ua","unitPattern-count-one":"{0} ua","unitPattern-count-other":"{0} ua"},"length-furlong":{displayName:"furlongs","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"braças","unitPattern-count-one":"{0} bça.","unitPattern-count-other":"{0} bça."},"length-nautical-mile":{displayName:"nmi","unitPattern-count-one":"{0} nmi","unitPattern-count-other":"{0} nmi"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0} smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"pontos","unitPattern-count-other":"{0} pt"},"length-solar-radius":{displayName:"raios solares","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"luminosidades solares","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0} t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0} kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"gramas","unitPattern-count-one":"{0} g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0} mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0} µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"toneladas","unitPattern-count-one":"{0} ton","unitPattern-count-other":"{0} ton"},"mass-stone":{displayName:"stones","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"libras","unitPattern-count-one":"{0} lb","unitPattern-count-other":"{0} lb",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz","unitPattern-count-one":"{0} oz","unitPattern-count-other":"{0} oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz troy","unitPattern-count-one":"{0} oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"quilates","unitPattern-count-one":"{0} ct","unitPattern-count-other":"{0} ct"},"mass-dalton":{displayName:"daltons","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"massas da Terra","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"massas solares","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0} GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0} MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0} kW","unitPattern-count-other":"{0} kW"},"power-watt":{displayName:"watts","unitPattern-count-one":"{0} W","unitPattern-count-other":"{0} W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0} mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"cv","unitPattern-count-one":"{0} cv","unitPattern-count-other":"{0} cv"},"pressure-millimeter-of-mercury":{displayName:"mm Hg","unitPattern-count-one":"{0} mm Hg","unitPattern-count-other":"{0} mm Hg"},"pressure-pound-per-square-inch":{displayName:"psi","unitPattern-count-one":"{0} psi","unitPattern-count-other":"{0} psi"},"pressure-inch-hg":{displayName:"in Hg","unitPattern-count-one":"{0} inHg","unitPattern-count-other":"{0} inHg"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"mbar","unitPattern-count-one":"{0} mb","unitPattern-count-other":"{0} mb"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-one":"{0} atm","unitPattern-count-other":"{0} atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0} hPa","unitPattern-count-other":"{0} hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-one":"{0} km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"m/s","unitPattern-count-one":"{0} m/s","unitPattern-count-other":"{0} m/s"},"speed-mile-per-hour":{displayName:"mi/h","unitPattern-count-one":"{0} mi/h","unitPattern-count-other":"{0} mi/h"},"speed-knot":{displayName:"nó","unitPattern-count-one":"{0} nó","unitPattern-count-other":"{0} nós"},"temperature-generic":{displayName:"°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"graus Celsius","unitPattern-count-one":"{0} °C","unitPattern-count-other":"{0} °C"},"temperature-fahrenheit":{displayName:"graus Fahrenheit","unitPattern-count-one":"{0} °F","unitPattern-count-other":"{0} °F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0} K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-other":"{0} lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-other":"{0} N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0} km³","unitPattern-count-other":"{0} km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0} m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0} cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mi³","unitPattern-count-one":"{0} mi³","unitPattern-count-other":"{0} mi³"},"volume-cubic-yard":{displayName:"yd³","unitPattern-count-one":"{0} yd³","unitPattern-count-other":"{0} yd³"},"volume-cubic-foot":{displayName:"ft³","unitPattern-count-one":"{0} ft³","unitPattern-count-other":"{0} ft³"},"volume-cubic-inch":{displayName:"in³","unitPattern-count-one":"{0} in³","unitPattern-count-other":"{0} in³"},"volume-megaliter":{displayName:"ML","unitPattern-count-one":"{0} ML","unitPattern-count-other":"{0} ML"},"volume-hectoliter":{displayName:"hl","unitPattern-count-one":"{0} hl","unitPattern-count-other":"{0} hl"},"volume-liter":{displayName:"litros","unitPattern-count-one":"{0} l","unitPattern-count-other":"{0} l",perUnitPattern:"{0}/l"},"volume-deciliter":{displayName:"dl","unitPattern-count-one":"{0} dl","unitPattern-count-other":"{0} dl"},"volume-centiliter":{displayName:"cl","unitPattern-count-one":"{0} cl","unitPattern-count-other":"{0} cl"},"volume-milliliter":{displayName:"ml","unitPattern-count-one":"{0} ml","unitPattern-count-other":"{0} ml"},"volume-pint-metric":{displayName:"ptm","unitPattern-count-one":"{0} ptm","unitPattern-count-other":"{0} ptm"},"volume-cup-metric":{displayName:"chám","unitPattern-count-one":"{0} chám","unitPattern-count-other":"{0} chám"},"volume-acre-foot":{displayName:"ac ft","unitPattern-count-one":"{0} ac ft","unitPattern-count-other":"{0} ac ft"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gal","unitPattern-count-one":"{0} gal","unitPattern-count-other":"{0} gal",perUnitPattern:"{0}/gal"},"volume-gallon-imperial":{displayName:"gal imp.","unitPattern-count-one":"{0} gal imp.","unitPattern-count-other":"{0} gal imp.",perUnitPattern:"{0}/gal imp."},"volume-quart":{displayName:"qts","unitPattern-count-one":"{0} qt","unitPattern-count-other":"{0} qt"},"volume-pint":{displayName:"pints","unitPattern-count-one":"{0} pt","unitPattern-count-other":"{0} pt"},"volume-cup":{displayName:"chávenas","unitPattern-count-one":"{0} cháv.","unitPattern-count-other":"{0} cháv."},"volume-fluid-ounce":{displayName:"fl oz","unitPattern-count-one":"{0} fl oz","unitPattern-count-other":"{0} fl oz"},"volume-fluid-ounce-imperial":{displayName:"onças fluidas imp.","unitPattern-count-one":"{0} onça fluida imp.","unitPattern-count-other":"{0} onças fluidas imp."},"volume-tablespoon":{displayName:"cs","unitPattern-count-one":"{0} cs","unitPattern-count-other":"{0} cs"},"volume-teaspoon":{displayName:"cc","unitPattern-count-one":"{0} cc","unitPattern-count-other":"{0} cc"},"volume-barrel":{displayName:"barril","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"direção",east:"{0} E",north:"{0} N",south:"{0} S",west:"{0} O"}},narrow:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"força g","unitPattern-count-one":"{0} G","unitPattern-count-other":"{0} G"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-one":"{0} m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"rev","unitPattern-count-other":"{0} rev"},"angle-radian":{displayName:"radianos","unitPattern-count-one":"{0} rad","unitPattern-count-other":"{0} rad"},"angle-degree":{displayName:"graus","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"minutos de arco","unitPattern-count-one":"{0}'","unitPattern-count-other":"{0}'"},"angle-arc-second":{displayName:"segundos de arco","unitPattern-count-one":'{0}\\"',"unitPattern-count-other":'{0}\\"'},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0} km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"hectares","unitPattern-count-one":"{0} ha","unitPattern-count-other":"{0} ha"},"area-square-meter":{displayName:"m²","unitPattern-count-one":"{0} m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0} cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"mi²","unitPattern-count-one":"{0} mi²","unitPattern-count-other":"{0} mi²",perUnitPattern:"{0}/mi²"},"area-acre":{displayName:"acres","unitPattern-count-one":"{0} acre","unitPattern-count-other":"{0} acres"},"area-square-yard":{displayName:"yd²","unitPattern-count-one":"{0} yd²","unitPattern-count-other":"{0} yd²"},"area-square-foot":{displayName:"pés quadrados","unitPattern-count-one":"{0} ft²","unitPattern-count-other":"{0} ft²"},"area-square-inch":{displayName:"in²","unitPattern-count-one":"{0} in²","unitPattern-count-other":"{0} in²",perUnitPattern:"{0}/in²"},"area-dunam":{displayName:"dunans","unitPattern-count-one":"{0} dunam","unitPattern-count-other":"{0} dunans"},"concentr-karat":{displayName:"quilates","unitPattern-count-one":"{0} kt","unitPattern-count-other":"{0} kt"},"concentr-milligram-per-deciliter":{displayName:"mg/dl","unitPattern-count-one":"{0} mg/dl","unitPattern-count-other":"{0} mg/dl"},"concentr-millimole-per-liter":{displayName:"milimole/litro","unitPattern-count-one":"{0} mmol/l","unitPattern-count-other":"{0} mmol/l"},"concentr-part-per-million":{displayName:"partes/milhão","unitPattern-count-other":"{0} ppm"},"concentr-percent":{displayName:"%","unitPattern-count-one":"{0}%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"por mil","unitPattern-count-one":"{0}‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"ponto base","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"litros/km","unitPattern-count-one":"{0} l/km","unitPattern-count-other":"{0} l/km"},"consumption-liter-per-100kilometers":{displayName:"l/100 km","unitPattern-count-one":"{0}l/100km","unitPattern-count-other":"{0}l/100km"},"consumption-mile-per-gallon":{displayName:"milhas/galão","unitPattern-count-one":"{0} mpg","unitPattern-count-other":"{0} mpg"},"consumption-mile-per-gallon-imperial":{displayName:"milhas/gal imp.","unitPattern-count-one":"{0} mpg imp.","unitPattern-count-other":"{0} mpg imp."},"digital-petabyte":{displayName:"PByte","unitPattern-count-one":"{0} PB","unitPattern-count-other":"{0} PB"},"digital-terabyte":{displayName:"TByte","unitPattern-count-one":"{0} TB","unitPattern-count-other":"{0} TB"},"digital-terabit":{displayName:"Tbit","unitPattern-count-one":"{0} Tb","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"GByte","unitPattern-count-one":"{0} GB","unitPattern-count-other":"{0} GB"},"digital-gigabit":{displayName:"Gbit","unitPattern-count-one":"{0} Gb","unitPattern-count-other":"{0} Gb"},"digital-megabyte":{displayName:"MByte","unitPattern-count-one":"{0} MB","unitPattern-count-other":"{0} MB"},"digital-megabit":{displayName:"Mbit","unitPattern-count-one":"{0} Mb","unitPattern-count-other":"{0} Mb"},"digital-kilobyte":{displayName:"kByte","unitPattern-count-one":"{0} kB","unitPattern-count-other":"{0} kB"},"digital-kilobit":{displayName:"kbit","unitPattern-count-one":"{0} kb","unitPattern-count-other":"{0} kb"},"digital-byte":{displayName:"byte","unitPattern-count-one":"{0} byte","unitPattern-count-other":"{0} byte"},"digital-bit":{displayName:"bit","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bit"},"duration-century":{displayName:"séc.","unitPattern-count-one":"{0} séc.","unitPattern-count-other":"{0} sécs."},"duration-decade":{displayName:"déc.","unitPattern-count-one":"{0} déc.","unitPattern-count-other":"{0} déc."},"duration-year":{displayName:"ano","unitPattern-count-one":"{0} ano","unitPattern-count-other":"{0} anos",perUnitPattern:"{0}/ano"},"duration-month":{displayName:"mês","unitPattern-count-one":"{0} mês","unitPattern-count-other":"{0} meses",perUnitPattern:"{0}/mês"},"duration-week":{displayName:"sem.","unitPattern-count-one":"{0} sem.","unitPattern-count-other":"{0} sem.",perUnitPattern:"{0}/sem."},"duration-day":{displayName:"dia","unitPattern-count-one":"{0} dia","unitPattern-count-other":"{0} dias",perUnitPattern:"{0}/d"},"duration-hour":{displayName:"hora","unitPattern-count-one":"{0} h","unitPattern-count-other":"{0} h",perUnitPattern:"{0}/h"},"duration-minute":{displayName:"min","unitPattern-count-one":"{0} min","unitPattern-count-other":"{0} min",perUnitPattern:"{0}/min"},"duration-second":{displayName:"s","unitPattern-count-one":"{0} s","unitPattern-count-other":"{0} s",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"ms","unitPattern-count-one":"{0} ms","unitPattern-count-other":"{0} ms"},"duration-microsecond":{displayName:"μs","unitPattern-count-one":"{0} μs","unitPattern-count-other":"{0} μs"},"duration-nanosecond":{displayName:"ns","unitPattern-count-one":"{0} ns","unitPattern-count-other":"{0} ns"},"electric-ampere":{displayName:"amps","unitPattern-count-one":"{0} A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"miliamps","unitPattern-count-one":"{0} mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"ohms","unitPattern-count-one":"{0} Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"volts","unitPattern-count-one":"{0} V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-calorie":{displayName:"cal","unitPattern-count-one":"{0} cal","unitPattern-count-other":"{0} cal"},"energy-foodcalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-kilojoule":{displayName:"quilojoule","unitPattern-count-one":"{0} kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"joules","unitPattern-count-one":"{0} J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kW-hora","unitPattern-count-one":"{0} kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"eletrão-volt","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0} BTU","unitPattern-count-other":"{0} BTU"},"energy-therm-us":{displayName:"thm EUA","unitPattern-count-one":"{0} thm EUA","unitPattern-count-other":"{0} thm EUA"},"force-pound-force":{displayName:"libra-força","unitPattern-count-other":"{0} lbf"},"force-newton":{displayName:"newton","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0} GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0} MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0} kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0} Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"píxeis","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"megapíxeis","unitPattern-count-other":"{0} MP"},"graphics-pixel-per-centimeter":{displayName:"ppcm","unitPattern-count-other":"{0} ppcm"},"graphics-pixel-per-inch":{displayName:"ppi","unitPattern-count-other":"{0} ppi"},"graphics-dot-per-centimeter":{displayName:"dpcm","unitPattern-count-other":"{0} dpcm"},"graphics-dot-per-inch":{displayName:"ppp","unitPattern-count-one":"{0} ppp","unitPattern-count-other":"{0} ppp"},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0} km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"metro","unitPattern-count-one":"{0} m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0} dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0} cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0} mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µm","unitPattern-count-one":"{0} µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0} nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0} pm","unitPattern-count-other":"{0} pm"},"length-mile":{displayName:"mil","unitPattern-count-one":"{0} milha","unitPattern-count-other":"{0} milhas"},"length-yard":{displayName:"jardas","unitPattern-count-one":"{0} yd","unitPattern-count-other":"{0} yd"},"length-foot":{displayName:"pés","unitPattern-count-one":"{0}′","unitPattern-count-other":"{0}′",perUnitPattern:"{0}/pé"},"length-inch":{displayName:"pol.","unitPattern-count-one":"{0}″","unitPattern-count-other":"{0}″",perUnitPattern:"{0}/pol."},"length-parsec":{displayName:"parsec","unitPattern-count-one":"{0} pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"anos-luz","unitPattern-count-one":"{0} ano-luz","unitPattern-count-other":"{0} anos-luz"},"length-astronomical-unit":{displayName:"ua","unitPattern-count-one":"{0} ua","unitPattern-count-other":"{0} ua"},"length-furlong":{displayName:"furlong","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"braça","unitPattern-count-one":"{0} bça.","unitPattern-count-other":"{0} bça."},"length-nautical-mile":{displayName:"mn","unitPattern-count-one":"{0} mn","unitPattern-count-other":"{0} mn"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0} smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"pontos","unitPattern-count-other":"{0} pt"},"length-solar-radius":{displayName:"raios solares","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"luminosidades solares","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0} t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0} kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"grama","unitPattern-count-one":"{0} g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0} mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0} µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"ton","unitPattern-count-one":"{0} ton","unitPattern-count-other":"{0} ton"},"mass-stone":{displayName:"stone","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"libras","unitPattern-count-one":"{0} lb","unitPattern-count-other":"{0} lb",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz","unitPattern-count-one":"{0} oz","unitPattern-count-other":"{0} oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz troy","unitPattern-count-one":"{0} oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"quilate","unitPattern-count-one":"{0} ql","unitPattern-count-other":"{0} ql"},"mass-dalton":{displayName:"daltons","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"massas da Terra","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"massas solares","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0} GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0} MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0} kW","unitPattern-count-other":"{0} kW"},"power-watt":{displayName:"watts","unitPattern-count-one":"{0} W","unitPattern-count-other":"{0} W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0} mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"cv","unitPattern-count-one":"{0} cv","unitPattern-count-other":"{0} cv"},"pressure-millimeter-of-mercury":{displayName:"mmHg","unitPattern-count-one":"{0} mmHg","unitPattern-count-other":"{0} mmHg"},"pressure-pound-per-square-inch":{displayName:"psi","unitPattern-count-one":"{0} psi","unitPattern-count-other":"{0} psi"},"pressure-inch-hg":{displayName:"in Hg","unitPattern-count-one":'{0}\\" Hg',"unitPattern-count-other":'{0}\\" Hg'},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"mbar","unitPattern-count-one":"{0} mb","unitPattern-count-other":"{0} mb"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-one":"{0} atm","unitPattern-count-other":"{0} atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0} hPa","unitPattern-count-other":"{0} hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-one":"{0} km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"m/s","unitPattern-count-one":"{0} m/s","unitPattern-count-other":"{0} m/s"},"speed-mile-per-hour":{displayName:"mi/h","unitPattern-count-one":"{0} mi/h","unitPattern-count-other":"{0} mi/h"},"speed-knot":{displayName:"nó","unitPattern-count-one":"{0} nó","unitPattern-count-other":"{0} nós"},"temperature-generic":{displayName:"°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"°C","unitPattern-count-one":"{0}°C","unitPattern-count-other":"{0}°C"},"temperature-fahrenheit":{displayName:"graus Fahrenheit","unitPattern-count-one":"{0}°F","unitPattern-count-other":"{0}°F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0} K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-other":"{0} lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-other":"{0} N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0} km³","unitPattern-count-other":"{0} km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0} m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0} cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mi³","unitPattern-count-one":"{0} mi³","unitPattern-count-other":"{0} mi³"},"volume-cubic-yard":{displayName:"yd³","unitPattern-count-one":"{0} yd³","unitPattern-count-other":"{0} yd³"},"volume-cubic-foot":{displayName:"ft³","unitPattern-count-one":"{0} ft³","unitPattern-count-other":"{0} ft³"},"volume-cubic-inch":{displayName:"in³","unitPattern-count-one":"{0} in³","unitPattern-count-other":"{0} in³"},"volume-megaliter":{displayName:"ML","unitPattern-count-one":"{0} ML","unitPattern-count-other":"{0} ML"},"volume-hectoliter":{displayName:"hl","unitPattern-count-one":"{0} hl","unitPattern-count-other":"{0} hl"},"volume-liter":{displayName:"litro","unitPattern-count-one":"{0} l","unitPattern-count-other":"{0} l",perUnitPattern:"{0}/l"},"volume-deciliter":{displayName:"dl","unitPattern-count-one":"{0} dl","unitPattern-count-other":"{0} dl"},"volume-centiliter":{displayName:"cl","unitPattern-count-one":"{0} cl","unitPattern-count-other":"{0} cl"},"volume-milliliter":{displayName:"ml","unitPattern-count-one":"{0} ml","unitPattern-count-other":"{0} ml"},"volume-pint-metric":{displayName:"ptm","unitPattern-count-one":"{0} ptm","unitPattern-count-other":"{0} ptm"},"volume-cup-metric":{displayName:"chám","unitPattern-count-one":"{0} chám","unitPattern-count-other":"{0} chám"},"volume-acre-foot":{displayName:"ac ft","unitPattern-count-one":"{0} ac ft","unitPattern-count-other":"{0} ac ft"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gal","unitPattern-count-one":"{0} gal","unitPattern-count-other":"{0} gal",perUnitPattern:"{0}/gal"},"volume-gallon-imperial":{displayName:"gal imp.","unitPattern-count-one":"{0} gal imp.","unitPattern-count-other":"{0} gal imp.",perUnitPattern:"{0}/gal imp."},"volume-quart":{displayName:"qts","unitPattern-count-one":"{0} qt","unitPattern-count-other":"{0} qt"},"volume-pint":{displayName:"pints","unitPattern-count-one":"{0} pt","unitPattern-count-other":"{0} pt"},"volume-cup":{displayName:"chávenas","unitPattern-count-one":"{0} cháv.","unitPattern-count-other":"{0} cháv."},"volume-fluid-ounce":{displayName:"fl. oz.","unitPattern-count-one":"{0} fl. oz.","unitPattern-count-other":"{0} fl. oz."},"volume-fluid-ounce-imperial":{displayName:"onças fluidas imp.","unitPattern-count-one":"{0} onça fluida imp.","unitPattern-count-other":"{0} onças fluidas imp."},"volume-tablespoon":{displayName:"cs","unitPattern-count-one":"{0} cs","unitPattern-count-other":"{0} cs"},"volume-teaspoon":{displayName:"cc","unitPattern-count-one":"{0} cc","unitPattern-count-other":"{0} cc"},"volume-barrel":{displayName:"barril","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"direção",east:"{0}L",north:"{0} N",south:"{0} S",west:"{0}O"}},"durationUnit-type-hm":{durationUnitPattern:"h:mm"},"durationUnit-type-hms":{durationUnitPattern:"h:mm:ss"},"durationUnit-type-ms":{durationUnitPattern:"m:ss"}}}}}];