var CldrData=[{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},likelySubtags:{aa:"aa-Latn-ET",aai:"aai-Latn-ZZ",aak:"aak-Latn-ZZ",aau:"aau-Latn-ZZ",ab:"ab-Cyrl-GE",abi:"abi-Latn-ZZ",abq:"abq-Cyrl-ZZ",abr:"abr-Latn-GH",abt:"abt-Latn-ZZ",aby:"aby-Latn-ZZ",acd:"acd-Latn-ZZ",ace:"ace-Latn-ID",ach:"ach-Latn-UG",ada:"ada-Latn-GH",ade:"ade-Latn-ZZ",adj:"adj-Latn-ZZ",adp:"adp-Tibt-BT",ady:"ady-Cyrl-RU",adz:"adz-Latn-ZZ",ae:"ae-Avst-IR",aeb:"aeb-Arab-TN",aey:"aey-Latn-ZZ",af:"af-Latn-ZA",agc:"agc-Latn-ZZ",agd:"agd-Latn-ZZ",agg:"agg-Latn-ZZ",agm:"agm-Latn-ZZ",ago:"ago-Latn-ZZ",agq:"agq-Latn-CM",aha:"aha-Latn-ZZ",ahl:"ahl-Latn-ZZ",aho:"aho-Ahom-IN",ajg:"ajg-Latn-ZZ",ak:"ak-Latn-GH",akk:"akk-Xsux-IQ",ala:"ala-Latn-ZZ",ali:"ali-Latn-ZZ",aln:"aln-Latn-XK",alt:"alt-Cyrl-RU",am:"am-Ethi-ET",amm:"amm-Latn-ZZ",amn:"amn-Latn-ZZ",amo:"amo-Latn-NG",amp:"amp-Latn-ZZ",an:"an-Latn-ES",anc:"anc-Latn-ZZ",ank:"ank-Latn-ZZ",ann:"ann-Latn-ZZ",any:"any-Latn-ZZ",aoj:"aoj-Latn-ZZ",aom:"aom-Latn-ZZ",aoz:"aoz-Latn-ID",apc:"apc-Arab-ZZ",apd:"apd-Arab-TG",ape:"ape-Latn-ZZ",apr:"apr-Latn-ZZ",aps:"aps-Latn-ZZ",apz:"apz-Latn-ZZ",ar:"ar-Arab-EG",arc:"arc-Armi-IR","arc-Nbat":"arc-Nbat-JO","arc-Palm":"arc-Palm-SY",arh:"arh-Latn-ZZ",arn:"arn-Latn-CL",aro:"aro-Latn-BO",arq:"arq-Arab-DZ",ars:"ars-Arab-SA",ary:"ary-Arab-MA",arz:"arz-Arab-EG",as:"as-Beng-IN",asa:"asa-Latn-TZ",ase:"ase-Sgnw-US",asg:"asg-Latn-ZZ",aso:"aso-Latn-ZZ",ast:"ast-Latn-ES",ata:"ata-Latn-ZZ",atg:"atg-Latn-ZZ",atj:"atj-Latn-CA",auy:"auy-Latn-ZZ",av:"av-Cyrl-RU",avl:"avl-Arab-ZZ",avn:"avn-Latn-ZZ",avt:"avt-Latn-ZZ",avu:"avu-Latn-ZZ",awa:"awa-Deva-IN",awb:"awb-Latn-ZZ",awo:"awo-Latn-ZZ",awx:"awx-Latn-ZZ",ay:"ay-Latn-BO",ayb:"ayb-Latn-ZZ",az:"az-Latn-AZ","az-Arab":"az-Arab-IR","az-IQ":"az-Arab-IQ","az-IR":"az-Arab-IR","az-RU":"az-Cyrl-RU",ba:"ba-Cyrl-RU",bal:"bal-Arab-PK",ban:"ban-Latn-ID",bap:"bap-Deva-NP",bar:"bar-Latn-AT",bas:"bas-Latn-CM",bav:"bav-Latn-ZZ",bax:"bax-Bamu-CM",bba:"bba-Latn-ZZ",bbb:"bbb-Latn-ZZ",bbc:"bbc-Latn-ID",bbd:"bbd-Latn-ZZ",bbj:"bbj-Latn-CM",bbp:"bbp-Latn-ZZ",bbr:"bbr-Latn-ZZ",bcf:"bcf-Latn-ZZ",bch:"bch-Latn-ZZ",bci:"bci-Latn-CI",bcm:"bcm-Latn-ZZ",bcn:"bcn-Latn-ZZ",bco:"bco-Latn-ZZ",bcq:"bcq-Ethi-ZZ",bcu:"bcu-Latn-ZZ",bdd:"bdd-Latn-ZZ",be:"be-Cyrl-BY",bef:"bef-Latn-ZZ",beh:"beh-Latn-ZZ",bej:"bej-Arab-SD",bem:"bem-Latn-ZM",bet:"bet-Latn-ZZ",bew:"bew-Latn-ID",bex:"bex-Latn-ZZ",bez:"bez-Latn-TZ",bfd:"bfd-Latn-CM",bfq:"bfq-Taml-IN",bft:"bft-Arab-PK",bfy:"bfy-Deva-IN",bg:"bg-Cyrl-BG",bgc:"bgc-Deva-IN",bgn:"bgn-Arab-PK",bgx:"bgx-Grek-TR",bhb:"bhb-Deva-IN",bhg:"bhg-Latn-ZZ",bhi:"bhi-Deva-IN",bhl:"bhl-Latn-ZZ",bho:"bho-Deva-IN",bhy:"bhy-Latn-ZZ",bi:"bi-Latn-VU",bib:"bib-Latn-ZZ",big:"big-Latn-ZZ",bik:"bik-Latn-PH",bim:"bim-Latn-ZZ",bin:"bin-Latn-NG",bio:"bio-Latn-ZZ",biq:"biq-Latn-ZZ",bjh:"bjh-Latn-ZZ",bji:"bji-Ethi-ZZ",bjj:"bjj-Deva-IN",bjn:"bjn-Latn-ID",bjo:"bjo-Latn-ZZ",bjr:"bjr-Latn-ZZ",bjt:"bjt-Latn-SN",bjz:"bjz-Latn-ZZ",bkc:"bkc-Latn-ZZ",bkm:"bkm-Latn-CM",bkq:"bkq-Latn-ZZ",bku:"bku-Latn-PH",bkv:"bkv-Latn-ZZ",blt:"blt-Tavt-VN",bm:"bm-Latn-ML",bmh:"bmh-Latn-ZZ",bmk:"bmk-Latn-ZZ",bmq:"bmq-Latn-ML",bmu:"bmu-Latn-ZZ",bn:"bn-Beng-BD",bng:"bng-Latn-ZZ",bnm:"bnm-Latn-ZZ",bnp:"bnp-Latn-ZZ",bo:"bo-Tibt-CN",boj:"boj-Latn-ZZ",bom:"bom-Latn-ZZ",bon:"bon-Latn-ZZ",bpy:"bpy-Beng-IN",bqc:"bqc-Latn-ZZ",bqi:"bqi-Arab-IR",bqp:"bqp-Latn-ZZ",bqv:"bqv-Latn-CI",br:"br-Latn-FR",bra:"bra-Deva-IN",brh:"brh-Arab-PK",brx:"brx-Deva-IN",brz:"brz-Latn-ZZ",bs:"bs-Latn-BA",bsj:"bsj-Latn-ZZ",bsq:"bsq-Bass-LR",bss:"bss-Latn-CM",bst:"bst-Ethi-ZZ",bto:"bto-Latn-PH",btt:"btt-Latn-ZZ",btv:"btv-Deva-PK",bua:"bua-Cyrl-RU",buc:"buc-Latn-YT",bud:"bud-Latn-ZZ",bug:"bug-Latn-ID",buk:"buk-Latn-ZZ",bum:"bum-Latn-CM",buo:"buo-Latn-ZZ",bus:"bus-Latn-ZZ",buu:"buu-Latn-ZZ",bvb:"bvb-Latn-GQ",bwd:"bwd-Latn-ZZ",bwr:"bwr-Latn-ZZ",bxh:"bxh-Latn-ZZ",bye:"bye-Latn-ZZ",byn:"byn-Ethi-ER",byr:"byr-Latn-ZZ",bys:"bys-Latn-ZZ",byv:"byv-Latn-CM",byx:"byx-Latn-ZZ",bza:"bza-Latn-ZZ",bze:"bze-Latn-ML",bzf:"bzf-Latn-ZZ",bzh:"bzh-Latn-ZZ",bzw:"bzw-Latn-ZZ",ca:"ca-Latn-ES",can:"can-Latn-ZZ",cbj:"cbj-Latn-ZZ",cch:"cch-Latn-NG",ccp:"ccp-Cakm-BD",ce:"ce-Cyrl-RU",ceb:"ceb-Latn-PH",cfa:"cfa-Latn-ZZ",cgg:"cgg-Latn-UG",ch:"ch-Latn-GU",chk:"chk-Latn-FM",chm:"chm-Cyrl-RU",cho:"cho-Latn-US",chp:"chp-Latn-CA",chr:"chr-Cher-US",cic:"cic-Latn-US",cja:"cja-Arab-KH",cjm:"cjm-Cham-VN",cjv:"cjv-Latn-ZZ",ckb:"ckb-Arab-IQ",ckl:"ckl-Latn-ZZ",cko:"cko-Latn-ZZ",cky:"cky-Latn-ZZ",cla:"cla-Latn-ZZ",cme:"cme-Latn-ZZ",cmg:"cmg-Soyo-MN",co:"co-Latn-FR",cop:"cop-Copt-EG",cps:"cps-Latn-PH",cr:"cr-Cans-CA",crh:"crh-Cyrl-UA",crj:"crj-Cans-CA",crk:"crk-Cans-CA",crl:"crl-Cans-CA",crm:"crm-Cans-CA",crs:"crs-Latn-SC",cs:"cs-Latn-CZ",csb:"csb-Latn-PL",csw:"csw-Cans-CA",ctd:"ctd-Pauc-MM",cu:"cu-Cyrl-RU","cu-Glag":"cu-Glag-BG",cv:"cv-Cyrl-RU",cy:"cy-Latn-GB",da:"da-Latn-DK",dad:"dad-Latn-ZZ",daf:"daf-Latn-ZZ",dag:"dag-Latn-ZZ",dah:"dah-Latn-ZZ",dak:"dak-Latn-US",dar:"dar-Cyrl-RU",dav:"dav-Latn-KE",dbd:"dbd-Latn-ZZ",dbq:"dbq-Latn-ZZ",dcc:"dcc-Arab-IN",ddn:"ddn-Latn-ZZ",de:"de-Latn-DE",ded:"ded-Latn-ZZ",den:"den-Latn-CA",dga:"dga-Latn-ZZ",dgh:"dgh-Latn-ZZ",dgi:"dgi-Latn-ZZ",dgl:"dgl-Arab-ZZ",dgr:"dgr-Latn-CA",dgz:"dgz-Latn-ZZ",dia:"dia-Latn-ZZ",dje:"dje-Latn-NE",dnj:"dnj-Latn-CI",dob:"dob-Latn-ZZ",doi:"doi-Arab-IN",dop:"dop-Latn-ZZ",dow:"dow-Latn-ZZ",drh:"drh-Mong-CN",dri:"dri-Latn-ZZ",drs:"drs-Ethi-ZZ",dsb:"dsb-Latn-DE",dtm:"dtm-Latn-ML",dtp:"dtp-Latn-MY",dts:"dts-Latn-ZZ",dty:"dty-Deva-NP",dua:"dua-Latn-CM",duc:"duc-Latn-ZZ",dud:"dud-Latn-ZZ",dug:"dug-Latn-ZZ",dv:"dv-Thaa-MV",dva:"dva-Latn-ZZ",dww:"dww-Latn-ZZ",dyo:"dyo-Latn-SN",dyu:"dyu-Latn-BF",dz:"dz-Tibt-BT",dzg:"dzg-Latn-ZZ",ebu:"ebu-Latn-KE",ee:"ee-Latn-GH",efi:"efi-Latn-NG",egl:"egl-Latn-IT",egy:"egy-Egyp-EG",eka:"eka-Latn-ZZ",eky:"eky-Kali-MM",el:"el-Grek-GR",ema:"ema-Latn-ZZ",emi:"emi-Latn-ZZ",en:"en-Latn-US","en-Shaw":"en-Shaw-GB",enn:"enn-Latn-ZZ",enq:"enq-Latn-ZZ",eo:"eo-Latn-001",eri:"eri-Latn-ZZ",es:"es-Latn-ES",esg:"esg-Gonm-IN",esu:"esu-Latn-US",et:"et-Latn-EE",etr:"etr-Latn-ZZ",ett:"ett-Ital-IT",etu:"etu-Latn-ZZ",etx:"etx-Latn-ZZ",eu:"eu-Latn-ES",ewo:"ewo-Latn-CM",ext:"ext-Latn-ES",fa:"fa-Arab-IR",faa:"faa-Latn-ZZ",fab:"fab-Latn-ZZ",fag:"fag-Latn-ZZ",fai:"fai-Latn-ZZ",fan:"fan-Latn-GQ",ff:"ff-Latn-SN","ff-Adlm":"ff-Adlm-GN",ffi:"ffi-Latn-ZZ",ffm:"ffm-Latn-ML",fi:"fi-Latn-FI",fia:"fia-Arab-SD",fil:"fil-Latn-PH",fit:"fit-Latn-SE",fj:"fj-Latn-FJ",flr:"flr-Latn-ZZ",fmp:"fmp-Latn-ZZ",fo:"fo-Latn-FO",fod:"fod-Latn-ZZ",fon:"fon-Latn-BJ",for:"for-Latn-ZZ",fpe:"fpe-Latn-ZZ",fqs:"fqs-Latn-ZZ",fr:"fr-Latn-FR",frc:"frc-Latn-US",frp:"frp-Latn-FR",frr:"frr-Latn-DE",frs:"frs-Latn-DE",fub:"fub-Arab-CM",fud:"fud-Latn-WF",fue:"fue-Latn-ZZ",fuf:"fuf-Latn-GN",fuh:"fuh-Latn-ZZ",fuq:"fuq-Latn-NE",fur:"fur-Latn-IT",fuv:"fuv-Latn-NG",fuy:"fuy-Latn-ZZ",fvr:"fvr-Latn-SD",fy:"fy-Latn-NL",ga:"ga-Latn-IE",gaa:"gaa-Latn-GH",gaf:"gaf-Latn-ZZ",gag:"gag-Latn-MD",gah:"gah-Latn-ZZ",gaj:"gaj-Latn-ZZ",gam:"gam-Latn-ZZ",gan:"gan-Hans-CN",gaw:"gaw-Latn-ZZ",gay:"gay-Latn-ID",gba:"gba-Latn-ZZ",gbf:"gbf-Latn-ZZ",gbm:"gbm-Deva-IN",gby:"gby-Latn-ZZ",gbz:"gbz-Arab-IR",gcr:"gcr-Latn-GF",gd:"gd-Latn-GB",gde:"gde-Latn-ZZ",gdn:"gdn-Latn-ZZ",gdr:"gdr-Latn-ZZ",geb:"geb-Latn-ZZ",gej:"gej-Latn-ZZ",gel:"gel-Latn-ZZ",gez:"gez-Ethi-ET",gfk:"gfk-Latn-ZZ",ggn:"ggn-Deva-NP",ghs:"ghs-Latn-ZZ",gil:"gil-Latn-KI",gim:"gim-Latn-ZZ",gjk:"gjk-Arab-PK",gjn:"gjn-Latn-ZZ",gju:"gju-Arab-PK",gkn:"gkn-Latn-ZZ",gkp:"gkp-Latn-ZZ",gl:"gl-Latn-ES",glk:"glk-Arab-IR",gmm:"gmm-Latn-ZZ",gmv:"gmv-Ethi-ZZ",gn:"gn-Latn-PY",gnd:"gnd-Latn-ZZ",gng:"gng-Latn-ZZ",god:"god-Latn-ZZ",gof:"gof-Ethi-ZZ",goi:"goi-Latn-ZZ",gom:"gom-Deva-IN",gon:"gon-Telu-IN",gor:"gor-Latn-ID",gos:"gos-Latn-NL",got:"got-Goth-UA",grb:"grb-Latn-ZZ",grc:"grc-Cprt-CY","grc-Linb":"grc-Linb-GR",grt:"grt-Beng-IN",grw:"grw-Latn-ZZ",gsw:"gsw-Latn-CH",gu:"gu-Gujr-IN",gub:"gub-Latn-BR",guc:"guc-Latn-CO",gud:"gud-Latn-ZZ",gur:"gur-Latn-GH",guw:"guw-Latn-ZZ",gux:"gux-Latn-ZZ",guz:"guz-Latn-KE",gv:"gv-Latn-IM",gvf:"gvf-Latn-ZZ",gvr:"gvr-Deva-NP",gvs:"gvs-Latn-ZZ",gwc:"gwc-Arab-ZZ",gwi:"gwi-Latn-CA",gwt:"gwt-Arab-ZZ",gyi:"gyi-Latn-ZZ",ha:"ha-Latn-NG","ha-CM":"ha-Arab-CM","ha-SD":"ha-Arab-SD",hag:"hag-Latn-ZZ",hak:"hak-Hans-CN",ham:"ham-Latn-ZZ",haw:"haw-Latn-US",haz:"haz-Arab-AF",hbb:"hbb-Latn-ZZ",hdy:"hdy-Ethi-ZZ",he:"he-Hebr-IL",hhy:"hhy-Latn-ZZ",hi:"hi-Deva-IN",hia:"hia-Latn-ZZ",hif:"hif-Latn-FJ",hig:"hig-Latn-ZZ",hih:"hih-Latn-ZZ",hil:"hil-Latn-PH",hla:"hla-Latn-ZZ",hlu:"hlu-Hluw-TR",hmd:"hmd-Plrd-CN",hmt:"hmt-Latn-ZZ",hnd:"hnd-Arab-PK",hne:"hne-Deva-IN",hnj:"hnj-Hmng-LA",hnn:"hnn-Latn-PH",hno:"hno-Arab-PK",ho:"ho-Latn-PG",hoc:"hoc-Deva-IN",hoj:"hoj-Deva-IN",hot:"hot-Latn-ZZ",hr:"hr-Latn-HR",hsb:"hsb-Latn-DE",hsn:"hsn-Hans-CN",ht:"ht-Latn-HT",hu:"hu-Latn-HU",hui:"hui-Latn-ZZ",hy:"hy-Armn-AM",hz:"hz-Latn-NA",ia:"ia-Latn-001",ian:"ian-Latn-ZZ",iar:"iar-Latn-ZZ",iba:"iba-Latn-MY",ibb:"ibb-Latn-NG",iby:"iby-Latn-ZZ",ica:"ica-Latn-ZZ",ich:"ich-Latn-ZZ",id:"id-Latn-ID",idd:"idd-Latn-ZZ",idi:"idi-Latn-ZZ",idu:"idu-Latn-ZZ",ife:"ife-Latn-TG",ig:"ig-Latn-NG",igb:"igb-Latn-ZZ",ige:"ige-Latn-ZZ",ii:"ii-Yiii-CN",ijj:"ijj-Latn-ZZ",ik:"ik-Latn-US",ikk:"ikk-Latn-ZZ",ikt:"ikt-Latn-CA",ikw:"ikw-Latn-ZZ",ikx:"ikx-Latn-ZZ",ilo:"ilo-Latn-PH",imo:"imo-Latn-ZZ",in:"in-Latn-ID",inh:"inh-Cyrl-RU",io:"io-Latn-001",iou:"iou-Latn-ZZ",iri:"iri-Latn-ZZ",is:"is-Latn-IS",it:"it-Latn-IT",iu:"iu-Cans-CA",iw:"iw-Hebr-IL",iwm:"iwm-Latn-ZZ",iws:"iws-Latn-ZZ",izh:"izh-Latn-RU",izi:"izi-Latn-ZZ",ja:"ja-Jpan-JP",jab:"jab-Latn-ZZ",jam:"jam-Latn-JM",jbo:"jbo-Latn-001",jbu:"jbu-Latn-ZZ",jen:"jen-Latn-ZZ",jgk:"jgk-Latn-ZZ",jgo:"jgo-Latn-CM",ji:"ji-Hebr-UA",jib:"jib-Latn-ZZ",jmc:"jmc-Latn-TZ",jml:"jml-Deva-NP",jra:"jra-Latn-ZZ",jut:"jut-Latn-DK",jv:"jv-Latn-ID",jw:"jw-Latn-ID",ka:"ka-Geor-GE",kaa:"kaa-Cyrl-UZ",kab:"kab-Latn-DZ",kac:"kac-Latn-MM",kad:"kad-Latn-ZZ",kai:"kai-Latn-ZZ",kaj:"kaj-Latn-NG",kam:"kam-Latn-KE",kao:"kao-Latn-ML",kbd:"kbd-Cyrl-RU",kbm:"kbm-Latn-ZZ",kbp:"kbp-Latn-ZZ",kbq:"kbq-Latn-ZZ",kbx:"kbx-Latn-ZZ",kby:"kby-Arab-NE",kcg:"kcg-Latn-NG",kck:"kck-Latn-ZW",kcl:"kcl-Latn-ZZ",kct:"kct-Latn-ZZ",kde:"kde-Latn-TZ",kdh:"kdh-Arab-TG",kdl:"kdl-Latn-ZZ",kdt:"kdt-Thai-TH",kea:"kea-Latn-CV",ken:"ken-Latn-CM",kez:"kez-Latn-ZZ",kfo:"kfo-Latn-CI",kfr:"kfr-Deva-IN",kfy:"kfy-Deva-IN",kg:"kg-Latn-CD",kge:"kge-Latn-ID",kgf:"kgf-Latn-ZZ",kgp:"kgp-Latn-BR",kha:"kha-Latn-IN",khb:"khb-Talu-CN",khn:"khn-Deva-IN",khq:"khq-Latn-ML",khs:"khs-Latn-ZZ",kht:"kht-Mymr-IN",khw:"khw-Arab-PK",khz:"khz-Latn-ZZ",ki:"ki-Latn-KE",kij:"kij-Latn-ZZ",kiu:"kiu-Latn-TR",kiw:"kiw-Latn-ZZ",kj:"kj-Latn-NA",kjd:"kjd-Latn-ZZ",kjg:"kjg-Laoo-LA",kjs:"kjs-Latn-ZZ",kjy:"kjy-Latn-ZZ",kk:"kk-Cyrl-KZ","kk-AF":"kk-Arab-AF","kk-Arab":"kk-Arab-CN","kk-CN":"kk-Arab-CN","kk-IR":"kk-Arab-IR","kk-MN":"kk-Arab-MN",kkc:"kkc-Latn-ZZ",kkj:"kkj-Latn-CM",kl:"kl-Latn-GL",kln:"kln-Latn-KE",klq:"klq-Latn-ZZ",klt:"klt-Latn-ZZ",klx:"klx-Latn-ZZ",km:"km-Khmr-KH",kmb:"kmb-Latn-AO",kmh:"kmh-Latn-ZZ",kmo:"kmo-Latn-ZZ",kms:"kms-Latn-ZZ",kmu:"kmu-Latn-ZZ",kmw:"kmw-Latn-ZZ",kn:"kn-Knda-IN",knf:"knf-Latn-GW",knp:"knp-Latn-ZZ",ko:"ko-Kore-KR",koi:"koi-Cyrl-RU",kok:"kok-Deva-IN",kol:"kol-Latn-ZZ",kos:"kos-Latn-FM",koz:"koz-Latn-ZZ",kpe:"kpe-Latn-LR",kpf:"kpf-Latn-ZZ",kpo:"kpo-Latn-ZZ",kpr:"kpr-Latn-ZZ",kpx:"kpx-Latn-ZZ",kqb:"kqb-Latn-ZZ",kqf:"kqf-Latn-ZZ",kqs:"kqs-Latn-ZZ",kqy:"kqy-Ethi-ZZ",kr:"kr-Latn-ZZ",krc:"krc-Cyrl-RU",kri:"kri-Latn-SL",krj:"krj-Latn-PH",krl:"krl-Latn-RU",krs:"krs-Latn-ZZ",kru:"kru-Deva-IN",ks:"ks-Arab-IN",ksb:"ksb-Latn-TZ",ksd:"ksd-Latn-ZZ",ksf:"ksf-Latn-CM",ksh:"ksh-Latn-DE",ksj:"ksj-Latn-ZZ",ksr:"ksr-Latn-ZZ",ktb:"ktb-Ethi-ZZ",ktm:"ktm-Latn-ZZ",kto:"kto-Latn-ZZ",ktr:"ktr-Latn-MY",ku:"ku-Latn-TR","ku-Arab":"ku-Arab-IQ","ku-LB":"ku-Arab-LB",kub:"kub-Latn-ZZ",kud:"kud-Latn-ZZ",kue:"kue-Latn-ZZ",kuj:"kuj-Latn-ZZ",kum:"kum-Cyrl-RU",kun:"kun-Latn-ZZ",kup:"kup-Latn-ZZ",kus:"kus-Latn-ZZ",kv:"kv-Cyrl-RU",kvg:"kvg-Latn-ZZ",kvr:"kvr-Latn-ID",kvx:"kvx-Arab-PK",kw:"kw-Latn-GB",kwj:"kwj-Latn-ZZ",kwo:"kwo-Latn-ZZ",kwq:"kwq-Latn-ZZ",kxa:"kxa-Latn-ZZ",kxc:"kxc-Ethi-ZZ",kxe:"kxe-Latn-ZZ",kxm:"kxm-Thai-TH",kxp:"kxp-Arab-PK",kxw:"kxw-Latn-ZZ",kxz:"kxz-Latn-ZZ",ky:"ky-Cyrl-KG","ky-Arab":"ky-Arab-CN","ky-CN":"ky-Arab-CN","ky-Latn":"ky-Latn-TR","ky-TR":"ky-Latn-TR",kye:"kye-Latn-ZZ",kyx:"kyx-Latn-ZZ",kzj:"kzj-Latn-MY",kzr:"kzr-Latn-ZZ",kzt:"kzt-Latn-MY",la:"la-Latn-VA",lab:"lab-Lina-GR",lad:"lad-Hebr-IL",lag:"lag-Latn-TZ",lah:"lah-Arab-PK",laj:"laj-Latn-UG",las:"las-Latn-ZZ",lb:"lb-Latn-LU",lbe:"lbe-Cyrl-RU",lbu:"lbu-Latn-ZZ",lbw:"lbw-Latn-ID",lcm:"lcm-Latn-ZZ",lcp:"lcp-Thai-CN",ldb:"ldb-Latn-ZZ",led:"led-Latn-ZZ",lee:"lee-Latn-ZZ",lem:"lem-Latn-ZZ",lep:"lep-Lepc-IN",leq:"leq-Latn-ZZ",leu:"leu-Latn-ZZ",lez:"lez-Cyrl-RU",lg:"lg-Latn-UG",lgg:"lgg-Latn-ZZ",li:"li-Latn-NL",lia:"lia-Latn-ZZ",lid:"lid-Latn-ZZ",lif:"lif-Deva-NP","lif-Limb":"lif-Limb-IN",lig:"lig-Latn-ZZ",lih:"lih-Latn-ZZ",lij:"lij-Latn-IT",lis:"lis-Lisu-CN",ljp:"ljp-Latn-ID",lki:"lki-Arab-IR",lkt:"lkt-Latn-US",lle:"lle-Latn-ZZ",lln:"lln-Latn-ZZ",lmn:"lmn-Telu-IN",lmo:"lmo-Latn-IT",lmp:"lmp-Latn-ZZ",ln:"ln-Latn-CD",lns:"lns-Latn-ZZ",lnu:"lnu-Latn-ZZ",lo:"lo-Laoo-LA",loj:"loj-Latn-ZZ",lok:"lok-Latn-ZZ",lol:"lol-Latn-CD",lor:"lor-Latn-ZZ",los:"los-Latn-ZZ",loz:"loz-Latn-ZM",lrc:"lrc-Arab-IR",lt:"lt-Latn-LT",ltg:"ltg-Latn-LV",lu:"lu-Latn-CD",lua:"lua-Latn-CD",luo:"luo-Latn-KE",luy:"luy-Latn-KE",luz:"luz-Arab-IR",lv:"lv-Latn-LV",lwl:"lwl-Thai-TH",lzh:"lzh-Hans-CN",lzz:"lzz-Latn-TR",mad:"mad-Latn-ID",maf:"maf-Latn-CM",mag:"mag-Deva-IN",mai:"mai-Deva-IN",mak:"mak-Latn-ID",man:"man-Latn-GM","man-GN":"man-Nkoo-GN","man-Nkoo":"man-Nkoo-GN",mas:"mas-Latn-KE",maw:"maw-Latn-ZZ",maz:"maz-Latn-MX",mbh:"mbh-Latn-ZZ",mbo:"mbo-Latn-ZZ",mbq:"mbq-Latn-ZZ",mbu:"mbu-Latn-ZZ",mbw:"mbw-Latn-ZZ",mci:"mci-Latn-ZZ",mcp:"mcp-Latn-ZZ",mcq:"mcq-Latn-ZZ",mcr:"mcr-Latn-ZZ",mcu:"mcu-Latn-ZZ",mda:"mda-Latn-ZZ",mde:"mde-Arab-ZZ",mdf:"mdf-Cyrl-RU",mdh:"mdh-Latn-PH",mdj:"mdj-Latn-ZZ",mdr:"mdr-Latn-ID",mdx:"mdx-Ethi-ZZ",med:"med-Latn-ZZ",mee:"mee-Latn-ZZ",mek:"mek-Latn-ZZ",men:"men-Latn-SL",mer:"mer-Latn-KE",met:"met-Latn-ZZ",meu:"meu-Latn-ZZ",mfa:"mfa-Arab-TH",mfe:"mfe-Latn-MU",mfn:"mfn-Latn-ZZ",mfo:"mfo-Latn-ZZ",mfq:"mfq-Latn-ZZ",mg:"mg-Latn-MG",mgh:"mgh-Latn-MZ",mgl:"mgl-Latn-ZZ",mgo:"mgo-Latn-CM",mgp:"mgp-Deva-NP",mgy:"mgy-Latn-TZ",mh:"mh-Latn-MH",mhi:"mhi-Latn-ZZ",mhl:"mhl-Latn-ZZ",mi:"mi-Latn-NZ",mif:"mif-Latn-ZZ",min:"min-Latn-ID",mis:"mis-Hatr-IQ","mis-Medf":"mis-Medf-NG",miw:"miw-Latn-ZZ",mk:"mk-Cyrl-MK",mki:"mki-Arab-ZZ",mkl:"mkl-Latn-ZZ",mkp:"mkp-Latn-ZZ",mkw:"mkw-Latn-ZZ",ml:"ml-Mlym-IN",mle:"mle-Latn-ZZ",mlp:"mlp-Latn-ZZ",mls:"mls-Latn-SD",mmo:"mmo-Latn-ZZ",mmu:"mmu-Latn-ZZ",mmx:"mmx-Latn-ZZ",mn:"mn-Cyrl-MN","mn-CN":"mn-Mong-CN","mn-Mong":"mn-Mong-CN",mna:"mna-Latn-ZZ",mnf:"mnf-Latn-ZZ",mni:"mni-Beng-IN",mnw:"mnw-Mymr-MM",mo:"mo-Latn-RO",moa:"moa-Latn-ZZ",moe:"moe-Latn-CA",moh:"moh-Latn-CA",mos:"mos-Latn-BF",mox:"mox-Latn-ZZ",mpp:"mpp-Latn-ZZ",mps:"mps-Latn-ZZ",mpt:"mpt-Latn-ZZ",mpx:"mpx-Latn-ZZ",mql:"mql-Latn-ZZ",mr:"mr-Deva-IN",mrd:"mrd-Deva-NP",mrj:"mrj-Cyrl-RU",mro:"mro-Mroo-BD",ms:"ms-Latn-MY","ms-CC":"ms-Arab-CC","ms-ID":"ms-Arab-ID",mt:"mt-Latn-MT",mtc:"mtc-Latn-ZZ",mtf:"mtf-Latn-ZZ",mti:"mti-Latn-ZZ",mtr:"mtr-Deva-IN",mua:"mua-Latn-CM",mur:"mur-Latn-ZZ",mus:"mus-Latn-US",mva:"mva-Latn-ZZ",mvn:"mvn-Latn-ZZ",mvy:"mvy-Arab-PK",mwk:"mwk-Latn-ML",mwr:"mwr-Deva-IN",mwv:"mwv-Latn-ID",mww:"mww-Hmnp-US",mxc:"mxc-Latn-ZW",mxm:"mxm-Latn-ZZ",my:"my-Mymr-MM",myk:"myk-Latn-ZZ",mym:"mym-Ethi-ZZ",myv:"myv-Cyrl-RU",myw:"myw-Latn-ZZ",myx:"myx-Latn-UG",myz:"myz-Mand-IR",mzk:"mzk-Latn-ZZ",mzm:"mzm-Latn-ZZ",mzn:"mzn-Arab-IR",mzp:"mzp-Latn-ZZ",mzw:"mzw-Latn-ZZ",mzz:"mzz-Latn-ZZ",na:"na-Latn-NR",nac:"nac-Latn-ZZ",naf:"naf-Latn-ZZ",nak:"nak-Latn-ZZ",nan:"nan-Hans-CN",nap:"nap-Latn-IT",naq:"naq-Latn-NA",nas:"nas-Latn-ZZ",nb:"nb-Latn-NO",nca:"nca-Latn-ZZ",nce:"nce-Latn-ZZ",ncf:"ncf-Latn-ZZ",nch:"nch-Latn-MX",nco:"nco-Latn-ZZ",ncu:"ncu-Latn-ZZ",nd:"nd-Latn-ZW",ndc:"ndc-Latn-MZ",nds:"nds-Latn-DE",ne:"ne-Deva-NP",neb:"neb-Latn-ZZ",new:"new-Deva-NP",nex:"nex-Latn-ZZ",nfr:"nfr-Latn-ZZ",ng:"ng-Latn-NA",nga:"nga-Latn-ZZ",ngb:"ngb-Latn-ZZ",ngl:"ngl-Latn-MZ",nhb:"nhb-Latn-ZZ",nhe:"nhe-Latn-MX",nhw:"nhw-Latn-MX",nif:"nif-Latn-ZZ",nii:"nii-Latn-ZZ",nij:"nij-Latn-ID",nin:"nin-Latn-ZZ",niu:"niu-Latn-NU",niy:"niy-Latn-ZZ",niz:"niz-Latn-ZZ",njo:"njo-Latn-IN",nkg:"nkg-Latn-ZZ",nko:"nko-Latn-ZZ",nl:"nl-Latn-NL",nmg:"nmg-Latn-CM",nmz:"nmz-Latn-ZZ",nn:"nn-Latn-NO",nnf:"nnf-Latn-ZZ",nnh:"nnh-Latn-CM",nnk:"nnk-Latn-ZZ",nnm:"nnm-Latn-ZZ",nnp:"nnp-Wcho-IN",no:"no-Latn-NO",nod:"nod-Lana-TH",noe:"noe-Deva-IN",non:"non-Runr-SE",nop:"nop-Latn-ZZ",nou:"nou-Latn-ZZ",nqo:"nqo-Nkoo-GN",nr:"nr-Latn-ZA",nrb:"nrb-Latn-ZZ",nsk:"nsk-Cans-CA",nsn:"nsn-Latn-ZZ",nso:"nso-Latn-ZA",nss:"nss-Latn-ZZ",ntm:"ntm-Latn-ZZ",ntr:"ntr-Latn-ZZ",nui:"nui-Latn-ZZ",nup:"nup-Latn-ZZ",nus:"nus-Latn-SS",nuv:"nuv-Latn-ZZ",nux:"nux-Latn-ZZ",nv:"nv-Latn-US",nwb:"nwb-Latn-ZZ",nxq:"nxq-Latn-CN",nxr:"nxr-Latn-ZZ",ny:"ny-Latn-MW",nym:"nym-Latn-TZ",nyn:"nyn-Latn-UG",nzi:"nzi-Latn-GH",oc:"oc-Latn-FR",ogc:"ogc-Latn-ZZ",okr:"okr-Latn-ZZ",okv:"okv-Latn-ZZ",om:"om-Latn-ET",ong:"ong-Latn-ZZ",onn:"onn-Latn-ZZ",ons:"ons-Latn-ZZ",opm:"opm-Latn-ZZ",or:"or-Orya-IN",oro:"oro-Latn-ZZ",oru:"oru-Arab-ZZ",os:"os-Cyrl-GE",osa:"osa-Osge-US",ota:"ota-Arab-ZZ",otk:"otk-Orkh-MN",ozm:"ozm-Latn-ZZ",pa:"pa-Guru-IN","pa-Arab":"pa-Arab-PK","pa-PK":"pa-Arab-PK",pag:"pag-Latn-PH",pal:"pal-Phli-IR","pal-Phlp":"pal-Phlp-CN",pam:"pam-Latn-PH",pap:"pap-Latn-AW",pau:"pau-Latn-PW",pbi:"pbi-Latn-ZZ",pcd:"pcd-Latn-FR",pcm:"pcm-Latn-NG",pdc:"pdc-Latn-US",pdt:"pdt-Latn-CA",ped:"ped-Latn-ZZ",peo:"peo-Xpeo-IR",pex:"pex-Latn-ZZ",pfl:"pfl-Latn-DE",phl:"phl-Arab-ZZ",phn:"phn-Phnx-LB",pil:"pil-Latn-ZZ",pip:"pip-Latn-ZZ",pka:"pka-Brah-IN",pko:"pko-Latn-KE",pl:"pl-Latn-PL",pla:"pla-Latn-ZZ",pms:"pms-Latn-IT",png:"png-Latn-ZZ",pnn:"pnn-Latn-ZZ",pnt:"pnt-Grek-GR",pon:"pon-Latn-FM",ppa:"ppa-Deva-IN",ppo:"ppo-Latn-ZZ",pra:"pra-Khar-PK",prd:"prd-Arab-IR",prg:"prg-Latn-001",ps:"ps-Arab-AF",pss:"pss-Latn-ZZ",pt:"pt-Latn-BR",ptp:"ptp-Latn-ZZ",puu:"puu-Latn-GA",pwa:"pwa-Latn-ZZ",qu:"qu-Latn-PE",quc:"quc-Latn-GT",qug:"qug-Latn-EC",rai:"rai-Latn-ZZ",raj:"raj-Deva-IN",rao:"rao-Latn-ZZ",rcf:"rcf-Latn-RE",rej:"rej-Latn-ID",rel:"rel-Latn-ZZ",res:"res-Latn-ZZ",rgn:"rgn-Latn-IT",rhg:"rhg-Arab-MM",ria:"ria-Latn-IN",rif:"rif-Tfng-MA","rif-NL":"rif-Latn-NL",rjs:"rjs-Deva-NP",rkt:"rkt-Beng-BD",rm:"rm-Latn-CH",rmf:"rmf-Latn-FI",rmo:"rmo-Latn-CH",rmt:"rmt-Arab-IR",rmu:"rmu-Latn-SE",rn:"rn-Latn-BI",rna:"rna-Latn-ZZ",rng:"rng-Latn-MZ",ro:"ro-Latn-RO",rob:"rob-Latn-ID",rof:"rof-Latn-TZ",roo:"roo-Latn-ZZ",rro:"rro-Latn-ZZ",rtm:"rtm-Latn-FJ",ru:"ru-Cyrl-RU",rue:"rue-Cyrl-UA",rug:"rug-Latn-SB",rw:"rw-Latn-RW",rwk:"rwk-Latn-TZ",rwo:"rwo-Latn-ZZ",ryu:"ryu-Kana-JP",sa:"sa-Deva-IN",saf:"saf-Latn-GH",sah:"sah-Cyrl-RU",saq:"saq-Latn-KE",sas:"sas-Latn-ID",sat:"sat-Latn-IN",sav:"sav-Latn-SN",saz:"saz-Saur-IN",sba:"sba-Latn-ZZ",sbe:"sbe-Latn-ZZ",sbp:"sbp-Latn-TZ",sc:"sc-Latn-IT",sck:"sck-Deva-IN",scl:"scl-Arab-ZZ",scn:"scn-Latn-IT",sco:"sco-Latn-GB",scs:"scs-Latn-CA",sd:"sd-Arab-PK","sd-Deva":"sd-Deva-IN","sd-Khoj":"sd-Khoj-IN","sd-Sind":"sd-Sind-IN",sdc:"sdc-Latn-IT",sdh:"sdh-Arab-IR",se:"se-Latn-NO",sef:"sef-Latn-CI",seh:"seh-Latn-MZ",sei:"sei-Latn-MX",ses:"ses-Latn-ML",sg:"sg-Latn-CF",sga:"sga-Ogam-IE",sgs:"sgs-Latn-LT",sgw:"sgw-Ethi-ZZ",sgz:"sgz-Latn-ZZ",shi:"shi-Tfng-MA",shk:"shk-Latn-ZZ",shn:"shn-Mymr-MM",shu:"shu-Arab-ZZ",si:"si-Sinh-LK",sid:"sid-Latn-ET",sig:"sig-Latn-ZZ",sil:"sil-Latn-ZZ",sim:"sim-Latn-ZZ",sjr:"sjr-Latn-ZZ",sk:"sk-Latn-SK",skc:"skc-Latn-ZZ",skr:"skr-Arab-PK",sks:"sks-Latn-ZZ",sl:"sl-Latn-SI",sld:"sld-Latn-ZZ",sli:"sli-Latn-PL",sll:"sll-Latn-ZZ",sly:"sly-Latn-ID",sm:"sm-Latn-WS",sma:"sma-Latn-SE",smj:"smj-Latn-SE",smn:"smn-Latn-FI",smp:"smp-Samr-IL",smq:"smq-Latn-ZZ",sms:"sms-Latn-FI",sn:"sn-Latn-ZW",snc:"snc-Latn-ZZ",snk:"snk-Latn-ML",snp:"snp-Latn-ZZ",snx:"snx-Latn-ZZ",sny:"sny-Latn-ZZ",so:"so-Latn-SO",sog:"sog-Sogd-UZ",sok:"sok-Latn-ZZ",soq:"soq-Latn-ZZ",sou:"sou-Thai-TH",soy:"soy-Latn-ZZ",spd:"spd-Latn-ZZ",spl:"spl-Latn-ZZ",sps:"sps-Latn-ZZ",sq:"sq-Latn-AL",sr:"sr-Cyrl-RS","sr-ME":"sr-Latn-ME","sr-RO":"sr-Latn-RO","sr-RU":"sr-Latn-RU","sr-TR":"sr-Latn-TR",srb:"srb-Sora-IN",srn:"srn-Latn-SR",srr:"srr-Latn-SN",srx:"srx-Deva-IN",ss:"ss-Latn-ZA",ssd:"ssd-Latn-ZZ",ssg:"ssg-Latn-ZZ",ssy:"ssy-Latn-ER",st:"st-Latn-ZA",stk:"stk-Latn-ZZ",stq:"stq-Latn-DE",su:"su-Latn-ID",sua:"sua-Latn-ZZ",sue:"sue-Latn-ZZ",suk:"suk-Latn-TZ",sur:"sur-Latn-ZZ",sus:"sus-Latn-GN",sv:"sv-Latn-SE",sw:"sw-Latn-TZ",swb:"swb-Arab-YT",swc:"swc-Latn-CD",swg:"swg-Latn-DE",swp:"swp-Latn-ZZ",swv:"swv-Deva-IN",sxn:"sxn-Latn-ID",sxw:"sxw-Latn-ZZ",syl:"syl-Beng-BD",syr:"syr-Syrc-IQ",szl:"szl-Latn-PL",ta:"ta-Taml-IN",taj:"taj-Deva-NP",tal:"tal-Latn-ZZ",tan:"tan-Latn-ZZ",taq:"taq-Latn-ZZ",tbc:"tbc-Latn-ZZ",tbd:"tbd-Latn-ZZ",tbf:"tbf-Latn-ZZ",tbg:"tbg-Latn-ZZ",tbo:"tbo-Latn-ZZ",tbw:"tbw-Latn-PH",tbz:"tbz-Latn-ZZ",tci:"tci-Latn-ZZ",tcy:"tcy-Knda-IN",tdd:"tdd-Tale-CN",tdg:"tdg-Deva-NP",tdh:"tdh-Deva-NP",tdu:"tdu-Latn-MY",te:"te-Telu-IN",ted:"ted-Latn-ZZ",tem:"tem-Latn-SL",teo:"teo-Latn-UG",tet:"tet-Latn-TL",tfi:"tfi-Latn-ZZ",tg:"tg-Cyrl-TJ","tg-Arab":"tg-Arab-PK","tg-PK":"tg-Arab-PK",tgc:"tgc-Latn-ZZ",tgo:"tgo-Latn-ZZ",tgu:"tgu-Latn-ZZ",th:"th-Thai-TH",thl:"thl-Deva-NP",thq:"thq-Deva-NP",thr:"thr-Deva-NP",ti:"ti-Ethi-ET",tif:"tif-Latn-ZZ",tig:"tig-Ethi-ER",tik:"tik-Latn-ZZ",tim:"tim-Latn-ZZ",tio:"tio-Latn-ZZ",tiv:"tiv-Latn-NG",tk:"tk-Latn-TM",tkl:"tkl-Latn-TK",tkr:"tkr-Latn-AZ",tkt:"tkt-Deva-NP",tl:"tl-Latn-PH",tlf:"tlf-Latn-ZZ",tlx:"tlx-Latn-ZZ",tly:"tly-Latn-AZ",tmh:"tmh-Latn-NE",tmy:"tmy-Latn-ZZ",tn:"tn-Latn-ZA",tnh:"tnh-Latn-ZZ",to:"to-Latn-TO",tof:"tof-Latn-ZZ",tog:"tog-Latn-MW",toq:"toq-Latn-ZZ",tpi:"tpi-Latn-PG",tpm:"tpm-Latn-ZZ",tpz:"tpz-Latn-ZZ",tqo:"tqo-Latn-ZZ",tr:"tr-Latn-TR",tru:"tru-Latn-TR",trv:"trv-Latn-TW",trw:"trw-Arab-ZZ",ts:"ts-Latn-ZA",tsd:"tsd-Grek-GR",tsf:"tsf-Deva-NP",tsg:"tsg-Latn-PH",tsj:"tsj-Tibt-BT",tsw:"tsw-Latn-ZZ",tt:"tt-Cyrl-RU",ttd:"ttd-Latn-ZZ",tte:"tte-Latn-ZZ",ttj:"ttj-Latn-UG",ttr:"ttr-Latn-ZZ",tts:"tts-Thai-TH",ttt:"ttt-Latn-AZ",tuh:"tuh-Latn-ZZ",tul:"tul-Latn-ZZ",tum:"tum-Latn-MW",tuq:"tuq-Latn-ZZ",tvd:"tvd-Latn-ZZ",tvl:"tvl-Latn-TV",tvu:"tvu-Latn-ZZ",twh:"twh-Latn-ZZ",twq:"twq-Latn-NE",txg:"txg-Tang-CN",ty:"ty-Latn-PF",tya:"tya-Latn-ZZ",tyv:"tyv-Cyrl-RU",tzm:"tzm-Latn-MA",ubu:"ubu-Latn-ZZ",udm:"udm-Cyrl-RU",ug:"ug-Arab-CN","ug-Cyrl":"ug-Cyrl-KZ","ug-KZ":"ug-Cyrl-KZ","ug-MN":"ug-Cyrl-MN",uga:"uga-Ugar-SY",uk:"uk-Cyrl-UA",uli:"uli-Latn-FM",umb:"umb-Latn-AO",und:"en-Latn-US","und-002":"en-Latn-NG","und-003":"en-Latn-US","und-005":"pt-Latn-BR","und-009":"en-Latn-AU","und-011":"en-Latn-NG","und-013":"es-Latn-MX","und-014":"sw-Latn-TZ","und-015":"ar-Arab-EG","und-017":"sw-Latn-CD","und-018":"en-Latn-ZA","und-019":"en-Latn-US","und-021":"en-Latn-US","und-029":"es-Latn-CU","und-030":"zh-Hans-CN","und-034":"hi-Deva-IN","und-035":"id-Latn-ID","und-039":"it-Latn-IT","und-053":"en-Latn-AU","und-054":"en-Latn-PG","und-057":"en-Latn-GU","und-061":"sm-Latn-WS","und-142":"zh-Hans-CN","und-143":"uz-Latn-UZ","und-145":"ar-Arab-SA","und-150":"ru-Cyrl-RU","und-151":"ru-Cyrl-RU","und-154":"en-Latn-GB","und-155":"de-Latn-DE","und-202":"en-Latn-NG","und-419":"es-Latn-419","und-AD":"ca-Latn-AD","und-Adlm":"ff-Adlm-GN","und-AE":"ar-Arab-AE","und-AF":"fa-Arab-AF","und-Aghb":"lez-Aghb-RU","und-Ahom":"aho-Ahom-IN","und-AL":"sq-Latn-AL","und-AM":"hy-Armn-AM","und-AO":"pt-Latn-AO","und-AQ":"und-Latn-AQ","und-AR":"es-Latn-AR","und-Arab":"ar-Arab-EG","und-Arab-CC":"ms-Arab-CC","und-Arab-CN":"ug-Arab-CN","und-Arab-GB":"ks-Arab-GB","und-Arab-ID":"ms-Arab-ID","und-Arab-IN":"ur-Arab-IN","und-Arab-KH":"cja-Arab-KH","und-Arab-MM":"rhg-Arab-MM","und-Arab-MN":"kk-Arab-MN","und-Arab-MU":"ur-Arab-MU","und-Arab-NG":"ha-Arab-NG","und-Arab-PK":"ur-Arab-PK","und-Arab-TG":"apd-Arab-TG","und-Arab-TH":"mfa-Arab-TH","und-Arab-TJ":"fa-Arab-TJ","und-Arab-TR":"az-Arab-TR","und-Arab-YT":"swb-Arab-YT","und-Armi":"arc-Armi-IR","und-Armn":"hy-Armn-AM","und-AS":"sm-Latn-AS","und-AT":"de-Latn-AT","und-Avst":"ae-Avst-IR","und-AW":"nl-Latn-AW","und-AX":"sv-Latn-AX","und-AZ":"az-Latn-AZ","und-BA":"bs-Latn-BA","und-Bali":"ban-Bali-ID","und-Bamu":"bax-Bamu-CM","und-Bass":"bsq-Bass-LR","und-Batk":"bbc-Batk-ID","und-BD":"bn-Beng-BD","und-BE":"nl-Latn-BE","und-Beng":"bn-Beng-BD","und-BF":"fr-Latn-BF","und-BG":"bg-Cyrl-BG","und-BH":"ar-Arab-BH","und-Bhks":"sa-Bhks-IN","und-BI":"rn-Latn-BI","und-BJ":"fr-Latn-BJ","und-BL":"fr-Latn-BL","und-BN":"ms-Latn-BN","und-BO":"es-Latn-BO","und-Bopo":"zh-Bopo-TW","und-BQ":"pap-Latn-BQ","und-BR":"pt-Latn-BR","und-Brah":"pka-Brah-IN","und-Brai":"fr-Brai-FR","und-BT":"dz-Tibt-BT","und-Bugi":"bug-Bugi-ID","und-Buhd":"bku-Buhd-PH","und-BV":"und-Latn-BV","und-BY":"be-Cyrl-BY","und-Cakm":"ccp-Cakm-BD","und-Cans":"cr-Cans-CA","und-Cari":"xcr-Cari-TR","und-CD":"sw-Latn-CD","und-CF":"fr-Latn-CF","und-CG":"fr-Latn-CG","und-CH":"de-Latn-CH","und-Cham":"cjm-Cham-VN","und-Cher":"chr-Cher-US","und-CI":"fr-Latn-CI","und-CL":"es-Latn-CL","und-CM":"fr-Latn-CM","und-CN":"zh-Hans-CN","und-CO":"es-Latn-CO","und-Copt":"cop-Copt-EG","und-CP":"und-Latn-CP","und-Cprt":"grc-Cprt-CY","und-CR":"es-Latn-CR","und-CU":"es-Latn-CU","und-CV":"pt-Latn-CV","und-CW":"pap-Latn-CW","und-CY":"el-Grek-CY","und-Cyrl":"ru-Cyrl-RU","und-Cyrl-AL":"mk-Cyrl-AL","und-Cyrl-BA":"sr-Cyrl-BA","und-Cyrl-GE":"ab-Cyrl-GE","und-Cyrl-GR":"mk-Cyrl-GR","und-Cyrl-MD":"uk-Cyrl-MD","und-Cyrl-RO":"bg-Cyrl-RO","und-Cyrl-SK":"uk-Cyrl-SK","und-Cyrl-TR":"kbd-Cyrl-TR","und-Cyrl-XK":"sr-Cyrl-XK","und-CZ":"cs-Latn-CZ","und-DE":"de-Latn-DE","und-Deva":"hi-Deva-IN","und-Deva-BT":"ne-Deva-BT","und-Deva-FJ":"hif-Deva-FJ","und-Deva-MU":"bho-Deva-MU","und-Deva-PK":"btv-Deva-PK","und-DJ":"aa-Latn-DJ","und-DK":"da-Latn-DK","und-DO":"es-Latn-DO","und-Dogr":"doi-Dogr-IN","und-Dupl":"fr-Dupl-FR","und-DZ":"ar-Arab-DZ","und-EA":"es-Latn-EA","und-EC":"es-Latn-EC","und-EE":"et-Latn-EE","und-EG":"ar-Arab-EG","und-Egyp":"egy-Egyp-EG","und-EH":"ar-Arab-EH","und-Elba":"sq-Elba-AL","und-Elym":"arc-Elym-IR","und-ER":"ti-Ethi-ER","und-ES":"es-Latn-ES","und-ET":"am-Ethi-ET","und-Ethi":"am-Ethi-ET","und-EU":"en-Latn-GB","und-EZ":"de-Latn-EZ","und-FI":"fi-Latn-FI","und-FO":"fo-Latn-FO","und-FR":"fr-Latn-FR","und-GA":"fr-Latn-GA","und-GE":"ka-Geor-GE","und-Geor":"ka-Geor-GE","und-GF":"fr-Latn-GF","und-GH":"ak-Latn-GH","und-GL":"kl-Latn-GL","und-Glag":"cu-Glag-BG","und-GN":"fr-Latn-GN","und-Gong":"wsg-Gong-IN","und-Gonm":"esg-Gonm-IN","und-Goth":"got-Goth-UA","und-GP":"fr-Latn-GP","und-GQ":"es-Latn-GQ","und-GR":"el-Grek-GR","und-Gran":"sa-Gran-IN","und-Grek":"el-Grek-GR","und-Grek-TR":"bgx-Grek-TR","und-GS":"und-Latn-GS","und-GT":"es-Latn-GT","und-Gujr":"gu-Gujr-IN","und-Guru":"pa-Guru-IN","und-GW":"pt-Latn-GW","und-Hanb":"zh-Hanb-TW","und-Hang":"ko-Hang-KR","und-Hani":"zh-Hani-CN","und-Hano":"hnn-Hano-PH","und-Hans":"zh-Hans-CN","und-Hant":"zh-Hant-TW","und-Hatr":"mis-Hatr-IQ","und-Hebr":"he-Hebr-IL","und-Hebr-CA":"yi-Hebr-CA","und-Hebr-GB":"yi-Hebr-GB","und-Hebr-SE":"yi-Hebr-SE","und-Hebr-UA":"yi-Hebr-UA","und-Hebr-US":"yi-Hebr-US","und-Hira":"ja-Hira-JP","und-HK":"zh-Hant-HK","und-Hluw":"hlu-Hluw-TR","und-HM":"und-Latn-HM","und-Hmng":"hnj-Hmng-LA","und-Hmnp":"mww-Hmnp-US","und-HN":"es-Latn-HN","und-HR":"hr-Latn-HR","und-HT":"ht-Latn-HT","und-HU":"hu-Latn-HU","und-Hung":"hu-Hung-HU","und-IC":"es-Latn-IC","und-ID":"id-Latn-ID","und-IL":"he-Hebr-IL","und-IN":"hi-Deva-IN","und-IQ":"ar-Arab-IQ","und-IR":"fa-Arab-IR","und-IS":"is-Latn-IS","und-IT":"it-Latn-IT","und-Ital":"ett-Ital-IT","und-Jamo":"ko-Jamo-KR","und-Java":"jv-Java-ID","und-JO":"ar-Arab-JO","und-JP":"ja-Jpan-JP","und-Jpan":"ja-Jpan-JP","und-Kali":"eky-Kali-MM","und-Kana":"ja-Kana-JP","und-KE":"sw-Latn-KE","und-KG":"ky-Cyrl-KG","und-KH":"km-Khmr-KH","und-Khar":"pra-Khar-PK","und-Khmr":"km-Khmr-KH","und-Khoj":"sd-Khoj-IN","und-KM":"ar-Arab-KM","und-Knda":"kn-Knda-IN","und-Kore":"ko-Kore-KR","und-KP":"ko-Kore-KP","und-KR":"ko-Kore-KR","und-Kthi":"bho-Kthi-IN","und-KW":"ar-Arab-KW","und-KZ":"ru-Cyrl-KZ","und-LA":"lo-Laoo-LA","und-Lana":"nod-Lana-TH","und-Laoo":"lo-Laoo-LA","und-Latn-AF":"tk-Latn-AF","und-Latn-AM":"ku-Latn-AM","und-Latn-CN":"za-Latn-CN","und-Latn-CY":"tr-Latn-CY","und-Latn-DZ":"fr-Latn-DZ","und-Latn-ET":"en-Latn-ET","und-Latn-GE":"ku-Latn-GE","und-Latn-IR":"tk-Latn-IR","und-Latn-KM":"fr-Latn-KM","und-Latn-MA":"fr-Latn-MA","und-Latn-MK":"sq-Latn-MK","und-Latn-MM":"kac-Latn-MM","und-Latn-MO":"pt-Latn-MO","und-Latn-MR":"fr-Latn-MR","und-Latn-RU":"krl-Latn-RU","und-Latn-SY":"fr-Latn-SY","und-Latn-TN":"fr-Latn-TN","und-Latn-TW":"trv-Latn-TW","und-Latn-UA":"pl-Latn-UA","und-LB":"ar-Arab-LB","und-Lepc":"lep-Lepc-IN","und-LI":"de-Latn-LI","und-Limb":"lif-Limb-IN","und-Lina":"lab-Lina-GR","und-Linb":"grc-Linb-GR","und-Lisu":"lis-Lisu-CN","und-LK":"si-Sinh-LK","und-LS":"st-Latn-LS","und-LT":"lt-Latn-LT","und-LU":"fr-Latn-LU","und-LV":"lv-Latn-LV","und-LY":"ar-Arab-LY","und-Lyci":"xlc-Lyci-TR","und-Lydi":"xld-Lydi-TR","und-MA":"ar-Arab-MA","und-Mahj":"hi-Mahj-IN","und-Maka":"mak-Maka-ID","und-Mand":"myz-Mand-IR","und-Mani":"xmn-Mani-CN","und-Marc":"bo-Marc-CN","und-MC":"fr-Latn-MC","und-MD":"ro-Latn-MD","und-ME":"sr-Latn-ME","und-Medf":"mis-Medf-NG","und-Mend":"men-Mend-SL","und-Merc":"xmr-Merc-SD","und-Mero":"xmr-Mero-SD","und-MF":"fr-Latn-MF","und-MG":"mg-Latn-MG","und-MK":"mk-Cyrl-MK","und-ML":"bm-Latn-ML","und-Mlym":"ml-Mlym-IN","und-MM":"my-Mymr-MM","und-MN":"mn-Cyrl-MN","und-MO":"zh-Hant-MO","und-Modi":"mr-Modi-IN","und-Mong":"mn-Mong-CN","und-MQ":"fr-Latn-MQ","und-MR":"ar-Arab-MR","und-Mroo":"mro-Mroo-BD","und-MT":"mt-Latn-MT","und-Mtei":"mni-Mtei-IN","und-MU":"mfe-Latn-MU","und-Mult":"skr-Mult-PK","und-MV":"dv-Thaa-MV","und-MX":"es-Latn-MX","und-MY":"ms-Latn-MY","und-Mymr":"my-Mymr-MM","und-Mymr-IN":"kht-Mymr-IN","und-Mymr-TH":"mnw-Mymr-TH","und-MZ":"pt-Latn-MZ","und-NA":"af-Latn-NA","und-Nand":"sa-Nand-IN","und-Narb":"xna-Narb-SA","und-Nbat":"arc-Nbat-JO","und-NC":"fr-Latn-NC","und-NE":"ha-Latn-NE","und-Newa":"new-Newa-NP","und-NI":"es-Latn-NI","und-Nkoo":"man-Nkoo-GN","und-NL":"nl-Latn-NL","und-NO":"nb-Latn-NO","und-NP":"ne-Deva-NP","und-Nshu":"zhx-Nshu-CN","und-Ogam":"sga-Ogam-IE","und-Olck":"sat-Olck-IN","und-OM":"ar-Arab-OM","und-Orkh":"otk-Orkh-MN","und-Orya":"or-Orya-IN","und-Osge":"osa-Osge-US","und-Osma":"so-Osma-SO","und-PA":"es-Latn-PA","und-Palm":"arc-Palm-SY","und-Pauc":"ctd-Pauc-MM","und-PE":"es-Latn-PE","und-Perm":"kv-Perm-RU","und-PF":"fr-Latn-PF","und-PG":"tpi-Latn-PG","und-PH":"fil-Latn-PH","und-Phag":"lzh-Phag-CN","und-Phli":"pal-Phli-IR","und-Phlp":"pal-Phlp-CN","und-Phnx":"phn-Phnx-LB","und-PK":"ur-Arab-PK","und-PL":"pl-Latn-PL","und-Plrd":"hmd-Plrd-CN","und-PM":"fr-Latn-PM","und-PR":"es-Latn-PR","und-Prti":"xpr-Prti-IR","und-PS":"ar-Arab-PS","und-PT":"pt-Latn-PT","und-PW":"pau-Latn-PW","und-PY":"gn-Latn-PY","und-QA":"ar-Arab-QA","und-QO":"en-Latn-DG","und-RE":"fr-Latn-RE","und-Rjng":"rej-Rjng-ID","und-RO":"ro-Latn-RO","und-Rohg":"rhg-Rohg-MM","und-RS":"sr-Cyrl-RS","und-RU":"ru-Cyrl-RU","und-Runr":"non-Runr-SE","und-RW":"rw-Latn-RW","und-SA":"ar-Arab-SA","und-Samr":"smp-Samr-IL","und-Sarb":"xsa-Sarb-YE","und-Saur":"saz-Saur-IN","und-SC":"fr-Latn-SC","und-SD":"ar-Arab-SD","und-SE":"sv-Latn-SE","und-Sgnw":"ase-Sgnw-US","und-Shaw":"en-Shaw-GB","und-Shrd":"sa-Shrd-IN","und-SI":"sl-Latn-SI","und-Sidd":"sa-Sidd-IN","und-Sind":"sd-Sind-IN","und-Sinh":"si-Sinh-LK","und-SJ":"nb-Latn-SJ","und-SK":"sk-Latn-SK","und-SM":"it-Latn-SM","und-SN":"fr-Latn-SN","und-SO":"so-Latn-SO","und-Sogd":"sog-Sogd-UZ","und-Sogo":"sog-Sogo-UZ","und-Sora":"srb-Sora-IN","und-Soyo":"cmg-Soyo-MN","und-SR":"nl-Latn-SR","und-ST":"pt-Latn-ST","und-Sund":"su-Sund-ID","und-SV":"es-Latn-SV","und-SY":"ar-Arab-SY","und-Sylo":"syl-Sylo-BD","und-Syrc":"syr-Syrc-IQ","und-Tagb":"tbw-Tagb-PH","und-Takr":"doi-Takr-IN","und-Tale":"tdd-Tale-CN","und-Talu":"khb-Talu-CN","und-Taml":"ta-Taml-IN","und-Tang":"txg-Tang-CN","und-Tavt":"blt-Tavt-VN","und-TD":"fr-Latn-TD","und-Telu":"te-Telu-IN","und-TF":"fr-Latn-TF","und-Tfng":"zgh-Tfng-MA","und-TG":"fr-Latn-TG","und-Tglg":"fil-Tglg-PH","und-TH":"th-Thai-TH","und-Thaa":"dv-Thaa-MV","und-Thai":"th-Thai-TH","und-Thai-CN":"lcp-Thai-CN","und-Thai-KH":"kdt-Thai-KH","und-Thai-LA":"kdt-Thai-LA","und-Tibt":"bo-Tibt-CN","und-Tirh":"mai-Tirh-IN","und-TJ":"tg-Cyrl-TJ","und-TK":"tkl-Latn-TK","und-TL":"pt-Latn-TL","und-TM":"tk-Latn-TM","und-TN":"ar-Arab-TN","und-TO":"to-Latn-TO","und-TR":"tr-Latn-TR","und-TV":"tvl-Latn-TV","und-TW":"zh-Hant-TW","und-TZ":"sw-Latn-TZ","und-UA":"uk-Cyrl-UA","und-UG":"sw-Latn-UG","und-Ugar":"uga-Ugar-SY","und-UY":"es-Latn-UY","und-UZ":"uz-Latn-UZ","und-VA":"it-Latn-VA","und-Vaii":"vai-Vaii-LR","und-VE":"es-Latn-VE","und-VN":"vi-Latn-VN","und-VU":"bi-Latn-VU","und-Wara":"hoc-Wara-IN","und-Wcho":"nnp-Wcho-IN","und-WF":"fr-Latn-WF","und-WS":"sm-Latn-WS","und-XK":"sq-Latn-XK","und-Xpeo":"peo-Xpeo-IR","und-Xsux":"akk-Xsux-IQ","und-YE":"ar-Arab-YE","und-Yiii":"ii-Yiii-CN","und-YT":"fr-Latn-YT","und-Zanb":"cmg-Zanb-MN","und-ZW":"sn-Latn-ZW",unr:"unr-Beng-IN","unr-Deva":"unr-Deva-NP","unr-NP":"unr-Deva-NP",unx:"unx-Beng-IN",uok:"uok-Latn-ZZ",ur:"ur-Arab-PK",uri:"uri-Latn-ZZ",urt:"urt-Latn-ZZ",urw:"urw-Latn-ZZ",usa:"usa-Latn-ZZ",utr:"utr-Latn-ZZ",uvh:"uvh-Latn-ZZ",uvl:"uvl-Latn-ZZ",uz:"uz-Latn-UZ","uz-AF":"uz-Arab-AF","uz-Arab":"uz-Arab-AF","uz-CN":"uz-Cyrl-CN",vag:"vag-Latn-ZZ",vai:"vai-Vaii-LR",van:"van-Latn-ZZ",ve:"ve-Latn-ZA",vec:"vec-Latn-IT",vep:"vep-Latn-RU",vi:"vi-Latn-VN",vic:"vic-Latn-SX",viv:"viv-Latn-ZZ",vls:"vls-Latn-BE",vmf:"vmf-Latn-DE",vmw:"vmw-Latn-MZ",vo:"vo-Latn-001",vot:"vot-Latn-RU",vro:"vro-Latn-EE",vun:"vun-Latn-TZ",vut:"vut-Latn-ZZ",wa:"wa-Latn-BE",wae:"wae-Latn-CH",waj:"waj-Latn-ZZ",wal:"wal-Ethi-ET",wan:"wan-Latn-ZZ",war:"war-Latn-PH",wbp:"wbp-Latn-AU",wbq:"wbq-Telu-IN",wbr:"wbr-Deva-IN",wci:"wci-Latn-ZZ",wer:"wer-Latn-ZZ",wgi:"wgi-Latn-ZZ",whg:"whg-Latn-ZZ",wib:"wib-Latn-ZZ",wiu:"wiu-Latn-ZZ",wiv:"wiv-Latn-ZZ",wja:"wja-Latn-ZZ",wji:"wji-Latn-ZZ",wls:"wls-Latn-WF",wmo:"wmo-Latn-ZZ",wnc:"wnc-Latn-ZZ",wni:"wni-Arab-KM",wnu:"wnu-Latn-ZZ",wo:"wo-Latn-SN",wob:"wob-Latn-ZZ",wos:"wos-Latn-ZZ",wrs:"wrs-Latn-ZZ",wsg:"wsg-Gong-IN",wsk:"wsk-Latn-ZZ",wtm:"wtm-Deva-IN",wuu:"wuu-Hans-CN",wuv:"wuv-Latn-ZZ",wwa:"wwa-Latn-ZZ",xav:"xav-Latn-BR",xbi:"xbi-Latn-ZZ",xcr:"xcr-Cari-TR",xes:"xes-Latn-ZZ",xh:"xh-Latn-ZA",xla:"xla-Latn-ZZ",xlc:"xlc-Lyci-TR",xld:"xld-Lydi-TR",xmf:"xmf-Geor-GE",xmn:"xmn-Mani-CN",xmr:"xmr-Merc-SD",xna:"xna-Narb-SA",xnr:"xnr-Deva-IN",xog:"xog-Latn-UG",xon:"xon-Latn-ZZ",xpr:"xpr-Prti-IR",xrb:"xrb-Latn-ZZ",xsa:"xsa-Sarb-YE",xsi:"xsi-Latn-ZZ",xsm:"xsm-Latn-ZZ",xsr:"xsr-Deva-NP",xwe:"xwe-Latn-ZZ",yam:"yam-Latn-ZZ",yao:"yao-Latn-MZ",yap:"yap-Latn-FM",yas:"yas-Latn-ZZ",yat:"yat-Latn-ZZ",yav:"yav-Latn-CM",yay:"yay-Latn-ZZ",yaz:"yaz-Latn-ZZ",yba:"yba-Latn-ZZ",ybb:"ybb-Latn-CM",yby:"yby-Latn-ZZ",yer:"yer-Latn-ZZ",ygr:"ygr-Latn-ZZ",ygw:"ygw-Latn-ZZ",yi:"yi-Hebr-001",yko:"yko-Latn-ZZ",yle:"yle-Latn-ZZ",ylg:"ylg-Latn-ZZ",yll:"yll-Latn-ZZ",yml:"yml-Latn-ZZ",yo:"yo-Latn-NG",yon:"yon-Latn-ZZ",yrb:"yrb-Latn-ZZ",yre:"yre-Latn-ZZ",yrl:"yrl-Latn-BR",yss:"yss-Latn-ZZ",yua:"yua-Latn-MX",yue:"yue-Hant-HK","yue-CN":"yue-Hans-CN","yue-Hans":"yue-Hans-CN",yuj:"yuj-Latn-ZZ",yut:"yut-Latn-ZZ",yuw:"yuw-Latn-ZZ",za:"za-Latn-CN",zag:"zag-Latn-SD",zdj:"zdj-Arab-KM",zea:"zea-Latn-NL",zgh:"zgh-Tfng-MA",zh:"zh-Hans-CN","zh-AU":"zh-Hant-AU","zh-BN":"zh-Hant-BN","zh-Bopo":"zh-Bopo-TW","zh-GB":"zh-Hant-GB","zh-GF":"zh-Hant-GF","zh-Hanb":"zh-Hanb-TW","zh-Hant":"zh-Hant-TW","zh-HK":"zh-Hant-HK","zh-ID":"zh-Hant-ID","zh-MO":"zh-Hant-MO","zh-MY":"zh-Hant-MY","zh-PA":"zh-Hant-PA","zh-PF":"zh-Hant-PF","zh-PH":"zh-Hant-PH","zh-SR":"zh-Hant-SR","zh-TH":"zh-Hant-TH","zh-TW":"zh-Hant-TW","zh-US":"zh-Hant-US","zh-VN":"zh-Hant-VN",zhx:"zhx-Nshu-CN",zia:"zia-Latn-ZZ",zlm:"zlm-Latn-TG",zmi:"zmi-Latn-MY",zne:"zne-Latn-ZZ",zu:"zu-Latn-ZA",zza:"zza-Latn-TR"}}},{main:{af:{identity:{version:{_cldrVersion:"36"},language:"af"},numbers:{defaultNumberingSystem:"latn",otherNumberingSystems:{native:"latn"},minimumGroupingDigits:"1","symbols-numberSystem-latn":{decimal:".",group:" ",list:";",percentSign:"%",plusSign:"+",minusSign:"-",exponential:"E",superscriptingExponent:"×",perMille:"‰",infinity:"∞",nan:"NaN",timeSeparator:":"},"decimalFormats-numberSystem-latn":{standard:"#,##0.###",long:{decimalFormat:{"1000-count-one":"0 duisend","1000-count-other":"0 duisend","10000-count-one":"00 duisend","10000-count-other":"00 duisend","100000-count-one":"000 duisend","100000-count-other":"000 duisend","1000000-count-one":"0 miljoen","1000000-count-other":"0 miljoen","********-count-one":"00 miljoen","********-count-other":"00 miljoen","********0-count-one":"000 miljoen","********0-count-other":"000 miljoen","********00-count-one":"0 miljard","********00-count-other":"0 miljard","1**********-count-one":"00 miljard","1**********-count-other":"00 miljard","1**********0-count-one":"000 miljard","1**********0-count-other":"000 miljard","1**********00-count-one":"0 biljoen","1**********00-count-other":"0 biljoen","1**********000-count-one":"00 biljoen","1**********000-count-other":"00 biljoen","***************-count-one":"000 biljoen","***************-count-other":"000 biljoen"}},short:{decimalFormat:{"1000-count-one":"0 k","1000-count-other":"0 k","10000-count-one":"00 k","10000-count-other":"00 k","100000-count-one":"000 k","100000-count-other":"000 k","1000000-count-one":"0 m","1000000-count-other":"0 m","********-count-one":"00 m","********-count-other":"00 m","********0-count-one":"000 m","********0-count-other":"000 m","********00-count-one":"0 mjd","********00-count-other":"0 mjd","1**********-count-one":"00 mjd","1**********-count-other":"00 mjd","1**********0-count-one":"000 mjd","1**********0-count-other":"000 mjd","1**********00-count-one":"0 bn","1**********00-count-other":"0 bn","1**********000-count-one":"00 bn","1**********000-count-other":"00 bn","***************-count-one":"000 bn","***************-count-other":"000 bn"}}},"scientificFormats-numberSystem-latn":{standard:"#E0"},"percentFormats-numberSystem-latn":{standard:"#,##0%"},"currencyFormats-numberSystem-latn":{currencySpacing:{beforeCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "},afterCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "}},standard:"¤#,##0.00",accounting:"¤#,##0.00;(¤#,##0.00)",short:{standard:{"1000-count-one":"¤0 k","1000-count-other":"¤0 k","10000-count-one":"¤00 k","10000-count-other":"¤00 k","100000-count-one":"¤000 k","100000-count-other":"¤000 k","1000000-count-one":"¤0 m","1000000-count-other":"¤0 m","********-count-one":"¤00 m","********-count-other":"¤00 m","********0-count-one":"¤000 m","********0-count-other":"¤000 m","********00-count-one":"¤0 mjd","********00-count-other":"¤0 mjd","1**********-count-one":"¤00 mjd","1**********-count-other":"¤00 mjd","1**********0-count-one":"¤000 mjd","1**********0-count-other":"¤000 mjd","1**********00-count-one":"¤0 bn","1**********00-count-other":"¤0 bn","1**********000-count-one":"¤00 bn","1**********000-count-other":"¤00 bn","***************-count-one":"¤000 bn","***************-count-other":"¤000 bn"}},"unitPattern-count-one":"{0} {1}","unitPattern-count-other":"{0} {1}"},"miscPatterns-numberSystem-latn":{approximately:"~{0}",atLeast:"{0}+",atMost:"≤{0}",range:"{0}–{1}"},minimalPairs:{"pluralMinimalPairs-count-one":"{0} dag","pluralMinimalPairs-count-other":"{0} dae",other:"Neem die {0}e afdraai na regs."}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},numberingSystems:{adlm:{_digits:"𞥐𞥑𞥒𞥓𞥔𞥕𞥖𞥗𞥘𞥙",_type:"numeric"},ahom:{_digits:"𑜰𑜱𑜲𑜳𑜴𑜵𑜶𑜷𑜸𑜹",_type:"numeric"},arab:{_digits:"٠١٢٣٤٥٦٧٨٩",_type:"numeric"},arabext:{_digits:"۰۱۲۳۴۵۶۷۸۹",_type:"numeric"},armn:{_rules:"armenian-upper",_type:"algorithmic"},armnlow:{_rules:"armenian-lower",_type:"algorithmic"},bali:{_digits:"᭐᭑᭒᭓᭔᭕᭖᭗᭘᭙",_type:"numeric"},beng:{_digits:"০১২৩৪৫৬৭৮৯",_type:"numeric"},bhks:{_digits:"𑱐𑱑𑱒𑱓𑱔𑱕𑱖𑱗𑱘𑱙",_type:"numeric"},brah:{_digits:"𑁦𑁧𑁨𑁩𑁪𑁫𑁬𑁭𑁮𑁯",_type:"numeric"},cakm:{_digits:"𑄶𑄷𑄸𑄹𑄺𑄻𑄼𑄽𑄾𑄿",_type:"numeric"},cham:{_digits:"꩐꩑꩒꩓꩔꩕꩖꩗꩘꩙",_type:"numeric"},cyrl:{_rules:"cyrillic-lower",_type:"algorithmic"},deva:{_digits:"०१२३४५६७८९",_type:"numeric"},ethi:{_rules:"ethiopic",_type:"algorithmic"},fullwide:{_digits:"０１２３４５６７８９",_type:"numeric"},geor:{_rules:"georgian",_type:"algorithmic"},gong:{_digits:"𑶠𑶡𑶢𑶣𑶤𑶥𑶦𑶧𑶨𑶩",_type:"numeric"},gonm:{_digits:"𑵐𑵑𑵒𑵓𑵔𑵕𑵖𑵗𑵘𑵙",_type:"numeric"},grek:{_rules:"greek-upper",_type:"algorithmic"},greklow:{_rules:"greek-lower",_type:"algorithmic"},gujr:{_digits:"૦૧૨૩૪૫૬૭૮૯",_type:"numeric"},guru:{_digits:"੦੧੨੩੪੫੬੭੮੯",_type:"numeric"},hanidays:{_rules:"zh/SpelloutRules/spellout-numbering-days",_type:"algorithmic"},hanidec:{_digits:"〇一二三四五六七八九",_type:"numeric"},hans:{_rules:"zh/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hansfin:{_rules:"zh/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hant:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hantfin:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hebr:{_rules:"hebrew",_type:"algorithmic"},hmng:{_digits:"𖭐𖭑𖭒𖭓𖭔𖭕𖭖𖭗𖭘𖭙",_type:"numeric"},hmnp:{_digits:"𞅀𞅁𞅂𞅃𞅄𞅅𞅆𞅇𞅈𞅉",_type:"numeric"},java:{_digits:"꧐꧑꧒꧓꧔꧕꧖꧗꧘꧙",_type:"numeric"},jpan:{_rules:"ja/SpelloutRules/spellout-cardinal",_type:"algorithmic"},jpanfin:{_rules:"ja/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},jpanyear:{_rules:"ja/SpelloutRules/spellout-numbering-year-latn",_type:"algorithmic"},kali:{_digits:"꤀꤁꤂꤃꤄꤅꤆꤇꤈꤉",_type:"numeric"},khmr:{_digits:"០១២៣៤៥៦៧៨៩",_type:"numeric"},knda:{_digits:"೦೧೨೩೪೫೬೭೮೯",_type:"numeric"},lana:{_digits:"᪀᪁᪂᪃᪄᪅᪆᪇᪈᪉",_type:"numeric"},lanatham:{_digits:"᪐᪑᪒᪓᪔᪕᪖᪗᪘᪙",_type:"numeric"},laoo:{_digits:"໐໑໒໓໔໕໖໗໘໙",_type:"numeric"},latn:{_digits:"0123456789",_type:"numeric"},lepc:{_digits:"᱀᱁᱂᱃᱄᱅᱆᱇᱈᱉",_type:"numeric"},limb:{_digits:"᥆᥇᥈᥉᥊᥋᥌᥍᥎᥏",_type:"numeric"},mathbold:{_digits:"𝟎𝟏𝟐𝟑𝟒𝟓𝟔𝟕𝟖𝟗",_type:"numeric"},mathdbl:{_digits:"𝟘𝟙𝟚𝟛𝟜𝟝𝟞𝟟𝟠𝟡",_type:"numeric"},mathmono:{_digits:"𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿",_type:"numeric"},mathsanb:{_digits:"𝟬𝟭𝟮𝟯𝟰𝟱𝟲𝟳𝟴𝟵",_type:"numeric"},mathsans:{_digits:"𝟢𝟣𝟤𝟥𝟦𝟧𝟨𝟩𝟪𝟫",_type:"numeric"},mlym:{_digits:"൦൧൨൩൪൫൬൭൮൯",_type:"numeric"},modi:{_digits:"𑙐𑙑𑙒𑙓𑙔𑙕𑙖𑙗𑙘𑙙",_type:"numeric"},mong:{_digits:"᠐᠑᠒᠓᠔᠕᠖᠗᠘᠙",_type:"numeric"},mroo:{_digits:"𖩠𖩡𖩢𖩣𖩤𖩥𖩦𖩧𖩨𖩩",_type:"numeric"},mtei:{_digits:"꯰꯱꯲꯳꯴꯵꯶꯷꯸꯹",_type:"numeric"},mymr:{_digits:"၀၁၂၃၄၅၆၇၈၉",_type:"numeric"},mymrshan:{_digits:"႐႑႒႓႔႕႖႗႘႙",_type:"numeric"},mymrtlng:{_digits:"꧰꧱꧲꧳꧴꧵꧶꧷꧸꧹",_type:"numeric"},newa:{_digits:"𑑐𑑑𑑒𑑓𑑔𑑕𑑖𑑗𑑘𑑙",_type:"numeric"},nkoo:{_digits:"߀߁߂߃߄߅߆߇߈߉",_type:"numeric"},olck:{_digits:"᱐᱑᱒᱓᱔᱕᱖᱗᱘᱙",_type:"numeric"},orya:{_digits:"୦୧୨୩୪୫୬୭୮୯",_type:"numeric"},osma:{_digits:"𐒠𐒡𐒢𐒣𐒤𐒥𐒦𐒧𐒨𐒩",_type:"numeric"},rohg:{_digits:"𐴰𐴱𐴲𐴳𐴴𐴵𐴶𐴷𐴸𐴹",_type:"numeric"},roman:{_rules:"roman-upper",_type:"algorithmic"},romanlow:{_rules:"roman-lower",_type:"algorithmic"},saur:{_digits:"꣐꣑꣒꣓꣔꣕꣖꣗꣘꣙",_type:"numeric"},shrd:{_digits:"𑇐𑇑𑇒𑇓𑇔𑇕𑇖𑇗𑇘𑇙",_type:"numeric"},sind:{_digits:"𑋰𑋱𑋲𑋳𑋴𑋵𑋶𑋷𑋸𑋹",_type:"numeric"},sinh:{_digits:"෦෧෨෩෪෫෬෭෮෯",_type:"numeric"},sora:{_digits:"𑃰𑃱𑃲𑃳𑃴𑃵𑃶𑃷𑃸𑃹",_type:"numeric"},sund:{_digits:"᮰᮱᮲᮳᮴᮵᮶᮷᮸᮹",_type:"numeric"},takr:{_digits:"𑛀𑛁𑛂𑛃𑛄𑛅𑛆𑛇𑛈𑛉",_type:"numeric"},talu:{_digits:"᧐᧑᧒᧓᧔᧕᧖᧗᧘᧙",_type:"numeric"},taml:{_rules:"tamil",_type:"algorithmic"},tamldec:{_digits:"௦௧௨௩௪௫௬௭௮௯",_type:"numeric"},telu:{_digits:"౦౧౨౩౪౫౬౭౮౯",_type:"numeric"},thai:{_digits:"๐๑๒๓๔๕๖๗๘๙",_type:"numeric"},tibt:{_digits:"༠༡༢༣༤༥༦༧༨༩",_type:"numeric"},tirh:{_digits:"𑓐𑓑𑓒𑓓𑓔𑓕𑓖𑓗𑓘𑓙",_type:"numeric"},vaii:{_digits:"꘠꘡꘢꘣꘤꘥꘦꘧꘨꘩",_type:"numeric"},wara:{_digits:"𑣠𑣡𑣢𑣣𑣤𑣥𑣦𑣧𑣨𑣩",_type:"numeric"},wcho:{_digits:"𞋰𞋱𞋲𞋳𞋴𞋵𞋶𞋷𞋸𞋹",_type:"numeric"}}}},{main:{af:{identity:{version:{_cldrVersion:"36"},language:"af"},numbers:{currencies:{ADP:{displayName:"ADP",symbol:"ADP"},AED:{displayName:"Verenigde Arabiese Emirate-dirham","displayName-count-one":"VAE-dirham","displayName-count-other":"VAE-dirham",symbol:"AED"},AFA:{displayName:"AFA",symbol:"AFA"},AFN:{displayName:"Afgaanse afgani","displayName-count-one":"Afgaanse afgani","displayName-count-other":"Afgaanse afgani",symbol:"AFN"},ALK:{displayName:"ALK",symbol:"ALK"},ALL:{displayName:"Albanese lek","displayName-count-one":"Albanese lek","displayName-count-other":"Albanese lek",symbol:"ALL"},AMD:{displayName:"Armeense dram","displayName-count-one":"Armeense dram","displayName-count-other":"Armeense dram",symbol:"AMD"},ANG:{displayName:"Nederlands-Antilliaanse gulde","displayName-count-one":"Nederlands-Antilliaanse gulde","displayName-count-other":"Nederlands-Antilliaanse gulde",symbol:"ANG"},AOA:{displayName:"Angolese kwanza","displayName-count-one":"Angolese kwanza","displayName-count-other":"Angolese kwanza",symbol:"AOA","symbol-alt-narrow":"Kz"},AOK:{displayName:"AOK",symbol:"AOK"},AON:{displayName:"AON",symbol:"AON"},AOR:{displayName:"AOR",symbol:"AOR"},ARA:{displayName:"ARA",symbol:"ARA"},ARL:{displayName:"ARL",symbol:"ARL"},ARM:{displayName:"ARM",symbol:"ARM"},ARP:{displayName:"ARP",symbol:"ARP"},ARS:{displayName:"Argentynse peso","displayName-count-one":"Argentynse peso","displayName-count-other":"Argentynse peso",symbol:"ARS","symbol-alt-narrow":"$"},ATS:{displayName:"ATS",symbol:"ATS"},AUD:{displayName:"Australiese dollar","displayName-count-one":"Australiese dollar","displayName-count-other":"Australiese dollar",symbol:"A$","symbol-alt-narrow":"$"},AWG:{displayName:"Arubaanse floryn","displayName-count-one":"Arubaanse floryn","displayName-count-other":"Arubaanse floryn",symbol:"AWG"},AZM:{displayName:"AZM",symbol:"AZM"},AZN:{displayName:"Azerbeidjaanse manat","displayName-count-one":"Azerbeidjaanse manat","displayName-count-other":"Azerbeidjaanse manat",symbol:"AZN"},BAD:{displayName:"BAD",symbol:"BAD"},BAM:{displayName:"Bosnies-Herzegowiniese omskakelbare marka","displayName-count-one":"Bosnies-Herzegowiniese omskakelbare marka","displayName-count-other":"Bosnies-Herzegowiniese omskakelbare marka",symbol:"BAM","symbol-alt-narrow":"KM"},BAN:{displayName:"BAN",symbol:"BAN"},BBD:{displayName:"Barbados-dollar","displayName-count-one":"Barbados-dollar","displayName-count-other":"Barbados-dollar",symbol:"BBD","symbol-alt-narrow":"$"},BDT:{displayName:"Bangladesjiese taka","displayName-count-one":"Bangladesjiese taka","displayName-count-other":"Bangladesjiese taka",symbol:"BDT","symbol-alt-narrow":"৳"},BEC:{displayName:"BEC",symbol:"BEC"},BEF:{displayName:"BEF",symbol:"BEF"},BEL:{displayName:"BEL",symbol:"BEL"},BGL:{displayName:"BGL",symbol:"BGL"},BGM:{displayName:"BGM",symbol:"BGM"},BGN:{displayName:"Bulgaarse lev","displayName-count-one":"Bulgaarse lev","displayName-count-other":"Bulgaarse lev",symbol:"BGN"},BGO:{displayName:"BGO",symbol:"BGO"},BHD:{displayName:"Bahreinse dinar","displayName-count-one":"Bahreinse dinar","displayName-count-other":"Bahreinse dinar",symbol:"BHD"},BIF:{displayName:"Burundiese frank","displayName-count-one":"Burundiese frank","displayName-count-other":"Burundiese frank",symbol:"BIF"},BMD:{displayName:"Bermuda-dollar","displayName-count-one":"Bermuda-dollar","displayName-count-other":"Bermuda-dollar",symbol:"BMD","symbol-alt-narrow":"$"},BND:{displayName:"Broeneise dollar","displayName-count-one":"Broeneise dollar","displayName-count-other":"Broeneise dollar",symbol:"BND","symbol-alt-narrow":"$"},BOB:{displayName:"Boliviaanse boliviano","displayName-count-one":"Boliviaanse boliviano","displayName-count-other":"Boliviaanse boliviano",symbol:"BOB","symbol-alt-narrow":"Bs"},BOL:{displayName:"BOL",symbol:"BOL"},BOP:{displayName:"BOP",symbol:"BOP"},BOV:{displayName:"BOV",symbol:"BOV"},BRB:{displayName:"BRB",symbol:"BRB"},BRC:{displayName:"BRC",symbol:"BRC"},BRE:{displayName:"BRE",symbol:"BRE"},BRL:{displayName:"Brasilliaanse reaal","displayName-count-one":"Brasillianse reaal","displayName-count-other":"Brasillianse reaal",symbol:"R$","symbol-alt-narrow":"R$"},BRN:{displayName:"BRN",symbol:"BRN"},BRR:{displayName:"BRR",symbol:"BRR"},BRZ:{displayName:"BRZ",symbol:"BRZ"},BSD:{displayName:"Bahamiaanse dollar","displayName-count-one":"Bahamiaanse dollar","displayName-count-other":"Bahamiaanse dollar",symbol:"BSD","symbol-alt-narrow":"$"},BTN:{displayName:"Bhoetanese ngoeltroem","displayName-count-one":"Bhoetanese ngoeltroem","displayName-count-other":"Bhoetanese ngoeltroem",symbol:"BTN"},BUK:{displayName:"BUK",symbol:"BUK"},BWP:{displayName:"Botswana-pula","displayName-count-one":"Botswana-pula","displayName-count-other":"Botswana-pula",symbol:"BWP","symbol-alt-narrow":"P"},BYB:{displayName:"BYB",symbol:"BYB"},BYN:{displayName:"Belarusiese roebel","displayName-count-one":"Belarusiese roebel","displayName-count-other":"Belarusiese roebel",symbol:"BYN","symbol-alt-narrow":"р."},BYR:{displayName:"Belo-Russiese roebel (2000–2016)","displayName-count-one":"Belo-Russiese roebel (2000–2016)","displayName-count-other":"Belo-Russiese roebel (2000–2016)",symbol:"BYR"},BZD:{displayName:"Beliziese dollar","displayName-count-one":"Beliziese dollar","displayName-count-other":"Beliziese dollar",symbol:"BZD","symbol-alt-narrow":"$"},CAD:{displayName:"Kanadese dollar","displayName-count-one":"Kanadese dollar","displayName-count-other":"Kanadese dollar",symbol:"CAD","symbol-alt-narrow":"$"},CDF:{displayName:"Kongolese frank","displayName-count-one":"Kongolese frank","displayName-count-other":"Kongolese frank",symbol:"CDF"},CHE:{displayName:"CHE",symbol:"CHE"},CHF:{displayName:"Switserse frank","displayName-count-one":"Switserse frank","displayName-count-other":"Switserse frank",symbol:"CHF"},CHW:{displayName:"CHW",symbol:"CHW"},CLE:{displayName:"CLE",symbol:"CLE"},CLF:{displayName:"CLF",symbol:"CLF"},CLP:{displayName:"Chileense peso","displayName-count-one":"Chileense peso","displayName-count-other":"Chileense peso",symbol:"CLP","symbol-alt-narrow":"$"},CNH:{displayName:"Chinese joean (buiteland)","displayName-count-one":"Chinese joean (buiteland)","displayName-count-other":"Chinese joean (buiteland)",symbol:"CNH"},CNX:{displayName:"CNX",symbol:"CNX"},CNY:{displayName:"Chinese joean","displayName-count-one":"Chinese joean","displayName-count-other":"Chinese joean",symbol:"CN¥","symbol-alt-narrow":"¥"},COP:{displayName:"Colombiaanse peso","displayName-count-one":"Colombiaanse peso","displayName-count-other":"Colombiaanse peso",symbol:"COP","symbol-alt-narrow":"$"},COU:{displayName:"COU",symbol:"COU"},CRC:{displayName:"Costa Ricaanse colón","displayName-count-one":"Costa Ricaanse colón","displayName-count-other":"Costa Ricaanse colón",symbol:"CRC","symbol-alt-narrow":"₡"},CSD:{displayName:"CSD",symbol:"CSD"},CSK:{displayName:"CSK",symbol:"CSK"},CUC:{displayName:"Kubaanse omskakelbare peso","displayName-count-one":"Kubaanse omskakelbare peso","displayName-count-other":"Kubaanse omskakelbare peso",symbol:"CUC","symbol-alt-narrow":"$"},CUP:{displayName:"Kubaanse peso","displayName-count-one":"Kubaanse peso","displayName-count-other":"Kubaanse peso",symbol:"CUP","symbol-alt-narrow":"$"},CVE:{displayName:"Kaap Verdiese escudo","displayName-count-one":"Kaap Verdiese escudo","displayName-count-other":"Kaap Verdiese escudo",symbol:"CVE"},CYP:{displayName:"CYP",symbol:"CYP"},CZK:{displayName:"Tsjeggiese kroon","displayName-count-one":"Tsjeggiese kroon","displayName-count-other":"Tsjeggiese kroon",symbol:"CZK","symbol-alt-narrow":"Kč"},DDM:{displayName:"DDM",symbol:"DDM"},DEM:{displayName:"DEM",symbol:"DEM"},DJF:{displayName:"Djiboeti-frank","displayName-count-one":"Djiboeti-frank","displayName-count-other":"Djiboeti-frank",symbol:"DJF"},DKK:{displayName:"Deense kroon","displayName-count-one":"Deense kroon","displayName-count-other":"Deense kroon",symbol:"DKK","symbol-alt-narrow":"kr"},DOP:{displayName:"Dominikaanse peso","displayName-count-one":"Dominikaanse peso","displayName-count-other":"Dominikaanse peso",symbol:"DOP","symbol-alt-narrow":"$"},DZD:{displayName:"Algeriese dinar","displayName-count-one":"Algeriese dinar","displayName-count-other":"Algeriese dinar",symbol:"DZD"},ECS:{displayName:"ECS",symbol:"ECS"},ECV:{displayName:"ECV",symbol:"ECV"},EEK:{displayName:"EEK",symbol:"EEK"},EGP:{displayName:"Egiptiese pond","displayName-count-one":"Egiptiese pond","displayName-count-other":"Egiptiese pond",symbol:"EGP","symbol-alt-narrow":"E£"},ERN:{displayName:"Eritrese nakfa","displayName-count-one":"Eritrese nakfa","displayName-count-other":"Eritrese nakfa",symbol:"ERN"},ESA:{displayName:"ESA",symbol:"ESA"},ESB:{displayName:"ESB",symbol:"ESB"},ESP:{displayName:"ESP",symbol:"ESP","symbol-alt-narrow":"₧"},ETB:{displayName:"Etiopiese birr","displayName-count-one":"Etiopiese birr","displayName-count-other":"Etiopiese birr",symbol:"ETB"},EUR:{displayName:"euro","displayName-count-one":"euro","displayName-count-other":"euro",symbol:"€","symbol-alt-narrow":"€"},FIM:{displayName:"FIM",symbol:"FIM"},FJD:{displayName:"Fidjiaanse dollar","displayName-count-one":"Fidjiaanse dollar","displayName-count-other":"Fidjiaanse dollar",symbol:"FJD","symbol-alt-narrow":"$"},FKP:{displayName:"Falkland-eilandse pond","displayName-count-one":"Falkland-eilandse pond","displayName-count-other":"Falkland-eilandse pond",symbol:"FKP","symbol-alt-narrow":"£"},FRF:{displayName:"FRF",symbol:"FRF"},GBP:{displayName:"Britse pond","displayName-count-one":"Britse pond","displayName-count-other":"Britse pond",symbol:"£","symbol-alt-narrow":"£"},GEK:{displayName:"GEK",symbol:"GEK"},GEL:{displayName:"Georgiese lari","displayName-count-one":"Georgiese lari","displayName-count-other":"Georgiese lari",symbol:"GEL","symbol-alt-narrow":"₾","symbol-alt-variant":"₾"},GHC:{displayName:"Ghanese cedi (1979–2007)",symbol:"GHC"},GHS:{displayName:"Ghanese cedi","displayName-count-one":"Ghanese cedi","displayName-count-other":"Ghanese cedi",symbol:"GHS"},GIP:{displayName:"Gibraltarese pond","displayName-count-one":"Gibraltarese pond","displayName-count-other":"Gibraltarese pond",symbol:"GIP","symbol-alt-narrow":"£"},GMD:{displayName:"Gambiese dalasi","displayName-count-one":"Gambiese dalasi","displayName-count-other":"Gambiese dalasi",symbol:"GMD"},GNF:{displayName:"Guinese frank","displayName-count-one":"Guinese frank","displayName-count-other":"Guinese frank",symbol:"GNF","symbol-alt-narrow":"FG"},GNS:{displayName:"Guinese syli",symbol:"GNS"},GQE:{displayName:"GQE",symbol:"GQE"},GRD:{displayName:"GRD",symbol:"GRD"},GTQ:{displayName:"Guatemalaanse quetzal","displayName-count-one":"Guatemalaanse quetzal","displayName-count-other":"Guatemalaanse quetzal",symbol:"GTQ","symbol-alt-narrow":"Q"},GWE:{displayName:"GWE",symbol:"GWE"},GWP:{displayName:"GWP",symbol:"GWP"},GYD:{displayName:"Guyanese dollar","displayName-count-one":"Guyanese dollar","displayName-count-other":"Guyanese dollar",symbol:"GYD","symbol-alt-narrow":"$"},HKD:{displayName:"Hongkongse dollar","displayName-count-one":"Hongkongse dollar","displayName-count-other":"Hongkongse dollar",symbol:"HK$","symbol-alt-narrow":"$"},HNL:{displayName:"Hondurese lempira","displayName-count-one":"Hondurese lempira","displayName-count-other":"Hondurese lempira",symbol:"HNL","symbol-alt-narrow":"L"},HRD:{displayName:"HRD",symbol:"HRD"},HRK:{displayName:"Kroatiese kuna","displayName-count-one":"Kroatiese kuna","displayName-count-other":"Kroatiese kuna",symbol:"HRK","symbol-alt-narrow":"kn"},HTG:{displayName:"Haïtiaanse gourde","displayName-count-one":"Haïtiaanse gourde","displayName-count-other":"Haïtiaanse gourde",symbol:"HTG"},HUF:{displayName:"Hongaarse florint","displayName-count-one":"Hongaarse florint","displayName-count-other":"Hongaarse florint",symbol:"HUF","symbol-alt-narrow":"Ft"},IDR:{displayName:"Indonesiese roepia","displayName-count-one":"Indonesiese roepia","displayName-count-other":"Indonesiese roepia",symbol:"IDR","symbol-alt-narrow":"Rp"},IEP:{displayName:"IEP",symbol:"IEP"},ILP:{displayName:"ILP",symbol:"ILP"},ILR:{displayName:"ILR",symbol:"ILR"},ILS:{displayName:"Israeliese nuwe sikkel","displayName-count-one":"Israeliese nuwe sikkel","displayName-count-other":"Israeliese nuwe sikkel",symbol:"₪","symbol-alt-narrow":"₪"},INR:{displayName:"Indiese roepee","displayName-count-one":"Indiese rupee","displayName-count-other":"Indiese rupee",symbol:"₹","symbol-alt-narrow":"₹"},IQD:{displayName:"Irakse dinar","displayName-count-one":"Irakse dinar","displayName-count-other":"Irakse dinar",symbol:"IQD"},IRR:{displayName:"Iranse rial","displayName-count-one":"Iranse rial","displayName-count-other":"Iranse rial",symbol:"IRR"},ISJ:{displayName:"ISJ",symbol:"ISJ"},ISK:{displayName:"Yslandse kroon","displayName-count-one":"Yslandse kroon","displayName-count-other":"Yslandse kroon",symbol:"ISK","symbol-alt-narrow":"kr"},ITL:{displayName:"Italiaanse lier",symbol:"ITL"},JMD:{displayName:"Jamaikaanse dollar","displayName-count-one":"Jamaikaanse dollar","displayName-count-other":"Jamaikaanse dollar",symbol:"JMD","symbol-alt-narrow":"$"},JOD:{displayName:"Jordaniese dinar","displayName-count-one":"Jordaniese dinar","displayName-count-other":"Jordaniese dinar",symbol:"JOD"},JPY:{displayName:"Japannese jen","displayName-count-one":"Japannese jen","displayName-count-other":"Japannese jen",symbol:"JP¥","symbol-alt-narrow":"¥"},KES:{displayName:"Keniaanse sjieling","displayName-count-one":"Keniaanse sjieling","displayName-count-other":"Keniaanse sjieling",symbol:"KES"},KGS:{displayName:"Kirgisiese som","displayName-count-one":"Kirgisiese som","displayName-count-other":"Kirgisiese som",symbol:"KGS"},KHR:{displayName:"Kambodjaanse riel","displayName-count-one":"Kambodjaanse riel","displayName-count-other":"Kambodjaanse riel",symbol:"KHR","symbol-alt-narrow":"៛"},KMF:{displayName:"Comoraanse frank","displayName-count-one":"Comoraanse frank","displayName-count-other":"Comoraanse frank",symbol:"KMF","symbol-alt-narrow":"CF"},KPW:{displayName:"Noord-Koreaanse won","displayName-count-one":"Noord-Koreaanse won","displayName-count-other":"Noord-Koreaanse won",symbol:"KPW","symbol-alt-narrow":"₩"},KRH:{displayName:"KRH",symbol:"KRH"},KRO:{displayName:"KRO",symbol:"KRO"},KRW:{displayName:"Suid-Koreaanse won","displayName-count-one":"Suid-Koreaanse won","displayName-count-other":"Suid-Koreaanse won",symbol:"₩","symbol-alt-narrow":"₩"},KWD:{displayName:"Koeweitse dinar","displayName-count-one":"Koeweitse dinar","displayName-count-other":"Koeweitse dinar",symbol:"KWD"},KYD:{displayName:"Cayman-eilandse dollar","displayName-count-one":"Cayman-eilandse dollar","displayName-count-other":"Cayman-eilandse dollar",symbol:"KYD","symbol-alt-narrow":"$"},KZT:{displayName:"Kazakse tenge","displayName-count-one":"Kazakse tenge","displayName-count-other":"Kazakse tenge",symbol:"KZT","symbol-alt-narrow":"₸"},LAK:{displayName:"Laosiaanse kip","displayName-count-one":"Laosiaanse kip","displayName-count-other":"Laosiaanse kip",symbol:"LAK","symbol-alt-narrow":"₭"},LBP:{displayName:"Libanese pond","displayName-count-one":"Libanese pond","displayName-count-other":"Libanese pond",symbol:"LBP","symbol-alt-narrow":"L£"},LKR:{displayName:"Sri Lankaanse roepee","displayName-count-one":"Sri Lankaanse roepee","displayName-count-other":"Sri Lankaanse roepee",symbol:"LKR","symbol-alt-narrow":"Rs"},LRD:{displayName:"Liberiese dollar","displayName-count-one":"Liberiese dollar","displayName-count-other":"Liberiese dollar",symbol:"LRD","symbol-alt-narrow":"$"},LSL:{displayName:"Lesotho loti",symbol:"LSL"},LTL:{displayName:"Litause litas","displayName-count-one":"Litause litas","displayName-count-other":"Litause litas",symbol:"LTL","symbol-alt-narrow":"Lt"},LTT:{displayName:"LTT",symbol:"LTT"},LUC:{displayName:"LUC",symbol:"LUC"},LUF:{displayName:"LUF",symbol:"LUF"},LUL:{displayName:"LUL",symbol:"LUL"},LVL:{displayName:"Lettiese lats",symbol:"LVL","symbol-alt-narrow":"Ls"},LVR:{displayName:"LVR",symbol:"LVR"},LYD:{displayName:"Libiese dinar","displayName-count-one":"Libiese dinar","displayName-count-other":"Libiese dinar",symbol:"LYD"},MAD:{displayName:"Marokkaanse dirham","displayName-count-one":"Marokkaanse dirham","displayName-count-other":"Marokkaanse dirham",symbol:"MAD"},MAF:{displayName:"MAF",symbol:"MAF"},MCF:{displayName:"MCF",symbol:"MCF"},MDC:{displayName:"MDC",symbol:"MDC"},MDL:{displayName:"Moldowiese leu","displayName-count-one":"Moldowiese leu","displayName-count-other":"Moldowiese leu",symbol:"MDL"},MGA:{displayName:"Malgassiese ariary","displayName-count-one":"Malgassiese ariary","displayName-count-other":"Malgassiese ariary",symbol:"MGA","symbol-alt-narrow":"Ar"},MGF:{displayName:"MGF",symbol:"MGF"},MKD:{displayName:"Macedoniese denar","displayName-count-one":"Macedoniese denar","displayName-count-other":"Macedoniese denar",symbol:"MKD"},MKN:{displayName:"MKN",symbol:"MKN"},MLF:{displayName:"MLF",symbol:"MLF"},MMK:{displayName:"Mianmese kyat","displayName-count-one":"Mianmese kyat","displayName-count-other":"Mianmese kyat",symbol:"MMK","symbol-alt-narrow":"K"},MNT:{displayName:"Mongoolse toegrik","displayName-count-one":"Mongoolse toegrik","displayName-count-other":"Mongoolse toegrik",symbol:"MNT","symbol-alt-narrow":"₮"},MOP:{displayName:"Macaose pataca","displayName-count-one":"Macaose pataca","displayName-count-other":"Macaose pataca",symbol:"MOP"},MRO:{displayName:"Mauritaniese ouguiya (1973–2017)","displayName-count-one":"Mauritaniese ouguiya (1973–2017)","displayName-count-other":"Mauritaniese ouguiya (1973–2017)",symbol:"MRO"},MRU:{displayName:"Mauritaniese ouguiya","displayName-count-one":"Mauritaniese ouguiya","displayName-count-other":"Mauritaniese ouguiya",symbol:"MRU"},MTL:{displayName:"MTL",symbol:"MTL"},MTP:{displayName:"MTP",symbol:"MTP"},MUR:{displayName:"Mauritiaanse roepee","displayName-count-one":"Mauritiaanse roepee","displayName-count-other":"Mauritiaanse roepee",symbol:"MUR","symbol-alt-narrow":"Rs"},MVP:{displayName:"MVP",symbol:"MVP"},MVR:{displayName:"Malediviese rufia","displayName-count-one":"Malediviese rufia","displayName-count-other":"Malediviese rufia",symbol:"MVR"},MWK:{displayName:"Malawiese kwacha","displayName-count-one":"Malawiese kwacha","displayName-count-other":"Malawiese kwacha",symbol:"MWK"},MXN:{displayName:"Meksikaanse peso","displayName-count-one":"Meksikaanse peso","displayName-count-other":"Meksikaanse peso",symbol:"MXN","symbol-alt-narrow":"$"},MXP:{displayName:"MXP",symbol:"MXP"},MXV:{displayName:"MXV",symbol:"MXV"},MYR:{displayName:"Maleisiese ringgit","displayName-count-one":"Maleisiese ringgit","displayName-count-other":"Maleisiese ringgit",symbol:"MYR","symbol-alt-narrow":"RM"},MZE:{displayName:"MZE",symbol:"MZE"},MZM:{displayName:"Mosambiekse metical (1980–2006)",symbol:"MZM"},MZN:{displayName:"Mosambiekse metical","displayName-count-one":"Mosambiekse metical","displayName-count-other":"Mosambiekse metical",symbol:"MZN"},NAD:{displayName:"Namibiese dollar","displayName-count-one":"Namibiese dollar","displayName-count-other":"Namibiese dollar",symbol:"NAD","symbol-alt-narrow":"$"},NGN:{displayName:"Nigeriese naira","displayName-count-one":"Nigeriese naira","displayName-count-other":"Nigeriese naira",symbol:"NGN","symbol-alt-narrow":"₦"},NIC:{displayName:"NIC",symbol:"NIC"},NIO:{displayName:"Nicaraguaanse córdoba","displayName-count-one":"Nicaraguaanse córdoba","displayName-count-other":"Nicaraguaanse córdoba",symbol:"NIO","symbol-alt-narrow":"C$"},NLG:{displayName:"NLG",symbol:"NLG"},NOK:{displayName:"Noorse kroon","displayName-count-one":"Noorse kroon","displayName-count-other":"Noorse kroon",symbol:"NOK","symbol-alt-narrow":"kr"},NPR:{displayName:"Nepalese roepee","displayName-count-one":"Nepalese roepee","displayName-count-other":"Nepalese roepee",symbol:"NPR","symbol-alt-narrow":"Rs"},NZD:{displayName:"Nieu-Seelandse dollar","displayName-count-one":"Nieu-Seelandse dollar","displayName-count-other":"Nieu-Seelandse dollar",symbol:"NZ$","symbol-alt-narrow":"$"},OMR:{displayName:"Omaanse rial","displayName-count-one":"Omaanse rial","displayName-count-other":"Omaanse rial",symbol:"OMR"},PAB:{displayName:"Panamese balboa","displayName-count-one":"Panamese balboa","displayName-count-other":"Panamese balboa",symbol:"PAB"},PEI:{displayName:"PEI",symbol:"PEI"},PEN:{displayName:"Peruaanse sol","displayName-count-one":"Peruaanse sol","displayName-count-other":"Peruaanse sol",symbol:"PEN"},PES:{displayName:"PES",symbol:"PES"},PGK:{displayName:"Papoea-Nieu-Guinese kina","displayName-count-one":"Papoea-Nieu-Guinese kina","displayName-count-other":"Papoea-Nieu-Guinese kina",symbol:"PGK"},PHP:{displayName:"Filippynse peso","displayName-count-one":"Filippynse peso","displayName-count-other":"Filippynse peso",symbol:"PHP","symbol-alt-narrow":"₱"},PKR:{displayName:"Pakistanse roepee","displayName-count-one":"Pakistanse roepee","displayName-count-other":"Pakistanse roepee",symbol:"PKR","symbol-alt-narrow":"Rs"},PLN:{displayName:"Poolse zloty","displayName-count-one":"Poolse zloty","displayName-count-other":"Poolse zloty",symbol:"PLN","symbol-alt-narrow":"zł"},PLZ:{displayName:"PLZ",symbol:"PLZ"},PTE:{displayName:"PTE",symbol:"PTE"},PYG:{displayName:"Paraguaanse guarani","displayName-count-one":"Paraguaanse guarani","displayName-count-other":"Paraguaanse guarani",symbol:"PYG","symbol-alt-narrow":"₲"},QAR:{displayName:"Katarrese rial","displayName-count-one":"Katarese rial","displayName-count-other":"Katarese rial",symbol:"QAR"},RHD:{displayName:"RHD",symbol:"RHD"},ROL:{displayName:"ROL",symbol:"ROL"},RON:{displayName:"Roemeense leu","displayName-count-one":"Roemeense leu","displayName-count-other":"Roemeense leu",symbol:"RON","symbol-alt-narrow":"leu"},RSD:{displayName:"Serwiese dinar","displayName-count-one":"Serwiese dinar","displayName-count-other":"Serwiese dinar",symbol:"RSD"},RUB:{displayName:"Russiese roebel","displayName-count-one":"Russiese roebel","displayName-count-other":"Russiese roebel",symbol:"RUB","symbol-alt-narrow":"₽","symbol-alt-variant":"₽"},RUR:{displayName:"RUR",symbol:"RUR","symbol-alt-narrow":"р."},RWF:{displayName:"Rwandese frank","displayName-count-one":"Rwandese frank","displayName-count-other":"Rwandese frank",symbol:"RWF","symbol-alt-narrow":"RF"},SAR:{displayName:"Saoedi-Arabiese riyal","displayName-count-one":"Saoedi-Arabiese riyal","displayName-count-other":"Saoedi-Arabiese riyal",symbol:"SAR"},SBD:{displayName:"Salomonseilandse dollar","displayName-count-one":"Salomonseilandse dollar","displayName-count-other":"Salomonseilandse dollar",symbol:"SBD","symbol-alt-narrow":"$"},SCR:{displayName:"Seychellese roepee","displayName-count-one":"Seychellese roepee","displayName-count-other":"Seychellese roepee",symbol:"SCR"},SDD:{displayName:"SDD",symbol:"SDD"},SDG:{displayName:"Soedannese pond","displayName-count-one":"Soedannese pond","displayName-count-other":"Soedannese pond",symbol:"SDG"},SDP:{displayName:"Soedannese pond (1957–1998)",symbol:"SDP"},SEK:{displayName:"Sweedse kroon","displayName-count-one":"Sweedse kroon","displayName-count-other":"Sweedse kroon",symbol:"SEK","symbol-alt-narrow":"kr"},SGD:{displayName:"Singapoer-dollar","displayName-count-one":"Singapoer-dollar","displayName-count-other":"Singapoer-dollar",symbol:"SGD","symbol-alt-narrow":"$"},SHP:{displayName:"Sint Helena-pond","displayName-count-one":"Sint Helena-pond","displayName-count-other":"Sint Helena-pond",symbol:"SHP","symbol-alt-narrow":"£"},SIT:{displayName:"SIT",symbol:"SIT"},SKK:{displayName:"SKK",symbol:"SKK"},SLL:{displayName:"Sierra Leoniese leone","displayName-count-one":"Sierra Leoniese leone","displayName-count-other":"Sierra Leoniese leone",symbol:"SLL"},SOS:{displayName:"Somaliese sjieling","displayName-count-one":"Somaliese sjieling","displayName-count-other":"Somaliese sjieling",symbol:"SOS"},SRD:{displayName:"Surinaamse dollar","displayName-count-one":"Surinaamse dollar","displayName-count-other":"Surinaamse dollar",symbol:"SRD","symbol-alt-narrow":"$"},SRG:{displayName:"SRG",symbol:"SRG"},SSP:{displayName:"Suid-Soedanese pond","displayName-count-one":"Suid-Soedanese pond","displayName-count-other":"Suid-Soedanese pond",symbol:"SSP","symbol-alt-narrow":"£"},STD:{displayName:"São Tomé en Príncipe dobra (1977–2017)","displayName-count-one":"São Tomé en Príncipe dobra (1977–2017)","displayName-count-other":"São Tomé en Príncipe dobra (1977–2017)",symbol:"STD"},STN:{displayName:"São Tomé en Príncipe-dobra","displayName-count-one":"São Tomé en Príncipe-dobra","displayName-count-other":"São Tomé en Príncipe-dobra",symbol:"STN","symbol-alt-narrow":"Db"},SUR:{displayName:"SUR",symbol:"SUR"},SVC:{displayName:"SVC",symbol:"SVC"},SYP:{displayName:"Siriese pond","displayName-count-one":"Siriese pond","displayName-count-other":"Siriese pond",symbol:"SYP","symbol-alt-narrow":"£"},SZL:{displayName:"Swazilandse lilangeni","displayName-count-one":"Swazilandse lilangeni","displayName-count-other":"Swazilandse lilangeni",symbol:"SZL"},THB:{displayName:"Thaise baht","displayName-count-one":"Thaise baht","displayName-count-other":"Thaise baht",symbol:"฿","symbol-alt-narrow":"฿"},TJR:{displayName:"TJR",symbol:"TJR"},TJS:{displayName:"Tadjikse somoni","displayName-count-one":"Tadjikse somoni","displayName-count-other":"Tadjikse somoni",symbol:"TJS"},TMM:{displayName:"TMM",symbol:"TMM"},TMT:{displayName:"Turkmeense manat","displayName-count-one":"Turkmeense manat","displayName-count-other":"Turkmeense manat",symbol:"TMT"},TND:{displayName:"Tunisiese dinar","displayName-count-one":"Tunisiese dinar","displayName-count-other":"Tunisiese dinar",symbol:"TND"},TOP:{displayName:"Tongaanse pa’anga","displayName-count-one":"Tongaanse pa’anga","displayName-count-other":"Tongaanse pa’anga",symbol:"TOP","symbol-alt-narrow":"T$"},TPE:{displayName:"TPE",symbol:"TPE"},TRL:{displayName:"Turkse lier (1922–2005)",symbol:"TRL"},TRY:{displayName:"Turkse lira","displayName-count-one":"Turkse lira","displayName-count-other":"Turkse lira",symbol:"TRY","symbol-alt-narrow":"₺","symbol-alt-variant":"TL"},TTD:{displayName:"Trinidad en Tobago-dollar","displayName-count-one":"Trinidad en Tobago-dollar","displayName-count-other":"Trinidad en Tobago-dollar",symbol:"TTD","symbol-alt-narrow":"$"},TWD:{displayName:"Nuwe Taiwanese dollar","displayName-count-one":"Nuwe Taiwanese dollar","displayName-count-other":"Nuwe Taiwanese dollar",symbol:"NT$","symbol-alt-narrow":"NT$"},TZS:{displayName:"Tanzaniese sjieling","displayName-count-one":"Tanzaniese sjieling","displayName-count-other":"Tanzaniese sjieling",symbol:"TZS"},UAH:{displayName:"Oekraïnse hriwna","displayName-count-one":"Oekraïnse hriwna","displayName-count-other":"Oekraïnse hriwna",symbol:"UAH","symbol-alt-narrow":"₴"},UAK:{displayName:"UAK",symbol:"UAK"},UGS:{displayName:"UGS",symbol:"UGS"},UGX:{displayName:"Ugandese sjieling","displayName-count-one":"Ugandese sjieling","displayName-count-other":"Ugandese sjieling",symbol:"UGX"},USD:{displayName:"VSA-dollar","displayName-count-one":"VSA-dollar","displayName-count-other":"VSA-dollar",symbol:"USD","symbol-alt-narrow":"$"},USN:{displayName:"USN",symbol:"USN"},USS:{displayName:"USS",symbol:"USS"},UYI:{displayName:"UYI",symbol:"UYI"},UYP:{displayName:"UYP",symbol:"UYP"},UYU:{displayName:"Uruguaanse peso","displayName-count-one":"Uruguaanse peso","displayName-count-other":"Uruguaanse peso",symbol:"UYU","symbol-alt-narrow":"$"},UYW:{displayName:"UYW",symbol:"UYW"},UZS:{displayName:"Oezbekiese som","displayName-count-one":"Oezbekiese som","displayName-count-other":"Oezbekiese som",symbol:"UZS"},VEB:{displayName:"VEB",symbol:"VEB"},VEF:{displayName:"Venezolaanse bolivar","displayName-count-one":"Venezolaanse bolívar (2008–2018)","displayName-count-other":"Venezolaanse bolívare (2008–2018)",symbol:"VEF","symbol-alt-narrow":"Bs"},VES:{displayName:"Venezolaanse bolívar","displayName-count-one":"Venezolaanse bolívar","displayName-count-other":"Venezolaanse bolívar",symbol:"VES"},VND:{displayName:"Viëtnamese dong","displayName-count-one":"Viëtnamese dong","displayName-count-other":"Viëtnamese dong",symbol:"₫","symbol-alt-narrow":"₫"},VNN:{displayName:"VNN",symbol:"VNN"},VUV:{displayName:"Vanuatuse vatu","displayName-count-one":"Vanuatuse vatu","displayName-count-other":"Vanuatuse vatu",symbol:"VUV"},WST:{displayName:"Samoaanse tala","displayName-count-one":"Samoaanse tala","displayName-count-other":"Samoaanse tala",symbol:"WST"},XAF:{displayName:"Sentraal Afrikaanse CFA-frank","displayName-count-one":"Sentraal Afrikaanse CFA-frank","displayName-count-other":"Sentraal Afrikaanse CFA-frank",symbol:"FCFA"},XAG:{displayName:"XAG",symbol:"XAG"},XAU:{displayName:"XAU",symbol:"XAU"},XBA:{displayName:"XBA",symbol:"XBA"},XBB:{displayName:"XBB",symbol:"XBB"},XBC:{displayName:"XBC",symbol:"XBC"},XBD:{displayName:"XBD",symbol:"XBD"},XCD:{displayName:"Oos-Karibiese dollar","displayName-count-one":"Oos-Karibiese dollar","displayName-count-other":"Oos-Karibiese dollar",symbol:"EC$","symbol-alt-narrow":"$"},XDR:{displayName:"XDR",symbol:"XDR"},XEU:{displayName:"XEU",symbol:"XEU"},XFO:{displayName:"XFO",symbol:"XFO"},XFU:{displayName:"XFU",symbol:"XFU"},XOF:{displayName:"Wes-Afrikaanse CFA-frank","displayName-count-one":"Wes-Afrikaanse CFA-frank","displayName-count-other":"Wes-Afrikaanse CFA-frank",symbol:"CFA"},XPD:{displayName:"XPD",symbol:"XPD"},XPF:{displayName:"CFP-frank","displayName-count-one":"CFP-frank","displayName-count-other":"CFP-frank",symbol:"CFPF"},XPT:{displayName:"XPT",symbol:"XPT"},XRE:{displayName:"XRE",symbol:"XRE"},XSU:{displayName:"XSU",symbol:"XSU"},XTS:{displayName:"XTS",symbol:"XTS"},XUA:{displayName:"XUA",symbol:"XUA"},XXX:{displayName:"onbekende geldeenheid","displayName-count-one":"(onbekende geldeenheid)","displayName-count-other":"(onbekende geldeenheid)",symbol:"¤"},YDD:{displayName:"YDD",symbol:"YDD"},YER:{displayName:"Jemenitiese rial","displayName-count-one":"Jemenitiese rial","displayName-count-other":"Jemenitiese rial",symbol:"YER"},YUD:{displayName:"YUD",symbol:"YUD"},YUM:{displayName:"YUM",symbol:"YUM"},YUN:{displayName:"YUN",symbol:"YUN"},YUR:{displayName:"YUR",symbol:"YUR"},ZAL:{displayName:"ZAL",symbol:"ZAL"},ZAR:{displayName:"Suid-Afrikaanse rand","displayName-count-one":"Suid-Afrikaanse rand","displayName-count-other":"Suid-Afrikaanse rand",symbol:"R","symbol-alt-narrow":"R"},ZMK:{displayName:"Zambiese kwacha (1968–2012)",symbol:"ZMK"},ZMW:{displayName:"Zambiese kwacha","displayName-count-one":"Zambiese kwacha","displayName-count-other":"Zambiese kwacha",symbol:"ZMW","symbol-alt-narrow":"ZK"},ZRN:{displayName:"ZRN",symbol:"ZRN"},ZRZ:{displayName:"ZRZ",symbol:"ZRZ"},ZWD:{displayName:"Zimbabwiese dollar",symbol:"ZWD"},ZWL:{displayName:"ZWL",symbol:"ZWL"},ZWR:{displayName:"ZWR",symbol:"ZWR"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},currencyData:{fractions:{ADP:{_rounding:"0",_digits:"0"},AFN:{_rounding:"0",_digits:"0"},ALL:{_rounding:"0",_digits:"0"},AMD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},BHD:{_rounding:"0",_digits:"3"},BIF:{_rounding:"0",_digits:"0"},BYN:{_rounding:"0",_digits:"2"},BYR:{_rounding:"0",_digits:"0"},CAD:{_rounding:"0",_digits:"2",_cashRounding:"5"},CHF:{_rounding:"0",_digits:"2",_cashRounding:"5"},CLF:{_rounding:"0",_digits:"4"},CLP:{_rounding:"0",_digits:"0"},COP:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CRC:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CZK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},DEFAULT:{_rounding:"0",_digits:"2"},DJF:{_rounding:"0",_digits:"0"},DKK:{_rounding:"0",_digits:"2",_cashRounding:"50"},ESP:{_rounding:"0",_digits:"0"},GNF:{_rounding:"0",_digits:"0"},GYD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},HUF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IDR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IQD:{_rounding:"0",_digits:"0"},IRR:{_rounding:"0",_digits:"0"},ISK:{_rounding:"0",_digits:"0"},ITL:{_rounding:"0",_digits:"0"},JOD:{_rounding:"0",_digits:"3"},JPY:{_rounding:"0",_digits:"0"},KMF:{_rounding:"0",_digits:"0"},KPW:{_rounding:"0",_digits:"0"},KRW:{_rounding:"0",_digits:"0"},KWD:{_rounding:"0",_digits:"3"},LAK:{_rounding:"0",_digits:"0"},LBP:{_rounding:"0",_digits:"0"},LUF:{_rounding:"0",_digits:"0"},LYD:{_rounding:"0",_digits:"3"},MGA:{_rounding:"0",_digits:"0"},MGF:{_rounding:"0",_digits:"0"},MMK:{_rounding:"0",_digits:"0"},MNT:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},MRO:{_rounding:"0",_digits:"0"},MUR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},NOK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},OMR:{_rounding:"0",_digits:"3"},PKR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},PYG:{_rounding:"0",_digits:"0"},RSD:{_rounding:"0",_digits:"0"},RWF:{_rounding:"0",_digits:"0"},SEK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},SLL:{_rounding:"0",_digits:"0"},SOS:{_rounding:"0",_digits:"0"},STD:{_rounding:"0",_digits:"0"},SYP:{_rounding:"0",_digits:"0"},TMM:{_rounding:"0",_digits:"0"},TND:{_rounding:"0",_digits:"3"},TRL:{_rounding:"0",_digits:"0"},TWD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},TZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},UGX:{_rounding:"0",_digits:"0"},UYI:{_rounding:"0",_digits:"0"},UYW:{_rounding:"0",_digits:"4"},UZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VEF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VND:{_rounding:"0",_digits:"0"},VUV:{_rounding:"0",_digits:"0"},XAF:{_rounding:"0",_digits:"0"},XOF:{_rounding:"0",_digits:"0"},XPF:{_rounding:"0",_digits:"0"},YER:{_rounding:"0",_digits:"0"},ZMK:{_rounding:"0",_digits:"0"},ZWD:{_rounding:"0",_digits:"0"}},region:{AC:[{SHP:{_from:"1976-01-01"}}],AD:[{ESP:{_from:"1873-01-01",_to:"2002-02-28"}},{ADP:{_from:"1936-01-01",_to:"2001-12-31"}},{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],AE:[{AED:{_from:"1973-05-19"}}],AF:[{AFA:{_from:"1927-03-14",_to:"2002-12-31"}},{AFN:{_from:"2002-10-07"}}],AG:[{XCD:{_from:"1965-10-06"}}],AI:[{XCD:{_from:"1965-10-06"}}],AL:[{ALK:{_from:"1946-11-01",_to:"1965-08-16"}},{ALL:{_from:"1965-08-16"}}],AM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-22"}},{AMD:{_from:"1993-11-22"}}],AO:[{AOK:{_from:"1977-01-08",_to:"1991-03-01"}},{AON:{_from:"1990-09-25",_to:"2000-02-01"}},{AOR:{_from:"1995-07-01",_to:"2000-02-01"}},{AOA:{_from:"1999-12-13"}}],AQ:[{XXX:{_tender:"false"}}],AR:[{ARM:{_from:"1881-11-05",_to:"1970-01-01"}},{ARL:{_from:"1970-01-01",_to:"1983-06-01"}},{ARP:{_from:"1983-06-01",_to:"1985-06-14"}},{ARA:{_from:"1985-06-14",_to:"1992-01-01"}},{ARS:{_from:"1992-01-01"}}],AS:[{USD:{_from:"1904-07-16"}}],AT:[{ATS:{_from:"1947-12-04",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],AU:[{AUD:{_from:"1966-02-14"}}],AW:[{ANG:{_from:"1940-05-10",_to:"1986-01-01"}},{AWG:{_from:"1986-01-01"}}],AX:[{EUR:{_from:"1999-01-01"}}],AZ:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-01-01"}},{AZM:{_from:"1993-11-22",_to:"2006-12-31"}},{AZN:{_from:"2006-01-01"}}],BA:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-01"}},{YUR:{_from:"1992-07-01",_to:"1993-10-01"}},{BAD:{_from:"1992-07-01",_to:"1994-08-15"}},{BAN:{_from:"1994-08-15",_to:"1997-07-01"}},{BAM:{_from:"1995-01-01"}}],BB:[{XCD:{_from:"1965-10-06",_to:"1973-12-03"}},{BBD:{_from:"1973-12-03"}}],BD:[{INR:{_from:"1835-08-17",_to:"1948-04-01"}},{PKR:{_from:"1948-04-01",_to:"1972-01-01"}},{BDT:{_from:"1972-01-01"}}],BE:[{NLG:{_from:"1816-12-15",_to:"1831-02-07"}},{BEF:{_from:"1831-02-07",_to:"2002-02-28"}},{BEC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{BEL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],BF:[{XOF:{_from:"1984-08-04"}}],BG:[{BGO:{_from:"1879-07-08",_to:"1952-05-12"}},{BGM:{_from:"1952-05-12",_to:"1962-01-01"}},{BGL:{_from:"1962-01-01",_to:"1999-07-05"}},{BGN:{_from:"1999-07-05"}}],BH:[{BHD:{_from:"1965-10-16"}}],BI:[{BIF:{_from:"1964-05-19"}}],BJ:[{XOF:{_from:"1975-11-30"}}],BL:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],BM:[{BMD:{_from:"1970-02-06"}}],BN:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{BND:{_from:"1967-06-12"}}],BO:[{BOV:{_tender:"false"}},{BOL:{_from:"1863-06-23",_to:"1963-01-01"}},{BOP:{_from:"1963-01-01",_to:"1986-12-31"}},{BOB:{_from:"1987-01-01"}}],BQ:[{ANG:{_from:"2010-10-10",_to:"2011-01-01"}},{USD:{_from:"2011-01-01"}}],BR:[{BRZ:{_from:"1942-11-01",_to:"1967-02-13"}},{BRB:{_from:"1967-02-13",_to:"1986-02-28"}},{BRC:{_from:"1986-02-28",_to:"1989-01-15"}},{BRN:{_from:"1989-01-15",_to:"1990-03-16"}},{BRE:{_from:"1990-03-16",_to:"1993-08-01"}},{BRR:{_from:"1993-08-01",_to:"1994-07-01"}},{BRL:{_from:"1994-07-01"}}],BS:[{BSD:{_from:"1966-05-25"}}],BT:[{INR:{_from:"1907-01-01"}},{BTN:{_from:"1974-04-16"}}],BU:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}}],BV:[{NOK:{_from:"1905-06-07"}}],BW:[{ZAR:{_from:"1961-02-14",_to:"1976-08-23"}},{BWP:{_from:"1976-08-23"}}],BY:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-11-08"}},{BYB:{_from:"1994-08-01",_to:"2000-12-31"}},{BYR:{_from:"2000-01-01",_to:"2017-01-01"}},{BYN:{_from:"2016-07-01"}}],BZ:[{BZD:{_from:"1974-01-01"}}],CA:[{CAD:{_from:"1858-01-01"}}],CC:[{AUD:{_from:"1966-02-14"}}],CD:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-01"}},{CDF:{_from:"1998-07-01"}}],CF:[{XAF:{_from:"1993-01-01"}}],CG:[{XAF:{_from:"1993-01-01"}}],CH:[{CHE:{_tender:"false"}},{CHW:{_tender:"false"}},{CHF:{_from:"1799-03-17"}}],CI:[{XOF:{_from:"1958-12-04"}}],CK:[{NZD:{_from:"1967-07-10"}}],CL:[{CLF:{_tender:"false"}},{CLE:{_from:"1960-01-01",_to:"1975-09-29"}},{CLP:{_from:"1975-09-29"}}],CM:[{XAF:{_from:"1973-04-01"}}],CN:[{CNY:{_from:"1953-03-01"}},{CNX:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{CNH:{_tender:"false",_from:"2010-07-19"}}],CO:[{COU:{_tender:"false"}},{COP:{_from:"1905-01-01"}}],CP:[{XXX:{_tender:"false"}}],CR:[{CRC:{_from:"1896-10-26"}}],CS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-06-03"}},{EUR:{_from:"2003-02-04",_to:"2006-06-03"}}],CU:[{CUP:{_from:"1859-01-01"}},{USD:{_from:"1899-01-01",_to:"1959-01-01"}},{CUC:{_from:"1994-01-01"}}],CV:[{PTE:{_from:"1911-05-22",_to:"1975-07-05"}},{CVE:{_from:"1914-01-01"}}],CW:[{ANG:{_from:"2010-10-10"}}],CX:[{AUD:{_from:"1966-02-14"}}],CY:[{CYP:{_from:"1914-09-10",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],CZ:[{CSK:{_from:"1953-06-01",_to:"1993-03-01"}},{CZK:{_from:"1993-01-01"}}],DD:[{DDM:{_from:"1948-07-20",_to:"1990-10-02"}}],DE:[{DEM:{_from:"1948-06-20",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],DG:[{USD:{_from:"1965-11-08"}}],DJ:[{DJF:{_from:"1977-06-27"}}],DK:[{DKK:{_from:"1873-05-27"}}],DM:[{XCD:{_from:"1965-10-06"}}],DO:[{USD:{_from:"1905-06-21",_to:"1947-10-01"}},{DOP:{_from:"1947-10-01"}}],DZ:[{DZD:{_from:"1964-04-01"}}],EA:[{EUR:{_from:"1999-01-01"}}],EC:[{ECS:{_from:"1884-04-01",_to:"2000-10-02"}},{ECV:{_tender:"false",_from:"1993-05-23",_to:"2000-01-09"}},{USD:{_from:"2000-10-02"}}],EE:[{SUR:{_from:"1961-01-01",_to:"1992-06-20"}},{EEK:{_from:"1992-06-21",_to:"2010-12-31"}},{EUR:{_from:"2011-01-01"}}],EG:[{EGP:{_from:"1885-11-14"}}],EH:[{MAD:{_from:"1976-02-26"}}],ER:[{ETB:{_from:"1993-05-24",_to:"1997-11-08"}},{ERN:{_from:"1997-11-08"}}],ES:[{ESP:{_from:"1868-10-19",_to:"2002-02-28"}},{ESB:{_tender:"false",_from:"1975-01-01",_to:"1994-12-31"}},{ESA:{_tender:"false",_from:"1978-01-01",_to:"1981-12-31"}},{EUR:{_from:"1999-01-01"}}],ET:[{ETB:{_from:"1976-09-15"}}],EU:[{XEU:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{EUR:{_from:"1999-01-01"}}],FI:[{FIM:{_from:"1963-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],FJ:[{FJD:{_from:"1969-01-13"}}],FK:[{FKP:{_from:"1901-01-01"}}],FM:[{JPY:{_from:"1914-10-03",_to:"1944-01-01"}},{USD:{_from:"1944-01-01"}}],FO:[{DKK:{_from:"1948-01-01"}}],FR:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GA:[{XAF:{_from:"1993-01-01"}}],GB:[{GBP:{_from:"1694-07-27"}}],GD:[{XCD:{_from:"1967-02-27"}}],GE:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-06-11"}},{GEK:{_from:"1993-04-05",_to:"1995-09-25"}},{GEL:{_from:"1995-09-23"}}],GF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GG:[{GBP:{_from:"1830-01-01"}}],GH:[{GHC:{_from:"1979-03-09",_to:"2007-12-31"}},{GHS:{_from:"2007-07-03"}}],GI:[{GIP:{_from:"1713-01-01"}}],GL:[{DKK:{_from:"1873-05-27"}}],GM:[{GMD:{_from:"1971-07-01"}}],GN:[{GNS:{_from:"1972-10-02",_to:"1986-01-06"}},{GNF:{_from:"1986-01-06"}}],GP:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GQ:[{GQE:{_from:"1975-07-07",_to:"1986-06-01"}},{XAF:{_from:"1993-01-01"}}],GR:[{GRD:{_from:"1954-05-01",_to:"2002-02-28"}},{EUR:{_from:"2001-01-01"}}],GS:[{GBP:{_from:"1908-01-01"}}],GT:[{GTQ:{_from:"1925-05-27"}}],GU:[{USD:{_from:"1944-08-21"}}],GW:[{GWE:{_from:"1914-01-01",_to:"1976-02-28"}},{GWP:{_from:"1976-02-28",_to:"1997-03-31"}},{XOF:{_from:"1997-03-31"}}],GY:[{GYD:{_from:"1966-05-26"}}],HK:[{HKD:{_from:"1895-02-02"}}],HM:[{AUD:{_from:"1967-02-16"}}],HN:[{HNL:{_from:"1926-04-03"}}],HR:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1991-12-23"}},{HRD:{_from:"1991-12-23",_to:"1995-01-01"}},{HRK:{_from:"1994-05-30"}}],HT:[{HTG:{_from:"1872-08-26"}},{USD:{_from:"1915-01-01"}}],HU:[{HUF:{_from:"1946-07-23"}}],IC:[{EUR:{_from:"1999-01-01"}}],ID:[{IDR:{_from:"1965-12-13"}}],IE:[{GBP:{_from:"1800-01-01",_to:"1922-01-01"}},{IEP:{_from:"1922-01-01",_to:"2002-02-09"}},{EUR:{_from:"1999-01-01"}}],IL:[{ILP:{_from:"1948-08-16",_to:"1980-02-22"}},{ILR:{_from:"1980-02-22",_to:"1985-09-04"}},{ILS:{_from:"1985-09-04"}}],IM:[{GBP:{_from:"1840-01-03"}}],IN:[{INR:{_from:"1835-08-17"}}],IO:[{USD:{_from:"1965-11-08"}}],IQ:[{EGP:{_from:"1920-11-11",_to:"1931-04-19"}},{INR:{_from:"1920-11-11",_to:"1931-04-19"}},{IQD:{_from:"1931-04-19"}}],IR:[{IRR:{_from:"1932-05-13"}}],IS:[{DKK:{_from:"1873-05-27",_to:"1918-12-01"}},{ISJ:{_from:"1918-12-01",_to:"1981-01-01"}},{ISK:{_from:"1981-01-01"}}],IT:[{ITL:{_from:"1862-08-24",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],JE:[{GBP:{_from:"1837-01-01"}}],JM:[{JMD:{_from:"1969-09-08"}}],JO:[{JOD:{_from:"1950-07-01"}}],JP:[{JPY:{_from:"1871-06-01"}}],KE:[{KES:{_from:"1966-09-14"}}],KG:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-05-10"}},{KGS:{_from:"1993-05-10"}}],KH:[{KHR:{_from:"1980-03-20"}}],KI:[{AUD:{_from:"1966-02-14"}}],KM:[{KMF:{_from:"1975-07-06"}}],KN:[{XCD:{_from:"1965-10-06"}}],KP:[{KPW:{_from:"1959-04-17"}}],KR:[{KRO:{_from:"1945-08-15",_to:"1953-02-15"}},{KRH:{_from:"1953-02-15",_to:"1962-06-10"}},{KRW:{_from:"1962-06-10"}}],KW:[{KWD:{_from:"1961-04-01"}}],KY:[{JMD:{_from:"1969-09-08",_to:"1971-01-01"}},{KYD:{_from:"1971-01-01"}}],KZ:[{KZT:{_from:"1993-11-05"}}],LA:[{LAK:{_from:"1979-12-10"}}],LB:[{LBP:{_from:"1948-02-02"}}],LC:[{XCD:{_from:"1965-10-06"}}],LI:[{CHF:{_from:"1921-02-01"}}],LK:[{LKR:{_from:"1978-05-22"}}],LR:[{LRD:{_from:"1944-01-01"}}],LS:[{ZAR:{_from:"1961-02-14"}},{LSL:{_from:"1980-01-22"}}],LT:[{SUR:{_from:"1961-01-01",_to:"1992-10-01"}},{LTT:{_from:"1992-10-01",_to:"1993-06-25"}},{LTL:{_from:"1993-06-25",_to:"2014-12-31"}},{EUR:{_from:"2015-01-01"}}],LU:[{LUF:{_from:"1944-09-04",_to:"2002-02-28"}},{LUC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{LUL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],LV:[{SUR:{_from:"1961-01-01",_to:"1992-07-20"}},{LVR:{_from:"1992-05-07",_to:"1993-10-17"}},{LVL:{_from:"1993-06-28",_to:"2013-12-31"}},{EUR:{_from:"2014-01-01"}}],LY:[{LYD:{_from:"1971-09-01"}}],MA:[{MAF:{_from:"1881-01-01",_to:"1959-10-17"}},{MAD:{_from:"1959-10-17"}}],MC:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{MCF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MD:[{MDC:{_from:"1992-06-01",_to:"1993-11-29"}},{MDL:{_from:"1993-11-29"}}],ME:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{DEM:{_from:"1999-10-02",_to:"2002-05-15"}},{EUR:{_from:"2002-01-01"}}],MF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MG:[{MGF:{_from:"1963-07-01",_to:"2004-12-31"}},{MGA:{_from:"1983-11-01"}}],MH:[{USD:{_from:"1944-01-01"}}],MK:[{MKN:{_from:"1992-04-26",_to:"1993-05-20"}},{MKD:{_from:"1993-05-20"}}],ML:[{XOF:{_from:"1958-11-24",_to:"1962-07-02"}},{MLF:{_from:"1962-07-02",_to:"1984-08-31"}},{XOF:{_from:"1984-06-01"}}],MM:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}},{MMK:{_from:"1989-06-18"}}],MN:[{MNT:{_from:"1915-03-01"}}],MO:[{MOP:{_from:"1901-01-01"}}],MP:[{USD:{_from:"1944-01-01"}}],MQ:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MR:[{XOF:{_from:"1958-11-28",_to:"1973-06-29"}},{MRO:{_from:"1973-06-29",_to:"2018-06-30"}},{MRU:{_from:"2018-01-01"}}],MS:[{XCD:{_from:"1967-02-27"}}],MT:[{MTP:{_from:"1914-08-13",_to:"1968-06-07"}},{MTL:{_from:"1968-06-07",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],MU:[{MUR:{_from:"1934-04-01"}}],MV:[{MVP:{_from:"1947-01-01",_to:"1981-07-01"}},{MVR:{_from:"1981-07-01"}}],MW:[{MWK:{_from:"1971-02-15"}}],MX:[{MXV:{_tender:"false"}},{MXP:{_from:"1822-01-01",_to:"1992-12-31"}},{MXN:{_from:"1993-01-01"}}],MY:[{MYR:{_from:"1963-09-16"}}],MZ:[{MZE:{_from:"1975-06-25",_to:"1980-06-16"}},{MZM:{_from:"1980-06-16",_to:"2006-12-31"}},{MZN:{_from:"2006-07-01"}}],NA:[{ZAR:{_from:"1961-02-14"}},{NAD:{_from:"1993-01-01"}}],NC:[{XPF:{_from:"1985-01-01"}}],NE:[{XOF:{_from:"1958-12-19"}}],NF:[{AUD:{_from:"1966-02-14"}}],NG:[{NGN:{_from:"1973-01-01"}}],NI:[{NIC:{_from:"1988-02-15",_to:"1991-04-30"}},{NIO:{_from:"1991-04-30"}}],NL:[{NLG:{_from:"1813-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],NO:[{SEK:{_from:"1873-05-27",_to:"1905-06-07"}},{NOK:{_from:"1905-06-07"}}],NP:[{INR:{_from:"1870-01-01",_to:"1966-10-17"}},{NPR:{_from:"1933-01-01"}}],NR:[{AUD:{_from:"1966-02-14"}}],NU:[{NZD:{_from:"1967-07-10"}}],NZ:[{NZD:{_from:"1967-07-10"}}],OM:[{OMR:{_from:"1972-11-11"}}],PA:[{PAB:{_from:"1903-11-04"}},{USD:{_from:"1903-11-18"}}],PE:[{PES:{_from:"1863-02-14",_to:"1985-02-01"}},{PEI:{_from:"1985-02-01",_to:"1991-07-01"}},{PEN:{_from:"1991-07-01"}}],PF:[{XPF:{_from:"1945-12-26"}}],PG:[{AUD:{_from:"1966-02-14",_to:"1975-09-16"}},{PGK:{_from:"1975-09-16"}}],PH:[{PHP:{_from:"1946-07-04"}}],PK:[{INR:{_from:"1835-08-17",_to:"1947-08-15"}},{PKR:{_from:"1948-04-01"}}],PL:[{PLZ:{_from:"1950-10-28",_to:"1994-12-31"}},{PLN:{_from:"1995-01-01"}}],PM:[{FRF:{_from:"1972-12-21",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],PN:[{NZD:{_from:"1969-01-13"}}],PR:[{ESP:{_from:"1800-01-01",_to:"1898-12-10"}},{USD:{_from:"1898-12-10"}}],PS:[{JOD:{_from:"1950-07-01",_to:"1967-06-01"}},{ILP:{_from:"1967-06-01",_to:"1980-02-22"}},{ILS:{_from:"1985-09-04"}},{JOD:{_from:"1996-02-12"}}],PT:[{PTE:{_from:"1911-05-22",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],PW:[{USD:{_from:"1944-01-01"}}],PY:[{PYG:{_from:"1943-11-01"}}],QA:[{QAR:{_from:"1973-05-19"}}],RE:[{FRF:{_from:"1975-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],RO:[{ROL:{_from:"1952-01-28",_to:"2006-12-31"}},{RON:{_from:"2005-07-01"}}],RS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-10-25"}},{RSD:{_from:"2006-10-25"}}],RU:[{RUR:{_from:"1991-12-25",_to:"1998-12-31"}},{RUB:{_from:"1999-01-01"}}],RW:[{RWF:{_from:"1964-05-19"}}],SA:[{SAR:{_from:"1952-10-22"}}],SB:[{AUD:{_from:"1966-02-14",_to:"1978-06-30"}},{SBD:{_from:"1977-10-24"}}],SC:[{SCR:{_from:"1903-11-01"}}],SD:[{EGP:{_from:"1889-01-19",_to:"1958-01-01"}},{GBP:{_from:"1889-01-19",_to:"1958-01-01"}},{SDP:{_from:"1957-04-08",_to:"1998-06-01"}},{SDD:{_from:"1992-06-08",_to:"2007-06-30"}},{SDG:{_from:"2007-01-10"}}],SE:[{SEK:{_from:"1873-05-27"}}],SG:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{SGD:{_from:"1967-06-12"}}],SH:[{SHP:{_from:"1917-02-15"}}],SI:[{SIT:{_from:"1992-10-07",_to:"2007-01-14"}},{EUR:{_from:"2007-01-01"}}],SJ:[{NOK:{_from:"1905-06-07"}}],SK:[{CSK:{_from:"1953-06-01",_to:"1992-12-31"}},{SKK:{_from:"1992-12-31",_to:"2009-01-01"}},{EUR:{_from:"2009-01-01"}}],SL:[{GBP:{_from:"1808-11-30",_to:"1966-02-04"}},{SLL:{_from:"1964-08-04"}}],SM:[{ITL:{_from:"1865-12-23",_to:"2001-02-28"}},{EUR:{_from:"1999-01-01"}}],SN:[{XOF:{_from:"1959-04-04"}}],SO:[{SOS:{_from:"1960-07-01"}}],SR:[{NLG:{_from:"1815-11-20",_to:"1940-05-10"}},{SRG:{_from:"1940-05-10",_to:"2003-12-31"}},{SRD:{_from:"2004-01-01"}}],SS:[{SDG:{_from:"2007-01-10",_to:"2011-09-01"}},{SSP:{_from:"2011-07-18"}}],ST:[{STD:{_from:"1977-09-08",_to:"2017-12-31"}},{STN:{_from:"2018-01-01"}}],SU:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}}],SV:[{SVC:{_from:"1919-11-11",_to:"2001-01-01"}},{USD:{_from:"2001-01-01"}}],SX:[{ANG:{_from:"2010-10-10"}}],SY:[{SYP:{_from:"1948-01-01"}}],SZ:[{SZL:{_from:"1974-09-06"}}],TA:[{GBP:{_from:"1938-01-12"}}],TC:[{USD:{_from:"1969-09-08"}}],TD:[{XAF:{_from:"1993-01-01"}}],TF:[{FRF:{_from:"1959-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],TG:[{XOF:{_from:"1958-11-28"}}],TH:[{THB:{_from:"1928-04-15"}}],TJ:[{RUR:{_from:"1991-12-25",_to:"1995-05-10"}},{TJR:{_from:"1995-05-10",_to:"2000-10-25"}},{TJS:{_from:"2000-10-26"}}],TK:[{NZD:{_from:"1967-07-10"}}],TL:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}},{USD:{_from:"1999-10-20"}}],TM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-01"}},{TMM:{_from:"1993-11-01",_to:"2009-01-01"}},{TMT:{_from:"2009-01-01"}}],TN:[{TND:{_from:"1958-11-01"}}],TO:[{TOP:{_from:"1966-02-14"}}],TP:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}}],TR:[{TRL:{_from:"1922-11-01",_to:"2005-12-31"}},{TRY:{_from:"2005-01-01"}}],TT:[{TTD:{_from:"1964-01-01"}}],TV:[{AUD:{_from:"1966-02-14"}}],TW:[{TWD:{_from:"1949-06-15"}}],TZ:[{TZS:{_from:"1966-06-14"}}],UA:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1992-11-13"}},{UAK:{_from:"1992-11-13",_to:"1993-10-17"}},{UAH:{_from:"1996-09-02"}}],UG:[{UGS:{_from:"1966-08-15",_to:"1987-05-15"}},{UGX:{_from:"1987-05-15"}}],UM:[{USD:{_from:"1944-01-01"}}],US:[{USN:{_tender:"false"}},{USS:{_tender:"false",_to:"2014-03-01"}},{USD:{_from:"1792-01-01"}}],UY:[{UYI:{_tender:"false"}},{UYW:{_tender:"false"}},{UYP:{_from:"1975-07-01",_to:"1993-03-01"}},{UYU:{_from:"1993-03-01"}}],UZ:[{UZS:{_from:"1994-07-01"}}],VA:[{ITL:{_from:"1870-10-19",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],VC:[{XCD:{_from:"1965-10-06"}}],VE:[{VEB:{_from:"1871-05-11",_to:"2008-06-30"}},{VEF:{_from:"2008-01-01",_to:"2018-08-20"}},{VES:{_from:"2018-08-20"}}],VG:[{USD:{_from:"1833-01-01"}},{GBP:{_from:"1833-01-01",_to:"1959-01-01"}}],VI:[{USD:{_from:"1837-01-01"}}],VN:[{VNN:{_from:"1978-05-03",_to:"1985-09-14"}},{VND:{_from:"1985-09-14"}}],VU:[{VUV:{_from:"1981-01-01"}}],WF:[{XPF:{_from:"1961-07-30"}}],WS:[{WST:{_from:"1967-07-10"}}],XK:[{YUM:{_from:"1994-01-24",_to:"1999-09-30"}},{DEM:{_from:"1999-09-01",_to:"2002-03-09"}},{EUR:{_from:"2002-01-01"}}],YD:[{YDD:{_from:"1965-04-01",_to:"1996-01-01"}}],YE:[{YER:{_from:"1990-05-22"}}],YT:[{KMF:{_from:"1975-01-01",_to:"1976-02-23"}},{FRF:{_from:"1976-02-23",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],YU:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-24"}},{YUM:{_from:"1994-01-24",_to:"2002-05-15"}}],ZA:[{ZAR:{_from:"1961-02-14"}},{ZAL:{_tender:"false",_from:"1985-09-01",_to:"1995-03-13"}}],ZM:[{ZMK:{_from:"1968-01-16",_to:"2013-01-01"}},{ZMW:{_from:"2013-01-01"}}],ZR:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-31"}}],ZW:[{RHD:{_from:"1970-02-17",_to:"1980-04-18"}},{ZWD:{_from:"1980-04-18",_to:"2008-08-01"}},{ZWR:{_from:"2008-08-01",_to:"2009-02-02"}},{ZWL:{_from:"2009-02-02",_to:"2009-04-12"}},{USD:{_from:"2009-04-12"}}],ZZ:[{XAG:{_tender:"false"}},{XAU:{_tender:"false"}},{XBA:{_tender:"false"}},{XBB:{_tender:"false"}},{XBC:{_tender:"false"}},{XBD:{_tender:"false"}},{XDR:{_tender:"false"}},{XPD:{_tender:"false"}},{XPT:{_tender:"false"}},{XSU:{_tender:"false"}},{XTS:{_tender:"false"}},{XUA:{_tender:"false"}},{XXX:{_tender:"false"}},{XRE:{_tender:"false",_to:"1999-11-30"}},{XFU:{_tender:"false",_to:"2013-11-30"}},{XFO:{_tender:"false",_from:"1930-01-01",_to:"2003-04-01"}}]}}}},{main:{af:{identity:{version:{_cldrVersion:"36"},language:"af"},dates:{calendars:{gregorian:{months:{format:{abbreviated:{1:"Jan.",2:"Feb.",3:"Mrt.",4:"Apr.",5:"Mei",6:"Jun.",7:"Jul.",8:"Aug.",9:"Sep.",10:"Okt.",11:"Nov.",12:"Des."},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"Januarie",2:"Februarie",3:"Maart",4:"April",5:"Mei",6:"Junie",7:"Julie",8:"Augustus",9:"September",10:"Oktober",11:"November",12:"Desember"}},"stand-alone":{abbreviated:{1:"Jan.",2:"Feb.",3:"Mrt.",4:"Apr.",5:"Mei",6:"Jun.",7:"Jul.",8:"Aug.",9:"Sep.",10:"Okt.",11:"Nov.",12:"Des."},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"Januarie",2:"Februarie",3:"Maart",4:"April",5:"Mei",6:"Junie",7:"Julie",8:"Augustus",9:"September",10:"Oktober",11:"November",12:"Desember"}}},days:{format:{abbreviated:{sun:"So.",mon:"Ma.",tue:"Di.",wed:"Wo.",thu:"Do.",fri:"Vr.",sat:"Sa."},narrow:{sun:"S",mon:"M",tue:"D",wed:"W",thu:"D",fri:"V",sat:"S"},short:{sun:"So.",mon:"Ma.",tue:"Di.",wed:"Wo.",thu:"Do.",fri:"Vr.",sat:"Sa."},wide:{sun:"Sondag",mon:"Maandag",tue:"Dinsdag",wed:"Woensdag",thu:"Donderdag",fri:"Vrydag",sat:"Saterdag"}},"stand-alone":{abbreviated:{sun:"So.",mon:"Ma.",tue:"Di.",wed:"Wo.",thu:"Do.",fri:"Vr.",sat:"Sa."},narrow:{sun:"S",mon:"M",tue:"D",wed:"W",thu:"D",fri:"V",sat:"S"},short:{sun:"So.",mon:"Ma.",tue:"Di.",wed:"Wo.",thu:"Do.",fri:"Vr.",sat:"Sa."},wide:{sun:"Sondag",mon:"Maandag",tue:"Dinsdag",wed:"Woensdag",thu:"Donderdag",fri:"Vrydag",sat:"Saterdag"}}},quarters:{format:{abbreviated:{1:"K1",2:"K2",3:"K3",4:"K4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1ste kwartaal",2:"2de kwartaal",3:"3de kwartaal",4:"4de kwartaal"}},"stand-alone":{abbreviated:{1:"K1",2:"K2",3:"K3",4:"K4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1ste kwartaal",2:"2de kwartaal",3:"3de kwartaal",4:"4de kwartaal"}}},dayPeriods:{format:{abbreviated:{midnight:"middernag",am:"vm.",pm:"nm.",morning1:"die oggend",afternoon1:"die middag",evening1:"die aand",night1:"die nag"},narrow:{midnight:"mn",am:"v",pm:"n",morning1:"o",afternoon1:"m",evening1:"a",night1:"n"},wide:{midnight:"middernag",am:"vm.",pm:"nm.",morning1:"die oggend",afternoon1:"die middag",evening1:"die aand",night1:"die nag"}},"stand-alone":{abbreviated:{midnight:"middernag",am:"vm.",pm:"nm.",morning1:"oggend",afternoon1:"middag",evening1:"aand",night1:"nag"},narrow:{midnight:"mn",am:"v",pm:"n",morning1:"o",afternoon1:"m",evening1:"a",night1:"n"},wide:{midnight:"middernag",am:"vm.",pm:"nm.",morning1:"oggend",afternoon1:"middag",evening1:"aand",night1:"nag"}}},eras:{eraNames:{0:"voor Christus",1:"na Christus","0-alt-variant":"voor die gewone jaartelling","1-alt-variant":"gewone jaartelling"},eraAbbr:{0:"v.C.",1:"n.C.","0-alt-variant":"v.g.j.","1-alt-variant":"g.j."},eraNarrow:{0:"v.C.",1:"n.C.","0-alt-variant":"v.g.j.","1-alt-variant":"g.j."}},dateFormats:{full:"EEEE dd MMMM y",long:"dd MMMM y",medium:"dd MMM y",short:"y-MM-dd"},timeFormats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},dateTimeFormats:{full:"{1} {0}",long:"{1} {0}",medium:"{1} {0}",short:"{1} {0}",availableFormats:{Bh:"h B",Bhm:"hh:mm B",Bhms:"hh:mm:ss B",d:"d",E:"ccc",EBhm:"E hh:mm B",EBhms:"E hh:mm:ss B",Ed:"E d",Ehm:"E hh:mm a",EHm:"E HH:mm",Ehms:"E hh:mm:ss a",EHms:"E HH:mm:ss",Gy:"y G",GyMMM:"MMM y G",GyMMMd:"dd MMM y G",GyMMMEd:"E dd MMM y G",h:"h a",H:"HH",hm:"h:mm a",Hm:"HH:mm",hms:"h:mm:ss a",Hms:"HH:mm:ss",hmsv:"h:mm:ss a v",Hmsv:"HH:mm:ss v",hmv:"h:mm a v",Hmv:"HH:mm v",M:"L",Md:"dd-MM",MEd:"E d/M",MMM:"LLL",MMMd:"d MMM",MMMEd:"E d MMM",MMMMd:"d MMMM",MMMMEd:"E d MMMM","MMMMW-count-one":"'week' W 'van' MMMM","MMMMW-count-other":"'week' W 'van' MMMM",ms:"mm:ss",y:"y",yM:"MM-y",yMd:"y-MM-dd",yMEd:"E y-MM-dd",yMMM:"MMM y",yMMMd:"d MMM y",yMMMEd:"E d MMM y",yMMMM:"MMMM y",yQQQ:"QQQ y",yQQQQ:"QQQQ y","yw-count-one":"'week' w 'van' Y","yw-count-other":"'week' w 'van' Y"},appendItems:{Day:"{0} ({2}: {1})","Day-Of-Week":"{0} {1}",Era:"{1} {0}",Hour:"{0} ({2}: {1})",Minute:"{0} ({2}: {1})",Month:"{0} ({2}: {1})",Quarter:"{0} ({2}: {1})",Second:"{0} ({2}: {1})",Timezone:"{0} {1}",Week:"{0} ({2}: {1})",Year:"{1} {0}"},intervalFormats:{intervalFormatFallback:"{0} – {1}",Bh:{B:"h B – h B",h:"h–h B"},Bhm:{B:"hh:mm B – hh:mm B",h:"hh:mm B – hh:mm B",m:"hh:mm–hh:mm"},d:{d:"d–d"},Gy:{G:"y G – y G",y:"y – y G"},GyM:{G:"y-M GGGGG – y-M GGGGG",M:"y-M – y-M GGGGG",y:"y-M – y-M GGGGG"},GyMd:{d:"y-M-d – y-M-d GGGGG",G:"y-M-d GGGGG – y-M-d GGGGG",M:"y-M-d – y-M-d GGGGG",y:"y-M-d – y-M-d GGGGG"},GyMEd:{d:"E y-M-d – E y-M-d GGGGG",G:"E y-M-d GGGGG – E y-M-d GGGGG",M:"E y-M-d – E y-M-d GGGGG",y:"E y-M-d – E y-M-d GGGGG"},GyMMM:{G:"MMM y G – MMM y G",M:"MMM – MMM y G",y:"MMM y – MMM y G"},GyMMMd:{d:"d–d MMM y G",G:"d MMM y G – d MMM y G",M:"d MMM – d MMM y G",y:"d MMM y – d MMM y G"},GyMMMEd:{d:"E d MMM – E d MMM y G",G:"E d MMM y G – E d MMM y G",M:"E d MMM – E d MMM y G",y:"E d MMM y – E d MMM y G"},h:{a:"h a – h a",h:"h – h a"},H:{H:"HH–HH"},hm:{a:"h:mm a – h:mm a",h:"h:mm – h:mm a",m:"h:mm – h:mm a"},Hm:{H:"HH:mm–HH:mm",m:"HH:mm–HH:mm"},hmv:{a:"h:mm a – h:mm a v",h:"h:mm a – h:mm a v",m:"h:mm a – h:mm a v"},Hmv:{H:"HH:mm–HH:mm v",m:"HH:mm–HH:mm v"},hv:{a:"h a – h a v",h:"h – h a v"},Hv:{H:"HH–HH v"},M:{M:"M–M"},Md:{d:"d/M – d/M",M:"d/M – d/M"},MEd:{d:"E d/M – E d/M",M:"E d/M – E d/M"},MMM:{M:"MMM–MMM"},MMMd:{d:"d–d MMM",M:"d MMM – d MMM"},MMMEd:{d:"E d MMM – E d MMM",M:"E d MMM – E d MMM"},y:{y:"y–y"},yM:{M:"M/y – M/y",y:"M/y – M/y"},yMd:{d:"d/M/y – d/M/y",M:"d/M/y – d/M/y",y:"d/M/y – d/M/y"},yMEd:{d:"E d/M/y – E d/M/y",M:"E d/M/y – E d/M/y",y:"E d/M/y – E d/M/y"},yMMM:{M:"MMM–MMM y",y:"MMM y – MMM y"},yMMMd:{d:"d–d MMM y",M:"d MMM – d MMM y",y:"d MMM y – d MMM y"},yMMMEd:{d:"E d MMM – E d MMM y",M:"E d MMM – E d MMM y",y:"E d MMM y – E d MMM y"},yMMMM:{M:"MMMM – MMMM y",y:"MMMM y – MMMM y"}}}}}}}}},{main:{af:{identity:{version:{_cldrVersion:"36"},language:"af"},dates:{fields:{era:{displayName:"era"},"era-short":{displayName:"era"},"era-narrow":{displayName:"era"},year:{displayName:"jaar","relative-type--1":"verlede jaar","relative-type-0":"hierdie jaar","relative-type-1":"volgende jaar","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} jaar","relativeTimePattern-count-other":"oor {0} jaar"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} jaar gelede","relativeTimePattern-count-other":"{0} jaar gelede"}},"year-short":{displayName:"j.","relative-type--1":"verlede j.","relative-type-0":"hierdie j.","relative-type-1":"volgende j.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} j.","relativeTimePattern-count-other":"oor {0} j."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} j. gelede","relativeTimePattern-count-other":"{0} j. gelede"}},"year-narrow":{displayName:"j.","relative-type--1":"verlede j.","relative-type-0":"hierdie j.","relative-type-1":"volgende j.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} j.","relativeTimePattern-count-other":"oor {0} j."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} j. gelede","relativeTimePattern-count-other":"{0} j. gelede"}},quarter:{displayName:"kwartaal","relative-type--1":"verlede kwartaal","relative-type-0":"hierdie kwartaal","relative-type-1":"volgende kwartaal","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} kwartaal","relativeTimePattern-count-other":"oor {0} kwartale"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} kwartaal gelede","relativeTimePattern-count-other":"{0} kwartale gelede"}},"quarter-short":{displayName:"kw.","relative-type--1":"verlede kwartaal","relative-type-0":"hierdie kwartaal","relative-type-1":"volgende kwartaal","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} kw.","relativeTimePattern-count-other":"oor {0} kw."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} kw. gelede","relativeTimePattern-count-other":"{0} kw. gelede"}},"quarter-narrow":{displayName:"kw.","relative-type--1":"verlede kwartaal","relative-type-0":"hierdie kwartaal","relative-type-1":"volgende kwartaal","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} kw.","relativeTimePattern-count-other":"oor {0} kw."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} kw. gelede","relativeTimePattern-count-other":"{0} kw. gelede"}},month:{displayName:"maand","relative-type--1":"verlede maand","relative-type-0":"vandeesmaand","relative-type-1":"volgende maand","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} maand","relativeTimePattern-count-other":"oor {0} maande"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} maand gelede","relativeTimePattern-count-other":"{0} maande gelede"}},"month-short":{displayName:"md.","relative-type--1":"verlede md.","relative-type-0":"hierdie md.","relative-type-1":"volgende md.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} md.","relativeTimePattern-count-other":"oor {0} md."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} md. gelede","relativeTimePattern-count-other":"{0} md. gelede"}},"month-narrow":{displayName:"md.","relative-type--1":"verlede md.","relative-type-0":"hierdie md.","relative-type-1":"volgende md.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} md.","relativeTimePattern-count-other":"oor {0} md."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} md. gelede","relativeTimePattern-count-other":"{0} md. gelede"}},week:{displayName:"week","relative-type--1":"verlede week","relative-type-0":"hierdie week","relative-type-1":"volgende week","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} week","relativeTimePattern-count-other":"oor {0} weke"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} week gelede","relativeTimePattern-count-other":"{0} weke gelede"},relativePeriod:"die week van {0}"},"week-short":{displayName:"wk.","relative-type--1":"verlede w.","relative-type-0":"hierdie w.","relative-type-1":"volgende w.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} w.","relativeTimePattern-count-other":"oor {0} w."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} w. gelede","relativeTimePattern-count-other":"{0} w. gelede"},relativePeriod:"die week van {0}"},"week-narrow":{displayName:"wk.","relative-type--1":"verlede w.","relative-type-0":"hierdie w.","relative-type-1":"volgende w.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} w.","relativeTimePattern-count-other":"oor {0} w."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} w. gelede","relativeTimePattern-count-other":"{0} w. gelede"},relativePeriod:"die week van {0}"},weekOfMonth:{displayName:"week van maand"},"weekOfMonth-short":{displayName:"wk. v. md."},"weekOfMonth-narrow":{displayName:"wk. v. md."},day:{displayName:"dag","relative-type--2":"eergister","relative-type--1":"gister","relative-type-0":"vandag","relative-type-1":"môre","relative-type-2":"oormôre","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} dag","relativeTimePattern-count-other":"oor {0} dae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} dag gelede","relativeTimePattern-count-other":"{0} dae gelede"}},"day-short":{displayName:"d.","relative-type--2":"eergister","relative-type--1":"gister","relative-type-0":"vandag","relative-type-1":"môre","relative-type-2":"oormôre","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} dag","relativeTimePattern-count-other":"oor {0} dae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} dag gelede","relativeTimePattern-count-other":"{0} dae gelede"}},"day-narrow":{displayName:"d.","relative-type--2":"eergister","relative-type--1":"gister","relative-type-0":"vandag","relative-type-1":"môre","relative-type-2":"oormôre","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} dag","relativeTimePattern-count-other":"oor {0} dae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} dag gelede","relativeTimePattern-count-other":"{0} dae gelede"}},dayOfYear:{displayName:"dag van jaar"},"dayOfYear-short":{displayName:"dag van j."},"dayOfYear-narrow":{displayName:"dag van j."},weekday:{displayName:"dag van die week"},"weekday-short":{displayName:"dag van wk."},"weekday-narrow":{displayName:"dag van wk."},weekdayOfMonth:{displayName:"weekdag van die jaar"},"weekdayOfMonth-short":{displayName:"wk.-dag van md."},"weekdayOfMonth-narrow":{displayName:"wk.-dag van md."},sun:{"relative-type--1":"verlede Sondag","relative-type-0":"hierdie Sondag","relative-type-1":"volgende Sondag","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Sondag","relativeTimePattern-count-other":"oor {0} Sondae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Sondag gelede","relativeTimePattern-count-other":"{0} Sondae gelede"}},"sun-short":{"relative-type--1":"verlede So.","relative-type-0":"hierdie So.","relative-type-1":"volgende So.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} So.","relativeTimePattern-count-other":"oor {0} So."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} So. gelede","relativeTimePattern-count-other":"{0} So. gelede"}},"sun-narrow":{"relative-type--1":"verlede So.","relative-type-0":"hierdie So.","relative-type-1":"volgende So.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} So.","relativeTimePattern-count-other":"oor {0} So."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} So. gelede","relativeTimePattern-count-other":"{0} So. gelede"}},mon:{"relative-type--1":"verlede Maandag","relative-type-0":"hierdie Maandag","relative-type-1":"volgende Maandag","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Maandag","relativeTimePattern-count-other":"oor {0} Maandae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Maandag gelede","relativeTimePattern-count-other":"{0} Maandae gelede"}},"mon-short":{"relative-type--1":"verlede Ma.","relative-type-0":"hierdie Ma.","relative-type-1":"volgende Ma.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Ma.","relativeTimePattern-count-other":"oor {0} Ma."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Ma. gelede","relativeTimePattern-count-other":"{0} Ma. gelede"}},"mon-narrow":{"relative-type--1":"verlede Ma.","relative-type-0":"hierdie Ma.","relative-type-1":"volgende Ma.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Ma.","relativeTimePattern-count-other":"oor {0} Ma."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Ma. gelede","relativeTimePattern-count-other":"{0} Ma. gelede"}},tue:{"relative-type--1":"verlede Dinsdag","relative-type-0":"hierdie Dinsdag","relative-type-1":"volgende Dinsdag","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Dinsdag","relativeTimePattern-count-other":"oor {0} Dinsdae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Dinsdag gelede","relativeTimePattern-count-other":"{0} Dinsdae gelede"}},"tue-short":{"relative-type--1":"verlede Di.","relative-type-0":"hierdie Di.","relative-type-1":"volgende Di.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Di.","relativeTimePattern-count-other":"oor {0} Di."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Di. gelede","relativeTimePattern-count-other":"{0} Di. gelede"}},"tue-narrow":{"relative-type--1":"verlede Di.","relative-type-0":"dié Di.","relative-type-1":"volgende Di.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Di.","relativeTimePattern-count-other":"oor {0} Di."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Di. gelede","relativeTimePattern-count-other":"{0} Di. gelede"}},wed:{"relative-type--1":"verlede Woensdag","relative-type-0":"hierdie Woensdag","relative-type-1":"volgende Woensdag","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Woensdag","relativeTimePattern-count-other":"oor {0} Woensdae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Woensdag gelede","relativeTimePattern-count-other":"{0} Woensdae gelede"}},"wed-short":{"relative-type--1":"verlede Wo.","relative-type-0":"hierdie Wo.","relative-type-1":"volgende Wo.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Wo.","relativeTimePattern-count-other":"oor {0} Wo."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Wo. gelede","relativeTimePattern-count-other":"{0} Wo. gelede"}},"wed-narrow":{"relative-type--1":"verlede Wo.","relative-type-0":"dié Wo.","relative-type-1":"vlg. Wo.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Wo.","relativeTimePattern-count-other":"oor {0} Wo."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Wo. gelede","relativeTimePattern-count-other":"{0} Wo. gelede"}},thu:{"relative-type--1":"verlede Donderdag","relative-type-0":"hierdie Donderdag","relative-type-1":"volgende Donderdag","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Donderdag","relativeTimePattern-count-other":"oor {0} Donderdae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Donderdag gelede","relativeTimePattern-count-other":"{0} Donderdae gelede"}},"thu-short":{"relative-type--1":"verlede Do.","relative-type-0":"hierdie Do.","relative-type-1":"volgende Do.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Do.","relativeTimePattern-count-other":"oor {0} Do."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Do. gelede","relativeTimePattern-count-other":"{0} Do. gelede"}},"thu-narrow":{"relative-type--1":"verlede Do.","relative-type-0":"dié Do.","relative-type-1":"vlg. Do.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Do.","relativeTimePattern-count-other":"oor {0} Do."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Do. gelede","relativeTimePattern-count-other":"{0} Do. gelede"}},fri:{"relative-type--1":"verlede Vrydag","relative-type-0":"hierdie Vrydag","relative-type-1":"volgende Vrydag","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Vrydag","relativeTimePattern-count-other":"oor {0} Vrydae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Vrydag gelede","relativeTimePattern-count-other":"{0} Vrydae gelede"}},"fri-short":{"relative-type--1":"verlede Vr.","relative-type-0":"hierdie Vr.","relative-type-1":"vlg. Vr.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Vr.","relativeTimePattern-count-other":"oor {0} Vr."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Vr. gelede","relativeTimePattern-count-other":"{0} Vr. gelede"}},"fri-narrow":{"relative-type--1":"verlede Vr.","relative-type-0":"hierdie Vr.","relative-type-1":"volgende Vr.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Vr.","relativeTimePattern-count-other":"oor {0} Vr."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Vr. gelede","relativeTimePattern-count-other":"{0} Vr. gelede"}},sat:{"relative-type--1":"verlede Saterdag","relative-type-0":"hierdie Saterdag","relative-type-1":"volgende Saterdag","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Saterdag","relativeTimePattern-count-other":"oor {0} Saterdae"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Saterdag gelede","relativeTimePattern-count-other":"{0} Saterdae gelede"}},"sat-short":{"relative-type--1":"verlede Sa.","relative-type-0":"dié Sa.","relative-type-1":"volgende Sa.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Sa.","relativeTimePattern-count-other":"oor {0} Sa."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Sa. gelede","relativeTimePattern-count-other":"{0} Sa. gelede"}},"sat-narrow":{"relative-type--1":"verlede Sa.","relative-type-0":"hierdie Sa.","relative-type-1":"volgende Sa.","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} Sa.","relativeTimePattern-count-other":"oor {0} Sa."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Sa. gelede","relativeTimePattern-count-other":"{0} Sa. gelede"}},"dayperiod-short":{displayName:"vm./nm."},dayperiod:{displayName:"vm./nm."},"dayperiod-narrow":{displayName:"vm./nm."},hour:{displayName:"uur","relative-type-0":"hierdie uur","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} uur","relativeTimePattern-count-other":"oor {0} uur"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} uur gelede","relativeTimePattern-count-other":"{0} uur gelede"}},"hour-short":{displayName:"u.","relative-type-0":"hierdie uur","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} u.","relativeTimePattern-count-other":"oor {0} u."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} u. gelede","relativeTimePattern-count-other":"{0} u. gelede"}},"hour-narrow":{displayName:"u.","relative-type-0":"hierdie uur","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} u.","relativeTimePattern-count-other":"oor {0} u."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} u. gelede","relativeTimePattern-count-other":"{0} u. gelede"}},minute:{displayName:"minuut","relative-type-0":"hierdie minuut","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} minuut","relativeTimePattern-count-other":"oor {0} minute"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} minuut gelede","relativeTimePattern-count-other":"{0} minute gelede"}},"minute-short":{displayName:"min.","relative-type-0":"hierdie minuut","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} min.","relativeTimePattern-count-other":"oor {0} min."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} min. gelede","relativeTimePattern-count-other":"{0} min. gelede"}},"minute-narrow":{displayName:"m.","relative-type-0":"hierdie minuut","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} min.","relativeTimePattern-count-other":"oor {0} min."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} min. gelede","relativeTimePattern-count-other":"{0} min. gelede"}},second:{displayName:"sekonde","relative-type-0":"nou","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} sekonde","relativeTimePattern-count-other":"oor {0} sekondes"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sekonde gelede","relativeTimePattern-count-other":"{0} sekondes gelede"}},"second-short":{displayName:"s.","relative-type-0":"nou","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} s.","relativeTimePattern-count-other":"oor {0} s."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} s. gelede","relativeTimePattern-count-other":"{0} s. gelede"}},"second-narrow":{displayName:"s.","relative-type-0":"nou","relativeTime-type-future":{"relativeTimePattern-count-one":"oor {0} s.","relativeTimePattern-count-other":"oor {0} s."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} s. gelede","relativeTimePattern-count-other":"{0} s. gelede"}},zone:{displayName:"tydsone"},"zone-short":{displayName:"sone"},"zone-narrow":{displayName:"sone"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},"plurals-type-cardinal":{af:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ak:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},am:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},an:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ar:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ars:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},as:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},asa:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ast:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},az:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},be:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..4 and n % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 2.0, 3.0, 4.0, 22.0, 23.0, 24.0, 32.0, 33.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 10 = 0 or n % 10 = 5..9 or n % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":"   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …"},bem:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bez:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bho:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bm:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},br:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11,71,91 @integer 1, 21, 31, 41, 51, 61, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 81.0, 101.0, 1001.0, …","pluralRule-count-two":"n % 10 = 2 and n % 100 != 12,72,92 @integer 2, 22, 32, 42, 52, 62, 82, 102, 1002, … @decimal 2.0, 22.0, 32.0, 42.0, 52.0, 62.0, 82.0, 102.0, 1002.0, …","pluralRule-count-few":"n % 10 = 3..4,9 and n % 100 != 10..19,70..79,90..99 @integer 3, 4, 9, 23, 24, 29, 33, 34, 39, 43, 44, 49, 103, 1003, … @decimal 3.0, 4.0, 9.0, 23.0, 24.0, 29.0, 33.0, 34.0, 103.0, 1003.0, …","pluralRule-count-many":"n != 0 and n % 1000000 = 0 @integer 1000000, … @decimal 1000000.0, 1000000.00, 1000000.000, …","pluralRule-count-other":" @integer 0, 5~8, 10~20, 100, 1000, 10000, 100000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, …"},brx:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bs:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ca:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ce:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ceb:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},cgg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},chr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ckb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},cs:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},cy:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3 @integer 3 @decimal 3.0, 3.00, 3.000, 3.0000","pluralRule-count-many":"n = 6 @integer 6 @decimal 6.0, 6.00, 6.000, 6.0000","pluralRule-count-other":" @integer 4, 5, 7~20, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},da:{"pluralRule-count-one":"n = 1 or t != 0 and i = 0,1 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0~3.4, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},de:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dv:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dz:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ee:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},el:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},en:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},es:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},et:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fa:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ff:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fil:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},fo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fr:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fur:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fy:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ga:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3..6 @integer 3~6 @decimal 3.0, 4.0, 5.0, 6.0, 3.00, 4.00, 5.00, 6.00, 3.000, 4.000, 5.000, 6.000, 3.0000, 4.0000, 5.0000, 6.0000","pluralRule-count-many":"n = 7..10 @integer 7~10 @decimal 7.0, 8.0, 9.0, 10.0, 7.00, 8.00, 9.00, 10.00, 7.000, 8.000, 9.000, 10.000, 7.0000, 8.0000, 9.0000, 10.0000","pluralRule-count-other":" @integer 0, 11~25, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gd:{"pluralRule-count-one":"n = 1,11 @integer 1, 11 @decimal 1.0, 11.0, 1.00, 11.00, 1.000, 11.000, 1.0000","pluralRule-count-two":"n = 2,12 @integer 2, 12 @decimal 2.0, 12.0, 2.00, 12.00, 2.000, 12.000, 2.0000","pluralRule-count-few":"n = 3..10,13..19 @integer 3~10, 13~19 @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 3.00","pluralRule-count-other":" @integer 0, 20~34, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gsw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},guw:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gv:{"pluralRule-count-one":"v = 0 and i % 10 = 1 @integer 1, 11, 21, 31, 41, 51, 61, 71, 101, 1001, …","pluralRule-count-two":"v = 0 and i % 10 = 2 @integer 2, 12, 22, 32, 42, 52, 62, 72, 102, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 0,20,40,60,80 @integer 0, 20, 40, 60, 80, 100, 120, 140, 1000, 10000, 100000, 1000000, …","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 3~10, 13~19, 23, 103, 1003, …"},ha:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},haw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},he:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hy:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ia:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},id:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ig:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ii:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},in:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},io:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},is:{"pluralRule-count-one":"t = 0 and i % 10 = 1 and i % 100 != 11 or t != 0 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1~1.6, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},it:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ja:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jbo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ji:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jmc:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jv:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jw:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ka:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kab:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kaj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kcg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kde:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kea:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kkj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kl:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},km:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ko:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ks:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksh:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ku:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kw:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n % 100 = 2,22,42,62,82 or n % 1000 = 0 and n % 100000 = 1000..20000,40000,60000,80000 or n != 0 and n % 1000000 = 100000 @integer 2, 22, 42, 62, 82, 102, 122, 142, 1000, 10000, 100000, … @decimal 2.0, 22.0, 42.0, 62.0, 82.0, 102.0, 122.0, 142.0, 1000.0, 10000.0, 100000.0, …","pluralRule-count-few":"n % 100 = 3,23,43,63,83 @integer 3, 23, 43, 63, 83, 103, 123, 143, 1003, … @decimal 3.0, 23.0, 43.0, 63.0, 83.0, 103.0, 123.0, 143.0, 1003.0, …","pluralRule-count-many":"n != 1 and n % 100 = 1,21,41,61,81 @integer 21, 41, 61, 81, 101, 121, 141, 161, 1001, … @decimal 21.0, 41.0, 61.0, 81.0, 101.0, 121.0, 141.0, 161.0, 1001.0, …","pluralRule-count-other":" @integer 4~19, 100, 1004, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.1, 1000000.0, …"},ky:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lag:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"i = 0,1 and n != 0 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lkt:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ln:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lt:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11..19 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..9 and n % 100 != 11..19 @integer 2~9, 22~29, 102, 1002, … @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 22.0, 102.0, 1002.0, …","pluralRule-count-many":"f != 0   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lv:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},mas:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mg:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.2~1.0, 1.2~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ml:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mo:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},mr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ms:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mt:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-few":"n = 0 or n % 100 = 2..10 @integer 0, 2~10, 102~107, 1002, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 100 = 11..19 @integer 11~19, 111~117, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},my:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nah:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},naq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ne:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nnh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},no:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nqo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nso:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ny:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nyn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},om:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},or:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},os:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},osa:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pap:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i != 1 and i % 10 = 0..1 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 12..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},prg:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},ps:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pt:{"pluralRule-count-one":"i = 0..1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},"pt-PT":{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rm:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ro:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},rof:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},root:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ru:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rwk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sah:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},saq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sc:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},scn:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sdh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},se:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},seh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ses:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sg:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sh:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},shi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-few":"n = 2..10 @integer 2~10 @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 8.00","pluralRule-count-other":" @integer 11~26, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~1.9, 2.1~2.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},si:{"pluralRule-count-one":"n = 0,1 or i = 0 and f = 1 @integer 0, 1 @decimal 0.0, 0.1, 1.0, 0.00, 0.01, 1.00, 0.000, 0.001, 1.000, 0.0000, 0.0001, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.2~0.9, 1.1~1.8, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sk:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sl:{"pluralRule-count-one":"v = 0 and i % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, …","pluralRule-count-two":"v = 0 and i % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or v != 0 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sma:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smi:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sms:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},so:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ss:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ssy:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},st:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},su:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sv:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},syr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ta:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},te:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},teo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},th:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ti:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tig:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tl:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},tn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},to:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ts:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tzm:{"pluralRule-count-one":"n = 0..1 or n = 11..99 @integer 0, 1, 11~24 @decimal 0.0, 1.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0","pluralRule-count-other":" @integer 2~10, 100~106, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ug:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ur:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uz:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ve:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vi:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vun:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wae:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xog:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yue:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zh:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"}}}},{main:{af:{identity:{version:{_cldrVersion:"36"},language:"af"},units:{long:{per:{compoundUnitPattern:"{0} per {1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"swaartekrag","unitPattern-count-one":"{0} swaartekrag","unitPattern-count-other":"{0} swaartekrag"},"acceleration-meter-per-second-squared":{displayName:"meter per vierkante sekonde","unitPattern-count-one":"{0} meter per vierkante sekonde","unitPattern-count-other":"{0} meter per vierkante sekonde"},"angle-revolution":{displayName:"omwenteling","unitPattern-count-one":"{0} omwenteling","unitPattern-count-other":"{0} omwentelings"},"angle-radian":{displayName:"boogmate","unitPattern-count-one":"{0} boogmaat","unitPattern-count-other":"{0} boogmate"},"angle-degree":{displayName:"grade","unitPattern-count-one":"{0} graad","unitPattern-count-other":"{0} grade"},"angle-arc-minute":{displayName:"boogminute","unitPattern-count-one":"{0} boogminuut","unitPattern-count-other":"{0} boogminute"},"angle-arc-second":{displayName:"boogsekondes","unitPattern-count-one":"{0} boogsekonde","unitPattern-count-other":"{0} boogsekondes"},"area-square-kilometer":{displayName:"vierkante kilometer","unitPattern-count-one":"{0} vierkante kilometer","unitPattern-count-other":"{0} vierkante kilometer",perUnitPattern:"{0} per vierkante kilometer"},"area-hectare":{displayName:"hektaar","unitPattern-count-one":"{0} hektaar","unitPattern-count-other":"{0} hektaar"},"area-square-meter":{displayName:"vierkante meter","unitPattern-count-one":"{0} vierkante meter","unitPattern-count-other":"{0} vierkante meter",perUnitPattern:"{0} per vierkante meter"},"area-square-centimeter":{displayName:"vierkante sentimeter","unitPattern-count-one":"{0} vierkante sentimeter","unitPattern-count-other":"{0} vierkante sentimeter",perUnitPattern:"{0} per vierkante sentimeter"},"area-square-mile":{displayName:"vierkante myl","unitPattern-count-one":"{0} vierkante myl","unitPattern-count-other":"{0} vierkante myl",perUnitPattern:"{0} per vierkante myl"},"area-acre":{displayName:"acre","unitPattern-count-one":"{0} acre","unitPattern-count-other":"{0} acre"},"area-square-yard":{displayName:"vierkante jaart","unitPattern-count-one":"{0} vierkante jaart","unitPattern-count-other":"{0} vierkante jaart"},"area-square-foot":{displayName:"vierkante voet","unitPattern-count-one":"{0} vierkante voet","unitPattern-count-other":"{0} vierkante voet"},"area-square-inch":{displayName:"vierkante duim","unitPattern-count-one":"{0} vierkante duim","unitPattern-count-other":"{0} vierkante duim",perUnitPattern:"{0} per vierkante duim"},"area-dunam":{displayName:"donum","unitPattern-count-one":"{0} donum","unitPattern-count-other":"{0} donum"},"concentr-karat":{displayName:"karaat","unitPattern-count-one":"{0} karaat","unitPattern-count-other":"{0} karaat"},"concentr-milligram-per-deciliter":{displayName:"milligram per desiliter","unitPattern-count-one":"{0} milligram per desiliter","unitPattern-count-other":"{0} milligram per desiliter"},"concentr-millimole-per-liter":{displayName:"millimol per liter","unitPattern-count-one":"{0} millimol per liter","unitPattern-count-other":"{0} millimol per liter"},"concentr-part-per-million":{displayName:"dele per miljoen","unitPattern-count-one":"{0} deel per miljoen","unitPattern-count-other":"{0} dele per miljoen"},"concentr-percent":{displayName:"persent","unitPattern-count-one":"{0} persent","unitPattern-count-other":"{0} persent"},"concentr-permille":{displayName:"per duisend","unitPattern-count-one":"{0} per duisend","unitPattern-count-other":"{0} per duisend"},"concentr-permyriad":{displayName:"per tienduisend","unitPattern-count-one":"{0} per tienduisend","unitPattern-count-other":"{0} per tienduisend"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"liter per kilometer","unitPattern-count-one":"{0} liter per kilometer","unitPattern-count-other":"{0} liter per kilometer"},"consumption-liter-per-100kilometers":{displayName:"liter per 100 kilometer","unitPattern-count-one":"{0} liter per 100 kilometer","unitPattern-count-other":"{0} liter per 100 kilometer"},"consumption-mile-per-gallon":{displayName:"myl per VSA-gelling","unitPattern-count-one":"{0} myl per VSA-gelling","unitPattern-count-other":"{0} myl per VSA-gelling"},"consumption-mile-per-gallon-imperial":{displayName:"myl per Britse gelling","unitPattern-count-one":"{0} myl per Britse gelling","unitPattern-count-other":"{0} myl per Britse gelling"},"digital-petabyte":{displayName:"petagreep","unitPattern-count-one":"{0} petagreep","unitPattern-count-other":"{0} petagreep"},"digital-terabyte":{displayName:"teragreep","unitPattern-count-one":"{0} teragreep","unitPattern-count-other":"{0} teragreep"},"digital-terabit":{displayName:"terabis","unitPattern-count-one":"{0} terabis","unitPattern-count-other":"{0} terabis"},"digital-gigabyte":{displayName:"gigagreep","unitPattern-count-one":"{0} gigagreep","unitPattern-count-other":"{0} gigagreep"},"digital-gigabit":{displayName:"gigabis","unitPattern-count-one":"{0} gigabis","unitPattern-count-other":"{0} gigabis"},"digital-megabyte":{displayName:"megagreep","unitPattern-count-one":"{0} megagreep","unitPattern-count-other":"{0} megagreep"},"digital-megabit":{displayName:"megabis","unitPattern-count-one":"{0} megabis","unitPattern-count-other":"{0} megabis"},"digital-kilobyte":{displayName:"kilogreep","unitPattern-count-one":"{0} kilogreep","unitPattern-count-other":"{0} kilogreep"},"digital-kilobit":{displayName:"kilobis","unitPattern-count-one":"{0} kilobis","unitPattern-count-other":"{0} kilobis"},"digital-byte":{displayName:"greep","unitPattern-count-one":"{0} greep","unitPattern-count-other":"{0} greep"},"digital-bit":{displayName:"bis","unitPattern-count-one":"{0} bis","unitPattern-count-other":"{0} bis"},"duration-century":{displayName:"eeu","unitPattern-count-one":"{0} eeu","unitPattern-count-other":"{0} eeue"},"duration-decade":{displayName:"dekades","unitPattern-count-one":"{0} dekade","unitPattern-count-other":"{0} dekades"},"duration-year":{displayName:"jaar","unitPattern-count-one":"{0} jaar","unitPattern-count-other":"{0} jaar",perUnitPattern:"{0} per jaar"},"duration-month":{displayName:"maande","unitPattern-count-one":"{0} maand","unitPattern-count-other":"{0} maande",perUnitPattern:"{0}/maand"},"duration-week":{displayName:"weke","unitPattern-count-one":"{0} week","unitPattern-count-other":"{0} weke",perUnitPattern:"{0} per week"},"duration-day":{displayName:"dae","unitPattern-count-one":"{0} dag","unitPattern-count-other":"{0} dae",perUnitPattern:"{0} per dag"},"duration-hour":{displayName:"uur","unitPattern-count-one":"{0} uur","unitPattern-count-other":"{0} uur",perUnitPattern:"{0} per uur"},"duration-minute":{displayName:"minute","unitPattern-count-one":"{0} minuut","unitPattern-count-other":"{0} minute",perUnitPattern:"{0} per minuut"},"duration-second":{displayName:"sekondes","unitPattern-count-one":"{0} sekonde","unitPattern-count-other":"{0} sekondes",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"millisekondes","unitPattern-count-one":"{0} millisekonde","unitPattern-count-other":"{0} millisekondes"},"duration-microsecond":{displayName:"mikrosekondes","unitPattern-count-one":"{0} mikrosekonde","unitPattern-count-other":"{0} mikrosekondes"},"duration-nanosecond":{displayName:"nanosekondes","unitPattern-count-one":"{0} nanosekonde","unitPattern-count-other":"{0} nanosekondes"},"electric-ampere":{displayName:"ampère","unitPattern-count-one":"{0} ampère","unitPattern-count-other":"{0} ampère"},"electric-milliampere":{displayName:"milliampère","unitPattern-count-one":"{0} milliampère","unitPattern-count-other":"{0} milliampère"},"electric-ohm":{displayName:"ohm","unitPattern-count-one":"{0} ohm","unitPattern-count-other":"{0} ohm"},"electric-volt":{displayName:"volt","unitPattern-count-one":"{0} volt","unitPattern-count-other":"{0} volt"},"energy-kilocalorie":{displayName:"kilokalorieë","unitPattern-count-one":"{0} kilokalorie","unitPattern-count-other":"{0} kilokalorieë"},"energy-calorie":{displayName:"kalorieë","unitPattern-count-one":"{0} kalorie","unitPattern-count-other":"{0} kalorieë"},"energy-foodcalorie":{displayName:"kilokalorieë","unitPattern-count-one":"{0} kilokalorie","unitPattern-count-other":"{0} kilokalorieë"},"energy-kilojoule":{displayName:"kilojoule","unitPattern-count-one":"{0} kilojoule","unitPattern-count-other":"{0} kilojoule"},"energy-joule":{displayName:"joule","unitPattern-count-one":"{0} joule","unitPattern-count-other":"{0} joule"},"energy-kilowatt-hour":{displayName:"kilowatt-uur","unitPattern-count-one":"{0} kilowatt-uur","unitPattern-count-other":"{0} kilowatt-uur"},"energy-electronvolt":{displayName:"elektronvolt","unitPattern-count-one":"{0} elektronvolt","unitPattern-count-other":"{0} elektronvolt"},"energy-british-thermal-unit":{displayName:"Britse termiese eenhede","unitPattern-count-one":"{0} Britse termiese eenheid","unitPattern-count-other":"{0} Britse termiese eenhede"},"energy-therm-us":{displayName:"VSA- termiese eenhede","unitPattern-count-one":"{0} VSA- termiese eenheid","unitPattern-count-other":"{0} VSA- termiese eenhede"},"force-pound-force":{displayName:"pondkrag","unitPattern-count-one":"{0} pondkrag","unitPattern-count-other":"{0} pondkrag"},"force-newton":{displayName:"newton","unitPattern-count-one":"{0} newton","unitPattern-count-other":"{0} newton"},"frequency-gigahertz":{displayName:"gigahertz","unitPattern-count-one":"{0} gigahertz","unitPattern-count-other":"{0} gigahertz"},"frequency-megahertz":{displayName:"megahertz","unitPattern-count-one":"{0} megahertz","unitPattern-count-other":"{0} megahertz"},"frequency-kilohertz":{displayName:"kilohertz","unitPattern-count-one":"{0} kilohertz","unitPattern-count-other":"{0} kilohertz"},"frequency-hertz":{displayName:"hertz","unitPattern-count-one":"{0} hertz","unitPattern-count-other":"{0} hertz"},"graphics-em":{displayName:"tipografiese em","unitPattern-count-one":"{0} em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"pieksels","unitPattern-count-one":"{0} pieksel","unitPattern-count-other":"{0} pieksels"},"graphics-megapixel":{displayName:"Mpx","unitPattern-count-one":"{0} Mpx","unitPattern-count-other":"{0} Mpx"},"graphics-pixel-per-centimeter":{displayName:"px/cm","unitPattern-count-one":"{0} px/cm","unitPattern-count-other":"{0} px/cm"},"graphics-pixel-per-inch":{displayName:"px/dm.","unitPattern-count-one":"{0} px/dm.","unitPattern-count-other":"{0} px/dm."},"graphics-dot-per-centimeter":{displayName:"stippels per sentimeter","unitPattern-count-one":"{0} stippel per sentimeter","unitPattern-count-other":"{0} stippels per sentimeter"},"graphics-dot-per-inch":{displayName:"stippels per duim","unitPattern-count-one":"{0} stippel per duim","unitPattern-count-other":"{0} stippels per duim"},"length-kilometer":{displayName:"kilometer","unitPattern-count-one":"{0} kilometer","unitPattern-count-other":"{0} kilometer",perUnitPattern:"{0} per kilometer"},"length-meter":{displayName:"meter","unitPattern-count-one":"{0} meter","unitPattern-count-other":"{0} meter",perUnitPattern:"{0} per meter"},"length-decimeter":{displayName:"desimeter","unitPattern-count-one":"{0} desimeter","unitPattern-count-other":"{0} desimeter"},"length-centimeter":{displayName:"sentimeter","unitPattern-count-one":"{0} sentimeter","unitPattern-count-other":"{0} sentimeter",perUnitPattern:"{0} per sentimeter"},"length-millimeter":{displayName:"millimeter","unitPattern-count-one":"{0} millimeter","unitPattern-count-other":"{0} millimeter"},"length-micrometer":{displayName:"mikrometer","unitPattern-count-one":"{0} mikrometer","unitPattern-count-other":"{0} mikrometer"},"length-nanometer":{displayName:"nanometer","unitPattern-count-one":"{0} nanometer","unitPattern-count-other":"{0} nanometer"},"length-picometer":{displayName:"pikometer","unitPattern-count-one":"{0} pikometer","unitPattern-count-other":"{0} pikometer"},"length-mile":{displayName:"myl","unitPattern-count-one":"{0} myl","unitPattern-count-other":"{0} myl"},"length-yard":{displayName:"jaart","unitPattern-count-one":"{0} jaart","unitPattern-count-other":"{0} jaart"},"length-foot":{displayName:"voet","unitPattern-count-one":"{0} voet","unitPattern-count-other":"{0} voet",perUnitPattern:"{0} per voet"},"length-inch":{displayName:"duim","unitPattern-count-one":"{0} duim","unitPattern-count-other":"{0} duim",perUnitPattern:"{0} per duim"},"length-parsec":{displayName:"parsek","unitPattern-count-one":"{0} parsek","unitPattern-count-other":"{0} parsek"},"length-light-year":{displayName:"ligjare","unitPattern-count-one":"{0} ligjaar","unitPattern-count-other":"{0} ligjare"},"length-astronomical-unit":{displayName:"astronomiese eenhede","unitPattern-count-one":"{0} astronomiese eenheid","unitPattern-count-other":"{0} astronomiese eenhede"},"length-furlong":{displayName:"fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"seemyl","unitPattern-count-one":"{0} seemyl","unitPattern-count-other":"{0} seemyl"},"length-mile-scandinavian":{displayName:"Skandinawiese myl","unitPattern-count-one":"{0} Skandinawiese myl","unitPattern-count-other":"{0} Skandinawiese myl"},"length-point":{displayName:"punte","unitPattern-count-one":"{0} punt","unitPattern-count-other":"{0} punte"},"length-solar-radius":{displayName:"sonradiusse","unitPattern-count-one":"{0} sonradius","unitPattern-count-other":"{0} sonradiusse"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lux","unitPattern-count-other":"{0} lux"},"light-solar-luminosity":{displayName:"sonligsterkte","unitPattern-count-one":"{0} sonligsterkte","unitPattern-count-other":"{0} sonligsterkte"},"mass-metric-ton":{displayName:"metrieke ton","unitPattern-count-one":"{0} metrieke ton","unitPattern-count-other":"{0} metrieke ton"},"mass-kilogram":{displayName:"kilogram","unitPattern-count-one":"{0} kilogram","unitPattern-count-other":"{0} kilogram",perUnitPattern:"{0} per kilogram"},"mass-gram":{displayName:"gram","unitPattern-count-one":"{0} gram","unitPattern-count-other":"{0} gram",perUnitPattern:"{0} per gram"},"mass-milligram":{displayName:"milligram","unitPattern-count-one":"{0} milligram","unitPattern-count-other":"{0} milligram"},"mass-microgram":{displayName:"mikrogram","unitPattern-count-one":"{0} mikrogram","unitPattern-count-other":"{0} mikrogram"},"mass-ton":{displayName:"VSA-ton","unitPattern-count-one":"{0} VSA-ton","unitPattern-count-other":"{0} VSA-ton"},"mass-stone":{displayName:"st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"pond","unitPattern-count-one":"{0} pond","unitPattern-count-other":"{0} pond",perUnitPattern:"{0} per pond"},"mass-ounce":{displayName:"onse","unitPattern-count-one":"{0} ons","unitPattern-count-other":"{0} onse",perUnitPattern:"{0} per ons"},"mass-ounce-troy":{displayName:"troy-onse","unitPattern-count-one":"{0} troy-ons","unitPattern-count-other":"{0} troy-onse"},"mass-carat":{displayName:"karaat","unitPattern-count-one":"{0} karaat","unitPattern-count-other":"{0} karaat"},"mass-dalton":{displayName:"dalton","unitPattern-count-one":"{0} dalton","unitPattern-count-other":"{0} dalton"},"mass-earth-mass":{displayName:"aardemassas","unitPattern-count-one":"{0} aardemassa","unitPattern-count-other":"{0} aardemassas"},"mass-solar-mass":{displayName:"sonmassas","unitPattern-count-one":"{0} sonmassa","unitPattern-count-other":"{0} sonmassas"},"power-gigawatt":{displayName:"gigawatt","unitPattern-count-one":"{0} gigawatt","unitPattern-count-other":"{0} gigawatt"},"power-megawatt":{displayName:"megawatt","unitPattern-count-one":"{0} megawatt","unitPattern-count-other":"{0} megawatt"},"power-kilowatt":{displayName:"kilowatt","unitPattern-count-one":"{0} kilowatt","unitPattern-count-other":"{0} kilowatt"},"power-watt":{displayName:"watt","unitPattern-count-one":"{0} watt","unitPattern-count-other":"{0} watt"},"power-milliwatt":{displayName:"milliwatt","unitPattern-count-one":"{0} milliwatt","unitPattern-count-other":"{0} milliwatt"},"power-horsepower":{displayName:"perdekrag","unitPattern-count-one":"{0} perdekrag","unitPattern-count-other":"{0} perdekrag"},"pressure-millimeter-of-mercury":{displayName:"millimeter kwik","unitPattern-count-one":"{0} millimeter kwik","unitPattern-count-other":"{0} millimeter kwik"},"pressure-pound-per-square-inch":{displayName:"pond per vierkante duim","unitPattern-count-one":"{0} pond per vierkante duim","unitPattern-count-other":"{0} pond per vierkante duim"},"pressure-inch-hg":{displayName:"duim kwik","unitPattern-count-one":"{0} duim kwik","unitPattern-count-other":"{0} duim kwik"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bar"},"pressure-millibar":{displayName:"millibar","unitPattern-count-one":"{0} millibar","unitPattern-count-other":"{0} millibar"},"pressure-atmosphere":{displayName:"atmosfere","unitPattern-count-one":"{0} atmosfeer","unitPattern-count-other":"{0} atmosfere"},"pressure-pascal":{displayName:"Pa","unitPattern-count-one":"{0} Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hektopascal","unitPattern-count-one":"{0} hektopascal","unitPattern-count-other":"{0} hektopascal"},"pressure-kilopascal":{displayName:"kilopascal","unitPattern-count-one":"{0} kilopascal","unitPattern-count-other":"{0} kilopascal"},"pressure-megapascal":{displayName:"megapascal","unitPattern-count-one":"{0} megapascal","unitPattern-count-other":"{0} megapascal"},"speed-kilometer-per-hour":{displayName:"kilometer per uur","unitPattern-count-one":"{0} kilometer per uur","unitPattern-count-other":"{0} kilometer per uur"},"speed-meter-per-second":{displayName:"meter per sekonde","unitPattern-count-one":"{0} meter per sekonde","unitPattern-count-other":"{0} meter per sekonde"},"speed-mile-per-hour":{displayName:"myl per uur","unitPattern-count-one":"{0} myl per uur","unitPattern-count-other":"{0} myl per uur"},"speed-knot":{displayName:"knoop","unitPattern-count-one":"{0} knoop","unitPattern-count-other":"{0} knope"},"temperature-generic":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"grade Celsius","unitPattern-count-one":"{0} graad Celsius","unitPattern-count-other":"{0} grade Celsius"},"temperature-fahrenheit":{displayName:"grade Fahrenheit","unitPattern-count-one":"{0} graad Fahrenheit","unitPattern-count-other":"{0} grade Fahrenheit"},"temperature-kelvin":{displayName:"kelvin","unitPattern-count-one":"{0} kelvin","unitPattern-count-other":"{0} kelvin"},"torque-pound-foot":{displayName:"pondvoet","unitPattern-count-one":"{0} pondvoet","unitPattern-count-other":"{0} pondvoet"},"torque-newton-meter":{displayName:"newtonmeter","unitPattern-count-one":"{0} newtonmeter","unitPattern-count-other":"{0} newtonmeter"},"volume-cubic-kilometer":{displayName:"kubieke kilometer","unitPattern-count-one":"{0} kubieke kilometer","unitPattern-count-other":"{0} kubieke kilometer"},"volume-cubic-meter":{displayName:"kubieke meter","unitPattern-count-one":"{0} kubieke meter","unitPattern-count-other":"{0} kubieke meter",perUnitPattern:"{0} per kubieke meter"},"volume-cubic-centimeter":{displayName:"kubieke sentimeter","unitPattern-count-one":"{0} kubieke sentimeter","unitPattern-count-other":"{0} kubieke sentimeter",perUnitPattern:"{0} per kubieke sentimeter"},"volume-cubic-mile":{displayName:"kubieke myl","unitPattern-count-one":"{0} kubieke myl","unitPattern-count-other":"{0} kubieke myl"},"volume-cubic-yard":{displayName:"kubieke jaart","unitPattern-count-one":"{0} kubieke jaart","unitPattern-count-other":"{0} kubieke jaart"},"volume-cubic-foot":{displayName:"kubieke voet","unitPattern-count-one":"{0} kubieke voet","unitPattern-count-other":"{0} kubieke voet"},"volume-cubic-inch":{displayName:"kubieke duim","unitPattern-count-one":"{0} kubieke duim","unitPattern-count-other":"{0} kubieke duim"},"volume-megaliter":{displayName:"megaliter","unitPattern-count-one":"{0} megaliter","unitPattern-count-other":"{0} megaliter"},"volume-hectoliter":{displayName:"hektoliter","unitPattern-count-one":"{0} hektoliter","unitPattern-count-other":"{0} hektoliter"},"volume-liter":{displayName:"liter","unitPattern-count-one":"{0} liter","unitPattern-count-other":"{0} liter",perUnitPattern:"{0} per liter"},"volume-deciliter":{displayName:"desiliter","unitPattern-count-one":"{0} desiliter","unitPattern-count-other":"{0} desiliter"},"volume-centiliter":{displayName:"sentiliter","unitPattern-count-one":"{0} sentiliter","unitPattern-count-other":"{0} sentiliter"},"volume-milliliter":{displayName:"milliliter","unitPattern-count-one":"{0} milliliter","unitPattern-count-other":"{0} milliliter"},"volume-pint-metric":{displayName:"metrieke pinte","unitPattern-count-one":"{0} metrieke pint","unitPattern-count-other":"{0} metrieke pinte"},"volume-cup-metric":{displayName:"metrieke koppies","unitPattern-count-one":"{0} metrieke koppie","unitPattern-count-other":"{0} metrieke koppies"},"volume-acre-foot":{displayName:"acre-voet","unitPattern-count-one":"{0} acre-voet","unitPattern-count-other":"{0} acre-voet"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gelling","unitPattern-count-one":"{0} gelling","unitPattern-count-other":"{0} gelling",perUnitPattern:"{0} per gelling"},"volume-gallon-imperial":{displayName:"Britse gelling","unitPattern-count-one":"{0} Britse gelling","unitPattern-count-other":"{0} Britse gelling",perUnitPattern:"{0}/Br. gell."},"volume-quart":{displayName:"VSA-kwartgellings","unitPattern-count-one":"{0} VSA-kwartgelling","unitPattern-count-other":"{0} VSA-kwartgellings"},"volume-pint":{displayName:"pinte","unitPattern-count-one":"{0} pint","unitPattern-count-other":"{0} pinte"},"volume-cup":{displayName:"koppies","unitPattern-count-one":"{0} koppie","unitPattern-count-other":"{0} koppies"},"volume-fluid-ounce":{displayName:"vloeistofons","unitPattern-count-one":"{0} vloeistofons","unitPattern-count-other":"{0} vloeistofons"},"volume-fluid-ounce-imperial":{displayName:"Britse vloeistofons","unitPattern-count-one":"{0} Britse vloeistofons","unitPattern-count-other":"{0} Britse vloeistofons"},"volume-tablespoon":{displayName:"eetlepels","unitPattern-count-one":"{0} eetlepel","unitPattern-count-other":"{0} eetlepels"},"volume-teaspoon":{displayName:"teelepels","unitPattern-count-one":"{0} teelepel","unitPattern-count-other":"{0} teelepels"},"volume-barrel":{displayName:"vate","unitPattern-count-one":"{0} vat","unitPattern-count-other":"{0} vate"},coordinateUnit:{displayName:"kompasrigting",east:"{0} oos",north:"{0} noord",south:"{0} suid",west:"{0} wes"}},short:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"swaartekrag","unitPattern-count-one":"{0} G","unitPattern-count-other":"{0} G"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-one":"{0} m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"o.","unitPattern-count-one":"{0} o.","unitPattern-count-other":"{0} o."},"angle-radian":{displayName:"boogmate","unitPattern-count-one":"{0} rad.","unitPattern-count-other":"{0} rad."},"angle-degree":{displayName:"grade","unitPattern-count-one":"{0} gr.","unitPattern-count-other":"{0} gr."},"angle-arc-minute":{displayName:"boogminute","unitPattern-count-one":"{0} boogmin.","unitPattern-count-other":"{0} boogmin."},"angle-arc-second":{displayName:"boogsekondes","unitPattern-count-one":"{0} boogsek.","unitPattern-count-other":"{0} boogsek."},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0} km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"hektaar","unitPattern-count-one":"{0} ha","unitPattern-count-other":"{0} ha"},"area-square-meter":{displayName:"m²","unitPattern-count-one":"{0} m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0} cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"myl²","unitPattern-count-one":"{0} myl²","unitPattern-count-other":"{0} myl²",perUnitPattern:"{0}/myl²"},"area-acre":{displayName:"acre","unitPattern-count-one":"{0} acre","unitPattern-count-other":"{0} acre"},"area-square-yard":{displayName:"jaart²","unitPattern-count-one":"{0} jt.²","unitPattern-count-other":"{0} jt.²"},"area-square-foot":{displayName:"vt.²","unitPattern-count-one":"{0} vt.²","unitPattern-count-other":"{0} vt.²"},"area-square-inch":{displayName:"duim²","unitPattern-count-one":"{0} dm.²","unitPattern-count-other":"{0} dm.²",perUnitPattern:"{0}/dm.²"},"area-dunam":{displayName:"donum","unitPattern-count-one":"{0} donum","unitPattern-count-other":"{0} donum"},"concentr-karat":{displayName:"karaat","unitPattern-count-one":"{0} kar.","unitPattern-count-other":"{0} kar."},"concentr-milligram-per-deciliter":{displayName:"mg/dℓ","unitPattern-count-one":"{0} mg/dℓ","unitPattern-count-other":"{0} mg/dℓ"},"concentr-millimole-per-liter":{displayName:"millimol/ℓ","unitPattern-count-one":"{0} mmol/ℓ","unitPattern-count-other":"{0} mmol/ℓ"},"concentr-part-per-million":{displayName:"dele/miljoen","unitPattern-count-one":"{0} d.p.m.","unitPattern-count-other":"{0} d.p.m."},"concentr-percent":{displayName:"percent","unitPattern-count-one":"{0}%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"per duisend","unitPattern-count-one":"{0}‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"per tienduisend","unitPattern-count-one":"{0}‱","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"liter/km","unitPattern-count-one":"{0} ℓ/km","unitPattern-count-other":"{0} ℓ/km"},"consumption-liter-per-100kilometers":{displayName:"ℓ/100 km","unitPattern-count-one":"{0} ℓ/100 km","unitPattern-count-other":"{0} ℓ/100 km"},"consumption-mile-per-gallon":{displayName:"myl/VSA-gell.","unitPattern-count-one":"{0} m.p.VSA-g.","unitPattern-count-other":"{0} m.p.VSA-g."},"consumption-mile-per-gallon-imperial":{displayName:"myl/Br. gelling","unitPattern-count-one":"{0} myl/Br.g.","unitPattern-count-other":"{0} myl/Br.g."},"digital-petabyte":{displayName:"PB","unitPattern-count-one":"{0} PB","unitPattern-count-other":"{0} PB"},"digital-terabyte":{displayName:"TB","unitPattern-count-one":"{0} TB","unitPattern-count-other":"{0} TB"},"digital-terabit":{displayName:"Tb","unitPattern-count-one":"{0} Tb","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"GB","unitPattern-count-one":"{0} GB","unitPattern-count-other":"{0} GB"},"digital-gigabit":{displayName:"Gb","unitPattern-count-one":"{0} Gb","unitPattern-count-other":"{0} Gb"},"digital-megabyte":{displayName:"MB","unitPattern-count-one":"{0} MB","unitPattern-count-other":"{0} MB"},"digital-megabit":{displayName:"Mb","unitPattern-count-one":"{0} Mb","unitPattern-count-other":"{0} Mb"},"digital-kilobyte":{displayName:"kB","unitPattern-count-one":"{0} kB","unitPattern-count-other":"{0} kB"},"digital-kilobit":{displayName:"kb","unitPattern-count-one":"{0} kb","unitPattern-count-other":"{0} kb"},"digital-byte":{displayName:"greep","unitPattern-count-one":"{0} greep","unitPattern-count-other":"{0} greep"},"digital-bit":{displayName:"bis","unitPattern-count-one":"{0} bis","unitPattern-count-other":"{0} bis"},"duration-century":{displayName:"e.","unitPattern-count-one":"{0} e.","unitPattern-count-other":"{0} e."},"duration-decade":{displayName:"dek.","unitPattern-count-one":"{0} dek.","unitPattern-count-other":"{0} dek."},"duration-year":{displayName:"jaar","unitPattern-count-one":"{0} j.","unitPattern-count-other":"{0} j.",perUnitPattern:"{0}/j."},"duration-month":{displayName:"maande","unitPattern-count-one":"{0} md.","unitPattern-count-other":"{0} md.",perUnitPattern:"{0}/md."},"duration-week":{displayName:"weke","unitPattern-count-one":"{0} w.","unitPattern-count-other":"{0} w.",perUnitPattern:"{0}/w."},"duration-day":{displayName:"dae","unitPattern-count-one":"{0} dag","unitPattern-count-other":"{0} dae",perUnitPattern:"{0}/d."},"duration-hour":{displayName:"uur","unitPattern-count-one":"{0} u.","unitPattern-count-other":"{0} u.",perUnitPattern:"{0}/h"},"duration-minute":{displayName:"min.","unitPattern-count-one":"{0} min.","unitPattern-count-other":"{0} min.",perUnitPattern:"{0}/min."},"duration-second":{displayName:"s.","unitPattern-count-one":"{0} s.","unitPattern-count-other":"{0} s.",perUnitPattern:"{0}/s."},"duration-millisecond":{displayName:"millisekondes","unitPattern-count-one":"{0} ms.","unitPattern-count-other":"{0} ms"},"duration-microsecond":{displayName:"μs.","unitPattern-count-one":"{0} μs.","unitPattern-count-other":"{0} μs."},"duration-nanosecond":{displayName:"ns.","unitPattern-count-one":"{0} ns.","unitPattern-count-other":"{0} ns."},"electric-ampere":{displayName:"A","unitPattern-count-one":"{0} A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"mA","unitPattern-count-one":"{0} mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"ohm","unitPattern-count-one":"{0} Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"volt","unitPattern-count-one":"{0} V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kkal.","unitPattern-count-one":"{0} kkal.","unitPattern-count-other":"{0} kkal."},"energy-calorie":{displayName:"kal.","unitPattern-count-one":"{0} kal.","unitPattern-count-other":"{0} kal."},"energy-foodcalorie":{displayName:"kkal.","unitPattern-count-one":"{0} kkal.","unitPattern-count-other":"{0} kkal."},"energy-kilojoule":{displayName:"kJ","unitPattern-count-one":"{0} kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"joule","unitPattern-count-one":"{0} J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-one":"{0} kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"elektronvolt","unitPattern-count-one":"{0} eV","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTE","unitPattern-count-one":"{0} BTE","unitPattern-count-other":"{0} BTE"},"energy-therm-us":{displayName:"VSA- termiese eenheid","unitPattern-count-one":"{0} VSA-term.","unitPattern-count-other":"{0} VSA-term."},"force-pound-force":{displayName:"pondkrag","unitPattern-count-one":"{0} lb.-krag","unitPattern-count-other":"{0} lb.-krag"},"force-newton":{displayName:"newton","unitPattern-count-one":"{0} N","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0} GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0} MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0} kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0} Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"em","unitPattern-count-one":"{0} em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"pieksels","unitPattern-count-one":"{0} px","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"Mpx","unitPattern-count-one":"{0} Mpx","unitPattern-count-other":"{0} Mpx"},"graphics-pixel-per-centimeter":{displayName:"px/cm","unitPattern-count-one":"{0} px/cm","unitPattern-count-other":"{0} px/cm"},"graphics-pixel-per-inch":{displayName:"px/dm.","unitPattern-count-one":"{0} px/dm.","unitPattern-count-other":"{0} px/dm."},"graphics-dot-per-centimeter":{displayName:"stip./cm","unitPattern-count-one":"{0} stip./cm","unitPattern-count-other":"{0} stip./cm"},"graphics-dot-per-inch":{displayName:"stip./dm.","unitPattern-count-one":"{0} stip./dm.","unitPattern-count-other":"{0} stip./dm."},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0} km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"meter","unitPattern-count-one":"{0} m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0} dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0} cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0} mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µm","unitPattern-count-one":"{0} µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0} nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0} pm","unitPattern-count-other":"{0} pm"},"length-mile":{displayName:"myl","unitPattern-count-one":"{0} myl","unitPattern-count-other":"{0} myl"},"length-yard":{displayName:"jaart","unitPattern-count-one":"{0} jt.","unitPattern-count-other":"{0} jt."},"length-foot":{displayName:"voet","unitPattern-count-one":"{0} vt.","unitPattern-count-other":"{0} vt.",perUnitPattern:"{0}/vt."},"length-inch":{displayName:"duim","unitPattern-count-one":"{0} duim","unitPattern-count-other":"{0} duim",perUnitPattern:"{0}/duim"},"length-parsec":{displayName:"parsek","unitPattern-count-one":"{0} pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"ligjare","unitPattern-count-one":"{0} lj.","unitPattern-count-other":"{0} lj."},"length-astronomical-unit":{displayName:"AE","unitPattern-count-one":"{0} AE","unitPattern-count-other":"{0} AE"},"length-furlong":{displayName:"fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"sm.","unitPattern-count-one":"{0} sm.","unitPattern-count-other":"{0} sm."},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0} smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"punte","unitPattern-count-one":"{0} pt.","unitPattern-count-other":"{0} pt."},"length-solar-radius":{displayName:"sonradiusse","unitPattern-count-one":"{0} R☉","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"sonligsterkte","unitPattern-count-one":"{0} L☉","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0} t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0} kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"gram","unitPattern-count-one":"{0} g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0} mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0} µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"VSA-ton","unitPattern-count-one":"{0} VSA-t.","unitPattern-count-other":"{0} VSA-t."},"mass-stone":{displayName:"st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"pond","unitPattern-count-one":"{0} lb.","unitPattern-count-other":"{0} lb.",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz.","unitPattern-count-one":"{0} oz.","unitPattern-count-other":"{0} oz.",perUnitPattern:"{0}/oz."},"mass-ounce-troy":{displayName:"troy-ons","unitPattern-count-one":"{0} oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"karaat","unitPattern-count-one":"{0} kar.","unitPattern-count-other":"{0} kar."},"mass-dalton":{displayName:"dalton","unitPattern-count-one":"{0} Da","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"aardemassas","unitPattern-count-one":"{0} M⊕","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"sonmassas","unitPattern-count-one":"{0} M☉","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0} GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0} MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0} kW","unitPattern-count-other":"{0} kW"},"power-watt":{displayName:"watt","unitPattern-count-one":"{0} W","unitPattern-count-other":"{0} W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0} mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"pk.","unitPattern-count-one":"{0} pk.","unitPattern-count-other":"{0} pk."},"pressure-millimeter-of-mercury":{displayName:"mm Hg","unitPattern-count-one":"{0} mm Hg","unitPattern-count-other":"{0} mm Hg"},"pressure-pound-per-square-inch":{displayName:"lb./vk. duim","unitPattern-count-one":"{0} pd.vk.dm.","unitPattern-count-other":"{0} pd.vk.dm."},"pressure-inch-hg":{displayName:"duim Hg","unitPattern-count-one":"{0} duim Hg","unitPattern-count-other":"{0} duim Hg"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bar"},"pressure-millibar":{displayName:"mbar","unitPattern-count-one":"{0} mbar","unitPattern-count-other":"{0} mbar"},"pressure-atmosphere":{displayName:"atm.","unitPattern-count-one":"{0} atm.","unitPattern-count-other":"{0} atm."},"pressure-pascal":{displayName:"Pa","unitPattern-count-one":"{0} Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0} hPa","unitPattern-count-other":"{0} hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0} kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0} MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/uur","unitPattern-count-one":"{0} km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"meter/s.","unitPattern-count-one":"{0} m/s.","unitPattern-count-other":"{0} m/s."},"speed-mile-per-hour":{displayName:"myl per uur","unitPattern-count-one":"{0} myl/h","unitPattern-count-other":"{0} myl/h"},"speed-knot":{displayName:"kn.","unitPattern-count-one":"{0} kn.","unitPattern-count-other":"{0} kn."},"temperature-generic":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"°C","unitPattern-count-one":"{0} °C","unitPattern-count-other":"{0} °C"},"temperature-fahrenheit":{displayName:"°F","unitPattern-count-one":"{0} °F","unitPattern-count-other":"{0} °F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0} K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lb.vt.","unitPattern-count-one":"{0} lb.vt.","unitPattern-count-other":"{0} lb.vt."},"torque-newton-meter":{displayName:"Nm","unitPattern-count-one":"{0} Nm","unitPattern-count-other":"{0} Nm"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0} km³","unitPattern-count-other":"{0} km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0} m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0} cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"myl³","unitPattern-count-one":"{0} myl³","unitPattern-count-other":"{0} myl³"},"volume-cubic-yard":{displayName:"jt.³","unitPattern-count-one":"{0} jt.³","unitPattern-count-other":"{0} jt.³"},"volume-cubic-foot":{displayName:"vt.³","unitPattern-count-one":"{0} vt.³","unitPattern-count-other":"{0} vt.³"},"volume-cubic-inch":{displayName:"duim³","unitPattern-count-one":"{0} dm.³","unitPattern-count-other":"{0} dm.³"},"volume-megaliter":{displayName:"Mℓ","unitPattern-count-one":"{0} Mℓ","unitPattern-count-other":"{0} Mℓ"},"volume-hectoliter":{displayName:"hℓ","unitPattern-count-one":"{0} hℓ","unitPattern-count-other":"{0} hℓ"},"volume-liter":{displayName:"liter","unitPattern-count-one":"{0} ℓ","unitPattern-count-other":"{0} ℓ",perUnitPattern:"{0}/ℓ"},"volume-deciliter":{displayName:"dℓ","unitPattern-count-one":"{0} dℓ","unitPattern-count-other":"{0} dℓ"},"volume-centiliter":{displayName:"cℓ","unitPattern-count-one":"{0} cℓ","unitPattern-count-other":"{0} cℓ"},"volume-milliliter":{displayName:"mℓ","unitPattern-count-one":"{0} mℓ","unitPattern-count-other":"{0} mℓ"},"volume-pint-metric":{displayName:"mpt.","unitPattern-count-one":"{0} mpt.","unitPattern-count-other":"{0} mpt."},"volume-cup-metric":{displayName:"m. kop","unitPattern-count-one":"{0} m. kop","unitPattern-count-other":"{0} m. kop"},"volume-acre-foot":{displayName:"acre-voet","unitPattern-count-one":"{0} acre-vt.","unitPattern-count-other":"{0} acre-vt."},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gell.","unitPattern-count-one":"{0} gell.","unitPattern-count-other":"{0} gell.",perUnitPattern:"{0}/gell."},"volume-gallon-imperial":{displayName:"Br. gell.","unitPattern-count-one":"{0} Br. gell.","unitPattern-count-other":"{0} Br. gell.",perUnitPattern:"{0}/Br. gell."},"volume-quart":{displayName:"VSA-kw.gell.","unitPattern-count-one":"{0} VSA-kw.gell.","unitPattern-count-other":"{0} VSA-kw.gell."},"volume-pint":{displayName:"pinte","unitPattern-count-one":"{0} pt.","unitPattern-count-other":"{0} pt."},"volume-cup":{displayName:"koppie","unitPattern-count-one":"{0} kp.","unitPattern-count-other":"{0} kp."},"volume-fluid-ounce":{displayName:"vl.oz.","unitPattern-count-one":"{0} vl.oz.","unitPattern-count-other":"{0} vl.oz."},"volume-fluid-ounce-imperial":{displayName:"Br. vl.oz.","unitPattern-count-one":"{0} Br. vl.oz.","unitPattern-count-other":"{0} Br. vl.oz."},"volume-tablespoon":{displayName:"e.","unitPattern-count-one":"{0} e.","unitPattern-count-other":"{0} e."},"volume-teaspoon":{displayName:"teel.","unitPattern-count-one":"{0} teel.","unitPattern-count-other":"{0} teel."},"volume-barrel":{displayName:"vat","unitPattern-count-one":"{0} bbl","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"rigting",east:"{0} O",north:"{0} N",south:"{0} S",west:"{0} W"}},narrow:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"swaartekrag","unitPattern-count-one":"{0}G","unitPattern-count-other":"{0}G"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-one":"{0} m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"o.","unitPattern-count-one":"{0} o.","unitPattern-count-other":"{0} o."},"angle-radian":{displayName:"boogmate","unitPattern-count-one":"{0} rad.","unitPattern-count-other":"{0} rad."},"angle-degree":{displayName:"grade","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"boogminute","unitPattern-count-one":"{0}′","unitPattern-count-other":"{0}′"},"angle-arc-second":{displayName:"boogsekondes","unitPattern-count-one":"{0}″","unitPattern-count-other":"{0}″"},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0} km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"hektaar","unitPattern-count-one":"{0}ha","unitPattern-count-other":"{0}ha"},"area-square-meter":{displayName:"m²","unitPattern-count-one":"{0} m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0} cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"myl²","unitPattern-count-one":"{0}myl²","unitPattern-count-other":"{0}myl²",perUnitPattern:"{0}/myl²"},"area-acre":{displayName:"acre","unitPattern-count-one":"{0} ak","unitPattern-count-other":"{0} ak"},"area-square-yard":{displayName:"jaart²","unitPattern-count-one":"{0} jt.²","unitPattern-count-other":"{0} jt.²"},"area-square-foot":{displayName:"vt.²","unitPattern-count-one":"{0} vt.²","unitPattern-count-other":"{0} vt.²"},"area-square-inch":{displayName:"duim²","unitPattern-count-one":"{0} dm.²","unitPattern-count-other":"{0} dm.²",perUnitPattern:"{0}/dm.²"},"area-dunam":{displayName:"donum","unitPattern-count-one":"{0} donum","unitPattern-count-other":"{0} donum"},"concentr-karat":{displayName:"karaat","unitPattern-count-one":"{0} kar.","unitPattern-count-other":"{0} kar."},"concentr-milligram-per-deciliter":{displayName:"mg/dℓ","unitPattern-count-one":"{0} mg/dℓ","unitPattern-count-other":"{0} mg/dℓ"},"concentr-millimole-per-liter":{displayName:"millimol/ℓ","unitPattern-count-one":"{0} mmol/ℓ","unitPattern-count-other":"{0} mmol/ℓ"},"concentr-part-per-million":{displayName:"dele/miljoen","unitPattern-count-one":"{0} d.p.m.","unitPattern-count-other":"{0} d.p.m."},"concentr-percent":{displayName:"%","unitPattern-count-one":"{0}%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"per duisend","unitPattern-count-one":"{0}‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"per tienduisend","unitPattern-count-one":"{0}‱","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"liter/km","unitPattern-count-one":"{0} ℓ/km","unitPattern-count-other":"{0} ℓ/km"},"consumption-liter-per-100kilometers":{displayName:"ℓ/100 km","unitPattern-count-one":"{0} ℓ/100 km","unitPattern-count-other":"{0} ℓ/100 km"},"consumption-mile-per-gallon":{displayName:"myl/VSA-gell.","unitPattern-count-one":"{0} m.p.VSA-g.","unitPattern-count-other":"{0} m.p.VSA-g."},"consumption-mile-per-gallon-imperial":{displayName:"myl/Br. gelling","unitPattern-count-one":"{0} myl/Br.g.","unitPattern-count-other":"{0} myl/Br.g."},"digital-petabyte":{displayName:"PB","unitPattern-count-one":"{0} PB","unitPattern-count-other":"{0} PB"},"digital-terabyte":{displayName:"TB","unitPattern-count-one":"{0} TB","unitPattern-count-other":"{0} TB"},"digital-terabit":{displayName:"Tb","unitPattern-count-one":"{0} Tb","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"GB","unitPattern-count-one":"{0} GB","unitPattern-count-other":"{0} GB"},"digital-gigabit":{displayName:"Gb","unitPattern-count-one":"{0} Gb","unitPattern-count-other":"{0} Gb"},"digital-megabyte":{displayName:"MB","unitPattern-count-one":"{0} MB","unitPattern-count-other":"{0} MB"},"digital-megabit":{displayName:"Mb","unitPattern-count-one":"{0} Mb","unitPattern-count-other":"{0} Mb"},"digital-kilobyte":{displayName:"kB","unitPattern-count-one":"{0} kB","unitPattern-count-other":"{0} kB"},"digital-kilobit":{displayName:"kb","unitPattern-count-one":"{0} kb","unitPattern-count-other":"{0} kb"},"digital-byte":{displayName:"greep","unitPattern-count-one":"{0} greep","unitPattern-count-other":"{0} greep"},"digital-bit":{displayName:"bis","unitPattern-count-one":"{0} bis","unitPattern-count-other":"{0} bis"},"duration-century":{displayName:"e.","unitPattern-count-one":"{0} e.","unitPattern-count-other":"{0} e."},"duration-decade":{displayName:"dek.","unitPattern-count-one":"{0} dek.","unitPattern-count-other":"{0} dek."},"duration-year":{displayName:"j.","unitPattern-count-one":"{0} j.","unitPattern-count-other":"{0} j.",perUnitPattern:"{0}/j."},"duration-month":{displayName:"maand","unitPattern-count-one":"{0} md.","unitPattern-count-other":"{0} md.",perUnitPattern:"{0}/md."},"duration-week":{displayName:"w.","unitPattern-count-one":"{0} w.","unitPattern-count-other":"{0} w.",perUnitPattern:"{0}/w."},"duration-day":{displayName:"dag","unitPattern-count-one":"{0} d.","unitPattern-count-other":"{0} d.",perUnitPattern:"{0}/d."},"duration-hour":{displayName:"uur","unitPattern-count-one":"{0} u.","unitPattern-count-other":"{0} u.",perUnitPattern:"{0}/h"},"duration-minute":{displayName:"min.","unitPattern-count-one":"{0} min.","unitPattern-count-other":"{0} min.",perUnitPattern:"{0}/min."},"duration-second":{displayName:"s.","unitPattern-count-one":"{0} s.","unitPattern-count-other":"{0} s.",perUnitPattern:"{0}/s."},"duration-millisecond":{displayName:"ms.","unitPattern-count-one":"{0} ms.","unitPattern-count-other":"{0} ms."},"duration-microsecond":{displayName:"μs.","unitPattern-count-one":"{0} μs.","unitPattern-count-other":"{0} μs."},"duration-nanosecond":{displayName:"ns.","unitPattern-count-one":"{0} ns.","unitPattern-count-other":"{0} ns."},"electric-ampere":{displayName:"A","unitPattern-count-one":"{0} A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"mA","unitPattern-count-one":"{0} mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"ohm","unitPattern-count-one":"{0} Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"volt","unitPattern-count-one":"{0} V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kkal.","unitPattern-count-one":"{0} kkal.","unitPattern-count-other":"{0} kkal."},"energy-calorie":{displayName:"kal.","unitPattern-count-one":"{0} kal.","unitPattern-count-other":"{0} kal."},"energy-foodcalorie":{displayName:"kkal.","unitPattern-count-one":"{0} kkal.","unitPattern-count-other":"{0} kkal."},"energy-kilojoule":{displayName:"kJ","unitPattern-count-one":"{0} kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"joule","unitPattern-count-one":"{0} J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-one":"{0} kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"elektronvolt","unitPattern-count-one":"{0} eV","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTE","unitPattern-count-one":"{0} BTE","unitPattern-count-other":"{0} BTE"},"energy-therm-us":{displayName:"VSA- termiese eenheid","unitPattern-count-one":"{0} VSA-term.","unitPattern-count-other":"{0} VSA-term."},"force-pound-force":{displayName:"pondkrag","unitPattern-count-one":"{0} lb.-krag","unitPattern-count-other":"{0} lb.-krag"},"force-newton":{displayName:"newton","unitPattern-count-one":"{0} N","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0} GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0} MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0} kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0} Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"em","unitPattern-count-one":"{0} em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"pieksels","unitPattern-count-one":"{0} px","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"Mpx","unitPattern-count-one":"{0} Mpx","unitPattern-count-other":"{0} Mpx"},"graphics-pixel-per-centimeter":{displayName:"px/cm","unitPattern-count-one":"{0} px/cm","unitPattern-count-other":"{0} px/cm"},"graphics-pixel-per-inch":{displayName:"px/dm.","unitPattern-count-one":"{0} px/dm.","unitPattern-count-other":"{0} px/dm."},"graphics-dot-per-centimeter":{displayName:"stip./cm","unitPattern-count-one":"{0} stip./cm","unitPattern-count-other":"{0} stip./cm"},"graphics-dot-per-inch":{displayName:"stip./dm.","unitPattern-count-one":"{0} stip./dm.","unitPattern-count-other":"{0} stip./dm."},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0} km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"m","unitPattern-count-one":"{0} m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0} dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0} cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0} mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µm","unitPattern-count-one":"{0} µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0} nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0}pm","unitPattern-count-other":"{0}pm"},"length-mile":{displayName:"myl","unitPattern-count-one":"{0} myl","unitPattern-count-other":"{0} myl"},"length-yard":{displayName:"jaart","unitPattern-count-one":"{0} jt.","unitPattern-count-other":"{0} jt."},"length-foot":{displayName:"voet","unitPattern-count-one":"{0} vt.","unitPattern-count-other":"{0} vt.",perUnitPattern:"{0}/vt."},"length-inch":{displayName:"duim","unitPattern-count-one":"{0} duim","unitPattern-count-other":"{0} duim",perUnitPattern:"{0}/duim"},"length-parsec":{displayName:"parsek","unitPattern-count-one":"{0} pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"ligjare","unitPattern-count-one":"{0} lj","unitPattern-count-other":"{0} lj"},"length-astronomical-unit":{displayName:"AE","unitPattern-count-one":"{0} AE","unitPattern-count-other":"{0} AE"},"length-furlong":{displayName:"fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"sm.","unitPattern-count-one":"{0} sm.","unitPattern-count-other":"{0} sm."},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0} smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"punte","unitPattern-count-one":"{0} pt.","unitPattern-count-other":"{0} pt."},"length-solar-radius":{displayName:"sonradiusse","unitPattern-count-one":"{0} R☉","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"sonligsterkte","unitPattern-count-one":"{0} L☉","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0} t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0} kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"gram","unitPattern-count-one":"{0} g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0} mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0} µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"VSA-ton","unitPattern-count-one":"{0} VSA-t.","unitPattern-count-other":"{0} VSA-t."},"mass-stone":{displayName:"st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"pond","unitPattern-count-one":"{0} lb.","unitPattern-count-other":"{0} lb.",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz.","unitPattern-count-one":"{0} oz.","unitPattern-count-other":"{0} oz.",perUnitPattern:"{0}/oz."},"mass-ounce-troy":{displayName:"troy-ons","unitPattern-count-one":"{0} oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"karaat","unitPattern-count-one":"{0} kar.","unitPattern-count-other":"{0} kar."},"mass-dalton":{displayName:"dalton","unitPattern-count-one":"{0} Da","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"aardemassas","unitPattern-count-one":"{0} M⊕","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"sonmassas","unitPattern-count-one":"{0} M☉","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0} GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0} MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0}kW","unitPattern-count-other":"{0}kW"},"power-watt":{displayName:"watt","unitPattern-count-one":"{0}W","unitPattern-count-other":"{0}W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0} mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"pk.","unitPattern-count-one":"{0}pk.","unitPattern-count-other":"{0}pk."},"pressure-millimeter-of-mercury":{displayName:"mm Hg","unitPattern-count-one":"{0} mm Hg","unitPattern-count-other":"{0} mm Hg"},"pressure-pound-per-square-inch":{displayName:"lb./vk. duim","unitPattern-count-one":"{0} pd.vk.dm.","unitPattern-count-other":"{0} pd.vk.dm."},"pressure-inch-hg":{displayName:"duim Hg","unitPattern-count-one":"{0} dm.Hg","unitPattern-count-other":"{0} dm.Hg"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bar"},"pressure-millibar":{displayName:"mbar","unitPattern-count-one":"{0}mbar","unitPattern-count-other":"{0} mbar"},"pressure-atmosphere":{displayName:"atm.","unitPattern-count-one":"{0} atm.","unitPattern-count-other":"{0} atm."},"pressure-pascal":{displayName:"Pa","unitPattern-count-one":"{0} Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0}hPa","unitPattern-count-other":"{0}hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0} kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0} MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-one":"{0} km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"meter/s.","unitPattern-count-one":"{0}m/s","unitPattern-count-other":"{0}m/s"},"speed-mile-per-hour":{displayName:"myl per uur","unitPattern-count-one":"{0} myl/h","unitPattern-count-other":"{0} myl/h"},"speed-knot":{displayName:"kn.","unitPattern-count-one":"{0} kn.","unitPattern-count-other":"{0} kn."},"temperature-generic":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"°C","unitPattern-count-one":"{0} °C","unitPattern-count-other":"{0} °C"},"temperature-fahrenheit":{displayName:"°F","unitPattern-count-one":"{0}°F","unitPattern-count-other":"{0}°F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0} K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lb.vt.","unitPattern-count-one":"{0} lb.vt.","unitPattern-count-other":"{0} lb.vt."},"torque-newton-meter":{displayName:"Nm","unitPattern-count-one":"{0} Nm","unitPattern-count-other":"{0} Nm"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0}km³","unitPattern-count-other":"{0}km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0} m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0} cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"myl³","unitPattern-count-one":"{0} myl³","unitPattern-count-other":"{0} myl³"},"volume-cubic-yard":{displayName:"jt.³","unitPattern-count-one":"{0} jt.³","unitPattern-count-other":"{0} jt.³"},"volume-cubic-foot":{displayName:"vt.³","unitPattern-count-one":"{0} vt.³","unitPattern-count-other":"{0} vt.³"},"volume-cubic-inch":{displayName:"duim³","unitPattern-count-one":"{0} dm.³","unitPattern-count-other":"{0} dm.³"},"volume-megaliter":{displayName:"Mℓ","unitPattern-count-one":"{0} Mℓ","unitPattern-count-other":"{0} Mℓ"},"volume-hectoliter":{displayName:"hℓ","unitPattern-count-one":"{0} hℓ","unitPattern-count-other":"{0} hℓ"},"volume-liter":{displayName:"liter","unitPattern-count-one":"{0} ℓ","unitPattern-count-other":"{0} ℓ",perUnitPattern:"{0}/ℓ"},"volume-deciliter":{displayName:"dℓ","unitPattern-count-one":"{0} dℓ","unitPattern-count-other":"{0} dℓ"},"volume-centiliter":{displayName:"cℓ","unitPattern-count-one":"{0} cℓ","unitPattern-count-other":"{0} cℓ"},"volume-milliliter":{displayName:"mℓ","unitPattern-count-one":"{0} mℓ","unitPattern-count-other":"{0} mℓ"},"volume-pint-metric":{displayName:"mpt.","unitPattern-count-one":"{0} mpt.","unitPattern-count-other":"{0} mpt."},"volume-cup-metric":{displayName:"m. kop","unitPattern-count-one":"{0} m. kop","unitPattern-count-other":"{0} m. kop"},"volume-acre-foot":{displayName:"acre-voet","unitPattern-count-one":"{0} acre-vt.","unitPattern-count-other":"{0} acre-vt."},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gell.","unitPattern-count-one":"{0} gell.","unitPattern-count-other":"{0} gell.",perUnitPattern:"{0}/gell."},"volume-gallon-imperial":{displayName:"Br. gell.","unitPattern-count-one":"{0} Br. gell.","unitPattern-count-other":"{0} Br. gell.",perUnitPattern:"{0}/Br. gell."},"volume-quart":{displayName:"VSA-kw.gell.","unitPattern-count-one":"{0} VSA-kw.gell.","unitPattern-count-other":"{0} VSA-kw.gell."},"volume-pint":{displayName:"pinte","unitPattern-count-one":"{0} pt.","unitPattern-count-other":"{0} pt."},"volume-cup":{displayName:"koppie","unitPattern-count-one":"{0} kp.","unitPattern-count-other":"{0} kp."},"volume-fluid-ounce":{displayName:"vl.oz.","unitPattern-count-one":"{0} vl.oz.","unitPattern-count-other":"{0} vl.oz."},"volume-fluid-ounce-imperial":{displayName:"Br. vl.oz.","unitPattern-count-one":"{0} Br. vl.oz.","unitPattern-count-other":"{0} Br. vl.oz."},"volume-tablespoon":{displayName:"e.","unitPattern-count-one":"{0} e.","unitPattern-count-other":"{0} e."},"volume-teaspoon":{displayName:"teel.","unitPattern-count-one":"{0} teel.","unitPattern-count-other":"{0} teel."},"volume-barrel":{displayName:"vat","unitPattern-count-one":"{0} bbl","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"rigting",east:"{0} O",north:"{0} N",south:"{0} S",west:"{0} W"}},"durationUnit-type-hm":{durationUnitPattern:"h:mm"},"durationUnit-type-hms":{durationUnitPattern:"h:mm:ss"},"durationUnit-type-ms":{durationUnitPattern:"mm:ss"}}}}}];