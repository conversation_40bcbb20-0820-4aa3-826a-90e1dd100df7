var CldrData=[{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},likelySubtags:{aa:"aa-Latn-ET",aai:"aai-Latn-ZZ",aak:"aak-Latn-ZZ",aau:"aau-Latn-ZZ",ab:"ab-Cyrl-GE",abi:"abi-Latn-ZZ",abq:"abq-Cyrl-ZZ",abr:"abr-Latn-GH",abt:"abt-Latn-ZZ",aby:"aby-Latn-ZZ",acd:"acd-Latn-ZZ",ace:"ace-Latn-ID",ach:"ach-Latn-UG",ada:"ada-Latn-GH",ade:"ade-Latn-ZZ",adj:"adj-Latn-ZZ",adp:"adp-Tibt-BT",ady:"ady-Cyrl-RU",adz:"adz-Latn-ZZ",ae:"ae-Avst-IR",aeb:"aeb-Arab-TN",aey:"aey-Latn-ZZ",af:"af-Latn-ZA",agc:"agc-Latn-ZZ",agd:"agd-Latn-ZZ",agg:"agg-Latn-ZZ",agm:"agm-Latn-ZZ",ago:"ago-Latn-ZZ",agq:"agq-Latn-CM",aha:"aha-Latn-ZZ",ahl:"ahl-Latn-ZZ",aho:"aho-Ahom-IN",ajg:"ajg-Latn-ZZ",ak:"ak-Latn-GH",akk:"akk-Xsux-IQ",ala:"ala-Latn-ZZ",ali:"ali-Latn-ZZ",aln:"aln-Latn-XK",alt:"alt-Cyrl-RU",am:"am-Ethi-ET",amm:"amm-Latn-ZZ",amn:"amn-Latn-ZZ",amo:"amo-Latn-NG",amp:"amp-Latn-ZZ",an:"an-Latn-ES",anc:"anc-Latn-ZZ",ank:"ank-Latn-ZZ",ann:"ann-Latn-ZZ",any:"any-Latn-ZZ",aoj:"aoj-Latn-ZZ",aom:"aom-Latn-ZZ",aoz:"aoz-Latn-ID",apc:"apc-Arab-ZZ",apd:"apd-Arab-TG",ape:"ape-Latn-ZZ",apr:"apr-Latn-ZZ",aps:"aps-Latn-ZZ",apz:"apz-Latn-ZZ",ar:"ar-Arab-EG",arc:"arc-Armi-IR","arc-Nbat":"arc-Nbat-JO","arc-Palm":"arc-Palm-SY",arh:"arh-Latn-ZZ",arn:"arn-Latn-CL",aro:"aro-Latn-BO",arq:"arq-Arab-DZ",ars:"ars-Arab-SA",ary:"ary-Arab-MA",arz:"arz-Arab-EG",as:"as-Beng-IN",asa:"asa-Latn-TZ",ase:"ase-Sgnw-US",asg:"asg-Latn-ZZ",aso:"aso-Latn-ZZ",ast:"ast-Latn-ES",ata:"ata-Latn-ZZ",atg:"atg-Latn-ZZ",atj:"atj-Latn-CA",auy:"auy-Latn-ZZ",av:"av-Cyrl-RU",avl:"avl-Arab-ZZ",avn:"avn-Latn-ZZ",avt:"avt-Latn-ZZ",avu:"avu-Latn-ZZ",awa:"awa-Deva-IN",awb:"awb-Latn-ZZ",awo:"awo-Latn-ZZ",awx:"awx-Latn-ZZ",ay:"ay-Latn-BO",ayb:"ayb-Latn-ZZ",az:"az-Latn-AZ","az-Arab":"az-Arab-IR","az-IQ":"az-Arab-IQ","az-IR":"az-Arab-IR","az-RU":"az-Cyrl-RU",ba:"ba-Cyrl-RU",bal:"bal-Arab-PK",ban:"ban-Latn-ID",bap:"bap-Deva-NP",bar:"bar-Latn-AT",bas:"bas-Latn-CM",bav:"bav-Latn-ZZ",bax:"bax-Bamu-CM",bba:"bba-Latn-ZZ",bbb:"bbb-Latn-ZZ",bbc:"bbc-Latn-ID",bbd:"bbd-Latn-ZZ",bbj:"bbj-Latn-CM",bbp:"bbp-Latn-ZZ",bbr:"bbr-Latn-ZZ",bcf:"bcf-Latn-ZZ",bch:"bch-Latn-ZZ",bci:"bci-Latn-CI",bcm:"bcm-Latn-ZZ",bcn:"bcn-Latn-ZZ",bco:"bco-Latn-ZZ",bcq:"bcq-Ethi-ZZ",bcu:"bcu-Latn-ZZ",bdd:"bdd-Latn-ZZ",be:"be-Cyrl-BY",bef:"bef-Latn-ZZ",beh:"beh-Latn-ZZ",bej:"bej-Arab-SD",bem:"bem-Latn-ZM",bet:"bet-Latn-ZZ",bew:"bew-Latn-ID",bex:"bex-Latn-ZZ",bez:"bez-Latn-TZ",bfd:"bfd-Latn-CM",bfq:"bfq-Taml-IN",bft:"bft-Arab-PK",bfy:"bfy-Deva-IN",bg:"bg-Cyrl-BG",bgc:"bgc-Deva-IN",bgn:"bgn-Arab-PK",bgx:"bgx-Grek-TR",bhb:"bhb-Deva-IN",bhg:"bhg-Latn-ZZ",bhi:"bhi-Deva-IN",bhl:"bhl-Latn-ZZ",bho:"bho-Deva-IN",bhy:"bhy-Latn-ZZ",bi:"bi-Latn-VU",bib:"bib-Latn-ZZ",big:"big-Latn-ZZ",bik:"bik-Latn-PH",bim:"bim-Latn-ZZ",bin:"bin-Latn-NG",bio:"bio-Latn-ZZ",biq:"biq-Latn-ZZ",bjh:"bjh-Latn-ZZ",bji:"bji-Ethi-ZZ",bjj:"bjj-Deva-IN",bjn:"bjn-Latn-ID",bjo:"bjo-Latn-ZZ",bjr:"bjr-Latn-ZZ",bjt:"bjt-Latn-SN",bjz:"bjz-Latn-ZZ",bkc:"bkc-Latn-ZZ",bkm:"bkm-Latn-CM",bkq:"bkq-Latn-ZZ",bku:"bku-Latn-PH",bkv:"bkv-Latn-ZZ",blt:"blt-Tavt-VN",bm:"bm-Latn-ML",bmh:"bmh-Latn-ZZ",bmk:"bmk-Latn-ZZ",bmq:"bmq-Latn-ML",bmu:"bmu-Latn-ZZ",bn:"bn-Beng-BD",bng:"bng-Latn-ZZ",bnm:"bnm-Latn-ZZ",bnp:"bnp-Latn-ZZ",bo:"bo-Tibt-CN",boj:"boj-Latn-ZZ",bom:"bom-Latn-ZZ",bon:"bon-Latn-ZZ",bpy:"bpy-Beng-IN",bqc:"bqc-Latn-ZZ",bqi:"bqi-Arab-IR",bqp:"bqp-Latn-ZZ",bqv:"bqv-Latn-CI",br:"br-Latn-FR",bra:"bra-Deva-IN",brh:"brh-Arab-PK",brx:"brx-Deva-IN",brz:"brz-Latn-ZZ",bs:"bs-Latn-BA",bsj:"bsj-Latn-ZZ",bsq:"bsq-Bass-LR",bss:"bss-Latn-CM",bst:"bst-Ethi-ZZ",bto:"bto-Latn-PH",btt:"btt-Latn-ZZ",btv:"btv-Deva-PK",bua:"bua-Cyrl-RU",buc:"buc-Latn-YT",bud:"bud-Latn-ZZ",bug:"bug-Latn-ID",buk:"buk-Latn-ZZ",bum:"bum-Latn-CM",buo:"buo-Latn-ZZ",bus:"bus-Latn-ZZ",buu:"buu-Latn-ZZ",bvb:"bvb-Latn-GQ",bwd:"bwd-Latn-ZZ",bwr:"bwr-Latn-ZZ",bxh:"bxh-Latn-ZZ",bye:"bye-Latn-ZZ",byn:"byn-Ethi-ER",byr:"byr-Latn-ZZ",bys:"bys-Latn-ZZ",byv:"byv-Latn-CM",byx:"byx-Latn-ZZ",bza:"bza-Latn-ZZ",bze:"bze-Latn-ML",bzf:"bzf-Latn-ZZ",bzh:"bzh-Latn-ZZ",bzw:"bzw-Latn-ZZ",ca:"ca-Latn-ES",can:"can-Latn-ZZ",cbj:"cbj-Latn-ZZ",cch:"cch-Latn-NG",ccp:"ccp-Cakm-BD",ce:"ce-Cyrl-RU",ceb:"ceb-Latn-PH",cfa:"cfa-Latn-ZZ",cgg:"cgg-Latn-UG",ch:"ch-Latn-GU",chk:"chk-Latn-FM",chm:"chm-Cyrl-RU",cho:"cho-Latn-US",chp:"chp-Latn-CA",chr:"chr-Cher-US",cic:"cic-Latn-US",cja:"cja-Arab-KH",cjm:"cjm-Cham-VN",cjv:"cjv-Latn-ZZ",ckb:"ckb-Arab-IQ",ckl:"ckl-Latn-ZZ",cko:"cko-Latn-ZZ",cky:"cky-Latn-ZZ",cla:"cla-Latn-ZZ",cme:"cme-Latn-ZZ",cmg:"cmg-Soyo-MN",co:"co-Latn-FR",cop:"cop-Copt-EG",cps:"cps-Latn-PH",cr:"cr-Cans-CA",crh:"crh-Cyrl-UA",crj:"crj-Cans-CA",crk:"crk-Cans-CA",crl:"crl-Cans-CA",crm:"crm-Cans-CA",crs:"crs-Latn-SC",cs:"cs-Latn-CZ",csb:"csb-Latn-PL",csw:"csw-Cans-CA",ctd:"ctd-Pauc-MM",cu:"cu-Cyrl-RU","cu-Glag":"cu-Glag-BG",cv:"cv-Cyrl-RU",cy:"cy-Latn-GB",da:"da-Latn-DK",dad:"dad-Latn-ZZ",daf:"daf-Latn-ZZ",dag:"dag-Latn-ZZ",dah:"dah-Latn-ZZ",dak:"dak-Latn-US",dar:"dar-Cyrl-RU",dav:"dav-Latn-KE",dbd:"dbd-Latn-ZZ",dbq:"dbq-Latn-ZZ",dcc:"dcc-Arab-IN",ddn:"ddn-Latn-ZZ",de:"de-Latn-DE",ded:"ded-Latn-ZZ",den:"den-Latn-CA",dga:"dga-Latn-ZZ",dgh:"dgh-Latn-ZZ",dgi:"dgi-Latn-ZZ",dgl:"dgl-Arab-ZZ",dgr:"dgr-Latn-CA",dgz:"dgz-Latn-ZZ",dia:"dia-Latn-ZZ",dje:"dje-Latn-NE",dnj:"dnj-Latn-CI",dob:"dob-Latn-ZZ",doi:"doi-Arab-IN",dop:"dop-Latn-ZZ",dow:"dow-Latn-ZZ",drh:"drh-Mong-CN",dri:"dri-Latn-ZZ",drs:"drs-Ethi-ZZ",dsb:"dsb-Latn-DE",dtm:"dtm-Latn-ML",dtp:"dtp-Latn-MY",dts:"dts-Latn-ZZ",dty:"dty-Deva-NP",dua:"dua-Latn-CM",duc:"duc-Latn-ZZ",dud:"dud-Latn-ZZ",dug:"dug-Latn-ZZ",dv:"dv-Thaa-MV",dva:"dva-Latn-ZZ",dww:"dww-Latn-ZZ",dyo:"dyo-Latn-SN",dyu:"dyu-Latn-BF",dz:"dz-Tibt-BT",dzg:"dzg-Latn-ZZ",ebu:"ebu-Latn-KE",ee:"ee-Latn-GH",efi:"efi-Latn-NG",egl:"egl-Latn-IT",egy:"egy-Egyp-EG",eka:"eka-Latn-ZZ",eky:"eky-Kali-MM",el:"el-Grek-GR",ema:"ema-Latn-ZZ",emi:"emi-Latn-ZZ",en:"en-Latn-US","en-Shaw":"en-Shaw-GB",enn:"enn-Latn-ZZ",enq:"enq-Latn-ZZ",eo:"eo-Latn-001",eri:"eri-Latn-ZZ",es:"es-Latn-ES",esg:"esg-Gonm-IN",esu:"esu-Latn-US",et:"et-Latn-EE",etr:"etr-Latn-ZZ",ett:"ett-Ital-IT",etu:"etu-Latn-ZZ",etx:"etx-Latn-ZZ",eu:"eu-Latn-ES",ewo:"ewo-Latn-CM",ext:"ext-Latn-ES",fa:"fa-Arab-IR",faa:"faa-Latn-ZZ",fab:"fab-Latn-ZZ",fag:"fag-Latn-ZZ",fai:"fai-Latn-ZZ",fan:"fan-Latn-GQ",ff:"ff-Latn-SN","ff-Adlm":"ff-Adlm-GN",ffi:"ffi-Latn-ZZ",ffm:"ffm-Latn-ML",fi:"fi-Latn-FI",fia:"fia-Arab-SD",fil:"fil-Latn-PH",fit:"fit-Latn-SE",fj:"fj-Latn-FJ",flr:"flr-Latn-ZZ",fmp:"fmp-Latn-ZZ",fo:"fo-Latn-FO",fod:"fod-Latn-ZZ",fon:"fon-Latn-BJ",for:"for-Latn-ZZ",fpe:"fpe-Latn-ZZ",fqs:"fqs-Latn-ZZ",fr:"fr-Latn-FR",frc:"frc-Latn-US",frp:"frp-Latn-FR",frr:"frr-Latn-DE",frs:"frs-Latn-DE",fub:"fub-Arab-CM",fud:"fud-Latn-WF",fue:"fue-Latn-ZZ",fuf:"fuf-Latn-GN",fuh:"fuh-Latn-ZZ",fuq:"fuq-Latn-NE",fur:"fur-Latn-IT",fuv:"fuv-Latn-NG",fuy:"fuy-Latn-ZZ",fvr:"fvr-Latn-SD",fy:"fy-Latn-NL",ga:"ga-Latn-IE",gaa:"gaa-Latn-GH",gaf:"gaf-Latn-ZZ",gag:"gag-Latn-MD",gah:"gah-Latn-ZZ",gaj:"gaj-Latn-ZZ",gam:"gam-Latn-ZZ",gan:"gan-Hans-CN",gaw:"gaw-Latn-ZZ",gay:"gay-Latn-ID",gba:"gba-Latn-ZZ",gbf:"gbf-Latn-ZZ",gbm:"gbm-Deva-IN",gby:"gby-Latn-ZZ",gbz:"gbz-Arab-IR",gcr:"gcr-Latn-GF",gd:"gd-Latn-GB",gde:"gde-Latn-ZZ",gdn:"gdn-Latn-ZZ",gdr:"gdr-Latn-ZZ",geb:"geb-Latn-ZZ",gej:"gej-Latn-ZZ",gel:"gel-Latn-ZZ",gez:"gez-Ethi-ET",gfk:"gfk-Latn-ZZ",ggn:"ggn-Deva-NP",ghs:"ghs-Latn-ZZ",gil:"gil-Latn-KI",gim:"gim-Latn-ZZ",gjk:"gjk-Arab-PK",gjn:"gjn-Latn-ZZ",gju:"gju-Arab-PK",gkn:"gkn-Latn-ZZ",gkp:"gkp-Latn-ZZ",gl:"gl-Latn-ES",glk:"glk-Arab-IR",gmm:"gmm-Latn-ZZ",gmv:"gmv-Ethi-ZZ",gn:"gn-Latn-PY",gnd:"gnd-Latn-ZZ",gng:"gng-Latn-ZZ",god:"god-Latn-ZZ",gof:"gof-Ethi-ZZ",goi:"goi-Latn-ZZ",gom:"gom-Deva-IN",gon:"gon-Telu-IN",gor:"gor-Latn-ID",gos:"gos-Latn-NL",got:"got-Goth-UA",grb:"grb-Latn-ZZ",grc:"grc-Cprt-CY","grc-Linb":"grc-Linb-GR",grt:"grt-Beng-IN",grw:"grw-Latn-ZZ",gsw:"gsw-Latn-CH",gu:"gu-Gujr-IN",gub:"gub-Latn-BR",guc:"guc-Latn-CO",gud:"gud-Latn-ZZ",gur:"gur-Latn-GH",guw:"guw-Latn-ZZ",gux:"gux-Latn-ZZ",guz:"guz-Latn-KE",gv:"gv-Latn-IM",gvf:"gvf-Latn-ZZ",gvr:"gvr-Deva-NP",gvs:"gvs-Latn-ZZ",gwc:"gwc-Arab-ZZ",gwi:"gwi-Latn-CA",gwt:"gwt-Arab-ZZ",gyi:"gyi-Latn-ZZ",ha:"ha-Latn-NG","ha-CM":"ha-Arab-CM","ha-SD":"ha-Arab-SD",hag:"hag-Latn-ZZ",hak:"hak-Hans-CN",ham:"ham-Latn-ZZ",haw:"haw-Latn-US",haz:"haz-Arab-AF",hbb:"hbb-Latn-ZZ",hdy:"hdy-Ethi-ZZ",he:"he-Hebr-IL",hhy:"hhy-Latn-ZZ",hi:"hi-Deva-IN",hia:"hia-Latn-ZZ",hif:"hif-Latn-FJ",hig:"hig-Latn-ZZ",hih:"hih-Latn-ZZ",hil:"hil-Latn-PH",hla:"hla-Latn-ZZ",hlu:"hlu-Hluw-TR",hmd:"hmd-Plrd-CN",hmt:"hmt-Latn-ZZ",hnd:"hnd-Arab-PK",hne:"hne-Deva-IN",hnj:"hnj-Hmng-LA",hnn:"hnn-Latn-PH",hno:"hno-Arab-PK",ho:"ho-Latn-PG",hoc:"hoc-Deva-IN",hoj:"hoj-Deva-IN",hot:"hot-Latn-ZZ",hr:"hr-Latn-HR",hsb:"hsb-Latn-DE",hsn:"hsn-Hans-CN",ht:"ht-Latn-HT",hu:"hu-Latn-HU",hui:"hui-Latn-ZZ",hy:"hy-Armn-AM",hz:"hz-Latn-NA",ia:"ia-Latn-001",ian:"ian-Latn-ZZ",iar:"iar-Latn-ZZ",iba:"iba-Latn-MY",ibb:"ibb-Latn-NG",iby:"iby-Latn-ZZ",ica:"ica-Latn-ZZ",ich:"ich-Latn-ZZ",id:"id-Latn-ID",idd:"idd-Latn-ZZ",idi:"idi-Latn-ZZ",idu:"idu-Latn-ZZ",ife:"ife-Latn-TG",ig:"ig-Latn-NG",igb:"igb-Latn-ZZ",ige:"ige-Latn-ZZ",ii:"ii-Yiii-CN",ijj:"ijj-Latn-ZZ",ik:"ik-Latn-US",ikk:"ikk-Latn-ZZ",ikt:"ikt-Latn-CA",ikw:"ikw-Latn-ZZ",ikx:"ikx-Latn-ZZ",ilo:"ilo-Latn-PH",imo:"imo-Latn-ZZ",in:"in-Latn-ID",inh:"inh-Cyrl-RU",io:"io-Latn-001",iou:"iou-Latn-ZZ",iri:"iri-Latn-ZZ",is:"is-Latn-IS",it:"it-Latn-IT",iu:"iu-Cans-CA",iw:"iw-Hebr-IL",iwm:"iwm-Latn-ZZ",iws:"iws-Latn-ZZ",izh:"izh-Latn-RU",izi:"izi-Latn-ZZ",ja:"ja-Jpan-JP",jab:"jab-Latn-ZZ",jam:"jam-Latn-JM",jbo:"jbo-Latn-001",jbu:"jbu-Latn-ZZ",jen:"jen-Latn-ZZ",jgk:"jgk-Latn-ZZ",jgo:"jgo-Latn-CM",ji:"ji-Hebr-UA",jib:"jib-Latn-ZZ",jmc:"jmc-Latn-TZ",jml:"jml-Deva-NP",jra:"jra-Latn-ZZ",jut:"jut-Latn-DK",jv:"jv-Latn-ID",jw:"jw-Latn-ID",ka:"ka-Geor-GE",kaa:"kaa-Cyrl-UZ",kab:"kab-Latn-DZ",kac:"kac-Latn-MM",kad:"kad-Latn-ZZ",kai:"kai-Latn-ZZ",kaj:"kaj-Latn-NG",kam:"kam-Latn-KE",kao:"kao-Latn-ML",kbd:"kbd-Cyrl-RU",kbm:"kbm-Latn-ZZ",kbp:"kbp-Latn-ZZ",kbq:"kbq-Latn-ZZ",kbx:"kbx-Latn-ZZ",kby:"kby-Arab-NE",kcg:"kcg-Latn-NG",kck:"kck-Latn-ZW",kcl:"kcl-Latn-ZZ",kct:"kct-Latn-ZZ",kde:"kde-Latn-TZ",kdh:"kdh-Arab-TG",kdl:"kdl-Latn-ZZ",kdt:"kdt-Thai-TH",kea:"kea-Latn-CV",ken:"ken-Latn-CM",kez:"kez-Latn-ZZ",kfo:"kfo-Latn-CI",kfr:"kfr-Deva-IN",kfy:"kfy-Deva-IN",kg:"kg-Latn-CD",kge:"kge-Latn-ID",kgf:"kgf-Latn-ZZ",kgp:"kgp-Latn-BR",kha:"kha-Latn-IN",khb:"khb-Talu-CN",khn:"khn-Deva-IN",khq:"khq-Latn-ML",khs:"khs-Latn-ZZ",kht:"kht-Mymr-IN",khw:"khw-Arab-PK",khz:"khz-Latn-ZZ",ki:"ki-Latn-KE",kij:"kij-Latn-ZZ",kiu:"kiu-Latn-TR",kiw:"kiw-Latn-ZZ",kj:"kj-Latn-NA",kjd:"kjd-Latn-ZZ",kjg:"kjg-Laoo-LA",kjs:"kjs-Latn-ZZ",kjy:"kjy-Latn-ZZ",kk:"kk-Cyrl-KZ","kk-AF":"kk-Arab-AF","kk-Arab":"kk-Arab-CN","kk-CN":"kk-Arab-CN","kk-IR":"kk-Arab-IR","kk-MN":"kk-Arab-MN",kkc:"kkc-Latn-ZZ",kkj:"kkj-Latn-CM",kl:"kl-Latn-GL",kln:"kln-Latn-KE",klq:"klq-Latn-ZZ",klt:"klt-Latn-ZZ",klx:"klx-Latn-ZZ",km:"km-Khmr-KH",kmb:"kmb-Latn-AO",kmh:"kmh-Latn-ZZ",kmo:"kmo-Latn-ZZ",kms:"kms-Latn-ZZ",kmu:"kmu-Latn-ZZ",kmw:"kmw-Latn-ZZ",kn:"kn-Knda-IN",knf:"knf-Latn-GW",knp:"knp-Latn-ZZ",ko:"ko-Kore-KR",koi:"koi-Cyrl-RU",kok:"kok-Deva-IN",kol:"kol-Latn-ZZ",kos:"kos-Latn-FM",koz:"koz-Latn-ZZ",kpe:"kpe-Latn-LR",kpf:"kpf-Latn-ZZ",kpo:"kpo-Latn-ZZ",kpr:"kpr-Latn-ZZ",kpx:"kpx-Latn-ZZ",kqb:"kqb-Latn-ZZ",kqf:"kqf-Latn-ZZ",kqs:"kqs-Latn-ZZ",kqy:"kqy-Ethi-ZZ",kr:"kr-Latn-ZZ",krc:"krc-Cyrl-RU",kri:"kri-Latn-SL",krj:"krj-Latn-PH",krl:"krl-Latn-RU",krs:"krs-Latn-ZZ",kru:"kru-Deva-IN",ks:"ks-Arab-IN",ksb:"ksb-Latn-TZ",ksd:"ksd-Latn-ZZ",ksf:"ksf-Latn-CM",ksh:"ksh-Latn-DE",ksj:"ksj-Latn-ZZ",ksr:"ksr-Latn-ZZ",ktb:"ktb-Ethi-ZZ",ktm:"ktm-Latn-ZZ",kto:"kto-Latn-ZZ",ktr:"ktr-Latn-MY",ku:"ku-Latn-TR","ku-Arab":"ku-Arab-IQ","ku-LB":"ku-Arab-LB",kub:"kub-Latn-ZZ",kud:"kud-Latn-ZZ",kue:"kue-Latn-ZZ",kuj:"kuj-Latn-ZZ",kum:"kum-Cyrl-RU",kun:"kun-Latn-ZZ",kup:"kup-Latn-ZZ",kus:"kus-Latn-ZZ",kv:"kv-Cyrl-RU",kvg:"kvg-Latn-ZZ",kvr:"kvr-Latn-ID",kvx:"kvx-Arab-PK",kw:"kw-Latn-GB",kwj:"kwj-Latn-ZZ",kwo:"kwo-Latn-ZZ",kwq:"kwq-Latn-ZZ",kxa:"kxa-Latn-ZZ",kxc:"kxc-Ethi-ZZ",kxe:"kxe-Latn-ZZ",kxm:"kxm-Thai-TH",kxp:"kxp-Arab-PK",kxw:"kxw-Latn-ZZ",kxz:"kxz-Latn-ZZ",ky:"ky-Cyrl-KG","ky-Arab":"ky-Arab-CN","ky-CN":"ky-Arab-CN","ky-Latn":"ky-Latn-TR","ky-TR":"ky-Latn-TR",kye:"kye-Latn-ZZ",kyx:"kyx-Latn-ZZ",kzj:"kzj-Latn-MY",kzr:"kzr-Latn-ZZ",kzt:"kzt-Latn-MY",la:"la-Latn-VA",lab:"lab-Lina-GR",lad:"lad-Hebr-IL",lag:"lag-Latn-TZ",lah:"lah-Arab-PK",laj:"laj-Latn-UG",las:"las-Latn-ZZ",lb:"lb-Latn-LU",lbe:"lbe-Cyrl-RU",lbu:"lbu-Latn-ZZ",lbw:"lbw-Latn-ID",lcm:"lcm-Latn-ZZ",lcp:"lcp-Thai-CN",ldb:"ldb-Latn-ZZ",led:"led-Latn-ZZ",lee:"lee-Latn-ZZ",lem:"lem-Latn-ZZ",lep:"lep-Lepc-IN",leq:"leq-Latn-ZZ",leu:"leu-Latn-ZZ",lez:"lez-Cyrl-RU",lg:"lg-Latn-UG",lgg:"lgg-Latn-ZZ",li:"li-Latn-NL",lia:"lia-Latn-ZZ",lid:"lid-Latn-ZZ",lif:"lif-Deva-NP","lif-Limb":"lif-Limb-IN",lig:"lig-Latn-ZZ",lih:"lih-Latn-ZZ",lij:"lij-Latn-IT",lis:"lis-Lisu-CN",ljp:"ljp-Latn-ID",lki:"lki-Arab-IR",lkt:"lkt-Latn-US",lle:"lle-Latn-ZZ",lln:"lln-Latn-ZZ",lmn:"lmn-Telu-IN",lmo:"lmo-Latn-IT",lmp:"lmp-Latn-ZZ",ln:"ln-Latn-CD",lns:"lns-Latn-ZZ",lnu:"lnu-Latn-ZZ",lo:"lo-Laoo-LA",loj:"loj-Latn-ZZ",lok:"lok-Latn-ZZ",lol:"lol-Latn-CD",lor:"lor-Latn-ZZ",los:"los-Latn-ZZ",loz:"loz-Latn-ZM",lrc:"lrc-Arab-IR",lt:"lt-Latn-LT",ltg:"ltg-Latn-LV",lu:"lu-Latn-CD",lua:"lua-Latn-CD",luo:"luo-Latn-KE",luy:"luy-Latn-KE",luz:"luz-Arab-IR",lv:"lv-Latn-LV",lwl:"lwl-Thai-TH",lzh:"lzh-Hans-CN",lzz:"lzz-Latn-TR",mad:"mad-Latn-ID",maf:"maf-Latn-CM",mag:"mag-Deva-IN",mai:"mai-Deva-IN",mak:"mak-Latn-ID",man:"man-Latn-GM","man-GN":"man-Nkoo-GN","man-Nkoo":"man-Nkoo-GN",mas:"mas-Latn-KE",maw:"maw-Latn-ZZ",maz:"maz-Latn-MX",mbh:"mbh-Latn-ZZ",mbo:"mbo-Latn-ZZ",mbq:"mbq-Latn-ZZ",mbu:"mbu-Latn-ZZ",mbw:"mbw-Latn-ZZ",mci:"mci-Latn-ZZ",mcp:"mcp-Latn-ZZ",mcq:"mcq-Latn-ZZ",mcr:"mcr-Latn-ZZ",mcu:"mcu-Latn-ZZ",mda:"mda-Latn-ZZ",mde:"mde-Arab-ZZ",mdf:"mdf-Cyrl-RU",mdh:"mdh-Latn-PH",mdj:"mdj-Latn-ZZ",mdr:"mdr-Latn-ID",mdx:"mdx-Ethi-ZZ",med:"med-Latn-ZZ",mee:"mee-Latn-ZZ",mek:"mek-Latn-ZZ",men:"men-Latn-SL",mer:"mer-Latn-KE",met:"met-Latn-ZZ",meu:"meu-Latn-ZZ",mfa:"mfa-Arab-TH",mfe:"mfe-Latn-MU",mfn:"mfn-Latn-ZZ",mfo:"mfo-Latn-ZZ",mfq:"mfq-Latn-ZZ",mg:"mg-Latn-MG",mgh:"mgh-Latn-MZ",mgl:"mgl-Latn-ZZ",mgo:"mgo-Latn-CM",mgp:"mgp-Deva-NP",mgy:"mgy-Latn-TZ",mh:"mh-Latn-MH",mhi:"mhi-Latn-ZZ",mhl:"mhl-Latn-ZZ",mi:"mi-Latn-NZ",mif:"mif-Latn-ZZ",min:"min-Latn-ID",mis:"mis-Hatr-IQ","mis-Medf":"mis-Medf-NG",miw:"miw-Latn-ZZ",mk:"mk-Cyrl-MK",mki:"mki-Arab-ZZ",mkl:"mkl-Latn-ZZ",mkp:"mkp-Latn-ZZ",mkw:"mkw-Latn-ZZ",ml:"ml-Mlym-IN",mle:"mle-Latn-ZZ",mlp:"mlp-Latn-ZZ",mls:"mls-Latn-SD",mmo:"mmo-Latn-ZZ",mmu:"mmu-Latn-ZZ",mmx:"mmx-Latn-ZZ",mn:"mn-Cyrl-MN","mn-CN":"mn-Mong-CN","mn-Mong":"mn-Mong-CN",mna:"mna-Latn-ZZ",mnf:"mnf-Latn-ZZ",mni:"mni-Beng-IN",mnw:"mnw-Mymr-MM",mo:"mo-Latn-RO",moa:"moa-Latn-ZZ",moe:"moe-Latn-CA",moh:"moh-Latn-CA",mos:"mos-Latn-BF",mox:"mox-Latn-ZZ",mpp:"mpp-Latn-ZZ",mps:"mps-Latn-ZZ",mpt:"mpt-Latn-ZZ",mpx:"mpx-Latn-ZZ",mql:"mql-Latn-ZZ",mr:"mr-Deva-IN",mrd:"mrd-Deva-NP",mrj:"mrj-Cyrl-RU",mro:"mro-Mroo-BD",ms:"ms-Latn-MY","ms-CC":"ms-Arab-CC","ms-ID":"ms-Arab-ID",mt:"mt-Latn-MT",mtc:"mtc-Latn-ZZ",mtf:"mtf-Latn-ZZ",mti:"mti-Latn-ZZ",mtr:"mtr-Deva-IN",mua:"mua-Latn-CM",mur:"mur-Latn-ZZ",mus:"mus-Latn-US",mva:"mva-Latn-ZZ",mvn:"mvn-Latn-ZZ",mvy:"mvy-Arab-PK",mwk:"mwk-Latn-ML",mwr:"mwr-Deva-IN",mwv:"mwv-Latn-ID",mww:"mww-Hmnp-US",mxc:"mxc-Latn-ZW",mxm:"mxm-Latn-ZZ",my:"my-Mymr-MM",myk:"myk-Latn-ZZ",mym:"mym-Ethi-ZZ",myv:"myv-Cyrl-RU",myw:"myw-Latn-ZZ",myx:"myx-Latn-UG",myz:"myz-Mand-IR",mzk:"mzk-Latn-ZZ",mzm:"mzm-Latn-ZZ",mzn:"mzn-Arab-IR",mzp:"mzp-Latn-ZZ",mzw:"mzw-Latn-ZZ",mzz:"mzz-Latn-ZZ",na:"na-Latn-NR",nac:"nac-Latn-ZZ",naf:"naf-Latn-ZZ",nak:"nak-Latn-ZZ",nan:"nan-Hans-CN",nap:"nap-Latn-IT",naq:"naq-Latn-NA",nas:"nas-Latn-ZZ",nb:"nb-Latn-NO",nca:"nca-Latn-ZZ",nce:"nce-Latn-ZZ",ncf:"ncf-Latn-ZZ",nch:"nch-Latn-MX",nco:"nco-Latn-ZZ",ncu:"ncu-Latn-ZZ",nd:"nd-Latn-ZW",ndc:"ndc-Latn-MZ",nds:"nds-Latn-DE",ne:"ne-Deva-NP",neb:"neb-Latn-ZZ",new:"new-Deva-NP",nex:"nex-Latn-ZZ",nfr:"nfr-Latn-ZZ",ng:"ng-Latn-NA",nga:"nga-Latn-ZZ",ngb:"ngb-Latn-ZZ",ngl:"ngl-Latn-MZ",nhb:"nhb-Latn-ZZ",nhe:"nhe-Latn-MX",nhw:"nhw-Latn-MX",nif:"nif-Latn-ZZ",nii:"nii-Latn-ZZ",nij:"nij-Latn-ID",nin:"nin-Latn-ZZ",niu:"niu-Latn-NU",niy:"niy-Latn-ZZ",niz:"niz-Latn-ZZ",njo:"njo-Latn-IN",nkg:"nkg-Latn-ZZ",nko:"nko-Latn-ZZ",nl:"nl-Latn-NL",nmg:"nmg-Latn-CM",nmz:"nmz-Latn-ZZ",nn:"nn-Latn-NO",nnf:"nnf-Latn-ZZ",nnh:"nnh-Latn-CM",nnk:"nnk-Latn-ZZ",nnm:"nnm-Latn-ZZ",nnp:"nnp-Wcho-IN",no:"no-Latn-NO",nod:"nod-Lana-TH",noe:"noe-Deva-IN",non:"non-Runr-SE",nop:"nop-Latn-ZZ",nou:"nou-Latn-ZZ",nqo:"nqo-Nkoo-GN",nr:"nr-Latn-ZA",nrb:"nrb-Latn-ZZ",nsk:"nsk-Cans-CA",nsn:"nsn-Latn-ZZ",nso:"nso-Latn-ZA",nss:"nss-Latn-ZZ",ntm:"ntm-Latn-ZZ",ntr:"ntr-Latn-ZZ",nui:"nui-Latn-ZZ",nup:"nup-Latn-ZZ",nus:"nus-Latn-SS",nuv:"nuv-Latn-ZZ",nux:"nux-Latn-ZZ",nv:"nv-Latn-US",nwb:"nwb-Latn-ZZ",nxq:"nxq-Latn-CN",nxr:"nxr-Latn-ZZ",ny:"ny-Latn-MW",nym:"nym-Latn-TZ",nyn:"nyn-Latn-UG",nzi:"nzi-Latn-GH",oc:"oc-Latn-FR",ogc:"ogc-Latn-ZZ",okr:"okr-Latn-ZZ",okv:"okv-Latn-ZZ",om:"om-Latn-ET",ong:"ong-Latn-ZZ",onn:"onn-Latn-ZZ",ons:"ons-Latn-ZZ",opm:"opm-Latn-ZZ",or:"or-Orya-IN",oro:"oro-Latn-ZZ",oru:"oru-Arab-ZZ",os:"os-Cyrl-GE",osa:"osa-Osge-US",ota:"ota-Arab-ZZ",otk:"otk-Orkh-MN",ozm:"ozm-Latn-ZZ",pa:"pa-Guru-IN","pa-Arab":"pa-Arab-PK","pa-PK":"pa-Arab-PK",pag:"pag-Latn-PH",pal:"pal-Phli-IR","pal-Phlp":"pal-Phlp-CN",pam:"pam-Latn-PH",pap:"pap-Latn-AW",pau:"pau-Latn-PW",pbi:"pbi-Latn-ZZ",pcd:"pcd-Latn-FR",pcm:"pcm-Latn-NG",pdc:"pdc-Latn-US",pdt:"pdt-Latn-CA",ped:"ped-Latn-ZZ",peo:"peo-Xpeo-IR",pex:"pex-Latn-ZZ",pfl:"pfl-Latn-DE",phl:"phl-Arab-ZZ",phn:"phn-Phnx-LB",pil:"pil-Latn-ZZ",pip:"pip-Latn-ZZ",pka:"pka-Brah-IN",pko:"pko-Latn-KE",pl:"pl-Latn-PL",pla:"pla-Latn-ZZ",pms:"pms-Latn-IT",png:"png-Latn-ZZ",pnn:"pnn-Latn-ZZ",pnt:"pnt-Grek-GR",pon:"pon-Latn-FM",ppa:"ppa-Deva-IN",ppo:"ppo-Latn-ZZ",pra:"pra-Khar-PK",prd:"prd-Arab-IR",prg:"prg-Latn-001",ps:"ps-Arab-AF",pss:"pss-Latn-ZZ",pt:"pt-Latn-BR",ptp:"ptp-Latn-ZZ",puu:"puu-Latn-GA",pwa:"pwa-Latn-ZZ",qu:"qu-Latn-PE",quc:"quc-Latn-GT",qug:"qug-Latn-EC",rai:"rai-Latn-ZZ",raj:"raj-Deva-IN",rao:"rao-Latn-ZZ",rcf:"rcf-Latn-RE",rej:"rej-Latn-ID",rel:"rel-Latn-ZZ",res:"res-Latn-ZZ",rgn:"rgn-Latn-IT",rhg:"rhg-Arab-MM",ria:"ria-Latn-IN",rif:"rif-Tfng-MA","rif-NL":"rif-Latn-NL",rjs:"rjs-Deva-NP",rkt:"rkt-Beng-BD",rm:"rm-Latn-CH",rmf:"rmf-Latn-FI",rmo:"rmo-Latn-CH",rmt:"rmt-Arab-IR",rmu:"rmu-Latn-SE",rn:"rn-Latn-BI",rna:"rna-Latn-ZZ",rng:"rng-Latn-MZ",ro:"ro-Latn-RO",rob:"rob-Latn-ID",rof:"rof-Latn-TZ",roo:"roo-Latn-ZZ",rro:"rro-Latn-ZZ",rtm:"rtm-Latn-FJ",ru:"ru-Cyrl-RU",rue:"rue-Cyrl-UA",rug:"rug-Latn-SB",rw:"rw-Latn-RW",rwk:"rwk-Latn-TZ",rwo:"rwo-Latn-ZZ",ryu:"ryu-Kana-JP",sa:"sa-Deva-IN",saf:"saf-Latn-GH",sah:"sah-Cyrl-RU",saq:"saq-Latn-KE",sas:"sas-Latn-ID",sat:"sat-Latn-IN",sav:"sav-Latn-SN",saz:"saz-Saur-IN",sba:"sba-Latn-ZZ",sbe:"sbe-Latn-ZZ",sbp:"sbp-Latn-TZ",sc:"sc-Latn-IT",sck:"sck-Deva-IN",scl:"scl-Arab-ZZ",scn:"scn-Latn-IT",sco:"sco-Latn-GB",scs:"scs-Latn-CA",sd:"sd-Arab-PK","sd-Deva":"sd-Deva-IN","sd-Khoj":"sd-Khoj-IN","sd-Sind":"sd-Sind-IN",sdc:"sdc-Latn-IT",sdh:"sdh-Arab-IR",se:"se-Latn-NO",sef:"sef-Latn-CI",seh:"seh-Latn-MZ",sei:"sei-Latn-MX",ses:"ses-Latn-ML",sg:"sg-Latn-CF",sga:"sga-Ogam-IE",sgs:"sgs-Latn-LT",sgw:"sgw-Ethi-ZZ",sgz:"sgz-Latn-ZZ",shi:"shi-Tfng-MA",shk:"shk-Latn-ZZ",shn:"shn-Mymr-MM",shu:"shu-Arab-ZZ",si:"si-Sinh-LK",sid:"sid-Latn-ET",sig:"sig-Latn-ZZ",sil:"sil-Latn-ZZ",sim:"sim-Latn-ZZ",sjr:"sjr-Latn-ZZ",sk:"sk-Latn-SK",skc:"skc-Latn-ZZ",skr:"skr-Arab-PK",sks:"sks-Latn-ZZ",sl:"sl-Latn-SI",sld:"sld-Latn-ZZ",sli:"sli-Latn-PL",sll:"sll-Latn-ZZ",sly:"sly-Latn-ID",sm:"sm-Latn-WS",sma:"sma-Latn-SE",smj:"smj-Latn-SE",smn:"smn-Latn-FI",smp:"smp-Samr-IL",smq:"smq-Latn-ZZ",sms:"sms-Latn-FI",sn:"sn-Latn-ZW",snc:"snc-Latn-ZZ",snk:"snk-Latn-ML",snp:"snp-Latn-ZZ",snx:"snx-Latn-ZZ",sny:"sny-Latn-ZZ",so:"so-Latn-SO",sog:"sog-Sogd-UZ",sok:"sok-Latn-ZZ",soq:"soq-Latn-ZZ",sou:"sou-Thai-TH",soy:"soy-Latn-ZZ",spd:"spd-Latn-ZZ",spl:"spl-Latn-ZZ",sps:"sps-Latn-ZZ",sq:"sq-Latn-AL",sr:"sr-Cyrl-RS","sr-ME":"sr-Latn-ME","sr-RO":"sr-Latn-RO","sr-RU":"sr-Latn-RU","sr-TR":"sr-Latn-TR",srb:"srb-Sora-IN",srn:"srn-Latn-SR",srr:"srr-Latn-SN",srx:"srx-Deva-IN",ss:"ss-Latn-ZA",ssd:"ssd-Latn-ZZ",ssg:"ssg-Latn-ZZ",ssy:"ssy-Latn-ER",st:"st-Latn-ZA",stk:"stk-Latn-ZZ",stq:"stq-Latn-DE",su:"su-Latn-ID",sua:"sua-Latn-ZZ",sue:"sue-Latn-ZZ",suk:"suk-Latn-TZ",sur:"sur-Latn-ZZ",sus:"sus-Latn-GN",sv:"sv-Latn-SE",sw:"sw-Latn-TZ",swb:"swb-Arab-YT",swc:"swc-Latn-CD",swg:"swg-Latn-DE",swp:"swp-Latn-ZZ",swv:"swv-Deva-IN",sxn:"sxn-Latn-ID",sxw:"sxw-Latn-ZZ",syl:"syl-Beng-BD",syr:"syr-Syrc-IQ",szl:"szl-Latn-PL",ta:"ta-Taml-IN",taj:"taj-Deva-NP",tal:"tal-Latn-ZZ",tan:"tan-Latn-ZZ",taq:"taq-Latn-ZZ",tbc:"tbc-Latn-ZZ",tbd:"tbd-Latn-ZZ",tbf:"tbf-Latn-ZZ",tbg:"tbg-Latn-ZZ",tbo:"tbo-Latn-ZZ",tbw:"tbw-Latn-PH",tbz:"tbz-Latn-ZZ",tci:"tci-Latn-ZZ",tcy:"tcy-Knda-IN",tdd:"tdd-Tale-CN",tdg:"tdg-Deva-NP",tdh:"tdh-Deva-NP",tdu:"tdu-Latn-MY",te:"te-Telu-IN",ted:"ted-Latn-ZZ",tem:"tem-Latn-SL",teo:"teo-Latn-UG",tet:"tet-Latn-TL",tfi:"tfi-Latn-ZZ",tg:"tg-Cyrl-TJ","tg-Arab":"tg-Arab-PK","tg-PK":"tg-Arab-PK",tgc:"tgc-Latn-ZZ",tgo:"tgo-Latn-ZZ",tgu:"tgu-Latn-ZZ",th:"th-Thai-TH",thl:"thl-Deva-NP",thq:"thq-Deva-NP",thr:"thr-Deva-NP",ti:"ti-Ethi-ET",tif:"tif-Latn-ZZ",tig:"tig-Ethi-ER",tik:"tik-Latn-ZZ",tim:"tim-Latn-ZZ",tio:"tio-Latn-ZZ",tiv:"tiv-Latn-NG",tk:"tk-Latn-TM",tkl:"tkl-Latn-TK",tkr:"tkr-Latn-AZ",tkt:"tkt-Deva-NP",tl:"tl-Latn-PH",tlf:"tlf-Latn-ZZ",tlx:"tlx-Latn-ZZ",tly:"tly-Latn-AZ",tmh:"tmh-Latn-NE",tmy:"tmy-Latn-ZZ",tn:"tn-Latn-ZA",tnh:"tnh-Latn-ZZ",to:"to-Latn-TO",tof:"tof-Latn-ZZ",tog:"tog-Latn-MW",toq:"toq-Latn-ZZ",tpi:"tpi-Latn-PG",tpm:"tpm-Latn-ZZ",tpz:"tpz-Latn-ZZ",tqo:"tqo-Latn-ZZ",tr:"tr-Latn-TR",tru:"tru-Latn-TR",trv:"trv-Latn-TW",trw:"trw-Arab-ZZ",ts:"ts-Latn-ZA",tsd:"tsd-Grek-GR",tsf:"tsf-Deva-NP",tsg:"tsg-Latn-PH",tsj:"tsj-Tibt-BT",tsw:"tsw-Latn-ZZ",tt:"tt-Cyrl-RU",ttd:"ttd-Latn-ZZ",tte:"tte-Latn-ZZ",ttj:"ttj-Latn-UG",ttr:"ttr-Latn-ZZ",tts:"tts-Thai-TH",ttt:"ttt-Latn-AZ",tuh:"tuh-Latn-ZZ",tul:"tul-Latn-ZZ",tum:"tum-Latn-MW",tuq:"tuq-Latn-ZZ",tvd:"tvd-Latn-ZZ",tvl:"tvl-Latn-TV",tvu:"tvu-Latn-ZZ",twh:"twh-Latn-ZZ",twq:"twq-Latn-NE",txg:"txg-Tang-CN",ty:"ty-Latn-PF",tya:"tya-Latn-ZZ",tyv:"tyv-Cyrl-RU",tzm:"tzm-Latn-MA",ubu:"ubu-Latn-ZZ",udm:"udm-Cyrl-RU",ug:"ug-Arab-CN","ug-Cyrl":"ug-Cyrl-KZ","ug-KZ":"ug-Cyrl-KZ","ug-MN":"ug-Cyrl-MN",uga:"uga-Ugar-SY",uk:"uk-Cyrl-UA",uli:"uli-Latn-FM",umb:"umb-Latn-AO",und:"en-Latn-US","und-002":"en-Latn-NG","und-003":"en-Latn-US","und-005":"pt-Latn-BR","und-009":"en-Latn-AU","und-011":"en-Latn-NG","und-013":"es-Latn-MX","und-014":"sw-Latn-TZ","und-015":"ar-Arab-EG","und-017":"sw-Latn-CD","und-018":"en-Latn-ZA","und-019":"en-Latn-US","und-021":"en-Latn-US","und-029":"es-Latn-CU","und-030":"zh-Hans-CN","und-034":"hi-Deva-IN","und-035":"id-Latn-ID","und-039":"it-Latn-IT","und-053":"en-Latn-AU","und-054":"en-Latn-PG","und-057":"en-Latn-GU","und-061":"sm-Latn-WS","und-142":"zh-Hans-CN","und-143":"uz-Latn-UZ","und-145":"ar-Arab-SA","und-150":"ru-Cyrl-RU","und-151":"ru-Cyrl-RU","und-154":"en-Latn-GB","und-155":"de-Latn-DE","und-202":"en-Latn-NG","und-419":"es-Latn-419","und-AD":"ca-Latn-AD","und-Adlm":"ff-Adlm-GN","und-AE":"ar-Arab-AE","und-AF":"fa-Arab-AF","und-Aghb":"lez-Aghb-RU","und-Ahom":"aho-Ahom-IN","und-AL":"sq-Latn-AL","und-AM":"hy-Armn-AM","und-AO":"pt-Latn-AO","und-AQ":"und-Latn-AQ","und-AR":"es-Latn-AR","und-Arab":"ar-Arab-EG","und-Arab-CC":"ms-Arab-CC","und-Arab-CN":"ug-Arab-CN","und-Arab-GB":"ks-Arab-GB","und-Arab-ID":"ms-Arab-ID","und-Arab-IN":"ur-Arab-IN","und-Arab-KH":"cja-Arab-KH","und-Arab-MM":"rhg-Arab-MM","und-Arab-MN":"kk-Arab-MN","und-Arab-MU":"ur-Arab-MU","und-Arab-NG":"ha-Arab-NG","und-Arab-PK":"ur-Arab-PK","und-Arab-TG":"apd-Arab-TG","und-Arab-TH":"mfa-Arab-TH","und-Arab-TJ":"fa-Arab-TJ","und-Arab-TR":"az-Arab-TR","und-Arab-YT":"swb-Arab-YT","und-Armi":"arc-Armi-IR","und-Armn":"hy-Armn-AM","und-AS":"sm-Latn-AS","und-AT":"de-Latn-AT","und-Avst":"ae-Avst-IR","und-AW":"nl-Latn-AW","und-AX":"sv-Latn-AX","und-AZ":"az-Latn-AZ","und-BA":"bs-Latn-BA","und-Bali":"ban-Bali-ID","und-Bamu":"bax-Bamu-CM","und-Bass":"bsq-Bass-LR","und-Batk":"bbc-Batk-ID","und-BD":"bn-Beng-BD","und-BE":"nl-Latn-BE","und-Beng":"bn-Beng-BD","und-BF":"fr-Latn-BF","und-BG":"bg-Cyrl-BG","und-BH":"ar-Arab-BH","und-Bhks":"sa-Bhks-IN","und-BI":"rn-Latn-BI","und-BJ":"fr-Latn-BJ","und-BL":"fr-Latn-BL","und-BN":"ms-Latn-BN","und-BO":"es-Latn-BO","und-Bopo":"zh-Bopo-TW","und-BQ":"pap-Latn-BQ","und-BR":"pt-Latn-BR","und-Brah":"pka-Brah-IN","und-Brai":"fr-Brai-FR","und-BT":"dz-Tibt-BT","und-Bugi":"bug-Bugi-ID","und-Buhd":"bku-Buhd-PH","und-BV":"und-Latn-BV","und-BY":"be-Cyrl-BY","und-Cakm":"ccp-Cakm-BD","und-Cans":"cr-Cans-CA","und-Cari":"xcr-Cari-TR","und-CD":"sw-Latn-CD","und-CF":"fr-Latn-CF","und-CG":"fr-Latn-CG","und-CH":"de-Latn-CH","und-Cham":"cjm-Cham-VN","und-Cher":"chr-Cher-US","und-CI":"fr-Latn-CI","und-CL":"es-Latn-CL","und-CM":"fr-Latn-CM","und-CN":"zh-Hans-CN","und-CO":"es-Latn-CO","und-Copt":"cop-Copt-EG","und-CP":"und-Latn-CP","und-Cprt":"grc-Cprt-CY","und-CR":"es-Latn-CR","und-CU":"es-Latn-CU","und-CV":"pt-Latn-CV","und-CW":"pap-Latn-CW","und-CY":"el-Grek-CY","und-Cyrl":"ru-Cyrl-RU","und-Cyrl-AL":"mk-Cyrl-AL","und-Cyrl-BA":"sr-Cyrl-BA","und-Cyrl-GE":"ab-Cyrl-GE","und-Cyrl-GR":"mk-Cyrl-GR","und-Cyrl-MD":"uk-Cyrl-MD","und-Cyrl-RO":"bg-Cyrl-RO","und-Cyrl-SK":"uk-Cyrl-SK","und-Cyrl-TR":"kbd-Cyrl-TR","und-Cyrl-XK":"sr-Cyrl-XK","und-CZ":"cs-Latn-CZ","und-DE":"de-Latn-DE","und-Deva":"hi-Deva-IN","und-Deva-BT":"ne-Deva-BT","und-Deva-FJ":"hif-Deva-FJ","und-Deva-MU":"bho-Deva-MU","und-Deva-PK":"btv-Deva-PK","und-DJ":"aa-Latn-DJ","und-DK":"da-Latn-DK","und-DO":"es-Latn-DO","und-Dogr":"doi-Dogr-IN","und-Dupl":"fr-Dupl-FR","und-DZ":"ar-Arab-DZ","und-EA":"es-Latn-EA","und-EC":"es-Latn-EC","und-EE":"et-Latn-EE","und-EG":"ar-Arab-EG","und-Egyp":"egy-Egyp-EG","und-EH":"ar-Arab-EH","und-Elba":"sq-Elba-AL","und-Elym":"arc-Elym-IR","und-ER":"ti-Ethi-ER","und-ES":"es-Latn-ES","und-ET":"am-Ethi-ET","und-Ethi":"am-Ethi-ET","und-EU":"en-Latn-GB","und-EZ":"de-Latn-EZ","und-FI":"fi-Latn-FI","und-FO":"fo-Latn-FO","und-FR":"fr-Latn-FR","und-GA":"fr-Latn-GA","und-GE":"ka-Geor-GE","und-Geor":"ka-Geor-GE","und-GF":"fr-Latn-GF","und-GH":"ak-Latn-GH","und-GL":"kl-Latn-GL","und-Glag":"cu-Glag-BG","und-GN":"fr-Latn-GN","und-Gong":"wsg-Gong-IN","und-Gonm":"esg-Gonm-IN","und-Goth":"got-Goth-UA","und-GP":"fr-Latn-GP","und-GQ":"es-Latn-GQ","und-GR":"el-Grek-GR","und-Gran":"sa-Gran-IN","und-Grek":"el-Grek-GR","und-Grek-TR":"bgx-Grek-TR","und-GS":"und-Latn-GS","und-GT":"es-Latn-GT","und-Gujr":"gu-Gujr-IN","und-Guru":"pa-Guru-IN","und-GW":"pt-Latn-GW","und-Hanb":"zh-Hanb-TW","und-Hang":"ko-Hang-KR","und-Hani":"zh-Hani-CN","und-Hano":"hnn-Hano-PH","und-Hans":"zh-Hans-CN","und-Hant":"zh-Hant-TW","und-Hatr":"mis-Hatr-IQ","und-Hebr":"he-Hebr-IL","und-Hebr-CA":"yi-Hebr-CA","und-Hebr-GB":"yi-Hebr-GB","und-Hebr-SE":"yi-Hebr-SE","und-Hebr-UA":"yi-Hebr-UA","und-Hebr-US":"yi-Hebr-US","und-Hira":"ja-Hira-JP","und-HK":"zh-Hant-HK","und-Hluw":"hlu-Hluw-TR","und-HM":"und-Latn-HM","und-Hmng":"hnj-Hmng-LA","und-Hmnp":"mww-Hmnp-US","und-HN":"es-Latn-HN","und-HR":"hr-Latn-HR","und-HT":"ht-Latn-HT","und-HU":"hu-Latn-HU","und-Hung":"hu-Hung-HU","und-IC":"es-Latn-IC","und-ID":"id-Latn-ID","und-IL":"he-Hebr-IL","und-IN":"hi-Deva-IN","und-IQ":"ar-Arab-IQ","und-IR":"fa-Arab-IR","und-IS":"is-Latn-IS","und-IT":"it-Latn-IT","und-Ital":"ett-Ital-IT","und-Jamo":"ko-Jamo-KR","und-Java":"jv-Java-ID","und-JO":"ar-Arab-JO","und-JP":"ja-Jpan-JP","und-Jpan":"ja-Jpan-JP","und-Kali":"eky-Kali-MM","und-Kana":"ja-Kana-JP","und-KE":"sw-Latn-KE","und-KG":"ky-Cyrl-KG","und-KH":"km-Khmr-KH","und-Khar":"pra-Khar-PK","und-Khmr":"km-Khmr-KH","und-Khoj":"sd-Khoj-IN","und-KM":"ar-Arab-KM","und-Knda":"kn-Knda-IN","und-Kore":"ko-Kore-KR","und-KP":"ko-Kore-KP","und-KR":"ko-Kore-KR","und-Kthi":"bho-Kthi-IN","und-KW":"ar-Arab-KW","und-KZ":"ru-Cyrl-KZ","und-LA":"lo-Laoo-LA","und-Lana":"nod-Lana-TH","und-Laoo":"lo-Laoo-LA","und-Latn-AF":"tk-Latn-AF","und-Latn-AM":"ku-Latn-AM","und-Latn-CN":"za-Latn-CN","und-Latn-CY":"tr-Latn-CY","und-Latn-DZ":"fr-Latn-DZ","und-Latn-ET":"en-Latn-ET","und-Latn-GE":"ku-Latn-GE","und-Latn-IR":"tk-Latn-IR","und-Latn-KM":"fr-Latn-KM","und-Latn-MA":"fr-Latn-MA","und-Latn-MK":"sq-Latn-MK","und-Latn-MM":"kac-Latn-MM","und-Latn-MO":"pt-Latn-MO","und-Latn-MR":"fr-Latn-MR","und-Latn-RU":"krl-Latn-RU","und-Latn-SY":"fr-Latn-SY","und-Latn-TN":"fr-Latn-TN","und-Latn-TW":"trv-Latn-TW","und-Latn-UA":"pl-Latn-UA","und-LB":"ar-Arab-LB","und-Lepc":"lep-Lepc-IN","und-LI":"de-Latn-LI","und-Limb":"lif-Limb-IN","und-Lina":"lab-Lina-GR","und-Linb":"grc-Linb-GR","und-Lisu":"lis-Lisu-CN","und-LK":"si-Sinh-LK","und-LS":"st-Latn-LS","und-LT":"lt-Latn-LT","und-LU":"fr-Latn-LU","und-LV":"lv-Latn-LV","und-LY":"ar-Arab-LY","und-Lyci":"xlc-Lyci-TR","und-Lydi":"xld-Lydi-TR","und-MA":"ar-Arab-MA","und-Mahj":"hi-Mahj-IN","und-Maka":"mak-Maka-ID","und-Mand":"myz-Mand-IR","und-Mani":"xmn-Mani-CN","und-Marc":"bo-Marc-CN","und-MC":"fr-Latn-MC","und-MD":"ro-Latn-MD","und-ME":"sr-Latn-ME","und-Medf":"mis-Medf-NG","und-Mend":"men-Mend-SL","und-Merc":"xmr-Merc-SD","und-Mero":"xmr-Mero-SD","und-MF":"fr-Latn-MF","und-MG":"mg-Latn-MG","und-MK":"mk-Cyrl-MK","und-ML":"bm-Latn-ML","und-Mlym":"ml-Mlym-IN","und-MM":"my-Mymr-MM","und-MN":"mn-Cyrl-MN","und-MO":"zh-Hant-MO","und-Modi":"mr-Modi-IN","und-Mong":"mn-Mong-CN","und-MQ":"fr-Latn-MQ","und-MR":"ar-Arab-MR","und-Mroo":"mro-Mroo-BD","und-MT":"mt-Latn-MT","und-Mtei":"mni-Mtei-IN","und-MU":"mfe-Latn-MU","und-Mult":"skr-Mult-PK","und-MV":"dv-Thaa-MV","und-MX":"es-Latn-MX","und-MY":"ms-Latn-MY","und-Mymr":"my-Mymr-MM","und-Mymr-IN":"kht-Mymr-IN","und-Mymr-TH":"mnw-Mymr-TH","und-MZ":"pt-Latn-MZ","und-NA":"af-Latn-NA","und-Nand":"sa-Nand-IN","und-Narb":"xna-Narb-SA","und-Nbat":"arc-Nbat-JO","und-NC":"fr-Latn-NC","und-NE":"ha-Latn-NE","und-Newa":"new-Newa-NP","und-NI":"es-Latn-NI","und-Nkoo":"man-Nkoo-GN","und-NL":"nl-Latn-NL","und-NO":"nb-Latn-NO","und-NP":"ne-Deva-NP","und-Nshu":"zhx-Nshu-CN","und-Ogam":"sga-Ogam-IE","und-Olck":"sat-Olck-IN","und-OM":"ar-Arab-OM","und-Orkh":"otk-Orkh-MN","und-Orya":"or-Orya-IN","und-Osge":"osa-Osge-US","und-Osma":"so-Osma-SO","und-PA":"es-Latn-PA","und-Palm":"arc-Palm-SY","und-Pauc":"ctd-Pauc-MM","und-PE":"es-Latn-PE","und-Perm":"kv-Perm-RU","und-PF":"fr-Latn-PF","und-PG":"tpi-Latn-PG","und-PH":"fil-Latn-PH","und-Phag":"lzh-Phag-CN","und-Phli":"pal-Phli-IR","und-Phlp":"pal-Phlp-CN","und-Phnx":"phn-Phnx-LB","und-PK":"ur-Arab-PK","und-PL":"pl-Latn-PL","und-Plrd":"hmd-Plrd-CN","und-PM":"fr-Latn-PM","und-PR":"es-Latn-PR","und-Prti":"xpr-Prti-IR","und-PS":"ar-Arab-PS","und-PT":"pt-Latn-PT","und-PW":"pau-Latn-PW","und-PY":"gn-Latn-PY","und-QA":"ar-Arab-QA","und-QO":"en-Latn-DG","und-RE":"fr-Latn-RE","und-Rjng":"rej-Rjng-ID","und-RO":"ro-Latn-RO","und-Rohg":"rhg-Rohg-MM","und-RS":"sr-Cyrl-RS","und-RU":"ru-Cyrl-RU","und-Runr":"non-Runr-SE","und-RW":"rw-Latn-RW","und-SA":"ar-Arab-SA","und-Samr":"smp-Samr-IL","und-Sarb":"xsa-Sarb-YE","und-Saur":"saz-Saur-IN","und-SC":"fr-Latn-SC","und-SD":"ar-Arab-SD","und-SE":"sv-Latn-SE","und-Sgnw":"ase-Sgnw-US","und-Shaw":"en-Shaw-GB","und-Shrd":"sa-Shrd-IN","und-SI":"sl-Latn-SI","und-Sidd":"sa-Sidd-IN","und-Sind":"sd-Sind-IN","und-Sinh":"si-Sinh-LK","und-SJ":"nb-Latn-SJ","und-SK":"sk-Latn-SK","und-SM":"it-Latn-SM","und-SN":"fr-Latn-SN","und-SO":"so-Latn-SO","und-Sogd":"sog-Sogd-UZ","und-Sogo":"sog-Sogo-UZ","und-Sora":"srb-Sora-IN","und-Soyo":"cmg-Soyo-MN","und-SR":"nl-Latn-SR","und-ST":"pt-Latn-ST","und-Sund":"su-Sund-ID","und-SV":"es-Latn-SV","und-SY":"ar-Arab-SY","und-Sylo":"syl-Sylo-BD","und-Syrc":"syr-Syrc-IQ","und-Tagb":"tbw-Tagb-PH","und-Takr":"doi-Takr-IN","und-Tale":"tdd-Tale-CN","und-Talu":"khb-Talu-CN","und-Taml":"ta-Taml-IN","und-Tang":"txg-Tang-CN","und-Tavt":"blt-Tavt-VN","und-TD":"fr-Latn-TD","und-Telu":"te-Telu-IN","und-TF":"fr-Latn-TF","und-Tfng":"zgh-Tfng-MA","und-TG":"fr-Latn-TG","und-Tglg":"fil-Tglg-PH","und-TH":"th-Thai-TH","und-Thaa":"dv-Thaa-MV","und-Thai":"th-Thai-TH","und-Thai-CN":"lcp-Thai-CN","und-Thai-KH":"kdt-Thai-KH","und-Thai-LA":"kdt-Thai-LA","und-Tibt":"bo-Tibt-CN","und-Tirh":"mai-Tirh-IN","und-TJ":"tg-Cyrl-TJ","und-TK":"tkl-Latn-TK","und-TL":"pt-Latn-TL","und-TM":"tk-Latn-TM","und-TN":"ar-Arab-TN","und-TO":"to-Latn-TO","und-TR":"tr-Latn-TR","und-TV":"tvl-Latn-TV","und-TW":"zh-Hant-TW","und-TZ":"sw-Latn-TZ","und-UA":"uk-Cyrl-UA","und-UG":"sw-Latn-UG","und-Ugar":"uga-Ugar-SY","und-UY":"es-Latn-UY","und-UZ":"uz-Latn-UZ","und-VA":"it-Latn-VA","und-Vaii":"vai-Vaii-LR","und-VE":"es-Latn-VE","und-VN":"vi-Latn-VN","und-VU":"bi-Latn-VU","und-Wara":"hoc-Wara-IN","und-Wcho":"nnp-Wcho-IN","und-WF":"fr-Latn-WF","und-WS":"sm-Latn-WS","und-XK":"sq-Latn-XK","und-Xpeo":"peo-Xpeo-IR","und-Xsux":"akk-Xsux-IQ","und-YE":"ar-Arab-YE","und-Yiii":"ii-Yiii-CN","und-YT":"fr-Latn-YT","und-Zanb":"cmg-Zanb-MN","und-ZW":"sn-Latn-ZW",unr:"unr-Beng-IN","unr-Deva":"unr-Deva-NP","unr-NP":"unr-Deva-NP",unx:"unx-Beng-IN",uok:"uok-Latn-ZZ",ur:"ur-Arab-PK",uri:"uri-Latn-ZZ",urt:"urt-Latn-ZZ",urw:"urw-Latn-ZZ",usa:"usa-Latn-ZZ",utr:"utr-Latn-ZZ",uvh:"uvh-Latn-ZZ",uvl:"uvl-Latn-ZZ",uz:"uz-Latn-UZ","uz-AF":"uz-Arab-AF","uz-Arab":"uz-Arab-AF","uz-CN":"uz-Cyrl-CN",vag:"vag-Latn-ZZ",vai:"vai-Vaii-LR",van:"van-Latn-ZZ",ve:"ve-Latn-ZA",vec:"vec-Latn-IT",vep:"vep-Latn-RU",vi:"vi-Latn-VN",vic:"vic-Latn-SX",viv:"viv-Latn-ZZ",vls:"vls-Latn-BE",vmf:"vmf-Latn-DE",vmw:"vmw-Latn-MZ",vo:"vo-Latn-001",vot:"vot-Latn-RU",vro:"vro-Latn-EE",vun:"vun-Latn-TZ",vut:"vut-Latn-ZZ",wa:"wa-Latn-BE",wae:"wae-Latn-CH",waj:"waj-Latn-ZZ",wal:"wal-Ethi-ET",wan:"wan-Latn-ZZ",war:"war-Latn-PH",wbp:"wbp-Latn-AU",wbq:"wbq-Telu-IN",wbr:"wbr-Deva-IN",wci:"wci-Latn-ZZ",wer:"wer-Latn-ZZ",wgi:"wgi-Latn-ZZ",whg:"whg-Latn-ZZ",wib:"wib-Latn-ZZ",wiu:"wiu-Latn-ZZ",wiv:"wiv-Latn-ZZ",wja:"wja-Latn-ZZ",wji:"wji-Latn-ZZ",wls:"wls-Latn-WF",wmo:"wmo-Latn-ZZ",wnc:"wnc-Latn-ZZ",wni:"wni-Arab-KM",wnu:"wnu-Latn-ZZ",wo:"wo-Latn-SN",wob:"wob-Latn-ZZ",wos:"wos-Latn-ZZ",wrs:"wrs-Latn-ZZ",wsg:"wsg-Gong-IN",wsk:"wsk-Latn-ZZ",wtm:"wtm-Deva-IN",wuu:"wuu-Hans-CN",wuv:"wuv-Latn-ZZ",wwa:"wwa-Latn-ZZ",xav:"xav-Latn-BR",xbi:"xbi-Latn-ZZ",xcr:"xcr-Cari-TR",xes:"xes-Latn-ZZ",xh:"xh-Latn-ZA",xla:"xla-Latn-ZZ",xlc:"xlc-Lyci-TR",xld:"xld-Lydi-TR",xmf:"xmf-Geor-GE",xmn:"xmn-Mani-CN",xmr:"xmr-Merc-SD",xna:"xna-Narb-SA",xnr:"xnr-Deva-IN",xog:"xog-Latn-UG",xon:"xon-Latn-ZZ",xpr:"xpr-Prti-IR",xrb:"xrb-Latn-ZZ",xsa:"xsa-Sarb-YE",xsi:"xsi-Latn-ZZ",xsm:"xsm-Latn-ZZ",xsr:"xsr-Deva-NP",xwe:"xwe-Latn-ZZ",yam:"yam-Latn-ZZ",yao:"yao-Latn-MZ",yap:"yap-Latn-FM",yas:"yas-Latn-ZZ",yat:"yat-Latn-ZZ",yav:"yav-Latn-CM",yay:"yay-Latn-ZZ",yaz:"yaz-Latn-ZZ",yba:"yba-Latn-ZZ",ybb:"ybb-Latn-CM",yby:"yby-Latn-ZZ",yer:"yer-Latn-ZZ",ygr:"ygr-Latn-ZZ",ygw:"ygw-Latn-ZZ",yi:"yi-Hebr-001",yko:"yko-Latn-ZZ",yle:"yle-Latn-ZZ",ylg:"ylg-Latn-ZZ",yll:"yll-Latn-ZZ",yml:"yml-Latn-ZZ",yo:"yo-Latn-NG",yon:"yon-Latn-ZZ",yrb:"yrb-Latn-ZZ",yre:"yre-Latn-ZZ",yrl:"yrl-Latn-BR",yss:"yss-Latn-ZZ",yua:"yua-Latn-MX",yue:"yue-Hant-HK","yue-CN":"yue-Hans-CN","yue-Hans":"yue-Hans-CN",yuj:"yuj-Latn-ZZ",yut:"yut-Latn-ZZ",yuw:"yuw-Latn-ZZ",za:"za-Latn-CN",zag:"zag-Latn-SD",zdj:"zdj-Arab-KM",zea:"zea-Latn-NL",zgh:"zgh-Tfng-MA",zh:"zh-Hans-CN","zh-AU":"zh-Hant-AU","zh-BN":"zh-Hant-BN","zh-Bopo":"zh-Bopo-TW","zh-GB":"zh-Hant-GB","zh-GF":"zh-Hant-GF","zh-Hanb":"zh-Hanb-TW","zh-Hant":"zh-Hant-TW","zh-HK":"zh-Hant-HK","zh-ID":"zh-Hant-ID","zh-MO":"zh-Hant-MO","zh-MY":"zh-Hant-MY","zh-PA":"zh-Hant-PA","zh-PF":"zh-Hant-PF","zh-PH":"zh-Hant-PH","zh-SR":"zh-Hant-SR","zh-TH":"zh-Hant-TH","zh-TW":"zh-Hant-TW","zh-US":"zh-Hant-US","zh-VN":"zh-Hant-VN",zhx:"zhx-Nshu-CN",zia:"zia-Latn-ZZ",zlm:"zlm-Latn-TG",zmi:"zmi-Latn-MY",zne:"zne-Latn-ZZ",zu:"zu-Latn-ZA",zza:"zza-Latn-TR"}}},{main:{"en-AU":{identity:{version:{_cldrVersion:"36"},language:"en",territory:"AU"},numbers:{defaultNumberingSystem:"latn",otherNumberingSystems:{native:"latn"},minimumGroupingDigits:"1","symbols-numberSystem-latn":{decimal:".",group:",",list:";",percentSign:"%",plusSign:"+",minusSign:"-",exponential:"e",superscriptingExponent:"×",perMille:"‰",infinity:"∞",nan:"NaN",timeSeparator:":"},"decimalFormats-numberSystem-latn":{standard:"#,##0.###",long:{decimalFormat:{"1000-count-one":"0 thousand","1000-count-other":"0 thousand","10000-count-one":"00 thousand","10000-count-other":"00 thousand","100000-count-one":"000 thousand","100000-count-other":"000 thousand","1000000-count-one":"0 million","1000000-count-other":"0 million","********-count-one":"00 million","********-count-other":"00 million","********0-count-one":"000 million","********0-count-other":"000 million","********00-count-one":"0 billion","********00-count-other":"0 billion","********000-count-one":"00 billion","********000-count-other":"00 billion","********0000-count-one":"000 billion","********0000-count-other":"000 billion","********00000-count-one":"0 trillion","********00000-count-other":"0 trillion","**************-count-one":"00 trillion","**************-count-other":"00 trillion","***************-count-one":"000 trillion","***************-count-other":"000 trillion"}},short:{decimalFormat:{"1000-count-one":"0K","1000-count-other":"0K","10000-count-one":"00K","10000-count-other":"00K","100000-count-one":"000K","100000-count-other":"000K","1000000-count-one":"0M","1000000-count-other":"0M","********-count-one":"00M","********-count-other":"00M","********0-count-one":"000M","********0-count-other":"000M","********00-count-one":"0B","********00-count-other":"0B","********000-count-one":"00B","********000-count-other":"00B","********0000-count-one":"000B","********0000-count-other":"000B","********00000-count-one":"0T","********00000-count-other":"0T","**************-count-one":"00T","**************-count-other":"00T","***************-count-one":"000T","***************-count-other":"000T"}}},"scientificFormats-numberSystem-latn":{standard:"#E0"},"percentFormats-numberSystem-latn":{standard:"#,##0%"},"currencyFormats-numberSystem-latn":{currencySpacing:{beforeCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "},afterCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "}},standard:"¤#,##0.00",accounting:"¤#,##0.00;(¤#,##0.00)",short:{standard:{"1000-count-one":"¤0K","1000-count-other":"¤0K","10000-count-one":"¤00K","10000-count-other":"¤00K","100000-count-one":"¤000K","100000-count-other":"¤000K","1000000-count-one":"¤0M","1000000-count-other":"¤0M","********-count-one":"¤00M","********-count-other":"¤00M","********0-count-one":"¤000M","********0-count-other":"¤000M","********00-count-one":"¤0B","********00-count-other":"¤0B","********000-count-one":"¤00B","********000-count-other":"¤00B","********0000-count-one":"¤000B","********0000-count-other":"¤000B","********00000-count-one":"¤0T","********00000-count-other":"¤0T","**************-count-one":"¤00T","**************-count-other":"¤00T","***************-count-one":"¤000T","***************-count-other":"¤000T"}},"unitPattern-count-one":"{0} {1}","unitPattern-count-other":"{0} {1}"},"miscPatterns-numberSystem-latn":{approximately:"~{0}",atLeast:"{0}+",atMost:"≤{0}",range:"{0}–{1}"},minimalPairs:{"pluralMinimalPairs-count-one":"{0} day","pluralMinimalPairs-count-other":"{0} days",few:"Take the {0}rd right.",one:"Take the {0}st right.",other:"Take the {0}th right.",two:"Take the {0}nd right."}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},numberingSystems:{adlm:{_digits:"𞥐𞥑𞥒𞥓𞥔𞥕𞥖𞥗𞥘𞥙",_type:"numeric"},ahom:{_digits:"𑜰𑜱𑜲𑜳𑜴𑜵𑜶𑜷𑜸𑜹",_type:"numeric"},arab:{_digits:"٠١٢٣٤٥٦٧٨٩",_type:"numeric"},arabext:{_digits:"۰۱۲۳۴۵۶۷۸۹",_type:"numeric"},armn:{_rules:"armenian-upper",_type:"algorithmic"},armnlow:{_rules:"armenian-lower",_type:"algorithmic"},bali:{_digits:"᭐᭑᭒᭓᭔᭕᭖᭗᭘᭙",_type:"numeric"},beng:{_digits:"০১২৩৪৫৬৭৮৯",_type:"numeric"},bhks:{_digits:"𑱐𑱑𑱒𑱓𑱔𑱕𑱖𑱗𑱘𑱙",_type:"numeric"},brah:{_digits:"𑁦𑁧𑁨𑁩𑁪𑁫𑁬𑁭𑁮𑁯",_type:"numeric"},cakm:{_digits:"𑄶𑄷𑄸𑄹𑄺𑄻𑄼𑄽𑄾𑄿",_type:"numeric"},cham:{_digits:"꩐꩑꩒꩓꩔꩕꩖꩗꩘꩙",_type:"numeric"},cyrl:{_rules:"cyrillic-lower",_type:"algorithmic"},deva:{_digits:"०१२३४५६७८९",_type:"numeric"},ethi:{_rules:"ethiopic",_type:"algorithmic"},fullwide:{_digits:"０１２３４５６７８９",_type:"numeric"},geor:{_rules:"georgian",_type:"algorithmic"},gong:{_digits:"𑶠𑶡𑶢𑶣𑶤𑶥𑶦𑶧𑶨𑶩",_type:"numeric"},gonm:{_digits:"𑵐𑵑𑵒𑵓𑵔𑵕𑵖𑵗𑵘𑵙",_type:"numeric"},grek:{_rules:"greek-upper",_type:"algorithmic"},greklow:{_rules:"greek-lower",_type:"algorithmic"},gujr:{_digits:"૦૧૨૩૪૫૬૭૮૯",_type:"numeric"},guru:{_digits:"੦੧੨੩੪੫੬੭੮੯",_type:"numeric"},hanidays:{_rules:"zh/SpelloutRules/spellout-numbering-days",_type:"algorithmic"},hanidec:{_digits:"〇一二三四五六七八九",_type:"numeric"},hans:{_rules:"zh/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hansfin:{_rules:"zh/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hant:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hantfin:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hebr:{_rules:"hebrew",_type:"algorithmic"},hmng:{_digits:"𖭐𖭑𖭒𖭓𖭔𖭕𖭖𖭗𖭘𖭙",_type:"numeric"},hmnp:{_digits:"𞅀𞅁𞅂𞅃𞅄𞅅𞅆𞅇𞅈𞅉",_type:"numeric"},java:{_digits:"꧐꧑꧒꧓꧔꧕꧖꧗꧘꧙",_type:"numeric"},jpan:{_rules:"ja/SpelloutRules/spellout-cardinal",_type:"algorithmic"},jpanfin:{_rules:"ja/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},jpanyear:{_rules:"ja/SpelloutRules/spellout-numbering-year-latn",_type:"algorithmic"},kali:{_digits:"꤀꤁꤂꤃꤄꤅꤆꤇꤈꤉",_type:"numeric"},khmr:{_digits:"០១២៣៤៥៦៧៨៩",_type:"numeric"},knda:{_digits:"೦೧೨೩೪೫೬೭೮೯",_type:"numeric"},lana:{_digits:"᪀᪁᪂᪃᪄᪅᪆᪇᪈᪉",_type:"numeric"},lanatham:{_digits:"᪐᪑᪒᪓᪔᪕᪖᪗᪘᪙",_type:"numeric"},laoo:{_digits:"໐໑໒໓໔໕໖໗໘໙",_type:"numeric"},latn:{_digits:"0123456789",_type:"numeric"},lepc:{_digits:"᱀᱁᱂᱃᱄᱅᱆᱇᱈᱉",_type:"numeric"},limb:{_digits:"᥆᥇᥈᥉᥊᥋᥌᥍᥎᥏",_type:"numeric"},mathbold:{_digits:"𝟎𝟏𝟐𝟑𝟒𝟓𝟔𝟕𝟖𝟗",_type:"numeric"},mathdbl:{_digits:"𝟘𝟙𝟚𝟛𝟜𝟝𝟞𝟟𝟠𝟡",_type:"numeric"},mathmono:{_digits:"𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿",_type:"numeric"},mathsanb:{_digits:"𝟬𝟭𝟮𝟯𝟰𝟱𝟲𝟳𝟴𝟵",_type:"numeric"},mathsans:{_digits:"𝟢𝟣𝟤𝟥𝟦𝟧𝟨𝟩𝟪𝟫",_type:"numeric"},mlym:{_digits:"൦൧൨൩൪൫൬൭൮൯",_type:"numeric"},modi:{_digits:"𑙐𑙑𑙒𑙓𑙔𑙕𑙖𑙗𑙘𑙙",_type:"numeric"},mong:{_digits:"᠐᠑᠒᠓᠔᠕᠖᠗᠘᠙",_type:"numeric"},mroo:{_digits:"𖩠𖩡𖩢𖩣𖩤𖩥𖩦𖩧𖩨𖩩",_type:"numeric"},mtei:{_digits:"꯰꯱꯲꯳꯴꯵꯶꯷꯸꯹",_type:"numeric"},mymr:{_digits:"၀၁၂၃၄၅၆၇၈၉",_type:"numeric"},mymrshan:{_digits:"႐႑႒႓႔႕႖႗႘႙",_type:"numeric"},mymrtlng:{_digits:"꧰꧱꧲꧳꧴꧵꧶꧷꧸꧹",_type:"numeric"},newa:{_digits:"𑑐𑑑𑑒𑑓𑑔𑑕𑑖𑑗𑑘𑑙",_type:"numeric"},nkoo:{_digits:"߀߁߂߃߄߅߆߇߈߉",_type:"numeric"},olck:{_digits:"᱐᱑᱒᱓᱔᱕᱖᱗᱘᱙",_type:"numeric"},orya:{_digits:"୦୧୨୩୪୫୬୭୮୯",_type:"numeric"},osma:{_digits:"𐒠𐒡𐒢𐒣𐒤𐒥𐒦𐒧𐒨𐒩",_type:"numeric"},rohg:{_digits:"𐴰𐴱𐴲𐴳𐴴𐴵𐴶𐴷𐴸𐴹",_type:"numeric"},roman:{_rules:"roman-upper",_type:"algorithmic"},romanlow:{_rules:"roman-lower",_type:"algorithmic"},saur:{_digits:"꣐꣑꣒꣓꣔꣕꣖꣗꣘꣙",_type:"numeric"},shrd:{_digits:"𑇐𑇑𑇒𑇓𑇔𑇕𑇖𑇗𑇘𑇙",_type:"numeric"},sind:{_digits:"𑋰𑋱𑋲𑋳𑋴𑋵𑋶𑋷𑋸𑋹",_type:"numeric"},sinh:{_digits:"෦෧෨෩෪෫෬෭෮෯",_type:"numeric"},sora:{_digits:"𑃰𑃱𑃲𑃳𑃴𑃵𑃶𑃷𑃸𑃹",_type:"numeric"},sund:{_digits:"᮰᮱᮲᮳᮴᮵᮶᮷᮸᮹",_type:"numeric"},takr:{_digits:"𑛀𑛁𑛂𑛃𑛄𑛅𑛆𑛇𑛈𑛉",_type:"numeric"},talu:{_digits:"᧐᧑᧒᧓᧔᧕᧖᧗᧘᧙",_type:"numeric"},taml:{_rules:"tamil",_type:"algorithmic"},tamldec:{_digits:"௦௧௨௩௪௫௬௭௮௯",_type:"numeric"},telu:{_digits:"౦౧౨౩౪౫౬౭౮౯",_type:"numeric"},thai:{_digits:"๐๑๒๓๔๕๖๗๘๙",_type:"numeric"},tibt:{_digits:"༠༡༢༣༤༥༦༧༨༩",_type:"numeric"},tirh:{_digits:"𑓐𑓑𑓒𑓓𑓔𑓕𑓖𑓗𑓘𑓙",_type:"numeric"},vaii:{_digits:"꘠꘡꘢꘣꘤꘥꘦꘧꘨꘩",_type:"numeric"},wara:{_digits:"𑣠𑣡𑣢𑣣𑣤𑣥𑣦𑣧𑣨𑣩",_type:"numeric"},wcho:{_digits:"𞋰𞋱𞋲𞋳𞋴𞋵𞋶𞋷𞋸𞋹",_type:"numeric"}}}},{main:{"en-AU":{identity:{version:{_cldrVersion:"36"},language:"en",territory:"AU"},numbers:{currencies:{ADP:{displayName:"Andorran Peseta","displayName-count-one":"Andorran peseta","displayName-count-other":"Andorran pesetas",symbol:"ADP"},AED:{displayName:"United Arab Emirates Dirham","displayName-count-one":"UAE dirham","displayName-count-other":"UAE dirhams",symbol:"AED"},AFA:{displayName:"Afghan Afghani (1927–2002)","displayName-count-one":"Afghan afghani (1927–2002)","displayName-count-other":"Afghan afghanis (1927–2002)",symbol:"AFA"},AFN:{displayName:"Afghan Afghani","displayName-count-one":"Afghan Afghani","displayName-count-other":"Afghan Afghanis",symbol:"AFN"},ALK:{displayName:"Albanian Lek (1946–1965)","displayName-count-one":"Albanian lek (1946–1965)","displayName-count-other":"Albanian lekë (1946–1965)",symbol:"ALK"},ALL:{displayName:"Albanian Lek","displayName-count-one":"Albanian lek","displayName-count-other":"Albanian lekë",symbol:"ALL"},AMD:{displayName:"Armenian Dram","displayName-count-one":"Armenian dram","displayName-count-other":"Armenian drams",symbol:"AMD"},ANG:{displayName:"Netherlands Antillean Guilder","displayName-count-one":"Netherlands Antillean guilder","displayName-count-other":"Netherlands Antillean guilders",symbol:"ANG"},AOA:{displayName:"Angolan Kwanza","displayName-count-one":"Angolan kwanza","displayName-count-other":"Angolan kwanzas",symbol:"AOA","symbol-alt-narrow":"Kz"},AOK:{displayName:"Angolan Kwanza (1977–1991)","displayName-count-one":"Angolan kwanza (1977–1991)","displayName-count-other":"Angolan kwanzas (1977–1991)",symbol:"AOK"},AON:{displayName:"Angolan New Kwanza (1990–2000)","displayName-count-one":"Angolan new kwanza (1990–2000)","displayName-count-other":"Angolan new kwanzas (1990–2000)",symbol:"AON"},AOR:{displayName:"Angolan Readjusted Kwanza (1995–1999)","displayName-count-one":"Angolan readjusted kwanza (1995–1999)","displayName-count-other":"Angolan readjusted kwanzas (1995–1999)",symbol:"AOR"},ARA:{displayName:"Argentine Austral","displayName-count-one":"Argentine austral","displayName-count-other":"Argentine australs",symbol:"ARA"},ARL:{displayName:"Argentine Peso Ley (1970–1983)","displayName-count-one":"Argentine peso ley (1970–1983)","displayName-count-other":"Argentine pesos ley (1970–1983)",symbol:"ARL"},ARM:{displayName:"Argentine Peso (1881–1970)","displayName-count-one":"Argentine peso (1881–1970)","displayName-count-other":"Argentine pesos (1881–1970)",symbol:"ARM"},ARP:{displayName:"Argentine Peso (1983–1985)","displayName-count-one":"Argentine peso (1983–1985)","displayName-count-other":"Argentine pesos (1983–1985)",symbol:"ARP"},ARS:{displayName:"Argentine Peso","displayName-count-one":"Argentine peso","displayName-count-other":"Argentine pesos",symbol:"ARS","symbol-alt-narrow":"$"},ATS:{displayName:"Austrian Schilling","displayName-count-one":"Austrian schilling","displayName-count-other":"Austrian schillings",symbol:"ATS"},AUD:{displayName:"Australian Dollar","displayName-count-one":"Australian dollar","displayName-count-other":"Australian dollars",symbol:"$","symbol-alt-narrow":"$"},AWG:{displayName:"Aruban Florin","displayName-count-one":"Aruban florin","displayName-count-other":"Aruban florin",symbol:"AWG"},AZM:{displayName:"Azerbaijani Manat (1993–2006)","displayName-count-one":"Azerbaijani manat (1993–2006)","displayName-count-other":"Azerbaijani manats (1993–2006)",symbol:"AZM"},AZN:{displayName:"Azerbaijani Manat","displayName-count-one":"Azerbaijani manat","displayName-count-other":"Azerbaijani manats",symbol:"AZN"},BAD:{displayName:"Bosnia-Herzegovina Dinar (1992–1994)","displayName-count-one":"Bosnia-Herzegovina dinar (1992–1994)","displayName-count-other":"Bosnia-Herzegovina dinars (1992–1994)",symbol:"BAD"},BAM:{displayName:"Bosnia-Herzegovina Convertible Marka","displayName-count-one":"Bosnia-Herzegovina convertible marka","displayName-count-other":"Bosnia-Herzegovina convertible marka",symbol:"BAM","symbol-alt-narrow":"KM"},BAN:{displayName:"Bosnia-Herzegovina New Dinar (1994–1997)","displayName-count-one":"Bosnia-Herzegovina new dinar (1994–1997)","displayName-count-other":"Bosnia-Herzegovina new dinars (1994–1997)",symbol:"BAN"},BBD:{displayName:"Barbados Dollar","displayName-count-one":"Barbados dollar","displayName-count-other":"Barbados dollars",symbol:"BBD","symbol-alt-narrow":"$"},BDT:{displayName:"Bangladeshi Taka","displayName-count-one":"Bangladeshi taka","displayName-count-other":"Bangladeshi takas",symbol:"BDT","symbol-alt-narrow":"Tk"},BEC:{displayName:"Belgian Franc (convertible)","displayName-count-one":"Belgian franc (convertible)","displayName-count-other":"Belgian francs (convertible)",symbol:"BEC"},BEF:{displayName:"Belgian Franc","displayName-count-one":"Belgian franc","displayName-count-other":"Belgian francs",symbol:"BEF"},BEL:{displayName:"Belgian Franc (financial)","displayName-count-one":"Belgian franc (financial)","displayName-count-other":"Belgian francs (financial)",symbol:"BEL"},BGL:{displayName:"Bulgarian Hard Lev","displayName-count-one":"Bulgarian hard lev","displayName-count-other":"Bulgarian hard leva",symbol:"BGL"},BGM:{displayName:"Bulgarian Socialist Lev","displayName-count-one":"Bulgarian socialist lev","displayName-count-other":"Bulgarian socialist leva",symbol:"BGM"},BGN:{displayName:"Bulgarian Lev","displayName-count-one":"Bulgarian lev","displayName-count-other":"Bulgarian leva",symbol:"BGN"},BGO:{displayName:"Bulgarian Lev (1879–1952)","displayName-count-one":"Bulgarian lev (1879–1952)","displayName-count-other":"Bulgarian leva (1879–1952)",symbol:"BGO"},BHD:{displayName:"Bahraini Dinar","displayName-count-one":"Bahraini dinar","displayName-count-other":"Bahraini dinars",symbol:"BHD"},BIF:{displayName:"Burundian Franc","displayName-count-one":"Burundian franc","displayName-count-other":"Burundian francs",symbol:"BIF"},BMD:{displayName:"Bermuda Dollar","displayName-count-one":"Bermuda dollar","displayName-count-other":"Bermuda dollars",symbol:"BMD","symbol-alt-narrow":"$"},BND:{displayName:"Brunei Dollar","displayName-count-one":"Brunei dollar","displayName-count-other":"Brunei dollars",symbol:"BND","symbol-alt-narrow":"$"},BOB:{displayName:"Boliviano","displayName-count-one":"boliviano","displayName-count-other":"bolivianos",symbol:"BOB","symbol-alt-narrow":"$b"},BOL:{displayName:"Bolivian Boliviano (1863–1963)","displayName-count-one":"Bolivian boliviano (1863–1963)","displayName-count-other":"Bolivian bolivianos (1863–1963)",symbol:"BOL"},BOP:{displayName:"Bolivian Peso","displayName-count-one":"Bolivian peso","displayName-count-other":"Bolivian pesos",symbol:"BOP"},BOV:{displayName:"Bolivian Mvdol","displayName-count-one":"Bolivian mvdol","displayName-count-other":"Bolivian mvdols",symbol:"BOV"},BRB:{displayName:"Brazilian New Cruzeiro (1967–1986)","displayName-count-one":"Brazilian new cruzeiro (1967–1986)","displayName-count-other":"Brazilian new cruzeiros (1967–1986)",symbol:"BRB"},BRC:{displayName:"Brazilian Cruzado (1986–1989)","displayName-count-one":"Brazilian cruzado (1986–1989)","displayName-count-other":"Brazilian cruzados (1986–1989)",symbol:"BRC"},BRE:{displayName:"Brazilian Cruzeiro (1990–1993)","displayName-count-one":"Brazilian cruzeiro (1990–1993)","displayName-count-other":"Brazilian cruzeiros (1990–1993)",symbol:"BRE"},BRL:{displayName:"Brazilian Real","displayName-count-one":"Brazilian real","displayName-count-other":"Brazilian reals",symbol:"BRL","symbol-alt-narrow":"R$"},BRN:{displayName:"Brazilian New Cruzado (1989–1990)","displayName-count-one":"Brazilian new cruzado (1989–1990)","displayName-count-other":"Brazilian new cruzados (1989–1990)",symbol:"BRN"},BRR:{displayName:"Brazilian Cruzeiro (1993–1994)","displayName-count-one":"Brazilian cruzeiro (1993–1994)","displayName-count-other":"Brazilian cruzeiros (1993–1994)",symbol:"BRR"},BRZ:{displayName:"Brazilian Cruzeiro (1942–1967)","displayName-count-one":"Brazilian cruzeiro (1942–1967)","displayName-count-other":"Brazilian cruzeiros (1942–1967)",symbol:"BRZ"},BSD:{displayName:"Bahamian Dollar","displayName-count-one":"Bahamian dollar","displayName-count-other":"Bahamian dollars",symbol:"BSD","symbol-alt-narrow":"$"},BTN:{displayName:"Bhutanese Ngultrum","displayName-count-one":"Bhutanese ngultrum","displayName-count-other":"Bhutanese ngultrums",symbol:"BTN"},BUK:{displayName:"Burmese Kyat","displayName-count-one":"Burmese kyat","displayName-count-other":"Burmese kyats",symbol:"BUK"},BWP:{displayName:"Botswanan Pula","displayName-count-one":"Botswanan pula","displayName-count-other":"Botswanan pulas",symbol:"BWP","symbol-alt-narrow":"P"},BYB:{displayName:"Belarusian New Rouble (1994–1999)","displayName-count-one":"Belarusian new rouble (1994–1999)","displayName-count-other":"Belarusian new roubles (1994–1999)",symbol:"BYB"},BYN:{displayName:"Belarusian Rouble","displayName-count-one":"Belarusian rouble","displayName-count-other":"Belarusian roubles",symbol:"BYN","symbol-alt-narrow":"р."},BYR:{displayName:"Belarusian Rouble (2000–2016)","displayName-count-one":"Belarusian rouble (2000–2016)","displayName-count-other":"Belarusian roubles (2000–2016)",symbol:"BYR"},BZD:{displayName:"Belize Dollar","displayName-count-one":"Belize dollar","displayName-count-other":"Belize dollars",symbol:"BZD","symbol-alt-narrow":"$"},CAD:{displayName:"Canadian Dollar","displayName-count-one":"Canadian dollar","displayName-count-other":"Canadian dollars",symbol:"CAD","symbol-alt-narrow":"$"},CDF:{displayName:"Congolese Franc","displayName-count-one":"Congolese franc","displayName-count-other":"Congolese francs",symbol:"CDF"},CHE:{displayName:"WIR Euro","displayName-count-one":"WIR euro","displayName-count-other":"WIR euros",symbol:"CHE"},CHF:{displayName:"Swiss Franc","displayName-count-one":"Swiss franc","displayName-count-other":"Swiss francs",symbol:"CHF"},CHW:{displayName:"WIR Franc","displayName-count-one":"WIR franc","displayName-count-other":"WIR francs",symbol:"CHW"},CLE:{displayName:"Chilean Escudo","displayName-count-one":"Chilean escudo","displayName-count-other":"Chilean escudos",symbol:"CLE"},CLF:{displayName:"Chilean Unit of Account (UF)","displayName-count-one":"Chilean unit of account (UF)","displayName-count-other":"Chilean units of account (UF)",symbol:"CLF"},CLP:{displayName:"Chilean Peso","displayName-count-one":"Chilean peso","displayName-count-other":"Chilean pesos",symbol:"CLP","symbol-alt-narrow":"$"},CNH:{displayName:"CNH","displayName-count-one":"CNH","displayName-count-other":"CNH",symbol:"CNH"},CNX:{displayName:"Chinese People’s Bank Dollar","displayName-count-one":"Chinese People’s Bank dollar","displayName-count-other":"Chinese People’s Bank dollars",symbol:"CNX"},CNY:{displayName:"Chinese Yuan","displayName-count-one":"Chinese yuan","displayName-count-other":"Chinese yuan",symbol:"CNY","symbol-alt-narrow":"¥"},COP:{displayName:"Colombian Peso","displayName-count-one":"Colombian peso","displayName-count-other":"Colombian pesos",symbol:"COP","symbol-alt-narrow":"$"},COU:{displayName:"Colombian Real Value Unit","displayName-count-one":"Colombian real value unit","displayName-count-other":"Colombian real value units",symbol:"COU"},CRC:{displayName:"Costa Rican Colón","displayName-count-one":"Costa Rican colón","displayName-count-other":"Costa Rican colóns",symbol:"CRC","symbol-alt-narrow":"₡"},CSD:{displayName:"Serbian Dinar (2002–2006)","displayName-count-one":"Serbian dinar (2002–2006)","displayName-count-other":"Serbian dinars (2002–2006)",symbol:"CSD"},CSK:{displayName:"Czechoslovak Hard Koruna","displayName-count-one":"Czechoslovak hard koruna","displayName-count-other":"Czechoslovak hard korunas",symbol:"CSK"},CUC:{displayName:"Cuban Convertible Peso","displayName-count-one":"Cuban convertible peso","displayName-count-other":"Cuban convertible pesos",symbol:"CUC","symbol-alt-narrow":"$"},CUP:{displayName:"Cuban Peso","displayName-count-one":"Cuban peso","displayName-count-other":"Cuban pesos",symbol:"CUP","symbol-alt-narrow":"₱"},CVE:{displayName:"Cape Verdean Escudo","displayName-count-one":"Cape Verdean escudo","displayName-count-other":"Cape Verdean escudos",symbol:"CVE"},CYP:{displayName:"Cypriot Pound","displayName-count-one":"Cypriot pound","displayName-count-other":"Cypriot pounds",symbol:"CYP"},CZK:{displayName:"Czech Koruna","displayName-count-one":"Czech koruna","displayName-count-other":"Czech korunas",symbol:"CZK","symbol-alt-narrow":"Kč"},DDM:{displayName:"East German Mark","displayName-count-one":"East German mark","displayName-count-other":"East German marks",symbol:"DDM"},DEM:{displayName:"German Mark","displayName-count-one":"German mark","displayName-count-other":"German marks",symbol:"DEM"},DJF:{displayName:"Djiboutian Franc","displayName-count-one":"Djiboutian franc","displayName-count-other":"Djiboutian francs",symbol:"DJF"},DKK:{displayName:"Danish Krone","displayName-count-one":"Danish krone","displayName-count-other":"Danish kroner",symbol:"DKK","symbol-alt-narrow":"kr"},DOP:{displayName:"Dominican Peso","displayName-count-one":"Dominican peso","displayName-count-other":"Dominican pesos",symbol:"DOP","symbol-alt-narrow":"$"},DZD:{displayName:"Algerian Dinar","displayName-count-one":"Algerian dinar","displayName-count-other":"Algerian dinars",symbol:"DZD"},ECS:{displayName:"Ecuadorian Sucre","displayName-count-one":"Ecuadorian sucre","displayName-count-other":"Ecuadorian sucres",symbol:"ECS"},ECV:{displayName:"Ecuadorian Unit of Constant Value","displayName-count-one":"Ecuadorian unit of constant value","displayName-count-other":"Ecuadorian units of constant value",symbol:"ECV"},EEK:{displayName:"Estonian Kroon","displayName-count-one":"Estonian kroon","displayName-count-other":"Estonian kroons",symbol:"EEK"},EGP:{displayName:"Egyptian Pound","displayName-count-one":"Egyptian pound","displayName-count-other":"Egyptian pounds",symbol:"EGP","symbol-alt-narrow":"£"},ERN:{displayName:"Eritrean Nakfa","displayName-count-one":"Eritrean nakfa","displayName-count-other":"Eritrean nakfas",symbol:"ERN"},ESA:{displayName:"Spanish Peseta (A account)","displayName-count-one":"Spanish peseta (A account)","displayName-count-other":"Spanish pesetas (A account)",symbol:"ESA"},ESB:{displayName:"Spanish Peseta (convertible account)","displayName-count-one":"Spanish peseta (convertible account)","displayName-count-other":"Spanish pesetas (convertible account)",symbol:"ESB"},ESP:{displayName:"Spanish Peseta","displayName-count-one":"Spanish peseta","displayName-count-other":"Spanish pesetas",symbol:"ESP","symbol-alt-narrow":"₧"},ETB:{displayName:"Ethiopian Birr","displayName-count-one":"Ethiopian birr","displayName-count-other":"Ethiopian birrs",symbol:"ETB"},EUR:{displayName:"Euro","displayName-count-one":"euro","displayName-count-other":"euro",symbol:"EUR","symbol-alt-narrow":"€"},FIM:{displayName:"Finnish Markka","displayName-count-one":"Finnish markka","displayName-count-other":"Finnish markkas",symbol:"FIM"},FJD:{displayName:"Fijian Dollar","displayName-count-one":"Fijian dollar","displayName-count-other":"Fijian dollars",symbol:"FJD","symbol-alt-narrow":"$"},FKP:{displayName:"Falkland Islands Pound","displayName-count-one":"Falkland Islands pound","displayName-count-other":"Falkland Islands pounds",symbol:"FKP","symbol-alt-narrow":"£"},FRF:{displayName:"French Franc","displayName-count-one":"French franc","displayName-count-other":"French francs",symbol:"FRF"},GBP:{displayName:"British Pound","displayName-count-one":"British pound","displayName-count-other":"British pounds",symbol:"GBP","symbol-alt-narrow":"£"},GEK:{displayName:"Georgian Kupon Larit","displayName-count-one":"Georgian kupon larit","displayName-count-other":"Georgian kupon larits",symbol:"GEK"},GEL:{displayName:"Georgian Lari","displayName-count-one":"Georgian lari","displayName-count-other":"Georgian lari",symbol:"GEL","symbol-alt-narrow":"₾"},GHC:{displayName:"Ghanaian Cedi (1979–2007)","displayName-count-one":"Ghanaian cedi (1979–2007)","displayName-count-other":"Ghanaian cedis (1979–2007)",symbol:"GHC"},GHS:{displayName:"Ghanaian Cedi","displayName-count-one":"Ghanaian cedi","displayName-count-other":"Ghanaian cedis",symbol:"GHS"},GIP:{displayName:"Gibraltar Pound","displayName-count-one":"Gibraltar pound","displayName-count-other":"Gibraltar pounds",symbol:"GIP","symbol-alt-narrow":"£"},GMD:{displayName:"Gambian Dalasi","displayName-count-one":"Gambian dalasi","displayName-count-other":"Gambian dalasis",symbol:"GMD"},GNF:{displayName:"Guinean Franc","displayName-count-one":"Guinean franc","displayName-count-other":"Guinean francs",symbol:"GNF","symbol-alt-narrow":"FG"},GNS:{displayName:"Guinean Syli","displayName-count-one":"Guinean syli","displayName-count-other":"Guinean sylis",symbol:"GNS"},GQE:{displayName:"Equatorial Guinean Ekwele","displayName-count-one":"Equatorial Guinean ekwele","displayName-count-other":"Equatorial Guinean ekwele",symbol:"GQE"},GRD:{displayName:"Greek Drachma","displayName-count-one":"Greek drachma","displayName-count-other":"Greek drachmas",symbol:"GRD"},GTQ:{displayName:"Guatemalan Quetzal","displayName-count-one":"Guatemalan quetzal","displayName-count-other":"Guatemalan quetzals",symbol:"GTQ","symbol-alt-narrow":"Q"},GWE:{displayName:"Portuguese Guinea Escudo","displayName-count-one":"Portuguese Guinea escudo","displayName-count-other":"Portuguese Guinea escudos",symbol:"GWE"},GWP:{displayName:"Guinea-Bissau Peso","displayName-count-one":"Guinea-Bissau peso","displayName-count-other":"Guinea-Bissau pesos",symbol:"GWP"},GYD:{displayName:"Guyanaese Dollar","displayName-count-one":"Guyanaese dollar","displayName-count-other":"Guyanaese dollars",symbol:"GYD","symbol-alt-narrow":"$"},HKD:{displayName:"Hong Kong Dollar","displayName-count-one":"Hong Kong dollar","displayName-count-other":"Hong Kong dollars",symbol:"HKD","symbol-alt-narrow":"$"},HNL:{displayName:"Honduran Lempira","displayName-count-one":"Honduran lempira","displayName-count-other":"Honduran lempiras",symbol:"HNL","symbol-alt-narrow":"L"},HRD:{displayName:"Croatian Dinar","displayName-count-one":"Croatian dinar","displayName-count-other":"Croatian dinars",symbol:"HRD"},HRK:{displayName:"Croatian Kuna","displayName-count-one":"Croatian kuna","displayName-count-other":"Croatian kunas",symbol:"HRK","symbol-alt-narrow":"kn"},HTG:{displayName:"Haitian Gourde","displayName-count-one":"Haitian gourde","displayName-count-other":"Haitian gourdes",symbol:"HTG"},HUF:{displayName:"Hungarian Forint","displayName-count-one":"Hungarian forint","displayName-count-other":"Hungarian forints",symbol:"HUF","symbol-alt-narrow":"Ft"},IDR:{displayName:"Indonesian Rupiah","displayName-count-one":"Indonesian rupiah","displayName-count-other":"Indonesian rupiahs",symbol:"IDR","symbol-alt-narrow":"Rp"},IEP:{displayName:"Irish Pound","displayName-count-one":"Irish pound","displayName-count-other":"Irish pounds",symbol:"IEP"},ILP:{displayName:"Israeli Pound","displayName-count-one":"Israeli pound","displayName-count-other":"Israeli pounds",symbol:"ILP"},ILR:{displayName:"Israeli Shekel (1980–1985)","displayName-count-one":"Israeli shekel (1980–1985)","displayName-count-other":"Israeli shekels (1980–1985)",symbol:"ILR"},ILS:{displayName:"Israeli Shekel","displayName-count-one":"Israeli shekel","displayName-count-other":"Israeli sheckles",symbol:"ILS","symbol-alt-narrow":"₪"},INR:{displayName:"Indian Rupee","displayName-count-one":"Indian rupee","displayName-count-other":"Indian rupees",symbol:"INR","symbol-alt-narrow":"₹"},IQD:{displayName:"Iraqi Dinar","displayName-count-one":"Iraqi dinar","displayName-count-other":"Iraqi dinars",symbol:"IQD"},IRR:{displayName:"Iranian Rial","displayName-count-one":"Iranian rial","displayName-count-other":"Iranian rials",symbol:"IRR"},ISJ:{displayName:"Icelandic Króna (1918–1981)","displayName-count-one":"Icelandic króna (1918–1981)","displayName-count-other":"Icelandic krónur (1918–1981)",symbol:"ISJ"},ISK:{displayName:"Icelandic Króna","displayName-count-one":"Icelandic króna","displayName-count-other":"Icelandic krónur",symbol:"ISK","symbol-alt-narrow":"Kr"},ITL:{displayName:"Italian Lira","displayName-count-one":"Italian lira","displayName-count-other":"Italian liras",symbol:"ITL"},JMD:{displayName:"Jamaican Dollar","displayName-count-one":"Jamaican dollar","displayName-count-other":"Jamaican dollars",symbol:"JMD","symbol-alt-narrow":"$"},JOD:{displayName:"Jordanian Dinar","displayName-count-one":"Jordanian dinar","displayName-count-other":"Jordanian dinars",symbol:"JOD"},JPY:{displayName:"Japanese Yen","displayName-count-one":"Japanese yen","displayName-count-other":"Japanese yen",symbol:"JPY","symbol-alt-narrow":"¥"},KES:{displayName:"Kenyan Shilling","displayName-count-one":"Kenyan shilling","displayName-count-other":"Kenyan shillings",symbol:"KES"},KGS:{displayName:"Kyrgystani Som","displayName-count-one":"Kyrgystani som","displayName-count-other":"Kyrgystani soms",symbol:"KGS"},KHR:{displayName:"Cambodian Riel","displayName-count-one":"Cambodian riel","displayName-count-other":"Cambodian riels",symbol:"KHR","symbol-alt-narrow":"៛"},KMF:{displayName:"Comorian Franc","displayName-count-one":"Comorian franc","displayName-count-other":"Comorian francs",symbol:"KMF","symbol-alt-narrow":"CF"},KPW:{displayName:"North Korean Won","displayName-count-one":"North Korean won","displayName-count-other":"North Korean won",symbol:"KPW","symbol-alt-narrow":"₩"},KRH:{displayName:"South Korean Hwan (1953–1962)","displayName-count-one":"South Korean hwan (1953–1962)","displayName-count-other":"South Korean hwan (1953–1962)",symbol:"KRH"},KRO:{displayName:"South Korean Won (1945–1953)","displayName-count-one":"South Korean won (1945–1953)","displayName-count-other":"South Korean won (1945–1953)",symbol:"KRO"},KRW:{displayName:"South Korean Won","displayName-count-one":"South Korean won","displayName-count-other":"South Korean won",symbol:"KRW","symbol-alt-narrow":"₩"},KWD:{displayName:"Kuwaiti Dinar","displayName-count-one":"Kuwaiti dinar","displayName-count-other":"Kuwaiti dinars",symbol:"KWD"},KYD:{displayName:"Cayman Islands Dollar","displayName-count-one":"Cayman Islands dollar","displayName-count-other":"Cayman Islands dollars",symbol:"KYD","symbol-alt-narrow":"$"},KZT:{displayName:"Kazakhstani Tenge","displayName-count-one":"Kazakhstani tenge","displayName-count-other":"Kazakhstani tenge",symbol:"KZT","symbol-alt-narrow":"₸"},LAK:{displayName:"Laotian Kip","displayName-count-one":"Laotian kip","displayName-count-other":"Laotian kip",symbol:"LAK","symbol-alt-narrow":"₭"},LBP:{displayName:"Lebanese Pound","displayName-count-one":"Lebanese pound","displayName-count-other":"Lebanese pounds",symbol:"LBP","symbol-alt-narrow":"L£"},LKR:{displayName:"Sri Lankan Rupee","displayName-count-one":"Sri Lankan rupee","displayName-count-other":"Sri Lankan rupees",symbol:"LKR","symbol-alt-narrow":"Rs"},LRD:{displayName:"Liberian Dollar","displayName-count-one":"Liberian dollar","displayName-count-other":"Liberian dollars",symbol:"LRD","symbol-alt-narrow":"$"},LSL:{displayName:"Lesotho Loti","displayName-count-one":"Lesotho loti","displayName-count-other":"Lesotho lotis",symbol:"LSL"},LTL:{displayName:"Lithuanian Litas","displayName-count-one":"Lithuanian litas","displayName-count-other":"Lithuanian litai",symbol:"LTL","symbol-alt-narrow":"Lt"},LTT:{displayName:"Lithuanian Talonas","displayName-count-one":"Lithuanian talonas","displayName-count-other":"Lithuanian talonases",symbol:"LTT"},LUC:{displayName:"Luxembourgian Convertible Franc","displayName-count-one":"Luxembourgian convertible franc","displayName-count-other":"Luxembourgian convertible francs",symbol:"LUC"},LUF:{displayName:"Luxembourgian Franc","displayName-count-one":"Luxembourgian franc","displayName-count-other":"Luxembourgian francs",symbol:"LUF"},LUL:{displayName:"Luxembourg Financial Franc","displayName-count-one":"Luxembourg financial franc","displayName-count-other":"Luxembourg financial francs",symbol:"LUL"},LVL:{displayName:"Latvian Lats","displayName-count-one":"Latvian lats","displayName-count-other":"Latvian lati",symbol:"LVL","symbol-alt-narrow":"Ls"},LVR:{displayName:"Latvian Rouble","displayName-count-one":"Latvian rouble","displayName-count-other":"Latvian roubles",symbol:"LVR"},LYD:{displayName:"Libyan Dinar","displayName-count-one":"Libyan dinar","displayName-count-other":"Libyan dinars",symbol:"LYD"},MAD:{displayName:"Moroccan Dirham","displayName-count-one":"Moroccan dirham","displayName-count-other":"Moroccan dirhams",symbol:"MAD"},MAF:{displayName:"Moroccan Franc","displayName-count-one":"Moroccan franc","displayName-count-other":"Moroccan francs",symbol:"MAF"},MCF:{displayName:"Monegasque Franc","displayName-count-one":"Monegasque franc","displayName-count-other":"Monegasque francs",symbol:"MCF"},MDC:{displayName:"Moldovan Cupon","displayName-count-one":"Moldovan cupon","displayName-count-other":"Moldovan cupon",symbol:"MDC"},MDL:{displayName:"Moldovan Leu","displayName-count-one":"Moldovan leu","displayName-count-other":"Moldovan lei",symbol:"MDL"},MGA:{displayName:"Malagasy Ariary","displayName-count-one":"Malagasy ariary","displayName-count-other":"Malagasy ariaries",symbol:"MGA","symbol-alt-narrow":"Ar"},MGF:{displayName:"Malagasy Franc","displayName-count-one":"Malagasy franc","displayName-count-other":"Malagasy francs",symbol:"MGF"},MKD:{displayName:"Macedonian Denar","displayName-count-one":"Macedonian denar","displayName-count-other":"Macedonian denar",symbol:"MKD"},MKN:{displayName:"Macedonian Denar (1992–1993)","displayName-count-one":"Macedonian denar (1992–1993)","displayName-count-other":"Macedonian denari (1992–1993)",symbol:"MKN"},MLF:{displayName:"Malian Franc","displayName-count-one":"Malian franc","displayName-count-other":"Malian francs",symbol:"MLF"},MMK:{displayName:"Myanmar Kyat","displayName-count-one":"Myanmar kyat","displayName-count-other":"Myanmar kyats",symbol:"MMK","symbol-alt-narrow":"K"},MNT:{displayName:"Mongolian Tugrik","displayName-count-one":"Mongolian tugrik","displayName-count-other":"Mongolian tugriks",symbol:"MNT","symbol-alt-narrow":"₮"},MOP:{displayName:"Macanese Pataca","displayName-count-one":"Macanese pataca","displayName-count-other":"Macanese patacas",symbol:"MOP"},MRO:{displayName:"Mauritanian Ouguiya (1973–2017)","displayName-count-one":"Mauritanian ouguiya (1973–2017)","displayName-count-other":"Mauritanian ouguiyas (1973–2017)",symbol:"MRO"},MRU:{displayName:"Mauritanian Ouguiya","displayName-count-one":"Mauritanian ouguiya","displayName-count-other":"Mauritanian ouguiyas",symbol:"MRU"},MTL:{displayName:"Maltese Lira","displayName-count-one":"Maltese lira","displayName-count-other":"Maltese lira",symbol:"MTL"},MTP:{displayName:"Maltese Pound","displayName-count-one":"Maltese pound","displayName-count-other":"Maltese pounds",symbol:"MTP"},MUR:{displayName:"Mauritian Rupee","displayName-count-one":"Mauritian rupee","displayName-count-other":"Mauritian rupees",symbol:"MUR","symbol-alt-narrow":"Rs"},MVP:{displayName:"Maldivian Rupee (1947–1981)","displayName-count-one":"Maldivian rupee (1947–1981)","displayName-count-other":"Maldivian rupees (1947–1981)",symbol:"MVP"},MVR:{displayName:"Maldivian Rufiyaa","displayName-count-one":"Maldivian rufiyaa","displayName-count-other":"Maldivian rufiyaas",symbol:"MVR"},MWK:{displayName:"Malawian Kwacha","displayName-count-one":"Malawian kwacha","displayName-count-other":"Malawian kwachas",symbol:"MWK"},MXN:{displayName:"Mexican Peso","displayName-count-one":"Mexican peso","displayName-count-other":"Mexican pesos",symbol:"MXN","symbol-alt-narrow":"$"},MXP:{displayName:"Mexican Silver Peso (1861–1992)","displayName-count-one":"Mexican silver peso (1861–1992)","displayName-count-other":"Mexican silver pesos (1861–1992)",symbol:"MXP"},MXV:{displayName:"Mexican Investment Unit","displayName-count-one":"Mexican investment unit","displayName-count-other":"Mexican investment units",symbol:"MXV"},MYR:{displayName:"Malaysian Ringgit","displayName-count-one":"Malaysian ringgit","displayName-count-other":"Malaysian ringgits",symbol:"MYR","symbol-alt-narrow":"RM"},MZE:{displayName:"Mozambican Escudo","displayName-count-one":"Mozambican escudo","displayName-count-other":"Mozambican escudos",symbol:"MZE"},MZM:{displayName:"Mozambican Metical (1980–2006)","displayName-count-one":"Mozambican metical (1980–2006)","displayName-count-other":"Mozambican meticals (1980–2006)",symbol:"MZM"},MZN:{displayName:"Mozambican Metical","displayName-count-one":"Mozambican metical","displayName-count-other":"Mozambican meticals",symbol:"MZN"},NAD:{displayName:"Namibian Dollar","displayName-count-one":"Namibian dollar","displayName-count-other":"Namibian dollars",symbol:"NAD","symbol-alt-narrow":"$"},NGN:{displayName:"Nigerian Naira","displayName-count-one":"Nigerian naira","displayName-count-other":"Nigerian nairas",symbol:"NGN","symbol-alt-narrow":"₦"},NIC:{displayName:"Nicaraguan Córdoba (1988–1991)","displayName-count-one":"Nicaraguan córdoba (1988–1991)","displayName-count-other":"Nicaraguan córdobas (1988–1991)",symbol:"NIC"},NIO:{displayName:"Nicaraguan Córdoba","displayName-count-one":"Nicaraguan córdoba","displayName-count-other":"Nicaraguan córdobas",symbol:"NIO","symbol-alt-narrow":"C$"},NLG:{displayName:"Dutch Guilder","displayName-count-one":"Dutch guilder","displayName-count-other":"Dutch guilders",symbol:"NLG"},NOK:{displayName:"Norwegian Krone","displayName-count-one":"Norwegian krone","displayName-count-other":"Norwegian kroner",symbol:"NOK","symbol-alt-narrow":"kr"},NPR:{displayName:"Nepalese Rupee","displayName-count-one":"Nepalese rupee","displayName-count-other":"Nepalese rupees",symbol:"NPR","symbol-alt-narrow":"Rs"},NZD:{displayName:"New Zealand Dollar","displayName-count-one":"New Zealand dollar","displayName-count-other":"New Zealand dollars",symbol:"NZD","symbol-alt-narrow":"$"},OMR:{displayName:"Omani Rial","displayName-count-one":"Omani rial","displayName-count-other":"Omani rials",symbol:"OMR"},PAB:{displayName:"Panamanian Balboa","displayName-count-one":"Panamanian balboa","displayName-count-other":"Panamanian balboas",symbol:"PAB"},PEI:{displayName:"Peruvian Inti","displayName-count-one":"Peruvian inti","displayName-count-other":"Peruvian intis",symbol:"PEI"},PEN:{displayName:"Peruvian Sol","displayName-count-one":"Peruvian sol","displayName-count-other":"Peruvian soles",symbol:"PEN"},PES:{displayName:"Peruvian Sol (1863–1965)","displayName-count-one":"Peruvian sol (1863–1965)","displayName-count-other":"Peruvian soles (1863–1965)",symbol:"PES"},PGK:{displayName:"Papua New Guinean Kina","displayName-count-one":"Papua New Guinean kina","displayName-count-other":"Papua New Guinean kinas",symbol:"PGK"},PHP:{displayName:"Philippine Piso","displayName-count-one":"Philippine piso","displayName-count-other":"Philippine pisos",symbol:"PHP","symbol-alt-narrow":"₱"},PKR:{displayName:"Pakistani Rupee","displayName-count-one":"Pakistani rupee","displayName-count-other":"Pakistani rupees",symbol:"PKR","symbol-alt-narrow":"Rs"},PLN:{displayName:"Polish Zloty","displayName-count-one":"Polish zloty","displayName-count-other":"Polish zlotys",symbol:"PLN","symbol-alt-narrow":"zł"},PLZ:{displayName:"Polish Zloty (1950–1995)","displayName-count-one":"Polish zloty (PLZ)","displayName-count-other":"Polish zlotys (PLZ)",symbol:"PLZ"},PTE:{displayName:"Portuguese Escudo","displayName-count-one":"Portuguese escudo","displayName-count-other":"Portuguese escudos",symbol:"PTE"},PYG:{displayName:"Paraguayan Guarani","displayName-count-one":"Paraguayan guarani","displayName-count-other":"Paraguayan guaranis",symbol:"PYG","symbol-alt-narrow":"Gs"},QAR:{displayName:"Qatari Riyal","displayName-count-one":"Qatari riyal","displayName-count-other":"Quatari riyals",symbol:"QAR"},RHD:{displayName:"Rhodesian Dollar","displayName-count-one":"Rhodesian dollar","displayName-count-other":"Rhodesian dollars",symbol:"RHD"},ROL:{displayName:"Romanian Leu (1952–2006)","displayName-count-one":"Romanian leu (1952–2006)","displayName-count-other":"Romanian Lei (1952–2006)",symbol:"ROL"},RON:{displayName:"Romanian Leu","displayName-count-one":"Romanian leu","displayName-count-other":"Romanian lei",symbol:"RON","symbol-alt-narrow":"lei"},RSD:{displayName:"Serbian Dinar","displayName-count-one":"Serbian dinar","displayName-count-other":"Serbian dinars",symbol:"RSD"},RUB:{displayName:"Russian Rouble","displayName-count-one":"Russian rouble","displayName-count-other":"Russian roubles",symbol:"RUB","symbol-alt-narrow":"₽"},RUR:{displayName:"Russian Rouble (1991–1998)","displayName-count-one":"Russian rouble (1991–1998)","displayName-count-other":"Russian roubles (1991–1998)",symbol:"RUR","symbol-alt-narrow":"р."},RWF:{displayName:"Rwandan Franc","displayName-count-one":"Rwandan franc","displayName-count-other":"Rwandan francs",symbol:"RWF","symbol-alt-narrow":"RF"},SAR:{displayName:"Saudi Riyal","displayName-count-one":"Saudi riyal","displayName-count-other":"Saudi riyals",symbol:"SAR"},SBD:{displayName:"Solomon Islands Dollar","displayName-count-one":"Solomon Islands dollar","displayName-count-other":"Solomon Islands dollars",symbol:"SBD","symbol-alt-narrow":"$"},SCR:{displayName:"Seychellois Rupee","displayName-count-one":"Seychellois rupee","displayName-count-other":"Seychellois rupees",symbol:"Rs"},SDD:{displayName:"Sudanese Dinar (1992–2007)","displayName-count-one":"Sudanese dinar (1992–2007)","displayName-count-other":"Sudanese dinars (1992–2007)",symbol:"SDD"},SDG:{displayName:"Sudanese Pound","displayName-count-one":"Sudanese pound","displayName-count-other":"Sudanese pounds",symbol:"SDG"},SDP:{displayName:"Sudanese Pound (1957–1998)","displayName-count-one":"Sudanese pound (1957–1998)","displayName-count-other":"Sudanese pounds (1957–1998)",symbol:"SDP"},SEK:{displayName:"Swedish Krona","displayName-count-one":"Swedish krona","displayName-count-other":"Swedish kronor",symbol:"SEK","symbol-alt-narrow":"Kr"},SGD:{displayName:"Singapore Dollar","displayName-count-one":"Singapore dollar","displayName-count-other":"Singapore dollars",symbol:"SGD","symbol-alt-narrow":"$"},SHP:{displayName:"St Helena Pound","displayName-count-one":"St Helena pound","displayName-count-other":"St Helena pounds",symbol:"SHP","symbol-alt-narrow":"£"},SIT:{displayName:"Slovenian Tolar","displayName-count-one":"Slovenian tolar","displayName-count-other":"Slovenian tolars",symbol:"SIT"},SKK:{displayName:"Slovak Koruna","displayName-count-one":"Slovak koruna","displayName-count-other":"Slovak korunas",symbol:"SKK"},SLL:{displayName:"Sierra Leonean Leone","displayName-count-one":"Sierra Leonean leone","displayName-count-other":"Sierra Leonean leones",symbol:"SLL"},SOS:{displayName:"Somali Shilling","displayName-count-one":"Somali shilling","displayName-count-other":"Somali shillings",symbol:"SOS"},SRD:{displayName:"Suriname Dollar","displayName-count-one":"Suriname dollar","displayName-count-other":"Suriname dollars",symbol:"SRD","symbol-alt-narrow":"$"},SRG:{displayName:"Surinamese Guilder","displayName-count-one":"Surinamese guilder","displayName-count-other":"Surinamese guilders",symbol:"SRG"},SSP:{displayName:"South Sudanese Pound","displayName-count-one":"South Sudanese pound","displayName-count-other":"South Sudanese pounds",symbol:"SSP","symbol-alt-narrow":"£"},STD:{displayName:"São Tomé & Príncipe Dobra (1977–2017)","displayName-count-one":"São Tomé & Príncipe dobra (1977–2017)","displayName-count-other":"São Tomé & Príncipe dobras (1977–2017)",symbol:"STD"},STN:{displayName:"São Tomé & Príncipe Dobra","displayName-count-one":"São Tomé & Príncipe dobra","displayName-count-other":"São Tomé & Príncipe dobras",symbol:"STN","symbol-alt-narrow":"Db"},SUR:{displayName:"Soviet Rouble","displayName-count-one":"Soviet rouble","displayName-count-other":"Soviet roubles",symbol:"SUR"},SVC:{displayName:"Salvadoran Colón","displayName-count-one":"Salvadoran colón","displayName-count-other":"Salvadoran colones",symbol:"SVC"},SYP:{displayName:"Syrian Pound","displayName-count-one":"Syrian pound","displayName-count-other":"Syrian pounds",symbol:"SYP","symbol-alt-narrow":"£"},SZL:{displayName:"Swazi Lilangeni","displayName-count-one":"Swazi lilangeni","displayName-count-other":"Swazi emalangeni",symbol:"SZL"},THB:{displayName:"Thai Baht","displayName-count-one":"Thai baht","displayName-count-other":"Thai baht",symbol:"THB","symbol-alt-narrow":"฿"},TJR:{displayName:"Tajikistani Rouble","displayName-count-one":"Tajikistani rouble","displayName-count-other":"Tajikistani roubles",symbol:"TJR"},TJS:{displayName:"Tajikistani Somoni","displayName-count-one":"Tajikistani somoni","displayName-count-other":"Tajikistani somonis",symbol:"TJS"},TMM:{displayName:"Turkmenistani Manat (1993–2009)","displayName-count-one":"Turkmenistani manat (1993–2009)","displayName-count-other":"Turkmenistani manat (1993–2009)",symbol:"TMM"},TMT:{displayName:"Turkmenistani Manat","displayName-count-one":"Turkmenistani manat","displayName-count-other":"Turkmenistani manat",symbol:"TMT"},TND:{displayName:"Tunisian Dinar","displayName-count-one":"Tunisian dinar","displayName-count-other":"Tunisian dinars",symbol:"TND"},TOP:{displayName:"Tongan Paʻanga","displayName-count-one":"Tongan paʻanga","displayName-count-other":"Tongan paʻanga",symbol:"TOP","symbol-alt-narrow":"T$"},TPE:{displayName:"Timorese Escudo","displayName-count-one":"Timorese escudo","displayName-count-other":"Timorese escudos",symbol:"TPE"},TRL:{displayName:"Turkish Lira (1922–2005)","displayName-count-one":"Turkish lira (1922–2005)","displayName-count-other":"Turkish Lira (1922–2005)",symbol:"TRL"},TRY:{displayName:"Turkish Lira","displayName-count-one":"Turkish lira","displayName-count-other":"Turkish lire",symbol:"TRY","symbol-alt-narrow":"₺","symbol-alt-variant":"TL"},TTD:{displayName:"Trinidad & Tobago Dollar","displayName-count-one":"Trinidad & Tobago dollar","displayName-count-other":"Trinidad & Tobago dollars",symbol:"TTD","symbol-alt-narrow":"$"},TWD:{displayName:"New Taiwan Dollar","displayName-count-one":"New Taiwan dollar","displayName-count-other":"New Taiwan dollars",symbol:"TWD","symbol-alt-narrow":"$"},TZS:{displayName:"Tanzanian Shilling","displayName-count-one":"Tanzanian shilling","displayName-count-other":"Tanzanian shillings",symbol:"TZS"},UAH:{displayName:"Ukrainian Hryvnia","displayName-count-one":"Ukrainian hryvnia","displayName-count-other":"Ukrainian hryvnias",symbol:"UAH","symbol-alt-narrow":"₴"},UAK:{displayName:"Ukrainian Karbovanets","displayName-count-one":"Ukrainian karbovanets","displayName-count-other":"Ukrainian karbovantsiv",symbol:"UAK"},UGS:{displayName:"Ugandan Shilling (1966–1987)","displayName-count-one":"Ugandan shilling (1966–1987)","displayName-count-other":"Ugandan shillings (1966–1987)",symbol:"UGS"},UGX:{displayName:"Ugandan Shilling","displayName-count-one":"Ugandan shilling","displayName-count-other":"Ugandan shillings",symbol:"UGX"},USD:{displayName:"US Dollar","displayName-count-one":"US dollar","displayName-count-other":"US dollars",symbol:"USD","symbol-alt-narrow":"$"},USN:{displayName:"US Dollar (Next day)","displayName-count-one":"US dollar (next day)","displayName-count-other":"US dollars (next day)",symbol:"USN"},USS:{displayName:"US Dollar (Same day)","displayName-count-one":"US dollar (same day)","displayName-count-other":"US dollars (same day)",symbol:"USS"},UYI:{displayName:"Uruguayan Peso (Indexed Units)","displayName-count-one":"Uruguayan peso (indexed units)","displayName-count-other":"Uruguayan pesos (indexed units)",symbol:"UYI"},UYP:{displayName:"Uruguayan Peso (1975–1993)","displayName-count-one":"Uruguayan peso (1975–1993)","displayName-count-other":"Uruguayan pesos (1975–1993)",symbol:"UYP"},UYU:{displayName:"Peso Uruguayo","displayName-count-one":"Uruguayan peso","displayName-count-other":"Uruguayan pesos",symbol:"UYU","symbol-alt-narrow":"$U"},UYW:{displayName:"Uruguayan Nominal Wage Index Unit","displayName-count-one":"Uruguayan nominal wage index unit","displayName-count-other":"Uruguayan nominal wage index units",symbol:"UYW"},UZS:{displayName:"Uzbekistani Som","displayName-count-one":"Uzbekistani som","displayName-count-other":"Uzbekistani soms",symbol:"UZS"},VEB:{displayName:"Venezuelan Bolívar (1871–2008)","displayName-count-one":"Venezuelan bolívar (1871–2008)","displayName-count-other":"Venezuelan bolívars (1871–2008)",symbol:"VEB"},VEF:{displayName:"Venezuelan Bolívar (2008–2018)","displayName-count-one":"Venezuelan bolívar","displayName-count-other":"Venezuelan bolívars",symbol:"VEF","symbol-alt-narrow":"Bs"},VES:{displayName:"VES","displayName-count-one":"VES","displayName-count-other":"VES",symbol:"VES"},VND:{displayName:"Vietnamese Dong","displayName-count-one":"Vietnamese dong","displayName-count-other":"Vietnamese dongs",symbol:"VND","symbol-alt-narrow":"₫"},VNN:{displayName:"Vietnamese Dong (1978–1985)","displayName-count-one":"Vietnamese dong (1978–1985)","displayName-count-other":"Vietnamese dong (1978–1985)",symbol:"VNN"},VUV:{displayName:"Vanuatu Vatu","displayName-count-one":"Vanuatu vatu","displayName-count-other":"Vanuatu vatus",symbol:"VUV"},WST:{displayName:"Samoan Tala","displayName-count-one":"Samoan tala","displayName-count-other":"Samoan talas",symbol:"WST"},XAF:{displayName:"Central African CFA Franc","displayName-count-one":"Central African CFA franc","displayName-count-other":"Central African CFA francs",symbol:"XAF"},XAG:{displayName:"Silver","displayName-count-one":"troy ounce of silver","displayName-count-other":"troy ounces of silver",symbol:"XAG"},XAU:{displayName:"Gold","displayName-count-one":"troy ounce of gold","displayName-count-other":"troy ounces of gold",symbol:"XAU"},XBA:{displayName:"European Composite Unit","displayName-count-one":"European composite unit","displayName-count-other":"European composite units",symbol:"XBA"},XBB:{displayName:"European Monetary Unit","displayName-count-one":"European monetary unit","displayName-count-other":"European monetary units",symbol:"XBB"},XBC:{displayName:"European Unit of Account (XBC)","displayName-count-one":"European unit of account (XBC)","displayName-count-other":"European units of account (XBC)",symbol:"XBC"},XBD:{displayName:"European Unit of Account (XBD)","displayName-count-one":"European unit of account (XBD)","displayName-count-other":"European units of account (XBD)",symbol:"XBD"},XCD:{displayName:"East Caribbean Dollar","displayName-count-one":"East Caribbean dollar","displayName-count-other":"East Caribbean dollars",symbol:"XCD","symbol-alt-narrow":"$"},XDR:{displayName:"Special Drawing Rights","displayName-count-one":"special drawing rights","displayName-count-other":"special drawing rights",symbol:"XDR"},XEU:{displayName:"European Currency Unit","displayName-count-one":"European currency unit","displayName-count-other":"European currency units",symbol:"XEU"},XFO:{displayName:"French Gold Franc","displayName-count-one":"French gold franc","displayName-count-other":"French gold francs",symbol:"XFO"},XFU:{displayName:"French UIC-Franc","displayName-count-one":"French UIC-franc","displayName-count-other":"French UIC-francs",symbol:"XFU"},XOF:{displayName:"West African CFA Franc","displayName-count-one":"West African CFA franc","displayName-count-other":"West African CFA francs",symbol:"XOF"},XPD:{displayName:"Palladium","displayName-count-one":"troy ounce of palladium","displayName-count-other":"troy ounces of palladium",symbol:"XPD"},XPF:{displayName:"CFP Franc","displayName-count-one":"CFP franc","displayName-count-other":"CFP francs",symbol:"CFP"},XPT:{displayName:"Platinum","displayName-count-one":"troy ounce of platinum","displayName-count-other":"troy ounces of platinum",symbol:"XPT"},XRE:{displayName:"RINET Funds","displayName-count-one":"RINET Funds unit","displayName-count-other":"RINET Funds units",symbol:"XRE"},XSU:{displayName:"Sucre","displayName-count-one":"Sucre","displayName-count-other":"Sucres",symbol:"XSU"},XTS:{displayName:"Testing Currency Code","displayName-count-one":"Testing Currency unit","displayName-count-other":"Testing Currency units",symbol:"XTS"},XUA:{displayName:"ADB Unit of Account","displayName-count-one":"ADB unit of account","displayName-count-other":"ADB units of account",symbol:"XUA"},XXX:{displayName:"Unknown Currency","displayName-count-one":"(unknown unit of currency)","displayName-count-other":"(unknown currency)",symbol:"¤"},YDD:{displayName:"Yemeni Dinar","displayName-count-one":"Yemeni dinar","displayName-count-other":"Yemeni dinars",symbol:"YDD"},YER:{displayName:"Yemeni Rial","displayName-count-one":"Yemeni rial","displayName-count-other":"Yemeni rials",symbol:"YER"},YUD:{displayName:"Yugoslavian Hard Dinar (1966–1990)","displayName-count-one":"Yugoslavian hard dinar (1966–1990)","displayName-count-other":"Yugoslavian hard dinars (1966–1990)",symbol:"YUD"},YUM:{displayName:"Yugoslavian New Dinar (1994–2002)","displayName-count-one":"Yugoslavian new dinar (1994–2002)","displayName-count-other":"Yugoslavian new dinars (1994–2002)",symbol:"YUM"},YUN:{displayName:"Yugoslavian Convertible Dinar (1990–1992)","displayName-count-one":"Yugoslavian convertible dinar (1990–1992)","displayName-count-other":"Yugoslavian convertible dinars (1990–1992)",symbol:"YUN"},YUR:{displayName:"Yugoslavian Reformed Dinar (1992–1993)","displayName-count-one":"Yugoslavian reformed dinar (1992–1993)","displayName-count-other":"Yugoslavian reformed dinars (1992–1993)",symbol:"YUR"},ZAL:{displayName:"South African Rand (financial)","displayName-count-one":"South African rand (financial)","displayName-count-other":"South African rands (financial)",symbol:"ZAL"},ZAR:{displayName:"South African Rand","displayName-count-one":"South African rand","displayName-count-other":"South African rand",symbol:"ZAR","symbol-alt-narrow":"R"},ZMK:{displayName:"Zambian Kwacha (1968–2012)","displayName-count-one":"Zambian kwacha (1968–2012)","displayName-count-other":"Zambian kwachas (1968–2012)",symbol:"ZMK"},ZMW:{displayName:"Zambian Kwacha","displayName-count-one":"Zambian kwacha","displayName-count-other":"Zambian kwachas",symbol:"ZMW","symbol-alt-narrow":"ZK"},ZRN:{displayName:"Zairean New Zaire (1993–1998)","displayName-count-one":"Zairean new zaire (1993–1998)","displayName-count-other":"Zairean new zaires (1993–1998)",symbol:"ZRN"},ZRZ:{displayName:"Zairean Zaire (1971–1993)","displayName-count-one":"Zairean zaire (1971–1993)","displayName-count-other":"Zairean zaires (1971–1993)",symbol:"ZRZ"},ZWD:{displayName:"Zimbabwean Dollar (1980–2008)","displayName-count-one":"Zimbabwean dollar (1980–2008)","displayName-count-other":"Zimbabwean dollars (1980–2008)",symbol:"ZWD"},ZWL:{displayName:"Zimbabwean Dollar (2009)","displayName-count-one":"Zimbabwean dollar (2009)","displayName-count-other":"Zimbabwean dollars (2009)",symbol:"ZWL"},ZWR:{displayName:"Zimbabwean Dollar (2008)","displayName-count-one":"Zimbabwean dollar (2008)","displayName-count-other":"Zimbabwean dollars (2008)",symbol:"ZWR"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},currencyData:{fractions:{ADP:{_rounding:"0",_digits:"0"},AFN:{_rounding:"0",_digits:"0"},ALL:{_rounding:"0",_digits:"0"},AMD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},BHD:{_rounding:"0",_digits:"3"},BIF:{_rounding:"0",_digits:"0"},BYN:{_rounding:"0",_digits:"2"},BYR:{_rounding:"0",_digits:"0"},CAD:{_rounding:"0",_digits:"2",_cashRounding:"5"},CHF:{_rounding:"0",_digits:"2",_cashRounding:"5"},CLF:{_rounding:"0",_digits:"4"},CLP:{_rounding:"0",_digits:"0"},COP:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CRC:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CZK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},DEFAULT:{_rounding:"0",_digits:"2"},DJF:{_rounding:"0",_digits:"0"},DKK:{_rounding:"0",_digits:"2",_cashRounding:"50"},ESP:{_rounding:"0",_digits:"0"},GNF:{_rounding:"0",_digits:"0"},GYD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},HUF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IDR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IQD:{_rounding:"0",_digits:"0"},IRR:{_rounding:"0",_digits:"0"},ISK:{_rounding:"0",_digits:"0"},ITL:{_rounding:"0",_digits:"0"},JOD:{_rounding:"0",_digits:"3"},JPY:{_rounding:"0",_digits:"0"},KMF:{_rounding:"0",_digits:"0"},KPW:{_rounding:"0",_digits:"0"},KRW:{_rounding:"0",_digits:"0"},KWD:{_rounding:"0",_digits:"3"},LAK:{_rounding:"0",_digits:"0"},LBP:{_rounding:"0",_digits:"0"},LUF:{_rounding:"0",_digits:"0"},LYD:{_rounding:"0",_digits:"3"},MGA:{_rounding:"0",_digits:"0"},MGF:{_rounding:"0",_digits:"0"},MMK:{_rounding:"0",_digits:"0"},MNT:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},MRO:{_rounding:"0",_digits:"0"},MUR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},NOK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},OMR:{_rounding:"0",_digits:"3"},PKR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},PYG:{_rounding:"0",_digits:"0"},RSD:{_rounding:"0",_digits:"0"},RWF:{_rounding:"0",_digits:"0"},SEK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},SLL:{_rounding:"0",_digits:"0"},SOS:{_rounding:"0",_digits:"0"},STD:{_rounding:"0",_digits:"0"},SYP:{_rounding:"0",_digits:"0"},TMM:{_rounding:"0",_digits:"0"},TND:{_rounding:"0",_digits:"3"},TRL:{_rounding:"0",_digits:"0"},TWD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},TZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},UGX:{_rounding:"0",_digits:"0"},UYI:{_rounding:"0",_digits:"0"},UYW:{_rounding:"0",_digits:"4"},UZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VEF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VND:{_rounding:"0",_digits:"0"},VUV:{_rounding:"0",_digits:"0"},XAF:{_rounding:"0",_digits:"0"},XOF:{_rounding:"0",_digits:"0"},XPF:{_rounding:"0",_digits:"0"},YER:{_rounding:"0",_digits:"0"},ZMK:{_rounding:"0",_digits:"0"},ZWD:{_rounding:"0",_digits:"0"}},region:{AC:[{SHP:{_from:"1976-01-01"}}],AD:[{ESP:{_from:"1873-01-01",_to:"2002-02-28"}},{ADP:{_from:"1936-01-01",_to:"2001-12-31"}},{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],AE:[{AED:{_from:"1973-05-19"}}],AF:[{AFA:{_from:"1927-03-14",_to:"2002-12-31"}},{AFN:{_from:"2002-10-07"}}],AG:[{XCD:{_from:"1965-10-06"}}],AI:[{XCD:{_from:"1965-10-06"}}],AL:[{ALK:{_from:"1946-11-01",_to:"1965-08-16"}},{ALL:{_from:"1965-08-16"}}],AM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-22"}},{AMD:{_from:"1993-11-22"}}],AO:[{AOK:{_from:"1977-01-08",_to:"1991-03-01"}},{AON:{_from:"1990-09-25",_to:"2000-02-01"}},{AOR:{_from:"1995-07-01",_to:"2000-02-01"}},{AOA:{_from:"1999-12-13"}}],AQ:[{XXX:{_tender:"false"}}],AR:[{ARM:{_from:"1881-11-05",_to:"1970-01-01"}},{ARL:{_from:"1970-01-01",_to:"1983-06-01"}},{ARP:{_from:"1983-06-01",_to:"1985-06-14"}},{ARA:{_from:"1985-06-14",_to:"1992-01-01"}},{ARS:{_from:"1992-01-01"}}],AS:[{USD:{_from:"1904-07-16"}}],AT:[{ATS:{_from:"1947-12-04",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],AU:[{AUD:{_from:"1966-02-14"}}],AW:[{ANG:{_from:"1940-05-10",_to:"1986-01-01"}},{AWG:{_from:"1986-01-01"}}],AX:[{EUR:{_from:"1999-01-01"}}],AZ:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-01-01"}},{AZM:{_from:"1993-11-22",_to:"2006-12-31"}},{AZN:{_from:"2006-01-01"}}],BA:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-01"}},{YUR:{_from:"1992-07-01",_to:"1993-10-01"}},{BAD:{_from:"1992-07-01",_to:"1994-08-15"}},{BAN:{_from:"1994-08-15",_to:"1997-07-01"}},{BAM:{_from:"1995-01-01"}}],BB:[{XCD:{_from:"1965-10-06",_to:"1973-12-03"}},{BBD:{_from:"1973-12-03"}}],BD:[{INR:{_from:"1835-08-17",_to:"1948-04-01"}},{PKR:{_from:"1948-04-01",_to:"1972-01-01"}},{BDT:{_from:"1972-01-01"}}],BE:[{NLG:{_from:"1816-12-15",_to:"1831-02-07"}},{BEF:{_from:"1831-02-07",_to:"2002-02-28"}},{BEC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{BEL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],BF:[{XOF:{_from:"1984-08-04"}}],BG:[{BGO:{_from:"1879-07-08",_to:"1952-05-12"}},{BGM:{_from:"1952-05-12",_to:"1962-01-01"}},{BGL:{_from:"1962-01-01",_to:"1999-07-05"}},{BGN:{_from:"1999-07-05"}}],BH:[{BHD:{_from:"1965-10-16"}}],BI:[{BIF:{_from:"1964-05-19"}}],BJ:[{XOF:{_from:"1975-11-30"}}],BL:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],BM:[{BMD:{_from:"1970-02-06"}}],BN:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{BND:{_from:"1967-06-12"}}],BO:[{BOV:{_tender:"false"}},{BOL:{_from:"1863-06-23",_to:"1963-01-01"}},{BOP:{_from:"1963-01-01",_to:"1986-12-31"}},{BOB:{_from:"1987-01-01"}}],BQ:[{ANG:{_from:"2010-10-10",_to:"2011-01-01"}},{USD:{_from:"2011-01-01"}}],BR:[{BRZ:{_from:"1942-11-01",_to:"1967-02-13"}},{BRB:{_from:"1967-02-13",_to:"1986-02-28"}},{BRC:{_from:"1986-02-28",_to:"1989-01-15"}},{BRN:{_from:"1989-01-15",_to:"1990-03-16"}},{BRE:{_from:"1990-03-16",_to:"1993-08-01"}},{BRR:{_from:"1993-08-01",_to:"1994-07-01"}},{BRL:{_from:"1994-07-01"}}],BS:[{BSD:{_from:"1966-05-25"}}],BT:[{INR:{_from:"1907-01-01"}},{BTN:{_from:"1974-04-16"}}],BU:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}}],BV:[{NOK:{_from:"1905-06-07"}}],BW:[{ZAR:{_from:"1961-02-14",_to:"1976-08-23"}},{BWP:{_from:"1976-08-23"}}],BY:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-11-08"}},{BYB:{_from:"1994-08-01",_to:"2000-12-31"}},{BYR:{_from:"2000-01-01",_to:"2017-01-01"}},{BYN:{_from:"2016-07-01"}}],BZ:[{BZD:{_from:"1974-01-01"}}],CA:[{CAD:{_from:"1858-01-01"}}],CC:[{AUD:{_from:"1966-02-14"}}],CD:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-01"}},{CDF:{_from:"1998-07-01"}}],CF:[{XAF:{_from:"1993-01-01"}}],CG:[{XAF:{_from:"1993-01-01"}}],CH:[{CHE:{_tender:"false"}},{CHW:{_tender:"false"}},{CHF:{_from:"1799-03-17"}}],CI:[{XOF:{_from:"1958-12-04"}}],CK:[{NZD:{_from:"1967-07-10"}}],CL:[{CLF:{_tender:"false"}},{CLE:{_from:"1960-01-01",_to:"1975-09-29"}},{CLP:{_from:"1975-09-29"}}],CM:[{XAF:{_from:"1973-04-01"}}],CN:[{CNY:{_from:"1953-03-01"}},{CNX:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{CNH:{_tender:"false",_from:"2010-07-19"}}],CO:[{COU:{_tender:"false"}},{COP:{_from:"1905-01-01"}}],CP:[{XXX:{_tender:"false"}}],CR:[{CRC:{_from:"1896-10-26"}}],CS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-06-03"}},{EUR:{_from:"2003-02-04",_to:"2006-06-03"}}],CU:[{CUP:{_from:"1859-01-01"}},{USD:{_from:"1899-01-01",_to:"1959-01-01"}},{CUC:{_from:"1994-01-01"}}],CV:[{PTE:{_from:"1911-05-22",_to:"1975-07-05"}},{CVE:{_from:"1914-01-01"}}],CW:[{ANG:{_from:"2010-10-10"}}],CX:[{AUD:{_from:"1966-02-14"}}],CY:[{CYP:{_from:"1914-09-10",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],CZ:[{CSK:{_from:"1953-06-01",_to:"1993-03-01"}},{CZK:{_from:"1993-01-01"}}],DD:[{DDM:{_from:"1948-07-20",_to:"1990-10-02"}}],DE:[{DEM:{_from:"1948-06-20",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],DG:[{USD:{_from:"1965-11-08"}}],DJ:[{DJF:{_from:"1977-06-27"}}],DK:[{DKK:{_from:"1873-05-27"}}],DM:[{XCD:{_from:"1965-10-06"}}],DO:[{USD:{_from:"1905-06-21",_to:"1947-10-01"}},{DOP:{_from:"1947-10-01"}}],DZ:[{DZD:{_from:"1964-04-01"}}],EA:[{EUR:{_from:"1999-01-01"}}],EC:[{ECS:{_from:"1884-04-01",_to:"2000-10-02"}},{ECV:{_tender:"false",_from:"1993-05-23",_to:"2000-01-09"}},{USD:{_from:"2000-10-02"}}],EE:[{SUR:{_from:"1961-01-01",_to:"1992-06-20"}},{EEK:{_from:"1992-06-21",_to:"2010-12-31"}},{EUR:{_from:"2011-01-01"}}],EG:[{EGP:{_from:"1885-11-14"}}],EH:[{MAD:{_from:"1976-02-26"}}],ER:[{ETB:{_from:"1993-05-24",_to:"1997-11-08"}},{ERN:{_from:"1997-11-08"}}],ES:[{ESP:{_from:"1868-10-19",_to:"2002-02-28"}},{ESB:{_tender:"false",_from:"1975-01-01",_to:"1994-12-31"}},{ESA:{_tender:"false",_from:"1978-01-01",_to:"1981-12-31"}},{EUR:{_from:"1999-01-01"}}],ET:[{ETB:{_from:"1976-09-15"}}],EU:[{XEU:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{EUR:{_from:"1999-01-01"}}],FI:[{FIM:{_from:"1963-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],FJ:[{FJD:{_from:"1969-01-13"}}],FK:[{FKP:{_from:"1901-01-01"}}],FM:[{JPY:{_from:"1914-10-03",_to:"1944-01-01"}},{USD:{_from:"1944-01-01"}}],FO:[{DKK:{_from:"1948-01-01"}}],FR:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GA:[{XAF:{_from:"1993-01-01"}}],GB:[{GBP:{_from:"1694-07-27"}}],GD:[{XCD:{_from:"1967-02-27"}}],GE:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-06-11"}},{GEK:{_from:"1993-04-05",_to:"1995-09-25"}},{GEL:{_from:"1995-09-23"}}],GF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GG:[{GBP:{_from:"1830-01-01"}}],GH:[{GHC:{_from:"1979-03-09",_to:"2007-12-31"}},{GHS:{_from:"2007-07-03"}}],GI:[{GIP:{_from:"1713-01-01"}}],GL:[{DKK:{_from:"1873-05-27"}}],GM:[{GMD:{_from:"1971-07-01"}}],GN:[{GNS:{_from:"1972-10-02",_to:"1986-01-06"}},{GNF:{_from:"1986-01-06"}}],GP:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GQ:[{GQE:{_from:"1975-07-07",_to:"1986-06-01"}},{XAF:{_from:"1993-01-01"}}],GR:[{GRD:{_from:"1954-05-01",_to:"2002-02-28"}},{EUR:{_from:"2001-01-01"}}],GS:[{GBP:{_from:"1908-01-01"}}],GT:[{GTQ:{_from:"1925-05-27"}}],GU:[{USD:{_from:"1944-08-21"}}],GW:[{GWE:{_from:"1914-01-01",_to:"1976-02-28"}},{GWP:{_from:"1976-02-28",_to:"1997-03-31"}},{XOF:{_from:"1997-03-31"}}],GY:[{GYD:{_from:"1966-05-26"}}],HK:[{HKD:{_from:"1895-02-02"}}],HM:[{AUD:{_from:"1967-02-16"}}],HN:[{HNL:{_from:"1926-04-03"}}],HR:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1991-12-23"}},{HRD:{_from:"1991-12-23",_to:"1995-01-01"}},{HRK:{_from:"1994-05-30"}}],HT:[{HTG:{_from:"1872-08-26"}},{USD:{_from:"1915-01-01"}}],HU:[{HUF:{_from:"1946-07-23"}}],IC:[{EUR:{_from:"1999-01-01"}}],ID:[{IDR:{_from:"1965-12-13"}}],IE:[{GBP:{_from:"1800-01-01",_to:"1922-01-01"}},{IEP:{_from:"1922-01-01",_to:"2002-02-09"}},{EUR:{_from:"1999-01-01"}}],IL:[{ILP:{_from:"1948-08-16",_to:"1980-02-22"}},{ILR:{_from:"1980-02-22",_to:"1985-09-04"}},{ILS:{_from:"1985-09-04"}}],IM:[{GBP:{_from:"1840-01-03"}}],IN:[{INR:{_from:"1835-08-17"}}],IO:[{USD:{_from:"1965-11-08"}}],IQ:[{EGP:{_from:"1920-11-11",_to:"1931-04-19"}},{INR:{_from:"1920-11-11",_to:"1931-04-19"}},{IQD:{_from:"1931-04-19"}}],IR:[{IRR:{_from:"1932-05-13"}}],IS:[{DKK:{_from:"1873-05-27",_to:"1918-12-01"}},{ISJ:{_from:"1918-12-01",_to:"1981-01-01"}},{ISK:{_from:"1981-01-01"}}],IT:[{ITL:{_from:"1862-08-24",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],JE:[{GBP:{_from:"1837-01-01"}}],JM:[{JMD:{_from:"1969-09-08"}}],JO:[{JOD:{_from:"1950-07-01"}}],JP:[{JPY:{_from:"1871-06-01"}}],KE:[{KES:{_from:"1966-09-14"}}],KG:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-05-10"}},{KGS:{_from:"1993-05-10"}}],KH:[{KHR:{_from:"1980-03-20"}}],KI:[{AUD:{_from:"1966-02-14"}}],KM:[{KMF:{_from:"1975-07-06"}}],KN:[{XCD:{_from:"1965-10-06"}}],KP:[{KPW:{_from:"1959-04-17"}}],KR:[{KRO:{_from:"1945-08-15",_to:"1953-02-15"}},{KRH:{_from:"1953-02-15",_to:"1962-06-10"}},{KRW:{_from:"1962-06-10"}}],KW:[{KWD:{_from:"1961-04-01"}}],KY:[{JMD:{_from:"1969-09-08",_to:"1971-01-01"}},{KYD:{_from:"1971-01-01"}}],KZ:[{KZT:{_from:"1993-11-05"}}],LA:[{LAK:{_from:"1979-12-10"}}],LB:[{LBP:{_from:"1948-02-02"}}],LC:[{XCD:{_from:"1965-10-06"}}],LI:[{CHF:{_from:"1921-02-01"}}],LK:[{LKR:{_from:"1978-05-22"}}],LR:[{LRD:{_from:"1944-01-01"}}],LS:[{ZAR:{_from:"1961-02-14"}},{LSL:{_from:"1980-01-22"}}],LT:[{SUR:{_from:"1961-01-01",_to:"1992-10-01"}},{LTT:{_from:"1992-10-01",_to:"1993-06-25"}},{LTL:{_from:"1993-06-25",_to:"2014-12-31"}},{EUR:{_from:"2015-01-01"}}],LU:[{LUF:{_from:"1944-09-04",_to:"2002-02-28"}},{LUC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{LUL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],LV:[{SUR:{_from:"1961-01-01",_to:"1992-07-20"}},{LVR:{_from:"1992-05-07",_to:"1993-10-17"}},{LVL:{_from:"1993-06-28",_to:"2013-12-31"}},{EUR:{_from:"2014-01-01"}}],LY:[{LYD:{_from:"1971-09-01"}}],MA:[{MAF:{_from:"1881-01-01",_to:"1959-10-17"}},{MAD:{_from:"1959-10-17"}}],MC:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{MCF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MD:[{MDC:{_from:"1992-06-01",_to:"1993-11-29"}},{MDL:{_from:"1993-11-29"}}],ME:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{DEM:{_from:"1999-10-02",_to:"2002-05-15"}},{EUR:{_from:"2002-01-01"}}],MF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MG:[{MGF:{_from:"1963-07-01",_to:"2004-12-31"}},{MGA:{_from:"1983-11-01"}}],MH:[{USD:{_from:"1944-01-01"}}],MK:[{MKN:{_from:"1992-04-26",_to:"1993-05-20"}},{MKD:{_from:"1993-05-20"}}],ML:[{XOF:{_from:"1958-11-24",_to:"1962-07-02"}},{MLF:{_from:"1962-07-02",_to:"1984-08-31"}},{XOF:{_from:"1984-06-01"}}],MM:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}},{MMK:{_from:"1989-06-18"}}],MN:[{MNT:{_from:"1915-03-01"}}],MO:[{MOP:{_from:"1901-01-01"}}],MP:[{USD:{_from:"1944-01-01"}}],MQ:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MR:[{XOF:{_from:"1958-11-28",_to:"1973-06-29"}},{MRO:{_from:"1973-06-29",_to:"2018-06-30"}},{MRU:{_from:"2018-01-01"}}],MS:[{XCD:{_from:"1967-02-27"}}],MT:[{MTP:{_from:"1914-08-13",_to:"1968-06-07"}},{MTL:{_from:"1968-06-07",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],MU:[{MUR:{_from:"1934-04-01"}}],MV:[{MVP:{_from:"1947-01-01",_to:"1981-07-01"}},{MVR:{_from:"1981-07-01"}}],MW:[{MWK:{_from:"1971-02-15"}}],MX:[{MXV:{_tender:"false"}},{MXP:{_from:"1822-01-01",_to:"1992-12-31"}},{MXN:{_from:"1993-01-01"}}],MY:[{MYR:{_from:"1963-09-16"}}],MZ:[{MZE:{_from:"1975-06-25",_to:"1980-06-16"}},{MZM:{_from:"1980-06-16",_to:"2006-12-31"}},{MZN:{_from:"2006-07-01"}}],NA:[{ZAR:{_from:"1961-02-14"}},{NAD:{_from:"1993-01-01"}}],NC:[{XPF:{_from:"1985-01-01"}}],NE:[{XOF:{_from:"1958-12-19"}}],NF:[{AUD:{_from:"1966-02-14"}}],NG:[{NGN:{_from:"1973-01-01"}}],NI:[{NIC:{_from:"1988-02-15",_to:"1991-04-30"}},{NIO:{_from:"1991-04-30"}}],NL:[{NLG:{_from:"1813-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],NO:[{SEK:{_from:"1873-05-27",_to:"1905-06-07"}},{NOK:{_from:"1905-06-07"}}],NP:[{INR:{_from:"1870-01-01",_to:"1966-10-17"}},{NPR:{_from:"1933-01-01"}}],NR:[{AUD:{_from:"1966-02-14"}}],NU:[{NZD:{_from:"1967-07-10"}}],NZ:[{NZD:{_from:"1967-07-10"}}],OM:[{OMR:{_from:"1972-11-11"}}],PA:[{PAB:{_from:"1903-11-04"}},{USD:{_from:"1903-11-18"}}],PE:[{PES:{_from:"1863-02-14",_to:"1985-02-01"}},{PEI:{_from:"1985-02-01",_to:"1991-07-01"}},{PEN:{_from:"1991-07-01"}}],PF:[{XPF:{_from:"1945-12-26"}}],PG:[{AUD:{_from:"1966-02-14",_to:"1975-09-16"}},{PGK:{_from:"1975-09-16"}}],PH:[{PHP:{_from:"1946-07-04"}}],PK:[{INR:{_from:"1835-08-17",_to:"1947-08-15"}},{PKR:{_from:"1948-04-01"}}],PL:[{PLZ:{_from:"1950-10-28",_to:"1994-12-31"}},{PLN:{_from:"1995-01-01"}}],PM:[{FRF:{_from:"1972-12-21",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],PN:[{NZD:{_from:"1969-01-13"}}],PR:[{ESP:{_from:"1800-01-01",_to:"1898-12-10"}},{USD:{_from:"1898-12-10"}}],PS:[{JOD:{_from:"1950-07-01",_to:"1967-06-01"}},{ILP:{_from:"1967-06-01",_to:"1980-02-22"}},{ILS:{_from:"1985-09-04"}},{JOD:{_from:"1996-02-12"}}],PT:[{PTE:{_from:"1911-05-22",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],PW:[{USD:{_from:"1944-01-01"}}],PY:[{PYG:{_from:"1943-11-01"}}],QA:[{QAR:{_from:"1973-05-19"}}],RE:[{FRF:{_from:"1975-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],RO:[{ROL:{_from:"1952-01-28",_to:"2006-12-31"}},{RON:{_from:"2005-07-01"}}],RS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-10-25"}},{RSD:{_from:"2006-10-25"}}],RU:[{RUR:{_from:"1991-12-25",_to:"1998-12-31"}},{RUB:{_from:"1999-01-01"}}],RW:[{RWF:{_from:"1964-05-19"}}],SA:[{SAR:{_from:"1952-10-22"}}],SB:[{AUD:{_from:"1966-02-14",_to:"1978-06-30"}},{SBD:{_from:"1977-10-24"}}],SC:[{SCR:{_from:"1903-11-01"}}],SD:[{EGP:{_from:"1889-01-19",_to:"1958-01-01"}},{GBP:{_from:"1889-01-19",_to:"1958-01-01"}},{SDP:{_from:"1957-04-08",_to:"1998-06-01"}},{SDD:{_from:"1992-06-08",_to:"2007-06-30"}},{SDG:{_from:"2007-01-10"}}],SE:[{SEK:{_from:"1873-05-27"}}],SG:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{SGD:{_from:"1967-06-12"}}],SH:[{SHP:{_from:"1917-02-15"}}],SI:[{SIT:{_from:"1992-10-07",_to:"2007-01-14"}},{EUR:{_from:"2007-01-01"}}],SJ:[{NOK:{_from:"1905-06-07"}}],SK:[{CSK:{_from:"1953-06-01",_to:"1992-12-31"}},{SKK:{_from:"1992-12-31",_to:"2009-01-01"}},{EUR:{_from:"2009-01-01"}}],SL:[{GBP:{_from:"1808-11-30",_to:"1966-02-04"}},{SLL:{_from:"1964-08-04"}}],SM:[{ITL:{_from:"1865-12-23",_to:"2001-02-28"}},{EUR:{_from:"1999-01-01"}}],SN:[{XOF:{_from:"1959-04-04"}}],SO:[{SOS:{_from:"1960-07-01"}}],SR:[{NLG:{_from:"1815-11-20",_to:"1940-05-10"}},{SRG:{_from:"1940-05-10",_to:"2003-12-31"}},{SRD:{_from:"2004-01-01"}}],SS:[{SDG:{_from:"2007-01-10",_to:"2011-09-01"}},{SSP:{_from:"2011-07-18"}}],ST:[{STD:{_from:"1977-09-08",_to:"2017-12-31"}},{STN:{_from:"2018-01-01"}}],SU:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}}],SV:[{SVC:{_from:"1919-11-11",_to:"2001-01-01"}},{USD:{_from:"2001-01-01"}}],SX:[{ANG:{_from:"2010-10-10"}}],SY:[{SYP:{_from:"1948-01-01"}}],SZ:[{SZL:{_from:"1974-09-06"}}],TA:[{GBP:{_from:"1938-01-12"}}],TC:[{USD:{_from:"1969-09-08"}}],TD:[{XAF:{_from:"1993-01-01"}}],TF:[{FRF:{_from:"1959-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],TG:[{XOF:{_from:"1958-11-28"}}],TH:[{THB:{_from:"1928-04-15"}}],TJ:[{RUR:{_from:"1991-12-25",_to:"1995-05-10"}},{TJR:{_from:"1995-05-10",_to:"2000-10-25"}},{TJS:{_from:"2000-10-26"}}],TK:[{NZD:{_from:"1967-07-10"}}],TL:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}},{USD:{_from:"1999-10-20"}}],TM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-01"}},{TMM:{_from:"1993-11-01",_to:"2009-01-01"}},{TMT:{_from:"2009-01-01"}}],TN:[{TND:{_from:"1958-11-01"}}],TO:[{TOP:{_from:"1966-02-14"}}],TP:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}}],TR:[{TRL:{_from:"1922-11-01",_to:"2005-12-31"}},{TRY:{_from:"2005-01-01"}}],TT:[{TTD:{_from:"1964-01-01"}}],TV:[{AUD:{_from:"1966-02-14"}}],TW:[{TWD:{_from:"1949-06-15"}}],TZ:[{TZS:{_from:"1966-06-14"}}],UA:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1992-11-13"}},{UAK:{_from:"1992-11-13",_to:"1993-10-17"}},{UAH:{_from:"1996-09-02"}}],UG:[{UGS:{_from:"1966-08-15",_to:"1987-05-15"}},{UGX:{_from:"1987-05-15"}}],UM:[{USD:{_from:"1944-01-01"}}],US:[{USN:{_tender:"false"}},{USS:{_tender:"false",_to:"2014-03-01"}},{USD:{_from:"1792-01-01"}}],UY:[{UYI:{_tender:"false"}},{UYW:{_tender:"false"}},{UYP:{_from:"1975-07-01",_to:"1993-03-01"}},{UYU:{_from:"1993-03-01"}}],UZ:[{UZS:{_from:"1994-07-01"}}],VA:[{ITL:{_from:"1870-10-19",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],VC:[{XCD:{_from:"1965-10-06"}}],VE:[{VEB:{_from:"1871-05-11",_to:"2008-06-30"}},{VEF:{_from:"2008-01-01",_to:"2018-08-20"}},{VES:{_from:"2018-08-20"}}],VG:[{USD:{_from:"1833-01-01"}},{GBP:{_from:"1833-01-01",_to:"1959-01-01"}}],VI:[{USD:{_from:"1837-01-01"}}],VN:[{VNN:{_from:"1978-05-03",_to:"1985-09-14"}},{VND:{_from:"1985-09-14"}}],VU:[{VUV:{_from:"1981-01-01"}}],WF:[{XPF:{_from:"1961-07-30"}}],WS:[{WST:{_from:"1967-07-10"}}],XK:[{YUM:{_from:"1994-01-24",_to:"1999-09-30"}},{DEM:{_from:"1999-09-01",_to:"2002-03-09"}},{EUR:{_from:"2002-01-01"}}],YD:[{YDD:{_from:"1965-04-01",_to:"1996-01-01"}}],YE:[{YER:{_from:"1990-05-22"}}],YT:[{KMF:{_from:"1975-01-01",_to:"1976-02-23"}},{FRF:{_from:"1976-02-23",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],YU:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-24"}},{YUM:{_from:"1994-01-24",_to:"2002-05-15"}}],ZA:[{ZAR:{_from:"1961-02-14"}},{ZAL:{_tender:"false",_from:"1985-09-01",_to:"1995-03-13"}}],ZM:[{ZMK:{_from:"1968-01-16",_to:"2013-01-01"}},{ZMW:{_from:"2013-01-01"}}],ZR:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-31"}}],ZW:[{RHD:{_from:"1970-02-17",_to:"1980-04-18"}},{ZWD:{_from:"1980-04-18",_to:"2008-08-01"}},{ZWR:{_from:"2008-08-01",_to:"2009-02-02"}},{ZWL:{_from:"2009-02-02",_to:"2009-04-12"}},{USD:{_from:"2009-04-12"}}],ZZ:[{XAG:{_tender:"false"}},{XAU:{_tender:"false"}},{XBA:{_tender:"false"}},{XBB:{_tender:"false"}},{XBC:{_tender:"false"}},{XBD:{_tender:"false"}},{XDR:{_tender:"false"}},{XPD:{_tender:"false"}},{XPT:{_tender:"false"}},{XSU:{_tender:"false"}},{XTS:{_tender:"false"}},{XUA:{_tender:"false"}},{XXX:{_tender:"false"}},{XRE:{_tender:"false",_to:"1999-11-30"}},{XFU:{_tender:"false",_to:"2013-11-30"}},{XFO:{_tender:"false",_from:"1930-01-01",_to:"2003-04-01"}}]}}}},{main:{"en-AU":{identity:{version:{_cldrVersion:"36"},language:"en",territory:"AU"},dates:{calendars:{gregorian:{months:{format:{abbreviated:{1:"Jan",2:"Feb",3:"Mar",4:"Apr",5:"May",6:"Jun",7:"Jul",8:"Aug",9:"Sep",10:"Oct",11:"Nov",12:"Dec"},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"January",2:"February",3:"March",4:"April",5:"May",6:"June",7:"July",8:"August",9:"September",10:"October",11:"November",12:"December"}},"stand-alone":{abbreviated:{1:"Jan",2:"Feb",3:"Mar",4:"Apr",5:"May",6:"Jun",7:"Jul",8:"Aug",9:"Sep",10:"Oct",11:"Nov",12:"Dec"},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"January",2:"February",3:"March",4:"April",5:"May",6:"June",7:"July",8:"August",9:"September",10:"October",11:"November",12:"December"}}},days:{format:{abbreviated:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},narrow:{sun:"Su.",mon:"M.",tue:"Tu.",wed:"W.",thu:"Th.",fri:"F.",sat:"Sa."},short:{sun:"Su",mon:"Mon",tue:"Tu",wed:"Wed",thu:"Th",fri:"Fri",sat:"Sat"},wide:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"}},"stand-alone":{abbreviated:{sun:"Sun",mon:"Mon",tue:"Tue",wed:"Wed",thu:"Thu",fri:"Fri",sat:"Sat"},narrow:{sun:"Su.",mon:"M.",tue:"Tu.",wed:"W.",thu:"Th.",fri:"F.",sat:"Sa."},short:{sun:"Su",mon:"Mon",tue:"Tu",wed:"Wed",thu:"Th",fri:"Fri",sat:"Sat"},wide:{sun:"Sunday",mon:"Monday",tue:"Tuesday",wed:"Wednesday",thu:"Thursday",fri:"Friday",sat:"Saturday"}}},quarters:{format:{abbreviated:{1:"Q1",2:"Q2",3:"Q3",4:"Q4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1st quarter",2:"2nd quarter",3:"3rd quarter",4:"4th quarter"}},"stand-alone":{abbreviated:{1:"Q1",2:"Q2",3:"Q3",4:"Q4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1st quarter",2:"2nd quarter",3:"3rd quarter",4:"4th quarter"}}},dayPeriods:{format:{abbreviated:{midnight:"midnight",am:"am","am-alt-variant":"am",noon:"midday",pm:"pm","pm-alt-variant":"pm",morning1:"morning",afternoon1:"afternoon",evening1:"evening",night1:"night"},narrow:{midnight:"midnight",am:"am","am-alt-variant":"am",noon:"midday",pm:"pm","pm-alt-variant":"pm",morning1:"morning",afternoon1:"afternoon",evening1:"evening",night1:"night"},wide:{midnight:"midnight",am:"am","am-alt-variant":"am",noon:"midday",pm:"pm","pm-alt-variant":"pm",morning1:"in the morning",afternoon1:"in the afternoon",evening1:"in the evening",night1:"at night"}},"stand-alone":{abbreviated:{midnight:"midnight",am:"am","am-alt-variant":"am",noon:"midday",pm:"pm","pm-alt-variant":"pm",morning1:"morning",afternoon1:"afternoon",evening1:"evening",night1:"night"},narrow:{midnight:"midnight",am:"am","am-alt-variant":"am",noon:"midday",pm:"pm","pm-alt-variant":"pm",morning1:"morning",afternoon1:"afternoon",evening1:"evening",night1:"night"},wide:{midnight:"midnight",am:"am","am-alt-variant":"am",noon:"midday",pm:"pm","pm-alt-variant":"pm",morning1:"morning",afternoon1:"afternoon",evening1:"evening",night1:"night"}}},eras:{eraNames:{0:"Before Christ",1:"Anno Domini","0-alt-variant":"Before Common Era","1-alt-variant":"Common Era"},eraAbbr:{0:"BC",1:"AD","0-alt-variant":"BCE","1-alt-variant":"CE"},eraNarrow:{0:"B",1:"A","0-alt-variant":"BCE","1-alt-variant":"CE"}},dateFormats:{full:"EEEE, d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"d/M/yy"},timeFormats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},dateTimeFormats:{full:"{1} 'at' {0}",long:"{1} 'at' {0}",medium:"{1}, {0}",short:"{1}, {0}",availableFormats:{Bh:"h B",Bhm:"h:mm B",Bhms:"h:mm:ss B",d:"d",E:"ccc",EBhm:"E, h:mm B",EBhms:"E, h:mm:ss B",Ed:"E d",Ehm:"E h:mm a",EHm:"E HH:mm",Ehms:"E h:mm:ss a",EHms:"E HH:mm:ss",Gy:"y G",GyMMM:"MMM y G",GyMMMd:"d MMM y G",GyMMMEd:"E, d MMM y G",h:"h a",H:"HH",hm:"h:mm a",Hm:"HH:mm",hms:"h:mm:ss a",Hms:"HH:mm:ss",hmsv:"h:mm:ss a v",Hmsv:"HH:mm:ss v",hmv:"h:mm a v",Hmv:"HH:mm v",M:"L",Md:"d/M",MEd:"E, d/M",MMdd:"dd/MM",MMM:"LLL",MMMd:"d MMM",MMMEd:"E, d MMM",MMMMd:"d MMMM","MMMMW-count-one":"'week' W 'of' MMMM","MMMMW-count-other":"'week' W 'of' MMMM",ms:"mm:ss",y:"y",yM:"MM/y",yMd:"dd/MM/y",yMEd:"E, dd/MM/y",yMMM:"MMM y",yMMMd:"d MMM y",yMMMEd:"E, d MMM y",yMMMM:"MMMM y",yQQQ:"QQQ y",yQQQQ:"QQQQ y","yw-count-one":"'week' w 'of' Y","yw-count-other":"'week' w 'of' Y"},appendItems:{Day:"{0} ({2}: {1})","Day-Of-Week":"{0} {1}",Era:"{0} {1}",Hour:"{0} ({2}: {1})",Minute:"{0} ({2}: {1})",Month:"{0} ({2}: {1})",Quarter:"{0} ({2}: {1})",Second:"{0} ({2}: {1})",Timezone:"{0} {1}",Week:"{0} ({2}: {1})",Year:"{0} {1}"},intervalFormats:{intervalFormatFallback:"{0} – {1}",Bh:{B:"h B – h B",h:"h – h B"},Bhm:{B:"h:mm B – h:mm B",h:"h:mm – h:mm B",m:"h:mm – h:mm B"},d:{d:"d–d"},Gy:{G:"y G – y G",y:"y – y G"},GyM:{G:"M/y GGGGG – M/y GGGGG",M:"M/y – M/y GGGGG",y:"M/y – M/y GGGGG"},GyMd:{d:"d/M/y – d/M/y GGGGG",G:"d/M/y GGGGG – d/M/y GGGGG",M:"d/M/y – d/M/y GGGGG",y:"d/M/y – d/M/y GGGGG"},GyMEd:{d:"E, d/M/y – E, d/M/y GGGGG",G:"E, d/M/y GGGGG – E, d/M/y GGGGG",M:"E, d/M/y – E, d/M/y GGGGG",y:"E, d/M/y – E, d/M/y GGGGG"},GyMMM:{G:"MMM y G – MMM y G",M:"MMM – MMM y G",y:"MMM y – MMM y G"},GyMMMd:{d:"d – d MMM, y G",G:"d MMM, y G – d MMM, y G",M:"d MMM – d MMM, y G",y:"d MMM, y – d MMM, y G"},GyMMMEd:{d:"E, d MMM – E, d MMM, y G",G:"E, d MMM, y G – E, d MMM, y G",M:"E, d MMM – E, d MMM, y G",y:"E, d MMM, y – E, d MMM, y G"},h:{a:"h a – h a",h:"h–h a"},H:{H:"HH–HH"},hm:{a:"h:mm a – h:mm a",h:"h:mm – h:mm a",m:"h:mm – h:mm a"},Hm:{H:"HH:mm–HH:mm",m:"HH:mm–HH:mm"},hmv:{a:"h:mm a – h:mm a v",h:"h:mm – h:mm a v",m:"h:mm – h:mm a v"},Hmv:{H:"HH:mm–HH:mm v",m:"HH:mm–HH:mm v"},hv:{a:"h a – h a v",h:"h–h a v"},Hv:{H:"HH–HH v"},M:{M:"M–M"},Md:{d:"dd/MM – dd/MM",M:"dd/MM – dd/MM"},MEd:{d:"E dd/MM – E dd/MM",M:"E dd/MM – E dd/MM"},MMM:{M:"MMM – MMM"},MMMd:{d:"d – d MMM",M:"d MMM – d MMM"},MMMEd:{d:"E d – E d MMM",M:"E d MMM – E d MMM"},y:{y:"y–y"},yM:{M:"MM/y – MM/y",y:"MM/y – MM/y"},yMd:{d:"dd/MM/y – dd/MM/y",M:"dd/MM/y – dd/MM/y",y:"dd/MM/y – dd/MM/y"},yMEd:{d:"E, dd/MM/y – E, dd/MM/y",M:"E, dd/MM/y – E, dd/MM/y",y:"E, dd/MM/y – E, dd/MM/y"},yMMM:{M:"MMM – MMM y",y:"MMM y – MMM y"},yMMMd:{d:"d–d MMM y",M:"d MMM – d MMM y",y:"d MMM y – d MMM y"},yMMMEd:{d:"E, d – E, d MMM y",M:"E, d MMM – E, d MMM y",y:"E, d MMM y – E, d MMM y"},yMMMM:{M:"MMMM – MMMM y",y:"MMMM y – MMMM y"}}}}}}}}},{main:{"en-AU":{identity:{version:{_cldrVersion:"36"},language:"en",territory:"AU"},dates:{fields:{era:{displayName:"era"},"era-short":{displayName:"era"},"era-narrow":{displayName:"era"},year:{displayName:"year","relative-type--1":"last year","relative-type-0":"this year","relative-type-1":"next year","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} year","relativeTimePattern-count-other":"in {0} years"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} year ago","relativeTimePattern-count-other":"{0} years ago"}},"year-short":{displayName:"yr","relative-type--1":"last yr","relative-type-0":"this yr","relative-type-1":"next yr","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} yr","relativeTimePattern-count-other":"in {0} yrs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} yr ago","relativeTimePattern-count-other":"{0} yrs ago"}},"year-narrow":{displayName:"yr","relative-type--1":"last yr","relative-type-0":"this yr","relative-type-1":"next yr","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} yr","relativeTimePattern-count-other":"in {0} yrs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} yr ago","relativeTimePattern-count-other":"{0} yrs ago"}},quarter:{displayName:"quarter","relative-type--1":"last quarter","relative-type-0":"this quarter","relative-type-1":"next quarter","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} quarter","relativeTimePattern-count-other":"in {0} quarters"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} quarter ago","relativeTimePattern-count-other":"{0} quarters ago"}},"quarter-short":{displayName:"qtr","relative-type--1":"last qtr.","relative-type-0":"this qtr.","relative-type-1":"next qtr.","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} qtr","relativeTimePattern-count-other":"in {0} qtrs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} qtr ago","relativeTimePattern-count-other":"{0} qtrs ago"}},"quarter-narrow":{displayName:"qtr","relative-type--1":"last qtr.","relative-type-0":"this qtr.","relative-type-1":"next qtr.","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} qtr","relativeTimePattern-count-other":"in {0} qtrs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"in {0} qtr ago","relativeTimePattern-count-other":"{0} qtrs ago"}},month:{displayName:"month","relative-type--1":"last month","relative-type-0":"this month","relative-type-1":"next month","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} month","relativeTimePattern-count-other":"in {0} months"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} month ago","relativeTimePattern-count-other":"{0} months ago"}},"month-short":{displayName:"mo.","relative-type--1":"last mo","relative-type-0":"this mo","relative-type-1":"next mo","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} mo.","relativeTimePattern-count-other":"in {0} mo."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} mo. ago","relativeTimePattern-count-other":"{0} mo. ago"}},"month-narrow":{displayName:"mo.","relative-type--1":"last mo","relative-type-0":"this mo","relative-type-1":"next mo","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} mo.","relativeTimePattern-count-other":"in {0} mo."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} mo. ago","relativeTimePattern-count-other":"{0} mo. ago"}},week:{displayName:"week","relative-type--1":"last week","relative-type-0":"this week","relative-type-1":"next week","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} week","relativeTimePattern-count-other":"in {0} weeks"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} week ago","relativeTimePattern-count-other":"{0} weeks ago"},relativePeriod:"the week of {0}"},"week-short":{displayName:"wk","relative-type--1":"last wk","relative-type-0":"this wk","relative-type-1":"next wk","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} wk","relativeTimePattern-count-other":"in {0} wks"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} wk ago","relativeTimePattern-count-other":"{0} wks ago"},relativePeriod:"the week of {0}"},"week-narrow":{displayName:"wk","relative-type--1":"last wk","relative-type-0":"this wk","relative-type-1":"next wk","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} wk","relativeTimePattern-count-other":"in {0} wks"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} wk ago","relativeTimePattern-count-other":"{0} wks ago"},relativePeriod:"the week of {0}"},weekOfMonth:{displayName:"week of month"},"weekOfMonth-short":{displayName:"wk of mo."},"weekOfMonth-narrow":{displayName:"wk of mo"},day:{displayName:"day","relative-type--1":"yesterday","relative-type-0":"today","relative-type-1":"tomorrow","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} day","relativeTimePattern-count-other":"in {0} days"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} day ago","relativeTimePattern-count-other":"{0} days ago"}},"day-short":{displayName:"day","relative-type--1":"yesterday","relative-type-0":"today","relative-type-1":"tomorrow","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} day","relativeTimePattern-count-other":"in {0} days"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} day ago","relativeTimePattern-count-other":"{0} days ago"}},"day-narrow":{displayName:"day","relative-type--1":"yesterday","relative-type-0":"today","relative-type-1":"tomorrow","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} day","relativeTimePattern-count-other":"in {0} days"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} day ago","relativeTimePattern-count-other":"{0} days ago"}},dayOfYear:{displayName:"day of year"},"dayOfYear-short":{displayName:"day of yr"},"dayOfYear-narrow":{displayName:"day of yr"},weekday:{displayName:"day of the week"},"weekday-short":{displayName:"day of wk"},"weekday-narrow":{displayName:"day of wk"},weekdayOfMonth:{displayName:"weekday of the month"},"weekdayOfMonth-short":{displayName:"wkday of mo."},"weekdayOfMonth-narrow":{displayName:"wkday of mo"},sun:{"relative-type--1":"last Sunday","relative-type-0":"this Sunday","relative-type-1":"next Sunday","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Sunday","relativeTimePattern-count-other":"in {0} Sundays"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Sunday ago","relativeTimePattern-count-other":"{0} Sundays ago"}},"sun-short":{"relative-type--1":"last Sun","relative-type-0":"this Sun","relative-type-1":"next Sun","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Sun.","relativeTimePattern-count-other":"in {0} Sun."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Sun. ago","relativeTimePattern-count-other":"{0} Sun. ago"}},"sun-narrow":{"relative-type--1":"last Su","relative-type-0":"this Su","relative-type-1":"next Su","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Su","relativeTimePattern-count-other":"in {0} Su"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Su ago","relativeTimePattern-count-other":"{0} Su ago"}},mon:{"relative-type--1":"last Monday","relative-type-0":"this Monday","relative-type-1":"next Monday","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Monday","relativeTimePattern-count-other":"in {0} Mondays"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Monday ago","relativeTimePattern-count-other":"{0} Mondays ago"}},"mon-short":{"relative-type--1":"last Mon","relative-type-0":"this Mon","relative-type-1":"next Mon","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Mon.","relativeTimePattern-count-other":"in {0} Mon."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Mon. ago","relativeTimePattern-count-other":"{0} Mon. ago"}},"mon-narrow":{"relative-type--1":"last M","relative-type-0":"this M","relative-type-1":"next M","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} M","relativeTimePattern-count-other":"in {0} M"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} M ago","relativeTimePattern-count-other":"{0} M ago"}},tue:{"relative-type--1":"last Tuesday","relative-type-0":"this Tuesday","relative-type-1":"next Tuesday","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Tuesday","relativeTimePattern-count-other":"in {0} Tuesdays"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Tuesday ago","relativeTimePattern-count-other":"{0} Tuesdays ago"}},"tue-short":{"relative-type--1":"last Tue","relative-type-0":"this Tue","relative-type-1":"next Tue","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Tue.","relativeTimePattern-count-other":"in {0} Tue."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Tue. ago","relativeTimePattern-count-other":"{0} Tue. ago"}},"tue-narrow":{"relative-type--1":"last Tu","relative-type-0":"this Tu","relative-type-1":"next Tu","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Tu","relativeTimePattern-count-other":"in {0} Tu"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Tu ago","relativeTimePattern-count-other":"{0} Tu ago"}},wed:{"relative-type--1":"last Wednesday","relative-type-0":"this Wednesday","relative-type-1":"next Wednesday","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Wednesday","relativeTimePattern-count-other":"in {0} Wednesdays"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Wednesday ago","relativeTimePattern-count-other":"{0} Wednesdays ago"}},"wed-short":{"relative-type--1":"last Wed","relative-type-0":"this Wed","relative-type-1":"next Wed","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Wed.","relativeTimePattern-count-other":"in {0} Wed."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Wed. ago","relativeTimePattern-count-other":"{0} Wed. ago"}},"wed-narrow":{"relative-type--1":"last W","relative-type-0":"this W","relative-type-1":"next W","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} W","relativeTimePattern-count-other":"in {0} W"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} W ago","relativeTimePattern-count-other":"{0} W ago"}},thu:{"relative-type--1":"last Thursday","relative-type-0":"this Thursday","relative-type-1":"next Thursday","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Thursday","relativeTimePattern-count-other":"in {0} Thursdays"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Thursday ago","relativeTimePattern-count-other":"{0} Thursdays ago"}},"thu-short":{"relative-type--1":"last Thu","relative-type-0":"this Thu","relative-type-1":"next Thu","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Thu.","relativeTimePattern-count-other":"in {0} Thu."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Thu. ago","relativeTimePattern-count-other":"{0} Thu. ago"}},"thu-narrow":{"relative-type--1":"last Th","relative-type-0":"this Th","relative-type-1":"next Th","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Th","relativeTimePattern-count-other":"in {0} Th"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Th ago","relativeTimePattern-count-other":"{0} Th ago"}},fri:{"relative-type--1":"last Friday","relative-type-0":"this Friday","relative-type-1":"next Friday","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Friday","relativeTimePattern-count-other":"in {0} Fridays"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Friday ago","relativeTimePattern-count-other":"{0} Fridays ago"}},"fri-short":{"relative-type--1":"last Fri","relative-type-0":"this Fri","relative-type-1":"next Fri","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Fri.","relativeTimePattern-count-other":"in {0} Fri."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Fri. ago","relativeTimePattern-count-other":"{0} Fri. ago"}},"fri-narrow":{"relative-type--1":"last F","relative-type-0":"this F","relative-type-1":"next F","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} F","relativeTimePattern-count-other":"in {0} F"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} F ago","relativeTimePattern-count-other":"{0} F ago"}},sat:{"relative-type--1":"last Saturday","relative-type-0":"this Saturday","relative-type-1":"next Saturday","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Saturday","relativeTimePattern-count-other":"in {0} Saturdays"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Saturday ago","relativeTimePattern-count-other":"{0} Saturdays ago"}},"sat-short":{"relative-type--1":"last Sat","relative-type-0":"this Sat","relative-type-1":"next Sat","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Sat.","relativeTimePattern-count-other":"in {0} Sat."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Sat. ago","relativeTimePattern-count-other":"{0} Sat. ago"}},"sat-narrow":{"relative-type--1":"last Sa","relative-type-0":"this Sa","relative-type-1":"next Sa","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} Sa","relativeTimePattern-count-other":"in {0} Sa"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} Sa ago","relativeTimePattern-count-other":"{0} Sa ago"}},"dayperiod-short":{displayName:"am/pm","displayName-alt-variant":"am/pm"},dayperiod:{displayName:"am/pm","displayName-alt-variant":"am/pm"},"dayperiod-narrow":{displayName:"am/pm","displayName-alt-variant":"am/pm"},hour:{displayName:"hour","relative-type-0":"this hour","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} hour","relativeTimePattern-count-other":"in {0} hours"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} hour ago","relativeTimePattern-count-other":"{0} hours ago"}},"hour-short":{displayName:"h","relative-type-0":"this hour","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} hr","relativeTimePattern-count-other":"in {0} hrs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} hr ago","relativeTimePattern-count-other":"{0} hrs ago"}},"hour-narrow":{displayName:"h","relative-type-0":"this hour","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} hr","relativeTimePattern-count-other":"in {0} hrs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} hr ago","relativeTimePattern-count-other":"{0} hrs ago"}},minute:{displayName:"minute","relative-type-0":"this minute","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} minute","relativeTimePattern-count-other":"in {0} minutes"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} minute ago","relativeTimePattern-count-other":"{0} minutes ago"}},"minute-short":{displayName:"min.","relative-type-0":"this minute","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} min.","relativeTimePattern-count-other":"in {0} mins"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} min. ago","relativeTimePattern-count-other":"{0} mins ago"}},"minute-narrow":{displayName:"min.","relative-type-0":"this minute","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} min.","relativeTimePattern-count-other":"in {0} mins"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} min. ago","relativeTimePattern-count-other":"{0} mins ago"}},second:{displayName:"second","relative-type-0":"now","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} second","relativeTimePattern-count-other":"in {0} seconds"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} second ago","relativeTimePattern-count-other":"{0} seconds ago"}},"second-short":{displayName:"sec.","relative-type-0":"now","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} sec.","relativeTimePattern-count-other":"in {0} secs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sec. ago","relativeTimePattern-count-other":"{0} secs ago"}},"second-narrow":{displayName:"sec.","relative-type-0":"now","relativeTime-type-future":{"relativeTimePattern-count-one":"in {0} sec.","relativeTimePattern-count-other":"in {0} secs"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sec. ago","relativeTimePattern-count-other":"{0} secs ago"}},zone:{displayName:"time zone"},"zone-short":{displayName:"zone"},"zone-narrow":{displayName:"zone"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},"plurals-type-cardinal":{af:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ak:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},am:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},an:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ar:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ars:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},as:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},asa:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ast:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},az:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},be:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..4 and n % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 2.0, 3.0, 4.0, 22.0, 23.0, 24.0, 32.0, 33.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 10 = 0 or n % 10 = 5..9 or n % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":"   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …"},bem:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bez:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bho:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bm:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},br:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11,71,91 @integer 1, 21, 31, 41, 51, 61, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 81.0, 101.0, 1001.0, …","pluralRule-count-two":"n % 10 = 2 and n % 100 != 12,72,92 @integer 2, 22, 32, 42, 52, 62, 82, 102, 1002, … @decimal 2.0, 22.0, 32.0, 42.0, 52.0, 62.0, 82.0, 102.0, 1002.0, …","pluralRule-count-few":"n % 10 = 3..4,9 and n % 100 != 10..19,70..79,90..99 @integer 3, 4, 9, 23, 24, 29, 33, 34, 39, 43, 44, 49, 103, 1003, … @decimal 3.0, 4.0, 9.0, 23.0, 24.0, 29.0, 33.0, 34.0, 103.0, 1003.0, …","pluralRule-count-many":"n != 0 and n % 1000000 = 0 @integer 1000000, … @decimal 1000000.0, 1000000.00, 1000000.000, …","pluralRule-count-other":" @integer 0, 5~8, 10~20, 100, 1000, 10000, 100000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, …"},brx:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bs:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ca:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ce:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ceb:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},cgg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},chr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ckb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},cs:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},cy:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3 @integer 3 @decimal 3.0, 3.00, 3.000, 3.0000","pluralRule-count-many":"n = 6 @integer 6 @decimal 6.0, 6.00, 6.000, 6.0000","pluralRule-count-other":" @integer 4, 5, 7~20, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},da:{"pluralRule-count-one":"n = 1 or t != 0 and i = 0,1 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0~3.4, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},de:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dv:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dz:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ee:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},el:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},en:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},es:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},et:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fa:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ff:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fil:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},fo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fr:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fur:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fy:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ga:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3..6 @integer 3~6 @decimal 3.0, 4.0, 5.0, 6.0, 3.00, 4.00, 5.00, 6.00, 3.000, 4.000, 5.000, 6.000, 3.0000, 4.0000, 5.0000, 6.0000","pluralRule-count-many":"n = 7..10 @integer 7~10 @decimal 7.0, 8.0, 9.0, 10.0, 7.00, 8.00, 9.00, 10.00, 7.000, 8.000, 9.000, 10.000, 7.0000, 8.0000, 9.0000, 10.0000","pluralRule-count-other":" @integer 0, 11~25, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gd:{"pluralRule-count-one":"n = 1,11 @integer 1, 11 @decimal 1.0, 11.0, 1.00, 11.00, 1.000, 11.000, 1.0000","pluralRule-count-two":"n = 2,12 @integer 2, 12 @decimal 2.0, 12.0, 2.00, 12.00, 2.000, 12.000, 2.0000","pluralRule-count-few":"n = 3..10,13..19 @integer 3~10, 13~19 @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 3.00","pluralRule-count-other":" @integer 0, 20~34, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gsw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},guw:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gv:{"pluralRule-count-one":"v = 0 and i % 10 = 1 @integer 1, 11, 21, 31, 41, 51, 61, 71, 101, 1001, …","pluralRule-count-two":"v = 0 and i % 10 = 2 @integer 2, 12, 22, 32, 42, 52, 62, 72, 102, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 0,20,40,60,80 @integer 0, 20, 40, 60, 80, 100, 120, 140, 1000, 10000, 100000, 1000000, …","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 3~10, 13~19, 23, 103, 1003, …"},ha:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},haw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},he:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hy:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ia:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},id:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ig:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ii:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},in:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},io:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},is:{"pluralRule-count-one":"t = 0 and i % 10 = 1 and i % 100 != 11 or t != 0 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1~1.6, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},it:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ja:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jbo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ji:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jmc:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jv:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jw:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ka:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kab:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kaj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kcg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kde:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kea:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kkj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kl:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},km:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ko:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ks:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksh:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ku:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kw:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n % 100 = 2,22,42,62,82 or n % 1000 = 0 and n % 100000 = 1000..20000,40000,60000,80000 or n != 0 and n % 1000000 = 100000 @integer 2, 22, 42, 62, 82, 102, 122, 142, 1000, 10000, 100000, … @decimal 2.0, 22.0, 42.0, 62.0, 82.0, 102.0, 122.0, 142.0, 1000.0, 10000.0, 100000.0, …","pluralRule-count-few":"n % 100 = 3,23,43,63,83 @integer 3, 23, 43, 63, 83, 103, 123, 143, 1003, … @decimal 3.0, 23.0, 43.0, 63.0, 83.0, 103.0, 123.0, 143.0, 1003.0, …","pluralRule-count-many":"n != 1 and n % 100 = 1,21,41,61,81 @integer 21, 41, 61, 81, 101, 121, 141, 161, 1001, … @decimal 21.0, 41.0, 61.0, 81.0, 101.0, 121.0, 141.0, 161.0, 1001.0, …","pluralRule-count-other":" @integer 4~19, 100, 1004, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.1, 1000000.0, …"},ky:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lag:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"i = 0,1 and n != 0 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lkt:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ln:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lt:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11..19 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..9 and n % 100 != 11..19 @integer 2~9, 22~29, 102, 1002, … @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 22.0, 102.0, 1002.0, …","pluralRule-count-many":"f != 0   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lv:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},mas:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mg:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.2~1.0, 1.2~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ml:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mo:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},mr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ms:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mt:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-few":"n = 0 or n % 100 = 2..10 @integer 0, 2~10, 102~107, 1002, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 100 = 11..19 @integer 11~19, 111~117, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},my:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nah:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},naq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ne:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nnh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},no:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nqo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nso:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ny:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nyn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},om:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},or:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},os:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},osa:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pap:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i != 1 and i % 10 = 0..1 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 12..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},prg:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},ps:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pt:{"pluralRule-count-one":"i = 0..1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},"pt-PT":{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rm:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ro:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},rof:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},root:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ru:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rwk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sah:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},saq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sc:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},scn:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sdh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},se:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},seh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ses:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sg:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sh:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},shi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-few":"n = 2..10 @integer 2~10 @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 8.00","pluralRule-count-other":" @integer 11~26, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~1.9, 2.1~2.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},si:{"pluralRule-count-one":"n = 0,1 or i = 0 and f = 1 @integer 0, 1 @decimal 0.0, 0.1, 1.0, 0.00, 0.01, 1.00, 0.000, 0.001, 1.000, 0.0000, 0.0001, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.2~0.9, 1.1~1.8, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sk:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sl:{"pluralRule-count-one":"v = 0 and i % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, …","pluralRule-count-two":"v = 0 and i % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or v != 0 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sma:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smi:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sms:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},so:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ss:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ssy:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},st:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},su:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sv:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},syr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ta:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},te:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},teo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},th:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ti:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tig:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tl:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},tn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},to:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ts:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tzm:{"pluralRule-count-one":"n = 0..1 or n = 11..99 @integer 0, 1, 11~24 @decimal 0.0, 1.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0","pluralRule-count-other":" @integer 2~10, 100~106, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ug:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ur:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uz:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ve:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vi:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vun:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wae:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xog:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yue:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zh:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"}}}},{main:{"en-AU":{identity:{version:{_cldrVersion:"36"},language:"en",territory:"AU"},units:{long:{per:{compoundUnitPattern:"{0} per {1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"g-force","unitPattern-count-one":"{0} g-force","unitPattern-count-other":"{0} g-force"},"acceleration-meter-per-second-squared":{displayName:"metres per second squared","unitPattern-count-one":"{0} metre per second squared","unitPattern-count-other":"{0} metres per second squared"},"angle-revolution":{displayName:"revolution","unitPattern-count-one":"{0} revolution","unitPattern-count-other":"{0} revolutions"},"angle-radian":{displayName:"radians","unitPattern-count-one":"{0} radian","unitPattern-count-other":"{0} radians"},"angle-degree":{displayName:"degrees","unitPattern-count-one":"{0} degree","unitPattern-count-other":"{0} degrees"},"angle-arc-minute":{displayName:"arcminutes","unitPattern-count-one":"{0} arcminute","unitPattern-count-other":"{0} arcminutes"},"angle-arc-second":{displayName:"arcseconds","unitPattern-count-one":"{0} arcsecond","unitPattern-count-other":"{0} arcseconds"},"area-square-kilometer":{displayName:"square kilometres","unitPattern-count-one":"{0} square kilometre","unitPattern-count-other":"{0} square kilometres",perUnitPattern:"{0} per square kilometre"},"area-hectare":{displayName:"hectares","unitPattern-count-one":"{0} hectare","unitPattern-count-other":"{0} hectares"},"area-square-meter":{displayName:"square metres","unitPattern-count-one":"{0} square metre","unitPattern-count-other":"{0} square metres",perUnitPattern:"{0} per square metre"},"area-square-centimeter":{displayName:"square centimetres","unitPattern-count-one":"{0} square centimetre","unitPattern-count-other":"{0} square centimetres",perUnitPattern:"{0} per square centimetre"},"area-square-mile":{displayName:"square miles","unitPattern-count-one":"{0} square mile","unitPattern-count-other":"{0} square miles",perUnitPattern:"{0} per square mile"},"area-acre":{displayName:"acres","unitPattern-count-one":"{0} acre","unitPattern-count-other":"{0} acres"},"area-square-yard":{displayName:"square yards","unitPattern-count-one":"{0} square yard","unitPattern-count-other":"{0} square yards"},"area-square-foot":{displayName:"square feet","unitPattern-count-one":"{0} square foot","unitPattern-count-other":"{0} square feet"},"area-square-inch":{displayName:"square inches","unitPattern-count-one":"{0} square inch","unitPattern-count-other":"{0} square inches",perUnitPattern:"{0} per square inch"},"area-dunam":{displayName:"dunams","unitPattern-count-one":"{0} dunam","unitPattern-count-other":"{0} dunams"},"concentr-karat":{displayName:"carat","unitPattern-count-one":"{0} carat","unitPattern-count-other":"{0} carats"},"concentr-milligram-per-deciliter":{displayName:"milligrams per decilitre","unitPattern-count-one":"{0} milligram per decilitre","unitPattern-count-other":"{0} milligrams per decilitre"},"concentr-millimole-per-liter":{displayName:"millimoles per litre","unitPattern-count-one":"{0} millimole per litre","unitPattern-count-other":"{0} millimoles per litre"},"concentr-part-per-million":{displayName:"parts per million","unitPattern-count-one":"{0} part per million","unitPattern-count-other":"{0} parts per million"},"concentr-percent":{displayName:"per cent","unitPattern-count-one":"{0} per cent","unitPattern-count-other":"{0} per cent"},"concentr-permille":{displayName:"per mill","unitPattern-count-one":"{0} per mill","unitPattern-count-other":"{0} per mill"},"concentr-permyriad":{displayName:"permyriad","unitPattern-count-one":"{0} permyriad","unitPattern-count-other":"{0} permyriad"},"concentr-mole":{displayName:"moles","unitPattern-count-one":"{0} mole","unitPattern-count-other":"{0} moles"},"consumption-liter-per-kilometer":{displayName:"litres per kilometre","unitPattern-count-one":"{0} litre per kilometre","unitPattern-count-other":"{0} litres per kilometre"},"consumption-liter-per-100kilometers":{displayName:"litres per 100 kilometres","unitPattern-count-one":"{0} litre per 100 kilometres","unitPattern-count-other":"{0} litres per 100 kilometres"},"consumption-mile-per-gallon":{displayName:"miles per US gallon","unitPattern-count-one":"{0} mile per US gallon","unitPattern-count-other":"{0} miles per US gallon"},"consumption-mile-per-gallon-imperial":{displayName:"miles per gallon","unitPattern-count-one":"{0} mile per gallon","unitPattern-count-other":"{0} miles per gallon"},"digital-petabyte":{displayName:"petabytes","unitPattern-count-one":"{0} petabyte","unitPattern-count-other":"{0} petabytes"},"digital-terabyte":{displayName:"terabytes","unitPattern-count-one":"{0} terabyte","unitPattern-count-other":"{0} terabytes"},"digital-terabit":{displayName:"terabits","unitPattern-count-one":"{0} terabit","unitPattern-count-other":"{0} terabits"},"digital-gigabyte":{displayName:"gigabytes","unitPattern-count-one":"{0} gigabyte","unitPattern-count-other":"{0} gigabytes"},"digital-gigabit":{displayName:"gigabits","unitPattern-count-one":"{0} gigabit","unitPattern-count-other":"{0} gigabits"},"digital-megabyte":{displayName:"megabytes","unitPattern-count-one":"{0} megabyte","unitPattern-count-other":"{0} megabytes"},"digital-megabit":{displayName:"megabits","unitPattern-count-one":"{0} megabit","unitPattern-count-other":"{0} megabits"},"digital-kilobyte":{displayName:"kilobytes","unitPattern-count-one":"{0} kilobyte","unitPattern-count-other":"{0} kilobytes"},"digital-kilobit":{displayName:"kilobits","unitPattern-count-one":"{0} kilobit","unitPattern-count-other":"{0} kilobits"},"digital-byte":{displayName:"bytes","unitPattern-count-one":"{0} byte","unitPattern-count-other":"{0} bytes"},"digital-bit":{displayName:"bits","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bits"},"duration-century":{displayName:"centuries","unitPattern-count-one":"{0} century","unitPattern-count-other":"{0} centuries"},"duration-decade":{displayName:"decades","unitPattern-count-one":"{0} decade","unitPattern-count-other":"{0} decades"},"duration-year":{displayName:"years","unitPattern-count-one":"{0} year","unitPattern-count-other":"{0} years",perUnitPattern:"{0} per year"},"duration-month":{displayName:"months","unitPattern-count-one":"{0} month","unitPattern-count-other":"{0} months",perUnitPattern:"{0} per month"},"duration-week":{displayName:"weeks","unitPattern-count-one":"{0} week","unitPattern-count-other":"{0} weeks",perUnitPattern:"{0} per week"},"duration-day":{displayName:"days","unitPattern-count-one":"{0} day","unitPattern-count-other":"{0} days",perUnitPattern:"{0} per day"},"duration-hour":{displayName:"hours","unitPattern-count-one":"{0} hour","unitPattern-count-other":"{0} hours",perUnitPattern:"{0} per hour"},"duration-minute":{displayName:"minutes","unitPattern-count-one":"{0} minute","unitPattern-count-other":"{0} minutes",perUnitPattern:"{0} per minute"},"duration-second":{displayName:"seconds","unitPattern-count-one":"{0} second","unitPattern-count-other":"{0} seconds",perUnitPattern:"{0} per second"},"duration-millisecond":{displayName:"milliseconds","unitPattern-count-one":"{0} millisecond","unitPattern-count-other":"{0} milliseconds"},"duration-microsecond":{displayName:"microseconds","unitPattern-count-one":"{0} microsecond","unitPattern-count-other":"{0} microseconds"},"duration-nanosecond":{displayName:"nanoseconds","unitPattern-count-one":"{0} nanosecond","unitPattern-count-other":"{0} nanoseconds"},"electric-ampere":{displayName:"amperes","unitPattern-count-one":"{0} ampere","unitPattern-count-other":"{0} amperes"},"electric-milliampere":{displayName:"milliamperes","unitPattern-count-one":"{0} milliampere","unitPattern-count-other":"{0} milliamperes"},"electric-ohm":{displayName:"ohms","unitPattern-count-one":"{0} ohm","unitPattern-count-other":"{0} ohms"},"electric-volt":{displayName:"volts","unitPattern-count-one":"{0} volt","unitPattern-count-other":"{0} volts"},"energy-kilocalorie":{displayName:"kilocalories","unitPattern-count-one":"{0} kilocalorie","unitPattern-count-other":"{0} kilocalories"},"energy-calorie":{displayName:"calories","unitPattern-count-one":"{0} calorie","unitPattern-count-other":"{0} calories"},"energy-foodcalorie":{displayName:"Calories","unitPattern-count-one":"{0} Calorie","unitPattern-count-other":"{0} Calories"},"energy-kilojoule":{displayName:"kilojoules","unitPattern-count-one":"{0} kilojoule","unitPattern-count-other":"{0} kilojoules"},"energy-joule":{displayName:"joules","unitPattern-count-one":"{0} joule","unitPattern-count-other":"{0} joules"},"energy-kilowatt-hour":{displayName:"kilowatt hours","unitPattern-count-one":"{0} kilowatt hour","unitPattern-count-other":"{0} kilowatt hours"},"energy-electronvolt":{displayName:"electronvolts","unitPattern-count-one":"{0} electronvolt","unitPattern-count-other":"{0} electronvolts"},"energy-british-thermal-unit":{displayName:"British thermal units","unitPattern-count-one":"{0} British thermal unit","unitPattern-count-other":"{0} British thermal units"},"energy-therm-us":{displayName:"US therms","unitPattern-count-one":"{0} US therm","unitPattern-count-other":"{0} US therms"},"force-pound-force":{displayName:"pounds of force","unitPattern-count-one":"{0} pound of force","unitPattern-count-other":"{0} pounds of force"},"force-newton":{displayName:"newtons","unitPattern-count-one":"{0} newton","unitPattern-count-other":"{0} newtons"},"frequency-gigahertz":{displayName:"gigahertz","unitPattern-count-one":"{0} gigahertz","unitPattern-count-other":"{0} gigahertz"},"frequency-megahertz":{displayName:"megahertz","unitPattern-count-one":"{0} megahertz","unitPattern-count-other":"{0} megahertz"},"frequency-kilohertz":{displayName:"kilohertz","unitPattern-count-one":"{0} kilohertz","unitPattern-count-other":"{0} kilohertz"},"frequency-hertz":{displayName:"hertz","unitPattern-count-one":"{0} hertz","unitPattern-count-other":"{0} hertz"},"graphics-em":{displayName:"typographic em","unitPattern-count-one":"{0} em","unitPattern-count-other":"{0} ems"},"graphics-pixel":{displayName:"pixels","unitPattern-count-one":"{0} pixel","unitPattern-count-other":"{0} pixels"},"graphics-megapixel":{displayName:"megapixels","unitPattern-count-one":"{0} megapixel","unitPattern-count-other":"{0} megapixels"},"graphics-pixel-per-centimeter":{displayName:"pixels per centimetre","unitPattern-count-one":"{0} pixel per centimetre","unitPattern-count-other":"{0} pixels per centimetre"},"graphics-pixel-per-inch":{displayName:"pixels per inch","unitPattern-count-one":"{0} pixel per inch","unitPattern-count-other":"{0} pixels per inch"},"graphics-dot-per-centimeter":{displayName:"dots per centimetre","unitPattern-count-one":"{0} dot per centimetre","unitPattern-count-other":"{0} dots per centimetre"},"graphics-dot-per-inch":{displayName:"dots per inch","unitPattern-count-one":"{0} dot per inch","unitPattern-count-other":"{0} dots per inch"},"length-kilometer":{displayName:"kilometre","unitPattern-count-one":"{0} kilometre","unitPattern-count-other":"{0} kilometres",perUnitPattern:"{0} per kilometre"},"length-meter":{displayName:"metres","unitPattern-count-one":"{0} metre","unitPattern-count-other":"{0} metres",perUnitPattern:"{0} per metre"},"length-decimeter":{displayName:"decimetre","unitPattern-count-one":"{0} decimetre","unitPattern-count-other":"{0} decimetres"},"length-centimeter":{displayName:"centimetres","unitPattern-count-one":"{0} centimetre","unitPattern-count-other":"{0} centimetres",perUnitPattern:"{0} per centimetre"},"length-millimeter":{displayName:"millimetres","unitPattern-count-one":"{0} millimetre","unitPattern-count-other":"{0} millimetres"},"length-micrometer":{displayName:"micrometres","unitPattern-count-one":"{0} micrometre","unitPattern-count-other":"{0} micrometres"},"length-nanometer":{displayName:"nanometres","unitPattern-count-one":"{0} nanometre","unitPattern-count-other":"{0} nanometres"},"length-picometer":{displayName:"picometres","unitPattern-count-one":"{0} picometre","unitPattern-count-other":"{0} picometres"},"length-mile":{displayName:"miles","unitPattern-count-one":"{0} mile","unitPattern-count-other":"{0} miles"},"length-yard":{displayName:"yards","unitPattern-count-one":"{0} yard","unitPattern-count-other":"{0} yards"},"length-foot":{displayName:"feet","unitPattern-count-one":"{0} foot","unitPattern-count-other":"{0} feet",perUnitPattern:"{0} per foot"},"length-inch":{displayName:"inches","unitPattern-count-one":"{0} inch","unitPattern-count-other":"{0} inches",perUnitPattern:"{0} per inch"},"length-parsec":{displayName:"parsecs","unitPattern-count-one":"{0} parsec","unitPattern-count-other":"{0} parsecs"},"length-light-year":{displayName:"light years","unitPattern-count-one":"{0} light year","unitPattern-count-other":"{0} light years"},"length-astronomical-unit":{displayName:"astronomical units","unitPattern-count-one":"{0} astronomical unit","unitPattern-count-other":"{0} astronomical units"},"length-furlong":{displayName:"furlongs","unitPattern-count-one":"{0} furlong","unitPattern-count-other":"{0} furlongs"},"length-fathom":{displayName:"fathoms","unitPattern-count-one":"{0} fathom","unitPattern-count-other":"{0} fathoms"},"length-nautical-mile":{displayName:"nautical miles","unitPattern-count-one":"{0} nautical mile","unitPattern-count-other":"{0} nautical miles"},"length-mile-scandinavian":{displayName:"mile-scandinavian","unitPattern-count-one":"{0} mile-scandinavian","unitPattern-count-other":"{0} miles-scandinavian"},"length-point":{displayName:"points","unitPattern-count-one":"{0} point","unitPattern-count-other":"{0} points"},"length-solar-radius":{displayName:"solar radii","unitPattern-count-one":"{0} solar radius","unitPattern-count-other":"{0} solar radii"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lux","unitPattern-count-other":"{0} lux"},"light-solar-luminosity":{displayName:"solar luminosities","unitPattern-count-one":"{0} solar luminosity","unitPattern-count-other":"{0} solar luminosities"},"mass-metric-ton":{displayName:"tonnes","unitPattern-count-one":"tonne","unitPattern-count-other":"{0} tonnes"},"mass-kilogram":{displayName:"kilograms","unitPattern-count-one":"{0} kilogram","unitPattern-count-other":"{0} kilograms",perUnitPattern:"{0} per kilogram"},"mass-gram":{displayName:"grams","unitPattern-count-one":"{0} gram","unitPattern-count-other":"{0} grams",perUnitPattern:"{0} per gram"},"mass-milligram":{displayName:"milligrams","unitPattern-count-one":"{0} milligram","unitPattern-count-other":"{0} milligrams"},"mass-microgram":{displayName:"micrograms","unitPattern-count-one":"{0} microgram","unitPattern-count-other":"{0} micrograms"},"mass-ton":{displayName:"tons","unitPattern-count-one":"{0} ton","unitPattern-count-other":"{0} tons"},"mass-stone":{displayName:"stone","unitPattern-count-one":"{0} stone","unitPattern-count-other":"{0} stone"},"mass-pound":{displayName:"pounds","unitPattern-count-one":"{0} pound","unitPattern-count-other":"{0} pounds",perUnitPattern:"{0} per pound"},"mass-ounce":{displayName:"ounces","unitPattern-count-one":"{0} ounce","unitPattern-count-other":"{0} ounces",perUnitPattern:"{0} per ounce"},"mass-ounce-troy":{displayName:"troy ounces","unitPattern-count-one":"{0} troy ounce","unitPattern-count-other":"{0} troy ounces"},"mass-carat":{displayName:"carats","unitPattern-count-one":"{0} carat","unitPattern-count-other":"{0} carats"},"mass-dalton":{displayName:"daltons","unitPattern-count-one":"{0} dalton","unitPattern-count-other":"{0} daltons"},"mass-earth-mass":{displayName:"Earth masses","unitPattern-count-one":"{0} Earth mass","unitPattern-count-other":"{0} Earth masses"},"mass-solar-mass":{displayName:"solar masses","unitPattern-count-one":"{0} solar mass","unitPattern-count-other":"{0} solar masses"},"power-gigawatt":{displayName:"gigawatts","unitPattern-count-one":"{0} gigawatt","unitPattern-count-other":"{0} gigawatts"},"power-megawatt":{displayName:"megawatts","unitPattern-count-one":"{0} megawatt","unitPattern-count-other":"{0} megawatts"},"power-kilowatt":{displayName:"kilowatts","unitPattern-count-one":"{0} kilowatt","unitPattern-count-other":"{0} kilowatts"},"power-watt":{displayName:"watts","unitPattern-count-one":"{0} watt","unitPattern-count-other":"{0} watts"},"power-milliwatt":{displayName:"milliwatts","unitPattern-count-one":"{0} milliwatt","unitPattern-count-other":"{0} milliwatts"},"power-horsepower":{displayName:"horsepower","unitPattern-count-one":"{0} horsepower","unitPattern-count-other":"{0} horsepower"},"pressure-millimeter-of-mercury":{displayName:"millimetres of mercury","unitPattern-count-one":"{0} millimetre of mercury","unitPattern-count-other":"{0} millimetres of mercury"},"pressure-pound-per-square-inch":{displayName:"pounds per square inch","unitPattern-count-one":"{0} pound per square inch","unitPattern-count-other":"{0} pounds per square inch"},"pressure-inch-hg":{displayName:"inches of mercury","unitPattern-count-one":"{0} inch of mercury","unitPattern-count-other":"{0} inches of mercury"},"pressure-bar":{displayName:"bars","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"millibars","unitPattern-count-one":"{0} millibar","unitPattern-count-other":"{0} millibars"},"pressure-atmosphere":{displayName:"atmospheres","unitPattern-count-one":"{0} atmosphere","unitPattern-count-other":"{0} atmospheres"},"pressure-pascal":{displayName:"pascals","unitPattern-count-one":"{0} pascal","unitPattern-count-other":"{0} pascals"},"pressure-hectopascal":{displayName:"hectopascals","unitPattern-count-one":"{0} hectopascal","unitPattern-count-other":"{0} hectopascals"},"pressure-kilopascal":{displayName:"kilopascals","unitPattern-count-one":"{0} kilopascal","unitPattern-count-other":"{0} kilopascals"},"pressure-megapascal":{displayName:"megapascals","unitPattern-count-one":"{0} megapascal","unitPattern-count-other":"{0} megapascals"},"speed-kilometer-per-hour":{displayName:"kilometres per hour","unitPattern-count-one":"{0} kilometre per hour","unitPattern-count-other":"{0} kilometres per hour"},"speed-meter-per-second":{displayName:"metres per second","unitPattern-count-one":"{0} metre per second","unitPattern-count-other":"{0} metres per second"},"speed-mile-per-hour":{displayName:"miles per hour","unitPattern-count-one":"{0} mile per hour","unitPattern-count-other":"{0} miles per hour"},"speed-knot":{displayName:"knots","unitPattern-count-one":"{0} knot","unitPattern-count-other":"{0} knots"},"temperature-generic":{displayName:"degrees","unitPattern-count-one":"{0} degree","unitPattern-count-other":"{0} degrees"},"temperature-celsius":{displayName:"degrees Celsius","unitPattern-count-one":"{0} degree Celsius","unitPattern-count-other":"{0} degrees Celsius"},"temperature-fahrenheit":{displayName:"degrees Fahrenheit","unitPattern-count-one":"{0} degree Fahrenheit","unitPattern-count-other":"{0} degrees Fahrenheit"},"temperature-kelvin":{displayName:"kelvin","unitPattern-count-one":"{0} kelvin","unitPattern-count-other":"{0} kelvin"},"torque-pound-foot":{displayName:"pound-feet","unitPattern-count-one":"{0} pound-foot","unitPattern-count-other":"{0} pound-feet"},"torque-newton-meter":{displayName:"newton metres","unitPattern-count-one":"{0} newton metre","unitPattern-count-other":"{0} newton metres"},"volume-cubic-kilometer":{displayName:"cubic kilometres","unitPattern-count-one":"{0} cubic kilometre","unitPattern-count-other":"{0} cubic kilometres"},"volume-cubic-meter":{displayName:"cubic metres","unitPattern-count-one":"{0} cubic metre","unitPattern-count-other":"{0} cubic metres",perUnitPattern:"{0} per cubic metre"},"volume-cubic-centimeter":{displayName:"cubic centimetres","unitPattern-count-one":"{0} cubic centimetre","unitPattern-count-other":"{0} cubic centimetres",perUnitPattern:"{0} per cubic centimetre"},"volume-cubic-mile":{displayName:"cubic miles","unitPattern-count-one":"{0} cubic mile","unitPattern-count-other":"{0} cubic miles"},"volume-cubic-yard":{displayName:"cubic yards","unitPattern-count-one":"{0} cubic yard","unitPattern-count-other":"{0} cubic yards"},"volume-cubic-foot":{displayName:"cubic feet","unitPattern-count-one":"{0} cubic foot","unitPattern-count-other":"{0} cubic feet"},"volume-cubic-inch":{displayName:"cubic inches","unitPattern-count-one":"{0} cubic inch","unitPattern-count-other":"{0} cubic inches"},"volume-megaliter":{displayName:"megalitres","unitPattern-count-one":"{0} megalitre","unitPattern-count-other":"{0} megalitres"},"volume-hectoliter":{displayName:"hectolitres","unitPattern-count-one":"{0} hectolitre","unitPattern-count-other":"{0} hectolitres"},"volume-liter":{displayName:"litres","unitPattern-count-one":"{0} litre","unitPattern-count-other":"{0} litres",perUnitPattern:"{0} per litre"},"volume-deciliter":{displayName:"decilitres","unitPattern-count-one":"{0} decilitre","unitPattern-count-other":"{0} decilitres"},"volume-centiliter":{displayName:"centilitres","unitPattern-count-one":"{0} centilitre","unitPattern-count-other":"{0} centilitres"},"volume-milliliter":{displayName:"millilitres","unitPattern-count-one":"{0} millilitre","unitPattern-count-other":"{0} millilitres"},"volume-pint-metric":{displayName:"metric pints","unitPattern-count-one":"{0} metric pint","unitPattern-count-other":"{0} metric pints"},"volume-cup-metric":{displayName:"metric cups","unitPattern-count-one":"{0} metric cup","unitPattern-count-other":"{0} metric cups"},"volume-acre-foot":{displayName:"acre-feet","unitPattern-count-one":"{0} acre-foot","unitPattern-count-other":"{0} acre-feet"},"volume-bushel":{displayName:"bushels","unitPattern-count-one":"{0} bushel","unitPattern-count-other":"{0} bushels"},"volume-gallon":{displayName:"US gallons","unitPattern-count-one":"{0} US gallon","unitPattern-count-other":"{0} US gallons",perUnitPattern:"{0} per US gallon"},"volume-gallon-imperial":{displayName:"gallons","unitPattern-count-one":"{0} gallon","unitPattern-count-other":"{0} gallons",perUnitPattern:"{0} per gallon"},"volume-quart":{displayName:"quarts","unitPattern-count-one":"{0} quart","unitPattern-count-other":"{0} quarts"},"volume-pint":{displayName:"pints","unitPattern-count-one":"{0} pint","unitPattern-count-other":"{0} pints"},"volume-cup":{displayName:"cups","unitPattern-count-one":"{0} cup","unitPattern-count-other":"{0} cups"},"volume-fluid-ounce":{displayName:"fluid ounces","unitPattern-count-one":"{0} fluid ounce","unitPattern-count-other":"{0} fluid ounces"},"volume-fluid-ounce-imperial":{displayName:"Imp. fluid ounces","unitPattern-count-one":"{0} Imp. fluid ounce","unitPattern-count-other":"{0} Imp. fluid ounces"},"volume-tablespoon":{displayName:"tablespoons","unitPattern-count-one":"{0} tablespoon","unitPattern-count-other":"{0} tablespoons"},"volume-teaspoon":{displayName:"teaspoons","unitPattern-count-one":"{0} teaspoon","unitPattern-count-other":"{0} teaspoons"},"volume-barrel":{displayName:"barrels","unitPattern-count-one":"{0} barrel","unitPattern-count-other":"{0} barrels"},coordinateUnit:{displayName:"cardinal direction",east:"{0} east",north:"{0} north",south:"{0} south",west:"{0} west"}},short:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"g-force","unitPattern-count-one":"{0} G","unitPattern-count-other":"{0} G"},"acceleration-meter-per-second-squared":{displayName:"metres/sec²","unitPattern-count-one":"{0} m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"rev","unitPattern-count-one":"{0} rev","unitPattern-count-other":"{0} rev"},"angle-radian":{displayName:"radians","unitPattern-count-one":"{0} rad","unitPattern-count-other":"{0} rad"},"angle-degree":{displayName:"degrees","unitPattern-count-one":"{0} deg.","unitPattern-count-other":"{0} deg."},"angle-arc-minute":{displayName:"arcmin.","unitPattern-count-one":"{0} arcmin.","unitPattern-count-other":"{0} arcmin."},"angle-arc-second":{displayName:"arcsec.","unitPattern-count-one":"{0} arcsec.","unitPattern-count-other":"{0} arcsec."},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0} km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"hectares","unitPattern-count-one":"{0} ha","unitPattern-count-other":"{0} ha"},"area-square-meter":{displayName:"metres²","unitPattern-count-one":"{0} m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0} cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"sq miles","unitPattern-count-one":"{0} sq mi","unitPattern-count-other":"{0} sq mi",perUnitPattern:"{0}/mi²"},"area-acre":{displayName:"acres","unitPattern-count-one":"{0} ac","unitPattern-count-other":"{0} ac"},"area-square-yard":{displayName:"yards²","unitPattern-count-one":"{0} yd²","unitPattern-count-other":"{0} yd²"},"area-square-foot":{displayName:"sq feet","unitPattern-count-one":"{0} sq ft","unitPattern-count-other":"{0} sq ft"},"area-square-inch":{displayName:"inches²","unitPattern-count-one":"{0} in²","unitPattern-count-other":"{0} in²",perUnitPattern:"{0}/in²"},"area-dunam":{displayName:"dunams","unitPattern-count-one":"{0} dunam","unitPattern-count-other":"{0} dunam"},"concentr-karat":{displayName:"carats","unitPattern-count-one":"{0} ct","unitPattern-count-other":"{0} ct"},"concentr-milligram-per-deciliter":{displayName:"mg/dL","unitPattern-count-one":"{0} mg/dL","unitPattern-count-other":"{0} mg/dL"},"concentr-millimole-per-liter":{displayName:"millimol/litre","unitPattern-count-one":"{0} mmol/L","unitPattern-count-other":"{0} mmol/L"},"concentr-part-per-million":{displayName:"parts/million","unitPattern-count-one":"{0} ppm","unitPattern-count-other":"{0} ppm"},"concentr-percent":{displayName:"per cent","unitPattern-count-one":"{0}%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"per mill","unitPattern-count-one":"{0}‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"permyriad","unitPattern-count-one":"{0}‱","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mole","unitPattern-count-one":"{0} mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"litres/km","unitPattern-count-one":"{0} L/km","unitPattern-count-other":"{0} L/km"},"consumption-liter-per-100kilometers":{displayName:"L/100 km","unitPattern-count-one":"{0} L/100 km","unitPattern-count-other":"{0} L/100 km"},"consumption-mile-per-gallon":{displayName:"miles/gal. US","unitPattern-count-one":"{0} m.p.g. US","unitPattern-count-other":"{0} m.p.g. US"},"consumption-mile-per-gallon-imperial":{displayName:"miles/gal.","unitPattern-count-one":"{0} m.p.g.","unitPattern-count-other":"{0} m.p.g."},"digital-petabyte":{displayName:"PByte","unitPattern-count-one":"{0} PB","unitPattern-count-other":"{0} PB"},"digital-terabyte":{displayName:"TByte","unitPattern-count-one":"{0} TB","unitPattern-count-other":"{0} TB"},"digital-terabit":{displayName:"Tbit","unitPattern-count-one":"{0} Tb","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"GByte","unitPattern-count-one":"{0} GB","unitPattern-count-other":"{0} GB"},"digital-gigabit":{displayName:"Gbit","unitPattern-count-one":"{0} Gb","unitPattern-count-other":"{0} Gb"},"digital-megabyte":{displayName:"MByte","unitPattern-count-one":"{0} MB","unitPattern-count-other":"{0} MB"},"digital-megabit":{displayName:"Mbit","unitPattern-count-one":"{0} Mb","unitPattern-count-other":"{0} Mb"},"digital-kilobyte":{displayName:"kByte","unitPattern-count-one":"{0} kB","unitPattern-count-other":"{0} kB"},"digital-kilobit":{displayName:"kbit","unitPattern-count-one":"{0} kb","unitPattern-count-other":"{0} kb"},"digital-byte":{displayName:"byte","unitPattern-count-one":"{0} byte","unitPattern-count-other":"{0} byte"},"digital-bit":{displayName:"bit","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bit"},"duration-century":{displayName:"C.","unitPattern-count-one":"{0} C.","unitPattern-count-other":"{0} C."},"duration-decade":{displayName:"dec","unitPattern-count-one":"{0} dec","unitPattern-count-other":"{0} dec"},"duration-year":{displayName:"years","unitPattern-count-one":"{0} yr","unitPattern-count-other":"{0} yrs",perUnitPattern:"{0}/y"},"duration-month":{displayName:"months","unitPattern-count-one":"{0} m.","unitPattern-count-other":"{0} m.",perUnitPattern:"{0}/m"},"duration-week":{displayName:"weeks","unitPattern-count-one":"{0} wk","unitPattern-count-other":"{0} wks",perUnitPattern:"{0}/w"},"duration-day":{displayName:"days","unitPattern-count-one":"{0} day","unitPattern-count-other":"{0} days",perUnitPattern:"{0}/d"},"duration-hour":{displayName:"hours","unitPattern-count-one":"{0} hr","unitPattern-count-other":"{0} hrs",perUnitPattern:"{0} phr"},"duration-minute":{displayName:"min.","unitPattern-count-one":"{0} min.","unitPattern-count-other":"{0} min.",perUnitPattern:"{0}/min."},"duration-second":{displayName:"sec.","unitPattern-count-one":"{0} sec.","unitPattern-count-other":"{0} sec.",perUnitPattern:"{0} ps."},"duration-millisecond":{displayName:"millisec.","unitPattern-count-one":"{0} ms","unitPattern-count-other":"{0} ms"},"duration-microsecond":{displayName:"μsec.","unitPattern-count-one":"{0} μs","unitPattern-count-other":"{0} μs"},"duration-nanosecond":{displayName:"nanosec.","unitPattern-count-one":"{0} ns","unitPattern-count-other":"{0} ns"},"electric-ampere":{displayName:"amps","unitPattern-count-one":"{0} A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"milliamps","unitPattern-count-one":"{0} mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"ohms","unitPattern-count-one":"{0} Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"volts","unitPattern-count-one":"{0} V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"Cal","unitPattern-count-one":"{0} Cal","unitPattern-count-other":"{0} Cal"},"energy-calorie":{displayName:"cal","unitPattern-count-one":"{0} cal","unitPattern-count-other":"{0} cal"},"energy-foodcalorie":{displayName:"Cal","unitPattern-count-one":"{0} Cal","unitPattern-count-other":"{0} Cal"},"energy-kilojoule":{displayName:"kilojoule","unitPattern-count-one":"{0} kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"joules","unitPattern-count-one":"{0} J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-one":"{0} kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"electronvolt","unitPattern-count-one":"{0} eV","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0} Btu","unitPattern-count-other":"{0} Btu"},"energy-therm-us":{displayName:"US therm","unitPattern-count-one":"{0} US therm","unitPattern-count-other":"{0} US therms"},"force-pound-force":{displayName:"pound-force","unitPattern-count-one":"{0} lbf","unitPattern-count-other":"{0} lbf"},"force-newton":{displayName:"newton","unitPattern-count-one":"{0} N","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0} GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0} MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0} kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0} Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"em","unitPattern-count-one":"{0} em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"pixels","unitPattern-count-one":"{0} px","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"megapixels","unitPattern-count-one":"{0} MP","unitPattern-count-other":"{0} MP"},"graphics-pixel-per-centimeter":{displayName:"ppcm","unitPattern-count-one":"{0} ppcm","unitPattern-count-other":"{0} ppcm"},"graphics-pixel-per-inch":{displayName:"ppi","unitPattern-count-one":"{0} ppi","unitPattern-count-other":"{0} ppi"},"graphics-dot-per-centimeter":{displayName:"dpcm","unitPattern-count-one":"{0} dpcm","unitPattern-count-other":"{0} dpcm"},"graphics-dot-per-inch":{displayName:"dpi","unitPattern-count-one":"{0} dpi","unitPattern-count-other":"{0} dpi"},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0} km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"metres","unitPattern-count-one":"{0} m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0} dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0} cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0} mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µmetres","unitPattern-count-one":"{0} µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0} nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0} pm","unitPattern-count-other":"{0} pm"},"length-mile":{displayName:"miles","unitPattern-count-one":"{0} mi","unitPattern-count-other":"{0} mi"},"length-yard":{displayName:"yards","unitPattern-count-one":"{0} yd","unitPattern-count-other":"{0} yd"},"length-foot":{displayName:"feet","unitPattern-count-one":"{0} ft","unitPattern-count-other":"{0} ft",perUnitPattern:"{0}/ft"},"length-inch":{displayName:"inches","unitPattern-count-one":"{0} in","unitPattern-count-other":"{0} in",perUnitPattern:"{0}/in"},"length-parsec":{displayName:"parsecs","unitPattern-count-one":"{0} pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"light yrs","unitPattern-count-one":"{0} l.y.","unitPattern-count-other":"{0} l.y."},"length-astronomical-unit":{displayName:"AU","unitPattern-count-one":"{0} AU","unitPattern-count-other":"{0} AU"},"length-furlong":{displayName:"furlongs","unitPattern-count-one":"{0} fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fathoms","unitPattern-count-one":"{0} fth","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"nmi","unitPattern-count-one":"{0} nmi","unitPattern-count-other":"{0} nmi"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0} smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"points","unitPattern-count-one":"{0} pt","unitPattern-count-other":"{0} pt"},"length-solar-radius":{displayName:"solar radii","unitPattern-count-one":"{0} R☉","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"solar luminosities","unitPattern-count-one":"{0} L☉","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0} t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0} kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"grams","unitPattern-count-one":"{0} g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0} mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0} µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"tons","unitPattern-count-one":"{0} tn","unitPattern-count-other":"{0} tn"},"mass-stone":{displayName:"stone","unitPattern-count-one":"{0} st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"pounds","unitPattern-count-one":"{0} lb","unitPattern-count-other":"{0} lb",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz","unitPattern-count-one":"{0} oz","unitPattern-count-other":"{0} oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz troy","unitPattern-count-one":"{0} oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"carats","unitPattern-count-one":"{0} CM","unitPattern-count-other":"{0} CM"},"mass-dalton":{displayName:"daltons","unitPattern-count-one":"{0} Da","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"Earth masses","unitPattern-count-one":"{0} M⊕","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"solar masses","unitPattern-count-one":"{0} M☉","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0} GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0} MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0} kW","unitPattern-count-other":"{0} kW"},"power-watt":{displayName:"watts","unitPattern-count-one":"{0} W","unitPattern-count-other":"{0} W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0} mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"hp","unitPattern-count-one":"{0} hp","unitPattern-count-other":"{0} hp"},"pressure-millimeter-of-mercury":{displayName:"mm Hg","unitPattern-count-one":"{0} mm Hg","unitPattern-count-other":"{0} mm Hg"},"pressure-pound-per-square-inch":{displayName:"psi","unitPattern-count-one":"{0} psi","unitPattern-count-other":"{0} psi"},"pressure-inch-hg":{displayName:"in Hg","unitPattern-count-one":"{0} inHg","unitPattern-count-other":"{0} inHg"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"mb","unitPattern-count-one":"{0} mb","unitPattern-count-other":"{0} mb"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-one":"{0} atm","unitPattern-count-other":"{0} atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-one":"{0} Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0} hPa","unitPattern-count-other":"{0} hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0} kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0} MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/hour","unitPattern-count-one":"{0} km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"metres/sec.","unitPattern-count-one":"{0} m/s.","unitPattern-count-other":"{0} m/s."},"speed-mile-per-hour":{displayName:"miles/hour","unitPattern-count-one":"{0} m.p.h.","unitPattern-count-other":"{0} m.p.h."},"speed-knot":{displayName:"kn","unitPattern-count-one":"{0} kn","unitPattern-count-other":"{0} kn"},"temperature-generic":{displayName:"deg.","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"deg. C","unitPattern-count-one":"{0}°C","unitPattern-count-other":"{0}°C"},"temperature-fahrenheit":{displayName:"deg. F","unitPattern-count-one":"{0}°F","unitPattern-count-other":"{0}°F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0} K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-one":"{0} lbf⋅ft","unitPattern-count-other":"{0} lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-one":"{0} N⋅m","unitPattern-count-other":"{0} N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0} km³","unitPattern-count-other":"{0} km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0} m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0} cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mi³","unitPattern-count-one":"{0} mi³","unitPattern-count-other":"{0} mi³"},"volume-cubic-yard":{displayName:"yards³","unitPattern-count-one":"{0} yd³","unitPattern-count-other":"{0} yd³"},"volume-cubic-foot":{displayName:"feet³","unitPattern-count-one":"{0} ft³","unitPattern-count-other":"{0} ft³"},"volume-cubic-inch":{displayName:"inches³","unitPattern-count-one":"{0} in³","unitPattern-count-other":"{0} in³"},"volume-megaliter":{displayName:"ML","unitPattern-count-one":"{0} ML","unitPattern-count-other":"{0} ML"},"volume-hectoliter":{displayName:"hL","unitPattern-count-one":"{0} hL","unitPattern-count-other":"{0} hL"},"volume-liter":{displayName:"litres","unitPattern-count-one":"{0} L","unitPattern-count-other":"{0} L",perUnitPattern:"{0}/L"},"volume-deciliter":{displayName:"dL","unitPattern-count-one":"{0} dL","unitPattern-count-other":"{0} dL"},"volume-centiliter":{displayName:"cL","unitPattern-count-one":"{0} cL","unitPattern-count-other":"{0} cL"},"volume-milliliter":{displayName:"mL","unitPattern-count-one":"{0} mL","unitPattern-count-other":"{0} mL"},"volume-pint-metric":{displayName:"mpt","unitPattern-count-one":"{0} mpt","unitPattern-count-other":"{0} mpt"},"volume-cup-metric":{displayName:"mcup","unitPattern-count-one":"{0} mc","unitPattern-count-other":"{0} mc"},"volume-acre-foot":{displayName:"acre ft","unitPattern-count-one":"{0} ac ft","unitPattern-count-other":"{0} ac ft"},"volume-bushel":{displayName:"bushels","unitPattern-count-one":"{0} bus.","unitPattern-count-other":"{0} bus."},"volume-gallon":{displayName:"US gal.","unitPattern-count-one":"{0} gal. US","unitPattern-count-other":"{0} gal. US",perUnitPattern:"{0}/gal. US"},"volume-gallon-imperial":{displayName:"gal.","unitPattern-count-one":"{0} gal.","unitPattern-count-other":"{0} gal.",perUnitPattern:"{0}/gal."},"volume-quart":{displayName:"qts","unitPattern-count-one":"{0} qt","unitPattern-count-other":"{0} qt"},"volume-pint":{displayName:"pints","unitPattern-count-one":"{0} pt","unitPattern-count-other":"{0} pt"},"volume-cup":{displayName:"cups","unitPattern-count-one":"{0} c","unitPattern-count-other":"{0} c"},"volume-fluid-ounce":{displayName:"fl oz","unitPattern-count-one":"{0} fl oz","unitPattern-count-other":"{0} fl oz"},"volume-fluid-ounce-imperial":{displayName:"Imp. fl oz","unitPattern-count-one":"{0} fl oz Imp.","unitPattern-count-other":"{0} fl oz Imp."},"volume-tablespoon":{displayName:"tbsp","unitPattern-count-one":"{0} tbsp","unitPattern-count-other":"{0} tbsp"},"volume-teaspoon":{displayName:"tsp","unitPattern-count-one":"{0} tsp","unitPattern-count-other":"{0} tsp"},"volume-barrel":{displayName:"barrel","unitPattern-count-one":"{0} bbl","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"direction",east:"{0} E",north:"{0} N",south:"{0} S",west:"{0} W"}},narrow:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"g-force","unitPattern-count-one":"{0}G","unitPattern-count-other":"{0}Gs"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-one":"{0}m/s²","unitPattern-count-other":"{0}m/s²"},"angle-revolution":{displayName:"rev","unitPattern-count-one":"{0}rev","unitPattern-count-other":"{0}rev"},"angle-radian":{displayName:"rad","unitPattern-count-one":"{0}rad","unitPattern-count-other":"{0}rad"},"angle-degree":{displayName:"deg","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"arcmin","unitPattern-count-one":"{0}′","unitPattern-count-other":"{0}′"},"angle-arc-second":{displayName:"arcsec","unitPattern-count-one":"{0}″","unitPattern-count-other":"{0}″"},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0} km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"hectare","unitPattern-count-one":"{0}ha","unitPattern-count-other":"{0}ha"},"area-square-meter":{displayName:"metres²","unitPattern-count-one":"{0} m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0}cm²","unitPattern-count-other":"{0}cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"mi²","unitPattern-count-one":"{0}mi²","unitPattern-count-other":"{0}mi²",perUnitPattern:"{0}/mi²"},"area-acre":{displayName:"acre","unitPattern-count-one":"{0}ac","unitPattern-count-other":"{0}ac"},"area-square-yard":{displayName:"yd²","unitPattern-count-one":"{0}yd²","unitPattern-count-other":"{0}yd²"},"area-square-foot":{displayName:"ft²","unitPattern-count-one":"{0}ft²","unitPattern-count-other":"{0}ft²"},"area-square-inch":{displayName:"in²","unitPattern-count-one":"{0}in²","unitPattern-count-other":"{0}in²",perUnitPattern:"{0}/in²"},"area-dunam":{displayName:"dunam","unitPattern-count-one":"{0}dunam","unitPattern-count-other":"{0}dunam"},"concentr-karat":{displayName:"karat","unitPattern-count-one":"{0}kt","unitPattern-count-other":"{0}kt"},"concentr-milligram-per-deciliter":{displayName:"mg/dL","unitPattern-count-one":"{0}mg/dL","unitPattern-count-other":"{0}mg/dL"},"concentr-millimole-per-liter":{displayName:"mmol/L","unitPattern-count-one":"{0}mmol/L","unitPattern-count-other":"{0}mmol/L"},"concentr-part-per-million":{displayName:"ppm","unitPattern-count-one":"{0}ppm","unitPattern-count-other":"{0}ppm"},"concentr-percent":{displayName:"%","unitPattern-count-one":"{0}%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"‰","unitPattern-count-one":"{0}‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"‱","unitPattern-count-one":"{0}‱","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0}mol","unitPattern-count-other":"{0}mol"},"consumption-liter-per-kilometer":{displayName:"L/km","unitPattern-count-one":"{0}L/km","unitPattern-count-other":"{0}L/km"},"consumption-liter-per-100kilometers":{displayName:"L/100km","unitPattern-count-one":"{0}L/100km","unitPattern-count-other":"{0}L/100km"},"consumption-mile-per-gallon":{displayName:"mpg US","unitPattern-count-one":"{0}mpgUS","unitPattern-count-other":"{0}mpgUS"},"consumption-mile-per-gallon-imperial":{displayName:"mpg","unitPattern-count-one":"{0}mpg","unitPattern-count-other":"{0}mpg"},"digital-petabyte":{displayName:"PByte","unitPattern-count-one":"{0}PB","unitPattern-count-other":"{0}PB"},"digital-terabyte":{displayName:"TByte","unitPattern-count-one":"{0}TB","unitPattern-count-other":"{0}TB"},"digital-terabit":{displayName:"Tbit","unitPattern-count-one":"{0}Tb","unitPattern-count-other":"{0}Tb"},"digital-gigabyte":{displayName:"GByte","unitPattern-count-one":"{0}GB","unitPattern-count-other":"{0}GB"},"digital-gigabit":{displayName:"Gbit","unitPattern-count-one":"{0}Gb","unitPattern-count-other":"{0}Gb"},"digital-megabyte":{displayName:"MByte","unitPattern-count-one":"{0}MB","unitPattern-count-other":"{0}MB"},"digital-megabit":{displayName:"Mbit","unitPattern-count-one":"{0}Mb","unitPattern-count-other":"{0}Mb"},"digital-kilobyte":{displayName:"kByte","unitPattern-count-one":"{0}kB","unitPattern-count-other":"{0}kB"},"digital-kilobit":{displayName:"kbit","unitPattern-count-one":"{0}kb","unitPattern-count-other":"{0}kb"},"digital-byte":{displayName:"byte","unitPattern-count-one":"{0}B","unitPattern-count-other":"{0}B"},"digital-bit":{displayName:"bit","unitPattern-count-one":"{0}bit","unitPattern-count-other":"{0}bit"},"duration-century":{displayName:"C.","unitPattern-count-one":"{0} C.","unitPattern-count-other":"{0} C."},"duration-decade":{displayName:"dec","unitPattern-count-one":"{0}dec","unitPattern-count-other":"{0}dec"},"duration-year":{displayName:"yr","unitPattern-count-one":"{0}y","unitPattern-count-other":"{0}y",perUnitPattern:"{0}/y"},"duration-month":{displayName:"month","unitPattern-count-one":"{0}m","unitPattern-count-other":"{0}m",perUnitPattern:"{0}/m"},"duration-week":{displayName:"wk","unitPattern-count-one":"{0}w","unitPattern-count-other":"{0}w",perUnitPattern:"{0}/w"},"duration-day":{displayName:"day","unitPattern-count-one":"{0}d","unitPattern-count-other":"{0}d",perUnitPattern:"{0}/d"},"duration-hour":{displayName:"hour","unitPattern-count-one":"{0}h","unitPattern-count-other":"{0}h",perUnitPattern:"{0} phr"},"duration-minute":{displayName:"min.","unitPattern-count-one":"{0}min.","unitPattern-count-other":"{0}min.",perUnitPattern:"{0}/min."},"duration-second":{displayName:"sec.","unitPattern-count-one":"{0}s.","unitPattern-count-other":"{0}s.",perUnitPattern:"{0} ps."},"duration-millisecond":{displayName:"msec.","unitPattern-count-one":"{0}ms","unitPattern-count-other":"{0}ms"},"duration-microsecond":{displayName:"μsec","unitPattern-count-one":"{0}μs","unitPattern-count-other":"{0}μs"},"duration-nanosecond":{displayName:"ns","unitPattern-count-one":"{0}ns","unitPattern-count-other":"{0}ns"},"electric-ampere":{displayName:"amp","unitPattern-count-one":"{0}A","unitPattern-count-other":"{0}A"},"electric-milliampere":{displayName:"mA","unitPattern-count-one":"{0}mA","unitPattern-count-other":"{0}mA"},"electric-ohm":{displayName:"ohm","unitPattern-count-one":"{0}Ω","unitPattern-count-other":"{0}Ω"},"electric-volt":{displayName:"volt","unitPattern-count-one":"{0}V","unitPattern-count-other":"{0}V"},"energy-kilocalorie":{displayName:"kcal","unitPattern-count-one":"{0}kcal","unitPattern-count-other":"{0}kcal"},"energy-calorie":{displayName:"cal","unitPattern-count-one":"{0}cal","unitPattern-count-other":"{0}cal"},"energy-foodcalorie":{displayName:"Cal","unitPattern-count-one":"{0}Cal","unitPattern-count-other":"{0}Cal"},"energy-kilojoule":{displayName:"kJ","unitPattern-count-one":"{0}kJ","unitPattern-count-other":"{0}kJ"},"energy-joule":{displayName:"joule","unitPattern-count-one":"{0}J","unitPattern-count-other":"{0}J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-one":"{0}kWh","unitPattern-count-other":"{0}kWh"},"energy-electronvolt":{displayName:"eV","unitPattern-count-one":"{0}eV","unitPattern-count-other":"{0}eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0}Btu","unitPattern-count-other":"{0}Btu"},"energy-therm-us":{displayName:"US therm","unitPattern-count-one":"{0}US therm","unitPattern-count-other":"{0}US therms"},"force-pound-force":{displayName:"lbf","unitPattern-count-one":"{0}lbf","unitPattern-count-other":"{0}lbf"},"force-newton":{displayName:"N","unitPattern-count-one":"{0}N","unitPattern-count-other":"{0}N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0}GHz","unitPattern-count-other":"{0}GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0}MHz","unitPattern-count-other":"{0}MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0}kHz","unitPattern-count-other":"{0}kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0}Hz","unitPattern-count-other":"{0}Hz"},"graphics-em":{displayName:"em","unitPattern-count-one":"{0}em","unitPattern-count-other":"{0}em"},"graphics-pixel":{displayName:"px","unitPattern-count-one":"{0}px","unitPattern-count-other":"{0}px"},"graphics-megapixel":{displayName:"MP","unitPattern-count-one":"{0}MP","unitPattern-count-other":"{0}MP"},"graphics-pixel-per-centimeter":{displayName:"ppcm","unitPattern-count-one":"{0}ppcm","unitPattern-count-other":"{0}ppcm"},"graphics-pixel-per-inch":{displayName:"ppi","unitPattern-count-one":"{0}ppi","unitPattern-count-other":"{0}ppi"},"graphics-dot-per-centimeter":{displayName:"dpcm","unitPattern-count-one":"{0}dpcm","unitPattern-count-other":"{0}dpcm"},"graphics-dot-per-inch":{displayName:"dpi","unitPattern-count-one":"{0}dpi","unitPattern-count-other":"{0}dpi"},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0}km","unitPattern-count-other":"{0}km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"metre","unitPattern-count-one":"{0}m","unitPattern-count-other":"{0}m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0}dm","unitPattern-count-other":"{0}dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0}cm","unitPattern-count-other":"{0}cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0}mm","unitPattern-count-other":"{0}mm"},"length-micrometer":{displayName:"µm","unitPattern-count-one":"{0}µm","unitPattern-count-other":"{0}µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0}nm","unitPattern-count-other":"{0}nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0}pm","unitPattern-count-other":"{0}pm"},"length-mile":{displayName:"mi","unitPattern-count-one":"{0}mi","unitPattern-count-other":"{0}mi"},"length-yard":{displayName:"yd","unitPattern-count-one":"{0}yd","unitPattern-count-other":"{0}yd"},"length-foot":{displayName:"ft","unitPattern-count-one":"{0}′","unitPattern-count-other":"{0}′",perUnitPattern:"{0}/ft"},"length-inch":{displayName:"in","unitPattern-count-one":"{0}″","unitPattern-count-other":"{0}″",perUnitPattern:"{0}/in"},"length-parsec":{displayName:"parsec","unitPattern-count-one":"{0}pc","unitPattern-count-other":"{0}pc"},"length-light-year":{displayName:"ly","unitPattern-count-one":"{0}ly","unitPattern-count-other":"{0}ly"},"length-astronomical-unit":{displayName:"au","unitPattern-count-one":"{0}au","unitPattern-count-other":"{0}au"},"length-furlong":{displayName:"furlong","unitPattern-count-one":"{0}fur","unitPattern-count-other":"{0}fur"},"length-fathom":{displayName:"fathom","unitPattern-count-one":"{0}fth","unitPattern-count-other":"{0}fth"},"length-nautical-mile":{displayName:"nmi","unitPattern-count-one":"{0}nmi","unitPattern-count-other":"{0}nmi"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0}smi","unitPattern-count-other":"{0}smi"},"length-point":{displayName:"pts","unitPattern-count-one":"{0}pt","unitPattern-count-other":"{0}pt"},"length-solar-radius":{displayName:"R☉","unitPattern-count-one":"{0}R☉","unitPattern-count-other":"{0}R☉"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0}lx","unitPattern-count-other":"{0}lx"},"light-solar-luminosity":{displayName:"L☉","unitPattern-count-one":"{0}L☉","unitPattern-count-other":"{0}L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0}t","unitPattern-count-other":"{0}t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0}kg","unitPattern-count-other":"{0}kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"gram","unitPattern-count-one":"{0}g","unitPattern-count-other":"{0}g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0}mg","unitPattern-count-other":"{0}mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0}µg","unitPattern-count-other":"{0}µg"},"mass-ton":{displayName:"ton","unitPattern-count-one":"{0}tn","unitPattern-count-other":"{0}tn"},"mass-stone":{displayName:"stone","unitPattern-count-one":"{0}st","unitPattern-count-other":"{0}st"},"mass-pound":{displayName:"lb","unitPattern-count-one":"{0}lb","unitPattern-count-other":"{0}lb",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz","unitPattern-count-one":"{0}oz","unitPattern-count-other":"{0}oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz t","unitPattern-count-one":"{0}oz t","unitPattern-count-other":"{0}oz t"},"mass-carat":{displayName:"carat","unitPattern-count-one":"{0}CD","unitPattern-count-other":"{0}CD"},"mass-dalton":{displayName:"Da","unitPattern-count-one":"{0}Da","unitPattern-count-other":"{0}Da"},"mass-earth-mass":{displayName:"M⊕","unitPattern-count-one":"{0}M⊕","unitPattern-count-other":"{0}M⊕"},"mass-solar-mass":{displayName:"M☉","unitPattern-count-one":"{0}M☉","unitPattern-count-other":"{0}M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0}GW","unitPattern-count-other":"{0}GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0}MW","unitPattern-count-other":"{0}MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0}kW","unitPattern-count-other":"{0}kW"},"power-watt":{displayName:"watt","unitPattern-count-one":"{0}W","unitPattern-count-other":"{0}W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0}mW","unitPattern-count-other":"{0}mW"},"power-horsepower":{displayName:"hp","unitPattern-count-one":"{0}hp","unitPattern-count-other":"{0}hp"},"pressure-millimeter-of-mercury":{displayName:"mmHg","unitPattern-count-one":"{0}mmHg","unitPattern-count-other":"{0}mmHg"},"pressure-pound-per-square-inch":{displayName:"psi","unitPattern-count-one":"{0}psi","unitPattern-count-other":"{0}psi"},"pressure-inch-hg":{displayName:"″ Hg","unitPattern-count-one":"{0}″ Hg","unitPattern-count-other":"{0}″ Hg"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0}bar","unitPattern-count-other":"{0}bars"},"pressure-millibar":{displayName:"mbar","unitPattern-count-one":"{0}mb","unitPattern-count-other":"{0}mb"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-one":"{0}atm","unitPattern-count-other":"{0}atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-one":"{0}Pa","unitPattern-count-other":"{0}Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0}hPa","unitPattern-count-other":"{0}hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0}kPa","unitPattern-count-other":"{0}kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0}MPa","unitPattern-count-other":"{0}MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-one":"{0} km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"m/s","unitPattern-count-one":"{0}m/s","unitPattern-count-other":"{0}m/s"},"speed-mile-per-hour":{displayName:"m.p.h.","unitPattern-count-one":"{0} m.p.h.","unitPattern-count-other":"{0} m.p.h."},"speed-knot":{displayName:"kn","unitPattern-count-one":"{0}kn","unitPattern-count-other":"{0}kn"},"temperature-generic":{displayName:"deg.","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"°C","unitPattern-count-one":"{0}°C","unitPattern-count-other":"{0}°C"},"temperature-fahrenheit":{displayName:"°F","unitPattern-count-one":"{0}°F","unitPattern-count-other":"{0}°F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0}K","unitPattern-count-other":"{0}K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-one":"{0}lbf⋅ft","unitPattern-count-other":"{0}lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-one":"{0}N⋅m","unitPattern-count-other":"{0}N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0}km³","unitPattern-count-other":"{0}km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0}m³","unitPattern-count-other":"{0}m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0}cm³","unitPattern-count-other":"{0}cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mi³","unitPattern-count-one":"{0}mi³","unitPattern-count-other":"{0}mi³"},"volume-cubic-yard":{displayName:"yd³","unitPattern-count-one":"{0}yd³","unitPattern-count-other":"{0}yd³"},"volume-cubic-foot":{displayName:"ft³","unitPattern-count-one":"{0}ft³","unitPattern-count-other":"{0}ft³"},"volume-cubic-inch":{displayName:"in³","unitPattern-count-one":"{0}in³","unitPattern-count-other":"{0}in³"},"volume-megaliter":{displayName:"ML","unitPattern-count-one":"{0}ML","unitPattern-count-other":"{0}ML"},"volume-hectoliter":{displayName:"hL","unitPattern-count-one":"{0}hL","unitPattern-count-other":"{0}hL"},"volume-liter":{displayName:"litre","unitPattern-count-one":"{0}L","unitPattern-count-other":"{0}L",perUnitPattern:"{0}/L"},"volume-deciliter":{displayName:"dL","unitPattern-count-one":"{0}dL","unitPattern-count-other":"{0}dL"},"volume-centiliter":{displayName:"cL","unitPattern-count-one":"{0}cL","unitPattern-count-other":"{0}cL"},"volume-milliliter":{displayName:"mL","unitPattern-count-one":"{0}mL","unitPattern-count-other":"{0}mL"},"volume-pint-metric":{displayName:"pt","unitPattern-count-one":"{0}mpt","unitPattern-count-other":"{0}mpt"},"volume-cup-metric":{displayName:"mcup","unitPattern-count-one":"{0}mc","unitPattern-count-other":"{0}mc"},"volume-acre-foot":{displayName:"acre ft","unitPattern-count-one":"{0}ac ft","unitPattern-count-other":"{0}ac ft"},"volume-bushel":{displayName:"bushel","unitPattern-count-one":"{0} bus.","unitPattern-count-other":"{0} bus."},"volume-gallon":{displayName:"US gal","unitPattern-count-one":"{0}galUS","unitPattern-count-other":"{0}galUS",perUnitPattern:"{0}/galUS"},"volume-gallon-imperial":{displayName:"gal","unitPattern-count-one":"{0}gal","unitPattern-count-other":"{0}gal",perUnitPattern:"{0}/gal"},"volume-quart":{displayName:"qt","unitPattern-count-one":"{0}qt","unitPattern-count-other":"{0}qt"},"volume-pint":{displayName:"pt","unitPattern-count-one":"{0}pt","unitPattern-count-other":"{0}pt"},"volume-cup":{displayName:"cup","unitPattern-count-one":"{0}c","unitPattern-count-other":"{0}c"},"volume-fluid-ounce":{displayName:"fl oz","unitPattern-count-one":"{0}fl oz","unitPattern-count-other":"{0}fl oz"},"volume-fluid-ounce-imperial":{displayName:"Imp fl oz","unitPattern-count-one":"{0}fl oz Im","unitPattern-count-other":"{0}fl oz Im"},"volume-tablespoon":{displayName:"tbsp","unitPattern-count-one":"{0}tbsp","unitPattern-count-other":"{0}tbsp"},"volume-teaspoon":{displayName:"tsp","unitPattern-count-one":"{0}tsp","unitPattern-count-other":"{0}tsp"},"volume-barrel":{displayName:"bbl","unitPattern-count-one":"{0}bbl","unitPattern-count-other":"{0}bbl"},coordinateUnit:{displayName:"direction",east:"{0}E",north:"{0}N",south:"{0}S",west:"{0}W"}},"durationUnit-type-hm":{durationUnitPattern:"h:mm"},"durationUnit-type-hms":{durationUnitPattern:"h:mm:ss"},"durationUnit-type-ms":{durationUnitPattern:"m:ss"}}}}}];