var CldrData=[{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},likelySubtags:{aa:"aa-Latn-ET",aai:"aai-Latn-ZZ",aak:"aak-Latn-ZZ",aau:"aau-Latn-ZZ",ab:"ab-Cyrl-GE",abi:"abi-Latn-ZZ",abq:"abq-Cyrl-ZZ",abr:"abr-Latn-GH",abt:"abt-Latn-ZZ",aby:"aby-Latn-ZZ",acd:"acd-Latn-ZZ",ace:"ace-Latn-ID",ach:"ach-Latn-UG",ada:"ada-Latn-GH",ade:"ade-Latn-ZZ",adj:"adj-Latn-ZZ",adp:"adp-Tibt-BT",ady:"ady-Cyrl-RU",adz:"adz-Latn-ZZ",ae:"ae-Avst-IR",aeb:"aeb-Arab-TN",aey:"aey-Latn-ZZ",af:"af-Latn-ZA",agc:"agc-Latn-ZZ",agd:"agd-Latn-ZZ",agg:"agg-Latn-ZZ",agm:"agm-Latn-ZZ",ago:"ago-Latn-ZZ",agq:"agq-Latn-CM",aha:"aha-Latn-ZZ",ahl:"ahl-Latn-ZZ",aho:"aho-Ahom-IN",ajg:"ajg-Latn-ZZ",ak:"ak-Latn-GH",akk:"akk-Xsux-IQ",ala:"ala-Latn-ZZ",ali:"ali-Latn-ZZ",aln:"aln-Latn-XK",alt:"alt-Cyrl-RU",am:"am-Ethi-ET",amm:"amm-Latn-ZZ",amn:"amn-Latn-ZZ",amo:"amo-Latn-NG",amp:"amp-Latn-ZZ",an:"an-Latn-ES",anc:"anc-Latn-ZZ",ank:"ank-Latn-ZZ",ann:"ann-Latn-ZZ",any:"any-Latn-ZZ",aoj:"aoj-Latn-ZZ",aom:"aom-Latn-ZZ",aoz:"aoz-Latn-ID",apc:"apc-Arab-ZZ",apd:"apd-Arab-TG",ape:"ape-Latn-ZZ",apr:"apr-Latn-ZZ",aps:"aps-Latn-ZZ",apz:"apz-Latn-ZZ",ar:"ar-Arab-EG",arc:"arc-Armi-IR","arc-Nbat":"arc-Nbat-JO","arc-Palm":"arc-Palm-SY",arh:"arh-Latn-ZZ",arn:"arn-Latn-CL",aro:"aro-Latn-BO",arq:"arq-Arab-DZ",ars:"ars-Arab-SA",ary:"ary-Arab-MA",arz:"arz-Arab-EG",as:"as-Beng-IN",asa:"asa-Latn-TZ",ase:"ase-Sgnw-US",asg:"asg-Latn-ZZ",aso:"aso-Latn-ZZ",ast:"ast-Latn-ES",ata:"ata-Latn-ZZ",atg:"atg-Latn-ZZ",atj:"atj-Latn-CA",auy:"auy-Latn-ZZ",av:"av-Cyrl-RU",avl:"avl-Arab-ZZ",avn:"avn-Latn-ZZ",avt:"avt-Latn-ZZ",avu:"avu-Latn-ZZ",awa:"awa-Deva-IN",awb:"awb-Latn-ZZ",awo:"awo-Latn-ZZ",awx:"awx-Latn-ZZ",ay:"ay-Latn-BO",ayb:"ayb-Latn-ZZ",az:"az-Latn-AZ","az-Arab":"az-Arab-IR","az-IQ":"az-Arab-IQ","az-IR":"az-Arab-IR","az-RU":"az-Cyrl-RU",ba:"ba-Cyrl-RU",bal:"bal-Arab-PK",ban:"ban-Latn-ID",bap:"bap-Deva-NP",bar:"bar-Latn-AT",bas:"bas-Latn-CM",bav:"bav-Latn-ZZ",bax:"bax-Bamu-CM",bba:"bba-Latn-ZZ",bbb:"bbb-Latn-ZZ",bbc:"bbc-Latn-ID",bbd:"bbd-Latn-ZZ",bbj:"bbj-Latn-CM",bbp:"bbp-Latn-ZZ",bbr:"bbr-Latn-ZZ",bcf:"bcf-Latn-ZZ",bch:"bch-Latn-ZZ",bci:"bci-Latn-CI",bcm:"bcm-Latn-ZZ",bcn:"bcn-Latn-ZZ",bco:"bco-Latn-ZZ",bcq:"bcq-Ethi-ZZ",bcu:"bcu-Latn-ZZ",bdd:"bdd-Latn-ZZ",be:"be-Cyrl-BY",bef:"bef-Latn-ZZ",beh:"beh-Latn-ZZ",bej:"bej-Arab-SD",bem:"bem-Latn-ZM",bet:"bet-Latn-ZZ",bew:"bew-Latn-ID",bex:"bex-Latn-ZZ",bez:"bez-Latn-TZ",bfd:"bfd-Latn-CM",bfq:"bfq-Taml-IN",bft:"bft-Arab-PK",bfy:"bfy-Deva-IN",bg:"bg-Cyrl-BG",bgc:"bgc-Deva-IN",bgn:"bgn-Arab-PK",bgx:"bgx-Grek-TR",bhb:"bhb-Deva-IN",bhg:"bhg-Latn-ZZ",bhi:"bhi-Deva-IN",bhl:"bhl-Latn-ZZ",bho:"bho-Deva-IN",bhy:"bhy-Latn-ZZ",bi:"bi-Latn-VU",bib:"bib-Latn-ZZ",big:"big-Latn-ZZ",bik:"bik-Latn-PH",bim:"bim-Latn-ZZ",bin:"bin-Latn-NG",bio:"bio-Latn-ZZ",biq:"biq-Latn-ZZ",bjh:"bjh-Latn-ZZ",bji:"bji-Ethi-ZZ",bjj:"bjj-Deva-IN",bjn:"bjn-Latn-ID",bjo:"bjo-Latn-ZZ",bjr:"bjr-Latn-ZZ",bjt:"bjt-Latn-SN",bjz:"bjz-Latn-ZZ",bkc:"bkc-Latn-ZZ",bkm:"bkm-Latn-CM",bkq:"bkq-Latn-ZZ",bku:"bku-Latn-PH",bkv:"bkv-Latn-ZZ",blt:"blt-Tavt-VN",bm:"bm-Latn-ML",bmh:"bmh-Latn-ZZ",bmk:"bmk-Latn-ZZ",bmq:"bmq-Latn-ML",bmu:"bmu-Latn-ZZ",bn:"bn-Beng-BD",bng:"bng-Latn-ZZ",bnm:"bnm-Latn-ZZ",bnp:"bnp-Latn-ZZ",bo:"bo-Tibt-CN",boj:"boj-Latn-ZZ",bom:"bom-Latn-ZZ",bon:"bon-Latn-ZZ",bpy:"bpy-Beng-IN",bqc:"bqc-Latn-ZZ",bqi:"bqi-Arab-IR",bqp:"bqp-Latn-ZZ",bqv:"bqv-Latn-CI",br:"br-Latn-FR",bra:"bra-Deva-IN",brh:"brh-Arab-PK",brx:"brx-Deva-IN",brz:"brz-Latn-ZZ",bs:"bs-Latn-BA",bsj:"bsj-Latn-ZZ",bsq:"bsq-Bass-LR",bss:"bss-Latn-CM",bst:"bst-Ethi-ZZ",bto:"bto-Latn-PH",btt:"btt-Latn-ZZ",btv:"btv-Deva-PK",bua:"bua-Cyrl-RU",buc:"buc-Latn-YT",bud:"bud-Latn-ZZ",bug:"bug-Latn-ID",buk:"buk-Latn-ZZ",bum:"bum-Latn-CM",buo:"buo-Latn-ZZ",bus:"bus-Latn-ZZ",buu:"buu-Latn-ZZ",bvb:"bvb-Latn-GQ",bwd:"bwd-Latn-ZZ",bwr:"bwr-Latn-ZZ",bxh:"bxh-Latn-ZZ",bye:"bye-Latn-ZZ",byn:"byn-Ethi-ER",byr:"byr-Latn-ZZ",bys:"bys-Latn-ZZ",byv:"byv-Latn-CM",byx:"byx-Latn-ZZ",bza:"bza-Latn-ZZ",bze:"bze-Latn-ML",bzf:"bzf-Latn-ZZ",bzh:"bzh-Latn-ZZ",bzw:"bzw-Latn-ZZ",ca:"ca-Latn-ES",can:"can-Latn-ZZ",cbj:"cbj-Latn-ZZ",cch:"cch-Latn-NG",ccp:"ccp-Cakm-BD",ce:"ce-Cyrl-RU",ceb:"ceb-Latn-PH",cfa:"cfa-Latn-ZZ",cgg:"cgg-Latn-UG",ch:"ch-Latn-GU",chk:"chk-Latn-FM",chm:"chm-Cyrl-RU",cho:"cho-Latn-US",chp:"chp-Latn-CA",chr:"chr-Cher-US",cic:"cic-Latn-US",cja:"cja-Arab-KH",cjm:"cjm-Cham-VN",cjv:"cjv-Latn-ZZ",ckb:"ckb-Arab-IQ",ckl:"ckl-Latn-ZZ",cko:"cko-Latn-ZZ",cky:"cky-Latn-ZZ",cla:"cla-Latn-ZZ",cme:"cme-Latn-ZZ",cmg:"cmg-Soyo-MN",co:"co-Latn-FR",cop:"cop-Copt-EG",cps:"cps-Latn-PH",cr:"cr-Cans-CA",crh:"crh-Cyrl-UA",crj:"crj-Cans-CA",crk:"crk-Cans-CA",crl:"crl-Cans-CA",crm:"crm-Cans-CA",crs:"crs-Latn-SC",cs:"cs-Latn-CZ",csb:"csb-Latn-PL",csw:"csw-Cans-CA",ctd:"ctd-Pauc-MM",cu:"cu-Cyrl-RU","cu-Glag":"cu-Glag-BG",cv:"cv-Cyrl-RU",cy:"cy-Latn-GB",da:"da-Latn-DK",dad:"dad-Latn-ZZ",daf:"daf-Latn-ZZ",dag:"dag-Latn-ZZ",dah:"dah-Latn-ZZ",dak:"dak-Latn-US",dar:"dar-Cyrl-RU",dav:"dav-Latn-KE",dbd:"dbd-Latn-ZZ",dbq:"dbq-Latn-ZZ",dcc:"dcc-Arab-IN",ddn:"ddn-Latn-ZZ",de:"de-Latn-DE",ded:"ded-Latn-ZZ",den:"den-Latn-CA",dga:"dga-Latn-ZZ",dgh:"dgh-Latn-ZZ",dgi:"dgi-Latn-ZZ",dgl:"dgl-Arab-ZZ",dgr:"dgr-Latn-CA",dgz:"dgz-Latn-ZZ",dia:"dia-Latn-ZZ",dje:"dje-Latn-NE",dnj:"dnj-Latn-CI",dob:"dob-Latn-ZZ",doi:"doi-Arab-IN",dop:"dop-Latn-ZZ",dow:"dow-Latn-ZZ",drh:"drh-Mong-CN",dri:"dri-Latn-ZZ",drs:"drs-Ethi-ZZ",dsb:"dsb-Latn-DE",dtm:"dtm-Latn-ML",dtp:"dtp-Latn-MY",dts:"dts-Latn-ZZ",dty:"dty-Deva-NP",dua:"dua-Latn-CM",duc:"duc-Latn-ZZ",dud:"dud-Latn-ZZ",dug:"dug-Latn-ZZ",dv:"dv-Thaa-MV",dva:"dva-Latn-ZZ",dww:"dww-Latn-ZZ",dyo:"dyo-Latn-SN",dyu:"dyu-Latn-BF",dz:"dz-Tibt-BT",dzg:"dzg-Latn-ZZ",ebu:"ebu-Latn-KE",ee:"ee-Latn-GH",efi:"efi-Latn-NG",egl:"egl-Latn-IT",egy:"egy-Egyp-EG",eka:"eka-Latn-ZZ",eky:"eky-Kali-MM",el:"el-Grek-GR",ema:"ema-Latn-ZZ",emi:"emi-Latn-ZZ",en:"en-Latn-US","en-Shaw":"en-Shaw-GB",enn:"enn-Latn-ZZ",enq:"enq-Latn-ZZ",eo:"eo-Latn-001",eri:"eri-Latn-ZZ",es:"es-Latn-ES",esg:"esg-Gonm-IN",esu:"esu-Latn-US",et:"et-Latn-EE",etr:"etr-Latn-ZZ",ett:"ett-Ital-IT",etu:"etu-Latn-ZZ",etx:"etx-Latn-ZZ",eu:"eu-Latn-ES",ewo:"ewo-Latn-CM",ext:"ext-Latn-ES",fa:"fa-Arab-IR",faa:"faa-Latn-ZZ",fab:"fab-Latn-ZZ",fag:"fag-Latn-ZZ",fai:"fai-Latn-ZZ",fan:"fan-Latn-GQ",ff:"ff-Latn-SN","ff-Adlm":"ff-Adlm-GN",ffi:"ffi-Latn-ZZ",ffm:"ffm-Latn-ML",fi:"fi-Latn-FI",fia:"fia-Arab-SD",fil:"fil-Latn-PH",fit:"fit-Latn-SE",fj:"fj-Latn-FJ",flr:"flr-Latn-ZZ",fmp:"fmp-Latn-ZZ",fo:"fo-Latn-FO",fod:"fod-Latn-ZZ",fon:"fon-Latn-BJ",for:"for-Latn-ZZ",fpe:"fpe-Latn-ZZ",fqs:"fqs-Latn-ZZ",fr:"fr-Latn-FR",frc:"frc-Latn-US",frp:"frp-Latn-FR",frr:"frr-Latn-DE",frs:"frs-Latn-DE",fub:"fub-Arab-CM",fud:"fud-Latn-WF",fue:"fue-Latn-ZZ",fuf:"fuf-Latn-GN",fuh:"fuh-Latn-ZZ",fuq:"fuq-Latn-NE",fur:"fur-Latn-IT",fuv:"fuv-Latn-NG",fuy:"fuy-Latn-ZZ",fvr:"fvr-Latn-SD",fy:"fy-Latn-NL",ga:"ga-Latn-IE",gaa:"gaa-Latn-GH",gaf:"gaf-Latn-ZZ",gag:"gag-Latn-MD",gah:"gah-Latn-ZZ",gaj:"gaj-Latn-ZZ",gam:"gam-Latn-ZZ",gan:"gan-Hans-CN",gaw:"gaw-Latn-ZZ",gay:"gay-Latn-ID",gba:"gba-Latn-ZZ",gbf:"gbf-Latn-ZZ",gbm:"gbm-Deva-IN",gby:"gby-Latn-ZZ",gbz:"gbz-Arab-IR",gcr:"gcr-Latn-GF",gd:"gd-Latn-GB",gde:"gde-Latn-ZZ",gdn:"gdn-Latn-ZZ",gdr:"gdr-Latn-ZZ",geb:"geb-Latn-ZZ",gej:"gej-Latn-ZZ",gel:"gel-Latn-ZZ",gez:"gez-Ethi-ET",gfk:"gfk-Latn-ZZ",ggn:"ggn-Deva-NP",ghs:"ghs-Latn-ZZ",gil:"gil-Latn-KI",gim:"gim-Latn-ZZ",gjk:"gjk-Arab-PK",gjn:"gjn-Latn-ZZ",gju:"gju-Arab-PK",gkn:"gkn-Latn-ZZ",gkp:"gkp-Latn-ZZ",gl:"gl-Latn-ES",glk:"glk-Arab-IR",gmm:"gmm-Latn-ZZ",gmv:"gmv-Ethi-ZZ",gn:"gn-Latn-PY",gnd:"gnd-Latn-ZZ",gng:"gng-Latn-ZZ",god:"god-Latn-ZZ",gof:"gof-Ethi-ZZ",goi:"goi-Latn-ZZ",gom:"gom-Deva-IN",gon:"gon-Telu-IN",gor:"gor-Latn-ID",gos:"gos-Latn-NL",got:"got-Goth-UA",grb:"grb-Latn-ZZ",grc:"grc-Cprt-CY","grc-Linb":"grc-Linb-GR",grt:"grt-Beng-IN",grw:"grw-Latn-ZZ",gsw:"gsw-Latn-CH",gu:"gu-Gujr-IN",gub:"gub-Latn-BR",guc:"guc-Latn-CO",gud:"gud-Latn-ZZ",gur:"gur-Latn-GH",guw:"guw-Latn-ZZ",gux:"gux-Latn-ZZ",guz:"guz-Latn-KE",gv:"gv-Latn-IM",gvf:"gvf-Latn-ZZ",gvr:"gvr-Deva-NP",gvs:"gvs-Latn-ZZ",gwc:"gwc-Arab-ZZ",gwi:"gwi-Latn-CA",gwt:"gwt-Arab-ZZ",gyi:"gyi-Latn-ZZ",ha:"ha-Latn-NG","ha-CM":"ha-Arab-CM","ha-SD":"ha-Arab-SD",hag:"hag-Latn-ZZ",hak:"hak-Hans-CN",ham:"ham-Latn-ZZ",haw:"haw-Latn-US",haz:"haz-Arab-AF",hbb:"hbb-Latn-ZZ",hdy:"hdy-Ethi-ZZ",he:"he-Hebr-IL",hhy:"hhy-Latn-ZZ",hi:"hi-Deva-IN",hia:"hia-Latn-ZZ",hif:"hif-Latn-FJ",hig:"hig-Latn-ZZ",hih:"hih-Latn-ZZ",hil:"hil-Latn-PH",hla:"hla-Latn-ZZ",hlu:"hlu-Hluw-TR",hmd:"hmd-Plrd-CN",hmt:"hmt-Latn-ZZ",hnd:"hnd-Arab-PK",hne:"hne-Deva-IN",hnj:"hnj-Hmng-LA",hnn:"hnn-Latn-PH",hno:"hno-Arab-PK",ho:"ho-Latn-PG",hoc:"hoc-Deva-IN",hoj:"hoj-Deva-IN",hot:"hot-Latn-ZZ",hr:"hr-Latn-HR",hsb:"hsb-Latn-DE",hsn:"hsn-Hans-CN",ht:"ht-Latn-HT",hu:"hu-Latn-HU",hui:"hui-Latn-ZZ",hy:"hy-Armn-AM",hz:"hz-Latn-NA",ia:"ia-Latn-001",ian:"ian-Latn-ZZ",iar:"iar-Latn-ZZ",iba:"iba-Latn-MY",ibb:"ibb-Latn-NG",iby:"iby-Latn-ZZ",ica:"ica-Latn-ZZ",ich:"ich-Latn-ZZ",id:"id-Latn-ID",idd:"idd-Latn-ZZ",idi:"idi-Latn-ZZ",idu:"idu-Latn-ZZ",ife:"ife-Latn-TG",ig:"ig-Latn-NG",igb:"igb-Latn-ZZ",ige:"ige-Latn-ZZ",ii:"ii-Yiii-CN",ijj:"ijj-Latn-ZZ",ik:"ik-Latn-US",ikk:"ikk-Latn-ZZ",ikt:"ikt-Latn-CA",ikw:"ikw-Latn-ZZ",ikx:"ikx-Latn-ZZ",ilo:"ilo-Latn-PH",imo:"imo-Latn-ZZ",in:"in-Latn-ID",inh:"inh-Cyrl-RU",io:"io-Latn-001",iou:"iou-Latn-ZZ",iri:"iri-Latn-ZZ",is:"is-Latn-IS",it:"it-Latn-IT",iu:"iu-Cans-CA",iw:"iw-Hebr-IL",iwm:"iwm-Latn-ZZ",iws:"iws-Latn-ZZ",izh:"izh-Latn-RU",izi:"izi-Latn-ZZ",ja:"ja-Jpan-JP",jab:"jab-Latn-ZZ",jam:"jam-Latn-JM",jbo:"jbo-Latn-001",jbu:"jbu-Latn-ZZ",jen:"jen-Latn-ZZ",jgk:"jgk-Latn-ZZ",jgo:"jgo-Latn-CM",ji:"ji-Hebr-UA",jib:"jib-Latn-ZZ",jmc:"jmc-Latn-TZ",jml:"jml-Deva-NP",jra:"jra-Latn-ZZ",jut:"jut-Latn-DK",jv:"jv-Latn-ID",jw:"jw-Latn-ID",ka:"ka-Geor-GE",kaa:"kaa-Cyrl-UZ",kab:"kab-Latn-DZ",kac:"kac-Latn-MM",kad:"kad-Latn-ZZ",kai:"kai-Latn-ZZ",kaj:"kaj-Latn-NG",kam:"kam-Latn-KE",kao:"kao-Latn-ML",kbd:"kbd-Cyrl-RU",kbm:"kbm-Latn-ZZ",kbp:"kbp-Latn-ZZ",kbq:"kbq-Latn-ZZ",kbx:"kbx-Latn-ZZ",kby:"kby-Arab-NE",kcg:"kcg-Latn-NG",kck:"kck-Latn-ZW",kcl:"kcl-Latn-ZZ",kct:"kct-Latn-ZZ",kde:"kde-Latn-TZ",kdh:"kdh-Arab-TG",kdl:"kdl-Latn-ZZ",kdt:"kdt-Thai-TH",kea:"kea-Latn-CV",ken:"ken-Latn-CM",kez:"kez-Latn-ZZ",kfo:"kfo-Latn-CI",kfr:"kfr-Deva-IN",kfy:"kfy-Deva-IN",kg:"kg-Latn-CD",kge:"kge-Latn-ID",kgf:"kgf-Latn-ZZ",kgp:"kgp-Latn-BR",kha:"kha-Latn-IN",khb:"khb-Talu-CN",khn:"khn-Deva-IN",khq:"khq-Latn-ML",khs:"khs-Latn-ZZ",kht:"kht-Mymr-IN",khw:"khw-Arab-PK",khz:"khz-Latn-ZZ",ki:"ki-Latn-KE",kij:"kij-Latn-ZZ",kiu:"kiu-Latn-TR",kiw:"kiw-Latn-ZZ",kj:"kj-Latn-NA",kjd:"kjd-Latn-ZZ",kjg:"kjg-Laoo-LA",kjs:"kjs-Latn-ZZ",kjy:"kjy-Latn-ZZ",kk:"kk-Cyrl-KZ","kk-AF":"kk-Arab-AF","kk-Arab":"kk-Arab-CN","kk-CN":"kk-Arab-CN","kk-IR":"kk-Arab-IR","kk-MN":"kk-Arab-MN",kkc:"kkc-Latn-ZZ",kkj:"kkj-Latn-CM",kl:"kl-Latn-GL",kln:"kln-Latn-KE",klq:"klq-Latn-ZZ",klt:"klt-Latn-ZZ",klx:"klx-Latn-ZZ",km:"km-Khmr-KH",kmb:"kmb-Latn-AO",kmh:"kmh-Latn-ZZ",kmo:"kmo-Latn-ZZ",kms:"kms-Latn-ZZ",kmu:"kmu-Latn-ZZ",kmw:"kmw-Latn-ZZ",kn:"kn-Knda-IN",knf:"knf-Latn-GW",knp:"knp-Latn-ZZ",ko:"ko-Kore-KR",koi:"koi-Cyrl-RU",kok:"kok-Deva-IN",kol:"kol-Latn-ZZ",kos:"kos-Latn-FM",koz:"koz-Latn-ZZ",kpe:"kpe-Latn-LR",kpf:"kpf-Latn-ZZ",kpo:"kpo-Latn-ZZ",kpr:"kpr-Latn-ZZ",kpx:"kpx-Latn-ZZ",kqb:"kqb-Latn-ZZ",kqf:"kqf-Latn-ZZ",kqs:"kqs-Latn-ZZ",kqy:"kqy-Ethi-ZZ",kr:"kr-Latn-ZZ",krc:"krc-Cyrl-RU",kri:"kri-Latn-SL",krj:"krj-Latn-PH",krl:"krl-Latn-RU",krs:"krs-Latn-ZZ",kru:"kru-Deva-IN",ks:"ks-Arab-IN",ksb:"ksb-Latn-TZ",ksd:"ksd-Latn-ZZ",ksf:"ksf-Latn-CM",ksh:"ksh-Latn-DE",ksj:"ksj-Latn-ZZ",ksr:"ksr-Latn-ZZ",ktb:"ktb-Ethi-ZZ",ktm:"ktm-Latn-ZZ",kto:"kto-Latn-ZZ",ktr:"ktr-Latn-MY",ku:"ku-Latn-TR","ku-Arab":"ku-Arab-IQ","ku-LB":"ku-Arab-LB",kub:"kub-Latn-ZZ",kud:"kud-Latn-ZZ",kue:"kue-Latn-ZZ",kuj:"kuj-Latn-ZZ",kum:"kum-Cyrl-RU",kun:"kun-Latn-ZZ",kup:"kup-Latn-ZZ",kus:"kus-Latn-ZZ",kv:"kv-Cyrl-RU",kvg:"kvg-Latn-ZZ",kvr:"kvr-Latn-ID",kvx:"kvx-Arab-PK",kw:"kw-Latn-GB",kwj:"kwj-Latn-ZZ",kwo:"kwo-Latn-ZZ",kwq:"kwq-Latn-ZZ",kxa:"kxa-Latn-ZZ",kxc:"kxc-Ethi-ZZ",kxe:"kxe-Latn-ZZ",kxm:"kxm-Thai-TH",kxp:"kxp-Arab-PK",kxw:"kxw-Latn-ZZ",kxz:"kxz-Latn-ZZ",ky:"ky-Cyrl-KG","ky-Arab":"ky-Arab-CN","ky-CN":"ky-Arab-CN","ky-Latn":"ky-Latn-TR","ky-TR":"ky-Latn-TR",kye:"kye-Latn-ZZ",kyx:"kyx-Latn-ZZ",kzj:"kzj-Latn-MY",kzr:"kzr-Latn-ZZ",kzt:"kzt-Latn-MY",la:"la-Latn-VA",lab:"lab-Lina-GR",lad:"lad-Hebr-IL",lag:"lag-Latn-TZ",lah:"lah-Arab-PK",laj:"laj-Latn-UG",las:"las-Latn-ZZ",lb:"lb-Latn-LU",lbe:"lbe-Cyrl-RU",lbu:"lbu-Latn-ZZ",lbw:"lbw-Latn-ID",lcm:"lcm-Latn-ZZ",lcp:"lcp-Thai-CN",ldb:"ldb-Latn-ZZ",led:"led-Latn-ZZ",lee:"lee-Latn-ZZ",lem:"lem-Latn-ZZ",lep:"lep-Lepc-IN",leq:"leq-Latn-ZZ",leu:"leu-Latn-ZZ",lez:"lez-Cyrl-RU",lg:"lg-Latn-UG",lgg:"lgg-Latn-ZZ",li:"li-Latn-NL",lia:"lia-Latn-ZZ",lid:"lid-Latn-ZZ",lif:"lif-Deva-NP","lif-Limb":"lif-Limb-IN",lig:"lig-Latn-ZZ",lih:"lih-Latn-ZZ",lij:"lij-Latn-IT",lis:"lis-Lisu-CN",ljp:"ljp-Latn-ID",lki:"lki-Arab-IR",lkt:"lkt-Latn-US",lle:"lle-Latn-ZZ",lln:"lln-Latn-ZZ",lmn:"lmn-Telu-IN",lmo:"lmo-Latn-IT",lmp:"lmp-Latn-ZZ",ln:"ln-Latn-CD",lns:"lns-Latn-ZZ",lnu:"lnu-Latn-ZZ",lo:"lo-Laoo-LA",loj:"loj-Latn-ZZ",lok:"lok-Latn-ZZ",lol:"lol-Latn-CD",lor:"lor-Latn-ZZ",los:"los-Latn-ZZ",loz:"loz-Latn-ZM",lrc:"lrc-Arab-IR",lt:"lt-Latn-LT",ltg:"ltg-Latn-LV",lu:"lu-Latn-CD",lua:"lua-Latn-CD",luo:"luo-Latn-KE",luy:"luy-Latn-KE",luz:"luz-Arab-IR",lv:"lv-Latn-LV",lwl:"lwl-Thai-TH",lzh:"lzh-Hans-CN",lzz:"lzz-Latn-TR",mad:"mad-Latn-ID",maf:"maf-Latn-CM",mag:"mag-Deva-IN",mai:"mai-Deva-IN",mak:"mak-Latn-ID",man:"man-Latn-GM","man-GN":"man-Nkoo-GN","man-Nkoo":"man-Nkoo-GN",mas:"mas-Latn-KE",maw:"maw-Latn-ZZ",maz:"maz-Latn-MX",mbh:"mbh-Latn-ZZ",mbo:"mbo-Latn-ZZ",mbq:"mbq-Latn-ZZ",mbu:"mbu-Latn-ZZ",mbw:"mbw-Latn-ZZ",mci:"mci-Latn-ZZ",mcp:"mcp-Latn-ZZ",mcq:"mcq-Latn-ZZ",mcr:"mcr-Latn-ZZ",mcu:"mcu-Latn-ZZ",mda:"mda-Latn-ZZ",mde:"mde-Arab-ZZ",mdf:"mdf-Cyrl-RU",mdh:"mdh-Latn-PH",mdj:"mdj-Latn-ZZ",mdr:"mdr-Latn-ID",mdx:"mdx-Ethi-ZZ",med:"med-Latn-ZZ",mee:"mee-Latn-ZZ",mek:"mek-Latn-ZZ",men:"men-Latn-SL",mer:"mer-Latn-KE",met:"met-Latn-ZZ",meu:"meu-Latn-ZZ",mfa:"mfa-Arab-TH",mfe:"mfe-Latn-MU",mfn:"mfn-Latn-ZZ",mfo:"mfo-Latn-ZZ",mfq:"mfq-Latn-ZZ",mg:"mg-Latn-MG",mgh:"mgh-Latn-MZ",mgl:"mgl-Latn-ZZ",mgo:"mgo-Latn-CM",mgp:"mgp-Deva-NP",mgy:"mgy-Latn-TZ",mh:"mh-Latn-MH",mhi:"mhi-Latn-ZZ",mhl:"mhl-Latn-ZZ",mi:"mi-Latn-NZ",mif:"mif-Latn-ZZ",min:"min-Latn-ID",mis:"mis-Hatr-IQ","mis-Medf":"mis-Medf-NG",miw:"miw-Latn-ZZ",mk:"mk-Cyrl-MK",mki:"mki-Arab-ZZ",mkl:"mkl-Latn-ZZ",mkp:"mkp-Latn-ZZ",mkw:"mkw-Latn-ZZ",ml:"ml-Mlym-IN",mle:"mle-Latn-ZZ",mlp:"mlp-Latn-ZZ",mls:"mls-Latn-SD",mmo:"mmo-Latn-ZZ",mmu:"mmu-Latn-ZZ",mmx:"mmx-Latn-ZZ",mn:"mn-Cyrl-MN","mn-CN":"mn-Mong-CN","mn-Mong":"mn-Mong-CN",mna:"mna-Latn-ZZ",mnf:"mnf-Latn-ZZ",mni:"mni-Beng-IN",mnw:"mnw-Mymr-MM",mo:"mo-Latn-RO",moa:"moa-Latn-ZZ",moe:"moe-Latn-CA",moh:"moh-Latn-CA",mos:"mos-Latn-BF",mox:"mox-Latn-ZZ",mpp:"mpp-Latn-ZZ",mps:"mps-Latn-ZZ",mpt:"mpt-Latn-ZZ",mpx:"mpx-Latn-ZZ",mql:"mql-Latn-ZZ",mr:"mr-Deva-IN",mrd:"mrd-Deva-NP",mrj:"mrj-Cyrl-RU",mro:"mro-Mroo-BD",ms:"ms-Latn-MY","ms-CC":"ms-Arab-CC","ms-ID":"ms-Arab-ID",mt:"mt-Latn-MT",mtc:"mtc-Latn-ZZ",mtf:"mtf-Latn-ZZ",mti:"mti-Latn-ZZ",mtr:"mtr-Deva-IN",mua:"mua-Latn-CM",mur:"mur-Latn-ZZ",mus:"mus-Latn-US",mva:"mva-Latn-ZZ",mvn:"mvn-Latn-ZZ",mvy:"mvy-Arab-PK",mwk:"mwk-Latn-ML",mwr:"mwr-Deva-IN",mwv:"mwv-Latn-ID",mww:"mww-Hmnp-US",mxc:"mxc-Latn-ZW",mxm:"mxm-Latn-ZZ",my:"my-Mymr-MM",myk:"myk-Latn-ZZ",mym:"mym-Ethi-ZZ",myv:"myv-Cyrl-RU",myw:"myw-Latn-ZZ",myx:"myx-Latn-UG",myz:"myz-Mand-IR",mzk:"mzk-Latn-ZZ",mzm:"mzm-Latn-ZZ",mzn:"mzn-Arab-IR",mzp:"mzp-Latn-ZZ",mzw:"mzw-Latn-ZZ",mzz:"mzz-Latn-ZZ",na:"na-Latn-NR",nac:"nac-Latn-ZZ",naf:"naf-Latn-ZZ",nak:"nak-Latn-ZZ",nan:"nan-Hans-CN",nap:"nap-Latn-IT",naq:"naq-Latn-NA",nas:"nas-Latn-ZZ",nb:"nb-Latn-NO",nca:"nca-Latn-ZZ",nce:"nce-Latn-ZZ",ncf:"ncf-Latn-ZZ",nch:"nch-Latn-MX",nco:"nco-Latn-ZZ",ncu:"ncu-Latn-ZZ",nd:"nd-Latn-ZW",ndc:"ndc-Latn-MZ",nds:"nds-Latn-DE",ne:"ne-Deva-NP",neb:"neb-Latn-ZZ",new:"new-Deva-NP",nex:"nex-Latn-ZZ",nfr:"nfr-Latn-ZZ",ng:"ng-Latn-NA",nga:"nga-Latn-ZZ",ngb:"ngb-Latn-ZZ",ngl:"ngl-Latn-MZ",nhb:"nhb-Latn-ZZ",nhe:"nhe-Latn-MX",nhw:"nhw-Latn-MX",nif:"nif-Latn-ZZ",nii:"nii-Latn-ZZ",nij:"nij-Latn-ID",nin:"nin-Latn-ZZ",niu:"niu-Latn-NU",niy:"niy-Latn-ZZ",niz:"niz-Latn-ZZ",njo:"njo-Latn-IN",nkg:"nkg-Latn-ZZ",nko:"nko-Latn-ZZ",nl:"nl-Latn-NL",nmg:"nmg-Latn-CM",nmz:"nmz-Latn-ZZ",nn:"nn-Latn-NO",nnf:"nnf-Latn-ZZ",nnh:"nnh-Latn-CM",nnk:"nnk-Latn-ZZ",nnm:"nnm-Latn-ZZ",nnp:"nnp-Wcho-IN",no:"no-Latn-NO",nod:"nod-Lana-TH",noe:"noe-Deva-IN",non:"non-Runr-SE",nop:"nop-Latn-ZZ",nou:"nou-Latn-ZZ",nqo:"nqo-Nkoo-GN",nr:"nr-Latn-ZA",nrb:"nrb-Latn-ZZ",nsk:"nsk-Cans-CA",nsn:"nsn-Latn-ZZ",nso:"nso-Latn-ZA",nss:"nss-Latn-ZZ",ntm:"ntm-Latn-ZZ",ntr:"ntr-Latn-ZZ",nui:"nui-Latn-ZZ",nup:"nup-Latn-ZZ",nus:"nus-Latn-SS",nuv:"nuv-Latn-ZZ",nux:"nux-Latn-ZZ",nv:"nv-Latn-US",nwb:"nwb-Latn-ZZ",nxq:"nxq-Latn-CN",nxr:"nxr-Latn-ZZ",ny:"ny-Latn-MW",nym:"nym-Latn-TZ",nyn:"nyn-Latn-UG",nzi:"nzi-Latn-GH",oc:"oc-Latn-FR",ogc:"ogc-Latn-ZZ",okr:"okr-Latn-ZZ",okv:"okv-Latn-ZZ",om:"om-Latn-ET",ong:"ong-Latn-ZZ",onn:"onn-Latn-ZZ",ons:"ons-Latn-ZZ",opm:"opm-Latn-ZZ",or:"or-Orya-IN",oro:"oro-Latn-ZZ",oru:"oru-Arab-ZZ",os:"os-Cyrl-GE",osa:"osa-Osge-US",ota:"ota-Arab-ZZ",otk:"otk-Orkh-MN",ozm:"ozm-Latn-ZZ",pa:"pa-Guru-IN","pa-Arab":"pa-Arab-PK","pa-PK":"pa-Arab-PK",pag:"pag-Latn-PH",pal:"pal-Phli-IR","pal-Phlp":"pal-Phlp-CN",pam:"pam-Latn-PH",pap:"pap-Latn-AW",pau:"pau-Latn-PW",pbi:"pbi-Latn-ZZ",pcd:"pcd-Latn-FR",pcm:"pcm-Latn-NG",pdc:"pdc-Latn-US",pdt:"pdt-Latn-CA",ped:"ped-Latn-ZZ",peo:"peo-Xpeo-IR",pex:"pex-Latn-ZZ",pfl:"pfl-Latn-DE",phl:"phl-Arab-ZZ",phn:"phn-Phnx-LB",pil:"pil-Latn-ZZ",pip:"pip-Latn-ZZ",pka:"pka-Brah-IN",pko:"pko-Latn-KE",pl:"pl-Latn-PL",pla:"pla-Latn-ZZ",pms:"pms-Latn-IT",png:"png-Latn-ZZ",pnn:"pnn-Latn-ZZ",pnt:"pnt-Grek-GR",pon:"pon-Latn-FM",ppa:"ppa-Deva-IN",ppo:"ppo-Latn-ZZ",pra:"pra-Khar-PK",prd:"prd-Arab-IR",prg:"prg-Latn-001",ps:"ps-Arab-AF",pss:"pss-Latn-ZZ",pt:"pt-Latn-BR",ptp:"ptp-Latn-ZZ",puu:"puu-Latn-GA",pwa:"pwa-Latn-ZZ",qu:"qu-Latn-PE",quc:"quc-Latn-GT",qug:"qug-Latn-EC",rai:"rai-Latn-ZZ",raj:"raj-Deva-IN",rao:"rao-Latn-ZZ",rcf:"rcf-Latn-RE",rej:"rej-Latn-ID",rel:"rel-Latn-ZZ",res:"res-Latn-ZZ",rgn:"rgn-Latn-IT",rhg:"rhg-Arab-MM",ria:"ria-Latn-IN",rif:"rif-Tfng-MA","rif-NL":"rif-Latn-NL",rjs:"rjs-Deva-NP",rkt:"rkt-Beng-BD",rm:"rm-Latn-CH",rmf:"rmf-Latn-FI",rmo:"rmo-Latn-CH",rmt:"rmt-Arab-IR",rmu:"rmu-Latn-SE",rn:"rn-Latn-BI",rna:"rna-Latn-ZZ",rng:"rng-Latn-MZ",ro:"ro-Latn-RO",rob:"rob-Latn-ID",rof:"rof-Latn-TZ",roo:"roo-Latn-ZZ",rro:"rro-Latn-ZZ",rtm:"rtm-Latn-FJ",ru:"ru-Cyrl-RU",rue:"rue-Cyrl-UA",rug:"rug-Latn-SB",rw:"rw-Latn-RW",rwk:"rwk-Latn-TZ",rwo:"rwo-Latn-ZZ",ryu:"ryu-Kana-JP",sa:"sa-Deva-IN",saf:"saf-Latn-GH",sah:"sah-Cyrl-RU",saq:"saq-Latn-KE",sas:"sas-Latn-ID",sat:"sat-Latn-IN",sav:"sav-Latn-SN",saz:"saz-Saur-IN",sba:"sba-Latn-ZZ",sbe:"sbe-Latn-ZZ",sbp:"sbp-Latn-TZ",sc:"sc-Latn-IT",sck:"sck-Deva-IN",scl:"scl-Arab-ZZ",scn:"scn-Latn-IT",sco:"sco-Latn-GB",scs:"scs-Latn-CA",sd:"sd-Arab-PK","sd-Deva":"sd-Deva-IN","sd-Khoj":"sd-Khoj-IN","sd-Sind":"sd-Sind-IN",sdc:"sdc-Latn-IT",sdh:"sdh-Arab-IR",se:"se-Latn-NO",sef:"sef-Latn-CI",seh:"seh-Latn-MZ",sei:"sei-Latn-MX",ses:"ses-Latn-ML",sg:"sg-Latn-CF",sga:"sga-Ogam-IE",sgs:"sgs-Latn-LT",sgw:"sgw-Ethi-ZZ",sgz:"sgz-Latn-ZZ",shi:"shi-Tfng-MA",shk:"shk-Latn-ZZ",shn:"shn-Mymr-MM",shu:"shu-Arab-ZZ",si:"si-Sinh-LK",sid:"sid-Latn-ET",sig:"sig-Latn-ZZ",sil:"sil-Latn-ZZ",sim:"sim-Latn-ZZ",sjr:"sjr-Latn-ZZ",sk:"sk-Latn-SK",skc:"skc-Latn-ZZ",skr:"skr-Arab-PK",sks:"sks-Latn-ZZ",sl:"sl-Latn-SI",sld:"sld-Latn-ZZ",sli:"sli-Latn-PL",sll:"sll-Latn-ZZ",sly:"sly-Latn-ID",sm:"sm-Latn-WS",sma:"sma-Latn-SE",smj:"smj-Latn-SE",smn:"smn-Latn-FI",smp:"smp-Samr-IL",smq:"smq-Latn-ZZ",sms:"sms-Latn-FI",sn:"sn-Latn-ZW",snc:"snc-Latn-ZZ",snk:"snk-Latn-ML",snp:"snp-Latn-ZZ",snx:"snx-Latn-ZZ",sny:"sny-Latn-ZZ",so:"so-Latn-SO",sog:"sog-Sogd-UZ",sok:"sok-Latn-ZZ",soq:"soq-Latn-ZZ",sou:"sou-Thai-TH",soy:"soy-Latn-ZZ",spd:"spd-Latn-ZZ",spl:"spl-Latn-ZZ",sps:"sps-Latn-ZZ",sq:"sq-Latn-AL",sr:"sr-Cyrl-RS","sr-ME":"sr-Latn-ME","sr-RO":"sr-Latn-RO","sr-RU":"sr-Latn-RU","sr-TR":"sr-Latn-TR",srb:"srb-Sora-IN",srn:"srn-Latn-SR",srr:"srr-Latn-SN",srx:"srx-Deva-IN",ss:"ss-Latn-ZA",ssd:"ssd-Latn-ZZ",ssg:"ssg-Latn-ZZ",ssy:"ssy-Latn-ER",st:"st-Latn-ZA",stk:"stk-Latn-ZZ",stq:"stq-Latn-DE",su:"su-Latn-ID",sua:"sua-Latn-ZZ",sue:"sue-Latn-ZZ",suk:"suk-Latn-TZ",sur:"sur-Latn-ZZ",sus:"sus-Latn-GN",sv:"sv-Latn-SE",sw:"sw-Latn-TZ",swb:"swb-Arab-YT",swc:"swc-Latn-CD",swg:"swg-Latn-DE",swp:"swp-Latn-ZZ",swv:"swv-Deva-IN",sxn:"sxn-Latn-ID",sxw:"sxw-Latn-ZZ",syl:"syl-Beng-BD",syr:"syr-Syrc-IQ",szl:"szl-Latn-PL",ta:"ta-Taml-IN",taj:"taj-Deva-NP",tal:"tal-Latn-ZZ",tan:"tan-Latn-ZZ",taq:"taq-Latn-ZZ",tbc:"tbc-Latn-ZZ",tbd:"tbd-Latn-ZZ",tbf:"tbf-Latn-ZZ",tbg:"tbg-Latn-ZZ",tbo:"tbo-Latn-ZZ",tbw:"tbw-Latn-PH",tbz:"tbz-Latn-ZZ",tci:"tci-Latn-ZZ",tcy:"tcy-Knda-IN",tdd:"tdd-Tale-CN",tdg:"tdg-Deva-NP",tdh:"tdh-Deva-NP",tdu:"tdu-Latn-MY",te:"te-Telu-IN",ted:"ted-Latn-ZZ",tem:"tem-Latn-SL",teo:"teo-Latn-UG",tet:"tet-Latn-TL",tfi:"tfi-Latn-ZZ",tg:"tg-Cyrl-TJ","tg-Arab":"tg-Arab-PK","tg-PK":"tg-Arab-PK",tgc:"tgc-Latn-ZZ",tgo:"tgo-Latn-ZZ",tgu:"tgu-Latn-ZZ",th:"th-Thai-TH",thl:"thl-Deva-NP",thq:"thq-Deva-NP",thr:"thr-Deva-NP",ti:"ti-Ethi-ET",tif:"tif-Latn-ZZ",tig:"tig-Ethi-ER",tik:"tik-Latn-ZZ",tim:"tim-Latn-ZZ",tio:"tio-Latn-ZZ",tiv:"tiv-Latn-NG",tk:"tk-Latn-TM",tkl:"tkl-Latn-TK",tkr:"tkr-Latn-AZ",tkt:"tkt-Deva-NP",tl:"tl-Latn-PH",tlf:"tlf-Latn-ZZ",tlx:"tlx-Latn-ZZ",tly:"tly-Latn-AZ",tmh:"tmh-Latn-NE",tmy:"tmy-Latn-ZZ",tn:"tn-Latn-ZA",tnh:"tnh-Latn-ZZ",to:"to-Latn-TO",tof:"tof-Latn-ZZ",tog:"tog-Latn-MW",toq:"toq-Latn-ZZ",tpi:"tpi-Latn-PG",tpm:"tpm-Latn-ZZ",tpz:"tpz-Latn-ZZ",tqo:"tqo-Latn-ZZ",tr:"tr-Latn-TR",tru:"tru-Latn-TR",trv:"trv-Latn-TW",trw:"trw-Arab-ZZ",ts:"ts-Latn-ZA",tsd:"tsd-Grek-GR",tsf:"tsf-Deva-NP",tsg:"tsg-Latn-PH",tsj:"tsj-Tibt-BT",tsw:"tsw-Latn-ZZ",tt:"tt-Cyrl-RU",ttd:"ttd-Latn-ZZ",tte:"tte-Latn-ZZ",ttj:"ttj-Latn-UG",ttr:"ttr-Latn-ZZ",tts:"tts-Thai-TH",ttt:"ttt-Latn-AZ",tuh:"tuh-Latn-ZZ",tul:"tul-Latn-ZZ",tum:"tum-Latn-MW",tuq:"tuq-Latn-ZZ",tvd:"tvd-Latn-ZZ",tvl:"tvl-Latn-TV",tvu:"tvu-Latn-ZZ",twh:"twh-Latn-ZZ",twq:"twq-Latn-NE",txg:"txg-Tang-CN",ty:"ty-Latn-PF",tya:"tya-Latn-ZZ",tyv:"tyv-Cyrl-RU",tzm:"tzm-Latn-MA",ubu:"ubu-Latn-ZZ",udm:"udm-Cyrl-RU",ug:"ug-Arab-CN","ug-Cyrl":"ug-Cyrl-KZ","ug-KZ":"ug-Cyrl-KZ","ug-MN":"ug-Cyrl-MN",uga:"uga-Ugar-SY",uk:"uk-Cyrl-UA",uli:"uli-Latn-FM",umb:"umb-Latn-AO",und:"en-Latn-US","und-002":"en-Latn-NG","und-003":"en-Latn-US","und-005":"pt-Latn-BR","und-009":"en-Latn-AU","und-011":"en-Latn-NG","und-013":"es-Latn-MX","und-014":"sw-Latn-TZ","und-015":"ar-Arab-EG","und-017":"sw-Latn-CD","und-018":"en-Latn-ZA","und-019":"en-Latn-US","und-021":"en-Latn-US","und-029":"es-Latn-CU","und-030":"zh-Hans-CN","und-034":"hi-Deva-IN","und-035":"id-Latn-ID","und-039":"it-Latn-IT","und-053":"en-Latn-AU","und-054":"en-Latn-PG","und-057":"en-Latn-GU","und-061":"sm-Latn-WS","und-142":"zh-Hans-CN","und-143":"uz-Latn-UZ","und-145":"ar-Arab-SA","und-150":"ru-Cyrl-RU","und-151":"ru-Cyrl-RU","und-154":"en-Latn-GB","und-155":"de-Latn-DE","und-202":"en-Latn-NG","und-419":"es-Latn-419","und-AD":"ca-Latn-AD","und-Adlm":"ff-Adlm-GN","und-AE":"ar-Arab-AE","und-AF":"fa-Arab-AF","und-Aghb":"lez-Aghb-RU","und-Ahom":"aho-Ahom-IN","und-AL":"sq-Latn-AL","und-AM":"hy-Armn-AM","und-AO":"pt-Latn-AO","und-AQ":"und-Latn-AQ","und-AR":"es-Latn-AR","und-Arab":"ar-Arab-EG","und-Arab-CC":"ms-Arab-CC","und-Arab-CN":"ug-Arab-CN","und-Arab-GB":"ks-Arab-GB","und-Arab-ID":"ms-Arab-ID","und-Arab-IN":"ur-Arab-IN","und-Arab-KH":"cja-Arab-KH","und-Arab-MM":"rhg-Arab-MM","und-Arab-MN":"kk-Arab-MN","und-Arab-MU":"ur-Arab-MU","und-Arab-NG":"ha-Arab-NG","und-Arab-PK":"ur-Arab-PK","und-Arab-TG":"apd-Arab-TG","und-Arab-TH":"mfa-Arab-TH","und-Arab-TJ":"fa-Arab-TJ","und-Arab-TR":"az-Arab-TR","und-Arab-YT":"swb-Arab-YT","und-Armi":"arc-Armi-IR","und-Armn":"hy-Armn-AM","und-AS":"sm-Latn-AS","und-AT":"de-Latn-AT","und-Avst":"ae-Avst-IR","und-AW":"nl-Latn-AW","und-AX":"sv-Latn-AX","und-AZ":"az-Latn-AZ","und-BA":"bs-Latn-BA","und-Bali":"ban-Bali-ID","und-Bamu":"bax-Bamu-CM","und-Bass":"bsq-Bass-LR","und-Batk":"bbc-Batk-ID","und-BD":"bn-Beng-BD","und-BE":"nl-Latn-BE","und-Beng":"bn-Beng-BD","und-BF":"fr-Latn-BF","und-BG":"bg-Cyrl-BG","und-BH":"ar-Arab-BH","und-Bhks":"sa-Bhks-IN","und-BI":"rn-Latn-BI","und-BJ":"fr-Latn-BJ","und-BL":"fr-Latn-BL","und-BN":"ms-Latn-BN","und-BO":"es-Latn-BO","und-Bopo":"zh-Bopo-TW","und-BQ":"pap-Latn-BQ","und-BR":"pt-Latn-BR","und-Brah":"pka-Brah-IN","und-Brai":"fr-Brai-FR","und-BT":"dz-Tibt-BT","und-Bugi":"bug-Bugi-ID","und-Buhd":"bku-Buhd-PH","und-BV":"und-Latn-BV","und-BY":"be-Cyrl-BY","und-Cakm":"ccp-Cakm-BD","und-Cans":"cr-Cans-CA","und-Cari":"xcr-Cari-TR","und-CD":"sw-Latn-CD","und-CF":"fr-Latn-CF","und-CG":"fr-Latn-CG","und-CH":"de-Latn-CH","und-Cham":"cjm-Cham-VN","und-Cher":"chr-Cher-US","und-CI":"fr-Latn-CI","und-CL":"es-Latn-CL","und-CM":"fr-Latn-CM","und-CN":"zh-Hans-CN","und-CO":"es-Latn-CO","und-Copt":"cop-Copt-EG","und-CP":"und-Latn-CP","und-Cprt":"grc-Cprt-CY","und-CR":"es-Latn-CR","und-CU":"es-Latn-CU","und-CV":"pt-Latn-CV","und-CW":"pap-Latn-CW","und-CY":"el-Grek-CY","und-Cyrl":"ru-Cyrl-RU","und-Cyrl-AL":"mk-Cyrl-AL","und-Cyrl-BA":"sr-Cyrl-BA","und-Cyrl-GE":"ab-Cyrl-GE","und-Cyrl-GR":"mk-Cyrl-GR","und-Cyrl-MD":"uk-Cyrl-MD","und-Cyrl-RO":"bg-Cyrl-RO","und-Cyrl-SK":"uk-Cyrl-SK","und-Cyrl-TR":"kbd-Cyrl-TR","und-Cyrl-XK":"sr-Cyrl-XK","und-CZ":"cs-Latn-CZ","und-DE":"de-Latn-DE","und-Deva":"hi-Deva-IN","und-Deva-BT":"ne-Deva-BT","und-Deva-FJ":"hif-Deva-FJ","und-Deva-MU":"bho-Deva-MU","und-Deva-PK":"btv-Deva-PK","und-DJ":"aa-Latn-DJ","und-DK":"da-Latn-DK","und-DO":"es-Latn-DO","und-Dogr":"doi-Dogr-IN","und-Dupl":"fr-Dupl-FR","und-DZ":"ar-Arab-DZ","und-EA":"es-Latn-EA","und-EC":"es-Latn-EC","und-EE":"et-Latn-EE","und-EG":"ar-Arab-EG","und-Egyp":"egy-Egyp-EG","und-EH":"ar-Arab-EH","und-Elba":"sq-Elba-AL","und-Elym":"arc-Elym-IR","und-ER":"ti-Ethi-ER","und-ES":"es-Latn-ES","und-ET":"am-Ethi-ET","und-Ethi":"am-Ethi-ET","und-EU":"en-Latn-GB","und-EZ":"de-Latn-EZ","und-FI":"fi-Latn-FI","und-FO":"fo-Latn-FO","und-FR":"fr-Latn-FR","und-GA":"fr-Latn-GA","und-GE":"ka-Geor-GE","und-Geor":"ka-Geor-GE","und-GF":"fr-Latn-GF","und-GH":"ak-Latn-GH","und-GL":"kl-Latn-GL","und-Glag":"cu-Glag-BG","und-GN":"fr-Latn-GN","und-Gong":"wsg-Gong-IN","und-Gonm":"esg-Gonm-IN","und-Goth":"got-Goth-UA","und-GP":"fr-Latn-GP","und-GQ":"es-Latn-GQ","und-GR":"el-Grek-GR","und-Gran":"sa-Gran-IN","und-Grek":"el-Grek-GR","und-Grek-TR":"bgx-Grek-TR","und-GS":"und-Latn-GS","und-GT":"es-Latn-GT","und-Gujr":"gu-Gujr-IN","und-Guru":"pa-Guru-IN","und-GW":"pt-Latn-GW","und-Hanb":"zh-Hanb-TW","und-Hang":"ko-Hang-KR","und-Hani":"zh-Hani-CN","und-Hano":"hnn-Hano-PH","und-Hans":"zh-Hans-CN","und-Hant":"zh-Hant-TW","und-Hatr":"mis-Hatr-IQ","und-Hebr":"he-Hebr-IL","und-Hebr-CA":"yi-Hebr-CA","und-Hebr-GB":"yi-Hebr-GB","und-Hebr-SE":"yi-Hebr-SE","und-Hebr-UA":"yi-Hebr-UA","und-Hebr-US":"yi-Hebr-US","und-Hira":"ja-Hira-JP","und-HK":"zh-Hant-HK","und-Hluw":"hlu-Hluw-TR","und-HM":"und-Latn-HM","und-Hmng":"hnj-Hmng-LA","und-Hmnp":"mww-Hmnp-US","und-HN":"es-Latn-HN","und-HR":"hr-Latn-HR","und-HT":"ht-Latn-HT","und-HU":"hu-Latn-HU","und-Hung":"hu-Hung-HU","und-IC":"es-Latn-IC","und-ID":"id-Latn-ID","und-IL":"he-Hebr-IL","und-IN":"hi-Deva-IN","und-IQ":"ar-Arab-IQ","und-IR":"fa-Arab-IR","und-IS":"is-Latn-IS","und-IT":"it-Latn-IT","und-Ital":"ett-Ital-IT","und-Jamo":"ko-Jamo-KR","und-Java":"jv-Java-ID","und-JO":"ar-Arab-JO","und-JP":"ja-Jpan-JP","und-Jpan":"ja-Jpan-JP","und-Kali":"eky-Kali-MM","und-Kana":"ja-Kana-JP","und-KE":"sw-Latn-KE","und-KG":"ky-Cyrl-KG","und-KH":"km-Khmr-KH","und-Khar":"pra-Khar-PK","und-Khmr":"km-Khmr-KH","und-Khoj":"sd-Khoj-IN","und-KM":"ar-Arab-KM","und-Knda":"kn-Knda-IN","und-Kore":"ko-Kore-KR","und-KP":"ko-Kore-KP","und-KR":"ko-Kore-KR","und-Kthi":"bho-Kthi-IN","und-KW":"ar-Arab-KW","und-KZ":"ru-Cyrl-KZ","und-LA":"lo-Laoo-LA","und-Lana":"nod-Lana-TH","und-Laoo":"lo-Laoo-LA","und-Latn-AF":"tk-Latn-AF","und-Latn-AM":"ku-Latn-AM","und-Latn-CN":"za-Latn-CN","und-Latn-CY":"tr-Latn-CY","und-Latn-DZ":"fr-Latn-DZ","und-Latn-ET":"en-Latn-ET","und-Latn-GE":"ku-Latn-GE","und-Latn-IR":"tk-Latn-IR","und-Latn-KM":"fr-Latn-KM","und-Latn-MA":"fr-Latn-MA","und-Latn-MK":"sq-Latn-MK","und-Latn-MM":"kac-Latn-MM","und-Latn-MO":"pt-Latn-MO","und-Latn-MR":"fr-Latn-MR","und-Latn-RU":"krl-Latn-RU","und-Latn-SY":"fr-Latn-SY","und-Latn-TN":"fr-Latn-TN","und-Latn-TW":"trv-Latn-TW","und-Latn-UA":"pl-Latn-UA","und-LB":"ar-Arab-LB","und-Lepc":"lep-Lepc-IN","und-LI":"de-Latn-LI","und-Limb":"lif-Limb-IN","und-Lina":"lab-Lina-GR","und-Linb":"grc-Linb-GR","und-Lisu":"lis-Lisu-CN","und-LK":"si-Sinh-LK","und-LS":"st-Latn-LS","und-LT":"lt-Latn-LT","und-LU":"fr-Latn-LU","und-LV":"lv-Latn-LV","und-LY":"ar-Arab-LY","und-Lyci":"xlc-Lyci-TR","und-Lydi":"xld-Lydi-TR","und-MA":"ar-Arab-MA","und-Mahj":"hi-Mahj-IN","und-Maka":"mak-Maka-ID","und-Mand":"myz-Mand-IR","und-Mani":"xmn-Mani-CN","und-Marc":"bo-Marc-CN","und-MC":"fr-Latn-MC","und-MD":"ro-Latn-MD","und-ME":"sr-Latn-ME","und-Medf":"mis-Medf-NG","und-Mend":"men-Mend-SL","und-Merc":"xmr-Merc-SD","und-Mero":"xmr-Mero-SD","und-MF":"fr-Latn-MF","und-MG":"mg-Latn-MG","und-MK":"mk-Cyrl-MK","und-ML":"bm-Latn-ML","und-Mlym":"ml-Mlym-IN","und-MM":"my-Mymr-MM","und-MN":"mn-Cyrl-MN","und-MO":"zh-Hant-MO","und-Modi":"mr-Modi-IN","und-Mong":"mn-Mong-CN","und-MQ":"fr-Latn-MQ","und-MR":"ar-Arab-MR","und-Mroo":"mro-Mroo-BD","und-MT":"mt-Latn-MT","und-Mtei":"mni-Mtei-IN","und-MU":"mfe-Latn-MU","und-Mult":"skr-Mult-PK","und-MV":"dv-Thaa-MV","und-MX":"es-Latn-MX","und-MY":"ms-Latn-MY","und-Mymr":"my-Mymr-MM","und-Mymr-IN":"kht-Mymr-IN","und-Mymr-TH":"mnw-Mymr-TH","und-MZ":"pt-Latn-MZ","und-NA":"af-Latn-NA","und-Nand":"sa-Nand-IN","und-Narb":"xna-Narb-SA","und-Nbat":"arc-Nbat-JO","und-NC":"fr-Latn-NC","und-NE":"ha-Latn-NE","und-Newa":"new-Newa-NP","und-NI":"es-Latn-NI","und-Nkoo":"man-Nkoo-GN","und-NL":"nl-Latn-NL","und-NO":"nb-Latn-NO","und-NP":"ne-Deva-NP","und-Nshu":"zhx-Nshu-CN","und-Ogam":"sga-Ogam-IE","und-Olck":"sat-Olck-IN","und-OM":"ar-Arab-OM","und-Orkh":"otk-Orkh-MN","und-Orya":"or-Orya-IN","und-Osge":"osa-Osge-US","und-Osma":"so-Osma-SO","und-PA":"es-Latn-PA","und-Palm":"arc-Palm-SY","und-Pauc":"ctd-Pauc-MM","und-PE":"es-Latn-PE","und-Perm":"kv-Perm-RU","und-PF":"fr-Latn-PF","und-PG":"tpi-Latn-PG","und-PH":"fil-Latn-PH","und-Phag":"lzh-Phag-CN","und-Phli":"pal-Phli-IR","und-Phlp":"pal-Phlp-CN","und-Phnx":"phn-Phnx-LB","und-PK":"ur-Arab-PK","und-PL":"pl-Latn-PL","und-Plrd":"hmd-Plrd-CN","und-PM":"fr-Latn-PM","und-PR":"es-Latn-PR","und-Prti":"xpr-Prti-IR","und-PS":"ar-Arab-PS","und-PT":"pt-Latn-PT","und-PW":"pau-Latn-PW","und-PY":"gn-Latn-PY","und-QA":"ar-Arab-QA","und-QO":"en-Latn-DG","und-RE":"fr-Latn-RE","und-Rjng":"rej-Rjng-ID","und-RO":"ro-Latn-RO","und-Rohg":"rhg-Rohg-MM","und-RS":"sr-Cyrl-RS","und-RU":"ru-Cyrl-RU","und-Runr":"non-Runr-SE","und-RW":"rw-Latn-RW","und-SA":"ar-Arab-SA","und-Samr":"smp-Samr-IL","und-Sarb":"xsa-Sarb-YE","und-Saur":"saz-Saur-IN","und-SC":"fr-Latn-SC","und-SD":"ar-Arab-SD","und-SE":"sv-Latn-SE","und-Sgnw":"ase-Sgnw-US","und-Shaw":"en-Shaw-GB","und-Shrd":"sa-Shrd-IN","und-SI":"sl-Latn-SI","und-Sidd":"sa-Sidd-IN","und-Sind":"sd-Sind-IN","und-Sinh":"si-Sinh-LK","und-SJ":"nb-Latn-SJ","und-SK":"sk-Latn-SK","und-SM":"it-Latn-SM","und-SN":"fr-Latn-SN","und-SO":"so-Latn-SO","und-Sogd":"sog-Sogd-UZ","und-Sogo":"sog-Sogo-UZ","und-Sora":"srb-Sora-IN","und-Soyo":"cmg-Soyo-MN","und-SR":"nl-Latn-SR","und-ST":"pt-Latn-ST","und-Sund":"su-Sund-ID","und-SV":"es-Latn-SV","und-SY":"ar-Arab-SY","und-Sylo":"syl-Sylo-BD","und-Syrc":"syr-Syrc-IQ","und-Tagb":"tbw-Tagb-PH","und-Takr":"doi-Takr-IN","und-Tale":"tdd-Tale-CN","und-Talu":"khb-Talu-CN","und-Taml":"ta-Taml-IN","und-Tang":"txg-Tang-CN","und-Tavt":"blt-Tavt-VN","und-TD":"fr-Latn-TD","und-Telu":"te-Telu-IN","und-TF":"fr-Latn-TF","und-Tfng":"zgh-Tfng-MA","und-TG":"fr-Latn-TG","und-Tglg":"fil-Tglg-PH","und-TH":"th-Thai-TH","und-Thaa":"dv-Thaa-MV","und-Thai":"th-Thai-TH","und-Thai-CN":"lcp-Thai-CN","und-Thai-KH":"kdt-Thai-KH","und-Thai-LA":"kdt-Thai-LA","und-Tibt":"bo-Tibt-CN","und-Tirh":"mai-Tirh-IN","und-TJ":"tg-Cyrl-TJ","und-TK":"tkl-Latn-TK","und-TL":"pt-Latn-TL","und-TM":"tk-Latn-TM","und-TN":"ar-Arab-TN","und-TO":"to-Latn-TO","und-TR":"tr-Latn-TR","und-TV":"tvl-Latn-TV","und-TW":"zh-Hant-TW","und-TZ":"sw-Latn-TZ","und-UA":"uk-Cyrl-UA","und-UG":"sw-Latn-UG","und-Ugar":"uga-Ugar-SY","und-UY":"es-Latn-UY","und-UZ":"uz-Latn-UZ","und-VA":"it-Latn-VA","und-Vaii":"vai-Vaii-LR","und-VE":"es-Latn-VE","und-VN":"vi-Latn-VN","und-VU":"bi-Latn-VU","und-Wara":"hoc-Wara-IN","und-Wcho":"nnp-Wcho-IN","und-WF":"fr-Latn-WF","und-WS":"sm-Latn-WS","und-XK":"sq-Latn-XK","und-Xpeo":"peo-Xpeo-IR","und-Xsux":"akk-Xsux-IQ","und-YE":"ar-Arab-YE","und-Yiii":"ii-Yiii-CN","und-YT":"fr-Latn-YT","und-Zanb":"cmg-Zanb-MN","und-ZW":"sn-Latn-ZW",unr:"unr-Beng-IN","unr-Deva":"unr-Deva-NP","unr-NP":"unr-Deva-NP",unx:"unx-Beng-IN",uok:"uok-Latn-ZZ",ur:"ur-Arab-PK",uri:"uri-Latn-ZZ",urt:"urt-Latn-ZZ",urw:"urw-Latn-ZZ",usa:"usa-Latn-ZZ",utr:"utr-Latn-ZZ",uvh:"uvh-Latn-ZZ",uvl:"uvl-Latn-ZZ",uz:"uz-Latn-UZ","uz-AF":"uz-Arab-AF","uz-Arab":"uz-Arab-AF","uz-CN":"uz-Cyrl-CN",vag:"vag-Latn-ZZ",vai:"vai-Vaii-LR",van:"van-Latn-ZZ",ve:"ve-Latn-ZA",vec:"vec-Latn-IT",vep:"vep-Latn-RU",vi:"vi-Latn-VN",vic:"vic-Latn-SX",viv:"viv-Latn-ZZ",vls:"vls-Latn-BE",vmf:"vmf-Latn-DE",vmw:"vmw-Latn-MZ",vo:"vo-Latn-001",vot:"vot-Latn-RU",vro:"vro-Latn-EE",vun:"vun-Latn-TZ",vut:"vut-Latn-ZZ",wa:"wa-Latn-BE",wae:"wae-Latn-CH",waj:"waj-Latn-ZZ",wal:"wal-Ethi-ET",wan:"wan-Latn-ZZ",war:"war-Latn-PH",wbp:"wbp-Latn-AU",wbq:"wbq-Telu-IN",wbr:"wbr-Deva-IN",wci:"wci-Latn-ZZ",wer:"wer-Latn-ZZ",wgi:"wgi-Latn-ZZ",whg:"whg-Latn-ZZ",wib:"wib-Latn-ZZ",wiu:"wiu-Latn-ZZ",wiv:"wiv-Latn-ZZ",wja:"wja-Latn-ZZ",wji:"wji-Latn-ZZ",wls:"wls-Latn-WF",wmo:"wmo-Latn-ZZ",wnc:"wnc-Latn-ZZ",wni:"wni-Arab-KM",wnu:"wnu-Latn-ZZ",wo:"wo-Latn-SN",wob:"wob-Latn-ZZ",wos:"wos-Latn-ZZ",wrs:"wrs-Latn-ZZ",wsg:"wsg-Gong-IN",wsk:"wsk-Latn-ZZ",wtm:"wtm-Deva-IN",wuu:"wuu-Hans-CN",wuv:"wuv-Latn-ZZ",wwa:"wwa-Latn-ZZ",xav:"xav-Latn-BR",xbi:"xbi-Latn-ZZ",xcr:"xcr-Cari-TR",xes:"xes-Latn-ZZ",xh:"xh-Latn-ZA",xla:"xla-Latn-ZZ",xlc:"xlc-Lyci-TR",xld:"xld-Lydi-TR",xmf:"xmf-Geor-GE",xmn:"xmn-Mani-CN",xmr:"xmr-Merc-SD",xna:"xna-Narb-SA",xnr:"xnr-Deva-IN",xog:"xog-Latn-UG",xon:"xon-Latn-ZZ",xpr:"xpr-Prti-IR",xrb:"xrb-Latn-ZZ",xsa:"xsa-Sarb-YE",xsi:"xsi-Latn-ZZ",xsm:"xsm-Latn-ZZ",xsr:"xsr-Deva-NP",xwe:"xwe-Latn-ZZ",yam:"yam-Latn-ZZ",yao:"yao-Latn-MZ",yap:"yap-Latn-FM",yas:"yas-Latn-ZZ",yat:"yat-Latn-ZZ",yav:"yav-Latn-CM",yay:"yay-Latn-ZZ",yaz:"yaz-Latn-ZZ",yba:"yba-Latn-ZZ",ybb:"ybb-Latn-CM",yby:"yby-Latn-ZZ",yer:"yer-Latn-ZZ",ygr:"ygr-Latn-ZZ",ygw:"ygw-Latn-ZZ",yi:"yi-Hebr-001",yko:"yko-Latn-ZZ",yle:"yle-Latn-ZZ",ylg:"ylg-Latn-ZZ",yll:"yll-Latn-ZZ",yml:"yml-Latn-ZZ",yo:"yo-Latn-NG",yon:"yon-Latn-ZZ",yrb:"yrb-Latn-ZZ",yre:"yre-Latn-ZZ",yrl:"yrl-Latn-BR",yss:"yss-Latn-ZZ",yua:"yua-Latn-MX",yue:"yue-Hant-HK","yue-CN":"yue-Hans-CN","yue-Hans":"yue-Hans-CN",yuj:"yuj-Latn-ZZ",yut:"yut-Latn-ZZ",yuw:"yuw-Latn-ZZ",za:"za-Latn-CN",zag:"zag-Latn-SD",zdj:"zdj-Arab-KM",zea:"zea-Latn-NL",zgh:"zgh-Tfng-MA",zh:"zh-Hans-CN","zh-AU":"zh-Hant-AU","zh-BN":"zh-Hant-BN","zh-Bopo":"zh-Bopo-TW","zh-GB":"zh-Hant-GB","zh-GF":"zh-Hant-GF","zh-Hanb":"zh-Hanb-TW","zh-Hant":"zh-Hant-TW","zh-HK":"zh-Hant-HK","zh-ID":"zh-Hant-ID","zh-MO":"zh-Hant-MO","zh-MY":"zh-Hant-MY","zh-PA":"zh-Hant-PA","zh-PF":"zh-Hant-PF","zh-PH":"zh-Hant-PH","zh-SR":"zh-Hant-SR","zh-TH":"zh-Hant-TH","zh-TW":"zh-Hant-TW","zh-US":"zh-Hant-US","zh-VN":"zh-Hant-VN",zhx:"zhx-Nshu-CN",zia:"zia-Latn-ZZ",zlm:"zlm-Latn-TG",zmi:"zmi-Latn-MY",zne:"zne-Latn-ZZ",zu:"zu-Latn-ZA",zza:"zza-Latn-TR"}}},{main:{fr:{identity:{version:{_cldrVersion:"36"},language:"fr"},numbers:{defaultNumberingSystem:"latn",otherNumberingSystems:{native:"latn"},minimumGroupingDigits:"1","symbols-numberSystem-latn":{decimal:",",group:" ",list:";",percentSign:"%",plusSign:"+",minusSign:"-",exponential:"E",superscriptingExponent:"×",perMille:"‰",infinity:"∞",nan:"NaN",timeSeparator:":"},"decimalFormats-numberSystem-latn":{standard:"#,##0.###",long:{decimalFormat:{"1000-count-one":"0 millier","1000-count-other":"0 mille","10000-count-one":"00 mille","10000-count-other":"00 mille","100000-count-one":"000 mille","100000-count-other":"000 mille","1000000-count-one":"0 million","1000000-count-other":"0 millions","********-count-one":"00 million","********-count-other":"00 millions","********0-count-one":"000 million","********0-count-other":"000 millions","********00-count-one":"0 milliard","********00-count-other":"0 milliards","********000-count-one":"00 milliard","********000-count-other":"00 milliards","********0000-count-one":"000 milliard","********0000-count-other":"000 milliards","********00000-count-one":"0 billion","********00000-count-other":"0 billions","**************-count-one":"00 billion","**************-count-other":"00 billions","***************-count-one":"000 billion","***************-count-other":"000 billions"}},short:{decimalFormat:{"1000-count-one":"0 k","1000-count-other":"0 k","10000-count-one":"00 k","10000-count-other":"00 k","100000-count-one":"000 k","100000-count-other":"000 k","1000000-count-one":"0 M","1000000-count-other":"0 M","********-count-one":"00 M","********-count-other":"00 M","********0-count-one":"000 M","********0-count-other":"000 M","********00-count-one":"0 Md","********00-count-other":"0 Md","********000-count-one":"00 Md","********000-count-other":"00 Md","********0000-count-one":"000 Md","********0000-count-other":"000 Md","********00000-count-one":"0 Bn","********00000-count-other":"0 Bn","**************-count-one":"00 Bn","**************-count-other":"00 Bn","***************-count-one":"000 Bn","***************-count-other":"000 Bn"}}},"scientificFormats-numberSystem-latn":{standard:"#E0"},"percentFormats-numberSystem-latn":{standard:"#,##0 %"},"currencyFormats-numberSystem-latn":{currencySpacing:{beforeCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "},afterCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "}},standard:"#,##0.00 ¤",accounting:"#,##0.00 ¤;(#,##0.00 ¤)",short:{standard:{"1000-count-one":"0 k ¤","1000-count-other":"0 k ¤","10000-count-one":"00 k ¤","10000-count-other":"00 k ¤","100000-count-one":"000 k ¤","100000-count-other":"000 k ¤","1000000-count-one":"0 M ¤","1000000-count-other":"0 M ¤","********-count-one":"00 M ¤","********-count-other":"00 M ¤","********0-count-one":"000 M ¤","********0-count-other":"000 M ¤","********00-count-one":"0 Md ¤","********00-count-other":"0 Md ¤","********000-count-one":"00 Md ¤","********000-count-other":"00 Md ¤","********0000-count-one":"000 Md ¤","********0000-count-other":"000 Md ¤","********00000-count-one":"0 Bn ¤","********00000-count-other":"0 Bn ¤","**************-count-one":"00 Bn ¤","**************-count-other":"00 Bn ¤","***************-count-one":"000 Bn ¤","***************-count-other":"000 Bn ¤"}},"unitPattern-count-one":"{0} {1}","unitPattern-count-other":"{0} {1}"},"miscPatterns-numberSystem-latn":{approximately:"≈{0}",atLeast:"≥{0}",atMost:"≤{0}",range:"{0}–{1}"},minimalPairs:{"pluralMinimalPairs-count-one":"{0} jour","pluralMinimalPairs-count-other":"{0} jours",one:"Prenez la {0}re à droite.",other:"Prenez la {0}e à droite."}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},numberingSystems:{adlm:{_digits:"𞥐𞥑𞥒𞥓𞥔𞥕𞥖𞥗𞥘𞥙",_type:"numeric"},ahom:{_digits:"𑜰𑜱𑜲𑜳𑜴𑜵𑜶𑜷𑜸𑜹",_type:"numeric"},arab:{_digits:"٠١٢٣٤٥٦٧٨٩",_type:"numeric"},arabext:{_digits:"۰۱۲۳۴۵۶۷۸۹",_type:"numeric"},armn:{_rules:"armenian-upper",_type:"algorithmic"},armnlow:{_rules:"armenian-lower",_type:"algorithmic"},bali:{_digits:"᭐᭑᭒᭓᭔᭕᭖᭗᭘᭙",_type:"numeric"},beng:{_digits:"০১২৩৪৫৬৭৮৯",_type:"numeric"},bhks:{_digits:"𑱐𑱑𑱒𑱓𑱔𑱕𑱖𑱗𑱘𑱙",_type:"numeric"},brah:{_digits:"𑁦𑁧𑁨𑁩𑁪𑁫𑁬𑁭𑁮𑁯",_type:"numeric"},cakm:{_digits:"𑄶𑄷𑄸𑄹𑄺𑄻𑄼𑄽𑄾𑄿",_type:"numeric"},cham:{_digits:"꩐꩑꩒꩓꩔꩕꩖꩗꩘꩙",_type:"numeric"},cyrl:{_rules:"cyrillic-lower",_type:"algorithmic"},deva:{_digits:"०१२३४५६७८९",_type:"numeric"},ethi:{_rules:"ethiopic",_type:"algorithmic"},fullwide:{_digits:"０１２３４５６７８９",_type:"numeric"},geor:{_rules:"georgian",_type:"algorithmic"},gong:{_digits:"𑶠𑶡𑶢𑶣𑶤𑶥𑶦𑶧𑶨𑶩",_type:"numeric"},gonm:{_digits:"𑵐𑵑𑵒𑵓𑵔𑵕𑵖𑵗𑵘𑵙",_type:"numeric"},grek:{_rules:"greek-upper",_type:"algorithmic"},greklow:{_rules:"greek-lower",_type:"algorithmic"},gujr:{_digits:"૦૧૨૩૪૫૬૭૮૯",_type:"numeric"},guru:{_digits:"੦੧੨੩੪੫੬੭੮੯",_type:"numeric"},hanidays:{_rules:"zh/SpelloutRules/spellout-numbering-days",_type:"algorithmic"},hanidec:{_digits:"〇一二三四五六七八九",_type:"numeric"},hans:{_rules:"zh/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hansfin:{_rules:"zh/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hant:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hantfin:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hebr:{_rules:"hebrew",_type:"algorithmic"},hmng:{_digits:"𖭐𖭑𖭒𖭓𖭔𖭕𖭖𖭗𖭘𖭙",_type:"numeric"},hmnp:{_digits:"𞅀𞅁𞅂𞅃𞅄𞅅𞅆𞅇𞅈𞅉",_type:"numeric"},java:{_digits:"꧐꧑꧒꧓꧔꧕꧖꧗꧘꧙",_type:"numeric"},jpan:{_rules:"ja/SpelloutRules/spellout-cardinal",_type:"algorithmic"},jpanfin:{_rules:"ja/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},jpanyear:{_rules:"ja/SpelloutRules/spellout-numbering-year-latn",_type:"algorithmic"},kali:{_digits:"꤀꤁꤂꤃꤄꤅꤆꤇꤈꤉",_type:"numeric"},khmr:{_digits:"០១២៣៤៥៦៧៨៩",_type:"numeric"},knda:{_digits:"೦೧೨೩೪೫೬೭೮೯",_type:"numeric"},lana:{_digits:"᪀᪁᪂᪃᪄᪅᪆᪇᪈᪉",_type:"numeric"},lanatham:{_digits:"᪐᪑᪒᪓᪔᪕᪖᪗᪘᪙",_type:"numeric"},laoo:{_digits:"໐໑໒໓໔໕໖໗໘໙",_type:"numeric"},latn:{_digits:"0123456789",_type:"numeric"},lepc:{_digits:"᱀᱁᱂᱃᱄᱅᱆᱇᱈᱉",_type:"numeric"},limb:{_digits:"᥆᥇᥈᥉᥊᥋᥌᥍᥎᥏",_type:"numeric"},mathbold:{_digits:"𝟎𝟏𝟐𝟑𝟒𝟓𝟔𝟕𝟖𝟗",_type:"numeric"},mathdbl:{_digits:"𝟘𝟙𝟚𝟛𝟜𝟝𝟞𝟟𝟠𝟡",_type:"numeric"},mathmono:{_digits:"𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿",_type:"numeric"},mathsanb:{_digits:"𝟬𝟭𝟮𝟯𝟰𝟱𝟲𝟳𝟴𝟵",_type:"numeric"},mathsans:{_digits:"𝟢𝟣𝟤𝟥𝟦𝟧𝟨𝟩𝟪𝟫",_type:"numeric"},mlym:{_digits:"൦൧൨൩൪൫൬൭൮൯",_type:"numeric"},modi:{_digits:"𑙐𑙑𑙒𑙓𑙔𑙕𑙖𑙗𑙘𑙙",_type:"numeric"},mong:{_digits:"᠐᠑᠒᠓᠔᠕᠖᠗᠘᠙",_type:"numeric"},mroo:{_digits:"𖩠𖩡𖩢𖩣𖩤𖩥𖩦𖩧𖩨𖩩",_type:"numeric"},mtei:{_digits:"꯰꯱꯲꯳꯴꯵꯶꯷꯸꯹",_type:"numeric"},mymr:{_digits:"၀၁၂၃၄၅၆၇၈၉",_type:"numeric"},mymrshan:{_digits:"႐႑႒႓႔႕႖႗႘႙",_type:"numeric"},mymrtlng:{_digits:"꧰꧱꧲꧳꧴꧵꧶꧷꧸꧹",_type:"numeric"},newa:{_digits:"𑑐𑑑𑑒𑑓𑑔𑑕𑑖𑑗𑑘𑑙",_type:"numeric"},nkoo:{_digits:"߀߁߂߃߄߅߆߇߈߉",_type:"numeric"},olck:{_digits:"᱐᱑᱒᱓᱔᱕᱖᱗᱘᱙",_type:"numeric"},orya:{_digits:"୦୧୨୩୪୫୬୭୮୯",_type:"numeric"},osma:{_digits:"𐒠𐒡𐒢𐒣𐒤𐒥𐒦𐒧𐒨𐒩",_type:"numeric"},rohg:{_digits:"𐴰𐴱𐴲𐴳𐴴𐴵𐴶𐴷𐴸𐴹",_type:"numeric"},roman:{_rules:"roman-upper",_type:"algorithmic"},romanlow:{_rules:"roman-lower",_type:"algorithmic"},saur:{_digits:"꣐꣑꣒꣓꣔꣕꣖꣗꣘꣙",_type:"numeric"},shrd:{_digits:"𑇐𑇑𑇒𑇓𑇔𑇕𑇖𑇗𑇘𑇙",_type:"numeric"},sind:{_digits:"𑋰𑋱𑋲𑋳𑋴𑋵𑋶𑋷𑋸𑋹",_type:"numeric"},sinh:{_digits:"෦෧෨෩෪෫෬෭෮෯",_type:"numeric"},sora:{_digits:"𑃰𑃱𑃲𑃳𑃴𑃵𑃶𑃷𑃸𑃹",_type:"numeric"},sund:{_digits:"᮰᮱᮲᮳᮴᮵᮶᮷᮸᮹",_type:"numeric"},takr:{_digits:"𑛀𑛁𑛂𑛃𑛄𑛅𑛆𑛇𑛈𑛉",_type:"numeric"},talu:{_digits:"᧐᧑᧒᧓᧔᧕᧖᧗᧘᧙",_type:"numeric"},taml:{_rules:"tamil",_type:"algorithmic"},tamldec:{_digits:"௦௧௨௩௪௫௬௭௮௯",_type:"numeric"},telu:{_digits:"౦౧౨౩౪౫౬౭౮౯",_type:"numeric"},thai:{_digits:"๐๑๒๓๔๕๖๗๘๙",_type:"numeric"},tibt:{_digits:"༠༡༢༣༤༥༦༧༨༩",_type:"numeric"},tirh:{_digits:"𑓐𑓑𑓒𑓓𑓔𑓕𑓖𑓗𑓘𑓙",_type:"numeric"},vaii:{_digits:"꘠꘡꘢꘣꘤꘥꘦꘧꘨꘩",_type:"numeric"},wara:{_digits:"𑣠𑣡𑣢𑣣𑣤𑣥𑣦𑣧𑣨𑣩",_type:"numeric"},wcho:{_digits:"𞋰𞋱𞋲𞋳𞋴𞋵𞋶𞋷𞋸𞋹",_type:"numeric"}}}},{main:{fr:{identity:{version:{_cldrVersion:"36"},language:"fr"},numbers:{currencies:{ADP:{displayName:"peseta andorrane","displayName-count-one":"peseta andorrane","displayName-count-other":"pesetas andorranes",symbol:"ADP"},AED:{displayName:"dirham des Émirats arabes unis","displayName-count-one":"dirham des Émirats arabes unis","displayName-count-other":"dirhams des Émirats arabes unis",symbol:"AED"},AFA:{displayName:"afghani (1927–2002)","displayName-count-one":"afghani (1927–2002)","displayName-count-other":"afghanis (1927–2002)",symbol:"AFA"},AFN:{displayName:"afghani afghan","displayName-count-one":"afghani afghan","displayName-count-other":"afghanis afghan",symbol:"AFN"},ALK:{displayName:"lek albanais (1947–1961)","displayName-count-one":"lek albanais (1947–1961)","displayName-count-other":"leks albanais (1947–1961)",symbol:"ALK"},ALL:{displayName:"lek albanais","displayName-count-one":"lek albanais","displayName-count-other":"leks albanais",symbol:"ALL"},AMD:{displayName:"dram arménien","displayName-count-one":"dram arménien","displayName-count-other":"drams arméniens",symbol:"AMD"},ANG:{displayName:"florin antillais","displayName-count-one":"florin antillais","displayName-count-other":"florins antillais",symbol:"ANG"},AOA:{displayName:"kwanza angolais","displayName-count-one":"kwanza angolais","displayName-count-other":"kwanzas angolais",symbol:"AOA","symbol-alt-narrow":"Kz"},AOK:{displayName:"kwanza angolais (1977–1990)","displayName-count-one":"kwanza angolais (1977–1990)","displayName-count-other":"kwanzas angolais (1977–1990)",symbol:"AOK"},AON:{displayName:"nouveau kwanza angolais (1990–2000)","displayName-count-one":"nouveau kwanza angolais (1990–2000)","displayName-count-other":"nouveaux kwanzas angolais (1990–2000)",symbol:"AON"},AOR:{displayName:"kwanza angolais réajusté (1995–1999)","displayName-count-one":"kwanza angolais réajusté (1995–1999)","displayName-count-other":"kwanzas angolais réajustés (1995–1999)",symbol:"AOR"},ARA:{displayName:"austral argentin","displayName-count-one":"austral argentin","displayName-count-other":"australs argentins",symbol:"ARA"},ARL:{displayName:"ARL",symbol:"ARL"},ARM:{displayName:"ARM",symbol:"ARM"},ARP:{displayName:"peso argentin (1983–1985)","displayName-count-one":"peso argentin (1983–1985)","displayName-count-other":"pesos argentins (1983–1985)",symbol:"ARP"},ARS:{displayName:"peso argentin","displayName-count-one":"peso argentin","displayName-count-other":"pesos argentins",symbol:"$AR","symbol-alt-narrow":"$"},ATS:{displayName:"schilling autrichien","displayName-count-one":"schilling autrichien","displayName-count-other":"schillings autrichiens",symbol:"ATS"},AUD:{displayName:"dollar australien","displayName-count-one":"dollar australien","displayName-count-other":"dollars australiens",symbol:"$AU","symbol-alt-narrow":"$"},AWG:{displayName:"florin arubais","displayName-count-one":"florin arubais","displayName-count-other":"florins arubais",symbol:"AWG"},AZM:{displayName:"manat azéri (1993–2006)","displayName-count-one":"manat azéri (1993–2006)","displayName-count-other":"manats azéris (1993–2006)",symbol:"AZM"},AZN:{displayName:"manat azéri","displayName-count-one":"manat azéri","displayName-count-other":"manats azéris",symbol:"AZN"},BAD:{displayName:"dinar bosniaque","displayName-count-one":"dinar bosniaque","displayName-count-other":"dinars bosniaques",symbol:"BAD"},BAM:{displayName:"mark convertible bosniaque","displayName-count-one":"mark convertible bosniaque","displayName-count-other":"marks convertibles bosniaques",symbol:"BAM","symbol-alt-narrow":"KM"},BAN:{displayName:"BAN",symbol:"BAN"},BBD:{displayName:"dollar barbadien","displayName-count-one":"dollar barbadien","displayName-count-other":"dollars barbadiens",symbol:"BBD","symbol-alt-narrow":"$"},BDT:{displayName:"taka bangladeshi","displayName-count-one":"taka bangladeshi","displayName-count-other":"takas bangladeshis",symbol:"BDT","symbol-alt-narrow":"৳"},BEC:{displayName:"franc belge (convertible)","displayName-count-one":"franc belge (convertible)","displayName-count-other":"francs belges (convertibles)",symbol:"BEC"},BEF:{displayName:"franc belge","displayName-count-one":"franc belge","displayName-count-other":"francs belges",symbol:"FB"},BEL:{displayName:"franc belge (financier)","displayName-count-one":"franc belge (financier)","displayName-count-other":"francs belges (financiers)",symbol:"BEL"},BGL:{displayName:"lev bulgare (1962–1999)","displayName-count-one":"lev bulgare (1962–1999)","displayName-count-other":"levs bulgares (1962–1999)",symbol:"BGL"},BGM:{displayName:"BGM",symbol:"BGM"},BGN:{displayName:"lev bulgare","displayName-count-one":"lev bulgare","displayName-count-other":"levs bulgares",symbol:"BGN"},BGO:{displayName:"BGO",symbol:"BGO"},BHD:{displayName:"dinar bahreïni","displayName-count-one":"dinar bahreïni","displayName-count-other":"dinars bahreïnis",symbol:"BHD"},BIF:{displayName:"franc burundais","displayName-count-one":"franc burundais","displayName-count-other":"francs burundais",symbol:"BIF"},BMD:{displayName:"dollar bermudien","displayName-count-one":"dollar bermudien","displayName-count-other":"dollars bermudiens",symbol:"$BM","symbol-alt-narrow":"$"},BND:{displayName:"dollar brunéien","displayName-count-one":"dollar brunéien","displayName-count-other":"dollars brunéiens",symbol:"$BN","symbol-alt-narrow":"$"},BOB:{displayName:"boliviano bolivien","displayName-count-one":"boliviano bolivien","displayName-count-other":"bolivianos boliviens",symbol:"BOB","symbol-alt-narrow":"Bs"},BOL:{displayName:"BOL",symbol:"BOL"},BOP:{displayName:"peso bolivien","displayName-count-one":"peso bolivien","displayName-count-other":"pesos boliviens",symbol:"BOP"},BOV:{displayName:"mvdol bolivien","displayName-count-one":"mvdol bolivien","displayName-count-other":"mvdols boliviens",symbol:"BOV"},BRB:{displayName:"nouveau cruzeiro brésilien (1967–1986)","displayName-count-one":"nouveau cruzeiro brésilien (1967–1986)","displayName-count-other":"nouveaux cruzeiros brésiliens (1967–1986)",symbol:"BRB"},BRC:{displayName:"cruzado brésilien (1986–1989)","displayName-count-one":"cruzado brésilien (1986–1989)","displayName-count-other":"cruzados brésiliens (1986–1989)",symbol:"BRC"},BRE:{displayName:"cruzeiro brésilien (1990–1993)","displayName-count-one":"cruzeiro brésilien (1990–1993)","displayName-count-other":"cruzeiros brésiliens (1990–1993)",symbol:"BRE"},BRL:{displayName:"réal brésilien","displayName-count-one":"réal brésilien","displayName-count-other":"réals brésiliens",symbol:"R$","symbol-alt-narrow":"R$"},BRN:{displayName:"nouveau cruzado","displayName-count-one":"nouveau cruzado brésilien (1989–1990)","displayName-count-other":"nouveaux cruzados brésiliens (1989–1990)",symbol:"BRN"},BRR:{displayName:"cruzeiro","displayName-count-one":"cruzeiro réal brésilien (1993–1994)","displayName-count-other":"cruzeiros réals brésiliens (1993–1994)",symbol:"BRR"},BRZ:{displayName:"BRZ",symbol:"BRZ"},BSD:{displayName:"dollar bahaméen","displayName-count-one":"dollar bahaméen","displayName-count-other":"dollars bahaméens",symbol:"BSD","symbol-alt-narrow":"$"},BTN:{displayName:"ngultrum bouthanais","displayName-count-one":"ngultrum bouthanais","displayName-count-other":"ngultrums bouthanais",symbol:"BTN"},BUK:{displayName:"kyat birman","displayName-count-one":"kyat birman","displayName-count-other":"kyats birmans",symbol:"BUK"},BWP:{displayName:"pula botswanais","displayName-count-one":"pula botswanais","displayName-count-other":"pulas botswanais",symbol:"BWP","symbol-alt-narrow":"P"},BYB:{displayName:"nouveau rouble biélorusse (1994–1999)","displayName-count-one":"nouveau rouble biélorusse (1994–1999)","displayName-count-other":"nouveaux roubles biélorusses (1994–1999)",symbol:"BYB"},BYN:{displayName:"rouble biélorusse","displayName-count-one":"rouble biélorusse","displayName-count-other":"roubles biélorusses",symbol:"BYN","symbol-alt-narrow":"р."},BYR:{displayName:"rouble biélorusse (2000–2016)","displayName-count-one":"rouble biélorusse (2000–2016)","displayName-count-other":"roubles biélorusses (2000–2016)",symbol:"BYR"},BZD:{displayName:"dollar bélizéen","displayName-count-one":"dollar bélizéen","displayName-count-other":"dollars bélizéens",symbol:"$BZ","symbol-alt-narrow":"$"},CAD:{displayName:"dollar canadien","displayName-count-one":"dollar canadien","displayName-count-other":"dollars canadiens",symbol:"$CA","symbol-alt-narrow":"$"},CDF:{displayName:"franc congolais","displayName-count-one":"franc congolais","displayName-count-other":"francs congolais",symbol:"CDF"},CHE:{displayName:"euro WIR","displayName-count-one":"euro WIR","displayName-count-other":"euros WIR",symbol:"CHE"},CHF:{displayName:"franc suisse","displayName-count-one":"franc suisse","displayName-count-other":"francs suisses",symbol:"CHF"},CHW:{displayName:"franc WIR","displayName-count-one":"franc WIR","displayName-count-other":"francs WIR",symbol:"CHW"},CLE:{displayName:"CLE",symbol:"CLE"},CLF:{displayName:"unité d’investissement chilienne","displayName-count-one":"unité d’investissement chilienne","displayName-count-other":"unités d’investissement chiliennes",symbol:"CLF"},CLP:{displayName:"peso chilien","displayName-count-one":"peso chilien","displayName-count-other":"pesos chiliens",symbol:"$CL","symbol-alt-narrow":"$"},CNH:{displayName:"yuan chinois (zone extracôtière)","displayName-count-one":"yuan chinois (zone extracôtière)","displayName-count-other":"yuans chinois (zone extracôtière)",symbol:"CNH"},CNX:{displayName:"CNX",symbol:"CNX"},CNY:{displayName:"yuan renminbi chinois","displayName-count-one":"yuan renminbi chinois","displayName-count-other":"yuans renminbi chinois",symbol:"CNY","symbol-alt-narrow":"¥"},COP:{displayName:"peso colombien","displayName-count-one":"peso colombien","displayName-count-other":"pesos colombiens",symbol:"$CO","symbol-alt-narrow":"$"},COU:{displayName:"unité de valeur réelle colombienne","displayName-count-one":"unité de valeur réelle colombienne","displayName-count-other":"unités de valeur réelle colombiennes",symbol:"COU"},CRC:{displayName:"colón costaricain","displayName-count-one":"colón costaricain","displayName-count-other":"colóns costaricains",symbol:"CRC","symbol-alt-narrow":"₡"},CSD:{displayName:"dinar serbo-monténégrin","displayName-count-one":"dinar serbo-monténégrin","displayName-count-other":"dinars serbo-monténégrins",symbol:"CSD"},CSK:{displayName:"couronne forte tchécoslovaque","displayName-count-one":"couronne forte tchécoslovaque","displayName-count-other":"couronnes fortes tchécoslovaques",symbol:"CSK"},CUC:{displayName:"peso cubain convertible","displayName-count-one":"peso cubain convertible","displayName-count-other":"pesos cubains convertibles",symbol:"CUC","symbol-alt-narrow":"$"},CUP:{displayName:"peso cubain","displayName-count-one":"peso cubain","displayName-count-other":"pesos cubains",symbol:"CUP","symbol-alt-narrow":"$"},CVE:{displayName:"escudo capverdien","displayName-count-one":"escudo capverdien","displayName-count-other":"escudos capverdiens",symbol:"CVE"},CYP:{displayName:"livre chypriote","displayName-count-one":"livre chypriote","displayName-count-other":"livres chypriotes",symbol:"£CY"},CZK:{displayName:"couronne tchèque","displayName-count-one":"couronne tchèque","displayName-count-other":"couronnes tchèques",symbol:"CZK","symbol-alt-narrow":"Kč"},DDM:{displayName:"mark est-allemand","displayName-count-one":"mark est-allemand","displayName-count-other":"marks est-allemands",symbol:"DDM"},DEM:{displayName:"mark allemand","displayName-count-one":"mark allemand","displayName-count-other":"marks allemands",symbol:"DEM"},DJF:{displayName:"franc djiboutien","displayName-count-one":"franc djiboutien","displayName-count-other":"francs djiboutiens",symbol:"DJF"},DKK:{displayName:"couronne danoise","displayName-count-one":"couronne danoise","displayName-count-other":"couronnes danoises",symbol:"DKK","symbol-alt-narrow":"kr"},DOP:{displayName:"peso dominicain","displayName-count-one":"peso dominicain","displayName-count-other":"pesos dominicains",symbol:"DOP","symbol-alt-narrow":"$"},DZD:{displayName:"dinar algérien","displayName-count-one":"dinar algérien","displayName-count-other":"dinars algériens",symbol:"DZD"},ECS:{displayName:"sucre équatorien","displayName-count-one":"sucre équatorien","displayName-count-other":"sucres équatoriens",symbol:"ECS"},ECV:{displayName:"unité de valeur constante équatoriale (UVC)","displayName-count-one":"unité de valeur constante équatorienne (UVC)","displayName-count-other":"unités de valeur constante équatoriennes (UVC)",symbol:"ECV"},EEK:{displayName:"couronne estonienne","displayName-count-one":"couronne estonienne","displayName-count-other":"couronnes estoniennes",symbol:"EEK"},EGP:{displayName:"livre égyptienne","displayName-count-one":"livre égyptienne","displayName-count-other":"livres égyptiennes",symbol:"EGP","symbol-alt-narrow":"£E"},ERN:{displayName:"nafka érythréen","displayName-count-one":"nafka érythréen","displayName-count-other":"nafkas érythréens",symbol:"ERN"},ESA:{displayName:"peseta espagnole (compte A)","displayName-count-one":"peseta espagnole (compte A)","displayName-count-other":"pesetas espagnoles (compte A)",symbol:"ESA"},ESB:{displayName:"peseta espagnole (compte convertible)","displayName-count-one":"peseta espagnole (compte convertible)","displayName-count-other":"pesetas espagnoles (compte convertible)",symbol:"ESB"},ESP:{displayName:"peseta espagnole","displayName-count-one":"peseta espagnole","displayName-count-other":"pesetas espagnoles",symbol:"ESP","symbol-alt-narrow":"₧"},ETB:{displayName:"birr éthiopien","displayName-count-one":"birr éthiopien","displayName-count-other":"birrs éthiopiens",symbol:"ETB"},EUR:{displayName:"euro","displayName-count-one":"euro","displayName-count-other":"euros",symbol:"€","symbol-alt-narrow":"€"},FIM:{displayName:"mark finlandais","displayName-count-one":"mark finlandais","displayName-count-other":"marks finlandais",symbol:"FIM"},FJD:{displayName:"dollar fidjien","displayName-count-one":"dollar fidjien","displayName-count-other":"dollars fidjiens",symbol:"$FJ","symbol-alt-narrow":"$"},FKP:{displayName:"livre des îles Malouines","displayName-count-one":"livre des îles Malouines","displayName-count-other":"livres des îles Malouines",symbol:"£FK","symbol-alt-narrow":"£"},FRF:{displayName:"franc français","displayName-count-one":"franc français","displayName-count-other":"francs français",symbol:"F"},GBP:{displayName:"livre sterling","displayName-count-one":"livre sterling","displayName-count-other":"livres sterling",symbol:"£GB","symbol-alt-narrow":"£"},GEK:{displayName:"coupon de lari géorgien","displayName-count-one":"coupon de lari géorgien","displayName-count-other":"coupons de lari géorgiens",symbol:"GEK"},GEL:{displayName:"lari géorgien","displayName-count-one":"lari géorgien","displayName-count-other":"lari géorgiens",symbol:"GEL","symbol-alt-narrow":"₾","symbol-alt-variant":"₾"},GHC:{displayName:"cédi","displayName-count-one":"cédi ghanéen (1967–2007)","displayName-count-other":"cédis ghanéens (1967–2007)",symbol:"GHC"},GHS:{displayName:"cédi ghanéen","displayName-count-one":"cédi ghanéen","displayName-count-other":"cédis ghanéens",symbol:"GHS"},GIP:{displayName:"livre de Gibraltar","displayName-count-one":"livre de Gibraltar","displayName-count-other":"livres de Gibraltar",symbol:"£GI","symbol-alt-narrow":"£"},GMD:{displayName:"dalasi gambien","displayName-count-one":"dalasi gambien","displayName-count-other":"dalasis gambiens",symbol:"GMD"},GNF:{displayName:"franc guinéen","displayName-count-one":"franc guinéen","displayName-count-other":"francs guinéens",symbol:"GNF","symbol-alt-narrow":"FG"},GNS:{displayName:"syli guinéen","displayName-count-one":"syli guinéen","displayName-count-other":"sylis guinéens",symbol:"GNS"},GQE:{displayName:"ekwélé équatoguinéen","displayName-count-one":"ekwélé équatoguinéen","displayName-count-other":"ekwélés équatoguinéens",symbol:"GQE"},GRD:{displayName:"drachme grecque","displayName-count-one":"drachme grecque","displayName-count-other":"drachmes grecques",symbol:"GRD"},GTQ:{displayName:"quetzal guatémaltèque","displayName-count-one":"quetzal guatémaltèque","displayName-count-other":"quetzals guatémaltèques",symbol:"GTQ","symbol-alt-narrow":"Q"},GWE:{displayName:"escudo de Guinée portugaise","displayName-count-one":"escudo de Guinée portugaise","displayName-count-other":"escudos de Guinée portugaise",symbol:"GWE"},GWP:{displayName:"peso bissau-guinéen","displayName-count-one":"peso bissau-guinéen","displayName-count-other":"pesos bissau-guinéens",symbol:"GWP"},GYD:{displayName:"dollar du Guyana","displayName-count-one":"dollar du Guyana","displayName-count-other":"dollars du Guyana",symbol:"GYD","symbol-alt-narrow":"$"},HKD:{displayName:"dollar de Hong Kong","displayName-count-one":"dollar de Hong Kong","displayName-count-other":"dollars de Hong Kong",symbol:"HKD","symbol-alt-narrow":"$"},HNL:{displayName:"lempira hondurien","displayName-count-one":"lempira hondurien","displayName-count-other":"lempiras honduriens",symbol:"HNL","symbol-alt-narrow":"L"},HRD:{displayName:"dinar croate","displayName-count-one":"dinar croate","displayName-count-other":"dinars croates",symbol:"HRD"},HRK:{displayName:"kuna croate","displayName-count-one":"kuna croate","displayName-count-other":"kunas croates",symbol:"HRK","symbol-alt-narrow":"kn"},HTG:{displayName:"gourde haïtienne","displayName-count-one":"gourde haïtienne","displayName-count-other":"gourdes haïtiennes",symbol:"HTG"},HUF:{displayName:"forint hongrois","displayName-count-one":"forint hongrois","displayName-count-other":"forints hongrois",symbol:"HUF","symbol-alt-narrow":"Ft"},IDR:{displayName:"roupie indonésienne","displayName-count-one":"roupie indonésienne","displayName-count-other":"roupies indonésiennes",symbol:"IDR","symbol-alt-narrow":"Rp"},IEP:{displayName:"livre irlandaise","displayName-count-one":"livre irlandaise","displayName-count-other":"livres irlandaises",symbol:"£IE"},ILP:{displayName:"livre israélienne","displayName-count-one":"livre israélienne","displayName-count-other":"livres israéliennes",symbol:"£IL"},ILR:{displayName:"ILR",symbol:"ILR"},ILS:{displayName:"nouveau shekel israélien","displayName-count-one":"nouveau shekel israélien","displayName-count-other":"nouveaux shekels israéliens",symbol:"₪","symbol-alt-narrow":"₪"},INR:{displayName:"roupie indienne","displayName-count-one":"roupie indienne","displayName-count-other":"roupies indiennes",symbol:"₹","symbol-alt-narrow":"₹"},IQD:{displayName:"dinar irakien","displayName-count-one":"dinar irakien","displayName-count-other":"dinars irakiens",symbol:"IQD"},IRR:{displayName:"riyal iranien","displayName-count-one":"riyal iranien","displayName-count-other":"riyals iraniens",symbol:"IRR"},ISJ:{displayName:"ISJ",symbol:"ISJ"},ISK:{displayName:"couronne islandaise","displayName-count-one":"couronne islandaise","displayName-count-other":"couronnes islandaises",symbol:"ISK","symbol-alt-narrow":"kr"},ITL:{displayName:"lire italienne","displayName-count-one":"lire italienne","displayName-count-other":"lires italiennes",symbol:"₤IT"},JMD:{displayName:"dollar jamaïcain","displayName-count-one":"dollar jamaïcain","displayName-count-other":"dollars jamaïcains",symbol:"JMD","symbol-alt-narrow":"$"},JOD:{displayName:"dinar jordanien","displayName-count-one":"dinar jordanien","displayName-count-other":"dinars jordaniens",symbol:"JOD"},JPY:{displayName:"yen japonais","displayName-count-one":"yen japonais","displayName-count-other":"yens japonais",symbol:"JPY","symbol-alt-narrow":"¥"},KES:{displayName:"shilling kényan","displayName-count-one":"shilling kényan","displayName-count-other":"shillings kényans",symbol:"KES"},KGS:{displayName:"som kirghize","displayName-count-one":"som kirghize","displayName-count-other":"soms kirghizes",symbol:"KGS"},KHR:{displayName:"riel cambodgien","displayName-count-one":"riel cambodgien","displayName-count-other":"riels cambodgiens",symbol:"KHR","symbol-alt-narrow":"៛"},KMF:{displayName:"franc comorien","displayName-count-one":"franc comorien","displayName-count-other":"francs comoriens",symbol:"KMF","symbol-alt-narrow":"FC"},KPW:{displayName:"won nord-coréen","displayName-count-one":"won nord-coréen","displayName-count-other":"wons nord-coréens",symbol:"KPW","symbol-alt-narrow":"₩"},KRH:{displayName:"KRH",symbol:"KRH"},KRO:{displayName:"KRO",symbol:"KRO"},KRW:{displayName:"won sud-coréen","displayName-count-one":"won sud-coréen","displayName-count-other":"wons sud-coréens",symbol:"₩","symbol-alt-narrow":"₩"},KWD:{displayName:"dinar koweïtien","displayName-count-one":"dinar koweïtien","displayName-count-other":"dinar koweïtiens",symbol:"KWD"},KYD:{displayName:"dollar des îles Caïmans","displayName-count-one":"dollar des îles Caïmans","displayName-count-other":"dollars des îles Caïmans",symbol:"KYD","symbol-alt-narrow":"$"},KZT:{displayName:"tenge kazakh","displayName-count-one":"tenge kazakh","displayName-count-other":"tenges kazakhs",symbol:"KZT","symbol-alt-narrow":"₸"},LAK:{displayName:"kip loatien","displayName-count-one":"kip loatien","displayName-count-other":"kips loatiens",symbol:"LAK","symbol-alt-narrow":"₭"},LBP:{displayName:"livre libanaise","displayName-count-one":"livre libanaise","displayName-count-other":"livres libanaises",symbol:"£LB","symbol-alt-narrow":"£L"},LKR:{displayName:"roupie srilankaise","displayName-count-one":"roupie srilankaise","displayName-count-other":"roupies srilankaises",symbol:"LKR","symbol-alt-narrow":"Rs"},LRD:{displayName:"dollar libérien","displayName-count-one":"dollar libérien","displayName-count-other":"dollars libériens",symbol:"LRD","symbol-alt-narrow":"$"},LSL:{displayName:"loti lesothan","displayName-count-one":"loti lesothan","displayName-count-other":"maloti lesothans",symbol:"LSL"},LTL:{displayName:"litas lituanien","displayName-count-one":"litas lituanien","displayName-count-other":"litas lituaniens",symbol:"LTL","symbol-alt-narrow":"Lt"},LTT:{displayName:"talonas lituanien","displayName-count-one":"talonas lituanien","displayName-count-other":"talonas lituaniens",symbol:"LTT"},LUC:{displayName:"franc convertible luxembourgeois","displayName-count-one":"franc convertible luxembourgeois","displayName-count-other":"francs convertibles luxembourgeois",symbol:"LUC"},LUF:{displayName:"franc luxembourgeois","displayName-count-one":"franc luxembourgeois","displayName-count-other":"francs luxembourgeois",symbol:"LUF"},LUL:{displayName:"franc financier luxembourgeois","displayName-count-one":"franc financier luxembourgeois","displayName-count-other":"francs financiers luxembourgeois",symbol:"LUL"},LVL:{displayName:"lats letton","displayName-count-one":"lats letton","displayName-count-other":"lats lettons",symbol:"LVL","symbol-alt-narrow":"Ls"},LVR:{displayName:"rouble letton","displayName-count-one":"rouble letton","displayName-count-other":"roubles lettons",symbol:"LVR"},LYD:{displayName:"dinar libyen","displayName-count-one":"dinar libyen","displayName-count-other":"dinars libyens",symbol:"LYD"},MAD:{displayName:"dirham marocain","displayName-count-one":"dirham marocain","displayName-count-other":"dirhams marocains",symbol:"MAD"},MAF:{displayName:"franc marocain","displayName-count-one":"franc marocain","displayName-count-other":"francs marocains",symbol:"MAF"},MCF:{displayName:"MCF",symbol:"MCF"},MDC:{displayName:"MDC",symbol:"MDC"},MDL:{displayName:"leu moldave","displayName-count-one":"leu moldave","displayName-count-other":"leus moldaves",symbol:"MDL"},MGA:{displayName:"ariary malgache","displayName-count-one":"ariary malgache","displayName-count-other":"ariarys malgaches",symbol:"MGA","symbol-alt-narrow":"Ar"},MGF:{displayName:"franc malgache","displayName-count-one":"franc malgache","displayName-count-other":"francs malgaches",symbol:"MGF"},MKD:{displayName:"denar macédonien","displayName-count-one":"denar macédonien","displayName-count-other":"denars macédoniens",symbol:"MKD"},MKN:{displayName:"MKN",symbol:"MKN"},MLF:{displayName:"franc malien","displayName-count-one":"franc malien","displayName-count-other":"francs maliens",symbol:"MLF"},MMK:{displayName:"kyat myanmarais","displayName-count-one":"kyat myanmarais","displayName-count-other":"kyats myanmarais",symbol:"MMK","symbol-alt-narrow":"K"},MNT:{displayName:"tugrik mongol","displayName-count-one":"tugrik mongol","displayName-count-other":"tugriks mongols",symbol:"MNT","symbol-alt-narrow":"₮"},MOP:{displayName:"pataca macanaise","displayName-count-one":"pataca macanaise","displayName-count-other":"patacas macanaises",symbol:"MOP"},MRO:{displayName:"ouguiya mauritanien (1973–2017)","displayName-count-one":"ouguiya mauritanien (1973–2017)","displayName-count-other":"ouguiyas mauritaniens (1973–2017)",symbol:"MRO"},MRU:{displayName:"ouguiya mauritanien","displayName-count-one":"ouguiya mauritanien","displayName-count-other":"ouguiyas mauritaniens",symbol:"MRU"},MTL:{displayName:"lire maltaise","displayName-count-one":"lire maltaise","displayName-count-other":"lires maltaises",symbol:"MTL"},MTP:{displayName:"livre maltaise","displayName-count-one":"livre maltaise","displayName-count-other":"livres maltaises",symbol:"£MT"},MUR:{displayName:"roupie mauricienne","displayName-count-one":"roupie mauricienne","displayName-count-other":"roupies mauriciennes",symbol:"MUR","symbol-alt-narrow":"Rs"},MVP:{displayName:"MVP",symbol:"MVP"},MVR:{displayName:"rufiyaa maldivien","displayName-count-one":"rufiyaa maldivienne","displayName-count-other":"rufiyaas maldiviennes",symbol:"MVR"},MWK:{displayName:"kwacha malawite","displayName-count-one":"kwacha malawite","displayName-count-other":"kwachas malawites",symbol:"MWK"},MXN:{displayName:"peso mexicain","displayName-count-one":"peso mexicain","displayName-count-other":"pesos mexicains",symbol:"$MX","symbol-alt-narrow":"$"},MXP:{displayName:"peso d’argent mexicain (1861–1992)","displayName-count-one":"peso d’argent mexicain (1861–1992)","displayName-count-other":"pesos d’argent mexicains (1861–1992)",symbol:"MXP"},MXV:{displayName:"unité de conversion mexicaine (UDI)","displayName-count-one":"unité de conversion mexicaine (UDI)","displayName-count-other":"unités de conversion mexicaines (UDI)",symbol:"MXV"},MYR:{displayName:"ringgit malais","displayName-count-one":"ringgit malais","displayName-count-other":"ringgits malais",symbol:"MYR","symbol-alt-narrow":"RM"},MZE:{displayName:"escudo mozambicain","displayName-count-one":"escudo mozambicain","displayName-count-other":"escudos mozambicains",symbol:"MZE"},MZM:{displayName:"métical","displayName-count-one":"metical mozambicain (1980–2006)","displayName-count-other":"meticais mozambicains (1980–2006)",symbol:"MZM"},MZN:{displayName:"metical mozambicain","displayName-count-one":"metical mozambicain","displayName-count-other":"meticais mozambicains",symbol:"MZN"},NAD:{displayName:"dollar namibien","displayName-count-one":"dollar namibien","displayName-count-other":"dollars namibiens",symbol:"$NA","symbol-alt-narrow":"$"},NGN:{displayName:"naira nigérian","displayName-count-one":"naira nigérian","displayName-count-other":"nairas nigérians",symbol:"NGN","symbol-alt-narrow":"₦"},NIC:{displayName:"cordoba","displayName-count-one":"córdoba nicaraguayen (1912–1988)","displayName-count-other":"córdobas nicaraguayens (1912–1988)",symbol:"NIC"},NIO:{displayName:"córdoba oro nicaraguayen","displayName-count-one":"córdoba oro nicaraguayen","displayName-count-other":"córdobas oro nicaraguayens",symbol:"NIO","symbol-alt-narrow":"$C"},NLG:{displayName:"florin néerlandais","displayName-count-one":"florin néerlandais","displayName-count-other":"florins néerlandais",symbol:"NLG"},NOK:{displayName:"couronne norvégienne","displayName-count-one":"couronne norvégienne","displayName-count-other":"couronnes norvégiennes",symbol:"NOK","symbol-alt-narrow":"kr"},NPR:{displayName:"roupie népalaise","displayName-count-one":"roupie népalaise","displayName-count-other":"roupies népalaises",symbol:"NPR","symbol-alt-narrow":"Rs"},NZD:{displayName:"dollar néo-zélandais","displayName-count-one":"dollar néo-zélandais","displayName-count-other":"dollars néo-zélandais",symbol:"$NZ","symbol-alt-narrow":"$"},OMR:{displayName:"riyal omanais","displayName-count-one":"riyal omanais","displayName-count-other":"riyals omanis",symbol:"OMR"},PAB:{displayName:"balboa panaméen","displayName-count-one":"balboa panaméen","displayName-count-other":"balboas panaméens",symbol:"PAB"},PEI:{displayName:"inti péruvien","displayName-count-one":"inti péruvien","displayName-count-other":"intis péruviens",symbol:"PEI"},PEN:{displayName:"sol péruvien","displayName-count-one":"sol péruvien","displayName-count-other":"sols péruviens",symbol:"PEN"},PES:{displayName:"sol péruvien (1863–1985)","displayName-count-one":"sol péruvien (1863–1985)","displayName-count-other":"sols péruviens (1863–1985)",symbol:"PES"},PGK:{displayName:"kina papouan-néo-guinéen","displayName-count-one":"kina papouan-néo-guinéen","displayName-count-other":"kinas papouan-néo-guinéens",symbol:"PGK"},PHP:{displayName:"peso philippin","displayName-count-one":"peso philippin","displayName-count-other":"pesos philippins",symbol:"PHP","symbol-alt-narrow":"₱"},PKR:{displayName:"roupie pakistanaise","displayName-count-one":"roupie pakistanaise","displayName-count-other":"roupies pakistanaises",symbol:"PKR","symbol-alt-narrow":"Rs"},PLN:{displayName:"zloty polonais","displayName-count-one":"zloty polonais","displayName-count-other":"zlotys polonais",symbol:"PLN","symbol-alt-narrow":"zł"},PLZ:{displayName:"zloty (1950–1995)","displayName-count-one":"zloty polonais (1950–1995)","displayName-count-other":"zlotys polonais (1950–1995)",symbol:"PLZ"},PTE:{displayName:"escudo portugais","displayName-count-one":"escudo portugais","displayName-count-other":"escudos portugais",symbol:"PTE"},PYG:{displayName:"guaraní paraguayen","displayName-count-one":"guaraní paraguayen","displayName-count-other":"guaranís paraguayens",symbol:"PYG","symbol-alt-narrow":"₲"},QAR:{displayName:"riyal qatari","displayName-count-one":"riyal qatari","displayName-count-other":"riyals qataris",symbol:"QAR"},RHD:{displayName:"dollar rhodésien","displayName-count-one":"dollar rhodésien","displayName-count-other":"dollars rhodésiens",symbol:"$RH"},ROL:{displayName:"ancien leu roumain","displayName-count-one":"leu roumain (1952–2005)","displayName-count-other":"lei roumains (1952–2005)",symbol:"ROL"},RON:{displayName:"leu roumain","displayName-count-one":"leu roumain","displayName-count-other":"lei roumains",symbol:"RON","symbol-alt-narrow":"L"},RSD:{displayName:"dinar serbe","displayName-count-one":"dinar serbe","displayName-count-other":"dinars serbes",symbol:"RSD"},RUB:{displayName:"rouble russe","displayName-count-one":"rouble russe","displayName-count-other":"roubles russes",symbol:"RUB","symbol-alt-narrow":"₽"},RUR:{displayName:"rouble russe (1991–1998)","displayName-count-one":"rouble russe (1991–1998)","displayName-count-other":"roubles russes (1991–1998)",symbol:"RUR","symbol-alt-narrow":"р."},RWF:{displayName:"franc rwandais","displayName-count-one":"franc rwandais","displayName-count-other":"francs rwandais",symbol:"RWF","symbol-alt-narrow":"FR"},SAR:{displayName:"riyal saoudien","displayName-count-one":"riyal saoudien","displayName-count-other":"riyals saoudiens",symbol:"SAR"},SBD:{displayName:"dollar des îles Salomon","displayName-count-one":"dollar des îles Salomon","displayName-count-other":"dollars des îles Salomon",symbol:"$SB","symbol-alt-narrow":"$"},SCR:{displayName:"roupie des Seychelles","displayName-count-one":"roupie des Seychelles","displayName-count-other":"roupies des Seychelles",symbol:"SCR"},SDD:{displayName:"dinar soudanais","displayName-count-one":"dinar soudanais (1992–2007)","displayName-count-other":"dinars soudanais (1992–2007)",symbol:"SDD"},SDG:{displayName:"livre soudanaise","displayName-count-one":"livre soudanaise","displayName-count-other":"livres soudanaises",symbol:"SDG"},SDP:{displayName:"livre soudanaise (1956–2007)","displayName-count-one":"livre soudanaise (1956–2007)","displayName-count-other":"livres soudanaises (1956–2007)",symbol:"SDP"},SEK:{displayName:"couronne suédoise","displayName-count-one":"couronne suédoise","displayName-count-other":"couronnes suédoises",symbol:"SEK","symbol-alt-narrow":"kr"},SGD:{displayName:"dollar de Singapour","displayName-count-one":"dollar de Singapour","displayName-count-other":"dollars de Singapour",symbol:"$SG","symbol-alt-narrow":"$"},SHP:{displayName:"livre de Sainte-Hélène","displayName-count-one":"livre de Sainte-Hélène","displayName-count-other":"livres de Sainte-Hélène",symbol:"SHP","symbol-alt-narrow":"£"},SIT:{displayName:"tolar slovène","displayName-count-one":"tolar slovène","displayName-count-other":"tolars slovènes",symbol:"SIT"},SKK:{displayName:"couronne slovaque","displayName-count-one":"couronne slovaque","displayName-count-other":"couronnes slovaques",symbol:"SKK"},SLL:{displayName:"leone sierra-léonais","displayName-count-one":"leone sierra-léonais","displayName-count-other":"leones sierra-léonais",symbol:"SLL"},SOS:{displayName:"shilling somalien","displayName-count-one":"shilling somalien","displayName-count-other":"shillings somaliens",symbol:"SOS"},SRD:{displayName:"dollar surinamais","displayName-count-one":"dollar surinamais","displayName-count-other":"dollars surinamais",symbol:"$SR","symbol-alt-narrow":"$"},SRG:{displayName:"florin surinamais","displayName-count-one":"florin surinamais","displayName-count-other":"florins surinamais",symbol:"SRG"},SSP:{displayName:"livre sud-soudanaise","displayName-count-one":"livre sud-soudanaise","displayName-count-other":"livres sud-soudanaises",symbol:"SSP","symbol-alt-narrow":"£"},STD:{displayName:"dobra santoméen (1977–2017)","displayName-count-one":"dobra santoméen (1977–2017)","displayName-count-other":"dobras santoméens (1977–2017)",symbol:"STD"},STN:{displayName:"dobra santoméen","displayName-count-one":"dobra santoméen","displayName-count-other":"dobras santoméens",symbol:"STN","symbol-alt-narrow":"Db"},SUR:{displayName:"rouble soviétique","displayName-count-one":"rouble soviétique","displayName-count-other":"roubles soviétiques",symbol:"SUR"},SVC:{displayName:"colón salvadorien","displayName-count-one":"colón salvadorien","displayName-count-other":"colóns salvadoriens",symbol:"SVC"},SYP:{displayName:"livre syrienne","displayName-count-one":"livre syrienne","displayName-count-other":"livres syriennes",symbol:"SYP","symbol-alt-narrow":"£"},SZL:{displayName:"lilangeni swazi","displayName-count-one":"lilangeni swazi","displayName-count-other":"lilangenis swazis",symbol:"SZL"},THB:{displayName:"baht thaïlandais","displayName-count-one":"baht thaïlandais","displayName-count-other":"bahts thaïlandais",symbol:"THB","symbol-alt-narrow":"฿"},TJR:{displayName:"rouble tadjik","displayName-count-one":"rouble tadjik","displayName-count-other":"roubles tadjiks",symbol:"TJR"},TJS:{displayName:"somoni tadjik","displayName-count-one":"somoni tadjik","displayName-count-other":"somonis tadjiks",symbol:"TJS"},TMM:{displayName:"manat turkmène","displayName-count-one":"manat turkmène","displayName-count-other":"manats turkmènes",symbol:"TMM"},TMT:{displayName:"nouveau manat turkmène","displayName-count-one":"nouveau manat turkmène","displayName-count-other":"nouveaux manats turkmènes",symbol:"TMT"},TND:{displayName:"dinar tunisien","displayName-count-one":"dinar tunisien","displayName-count-other":"dinars tunisiens",symbol:"TND"},TOP:{displayName:"pa’anga tongan","displayName-count-one":"pa’anga tongan","displayName-count-other":"pa’angas tongans",symbol:"TOP","symbol-alt-narrow":"$T"},TPE:{displayName:"escudo timorais","displayName-count-one":"escudo timorais","displayName-count-other":"escudos timorais",symbol:"TPE"},TRL:{displayName:"livre turque (1844–2005)","displayName-count-one":"livre turque (1844–2005)","displayName-count-other":"livres turques (1844–2005)",symbol:"TRL"},TRY:{displayName:"livre turque","displayName-count-one":"livre turque","displayName-count-other":"livres turques",symbol:"TRY","symbol-alt-narrow":"₺","symbol-alt-variant":"LT"},TTD:{displayName:"dollar trinidadien","displayName-count-one":"dollar de Trinité-et-Tobago","displayName-count-other":"dollars de Trinité-et-Tobago",symbol:"$TT","symbol-alt-narrow":"$"},TWD:{displayName:"nouveau dollar taïwanais","displayName-count-one":"nouveau dollar taïwanais","displayName-count-other":"nouveaux dollars taïwanais",symbol:"TWD","symbol-alt-narrow":"NT$"},TZS:{displayName:"shilling tanzanien","displayName-count-one":"shilling tanzanien","displayName-count-other":"shillings tanzaniens",symbol:"TZS"},UAH:{displayName:"hryvnia ukrainienne","displayName-count-one":"hryvnia ukrainienne","displayName-count-other":"hryvnias ukrainiennes",symbol:"UAH","symbol-alt-narrow":"₴"},UAK:{displayName:"karbovanetz","displayName-count-one":"karbovanets ukrainien (1992–1996)","displayName-count-other":"karbovanets ukrainiens (1992–1996)",symbol:"UAK"},UGS:{displayName:"shilling ougandais (1966–1987)","displayName-count-one":"shilling ougandais (1966–1987)","displayName-count-other":"shillings ougandais (1966–1987)",symbol:"UGS"},UGX:{displayName:"shilling ougandais","displayName-count-one":"shilling ougandais","displayName-count-other":"shillings ougandais",symbol:"UGX"},USD:{displayName:"dollar des États-Unis","displayName-count-one":"dollar des États-Unis","displayName-count-other":"dollars des États-Unis",symbol:"$US","symbol-alt-narrow":"$"},USN:{displayName:"dollar des Etats-Unis (jour suivant)","displayName-count-one":"dollar des États-Unis (jour suivant)","displayName-count-other":"dollars des États-Unis (jour suivant)",symbol:"USN"},USS:{displayName:"dollar des Etats-Unis (jour même)","displayName-count-one":"dollar des États-Unis (jour même)","displayName-count-other":"dollars des États-Unis (jour même)",symbol:"USS"},UYI:{displayName:"peso uruguayen (unités indexées)","displayName-count-one":"peso uruguayen (unités indexées)","displayName-count-other":"pesos uruguayen (unités indexées)",symbol:"UYI"},UYP:{displayName:"peso uruguayen (1975–1993)","displayName-count-one":"peso uruguayen (1975–1993)","displayName-count-other":"pesos uruguayens (1975–1993)",symbol:"UYP"},UYU:{displayName:"peso uruguayen","displayName-count-one":"peso uruguayen","displayName-count-other":"pesos uruguayens",symbol:"$UY","symbol-alt-narrow":"$"},UYW:{displayName:"UYW",symbol:"UYW"},UZS:{displayName:"sum ouzbek","displayName-count-one":"sum ouzbek","displayName-count-other":"sums ouzbeks",symbol:"UZS"},VEB:{displayName:"bolivar vénézuélien (1871–2008)","displayName-count-one":"bolivar vénézuélien (1871–2008)","displayName-count-other":"bolivar vénézuélien (1871–2008)",symbol:"VEB"},VEF:{displayName:"bolivar vénézuélien (2008–2018)","displayName-count-one":"bolivar vénézuélien (2008–2018)","displayName-count-other":"bolivars vénézuéliens (2008–2018)",symbol:"VEF","symbol-alt-narrow":"Bs"},VES:{displayName:"bolivar vénézuélien","displayName-count-one":"bolivar vénézuélien","displayName-count-other":"bolivars vénézuéliens",symbol:"VES"},VND:{displayName:"dông vietnamien","displayName-count-one":"dông vietnamien","displayName-count-other":"dôngs vietnamiens",symbol:"₫","symbol-alt-narrow":"₫"},VNN:{displayName:"VNN",symbol:"VNN"},VUV:{displayName:"vatu vanuatuan","displayName-count-one":"vatu vanuatuan","displayName-count-other":"vatus vanuatuans",symbol:"VUV"},WST:{displayName:"tala samoan","displayName-count-one":"tala samoan","displayName-count-other":"talas samoans",symbol:"$WS"},XAF:{displayName:"franc CFA (BEAC)","displayName-count-one":"franc CFA (BEAC)","displayName-count-other":"francs CFA (BEAC)",symbol:"FCFA"},XAG:{displayName:"argent","displayName-count-one":"once troy d’argent","displayName-count-other":"onces troy d’argent",symbol:"XAG"},XAU:{displayName:"or","displayName-count-one":"once troy d’or","displayName-count-other":"onces troy d’or",symbol:"XAU"},XBA:{displayName:"unité européenne composée","displayName-count-one":"unité composée européenne (EURCO)","displayName-count-other":"unités composées européennes (EURCO)",symbol:"XBA"},XBB:{displayName:"unité monétaire européenne","displayName-count-one":"unité monétaire européenne (UME–6)","displayName-count-other":"unités monétaires européennes (UME–6)",symbol:"XBB"},XBC:{displayName:"unité de compte européenne (XBC)","displayName-count-one":"unité de compte 9 européenne (UEC–9)","displayName-count-other":"unités de compte 9 européennes (UEC–9)",symbol:"XBC"},XBD:{displayName:"unité de compte européenne (XBD)","displayName-count-one":"unité de compte 17 européenne (UEC–17)","displayName-count-other":"unités de compte 17 européennes (UEC–17)",symbol:"XBD"},XCD:{displayName:"dollar des Caraïbes orientales","displayName-count-one":"dollar des Caraïbes orientales","displayName-count-other":"dollars des Caraïbes orientales",symbol:"XCD","symbol-alt-narrow":"$"},XDR:{displayName:"droit de tirage spécial","displayName-count-one":"droit de tirage spécial","displayName-count-other":"droits de tirage spéciaux",symbol:"XDR"},XEU:{displayName:"unité de compte européenne (ECU)",symbol:"XEU"},XFO:{displayName:"franc or","displayName-count-one":"franc or","displayName-count-other":"francs or",symbol:"XFO"},XFU:{displayName:"franc UIC","displayName-count-one":"franc UIC","displayName-count-other":"francs UIC",symbol:"XFU"},XOF:{displayName:"franc CFA (BCEAO)","displayName-count-one":"franc CFA (BCEAO)","displayName-count-other":"francs CFA (BCEAO)",symbol:"CFA"},XPD:{displayName:"palladium","displayName-count-one":"once troy de palladium","displayName-count-other":"onces troy de palladium",symbol:"XPD"},XPF:{displayName:"franc CFP","displayName-count-one":"franc CFP","displayName-count-other":"francs CFP",symbol:"FCFP"},XPT:{displayName:"platine","displayName-count-one":"once troy de platine","displayName-count-other":"onces troy de platine",symbol:"XPT"},XRE:{displayName:"type de fonds RINET","displayName-count-one":"unité de fonds RINET","displayName-count-other":"unités de fonds RINET",symbol:"XRE"},XSU:{displayName:"XSU",symbol:"XSU"},XTS:{displayName:"(devise de test)","displayName-count-one":"(devise de test)","displayName-count-other":"(devises de test)",symbol:"XTS"},XUA:{displayName:"XUA",symbol:"XUA"},XXX:{displayName:"devise inconnue ou non valide","displayName-count-one":"devise inconnue","displayName-count-other":"devises inconnues",symbol:"¤"},YDD:{displayName:"dinar du Yémen","displayName-count-one":"dinar nord-yéménite","displayName-count-other":"dinars nord-yéménites",symbol:"YDD"},YER:{displayName:"riyal yéménite","displayName-count-one":"riyal yéménite","displayName-count-other":"riyals yéménites",symbol:"YER"},YUD:{displayName:"nouveau dinar yougoslave","displayName-count-one":"dinar fort yougoslave (1966–1989)","displayName-count-other":"dinars forts yougoslaves (1966–1989)",symbol:"YUD"},YUM:{displayName:"dinar yougoslave Noviy","displayName-count-one":"nouveau dinar yougoslave (1994–2003)","displayName-count-other":"nouveaux dinars yougoslaves (1994–2003)",symbol:"YUM"},YUN:{displayName:"dinar yougoslave convertible","displayName-count-one":"dinar convertible yougoslave (1990–1992)","displayName-count-other":"dinars convertibles yougoslaves (1990–1992)",symbol:"YUN"},YUR:{displayName:"YUR",symbol:"YUR"},ZAL:{displayName:"rand sud-africain (financier)","displayName-count-one":"rand sud-africain (financier)","displayName-count-other":"rands sud-africains (financiers)",symbol:"ZAL"},ZAR:{displayName:"rand sud-africain","displayName-count-one":"rand sud-africain","displayName-count-other":"rands sud-africains",symbol:"ZAR","symbol-alt-narrow":"R"},ZMK:{displayName:"kwacha zambien (1968–2012)","displayName-count-one":"kwacha zambien (1968–2012)","displayName-count-other":"kwachas zambiens (1968–2012)",symbol:"ZMK"},ZMW:{displayName:"kwacha zambien","displayName-count-one":"kwacha zambien","displayName-count-other":"kwachas zambiens",symbol:"ZMW","symbol-alt-narrow":"Kw"},ZRN:{displayName:"nouveau zaïre zaïrien","displayName-count-one":"nouveau zaïre zaïrien","displayName-count-other":"nouveaux zaïres zaïriens",symbol:"ZRN"},ZRZ:{displayName:"zaïre zaïrois","displayName-count-one":"zaïre zaïrois","displayName-count-other":"zaïres zaïrois",symbol:"ZRZ"},ZWD:{displayName:"dollar zimbabwéen","displayName-count-one":"dollar zimbabwéen","displayName-count-other":"dollars zimbabwéens",symbol:"ZWD"},ZWL:{displayName:"dollar zimbabwéen (2009)","displayName-count-one":"dollar zimbabwéen (2009)","displayName-count-other":"dollars zimbabwéens (2009)",symbol:"ZWL"},ZWR:{displayName:"dollar zimbabwéen (2008)","displayName-count-one":"dollar zimbabwéen (2008)","displayName-count-other":"dollars zimbabwéens (2008)",symbol:"ZWR"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},currencyData:{fractions:{ADP:{_rounding:"0",_digits:"0"},AFN:{_rounding:"0",_digits:"0"},ALL:{_rounding:"0",_digits:"0"},AMD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},BHD:{_rounding:"0",_digits:"3"},BIF:{_rounding:"0",_digits:"0"},BYN:{_rounding:"0",_digits:"2"},BYR:{_rounding:"0",_digits:"0"},CAD:{_rounding:"0",_digits:"2",_cashRounding:"5"},CHF:{_rounding:"0",_digits:"2",_cashRounding:"5"},CLF:{_rounding:"0",_digits:"4"},CLP:{_rounding:"0",_digits:"0"},COP:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CRC:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CZK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},DEFAULT:{_rounding:"0",_digits:"2"},DJF:{_rounding:"0",_digits:"0"},DKK:{_rounding:"0",_digits:"2",_cashRounding:"50"},ESP:{_rounding:"0",_digits:"0"},GNF:{_rounding:"0",_digits:"0"},GYD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},HUF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IDR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IQD:{_rounding:"0",_digits:"0"},IRR:{_rounding:"0",_digits:"0"},ISK:{_rounding:"0",_digits:"0"},ITL:{_rounding:"0",_digits:"0"},JOD:{_rounding:"0",_digits:"3"},JPY:{_rounding:"0",_digits:"0"},KMF:{_rounding:"0",_digits:"0"},KPW:{_rounding:"0",_digits:"0"},KRW:{_rounding:"0",_digits:"0"},KWD:{_rounding:"0",_digits:"3"},LAK:{_rounding:"0",_digits:"0"},LBP:{_rounding:"0",_digits:"0"},LUF:{_rounding:"0",_digits:"0"},LYD:{_rounding:"0",_digits:"3"},MGA:{_rounding:"0",_digits:"0"},MGF:{_rounding:"0",_digits:"0"},MMK:{_rounding:"0",_digits:"0"},MNT:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},MRO:{_rounding:"0",_digits:"0"},MUR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},NOK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},OMR:{_rounding:"0",_digits:"3"},PKR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},PYG:{_rounding:"0",_digits:"0"},RSD:{_rounding:"0",_digits:"0"},RWF:{_rounding:"0",_digits:"0"},SEK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},SLL:{_rounding:"0",_digits:"0"},SOS:{_rounding:"0",_digits:"0"},STD:{_rounding:"0",_digits:"0"},SYP:{_rounding:"0",_digits:"0"},TMM:{_rounding:"0",_digits:"0"},TND:{_rounding:"0",_digits:"3"},TRL:{_rounding:"0",_digits:"0"},TWD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},TZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},UGX:{_rounding:"0",_digits:"0"},UYI:{_rounding:"0",_digits:"0"},UYW:{_rounding:"0",_digits:"4"},UZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VEF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VND:{_rounding:"0",_digits:"0"},VUV:{_rounding:"0",_digits:"0"},XAF:{_rounding:"0",_digits:"0"},XOF:{_rounding:"0",_digits:"0"},XPF:{_rounding:"0",_digits:"0"},YER:{_rounding:"0",_digits:"0"},ZMK:{_rounding:"0",_digits:"0"},ZWD:{_rounding:"0",_digits:"0"}},region:{AC:[{SHP:{_from:"1976-01-01"}}],AD:[{ESP:{_from:"1873-01-01",_to:"2002-02-28"}},{ADP:{_from:"1936-01-01",_to:"2001-12-31"}},{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],AE:[{AED:{_from:"1973-05-19"}}],AF:[{AFA:{_from:"1927-03-14",_to:"2002-12-31"}},{AFN:{_from:"2002-10-07"}}],AG:[{XCD:{_from:"1965-10-06"}}],AI:[{XCD:{_from:"1965-10-06"}}],AL:[{ALK:{_from:"1946-11-01",_to:"1965-08-16"}},{ALL:{_from:"1965-08-16"}}],AM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-22"}},{AMD:{_from:"1993-11-22"}}],AO:[{AOK:{_from:"1977-01-08",_to:"1991-03-01"}},{AON:{_from:"1990-09-25",_to:"2000-02-01"}},{AOR:{_from:"1995-07-01",_to:"2000-02-01"}},{AOA:{_from:"1999-12-13"}}],AQ:[{XXX:{_tender:"false"}}],AR:[{ARM:{_from:"1881-11-05",_to:"1970-01-01"}},{ARL:{_from:"1970-01-01",_to:"1983-06-01"}},{ARP:{_from:"1983-06-01",_to:"1985-06-14"}},{ARA:{_from:"1985-06-14",_to:"1992-01-01"}},{ARS:{_from:"1992-01-01"}}],AS:[{USD:{_from:"1904-07-16"}}],AT:[{ATS:{_from:"1947-12-04",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],AU:[{AUD:{_from:"1966-02-14"}}],AW:[{ANG:{_from:"1940-05-10",_to:"1986-01-01"}},{AWG:{_from:"1986-01-01"}}],AX:[{EUR:{_from:"1999-01-01"}}],AZ:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-01-01"}},{AZM:{_from:"1993-11-22",_to:"2006-12-31"}},{AZN:{_from:"2006-01-01"}}],BA:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-01"}},{YUR:{_from:"1992-07-01",_to:"1993-10-01"}},{BAD:{_from:"1992-07-01",_to:"1994-08-15"}},{BAN:{_from:"1994-08-15",_to:"1997-07-01"}},{BAM:{_from:"1995-01-01"}}],BB:[{XCD:{_from:"1965-10-06",_to:"1973-12-03"}},{BBD:{_from:"1973-12-03"}}],BD:[{INR:{_from:"1835-08-17",_to:"1948-04-01"}},{PKR:{_from:"1948-04-01",_to:"1972-01-01"}},{BDT:{_from:"1972-01-01"}}],BE:[{NLG:{_from:"1816-12-15",_to:"1831-02-07"}},{BEF:{_from:"1831-02-07",_to:"2002-02-28"}},{BEC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{BEL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],BF:[{XOF:{_from:"1984-08-04"}}],BG:[{BGO:{_from:"1879-07-08",_to:"1952-05-12"}},{BGM:{_from:"1952-05-12",_to:"1962-01-01"}},{BGL:{_from:"1962-01-01",_to:"1999-07-05"}},{BGN:{_from:"1999-07-05"}}],BH:[{BHD:{_from:"1965-10-16"}}],BI:[{BIF:{_from:"1964-05-19"}}],BJ:[{XOF:{_from:"1975-11-30"}}],BL:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],BM:[{BMD:{_from:"1970-02-06"}}],BN:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{BND:{_from:"1967-06-12"}}],BO:[{BOV:{_tender:"false"}},{BOL:{_from:"1863-06-23",_to:"1963-01-01"}},{BOP:{_from:"1963-01-01",_to:"1986-12-31"}},{BOB:{_from:"1987-01-01"}}],BQ:[{ANG:{_from:"2010-10-10",_to:"2011-01-01"}},{USD:{_from:"2011-01-01"}}],BR:[{BRZ:{_from:"1942-11-01",_to:"1967-02-13"}},{BRB:{_from:"1967-02-13",_to:"1986-02-28"}},{BRC:{_from:"1986-02-28",_to:"1989-01-15"}},{BRN:{_from:"1989-01-15",_to:"1990-03-16"}},{BRE:{_from:"1990-03-16",_to:"1993-08-01"}},{BRR:{_from:"1993-08-01",_to:"1994-07-01"}},{BRL:{_from:"1994-07-01"}}],BS:[{BSD:{_from:"1966-05-25"}}],BT:[{INR:{_from:"1907-01-01"}},{BTN:{_from:"1974-04-16"}}],BU:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}}],BV:[{NOK:{_from:"1905-06-07"}}],BW:[{ZAR:{_from:"1961-02-14",_to:"1976-08-23"}},{BWP:{_from:"1976-08-23"}}],BY:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-11-08"}},{BYB:{_from:"1994-08-01",_to:"2000-12-31"}},{BYR:{_from:"2000-01-01",_to:"2017-01-01"}},{BYN:{_from:"2016-07-01"}}],BZ:[{BZD:{_from:"1974-01-01"}}],CA:[{CAD:{_from:"1858-01-01"}}],CC:[{AUD:{_from:"1966-02-14"}}],CD:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-01"}},{CDF:{_from:"1998-07-01"}}],CF:[{XAF:{_from:"1993-01-01"}}],CG:[{XAF:{_from:"1993-01-01"}}],CH:[{CHE:{_tender:"false"}},{CHW:{_tender:"false"}},{CHF:{_from:"1799-03-17"}}],CI:[{XOF:{_from:"1958-12-04"}}],CK:[{NZD:{_from:"1967-07-10"}}],CL:[{CLF:{_tender:"false"}},{CLE:{_from:"1960-01-01",_to:"1975-09-29"}},{CLP:{_from:"1975-09-29"}}],CM:[{XAF:{_from:"1973-04-01"}}],CN:[{CNY:{_from:"1953-03-01"}},{CNX:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{CNH:{_tender:"false",_from:"2010-07-19"}}],CO:[{COU:{_tender:"false"}},{COP:{_from:"1905-01-01"}}],CP:[{XXX:{_tender:"false"}}],CR:[{CRC:{_from:"1896-10-26"}}],CS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-06-03"}},{EUR:{_from:"2003-02-04",_to:"2006-06-03"}}],CU:[{CUP:{_from:"1859-01-01"}},{USD:{_from:"1899-01-01",_to:"1959-01-01"}},{CUC:{_from:"1994-01-01"}}],CV:[{PTE:{_from:"1911-05-22",_to:"1975-07-05"}},{CVE:{_from:"1914-01-01"}}],CW:[{ANG:{_from:"2010-10-10"}}],CX:[{AUD:{_from:"1966-02-14"}}],CY:[{CYP:{_from:"1914-09-10",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],CZ:[{CSK:{_from:"1953-06-01",_to:"1993-03-01"}},{CZK:{_from:"1993-01-01"}}],DD:[{DDM:{_from:"1948-07-20",_to:"1990-10-02"}}],DE:[{DEM:{_from:"1948-06-20",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],DG:[{USD:{_from:"1965-11-08"}}],DJ:[{DJF:{_from:"1977-06-27"}}],DK:[{DKK:{_from:"1873-05-27"}}],DM:[{XCD:{_from:"1965-10-06"}}],DO:[{USD:{_from:"1905-06-21",_to:"1947-10-01"}},{DOP:{_from:"1947-10-01"}}],DZ:[{DZD:{_from:"1964-04-01"}}],EA:[{EUR:{_from:"1999-01-01"}}],EC:[{ECS:{_from:"1884-04-01",_to:"2000-10-02"}},{ECV:{_tender:"false",_from:"1993-05-23",_to:"2000-01-09"}},{USD:{_from:"2000-10-02"}}],EE:[{SUR:{_from:"1961-01-01",_to:"1992-06-20"}},{EEK:{_from:"1992-06-21",_to:"2010-12-31"}},{EUR:{_from:"2011-01-01"}}],EG:[{EGP:{_from:"1885-11-14"}}],EH:[{MAD:{_from:"1976-02-26"}}],ER:[{ETB:{_from:"1993-05-24",_to:"1997-11-08"}},{ERN:{_from:"1997-11-08"}}],ES:[{ESP:{_from:"1868-10-19",_to:"2002-02-28"}},{ESB:{_tender:"false",_from:"1975-01-01",_to:"1994-12-31"}},{ESA:{_tender:"false",_from:"1978-01-01",_to:"1981-12-31"}},{EUR:{_from:"1999-01-01"}}],ET:[{ETB:{_from:"1976-09-15"}}],EU:[{XEU:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{EUR:{_from:"1999-01-01"}}],FI:[{FIM:{_from:"1963-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],FJ:[{FJD:{_from:"1969-01-13"}}],FK:[{FKP:{_from:"1901-01-01"}}],FM:[{JPY:{_from:"1914-10-03",_to:"1944-01-01"}},{USD:{_from:"1944-01-01"}}],FO:[{DKK:{_from:"1948-01-01"}}],FR:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GA:[{XAF:{_from:"1993-01-01"}}],GB:[{GBP:{_from:"1694-07-27"}}],GD:[{XCD:{_from:"1967-02-27"}}],GE:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-06-11"}},{GEK:{_from:"1993-04-05",_to:"1995-09-25"}},{GEL:{_from:"1995-09-23"}}],GF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GG:[{GBP:{_from:"1830-01-01"}}],GH:[{GHC:{_from:"1979-03-09",_to:"2007-12-31"}},{GHS:{_from:"2007-07-03"}}],GI:[{GIP:{_from:"1713-01-01"}}],GL:[{DKK:{_from:"1873-05-27"}}],GM:[{GMD:{_from:"1971-07-01"}}],GN:[{GNS:{_from:"1972-10-02",_to:"1986-01-06"}},{GNF:{_from:"1986-01-06"}}],GP:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GQ:[{GQE:{_from:"1975-07-07",_to:"1986-06-01"}},{XAF:{_from:"1993-01-01"}}],GR:[{GRD:{_from:"1954-05-01",_to:"2002-02-28"}},{EUR:{_from:"2001-01-01"}}],GS:[{GBP:{_from:"1908-01-01"}}],GT:[{GTQ:{_from:"1925-05-27"}}],GU:[{USD:{_from:"1944-08-21"}}],GW:[{GWE:{_from:"1914-01-01",_to:"1976-02-28"}},{GWP:{_from:"1976-02-28",_to:"1997-03-31"}},{XOF:{_from:"1997-03-31"}}],GY:[{GYD:{_from:"1966-05-26"}}],HK:[{HKD:{_from:"1895-02-02"}}],HM:[{AUD:{_from:"1967-02-16"}}],HN:[{HNL:{_from:"1926-04-03"}}],HR:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1991-12-23"}},{HRD:{_from:"1991-12-23",_to:"1995-01-01"}},{HRK:{_from:"1994-05-30"}}],HT:[{HTG:{_from:"1872-08-26"}},{USD:{_from:"1915-01-01"}}],HU:[{HUF:{_from:"1946-07-23"}}],IC:[{EUR:{_from:"1999-01-01"}}],ID:[{IDR:{_from:"1965-12-13"}}],IE:[{GBP:{_from:"1800-01-01",_to:"1922-01-01"}},{IEP:{_from:"1922-01-01",_to:"2002-02-09"}},{EUR:{_from:"1999-01-01"}}],IL:[{ILP:{_from:"1948-08-16",_to:"1980-02-22"}},{ILR:{_from:"1980-02-22",_to:"1985-09-04"}},{ILS:{_from:"1985-09-04"}}],IM:[{GBP:{_from:"1840-01-03"}}],IN:[{INR:{_from:"1835-08-17"}}],IO:[{USD:{_from:"1965-11-08"}}],IQ:[{EGP:{_from:"1920-11-11",_to:"1931-04-19"}},{INR:{_from:"1920-11-11",_to:"1931-04-19"}},{IQD:{_from:"1931-04-19"}}],IR:[{IRR:{_from:"1932-05-13"}}],IS:[{DKK:{_from:"1873-05-27",_to:"1918-12-01"}},{ISJ:{_from:"1918-12-01",_to:"1981-01-01"}},{ISK:{_from:"1981-01-01"}}],IT:[{ITL:{_from:"1862-08-24",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],JE:[{GBP:{_from:"1837-01-01"}}],JM:[{JMD:{_from:"1969-09-08"}}],JO:[{JOD:{_from:"1950-07-01"}}],JP:[{JPY:{_from:"1871-06-01"}}],KE:[{KES:{_from:"1966-09-14"}}],KG:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-05-10"}},{KGS:{_from:"1993-05-10"}}],KH:[{KHR:{_from:"1980-03-20"}}],KI:[{AUD:{_from:"1966-02-14"}}],KM:[{KMF:{_from:"1975-07-06"}}],KN:[{XCD:{_from:"1965-10-06"}}],KP:[{KPW:{_from:"1959-04-17"}}],KR:[{KRO:{_from:"1945-08-15",_to:"1953-02-15"}},{KRH:{_from:"1953-02-15",_to:"1962-06-10"}},{KRW:{_from:"1962-06-10"}}],KW:[{KWD:{_from:"1961-04-01"}}],KY:[{JMD:{_from:"1969-09-08",_to:"1971-01-01"}},{KYD:{_from:"1971-01-01"}}],KZ:[{KZT:{_from:"1993-11-05"}}],LA:[{LAK:{_from:"1979-12-10"}}],LB:[{LBP:{_from:"1948-02-02"}}],LC:[{XCD:{_from:"1965-10-06"}}],LI:[{CHF:{_from:"1921-02-01"}}],LK:[{LKR:{_from:"1978-05-22"}}],LR:[{LRD:{_from:"1944-01-01"}}],LS:[{ZAR:{_from:"1961-02-14"}},{LSL:{_from:"1980-01-22"}}],LT:[{SUR:{_from:"1961-01-01",_to:"1992-10-01"}},{LTT:{_from:"1992-10-01",_to:"1993-06-25"}},{LTL:{_from:"1993-06-25",_to:"2014-12-31"}},{EUR:{_from:"2015-01-01"}}],LU:[{LUF:{_from:"1944-09-04",_to:"2002-02-28"}},{LUC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{LUL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],LV:[{SUR:{_from:"1961-01-01",_to:"1992-07-20"}},{LVR:{_from:"1992-05-07",_to:"1993-10-17"}},{LVL:{_from:"1993-06-28",_to:"2013-12-31"}},{EUR:{_from:"2014-01-01"}}],LY:[{LYD:{_from:"1971-09-01"}}],MA:[{MAF:{_from:"1881-01-01",_to:"1959-10-17"}},{MAD:{_from:"1959-10-17"}}],MC:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{MCF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MD:[{MDC:{_from:"1992-06-01",_to:"1993-11-29"}},{MDL:{_from:"1993-11-29"}}],ME:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{DEM:{_from:"1999-10-02",_to:"2002-05-15"}},{EUR:{_from:"2002-01-01"}}],MF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MG:[{MGF:{_from:"1963-07-01",_to:"2004-12-31"}},{MGA:{_from:"1983-11-01"}}],MH:[{USD:{_from:"1944-01-01"}}],MK:[{MKN:{_from:"1992-04-26",_to:"1993-05-20"}},{MKD:{_from:"1993-05-20"}}],ML:[{XOF:{_from:"1958-11-24",_to:"1962-07-02"}},{MLF:{_from:"1962-07-02",_to:"1984-08-31"}},{XOF:{_from:"1984-06-01"}}],MM:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}},{MMK:{_from:"1989-06-18"}}],MN:[{MNT:{_from:"1915-03-01"}}],MO:[{MOP:{_from:"1901-01-01"}}],MP:[{USD:{_from:"1944-01-01"}}],MQ:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MR:[{XOF:{_from:"1958-11-28",_to:"1973-06-29"}},{MRO:{_from:"1973-06-29",_to:"2018-06-30"}},{MRU:{_from:"2018-01-01"}}],MS:[{XCD:{_from:"1967-02-27"}}],MT:[{MTP:{_from:"1914-08-13",_to:"1968-06-07"}},{MTL:{_from:"1968-06-07",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],MU:[{MUR:{_from:"1934-04-01"}}],MV:[{MVP:{_from:"1947-01-01",_to:"1981-07-01"}},{MVR:{_from:"1981-07-01"}}],MW:[{MWK:{_from:"1971-02-15"}}],MX:[{MXV:{_tender:"false"}},{MXP:{_from:"1822-01-01",_to:"1992-12-31"}},{MXN:{_from:"1993-01-01"}}],MY:[{MYR:{_from:"1963-09-16"}}],MZ:[{MZE:{_from:"1975-06-25",_to:"1980-06-16"}},{MZM:{_from:"1980-06-16",_to:"2006-12-31"}},{MZN:{_from:"2006-07-01"}}],NA:[{ZAR:{_from:"1961-02-14"}},{NAD:{_from:"1993-01-01"}}],NC:[{XPF:{_from:"1985-01-01"}}],NE:[{XOF:{_from:"1958-12-19"}}],NF:[{AUD:{_from:"1966-02-14"}}],NG:[{NGN:{_from:"1973-01-01"}}],NI:[{NIC:{_from:"1988-02-15",_to:"1991-04-30"}},{NIO:{_from:"1991-04-30"}}],NL:[{NLG:{_from:"1813-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],NO:[{SEK:{_from:"1873-05-27",_to:"1905-06-07"}},{NOK:{_from:"1905-06-07"}}],NP:[{INR:{_from:"1870-01-01",_to:"1966-10-17"}},{NPR:{_from:"1933-01-01"}}],NR:[{AUD:{_from:"1966-02-14"}}],NU:[{NZD:{_from:"1967-07-10"}}],NZ:[{NZD:{_from:"1967-07-10"}}],OM:[{OMR:{_from:"1972-11-11"}}],PA:[{PAB:{_from:"1903-11-04"}},{USD:{_from:"1903-11-18"}}],PE:[{PES:{_from:"1863-02-14",_to:"1985-02-01"}},{PEI:{_from:"1985-02-01",_to:"1991-07-01"}},{PEN:{_from:"1991-07-01"}}],PF:[{XPF:{_from:"1945-12-26"}}],PG:[{AUD:{_from:"1966-02-14",_to:"1975-09-16"}},{PGK:{_from:"1975-09-16"}}],PH:[{PHP:{_from:"1946-07-04"}}],PK:[{INR:{_from:"1835-08-17",_to:"1947-08-15"}},{PKR:{_from:"1948-04-01"}}],PL:[{PLZ:{_from:"1950-10-28",_to:"1994-12-31"}},{PLN:{_from:"1995-01-01"}}],PM:[{FRF:{_from:"1972-12-21",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],PN:[{NZD:{_from:"1969-01-13"}}],PR:[{ESP:{_from:"1800-01-01",_to:"1898-12-10"}},{USD:{_from:"1898-12-10"}}],PS:[{JOD:{_from:"1950-07-01",_to:"1967-06-01"}},{ILP:{_from:"1967-06-01",_to:"1980-02-22"}},{ILS:{_from:"1985-09-04"}},{JOD:{_from:"1996-02-12"}}],PT:[{PTE:{_from:"1911-05-22",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],PW:[{USD:{_from:"1944-01-01"}}],PY:[{PYG:{_from:"1943-11-01"}}],QA:[{QAR:{_from:"1973-05-19"}}],RE:[{FRF:{_from:"1975-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],RO:[{ROL:{_from:"1952-01-28",_to:"2006-12-31"}},{RON:{_from:"2005-07-01"}}],RS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-10-25"}},{RSD:{_from:"2006-10-25"}}],RU:[{RUR:{_from:"1991-12-25",_to:"1998-12-31"}},{RUB:{_from:"1999-01-01"}}],RW:[{RWF:{_from:"1964-05-19"}}],SA:[{SAR:{_from:"1952-10-22"}}],SB:[{AUD:{_from:"1966-02-14",_to:"1978-06-30"}},{SBD:{_from:"1977-10-24"}}],SC:[{SCR:{_from:"1903-11-01"}}],SD:[{EGP:{_from:"1889-01-19",_to:"1958-01-01"}},{GBP:{_from:"1889-01-19",_to:"1958-01-01"}},{SDP:{_from:"1957-04-08",_to:"1998-06-01"}},{SDD:{_from:"1992-06-08",_to:"2007-06-30"}},{SDG:{_from:"2007-01-10"}}],SE:[{SEK:{_from:"1873-05-27"}}],SG:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{SGD:{_from:"1967-06-12"}}],SH:[{SHP:{_from:"1917-02-15"}}],SI:[{SIT:{_from:"1992-10-07",_to:"2007-01-14"}},{EUR:{_from:"2007-01-01"}}],SJ:[{NOK:{_from:"1905-06-07"}}],SK:[{CSK:{_from:"1953-06-01",_to:"1992-12-31"}},{SKK:{_from:"1992-12-31",_to:"2009-01-01"}},{EUR:{_from:"2009-01-01"}}],SL:[{GBP:{_from:"1808-11-30",_to:"1966-02-04"}},{SLL:{_from:"1964-08-04"}}],SM:[{ITL:{_from:"1865-12-23",_to:"2001-02-28"}},{EUR:{_from:"1999-01-01"}}],SN:[{XOF:{_from:"1959-04-04"}}],SO:[{SOS:{_from:"1960-07-01"}}],SR:[{NLG:{_from:"1815-11-20",_to:"1940-05-10"}},{SRG:{_from:"1940-05-10",_to:"2003-12-31"}},{SRD:{_from:"2004-01-01"}}],SS:[{SDG:{_from:"2007-01-10",_to:"2011-09-01"}},{SSP:{_from:"2011-07-18"}}],ST:[{STD:{_from:"1977-09-08",_to:"2017-12-31"}},{STN:{_from:"2018-01-01"}}],SU:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}}],SV:[{SVC:{_from:"1919-11-11",_to:"2001-01-01"}},{USD:{_from:"2001-01-01"}}],SX:[{ANG:{_from:"2010-10-10"}}],SY:[{SYP:{_from:"1948-01-01"}}],SZ:[{SZL:{_from:"1974-09-06"}}],TA:[{GBP:{_from:"1938-01-12"}}],TC:[{USD:{_from:"1969-09-08"}}],TD:[{XAF:{_from:"1993-01-01"}}],TF:[{FRF:{_from:"1959-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],TG:[{XOF:{_from:"1958-11-28"}}],TH:[{THB:{_from:"1928-04-15"}}],TJ:[{RUR:{_from:"1991-12-25",_to:"1995-05-10"}},{TJR:{_from:"1995-05-10",_to:"2000-10-25"}},{TJS:{_from:"2000-10-26"}}],TK:[{NZD:{_from:"1967-07-10"}}],TL:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}},{USD:{_from:"1999-10-20"}}],TM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-01"}},{TMM:{_from:"1993-11-01",_to:"2009-01-01"}},{TMT:{_from:"2009-01-01"}}],TN:[{TND:{_from:"1958-11-01"}}],TO:[{TOP:{_from:"1966-02-14"}}],TP:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}}],TR:[{TRL:{_from:"1922-11-01",_to:"2005-12-31"}},{TRY:{_from:"2005-01-01"}}],TT:[{TTD:{_from:"1964-01-01"}}],TV:[{AUD:{_from:"1966-02-14"}}],TW:[{TWD:{_from:"1949-06-15"}}],TZ:[{TZS:{_from:"1966-06-14"}}],UA:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1992-11-13"}},{UAK:{_from:"1992-11-13",_to:"1993-10-17"}},{UAH:{_from:"1996-09-02"}}],UG:[{UGS:{_from:"1966-08-15",_to:"1987-05-15"}},{UGX:{_from:"1987-05-15"}}],UM:[{USD:{_from:"1944-01-01"}}],US:[{USN:{_tender:"false"}},{USS:{_tender:"false",_to:"2014-03-01"}},{USD:{_from:"1792-01-01"}}],UY:[{UYI:{_tender:"false"}},{UYW:{_tender:"false"}},{UYP:{_from:"1975-07-01",_to:"1993-03-01"}},{UYU:{_from:"1993-03-01"}}],UZ:[{UZS:{_from:"1994-07-01"}}],VA:[{ITL:{_from:"1870-10-19",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],VC:[{XCD:{_from:"1965-10-06"}}],VE:[{VEB:{_from:"1871-05-11",_to:"2008-06-30"}},{VEF:{_from:"2008-01-01",_to:"2018-08-20"}},{VES:{_from:"2018-08-20"}}],VG:[{USD:{_from:"1833-01-01"}},{GBP:{_from:"1833-01-01",_to:"1959-01-01"}}],VI:[{USD:{_from:"1837-01-01"}}],VN:[{VNN:{_from:"1978-05-03",_to:"1985-09-14"}},{VND:{_from:"1985-09-14"}}],VU:[{VUV:{_from:"1981-01-01"}}],WF:[{XPF:{_from:"1961-07-30"}}],WS:[{WST:{_from:"1967-07-10"}}],XK:[{YUM:{_from:"1994-01-24",_to:"1999-09-30"}},{DEM:{_from:"1999-09-01",_to:"2002-03-09"}},{EUR:{_from:"2002-01-01"}}],YD:[{YDD:{_from:"1965-04-01",_to:"1996-01-01"}}],YE:[{YER:{_from:"1990-05-22"}}],YT:[{KMF:{_from:"1975-01-01",_to:"1976-02-23"}},{FRF:{_from:"1976-02-23",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],YU:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-24"}},{YUM:{_from:"1994-01-24",_to:"2002-05-15"}}],ZA:[{ZAR:{_from:"1961-02-14"}},{ZAL:{_tender:"false",_from:"1985-09-01",_to:"1995-03-13"}}],ZM:[{ZMK:{_from:"1968-01-16",_to:"2013-01-01"}},{ZMW:{_from:"2013-01-01"}}],ZR:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-31"}}],ZW:[{RHD:{_from:"1970-02-17",_to:"1980-04-18"}},{ZWD:{_from:"1980-04-18",_to:"2008-08-01"}},{ZWR:{_from:"2008-08-01",_to:"2009-02-02"}},{ZWL:{_from:"2009-02-02",_to:"2009-04-12"}},{USD:{_from:"2009-04-12"}}],ZZ:[{XAG:{_tender:"false"}},{XAU:{_tender:"false"}},{XBA:{_tender:"false"}},{XBB:{_tender:"false"}},{XBC:{_tender:"false"}},{XBD:{_tender:"false"}},{XDR:{_tender:"false"}},{XPD:{_tender:"false"}},{XPT:{_tender:"false"}},{XSU:{_tender:"false"}},{XTS:{_tender:"false"}},{XUA:{_tender:"false"}},{XXX:{_tender:"false"}},{XRE:{_tender:"false",_to:"1999-11-30"}},{XFU:{_tender:"false",_to:"2013-11-30"}},{XFO:{_tender:"false",_from:"1930-01-01",_to:"2003-04-01"}}]}}}},{main:{fr:{identity:{version:{_cldrVersion:"36"},language:"fr"},dates:{calendars:{gregorian:{months:{format:{abbreviated:{1:"janv.",2:"févr.",3:"mars",4:"avr.",5:"mai",6:"juin",7:"juil.",8:"août",9:"sept.",10:"oct.",11:"nov.",12:"déc."},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"janvier",2:"février",3:"mars",4:"avril",5:"mai",6:"juin",7:"juillet",8:"août",9:"septembre",10:"octobre",11:"novembre",12:"décembre"}},"stand-alone":{abbreviated:{1:"janv.",2:"févr.",3:"mars",4:"avr.",5:"mai",6:"juin",7:"juil.",8:"août",9:"sept.",10:"oct.",11:"nov.",12:"déc."},narrow:{1:"J",2:"F",3:"M",4:"A",5:"M",6:"J",7:"J",8:"A",9:"S",10:"O",11:"N",12:"D"},wide:{1:"janvier",2:"février",3:"mars",4:"avril",5:"mai",6:"juin",7:"juillet",8:"août",9:"septembre",10:"octobre",11:"novembre",12:"décembre"}}},days:{format:{abbreviated:{sun:"dim.",mon:"lun.",tue:"mar.",wed:"mer.",thu:"jeu.",fri:"ven.",sat:"sam."},narrow:{sun:"D",mon:"L",tue:"M",wed:"M",thu:"J",fri:"V",sat:"S"},short:{sun:"di",mon:"lu",tue:"ma",wed:"me",thu:"je",fri:"ve",sat:"sa"},wide:{sun:"dimanche",mon:"lundi",tue:"mardi",wed:"mercredi",thu:"jeudi",fri:"vendredi",sat:"samedi"}},"stand-alone":{abbreviated:{sun:"dim.",mon:"lun.",tue:"mar.",wed:"mer.",thu:"jeu.",fri:"ven.",sat:"sam."},narrow:{sun:"D",mon:"L",tue:"M",wed:"M",thu:"J",fri:"V",sat:"S"},short:{sun:"di",mon:"lu",tue:"ma",wed:"me",thu:"je",fri:"ve",sat:"sa"},wide:{sun:"dimanche",mon:"lundi",tue:"mardi",wed:"mercredi",thu:"jeudi",fri:"vendredi",sat:"samedi"}}},quarters:{format:{abbreviated:{1:"T1",2:"T2",3:"T3",4:"T4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1er trimestre",2:"2e trimestre",3:"3e trimestre",4:"4e trimestre"}},"stand-alone":{abbreviated:{1:"T1",2:"T2",3:"T3",4:"T4"},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"1er trimestre",2:"2e trimestre",3:"3e trimestre",4:"4e trimestre"}}},dayPeriods:{format:{abbreviated:{midnight:"minuit",am:"AM",noon:"midi",pm:"PM",morning1:"mat.",afternoon1:"ap.m.",evening1:"soir",night1:"nuit"},narrow:{midnight:"minuit",am:"AM",noon:"midi",pm:"PM",morning1:"mat.",afternoon1:"ap.m.",evening1:"soir",night1:"nuit"},wide:{midnight:"minuit",am:"AM",noon:"midi",pm:"PM",morning1:"du matin",afternoon1:"de l’après-midi",evening1:"du soir",night1:"du matin"}},"stand-alone":{abbreviated:{midnight:"minuit",am:"AM",noon:"midi",pm:"PM",morning1:"mat.",afternoon1:"ap.m.",evening1:"soir",night1:"nuit"},narrow:{midnight:"minuit",am:"AM",noon:"midi",pm:"PM",morning1:"mat.",afternoon1:"ap.m.",evening1:"soir",night1:"nuit"},wide:{midnight:"minuit",am:"AM",noon:"midi",pm:"PM",morning1:"matin",afternoon1:"après-midi",evening1:"soir",night1:"nuit"}}},eras:{eraNames:{0:"avant Jésus-Christ",1:"après Jésus-Christ","0-alt-variant":"avant l’ère commune","1-alt-variant":"de l’ère commune"},eraAbbr:{0:"av. J.-C.",1:"ap. J.-C.","0-alt-variant":"AEC","1-alt-variant":"EC"},eraNarrow:{0:"av. J.-C.",1:"ap. J.-C.","0-alt-variant":"AEC","1-alt-variant":"EC"}},dateFormats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},timeFormats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},dateTimeFormats:{full:"{1} 'à' {0}",long:"{1} 'à' {0}",medium:"{1} 'à' {0}",short:"{1} {0}",availableFormats:{Bh:"h B",Bhm:"h:mm B",Bhms:"h:mm:ss B",d:"d",E:"E",EBhm:"E h:mm B",EBhms:"E h:mm:ss B",Ed:"E d",Ehm:"E h:mm a",EHm:"E HH:mm",Ehms:"E h:mm:ss a",EHms:"E HH:mm:ss",Gy:"y G",GyMMM:"MMM y G",GyMMMd:"d MMM y G",GyMMMEd:"E d MMM y G",h:"h a",H:"HH 'h'",hm:"h:mm a",Hm:"HH:mm",hms:"h:mm:ss a",Hms:"HH:mm:ss",hmsv:"h:mm:ss a v",Hmsv:"HH:mm:ss v",hmv:"h:mm a v",Hmv:"HH:mm v",M:"L",Md:"dd/MM",MEd:"E dd/MM",MMM:"LLL",MMMd:"d MMM",MMMEd:"E d MMM",MMMMd:"d MMMM","MMMMW-count-one":"'semaine' W (MMMM)","MMMMW-count-other":"'semaine' W (MMMM)",ms:"mm:ss",y:"y",yM:"MM/y",yMd:"dd/MM/y",yMEd:"E dd/MM/y",yMMM:"MMM y",yMMMd:"d MMM y",yMMMEd:"E d MMM y",yMMMM:"MMMM y",yQQQ:"QQQ y",yQQQQ:"QQQQ y","yw-count-one":"'semaine' w 'de' Y","yw-count-other":"'semaine' w 'de' Y"},appendItems:{Day:"{0} ({2}: {1})","Day-Of-Week":"{0} {1}",Era:"{1} {0}",Hour:"{0} ({2}: {1})",Minute:"{0} ({2}: {1})",Month:"{0} ({2}: {1})",Quarter:"{0} ({2}: {1})",Second:"{0} ({2}: {1})",Timezone:"{0} {1}",Week:"{0} ({2}: {1})",Year:"{1} {0}"},intervalFormats:{intervalFormatFallback:"{0} – {1}",Bh:{B:"h B – h B",h:"h – h B"},Bhm:{B:"h:mm B – h:mm B",h:"h:mm – h:mm B",m:"h:mm – h:mm B"},d:{d:"d–d"},Gy:{G:"y G 'à' y G",y:"y–y G"},GyM:{G:"M/y G 'à' M/y G",M:"M–M/y G",y:"M/y 'à' M/y G"},GyMd:{d:"d–d/M/y G",G:"d/M/y G 'à' d/M/y G",M:"d/M 'à' d/M/y G",y:"d/M/y 'à' d/M/y G"},GyMEd:{d:"E d 'à' E d/M/y G",G:"E d/M/y G 'à' E d/M/y G",M:"E d/M 'à' E d/M/y G",y:"E d/M/y 'à' E d/M/y G"},GyMMM:{G:"MMM y G 'à' MMM y G",M:"MMM 'à' MMM y G",y:"MMM y 'à' MMM y G"},GyMMMd:{d:"d–d MMM y G",G:"d MMM y G 'à' d MMM y G",M:"d MMM 'à' d MMM y G",y:"d MMM y 'à' d MMM y G"},GyMMMEd:{d:"E d 'à' E d MMM y G",G:"E d MMM y G 'à' E d MMM y G",M:"E d MMM 'à' E d MMM y G",y:"E d MMM y 'à' E d MMM y G"},h:{a:"h a – h a",h:"h – h a"},H:{H:"HH – HH"},hm:{a:"h:mm a – h:mm a",h:"h:mm – h:mm a",m:"h:mm – h:mm a"},Hm:{H:"HH:mm – HH:mm",m:"HH:mm – HH:mm"},hmv:{a:"h:mm a – h:mm a v",h:"h:mm – h:mm a v",m:"h:mm – h:mm a v"},Hmv:{H:"HH:mm – HH:mm v",m:"HH:mm – HH:mm v"},hv:{a:"h a – h a v",h:"h – h a v"},Hv:{H:"HH – HH v"},M:{M:"M–M"},Md:{d:"dd/MM – dd/MM",M:"dd/MM – dd/MM"},MEd:{d:"E dd/MM – E dd/MM",M:"E dd/MM – E dd/MM"},MMM:{M:"MMM–MMM"},MMMd:{d:"d–d MMM",M:"d MMM – d MMM"},MMMEd:{d:"E d – E d MMM",M:"E d MMM – E d MMM"},y:{y:"y–y"},yM:{M:"MM/y – MM/y",y:"MM/y – MM/y"},yMd:{d:"dd/MM/y – dd/MM/y",M:"dd/MM/y – dd/MM/y",y:"dd/MM/y – dd/MM/y"},yMEd:{d:"E dd/MM/y – E dd/MM/y",M:"E dd/MM/y – E dd/MM/y",y:"E dd/MM/y – E dd/MM/y"},yMMM:{M:"MMM–MMM y",y:"MMM y – MMM y"},yMMMd:{d:"d–d MMM y",M:"d MMM – d MMM y",y:"d MMM y – d MMM y"},yMMMEd:{d:"E d – E d MMM y",M:"E d MMM – E d MMM y",y:"E d MMM y – E d MMM y"},yMMMM:{M:"MMMM – MMMM y",y:"MMMM y – MMMM y"}}}}}}}}},{main:{fr:{identity:{version:{_cldrVersion:"36"},language:"fr"},dates:{fields:{era:{displayName:"ère"},"era-short":{displayName:"ère"},"era-narrow":{displayName:"ère"},year:{displayName:"année","relative-type--1":"l’année dernière","relative-type-0":"cette année","relative-type-1":"l’année prochaine","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} an","relativeTimePattern-count-other":"dans {0} ans"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} an","relativeTimePattern-count-other":"il y a {0} ans"}},"year-short":{displayName:"an","relative-type--1":"l’année dernière","relative-type-0":"cette année","relative-type-1":"l’année prochaine","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} a","relativeTimePattern-count-other":"dans {0} a"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} a","relativeTimePattern-count-other":"il y a {0} a"}},"year-narrow":{displayName:"a","relative-type--1":"l’année dernière","relative-type-0":"cette année","relative-type-1":"l’année prochaine","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} a","relativeTimePattern-count-other":"+{0} a"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} a","relativeTimePattern-count-other":"-{0} a"}},quarter:{displayName:"trimestre","relative-type--1":"le trimestre dernier","relative-type-0":"ce trimestre","relative-type-1":"le trimestre prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} trimestre","relativeTimePattern-count-other":"dans {0} trimestres"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} trimestre","relativeTimePattern-count-other":"il y a {0} trimestres"}},"quarter-short":{displayName:"trim.","relative-type--1":"le trimestre dernier","relative-type-0":"ce trimestre","relative-type-1":"le trimestre prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} trim.","relativeTimePattern-count-other":"dans {0} trim."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} trim.","relativeTimePattern-count-other":"il y a {0} trim."}},"quarter-narrow":{displayName:"trim.","relative-type--1":"le trimestre dernier","relative-type-0":"ce trimestre","relative-type-1":"le trimestre prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} trim.","relativeTimePattern-count-other":"+{0} trim."},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} trim.","relativeTimePattern-count-other":"-{0} trim."}},month:{displayName:"mois","relative-type--1":"le mois dernier","relative-type-0":"ce mois-ci","relative-type-1":"le mois prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} mois","relativeTimePattern-count-other":"dans {0} mois"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} mois","relativeTimePattern-count-other":"il y a {0} mois"}},"month-short":{displayName:"m.","relative-type--1":"le mois dernier","relative-type-0":"ce mois-ci","relative-type-1":"le mois prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} m.","relativeTimePattern-count-other":"dans {0} m."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} m.","relativeTimePattern-count-other":"il y a {0} m."}},"month-narrow":{displayName:"m.","relative-type--1":"le mois dernier","relative-type-0":"ce mois-ci","relative-type-1":"le mois prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} m.","relativeTimePattern-count-other":"+{0} m."},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} m.","relativeTimePattern-count-other":"-{0} m."}},week:{displayName:"semaine","relative-type--1":"la semaine dernière","relative-type-0":"cette semaine","relative-type-1":"la semaine prochaine","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} semaine","relativeTimePattern-count-other":"dans {0} semaines"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} semaine","relativeTimePattern-count-other":"il y a {0} semaines"},relativePeriod:"la semaine du {0}"},"week-short":{displayName:"sem.","relative-type--1":"la semaine dernière","relative-type-0":"cette semaine","relative-type-1":"la semaine prochaine","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} sem.","relativeTimePattern-count-other":"dans {0} sem."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} sem.","relativeTimePattern-count-other":"il y a {0} sem."},relativePeriod:"sem. du {0}"},"week-narrow":{displayName:"sem.","relative-type--1":"la semaine dernière","relative-type-0":"cette semaine","relative-type-1":"la semaine prochaine","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} sem.","relativeTimePattern-count-other":"+{0} sem."},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} sem.","relativeTimePattern-count-other":"-{0} sem."},relativePeriod:"sem. du {0}"},weekOfMonth:{displayName:"semaine (mois)"},"weekOfMonth-short":{displayName:"sem. (m.)"},"weekOfMonth-narrow":{displayName:"sem. (m.)"},day:{displayName:"jour","relative-type--2":"avant-hier","relative-type--1":"hier","relative-type-0":"aujourd’hui","relative-type-1":"demain","relative-type-2":"après-demain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} jour","relativeTimePattern-count-other":"dans {0} jours"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} jour","relativeTimePattern-count-other":"il y a {0} jours"}},"day-short":{displayName:"j","relative-type--2":"avant-hier","relative-type--1":"hier","relative-type-0":"aujourd’hui","relative-type-1":"demain","relative-type-2":"après-demain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} j","relativeTimePattern-count-other":"dans {0} j"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} j","relativeTimePattern-count-other":"il y a {0} j"}},"day-narrow":{displayName:"j","relative-type--2":"avant-hier","relative-type--1":"hier","relative-type-0":"aujourd’hui","relative-type-1":"demain","relative-type-2":"après-demain","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} j","relativeTimePattern-count-other":"+{0} j"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} j","relativeTimePattern-count-other":"-{0} j"}},dayOfYear:{displayName:"jour (année)"},"dayOfYear-short":{displayName:"j (an)"},"dayOfYear-narrow":{displayName:"j (an)"},weekday:{displayName:"jour de la semaine"},"weekday-short":{displayName:"j (sem.)"},"weekday-narrow":{displayName:"j (sem.)"},weekdayOfMonth:{displayName:"jour (mois)"},"weekdayOfMonth-short":{displayName:"jour (mois)"},"weekdayOfMonth-narrow":{displayName:"jour (mois)"},sun:{"relative-type--1":"dimanche dernier","relative-type-0":"ce dimanche","relative-type-1":"dimanche prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} dimanche","relativeTimePattern-count-other":"dans {0} dimanches"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} dimanche","relativeTimePattern-count-other":"il y a {0} dimanches"}},"sun-short":{"relative-type--1":"dim. dernier","relative-type-0":"ce dim.","relative-type-1":"dim. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} dim.","relativeTimePattern-count-other":"dans {0} dim."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} dim.","relativeTimePattern-count-other":"il y a {0} dim."}},"sun-narrow":{"relative-type--1":"dim. dernier","relative-type-0":"ce dim.","relative-type-1":"dim. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} dim.","relativeTimePattern-count-other":"dans {0} dim."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} dim.","relativeTimePattern-count-other":"il y a {0} dim."}},mon:{"relative-type--1":"lundi dernier","relative-type-0":"ce lundi","relative-type-1":"lundi prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} lundi","relativeTimePattern-count-other":"dans {0} lundis"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} lundi","relativeTimePattern-count-other":"il y a {0} lundis"}},"mon-short":{"relative-type--1":"lun. dernier","relative-type-0":"ce lun.","relative-type-1":"lun. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} lun.","relativeTimePattern-count-other":"dans {0} lun."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} lun.","relativeTimePattern-count-other":"il y a {0} lun."}},"mon-narrow":{"relative-type--1":"lun. dernier","relative-type-0":"ce lun.","relative-type-1":"lun. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} lun.","relativeTimePattern-count-other":"dans {0} lun."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} lun.","relativeTimePattern-count-other":"il y a {0} lun."}},tue:{"relative-type--1":"mardi dernier","relative-type-0":"ce mardi","relative-type-1":"mardi prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} mardi","relativeTimePattern-count-other":"dans {0} mardis"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} mardi","relativeTimePattern-count-other":"il y a {0} mardis"}},"tue-short":{"relative-type--1":"mar. dernier","relative-type-0":"ce mar.","relative-type-1":"mar. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} mar.","relativeTimePattern-count-other":"dans {0} mar."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} mar.","relativeTimePattern-count-other":"il y a {0} mar."}},"tue-narrow":{"relative-type--1":"mar. dernier","relative-type-0":"ce mar.","relative-type-1":"mar. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} mar.","relativeTimePattern-count-other":"dans {0} mar."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} mar.","relativeTimePattern-count-other":"il y a {0} mar."}},wed:{"relative-type--1":"mercredi dernier","relative-type-0":"ce mercredi","relative-type-1":"mercredi prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} mercredi","relativeTimePattern-count-other":"dans {0} mercredis"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} mercredi","relativeTimePattern-count-other":"il y a {0} mercredis"}},"wed-short":{"relative-type--1":"mer. dernier","relative-type-0":"ce mer.","relative-type-1":"mer. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} mer.","relativeTimePattern-count-other":"dans {0} mer."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} mer.","relativeTimePattern-count-other":"il y a {0} mer."}},"wed-narrow":{"relative-type--1":"mer. dernier","relative-type-0":"ce mer.","relative-type-1":"mer. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} mer.","relativeTimePattern-count-other":"dans {0} mer."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} mer.","relativeTimePattern-count-other":"il y a {0} mer."}},thu:{"relative-type--1":"jeudi dernier","relative-type-0":"ce jeudi","relative-type-1":"jeudi prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} jeudi","relativeTimePattern-count-other":"dans {0} jeudis"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} jeudi","relativeTimePattern-count-other":"il y a {0} jeudis"}},"thu-short":{"relative-type--1":"jeu. dernier","relative-type-0":"ce jeu.","relative-type-1":"jeu. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} jeu.","relativeTimePattern-count-other":"dans {0} jeu."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} jeu.","relativeTimePattern-count-other":"il y a {0} jeu."}},"thu-narrow":{"relative-type--1":"jeu. dernier","relative-type-0":"ce jeu.","relative-type-1":"jeu. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} jeu.","relativeTimePattern-count-other":"dans {0} jeu."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} jeu.","relativeTimePattern-count-other":"il y a {0} jeu."}},fri:{"relative-type--1":"vendredi dernier","relative-type-0":"ce vendredi","relative-type-1":"vendredi prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} vendredi","relativeTimePattern-count-other":"dans {0} vendredis"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} vendredi","relativeTimePattern-count-other":"il y a {0} vendredis"}},"fri-short":{"relative-type--1":"ven. dernier","relative-type-0":"ce ven.","relative-type-1":"ven. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} ven.","relativeTimePattern-count-other":"dans {0} ven."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} ven.","relativeTimePattern-count-other":"il y a {0} ven."}},"fri-narrow":{"relative-type--1":"ven. dernier","relative-type-0":"ce ven.","relative-type-1":"ven. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} ven.","relativeTimePattern-count-other":"dans {0} ven."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} ven.","relativeTimePattern-count-other":"il y a {0} ven."}},sat:{"relative-type--1":"samedi dernier","relative-type-0":"ce samedi","relative-type-1":"samedi prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} samedi","relativeTimePattern-count-other":"dans {0} samedis"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} samedi","relativeTimePattern-count-other":"il y a {0} samedis"}},"sat-short":{"relative-type--1":"sam. dernier","relative-type-0":"ce sam.","relative-type-1":"sam. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} sam.","relativeTimePattern-count-other":"dans {0} sam."},"relativeTime-type-past":{"relativeTimePattern-count-one":"dans {0} sam.","relativeTimePattern-count-other":"dans {0} sam."}},"sat-narrow":{"relative-type--1":"sam. dernier","relative-type-0":"ce sam.","relative-type-1":"sam. prochain","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} sam.","relativeTimePattern-count-other":"dans {0} sam."},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} sam.","relativeTimePattern-count-other":"il y a {0} sam."}},"dayperiod-short":{displayName:"cadran"},dayperiod:{displayName:"cadran"},"dayperiod-narrow":{displayName:"cadran"},hour:{displayName:"heure","relative-type-0":"cette heure-ci","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} heure","relativeTimePattern-count-other":"dans {0} heures"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} heure","relativeTimePattern-count-other":"il y a {0} heures"}},"hour-short":{displayName:"h","relative-type-0":"cette heure-ci","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} h","relativeTimePattern-count-other":"dans {0} h"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} h","relativeTimePattern-count-other":"il y a {0} h"}},"hour-narrow":{displayName:"h","relative-type-0":"cette heure-ci","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} h","relativeTimePattern-count-other":"+{0} h"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} h","relativeTimePattern-count-other":"-{0} h"}},minute:{displayName:"minute","relative-type-0":"cette minute-ci","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} minute","relativeTimePattern-count-other":"dans {0} minutes"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} minute","relativeTimePattern-count-other":"il y a {0} minutes"}},"minute-short":{displayName:"min","relative-type-0":"cette minute-ci","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} min","relativeTimePattern-count-other":"dans {0} min"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} min","relativeTimePattern-count-other":"il y a {0} min"}},"minute-narrow":{displayName:"min","relative-type-0":"cette minute-ci","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} min","relativeTimePattern-count-other":"+{0} min"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} min","relativeTimePattern-count-other":"-{0} min"}},second:{displayName:"seconde","relative-type-0":"maintenant","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} seconde","relativeTimePattern-count-other":"dans {0} secondes"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} seconde","relativeTimePattern-count-other":"il y a {0} secondes"}},"second-short":{displayName:"s","relative-type-0":"maintenant","relativeTime-type-future":{"relativeTimePattern-count-one":"dans {0} s","relativeTimePattern-count-other":"dans {0} s"},"relativeTime-type-past":{"relativeTimePattern-count-one":"il y a {0} s","relativeTimePattern-count-other":"il y a {0} s"}},"second-narrow":{displayName:"s","relative-type-0":"maintenant","relativeTime-type-future":{"relativeTimePattern-count-one":"+{0} s","relativeTimePattern-count-other":"+{0} s"},"relativeTime-type-past":{"relativeTimePattern-count-one":"-{0} s","relativeTimePattern-count-other":"-{0} s"}},zone:{displayName:"fuseau horaire"},"zone-short":{displayName:"fuseau horaire"},"zone-narrow":{displayName:"fuseau horaire"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},"plurals-type-cardinal":{af:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ak:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},am:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},an:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ar:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ars:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},as:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},asa:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ast:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},az:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},be:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..4 and n % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 2.0, 3.0, 4.0, 22.0, 23.0, 24.0, 32.0, 33.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 10 = 0 or n % 10 = 5..9 or n % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":"   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …"},bem:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bez:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bho:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bm:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},br:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11,71,91 @integer 1, 21, 31, 41, 51, 61, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 81.0, 101.0, 1001.0, …","pluralRule-count-two":"n % 10 = 2 and n % 100 != 12,72,92 @integer 2, 22, 32, 42, 52, 62, 82, 102, 1002, … @decimal 2.0, 22.0, 32.0, 42.0, 52.0, 62.0, 82.0, 102.0, 1002.0, …","pluralRule-count-few":"n % 10 = 3..4,9 and n % 100 != 10..19,70..79,90..99 @integer 3, 4, 9, 23, 24, 29, 33, 34, 39, 43, 44, 49, 103, 1003, … @decimal 3.0, 4.0, 9.0, 23.0, 24.0, 29.0, 33.0, 34.0, 103.0, 1003.0, …","pluralRule-count-many":"n != 0 and n % 1000000 = 0 @integer 1000000, … @decimal 1000000.0, 1000000.00, 1000000.000, …","pluralRule-count-other":" @integer 0, 5~8, 10~20, 100, 1000, 10000, 100000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, …"},brx:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bs:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ca:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ce:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ceb:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},cgg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},chr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ckb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},cs:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},cy:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3 @integer 3 @decimal 3.0, 3.00, 3.000, 3.0000","pluralRule-count-many":"n = 6 @integer 6 @decimal 6.0, 6.00, 6.000, 6.0000","pluralRule-count-other":" @integer 4, 5, 7~20, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},da:{"pluralRule-count-one":"n = 1 or t != 0 and i = 0,1 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0~3.4, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},de:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dv:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dz:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ee:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},el:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},en:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},es:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},et:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fa:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ff:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fil:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},fo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fr:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fur:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fy:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ga:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3..6 @integer 3~6 @decimal 3.0, 4.0, 5.0, 6.0, 3.00, 4.00, 5.00, 6.00, 3.000, 4.000, 5.000, 6.000, 3.0000, 4.0000, 5.0000, 6.0000","pluralRule-count-many":"n = 7..10 @integer 7~10 @decimal 7.0, 8.0, 9.0, 10.0, 7.00, 8.00, 9.00, 10.00, 7.000, 8.000, 9.000, 10.000, 7.0000, 8.0000, 9.0000, 10.0000","pluralRule-count-other":" @integer 0, 11~25, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gd:{"pluralRule-count-one":"n = 1,11 @integer 1, 11 @decimal 1.0, 11.0, 1.00, 11.00, 1.000, 11.000, 1.0000","pluralRule-count-two":"n = 2,12 @integer 2, 12 @decimal 2.0, 12.0, 2.00, 12.00, 2.000, 12.000, 2.0000","pluralRule-count-few":"n = 3..10,13..19 @integer 3~10, 13~19 @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 3.00","pluralRule-count-other":" @integer 0, 20~34, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gsw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},guw:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gv:{"pluralRule-count-one":"v = 0 and i % 10 = 1 @integer 1, 11, 21, 31, 41, 51, 61, 71, 101, 1001, …","pluralRule-count-two":"v = 0 and i % 10 = 2 @integer 2, 12, 22, 32, 42, 52, 62, 72, 102, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 0,20,40,60,80 @integer 0, 20, 40, 60, 80, 100, 120, 140, 1000, 10000, 100000, 1000000, …","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 3~10, 13~19, 23, 103, 1003, …"},ha:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},haw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},he:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hy:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ia:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},id:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ig:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ii:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},in:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},io:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},is:{"pluralRule-count-one":"t = 0 and i % 10 = 1 and i % 100 != 11 or t != 0 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1~1.6, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},it:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ja:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jbo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ji:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jmc:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jv:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jw:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ka:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kab:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kaj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kcg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kde:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kea:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kkj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kl:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},km:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ko:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ks:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksh:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ku:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kw:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n % 100 = 2,22,42,62,82 or n % 1000 = 0 and n % 100000 = 1000..20000,40000,60000,80000 or n != 0 and n % 1000000 = 100000 @integer 2, 22, 42, 62, 82, 102, 122, 142, 1000, 10000, 100000, … @decimal 2.0, 22.0, 42.0, 62.0, 82.0, 102.0, 122.0, 142.0, 1000.0, 10000.0, 100000.0, …","pluralRule-count-few":"n % 100 = 3,23,43,63,83 @integer 3, 23, 43, 63, 83, 103, 123, 143, 1003, … @decimal 3.0, 23.0, 43.0, 63.0, 83.0, 103.0, 123.0, 143.0, 1003.0, …","pluralRule-count-many":"n != 1 and n % 100 = 1,21,41,61,81 @integer 21, 41, 61, 81, 101, 121, 141, 161, 1001, … @decimal 21.0, 41.0, 61.0, 81.0, 101.0, 121.0, 141.0, 161.0, 1001.0, …","pluralRule-count-other":" @integer 4~19, 100, 1004, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.1, 1000000.0, …"},ky:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lag:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"i = 0,1 and n != 0 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lkt:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ln:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lt:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11..19 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..9 and n % 100 != 11..19 @integer 2~9, 22~29, 102, 1002, … @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 22.0, 102.0, 1002.0, …","pluralRule-count-many":"f != 0   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lv:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},mas:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mg:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.2~1.0, 1.2~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ml:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mo:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},mr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ms:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mt:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-few":"n = 0 or n % 100 = 2..10 @integer 0, 2~10, 102~107, 1002, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 100 = 11..19 @integer 11~19, 111~117, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},my:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nah:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},naq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ne:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nnh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},no:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nqo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nso:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ny:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nyn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},om:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},or:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},os:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},osa:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pap:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i != 1 and i % 10 = 0..1 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 12..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},prg:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},ps:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pt:{"pluralRule-count-one":"i = 0..1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},"pt-PT":{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rm:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ro:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},rof:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},root:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ru:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rwk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sah:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},saq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sc:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},scn:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sdh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},se:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},seh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ses:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sg:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sh:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},shi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-few":"n = 2..10 @integer 2~10 @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 8.00","pluralRule-count-other":" @integer 11~26, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~1.9, 2.1~2.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},si:{"pluralRule-count-one":"n = 0,1 or i = 0 and f = 1 @integer 0, 1 @decimal 0.0, 0.1, 1.0, 0.00, 0.01, 1.00, 0.000, 0.001, 1.000, 0.0000, 0.0001, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.2~0.9, 1.1~1.8, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sk:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sl:{"pluralRule-count-one":"v = 0 and i % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, …","pluralRule-count-two":"v = 0 and i % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or v != 0 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sma:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smi:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sms:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},so:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ss:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ssy:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},st:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},su:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sv:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},syr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ta:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},te:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},teo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},th:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ti:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tig:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tl:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},tn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},to:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ts:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tzm:{"pluralRule-count-one":"n = 0..1 or n = 11..99 @integer 0, 1, 11~24 @decimal 0.0, 1.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0","pluralRule-count-other":" @integer 2~10, 100~106, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ug:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ur:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uz:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ve:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vi:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vun:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wae:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xog:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yue:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zh:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"}}}},{main:{fr:{identity:{version:{_cldrVersion:"36"},language:"fr"},units:{long:{per:{compoundUnitPattern:"{0} par {1}"},times:{compoundUnitPattern:"{0}{1}"},"acceleration-g-force":{displayName:"accélération de pesanteur terrestre","unitPattern-count-one":"{0} fois l’accélération de pesanteur terrestre","unitPattern-count-other":"{0} fois l’accélération de pesanteur terrestre"},"acceleration-meter-per-second-squared":{displayName:"mètres par seconde carrée","unitPattern-count-one":"{0} mètre par seconde carrée","unitPattern-count-other":"{0} mètres par seconde carrée"},"angle-revolution":{displayName:"tour","unitPattern-count-one":"{0} tour","unitPattern-count-other":"{0} tours"},"angle-radian":{displayName:"radians","unitPattern-count-one":"{0} radian","unitPattern-count-other":"{0} radians"},"angle-degree":{displayName:"degrés","unitPattern-count-one":"{0} degré","unitPattern-count-other":"{0} degrés"},"angle-arc-minute":{displayName:"minutes d’arc","unitPattern-count-one":"{0} minute d’arc","unitPattern-count-other":"{0} minutes d’arc"},"angle-arc-second":{displayName:"secondes d’arc","unitPattern-count-one":"{0} seconde d’arc","unitPattern-count-other":"{0} secondes d’arc"},"area-square-kilometer":{displayName:"kilomètres carrés","unitPattern-count-one":"{0} kilomètre carré","unitPattern-count-other":"{0} kilomètres carrés",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"hectares","unitPattern-count-one":"{0} hectare","unitPattern-count-other":"{0} hectares"},"area-square-meter":{displayName:"mètres carrés","unitPattern-count-one":"{0} mètre carré","unitPattern-count-other":"{0} mètres carrés",perUnitPattern:"{0} par mètre carré"},"area-square-centimeter":{displayName:"centimètres carrés","unitPattern-count-one":"{0} centimètre carré","unitPattern-count-other":"{0} centimètres carrés",perUnitPattern:"{0} par centimètre carré"},"area-square-mile":{displayName:"milles carrés","unitPattern-count-one":"{0} mille carré","unitPattern-count-other":"{0} milles carrés",perUnitPattern:"{0}/mi²"},"area-acre":{displayName:"acres anglo-saxonnes","unitPattern-count-one":"{0} acre anglo-saxonne","unitPattern-count-other":"{0} acres anglo-saxonnes"},"area-square-yard":{displayName:"yards carrés","unitPattern-count-one":"{0} yard carré","unitPattern-count-other":"{0} yards carrés"},"area-square-foot":{displayName:"pieds carrés","unitPattern-count-one":"{0} pied carré","unitPattern-count-other":"{0} pieds carrés"},"area-square-inch":{displayName:"pouces carrés","unitPattern-count-one":"{0} pouce carré","unitPattern-count-other":"{0} pouces carrés",perUnitPattern:"{0} par pouce carré"},"area-dunam":{displayName:"dounams","unitPattern-count-one":"{0} dounam","unitPattern-count-other":"{0} dounams"},"concentr-karat":{displayName:"carats","unitPattern-count-one":"{0} carat","unitPattern-count-other":"{0} carats"},"concentr-milligram-per-deciliter":{displayName:"milligrammes par décilitre","unitPattern-count-one":"{0} milligramme par décilitre","unitPattern-count-other":"{0} milligrammes par décilitre"},"concentr-millimole-per-liter":{displayName:"millimoles par litre","unitPattern-count-one":"{0} millimole par litre","unitPattern-count-other":"{0} millimoles par litre"},"concentr-part-per-million":{displayName:"parts par million","unitPattern-count-one":"{0} part par million","unitPattern-count-other":"{0} parts par million"},"concentr-percent":{displayName:"pour cent","unitPattern-count-one":"{0} pour cent","unitPattern-count-other":"{0} pour cent"},"concentr-permille":{displayName:"pour mille","unitPattern-count-one":"{0} pour mille","unitPattern-count-other":"{0} pour mille"},"concentr-permyriad":{displayName:"pour dix mille","unitPattern-count-one":"{0} pour dix mille","unitPattern-count-other":"{0} pour dix mille"},"concentr-mole":{displayName:"moles","unitPattern-count-one":"{0} mole","unitPattern-count-other":"{0} moles"},"consumption-liter-per-kilometer":{displayName:"litres au kilomètre","unitPattern-count-one":"{0} litre au kilomètre","unitPattern-count-other":"{0} litres au kilomètre"},"consumption-liter-per-100kilometers":{displayName:"litres aux 100 km","unitPattern-count-one":"{0} litre aux 100 km","unitPattern-count-other":"{0} litres aux 100 km"},"consumption-mile-per-gallon":{displayName:"miles par gallon","unitPattern-count-one":"{0} mile par gallon","unitPattern-count-other":"{0} miles par gallon"},"consumption-mile-per-gallon-imperial":{displayName:"miles par gallon impérial","unitPattern-count-one":"{0} mile par gallon impérial","unitPattern-count-other":"{0} miles par gallon impérial"},"digital-petabyte":{displayName:"pétaoctets","unitPattern-count-one":"{0} pétaoctet","unitPattern-count-other":"{0} pétaoctets"},"digital-terabyte":{displayName:"téraoctets","unitPattern-count-one":"{0} téraoctet","unitPattern-count-other":"{0} téraoctets"},"digital-terabit":{displayName:"térabits","unitPattern-count-one":"{0} térabit","unitPattern-count-other":"{0} térabits"},"digital-gigabyte":{displayName:"gigaoctets","unitPattern-count-one":"{0} gigaoctet","unitPattern-count-other":"{0} gigaoctets"},"digital-gigabit":{displayName:"gigabits","unitPattern-count-one":"{0} gigabit","unitPattern-count-other":"{0} gigabits"},"digital-megabyte":{displayName:"mégaoctets","unitPattern-count-one":"{0} mégaoctet","unitPattern-count-other":"{0} mégaoctets"},"digital-megabit":{displayName:"mégabits","unitPattern-count-one":"{0} mégabit","unitPattern-count-other":"{0} mégabits"},"digital-kilobyte":{displayName:"kilooctets","unitPattern-count-one":"{0} kilooctet","unitPattern-count-other":"{0} kilooctets"},"digital-kilobit":{displayName:"kilobits","unitPattern-count-one":"{0} kilobit","unitPattern-count-other":"{0} kilobits"},"digital-byte":{displayName:"octets","unitPattern-count-one":"{0} octet","unitPattern-count-other":"{0} octets"},"digital-bit":{displayName:"bits","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bits"},"duration-century":{displayName:"siècles","unitPattern-count-one":"{0} siècle","unitPattern-count-other":"{0} siècles"},"duration-decade":{displayName:"décennies","unitPattern-count-one":"{0} décennie","unitPattern-count-other":"{0} décennies"},"duration-year":{displayName:"ans","unitPattern-count-one":"{0} an","unitPattern-count-other":"{0} ans",perUnitPattern:"{0} par an"},"duration-month":{displayName:"mois","unitPattern-count-one":"{0} mois","unitPattern-count-other":"{0} mois",perUnitPattern:"{0} par mois"},"duration-week":{displayName:"semaines","unitPattern-count-one":"{0} semaine","unitPattern-count-other":"{0} semaines",perUnitPattern:"{0} par semaine"},"duration-day":{displayName:"jours","unitPattern-count-one":"{0} jour","unitPattern-count-other":"{0} jours",perUnitPattern:"{0} par jour"},"duration-hour":{displayName:"heures","unitPattern-count-one":"{0} heure","unitPattern-count-other":"{0} heures",perUnitPattern:"{0} par heure"},"duration-minute":{displayName:"minutes","unitPattern-count-one":"{0} minute","unitPattern-count-other":"{0} minutes",perUnitPattern:"{0} par minute"},"duration-second":{displayName:"secondes","unitPattern-count-one":"{0} seconde","unitPattern-count-other":"{0} secondes",perUnitPattern:"{0} par seconde"},"duration-millisecond":{displayName:"millisecondes","unitPattern-count-one":"{0} milliseconde","unitPattern-count-other":"{0} millisecondes"},"duration-microsecond":{displayName:"microsecondes","unitPattern-count-one":"{0} microseconde","unitPattern-count-other":"{0} microsecondes"},"duration-nanosecond":{displayName:"nanosecondes","unitPattern-count-one":"{0} nanoseconde","unitPattern-count-other":"{0} nanosecondes"},"electric-ampere":{displayName:"ampères","unitPattern-count-one":"{0} ampère","unitPattern-count-other":"{0} ampères"},"electric-milliampere":{displayName:"milliampères","unitPattern-count-one":"{0} milliampère","unitPattern-count-other":"{0} milliampères"},"electric-ohm":{displayName:"ohms","unitPattern-count-one":"{0} ohm","unitPattern-count-other":"{0} ohms"},"electric-volt":{displayName:"volts","unitPattern-count-one":"{0} volt","unitPattern-count-other":"{0} volts"},"energy-kilocalorie":{displayName:"kilocalories","unitPattern-count-one":"{0} kilocalorie","unitPattern-count-other":"{0} kilocalories"},"energy-calorie":{displayName:"calories","unitPattern-count-one":"{0} calorie","unitPattern-count-other":"{0} calories"},"energy-foodcalorie":{displayName:"kilocalories","unitPattern-count-one":"{0} kilocalorie","unitPattern-count-other":"{0} kilocalories"},"energy-kilojoule":{displayName:"kilojoules","unitPattern-count-one":"{0} kilojoule","unitPattern-count-other":"{0} kilojoules"},"energy-joule":{displayName:"joules","unitPattern-count-one":"{0} joule","unitPattern-count-other":"{0} joules"},"energy-kilowatt-hour":{displayName:"kilowattheures","unitPattern-count-one":"{0} kilowattheure","unitPattern-count-other":"{0} kilowattheures"},"energy-electronvolt":{displayName:"électronvolts","unitPattern-count-one":"{0} électronvolt","unitPattern-count-other":"{0} électronvolts"},"energy-british-thermal-unit":{displayName:"British Thermal Units","unitPattern-count-one":"{0} British Thermal Unit","unitPattern-count-other":"{0} British Thermal Units"},"energy-therm-us":{displayName:"therms US","unitPattern-count-one":"{0} therm US","unitPattern-count-other":"{0} therms US"},"force-pound-force":{displayName:"livres-force","unitPattern-count-one":"{0} livre-force","unitPattern-count-other":"{0} livres-force"},"force-newton":{displayName:"newtons","unitPattern-count-one":"{0} newton","unitPattern-count-other":"{0} newtons"},"frequency-gigahertz":{displayName:"gigahertz","unitPattern-count-one":"{0} gigahertz","unitPattern-count-other":"{0} gigahertz"},"frequency-megahertz":{displayName:"mégahertz","unitPattern-count-one":"{0} mégahertz","unitPattern-count-other":"{0} mégahertz"},"frequency-kilohertz":{displayName:"kilohertz","unitPattern-count-one":"{0} kilohertz","unitPattern-count-other":"{0} kilohertz"},"frequency-hertz":{displayName:"hertz","unitPattern-count-one":"{0} hertz","unitPattern-count-other":"{0} hertz"},"graphics-em":{displayName:"cadratin","unitPattern-count-one":"{0} cadratin","unitPattern-count-other":"{0} cadratins"},"graphics-pixel":{displayName:"pixels","unitPattern-count-one":"{0} pixel","unitPattern-count-other":"{0} pixels"},"graphics-megapixel":{displayName:"mégapixels","unitPattern-count-one":"{0} mégapixel","unitPattern-count-other":"{0} mégapixels"},"graphics-pixel-per-centimeter":{displayName:"pixels par centimètre","unitPattern-count-one":"{0} pixel par centimètre","unitPattern-count-other":"{0} pixels par centimètre"},"graphics-pixel-per-inch":{displayName:"pixels par pouce","unitPattern-count-one":"{0} pixel par pouce","unitPattern-count-other":"{0} pixels par pouce"},"graphics-dot-per-centimeter":{displayName:"points par centimètre","unitPattern-count-one":"{0} point par centimètre","unitPattern-count-other":"{0} points par centimètre"},"graphics-dot-per-inch":{displayName:"points par pouce","unitPattern-count-one":"{0} point par pouce","unitPattern-count-other":"{0} points par pouce"},"length-kilometer":{displayName:"kilomètres","unitPattern-count-one":"{0} kilomètre","unitPattern-count-other":"{0} kilomètres",perUnitPattern:"{0} par kilomètre"},"length-meter":{displayName:"mètres","unitPattern-count-one":"{0} mètre","unitPattern-count-other":"{0} mètres",perUnitPattern:"{0} par mètre"},"length-decimeter":{displayName:"décimètres","unitPattern-count-one":"{0} décimètre","unitPattern-count-other":"{0} décimètres"},"length-centimeter":{displayName:"centimètres","unitPattern-count-one":"{0} centimètre","unitPattern-count-other":"{0} centimètres",perUnitPattern:"{0} par centimètre"},"length-millimeter":{displayName:"millimètres","unitPattern-count-one":"{0} millimètre","unitPattern-count-other":"{0} millimètres"},"length-micrometer":{displayName:"micromètres","unitPattern-count-one":"{0} micromètre","unitPattern-count-other":"{0} micromètres"},"length-nanometer":{displayName:"nanomètres","unitPattern-count-one":"{0} nanomètre","unitPattern-count-other":"{0} nanomètres"},"length-picometer":{displayName:"picomètres","unitPattern-count-one":"{0} picomètre","unitPattern-count-other":"{0} picomètres"},"length-mile":{displayName:"miles","unitPattern-count-one":"{0} mile","unitPattern-count-other":"{0} miles"},"length-yard":{displayName:"yards","unitPattern-count-one":"{0} yard","unitPattern-count-other":"{0} yards"},"length-foot":{displayName:"pieds","unitPattern-count-one":"{0} pied","unitPattern-count-other":"{0} pieds",perUnitPattern:"{0} par pied"},"length-inch":{displayName:"pouces","unitPattern-count-one":"{0} pouce","unitPattern-count-other":"{0} pouces",perUnitPattern:"{0} par pouce"},"length-parsec":{displayName:"parsecs","unitPattern-count-one":"{0} parsec","unitPattern-count-other":"{0} parsecs"},"length-light-year":{displayName:"années-lumière","unitPattern-count-one":"{0} année-lumière","unitPattern-count-other":"{0} années-lumière"},"length-astronomical-unit":{displayName:"unités astronomiques","unitPattern-count-one":"{0} unité astronomique","unitPattern-count-other":"{0} unités astronomiques"},"length-furlong":{displayName:"furlongs","unitPattern-count-one":"{0} furlong","unitPattern-count-other":"{0} furlongs"},"length-fathom":{displayName:"brasses","unitPattern-count-one":"{0} brasse","unitPattern-count-other":"{0} brasses"},"length-nautical-mile":{displayName:"milles marins","unitPattern-count-one":"{0} mille marin","unitPattern-count-other":"{0} milles marins"},"length-mile-scandinavian":{displayName:"milles scandinaves","unitPattern-count-one":"{0} mille scandinave","unitPattern-count-other":"{0} milles scandinaves"},"length-point":{displayName:"points","unitPattern-count-one":"{0} point","unitPattern-count-other":"{0} points"},"length-solar-radius":{displayName:"rayons solaires","unitPattern-count-one":"{0} rayon solaire","unitPattern-count-other":"{0} rayons solaires"},"light-lux":{displayName:"lux","unitPattern-count-one":"{0} lux","unitPattern-count-other":"{0} lux"},"light-solar-luminosity":{displayName:"luminosités solaires","unitPattern-count-one":"{0} luminosité solaire","unitPattern-count-other":"{0} luminosités solaires"},"mass-metric-ton":{displayName:"tonnes","unitPattern-count-one":"{0} tonne","unitPattern-count-other":"{0} tonnes"},"mass-kilogram":{displayName:"kilogrammes","unitPattern-count-one":"{0} kilogramme","unitPattern-count-other":"{0} kilogrammes",perUnitPattern:"{0} par kg"},"mass-gram":{displayName:"grammes","unitPattern-count-one":"{0} gramme","unitPattern-count-other":"{0} grammes",perUnitPattern:"{0} par gramme"},"mass-milligram":{displayName:"milligrammes","unitPattern-count-one":"{0} milligramme","unitPattern-count-other":"{0} milligrammes"},"mass-microgram":{displayName:"microgrammes","unitPattern-count-one":"{0} microgramme","unitPattern-count-other":"{0} microgrammes"},"mass-ton":{displayName:"tonnes courtes","unitPattern-count-one":"{0} tonne courte","unitPattern-count-other":"{0} tonnes courtes"},"mass-stone":{displayName:"stones","unitPattern-count-one":"{0} stone","unitPattern-count-other":"{0} stones"},"mass-pound":{displayName:"livres","unitPattern-count-one":"{0} livre","unitPattern-count-other":"{0} livres",perUnitPattern:"{0} par livre"},"mass-ounce":{displayName:"onces","unitPattern-count-one":"{0} once","unitPattern-count-other":"{0} onces",perUnitPattern:"{0} par once"},"mass-ounce-troy":{displayName:"onces troy","unitPattern-count-one":"{0} once troy","unitPattern-count-other":"{0} onces troy"},"mass-carat":{displayName:"carats","unitPattern-count-one":"{0} carat","unitPattern-count-other":"{0} carats"},"mass-dalton":{displayName:"daltons","unitPattern-count-one":"{0} dalton","unitPattern-count-other":"{0} daltons"},"mass-earth-mass":{displayName:"masses terrestres","unitPattern-count-one":"{0} masse terrestre","unitPattern-count-other":"{0} masses terrestres"},"mass-solar-mass":{displayName:"masses solaires","unitPattern-count-one":"{0} masse solaire","unitPattern-count-other":"{0} masses solaires"},"power-gigawatt":{displayName:"gigawatts","unitPattern-count-one":"{0} gigawatt","unitPattern-count-other":"{0} gigawatts"},"power-megawatt":{displayName:"mégawatts","unitPattern-count-one":"{0} mégawatt","unitPattern-count-other":"{0} mégawatts"},"power-kilowatt":{displayName:"kilowatts","unitPattern-count-one":"{0} kilowatt","unitPattern-count-other":"{0} kilowatts"},"power-watt":{displayName:"watts","unitPattern-count-one":"{0} watt","unitPattern-count-other":"{0} watts"},"power-milliwatt":{displayName:"milliwatts","unitPattern-count-one":"{0} milliwatt","unitPattern-count-other":"{0} milliwatts"},"power-horsepower":{displayName:"chevaux-vapeur","unitPattern-count-one":"{0} cheval-vapeur","unitPattern-count-other":"{0} chevaux-vapeur"},"pressure-millimeter-of-mercury":{displayName:"millimètres de mercure","unitPattern-count-one":"{0} millimètre de mercure","unitPattern-count-other":"{0} millimètres de mercure"},"pressure-pound-per-square-inch":{displayName:"livres par pouce carré","unitPattern-count-one":"{0} livre par pouce carré","unitPattern-count-other":"{0} livres par pouce carré"},"pressure-inch-hg":{displayName:"pouces de mercure","unitPattern-count-one":"{0} pouce de mercure","unitPattern-count-other":"{0} pouces de mercure"},"pressure-bar":{displayName:"bars","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"millibars","unitPattern-count-one":"{0} millibar","unitPattern-count-other":"{0} millibars"},"pressure-atmosphere":{displayName:"atmosphères","unitPattern-count-one":"{0} atmosphère","unitPattern-count-other":"{0} atmosphères"},"pressure-pascal":{displayName:"pascals","unitPattern-count-one":"{0} pascal","unitPattern-count-other":"{0} pascals"},"pressure-hectopascal":{displayName:"hectopascals","unitPattern-count-one":"{0} hectopascal","unitPattern-count-other":"{0} hectopascals"},"pressure-kilopascal":{displayName:"kilopascals","unitPattern-count-one":"{0} kilopascal","unitPattern-count-other":"{0} kilopascals"},"pressure-megapascal":{displayName:"mégapascals","unitPattern-count-one":"{0} mégapascal","unitPattern-count-other":"{0} mégapascals"},"speed-kilometer-per-hour":{displayName:"kilomètres par heure","unitPattern-count-one":"{0} kilomètre par heure","unitPattern-count-other":"{0} kilomètres par heure"},"speed-meter-per-second":{displayName:"mètres par seconde","unitPattern-count-one":"{0} mètre par seconde","unitPattern-count-other":"{0} mètres par seconde"},"speed-mile-per-hour":{displayName:"miles par heure","unitPattern-count-one":"{0} mile par heure","unitPattern-count-other":"{0} miles par heure"},"speed-knot":{displayName:"nœuds","unitPattern-count-one":"{0} nœud","unitPattern-count-other":"{0} nœuds"},"temperature-generic":{displayName:"degrés","unitPattern-count-one":"{0} degré","unitPattern-count-other":"{0} degrés"},"temperature-celsius":{displayName:"degrés Celsius","unitPattern-count-one":"{0} degré Celsius","unitPattern-count-other":"{0} degrés Celsius"},"temperature-fahrenheit":{displayName:"degrés Fahrenheit","unitPattern-count-one":"{0} degré Fahrenheit","unitPattern-count-other":"{0} degrés Fahrenheit"},"temperature-kelvin":{displayName:"kelvins","unitPattern-count-one":"{0} kelvin","unitPattern-count-other":"{0} kelvins"},"torque-pound-foot":{displayName:"livres-pieds","unitPattern-count-one":"{0} livre-pied","unitPattern-count-other":"{0} livres-pieds"},"torque-newton-meter":{displayName:"newtons-mètres","unitPattern-count-one":"{0} newton-mètre","unitPattern-count-other":"{0} newtons-mètres"},"volume-cubic-kilometer":{displayName:"kilomètres cubes","unitPattern-count-one":"{0} kilomètre cube","unitPattern-count-other":"{0} kilomètres cubes"},"volume-cubic-meter":{displayName:"mètres cubes","unitPattern-count-one":"{0} mètre cube","unitPattern-count-other":"{0} mètres cubes",perUnitPattern:"{0} par mètre cube"},"volume-cubic-centimeter":{displayName:"centimètres cubes","unitPattern-count-one":"{0} centimètre cube","unitPattern-count-other":"{0} centimètres cubes",perUnitPattern:"{0} par centimètre cube"},"volume-cubic-mile":{displayName:"milles cubes","unitPattern-count-one":"{0} mille cube","unitPattern-count-other":"{0} milles cubes"},"volume-cubic-yard":{displayName:"yards cubes","unitPattern-count-one":"{0} yard cube","unitPattern-count-other":"{0} yards cubes"},"volume-cubic-foot":{displayName:"pieds cubes","unitPattern-count-one":"{0} pied cube","unitPattern-count-other":"{0} pieds cubes"},"volume-cubic-inch":{displayName:"pouces cubes","unitPattern-count-one":"{0} pouce cube","unitPattern-count-other":"{0} pouces cubes"},"volume-megaliter":{displayName:"mégalitres","unitPattern-count-one":"{0} mégalitre","unitPattern-count-other":"{0} mégalitres"},"volume-hectoliter":{displayName:"hectolitres","unitPattern-count-one":"{0} hectolitre","unitPattern-count-other":"{0} hectolitres"},"volume-liter":{displayName:"litres","unitPattern-count-one":"{0} litre","unitPattern-count-other":"{0} litres",perUnitPattern:"{0} par litre"},"volume-deciliter":{displayName:"décilitres","unitPattern-count-one":"{0} décilitre","unitPattern-count-other":"{0} décilitres"},"volume-centiliter":{displayName:"centilitres","unitPattern-count-one":"{0} centilitre","unitPattern-count-other":"{0} centilitres"},"volume-milliliter":{displayName:"millilitres","unitPattern-count-one":"{0} millilitre","unitPattern-count-other":"{0} millilitres"},"volume-pint-metric":{displayName:"pintes métriques","unitPattern-count-one":"{0} pinte métrique","unitPattern-count-other":"{0} pintes métriques"},"volume-cup-metric":{displayName:"tasses métriques","unitPattern-count-one":"{0} tasse métrique","unitPattern-count-other":"{0} tasses métriques"},"volume-acre-foot":{displayName:"acres-pieds","unitPattern-count-one":"{0} acre-pied","unitPattern-count-other":"{0} acres-pieds"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gallons","unitPattern-count-one":"{0} gallon","unitPattern-count-other":"{0} gallons",perUnitPattern:"{0} par gallon"},"volume-gallon-imperial":{displayName:"gallons impériaux","unitPattern-count-one":"{0} gallon impérial","unitPattern-count-other":"{0} gallons impériaux",perUnitPattern:"{0} par gallon impérial"},"volume-quart":{displayName:"quarts","unitPattern-count-one":"{0} quart","unitPattern-count-other":"{0} quarts"},"volume-pint":{displayName:"pintes","unitPattern-count-one":"{0} pinte","unitPattern-count-other":"{0} pintes"},"volume-cup":{displayName:"tasses","unitPattern-count-one":"{0} tasse","unitPattern-count-other":"{0} tasses"},"volume-fluid-ounce":{displayName:"onces liquides","unitPattern-count-one":"{0} once liquide","unitPattern-count-other":"{0} onces liquides"},"volume-fluid-ounce-imperial":{displayName:"onces liquides impériales","unitPattern-count-one":"{0} once liquide impériale","unitPattern-count-other":"{0} onces liquides impériales"},"volume-tablespoon":{displayName:"cuillères à soupe","unitPattern-count-one":"{0} cuillère à soupe","unitPattern-count-other":"{0} cuillères à soupe"},"volume-teaspoon":{displayName:"cuillères à café","unitPattern-count-one":"{0} cuillère à café","unitPattern-count-other":"{0} cuillères à café"},"volume-barrel":{displayName:"barils","unitPattern-count-one":"{0} baril","unitPattern-count-other":"{0} barils"},coordinateUnit:{displayName:"direction",east:"{0} est",north:"{0} nord",south:"{0} sud",west:"{0} ouest"}},short:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}{1}"},"acceleration-g-force":{displayName:"force g","unitPattern-count-one":"{0} force g","unitPattern-count-other":"{0} force g"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-one":"{0} m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"tr","unitPattern-count-one":"{0} tr","unitPattern-count-other":"{0} tr"},"angle-radian":{displayName:"rad","unitPattern-count-one":"{0} rad","unitPattern-count-other":"{0} rad"},"angle-degree":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"′","unitPattern-count-one":"{0}′","unitPattern-count-other":"{0}′"},"angle-arc-second":{displayName:"″","unitPattern-count-one":"{0}″","unitPattern-count-other":"{0}″"},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0} km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"ha","unitPattern-count-one":"{0} ha","unitPattern-count-other":"{0} ha"},"area-square-meter":{displayName:"m²","unitPattern-count-one":"{0} m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0} cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"mi²","unitPattern-count-one":"{0} mi²","unitPattern-count-other":"{0} mi²",perUnitPattern:"{0}/mi²"},"area-acre":{displayName:"ac","unitPattern-count-one":"{0} ac","unitPattern-count-other":"{0} ac"},"area-square-yard":{displayName:"yd²","unitPattern-count-one":"{0} yd²","unitPattern-count-other":"{0} yd²"},"area-square-foot":{displayName:"pi²","unitPattern-count-one":"{0} pi²","unitPattern-count-other":"{0} pi²"},"area-square-inch":{displayName:"po²","unitPattern-count-one":"{0} po²","unitPattern-count-other":"{0} po²",perUnitPattern:"{0}/po²"},"area-dunam":{displayName:"dounam","unitPattern-count-one":"{0} dounam","unitPattern-count-other":"{0} dounam"},"concentr-karat":{displayName:"ct","unitPattern-count-one":"{0} ct","unitPattern-count-other":"{0} ct"},"concentr-milligram-per-deciliter":{displayName:"mg/dl","unitPattern-count-one":"{0} mg/dl","unitPattern-count-other":"{0} mg/dl"},"concentr-millimole-per-liter":{displayName:"mmol/l","unitPattern-count-one":"{0} mmol/l","unitPattern-count-other":"{0} mmol/l"},"concentr-part-per-million":{displayName:"ppm","unitPattern-count-one":"{0} ppm","unitPattern-count-other":"{0} ppm"},"concentr-percent":{displayName:"%","unitPattern-count-one":"{0} %","unitPattern-count-other":"{0} %"},"concentr-permille":{displayName:"‰","unitPattern-count-one":"{0} ‰","unitPattern-count-other":"{0} ‰"},"concentr-permyriad":{displayName:"‱","unitPattern-count-one":"{0} ‱","unitPattern-count-other":"{0} ‱"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"l/km","unitPattern-count-one":"{0} l/km","unitPattern-count-other":"{0} l/km"},"consumption-liter-per-100kilometers":{displayName:"l/100 km","unitPattern-count-one":"{0} l/100 km","unitPattern-count-other":"{0} l/100 km"},"consumption-mile-per-gallon":{displayName:"mi/gal","unitPattern-count-one":"{0} mi/gal","unitPattern-count-other":"{0} mi/gal"},"consumption-mile-per-gallon-imperial":{displayName:"mi/gal imp.","unitPattern-count-one":"{0} mi/gal imp.","unitPattern-count-other":"{0} mi/gal imp."},"digital-petabyte":{displayName:"Po","unitPattern-count-one":"{0} Po","unitPattern-count-other":"{0} Po"},"digital-terabyte":{displayName:"To","unitPattern-count-one":"{0} To","unitPattern-count-other":"{0} To"},"digital-terabit":{displayName:"Tbit","unitPattern-count-one":"{0} Tbit","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"Go","unitPattern-count-one":"{0} Go","unitPattern-count-other":"{0} Go"},"digital-gigabit":{displayName:"Gbit","unitPattern-count-one":"{0} Gbit","unitPattern-count-other":"{0} Gbit"},"digital-megabyte":{displayName:"Mo","unitPattern-count-one":"{0} Mo","unitPattern-count-other":"{0} Mo"},"digital-megabit":{displayName:"Mbit","unitPattern-count-one":"{0} Mbit","unitPattern-count-other":"{0} Mbit"},"digital-kilobyte":{displayName:"ko","unitPattern-count-one":"{0} ko","unitPattern-count-other":"{0} ko"},"digital-kilobit":{displayName:"kbit","unitPattern-count-one":"{0} kbit","unitPattern-count-other":"{0} kbit"},"digital-byte":{displayName:"octet","unitPattern-count-one":"{0} o","unitPattern-count-other":"{0} o"},"digital-bit":{displayName:"bit","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bit"},"duration-century":{displayName:"siècles","unitPattern-count-one":"{0} siècle","unitPattern-count-other":"{0} siècles"},"duration-decade":{displayName:"décennies","unitPattern-count-one":"{0} décennie","unitPattern-count-other":"{0} décennies"},"duration-year":{displayName:"ans","unitPattern-count-one":"{0} an","unitPattern-count-other":"{0} ans",perUnitPattern:"{0}/an"},"duration-month":{displayName:"m.","unitPattern-count-one":"{0} m.","unitPattern-count-other":"{0} m.",perUnitPattern:"{0}/m."},"duration-week":{displayName:"sem.","unitPattern-count-one":"{0} sem.","unitPattern-count-other":"{0} sem.",perUnitPattern:"{0}/sem."},"duration-day":{displayName:"j","unitPattern-count-one":"{0} j","unitPattern-count-other":"{0} j",perUnitPattern:"{0}/j"},"duration-hour":{displayName:"h","unitPattern-count-one":"{0} h","unitPattern-count-other":"{0} h",perUnitPattern:"{0}/h"},"duration-minute":{displayName:"min","unitPattern-count-one":"{0} min","unitPattern-count-other":"{0} min",perUnitPattern:"{0}/min"},"duration-second":{displayName:"s","unitPattern-count-one":"{0} s","unitPattern-count-other":"{0} s",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"ms","unitPattern-count-one":"{0} ms","unitPattern-count-other":"{0} ms"},"duration-microsecond":{displayName:"μs","unitPattern-count-one":"{0} μs","unitPattern-count-other":"{0} μs"},"duration-nanosecond":{displayName:"ns","unitPattern-count-one":"{0} ns","unitPattern-count-other":"{0} ns"},"electric-ampere":{displayName:"A","unitPattern-count-one":"{0} A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"mA","unitPattern-count-one":"{0} mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"Ω","unitPattern-count-one":"{0} Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"V","unitPattern-count-one":"{0} V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-calorie":{displayName:"cal","unitPattern-count-one":"{0} cal","unitPattern-count-other":"{0} cal"},"energy-foodcalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-kilojoule":{displayName:"kJ","unitPattern-count-one":"{0} kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"J","unitPattern-count-one":"{0} J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-one":"{0} kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"eV","unitPattern-count-one":"{0} eV","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0} Btu","unitPattern-count-other":"{0} Btu"},"energy-therm-us":{displayName:"therm US","unitPattern-count-one":"{0} therm US","unitPattern-count-other":"{0} therms US"},"force-pound-force":{displayName:"lbf","unitPattern-count-one":"{0} lbf","unitPattern-count-other":"{0} lbf"},"force-newton":{displayName:"N","unitPattern-count-one":"{0} N","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0} GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0} MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0} kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0} Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"em","unitPattern-count-one":"{0} em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"px","unitPattern-count-one":"{0} px","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"Mpx","unitPattern-count-one":"{0} Mpx","unitPattern-count-other":"{0} Mpx"},"graphics-pixel-per-centimeter":{displayName:"px/cm","unitPattern-count-one":"{0} px/cm","unitPattern-count-other":"{0} px/cm"},"graphics-pixel-per-inch":{displayName:"px/po","unitPattern-count-one":"{0} px/po","unitPattern-count-other":"{0} px/po"},"graphics-dot-per-centimeter":{displayName:"pt/cm","unitPattern-count-one":"{0} pt/cm","unitPattern-count-other":"{0} pt/cm"},"graphics-dot-per-inch":{displayName:"pt/po","unitPattern-count-one":"{0} pt/po","unitPattern-count-other":"{0} pt/po"},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0} km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"m","unitPattern-count-one":"{0} m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0} dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0} cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0} mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µm","unitPattern-count-one":"{0} µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0} nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0} pm","unitPattern-count-other":"{0} pm"},"length-mile":{displayName:"mi","unitPattern-count-one":"{0} mi","unitPattern-count-other":"{0} mi"},"length-yard":{displayName:"yd","unitPattern-count-one":"{0} yd","unitPattern-count-other":"{0} yd"},"length-foot":{displayName:"pi","unitPattern-count-one":"{0} pi","unitPattern-count-other":"{0} pi",perUnitPattern:"{0}/pi"},"length-inch":{displayName:"po","unitPattern-count-one":"{0} po","unitPattern-count-other":"{0} po",perUnitPattern:"{0}/po"},"length-parsec":{displayName:"pc","unitPattern-count-one":"{0} pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"al","unitPattern-count-one":"{0} al","unitPattern-count-other":"{0} al"},"length-astronomical-unit":{displayName:"ua","unitPattern-count-one":"{0} ua","unitPattern-count-other":"{0} ua"},"length-furlong":{displayName:"fur","unitPattern-count-one":"{0} fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-one":"{0} fth","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"nmi","unitPattern-count-one":"{0} nmi","unitPattern-count-other":"{0} nmi"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0} smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"points","unitPattern-count-one":"{0} pt","unitPattern-count-other":"{0} pt"},"length-solar-radius":{displayName:"R☉","unitPattern-count-one":"{0} R☉","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lx","unitPattern-count-one":"{0} lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"L☉","unitPattern-count-one":"{0} L☉","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0} t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0} kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"g","unitPattern-count-one":"{0} g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0} mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0} µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"sh tn","unitPattern-count-one":"{0} sh tn","unitPattern-count-other":"{0} sh tn"},"mass-stone":{displayName:"st","unitPattern-count-one":"{0} st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"lb","unitPattern-count-one":"{0} lb","unitPattern-count-other":"{0} lb",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz","unitPattern-count-one":"{0} oz","unitPattern-count-other":"{0} oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz t","unitPattern-count-one":"{0} oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"ct","unitPattern-count-one":"{0} ct","unitPattern-count-other":"{0} ct"},"mass-dalton":{displayName:"Da","unitPattern-count-one":"{0} Da","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"M⊕","unitPattern-count-one":"{0} M⊕","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"M☉","unitPattern-count-one":"{0} M☉","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0} GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0} MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0} kW","unitPattern-count-other":"{0} kW"},"power-watt":{displayName:"W","unitPattern-count-one":"{0} W","unitPattern-count-other":"{0} W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0} mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"ch","unitPattern-count-one":"{0} ch","unitPattern-count-other":"{0} ch"},"pressure-millimeter-of-mercury":{displayName:"mmHg","unitPattern-count-one":"{0} mmHg","unitPattern-count-other":"{0} mmHg"},"pressure-pound-per-square-inch":{displayName:"lb/po²","unitPattern-count-one":"{0} lb/po²","unitPattern-count-other":"{0} lb/po²"},"pressure-inch-hg":{displayName:"inHg","unitPattern-count-one":"{0} inHg","unitPattern-count-other":"{0} inHg"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"mbar","unitPattern-count-one":"{0} mbar","unitPattern-count-other":"{0} mbar"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-one":"{0} atm","unitPattern-count-other":"{0} atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-one":"{0} Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0} hPa","unitPattern-count-other":"{0} hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0} kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0} MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-one":"{0} km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"m/s","unitPattern-count-one":"{0} m/s","unitPattern-count-other":"{0} m/s"},"speed-mile-per-hour":{displayName:"mi/h","unitPattern-count-one":"{0} mi/h","unitPattern-count-other":"{0} mi/h"},"speed-knot":{displayName:"nd","unitPattern-count-one":"{0} nd","unitPattern-count-other":"{0} nd"},"temperature-generic":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"°C","unitPattern-count-one":"{0} °C","unitPattern-count-other":"{0} °C"},"temperature-fahrenheit":{displayName:"°F","unitPattern-count-one":"{0} °F","unitPattern-count-other":"{0} °F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0} K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-one":"{0} lbf⋅ft","unitPattern-count-other":"{0} lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-one":"{0} N⋅m","unitPattern-count-other":"{0} N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0} km³","unitPattern-count-other":"{0} km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0} m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0} cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mi³","unitPattern-count-one":"{0} mi³","unitPattern-count-other":"{0} mi³"},"volume-cubic-yard":{displayName:"yd³","unitPattern-count-one":"{0} yd³","unitPattern-count-other":"{0} yd³"},"volume-cubic-foot":{displayName:"pi³","unitPattern-count-one":"{0} pi³","unitPattern-count-other":"{0} pi³"},"volume-cubic-inch":{displayName:"po³","unitPattern-count-one":"{0} po³","unitPattern-count-other":"{0} po³"},"volume-megaliter":{displayName:"Ml","unitPattern-count-one":"{0} Ml","unitPattern-count-other":"{0} Ml"},"volume-hectoliter":{displayName:"hl","unitPattern-count-one":"{0} hl","unitPattern-count-other":"{0} hl"},"volume-liter":{displayName:"l","unitPattern-count-one":"{0} l","unitPattern-count-other":"{0} l",perUnitPattern:"{0}/l"},"volume-deciliter":{displayName:"dl","unitPattern-count-one":"{0} dl","unitPattern-count-other":"{0} dl"},"volume-centiliter":{displayName:"cl","unitPattern-count-one":"{0} cl","unitPattern-count-other":"{0} cl"},"volume-milliliter":{displayName:"ml","unitPattern-count-one":"{0} ml","unitPattern-count-other":"{0} ml"},"volume-pint-metric":{displayName:"mpt","unitPattern-count-one":"{0} mpt","unitPattern-count-other":"{0} mpt"},"volume-cup-metric":{displayName:"tm","unitPattern-count-one":"{0} tm","unitPattern-count-other":"{0} tm"},"volume-acre-foot":{displayName:"ac pi","unitPattern-count-one":"{0} ac pi","unitPattern-count-other":"{0} ac pi"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gal","unitPattern-count-one":"{0} gal","unitPattern-count-other":"{0} gal",perUnitPattern:"{0}/gal"},"volume-gallon-imperial":{displayName:"gal imp.","unitPattern-count-one":"{0} gal imp.","unitPattern-count-other":"{0} gal imp.",perUnitPattern:"{0} gal imp."},"volume-quart":{displayName:"qt","unitPattern-count-one":"{0} qt","unitPattern-count-other":"{0} qt"},"volume-pint":{displayName:"pte","unitPattern-count-one":"{0} pte","unitPattern-count-other":"{0} pte"},"volume-cup":{displayName:"tasses","unitPattern-count-one":"{0} tasse","unitPattern-count-other":"{0} tasses"},"volume-fluid-ounce":{displayName:"fl oz","unitPattern-count-one":"{0} fl oz","unitPattern-count-other":"{0} fl oz"},"volume-fluid-ounce-imperial":{displayName:"fl oz imp.","unitPattern-count-one":"{0} fl oz imp.","unitPattern-count-other":"{0} fl oz imp."},"volume-tablespoon":{displayName:"c. à s.","unitPattern-count-one":"{0} c. à s.","unitPattern-count-other":"{0} c. à s."},"volume-teaspoon":{displayName:"c. à c.","unitPattern-count-one":"{0} c. à c.","unitPattern-count-other":"{0} c. à c."},"volume-barrel":{displayName:"bbl","unitPattern-count-one":"{0} bbl","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"direction",east:"{0} E",north:"{0} N",south:"{0} S",west:"{0} O"}},narrow:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}{1}"},"acceleration-g-force":{displayName:"G","unitPattern-count-one":"{0}G","unitPattern-count-other":"{0}G"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-one":"{0} m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"tr","unitPattern-count-one":"{0} tr","unitPattern-count-other":"{0} tr"},"angle-radian":{displayName:"rad","unitPattern-count-one":"{0} rad","unitPattern-count-other":"{0} rad"},"angle-degree":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"′","unitPattern-count-one":"{0}′","unitPattern-count-other":"{0}′"},"angle-arc-second":{displayName:"″","unitPattern-count-one":"{0}″","unitPattern-count-other":"{0}″"},"area-square-kilometer":{displayName:"km²","unitPattern-count-one":"{0}km²","unitPattern-count-other":"{0}km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"ha","unitPattern-count-one":"{0}ha","unitPattern-count-other":"{0}ha"},"area-square-meter":{displayName:"m²","unitPattern-count-one":"{0}m²","unitPattern-count-other":"{0}m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-one":"{0} cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"mi²","unitPattern-count-one":"{0}mi²","unitPattern-count-other":"{0}mi²",perUnitPattern:"{0}/mi²"},"area-acre":{displayName:"ac","unitPattern-count-one":"{0}ac","unitPattern-count-other":"{0}ac"},"area-square-yard":{displayName:"yd²","unitPattern-count-one":"{0} yd²","unitPattern-count-other":"{0} yd²"},"area-square-foot":{displayName:"pi²","unitPattern-count-one":"{0}pi²","unitPattern-count-other":"{0}pi²"},"area-square-inch":{displayName:"po²","unitPattern-count-one":"{0} po²","unitPattern-count-other":"{0} po²",perUnitPattern:"{0}/po²"},"area-dunam":{displayName:"dounam","unitPattern-count-one":"{0} dounam","unitPattern-count-other":"{0} dounam"},"concentr-karat":{displayName:"ct","unitPattern-count-one":"{0} ct","unitPattern-count-other":"{0} ct"},"concentr-milligram-per-deciliter":{displayName:"mg/dl","unitPattern-count-one":"{0} mg/dl","unitPattern-count-other":"{0} mg/dl"},"concentr-millimole-per-liter":{displayName:"mmol/l","unitPattern-count-one":"{0} mmol/l","unitPattern-count-other":"{0} mmol/l"},"concentr-part-per-million":{displayName:"ppm","unitPattern-count-one":"{0} ppm","unitPattern-count-other":"{0} ppm"},"concentr-percent":{displayName:"%","unitPattern-count-one":"{0} %","unitPattern-count-other":"{0} %"},"concentr-permille":{displayName:"‰","unitPattern-count-one":"{0} ‰","unitPattern-count-other":"{0} ‰"},"concentr-permyriad":{displayName:"‱","unitPattern-count-one":"{0} ‱","unitPattern-count-other":"{0} ‱"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-other":"{0} mol"},"consumption-liter-per-kilometer":{displayName:"L/km","unitPattern-count-one":"{0} l/km","unitPattern-count-other":"{0} l/km"},"consumption-liter-per-100kilometers":{displayName:"l/100km","unitPattern-count-one":"{0}l/100km","unitPattern-count-other":"{0}l/100km"},"consumption-mile-per-gallon":{displayName:"mi/gal","unitPattern-count-one":"{0} mi/gal","unitPattern-count-other":"{0} mi/gal"},"consumption-mile-per-gallon-imperial":{displayName:"mi/gal imp.","unitPattern-count-one":"{0} mi/gal imp.","unitPattern-count-other":"{0} mi/gal imp."},"digital-petabyte":{displayName:"Po","unitPattern-count-one":"{0} Po","unitPattern-count-other":"{0} Po"},"digital-terabyte":{displayName:"To","unitPattern-count-one":"{0} To","unitPattern-count-other":"{0} To"},"digital-terabit":{displayName:"Tbit","unitPattern-count-one":"{0} Tbit","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"Go","unitPattern-count-one":"{0} Go","unitPattern-count-other":"{0} Go"},"digital-gigabit":{displayName:"Gbit","unitPattern-count-one":"{0} Gbit","unitPattern-count-other":"{0} Gbit"},"digital-megabyte":{displayName:"Mo","unitPattern-count-one":"{0} Mo","unitPattern-count-other":"{0} Mo"},"digital-megabit":{displayName:"Mbit","unitPattern-count-one":"{0} Mbit","unitPattern-count-other":"{0} Mbit"},"digital-kilobyte":{displayName:"ko","unitPattern-count-one":"{0} ko","unitPattern-count-other":"{0} ko"},"digital-kilobit":{displayName:"kbit","unitPattern-count-one":"{0} kbit","unitPattern-count-other":"{0} kbit"},"digital-byte":{displayName:"octet","unitPattern-count-one":"{0} o","unitPattern-count-other":"{0} o"},"digital-bit":{displayName:"bit","unitPattern-count-one":"{0} bit","unitPattern-count-other":"{0} bit"},"duration-century":{displayName:"s.","unitPattern-count-one":"{0} s.","unitPattern-count-other":"{0} s."},"duration-decade":{displayName:"décennies","unitPattern-count-one":"{0} décennie","unitPattern-count-other":"{0} décennies"},"duration-year":{displayName:"a","unitPattern-count-one":"{0}a","unitPattern-count-other":"{0}a",perUnitPattern:"{0}/a"},"duration-month":{displayName:"m.","unitPattern-count-one":"{0}m.","unitPattern-count-other":"{0}m.",perUnitPattern:"{0}/m."},"duration-week":{displayName:"sem.","unitPattern-count-one":"{0}sem.","unitPattern-count-other":"{0}sem.",perUnitPattern:"{0}/sem."},"duration-day":{displayName:"j","unitPattern-count-one":"{0}j","unitPattern-count-other":"{0}j",perUnitPattern:"{0}/j"},"duration-hour":{displayName:"h","unitPattern-count-one":"{0}h","unitPattern-count-other":"{0}h",perUnitPattern:"{0}/h"},"duration-minute":{displayName:"min","unitPattern-count-one":"{0}min","unitPattern-count-other":"{0}min",perUnitPattern:"{0}/min"},"duration-second":{displayName:"s","unitPattern-count-one":"{0}s","unitPattern-count-other":"{0}s",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"ms","unitPattern-count-one":"{0}ms","unitPattern-count-other":"{0}ms"},"duration-microsecond":{displayName:"μs","unitPattern-count-one":"{0} μs","unitPattern-count-other":"{0} μs"},"duration-nanosecond":{displayName:"ns","unitPattern-count-one":"{0} ns","unitPattern-count-other":"{0} ns"},"electric-ampere":{displayName:"A","unitPattern-count-one":"{0} A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"mA","unitPattern-count-one":"{0} mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"Ω","unitPattern-count-one":"{0} Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"V","unitPattern-count-one":"{0} V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-calorie":{displayName:"cal","unitPattern-count-one":"{0} cal","unitPattern-count-other":"{0} cal"},"energy-foodcalorie":{displayName:"kcal","unitPattern-count-one":"{0} kcal","unitPattern-count-other":"{0} kcal"},"energy-kilojoule":{displayName:"kJ","unitPattern-count-one":"{0} kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"J","unitPattern-count-one":"{0} J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-one":"{0} kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"eV","unitPattern-count-one":"{0} eV","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0} Btu","unitPattern-count-other":"{0} Btu"},"energy-therm-us":{displayName:"therm US","unitPattern-count-one":"{0} therm US","unitPattern-count-other":"{0} therms US"},"force-pound-force":{displayName:"lbf","unitPattern-count-one":"{0} lbf","unitPattern-count-other":"{0} lbf"},"force-newton":{displayName:"N","unitPattern-count-one":"{0} N","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-one":"{0} GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-one":"{0} MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-one":"{0} kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-one":"{0} Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"em","unitPattern-count-one":"{0} em","unitPattern-count-other":"{0} em"},"graphics-pixel":{displayName:"px","unitPattern-count-one":"{0} px","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"Mpx","unitPattern-count-one":"{0} Mpx","unitPattern-count-other":"{0} Mpx"},"graphics-pixel-per-centimeter":{displayName:"px/cm","unitPattern-count-one":"{0} px/cm","unitPattern-count-other":"{0} px/cm"},"graphics-pixel-per-inch":{displayName:"px/po","unitPattern-count-one":"{0} px/po","unitPattern-count-other":"{0} px/po"},"graphics-dot-per-centimeter":{displayName:"pt/cm","unitPattern-count-one":"{0} pt/cm","unitPattern-count-other":"{0} pt/cm"},"graphics-dot-per-inch":{displayName:"pt/po","unitPattern-count-one":"{0} pt/po","unitPattern-count-other":"{0} pt/po"},"length-kilometer":{displayName:"km","unitPattern-count-one":"{0}km","unitPattern-count-other":"{0}km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"m","unitPattern-count-one":"{0}m","unitPattern-count-other":"{0}m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-one":"{0} dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-one":"{0}cm","unitPattern-count-other":"{0}cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-one":"{0}mm","unitPattern-count-other":"{0}mm"},"length-micrometer":{displayName:"µm","unitPattern-count-one":"{0} µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-one":"{0} nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-one":"{0}pm","unitPattern-count-other":"{0}pm"},"length-mile":{displayName:"mi","unitPattern-count-one":"{0}mi","unitPattern-count-other":"{0}mi"},"length-yard":{displayName:"yd","unitPattern-count-one":"{0}yd","unitPattern-count-other":"{0}yd"},"length-foot":{displayName:"pi","unitPattern-count-one":"{0}′","unitPattern-count-other":"{0}′",perUnitPattern:"{0}/pi"},"length-inch":{displayName:"po","unitPattern-count-one":"{0}″","unitPattern-count-other":"{0}″",perUnitPattern:"{0}/po"},"length-parsec":{displayName:"pc","unitPattern-count-one":"{0} pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"al","unitPattern-count-one":"{0}a.l.","unitPattern-count-other":"{0}a.l."},"length-astronomical-unit":{displayName:"ua","unitPattern-count-one":"{0}ua","unitPattern-count-other":"{0}ua"},"length-furlong":{displayName:"fur","unitPattern-count-one":"{0} fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-one":"{0} fth","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"nmi","unitPattern-count-one":"{0} nmi","unitPattern-count-other":"{0} nmi"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-one":"{0} smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"points","unitPattern-count-one":"{0} pt","unitPattern-count-other":"{0} pt"},"length-solar-radius":{displayName:"R☉","unitPattern-count-one":"{0} R☉","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lx","unitPattern-count-one":"{0} lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"L☉","unitPattern-count-one":"{0} L☉","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-one":"{0} t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-one":"{0}kg","unitPattern-count-other":"{0}kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"g","unitPattern-count-one":"{0}g","unitPattern-count-other":"{0}g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-one":"{0} mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-one":"{0} µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"sh tn","unitPattern-count-one":"{0} sh tn","unitPattern-count-other":"{0} sh tn"},"mass-stone":{displayName:"st","unitPattern-count-one":"{0} st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"lb","unitPattern-count-one":"{0}lb","unitPattern-count-other":"{0}lb",perUnitPattern:"{0}/lb"},"mass-ounce":{displayName:"oz","unitPattern-count-one":"{0}oz","unitPattern-count-other":"{0}oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz t","unitPattern-count-one":"{0} oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"ct","unitPattern-count-one":"{0} ct","unitPattern-count-other":"{0} ct"},"mass-dalton":{displayName:"Da","unitPattern-count-one":"{0} Da","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"M⊕","unitPattern-count-one":"{0} M⊕","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"M☉","unitPattern-count-one":"{0} M☉","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-one":"{0} GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-one":"{0} MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-one":"{0}kW","unitPattern-count-other":"{0}kW"},"power-watt":{displayName:"W","unitPattern-count-one":"{0}W","unitPattern-count-other":"{0}W"},"power-milliwatt":{displayName:"mW","unitPattern-count-one":"{0} mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"ch","unitPattern-count-one":"{0}ch","unitPattern-count-other":"{0}ch"},"pressure-millimeter-of-mercury":{displayName:"mmHg","unitPattern-count-one":"{0} mmHg","unitPattern-count-other":"{0} mmHg"},"pressure-pound-per-square-inch":{displayName:"lb/po²","unitPattern-count-one":"{0} lb/po²","unitPattern-count-other":"{0} lb/po²"},"pressure-inch-hg":{displayName:"″Hg","unitPattern-count-one":"{0}″ Hg","unitPattern-count-other":"{0}″ Hg"},"pressure-bar":{displayName:"bar","unitPattern-count-one":"{0} bar","unitPattern-count-other":"{0} bars"},"pressure-millibar":{displayName:"mbar","unitPattern-count-one":"{0}mbar","unitPattern-count-other":"{0}mbar"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-one":"{0} atm","unitPattern-count-other":"{0} atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-one":"{0} Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-one":"{0}hPa","unitPattern-count-other":"{0}hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0} kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0} MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-one":"{0}km/h","unitPattern-count-other":"{0}km/h"},"speed-meter-per-second":{displayName:"m/s","unitPattern-count-one":"{0}m/s","unitPattern-count-other":"{0}m/s"},"speed-mile-per-hour":{displayName:"mi/h","unitPattern-count-one":"{0}mi/h","unitPattern-count-other":"{0}mi/h"},"speed-knot":{displayName:"nd","unitPattern-count-one":"{0} nd","unitPattern-count-other":"{0} nd"},"temperature-generic":{displayName:"°","unitPattern-count-one":"{0}°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"°C","unitPattern-count-one":"{0}°C","unitPattern-count-other":"{0}°C"},"temperature-fahrenheit":{displayName:"°F","unitPattern-count-one":"{0} °F","unitPattern-count-other":"{0} °F"},"temperature-kelvin":{displayName:"K","unitPattern-count-one":"{0} K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-one":"{0} lbf⋅ft","unitPattern-count-other":"{0} lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-one":"{0} N⋅m","unitPattern-count-other":"{0} N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-one":"{0}km³","unitPattern-count-other":"{0}km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-one":"{0}m³","unitPattern-count-other":"{0}m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-one":"{0}cm³","unitPattern-count-other":"{0}cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mi³","unitPattern-count-one":"{0}mi³","unitPattern-count-other":"{0}mi³"},"volume-cubic-yard":{displayName:"yd³","unitPattern-count-one":"{0}yd³","unitPattern-count-other":"{0}yd³"},"volume-cubic-foot":{displayName:"pi³","unitPattern-count-one":"{0}pi³","unitPattern-count-other":"{0}pi³"},"volume-cubic-inch":{displayName:"po³","unitPattern-count-one":"{0}po³","unitPattern-count-other":"{0}po³"},"volume-megaliter":{displayName:"Ml","unitPattern-count-one":"{0}Ml","unitPattern-count-other":"{0}Ml"},"volume-hectoliter":{displayName:"hl","unitPattern-count-one":"{0}hl","unitPattern-count-other":"{0}hl"},"volume-liter":{displayName:"l","unitPattern-count-one":"{0}l","unitPattern-count-other":"{0}l",perUnitPattern:"{0}/l"},"volume-deciliter":{displayName:"dl","unitPattern-count-one":"{0}dl","unitPattern-count-other":"{0}dl"},"volume-centiliter":{displayName:"cl","unitPattern-count-one":"{0}cl","unitPattern-count-other":"{0}cl"},"volume-milliliter":{displayName:"ml","unitPattern-count-one":"{0}ml","unitPattern-count-other":"{0}ml"},"volume-pint-metric":{displayName:"mpt","unitPattern-count-one":"{0} mpt","unitPattern-count-other":"{0} mpt"},"volume-cup-metric":{displayName:"tm","unitPattern-count-one":"{0} tm","unitPattern-count-other":"{0} tm"},"volume-acre-foot":{displayName:"ac pi","unitPattern-count-one":"{0} ac pi","unitPattern-count-other":"{0} ac pi"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gal","unitPattern-count-one":"{0}gal","unitPattern-count-other":"{0}gal",perUnitPattern:"{0}/gal"},"volume-gallon-imperial":{displayName:"gal imp.","unitPattern-count-one":"{0} gal imp.","unitPattern-count-other":"{0} gal imp.",perUnitPattern:"{0} gal imp."},"volume-quart":{displayName:"qt","unitPattern-count-one":"{0} qt","unitPattern-count-other":"{0} qt"},"volume-pint":{displayName:"pte","unitPattern-count-one":"{0} pte","unitPattern-count-other":"{0} pte"},"volume-cup":{displayName:"tasses","unitPattern-count-one":"{0} tasse","unitPattern-count-other":"{0} tasses"},"volume-fluid-ounce":{displayName:"fl oz","unitPattern-count-one":"{0}fl oz","unitPattern-count-other":"{0}fl oz"},"volume-fluid-ounce-imperial":{displayName:"fl oz imp.","unitPattern-count-one":"{0} fl oz imp.","unitPattern-count-other":"{0} fl oz imp."},"volume-tablespoon":{displayName:"c. à s.","unitPattern-count-one":"{0} c. à s.","unitPattern-count-other":"{0} c. à s."},"volume-teaspoon":{displayName:"c. à c.","unitPattern-count-one":"{0} c. à c.","unitPattern-count-other":"{0} c. à c."},"volume-barrel":{displayName:"bbl","unitPattern-count-one":"{0} bbl","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"direction",east:"{0}E",north:"{0}N",south:"{0}S",west:"{0}O"}},"durationUnit-type-hm":{durationUnitPattern:"h:mm"},"durationUnit-type-hms":{durationUnitPattern:"h:mm:ss"},"durationUnit-type-ms":{durationUnitPattern:"m:ss"}}}}}];