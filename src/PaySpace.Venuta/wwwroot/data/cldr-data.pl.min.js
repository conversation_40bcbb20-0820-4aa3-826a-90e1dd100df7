var CldrData=[{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},likelySubtags:{aa:"aa-Latn-ET",aai:"aai-Latn-ZZ",aak:"aak-Latn-ZZ",aau:"aau-Latn-ZZ",ab:"ab-Cyrl-GE",abi:"abi-Latn-ZZ",abq:"abq-Cyrl-ZZ",abr:"abr-Latn-GH",abt:"abt-Latn-ZZ",aby:"aby-Latn-ZZ",acd:"acd-Latn-ZZ",ace:"ace-Latn-ID",ach:"ach-Latn-UG",ada:"ada-Latn-GH",ade:"ade-Latn-ZZ",adj:"adj-Latn-ZZ",adp:"adp-Tibt-BT",ady:"ady-Cyrl-RU",adz:"adz-Latn-ZZ",ae:"ae-Avst-IR",aeb:"aeb-Arab-TN",aey:"aey-Latn-ZZ",af:"af-Latn-ZA",agc:"agc-Latn-ZZ",agd:"agd-Latn-ZZ",agg:"agg-Latn-ZZ",agm:"agm-Latn-ZZ",ago:"ago-Latn-ZZ",agq:"agq-Latn-CM",aha:"aha-Latn-ZZ",ahl:"ahl-Latn-ZZ",aho:"aho-Ahom-IN",ajg:"ajg-Latn-ZZ",ak:"ak-Latn-GH",akk:"akk-Xsux-IQ",ala:"ala-Latn-ZZ",ali:"ali-Latn-ZZ",aln:"aln-Latn-XK",alt:"alt-Cyrl-RU",am:"am-Ethi-ET",amm:"amm-Latn-ZZ",amn:"amn-Latn-ZZ",amo:"amo-Latn-NG",amp:"amp-Latn-ZZ",an:"an-Latn-ES",anc:"anc-Latn-ZZ",ank:"ank-Latn-ZZ",ann:"ann-Latn-ZZ",any:"any-Latn-ZZ",aoj:"aoj-Latn-ZZ",aom:"aom-Latn-ZZ",aoz:"aoz-Latn-ID",apc:"apc-Arab-ZZ",apd:"apd-Arab-TG",ape:"ape-Latn-ZZ",apr:"apr-Latn-ZZ",aps:"aps-Latn-ZZ",apz:"apz-Latn-ZZ",ar:"ar-Arab-EG",arc:"arc-Armi-IR","arc-Nbat":"arc-Nbat-JO","arc-Palm":"arc-Palm-SY",arh:"arh-Latn-ZZ",arn:"arn-Latn-CL",aro:"aro-Latn-BO",arq:"arq-Arab-DZ",ars:"ars-Arab-SA",ary:"ary-Arab-MA",arz:"arz-Arab-EG",as:"as-Beng-IN",asa:"asa-Latn-TZ",ase:"ase-Sgnw-US",asg:"asg-Latn-ZZ",aso:"aso-Latn-ZZ",ast:"ast-Latn-ES",ata:"ata-Latn-ZZ",atg:"atg-Latn-ZZ",atj:"atj-Latn-CA",auy:"auy-Latn-ZZ",av:"av-Cyrl-RU",avl:"avl-Arab-ZZ",avn:"avn-Latn-ZZ",avt:"avt-Latn-ZZ",avu:"avu-Latn-ZZ",awa:"awa-Deva-IN",awb:"awb-Latn-ZZ",awo:"awo-Latn-ZZ",awx:"awx-Latn-ZZ",ay:"ay-Latn-BO",ayb:"ayb-Latn-ZZ",az:"az-Latn-AZ","az-Arab":"az-Arab-IR","az-IQ":"az-Arab-IQ","az-IR":"az-Arab-IR","az-RU":"az-Cyrl-RU",ba:"ba-Cyrl-RU",bal:"bal-Arab-PK",ban:"ban-Latn-ID",bap:"bap-Deva-NP",bar:"bar-Latn-AT",bas:"bas-Latn-CM",bav:"bav-Latn-ZZ",bax:"bax-Bamu-CM",bba:"bba-Latn-ZZ",bbb:"bbb-Latn-ZZ",bbc:"bbc-Latn-ID",bbd:"bbd-Latn-ZZ",bbj:"bbj-Latn-CM",bbp:"bbp-Latn-ZZ",bbr:"bbr-Latn-ZZ",bcf:"bcf-Latn-ZZ",bch:"bch-Latn-ZZ",bci:"bci-Latn-CI",bcm:"bcm-Latn-ZZ",bcn:"bcn-Latn-ZZ",bco:"bco-Latn-ZZ",bcq:"bcq-Ethi-ZZ",bcu:"bcu-Latn-ZZ",bdd:"bdd-Latn-ZZ",be:"be-Cyrl-BY",bef:"bef-Latn-ZZ",beh:"beh-Latn-ZZ",bej:"bej-Arab-SD",bem:"bem-Latn-ZM",bet:"bet-Latn-ZZ",bew:"bew-Latn-ID",bex:"bex-Latn-ZZ",bez:"bez-Latn-TZ",bfd:"bfd-Latn-CM",bfq:"bfq-Taml-IN",bft:"bft-Arab-PK",bfy:"bfy-Deva-IN",bg:"bg-Cyrl-BG",bgc:"bgc-Deva-IN",bgn:"bgn-Arab-PK",bgx:"bgx-Grek-TR",bhb:"bhb-Deva-IN",bhg:"bhg-Latn-ZZ",bhi:"bhi-Deva-IN",bhl:"bhl-Latn-ZZ",bho:"bho-Deva-IN",bhy:"bhy-Latn-ZZ",bi:"bi-Latn-VU",bib:"bib-Latn-ZZ",big:"big-Latn-ZZ",bik:"bik-Latn-PH",bim:"bim-Latn-ZZ",bin:"bin-Latn-NG",bio:"bio-Latn-ZZ",biq:"biq-Latn-ZZ",bjh:"bjh-Latn-ZZ",bji:"bji-Ethi-ZZ",bjj:"bjj-Deva-IN",bjn:"bjn-Latn-ID",bjo:"bjo-Latn-ZZ",bjr:"bjr-Latn-ZZ",bjt:"bjt-Latn-SN",bjz:"bjz-Latn-ZZ",bkc:"bkc-Latn-ZZ",bkm:"bkm-Latn-CM",bkq:"bkq-Latn-ZZ",bku:"bku-Latn-PH",bkv:"bkv-Latn-ZZ",blt:"blt-Tavt-VN",bm:"bm-Latn-ML",bmh:"bmh-Latn-ZZ",bmk:"bmk-Latn-ZZ",bmq:"bmq-Latn-ML",bmu:"bmu-Latn-ZZ",bn:"bn-Beng-BD",bng:"bng-Latn-ZZ",bnm:"bnm-Latn-ZZ",bnp:"bnp-Latn-ZZ",bo:"bo-Tibt-CN",boj:"boj-Latn-ZZ",bom:"bom-Latn-ZZ",bon:"bon-Latn-ZZ",bpy:"bpy-Beng-IN",bqc:"bqc-Latn-ZZ",bqi:"bqi-Arab-IR",bqp:"bqp-Latn-ZZ",bqv:"bqv-Latn-CI",br:"br-Latn-FR",bra:"bra-Deva-IN",brh:"brh-Arab-PK",brx:"brx-Deva-IN",brz:"brz-Latn-ZZ",bs:"bs-Latn-BA",bsj:"bsj-Latn-ZZ",bsq:"bsq-Bass-LR",bss:"bss-Latn-CM",bst:"bst-Ethi-ZZ",bto:"bto-Latn-PH",btt:"btt-Latn-ZZ",btv:"btv-Deva-PK",bua:"bua-Cyrl-RU",buc:"buc-Latn-YT",bud:"bud-Latn-ZZ",bug:"bug-Latn-ID",buk:"buk-Latn-ZZ",bum:"bum-Latn-CM",buo:"buo-Latn-ZZ",bus:"bus-Latn-ZZ",buu:"buu-Latn-ZZ",bvb:"bvb-Latn-GQ",bwd:"bwd-Latn-ZZ",bwr:"bwr-Latn-ZZ",bxh:"bxh-Latn-ZZ",bye:"bye-Latn-ZZ",byn:"byn-Ethi-ER",byr:"byr-Latn-ZZ",bys:"bys-Latn-ZZ",byv:"byv-Latn-CM",byx:"byx-Latn-ZZ",bza:"bza-Latn-ZZ",bze:"bze-Latn-ML",bzf:"bzf-Latn-ZZ",bzh:"bzh-Latn-ZZ",bzw:"bzw-Latn-ZZ",ca:"ca-Latn-ES",can:"can-Latn-ZZ",cbj:"cbj-Latn-ZZ",cch:"cch-Latn-NG",ccp:"ccp-Cakm-BD",ce:"ce-Cyrl-RU",ceb:"ceb-Latn-PH",cfa:"cfa-Latn-ZZ",cgg:"cgg-Latn-UG",ch:"ch-Latn-GU",chk:"chk-Latn-FM",chm:"chm-Cyrl-RU",cho:"cho-Latn-US",chp:"chp-Latn-CA",chr:"chr-Cher-US",cic:"cic-Latn-US",cja:"cja-Arab-KH",cjm:"cjm-Cham-VN",cjv:"cjv-Latn-ZZ",ckb:"ckb-Arab-IQ",ckl:"ckl-Latn-ZZ",cko:"cko-Latn-ZZ",cky:"cky-Latn-ZZ",cla:"cla-Latn-ZZ",cme:"cme-Latn-ZZ",cmg:"cmg-Soyo-MN",co:"co-Latn-FR",cop:"cop-Copt-EG",cps:"cps-Latn-PH",cr:"cr-Cans-CA",crh:"crh-Cyrl-UA",crj:"crj-Cans-CA",crk:"crk-Cans-CA",crl:"crl-Cans-CA",crm:"crm-Cans-CA",crs:"crs-Latn-SC",cs:"cs-Latn-CZ",csb:"csb-Latn-PL",csw:"csw-Cans-CA",ctd:"ctd-Pauc-MM",cu:"cu-Cyrl-RU","cu-Glag":"cu-Glag-BG",cv:"cv-Cyrl-RU",cy:"cy-Latn-GB",da:"da-Latn-DK",dad:"dad-Latn-ZZ",daf:"daf-Latn-ZZ",dag:"dag-Latn-ZZ",dah:"dah-Latn-ZZ",dak:"dak-Latn-US",dar:"dar-Cyrl-RU",dav:"dav-Latn-KE",dbd:"dbd-Latn-ZZ",dbq:"dbq-Latn-ZZ",dcc:"dcc-Arab-IN",ddn:"ddn-Latn-ZZ",de:"de-Latn-DE",ded:"ded-Latn-ZZ",den:"den-Latn-CA",dga:"dga-Latn-ZZ",dgh:"dgh-Latn-ZZ",dgi:"dgi-Latn-ZZ",dgl:"dgl-Arab-ZZ",dgr:"dgr-Latn-CA",dgz:"dgz-Latn-ZZ",dia:"dia-Latn-ZZ",dje:"dje-Latn-NE",dnj:"dnj-Latn-CI",dob:"dob-Latn-ZZ",doi:"doi-Arab-IN",dop:"dop-Latn-ZZ",dow:"dow-Latn-ZZ",drh:"drh-Mong-CN",dri:"dri-Latn-ZZ",drs:"drs-Ethi-ZZ",dsb:"dsb-Latn-DE",dtm:"dtm-Latn-ML",dtp:"dtp-Latn-MY",dts:"dts-Latn-ZZ",dty:"dty-Deva-NP",dua:"dua-Latn-CM",duc:"duc-Latn-ZZ",dud:"dud-Latn-ZZ",dug:"dug-Latn-ZZ",dv:"dv-Thaa-MV",dva:"dva-Latn-ZZ",dww:"dww-Latn-ZZ",dyo:"dyo-Latn-SN",dyu:"dyu-Latn-BF",dz:"dz-Tibt-BT",dzg:"dzg-Latn-ZZ",ebu:"ebu-Latn-KE",ee:"ee-Latn-GH",efi:"efi-Latn-NG",egl:"egl-Latn-IT",egy:"egy-Egyp-EG",eka:"eka-Latn-ZZ",eky:"eky-Kali-MM",el:"el-Grek-GR",ema:"ema-Latn-ZZ",emi:"emi-Latn-ZZ",en:"en-Latn-US","en-Shaw":"en-Shaw-GB",enn:"enn-Latn-ZZ",enq:"enq-Latn-ZZ",eo:"eo-Latn-001",eri:"eri-Latn-ZZ",es:"es-Latn-ES",esg:"esg-Gonm-IN",esu:"esu-Latn-US",et:"et-Latn-EE",etr:"etr-Latn-ZZ",ett:"ett-Ital-IT",etu:"etu-Latn-ZZ",etx:"etx-Latn-ZZ",eu:"eu-Latn-ES",ewo:"ewo-Latn-CM",ext:"ext-Latn-ES",fa:"fa-Arab-IR",faa:"faa-Latn-ZZ",fab:"fab-Latn-ZZ",fag:"fag-Latn-ZZ",fai:"fai-Latn-ZZ",fan:"fan-Latn-GQ",ff:"ff-Latn-SN","ff-Adlm":"ff-Adlm-GN",ffi:"ffi-Latn-ZZ",ffm:"ffm-Latn-ML",fi:"fi-Latn-FI",fia:"fia-Arab-SD",fil:"fil-Latn-PH",fit:"fit-Latn-SE",fj:"fj-Latn-FJ",flr:"flr-Latn-ZZ",fmp:"fmp-Latn-ZZ",fo:"fo-Latn-FO",fod:"fod-Latn-ZZ",fon:"fon-Latn-BJ",for:"for-Latn-ZZ",fpe:"fpe-Latn-ZZ",fqs:"fqs-Latn-ZZ",fr:"fr-Latn-FR",frc:"frc-Latn-US",frp:"frp-Latn-FR",frr:"frr-Latn-DE",frs:"frs-Latn-DE",fub:"fub-Arab-CM",fud:"fud-Latn-WF",fue:"fue-Latn-ZZ",fuf:"fuf-Latn-GN",fuh:"fuh-Latn-ZZ",fuq:"fuq-Latn-NE",fur:"fur-Latn-IT",fuv:"fuv-Latn-NG",fuy:"fuy-Latn-ZZ",fvr:"fvr-Latn-SD",fy:"fy-Latn-NL",ga:"ga-Latn-IE",gaa:"gaa-Latn-GH",gaf:"gaf-Latn-ZZ",gag:"gag-Latn-MD",gah:"gah-Latn-ZZ",gaj:"gaj-Latn-ZZ",gam:"gam-Latn-ZZ",gan:"gan-Hans-CN",gaw:"gaw-Latn-ZZ",gay:"gay-Latn-ID",gba:"gba-Latn-ZZ",gbf:"gbf-Latn-ZZ",gbm:"gbm-Deva-IN",gby:"gby-Latn-ZZ",gbz:"gbz-Arab-IR",gcr:"gcr-Latn-GF",gd:"gd-Latn-GB",gde:"gde-Latn-ZZ",gdn:"gdn-Latn-ZZ",gdr:"gdr-Latn-ZZ",geb:"geb-Latn-ZZ",gej:"gej-Latn-ZZ",gel:"gel-Latn-ZZ",gez:"gez-Ethi-ET",gfk:"gfk-Latn-ZZ",ggn:"ggn-Deva-NP",ghs:"ghs-Latn-ZZ",gil:"gil-Latn-KI",gim:"gim-Latn-ZZ",gjk:"gjk-Arab-PK",gjn:"gjn-Latn-ZZ",gju:"gju-Arab-PK",gkn:"gkn-Latn-ZZ",gkp:"gkp-Latn-ZZ",gl:"gl-Latn-ES",glk:"glk-Arab-IR",gmm:"gmm-Latn-ZZ",gmv:"gmv-Ethi-ZZ",gn:"gn-Latn-PY",gnd:"gnd-Latn-ZZ",gng:"gng-Latn-ZZ",god:"god-Latn-ZZ",gof:"gof-Ethi-ZZ",goi:"goi-Latn-ZZ",gom:"gom-Deva-IN",gon:"gon-Telu-IN",gor:"gor-Latn-ID",gos:"gos-Latn-NL",got:"got-Goth-UA",grb:"grb-Latn-ZZ",grc:"grc-Cprt-CY","grc-Linb":"grc-Linb-GR",grt:"grt-Beng-IN",grw:"grw-Latn-ZZ",gsw:"gsw-Latn-CH",gu:"gu-Gujr-IN",gub:"gub-Latn-BR",guc:"guc-Latn-CO",gud:"gud-Latn-ZZ",gur:"gur-Latn-GH",guw:"guw-Latn-ZZ",gux:"gux-Latn-ZZ",guz:"guz-Latn-KE",gv:"gv-Latn-IM",gvf:"gvf-Latn-ZZ",gvr:"gvr-Deva-NP",gvs:"gvs-Latn-ZZ",gwc:"gwc-Arab-ZZ",gwi:"gwi-Latn-CA",gwt:"gwt-Arab-ZZ",gyi:"gyi-Latn-ZZ",ha:"ha-Latn-NG","ha-CM":"ha-Arab-CM","ha-SD":"ha-Arab-SD",hag:"hag-Latn-ZZ",hak:"hak-Hans-CN",ham:"ham-Latn-ZZ",haw:"haw-Latn-US",haz:"haz-Arab-AF",hbb:"hbb-Latn-ZZ",hdy:"hdy-Ethi-ZZ",he:"he-Hebr-IL",hhy:"hhy-Latn-ZZ",hi:"hi-Deva-IN",hia:"hia-Latn-ZZ",hif:"hif-Latn-FJ",hig:"hig-Latn-ZZ",hih:"hih-Latn-ZZ",hil:"hil-Latn-PH",hla:"hla-Latn-ZZ",hlu:"hlu-Hluw-TR",hmd:"hmd-Plrd-CN",hmt:"hmt-Latn-ZZ",hnd:"hnd-Arab-PK",hne:"hne-Deva-IN",hnj:"hnj-Hmng-LA",hnn:"hnn-Latn-PH",hno:"hno-Arab-PK",ho:"ho-Latn-PG",hoc:"hoc-Deva-IN",hoj:"hoj-Deva-IN",hot:"hot-Latn-ZZ",hr:"hr-Latn-HR",hsb:"hsb-Latn-DE",hsn:"hsn-Hans-CN",ht:"ht-Latn-HT",hu:"hu-Latn-HU",hui:"hui-Latn-ZZ",hy:"hy-Armn-AM",hz:"hz-Latn-NA",ia:"ia-Latn-001",ian:"ian-Latn-ZZ",iar:"iar-Latn-ZZ",iba:"iba-Latn-MY",ibb:"ibb-Latn-NG",iby:"iby-Latn-ZZ",ica:"ica-Latn-ZZ",ich:"ich-Latn-ZZ",id:"id-Latn-ID",idd:"idd-Latn-ZZ",idi:"idi-Latn-ZZ",idu:"idu-Latn-ZZ",ife:"ife-Latn-TG",ig:"ig-Latn-NG",igb:"igb-Latn-ZZ",ige:"ige-Latn-ZZ",ii:"ii-Yiii-CN",ijj:"ijj-Latn-ZZ",ik:"ik-Latn-US",ikk:"ikk-Latn-ZZ",ikt:"ikt-Latn-CA",ikw:"ikw-Latn-ZZ",ikx:"ikx-Latn-ZZ",ilo:"ilo-Latn-PH",imo:"imo-Latn-ZZ",in:"in-Latn-ID",inh:"inh-Cyrl-RU",io:"io-Latn-001",iou:"iou-Latn-ZZ",iri:"iri-Latn-ZZ",is:"is-Latn-IS",it:"it-Latn-IT",iu:"iu-Cans-CA",iw:"iw-Hebr-IL",iwm:"iwm-Latn-ZZ",iws:"iws-Latn-ZZ",izh:"izh-Latn-RU",izi:"izi-Latn-ZZ",ja:"ja-Jpan-JP",jab:"jab-Latn-ZZ",jam:"jam-Latn-JM",jbo:"jbo-Latn-001",jbu:"jbu-Latn-ZZ",jen:"jen-Latn-ZZ",jgk:"jgk-Latn-ZZ",jgo:"jgo-Latn-CM",ji:"ji-Hebr-UA",jib:"jib-Latn-ZZ",jmc:"jmc-Latn-TZ",jml:"jml-Deva-NP",jra:"jra-Latn-ZZ",jut:"jut-Latn-DK",jv:"jv-Latn-ID",jw:"jw-Latn-ID",ka:"ka-Geor-GE",kaa:"kaa-Cyrl-UZ",kab:"kab-Latn-DZ",kac:"kac-Latn-MM",kad:"kad-Latn-ZZ",kai:"kai-Latn-ZZ",kaj:"kaj-Latn-NG",kam:"kam-Latn-KE",kao:"kao-Latn-ML",kbd:"kbd-Cyrl-RU",kbm:"kbm-Latn-ZZ",kbp:"kbp-Latn-ZZ",kbq:"kbq-Latn-ZZ",kbx:"kbx-Latn-ZZ",kby:"kby-Arab-NE",kcg:"kcg-Latn-NG",kck:"kck-Latn-ZW",kcl:"kcl-Latn-ZZ",kct:"kct-Latn-ZZ",kde:"kde-Latn-TZ",kdh:"kdh-Arab-TG",kdl:"kdl-Latn-ZZ",kdt:"kdt-Thai-TH",kea:"kea-Latn-CV",ken:"ken-Latn-CM",kez:"kez-Latn-ZZ",kfo:"kfo-Latn-CI",kfr:"kfr-Deva-IN",kfy:"kfy-Deva-IN",kg:"kg-Latn-CD",kge:"kge-Latn-ID",kgf:"kgf-Latn-ZZ",kgp:"kgp-Latn-BR",kha:"kha-Latn-IN",khb:"khb-Talu-CN",khn:"khn-Deva-IN",khq:"khq-Latn-ML",khs:"khs-Latn-ZZ",kht:"kht-Mymr-IN",khw:"khw-Arab-PK",khz:"khz-Latn-ZZ",ki:"ki-Latn-KE",kij:"kij-Latn-ZZ",kiu:"kiu-Latn-TR",kiw:"kiw-Latn-ZZ",kj:"kj-Latn-NA",kjd:"kjd-Latn-ZZ",kjg:"kjg-Laoo-LA",kjs:"kjs-Latn-ZZ",kjy:"kjy-Latn-ZZ",kk:"kk-Cyrl-KZ","kk-AF":"kk-Arab-AF","kk-Arab":"kk-Arab-CN","kk-CN":"kk-Arab-CN","kk-IR":"kk-Arab-IR","kk-MN":"kk-Arab-MN",kkc:"kkc-Latn-ZZ",kkj:"kkj-Latn-CM",kl:"kl-Latn-GL",kln:"kln-Latn-KE",klq:"klq-Latn-ZZ",klt:"klt-Latn-ZZ",klx:"klx-Latn-ZZ",km:"km-Khmr-KH",kmb:"kmb-Latn-AO",kmh:"kmh-Latn-ZZ",kmo:"kmo-Latn-ZZ",kms:"kms-Latn-ZZ",kmu:"kmu-Latn-ZZ",kmw:"kmw-Latn-ZZ",kn:"kn-Knda-IN",knf:"knf-Latn-GW",knp:"knp-Latn-ZZ",ko:"ko-Kore-KR",koi:"koi-Cyrl-RU",kok:"kok-Deva-IN",kol:"kol-Latn-ZZ",kos:"kos-Latn-FM",koz:"koz-Latn-ZZ",kpe:"kpe-Latn-LR",kpf:"kpf-Latn-ZZ",kpo:"kpo-Latn-ZZ",kpr:"kpr-Latn-ZZ",kpx:"kpx-Latn-ZZ",kqb:"kqb-Latn-ZZ",kqf:"kqf-Latn-ZZ",kqs:"kqs-Latn-ZZ",kqy:"kqy-Ethi-ZZ",kr:"kr-Latn-ZZ",krc:"krc-Cyrl-RU",kri:"kri-Latn-SL",krj:"krj-Latn-PH",krl:"krl-Latn-RU",krs:"krs-Latn-ZZ",kru:"kru-Deva-IN",ks:"ks-Arab-IN",ksb:"ksb-Latn-TZ",ksd:"ksd-Latn-ZZ",ksf:"ksf-Latn-CM",ksh:"ksh-Latn-DE",ksj:"ksj-Latn-ZZ",ksr:"ksr-Latn-ZZ",ktb:"ktb-Ethi-ZZ",ktm:"ktm-Latn-ZZ",kto:"kto-Latn-ZZ",ktr:"ktr-Latn-MY",ku:"ku-Latn-TR","ku-Arab":"ku-Arab-IQ","ku-LB":"ku-Arab-LB",kub:"kub-Latn-ZZ",kud:"kud-Latn-ZZ",kue:"kue-Latn-ZZ",kuj:"kuj-Latn-ZZ",kum:"kum-Cyrl-RU",kun:"kun-Latn-ZZ",kup:"kup-Latn-ZZ",kus:"kus-Latn-ZZ",kv:"kv-Cyrl-RU",kvg:"kvg-Latn-ZZ",kvr:"kvr-Latn-ID",kvx:"kvx-Arab-PK",kw:"kw-Latn-GB",kwj:"kwj-Latn-ZZ",kwo:"kwo-Latn-ZZ",kwq:"kwq-Latn-ZZ",kxa:"kxa-Latn-ZZ",kxc:"kxc-Ethi-ZZ",kxe:"kxe-Latn-ZZ",kxm:"kxm-Thai-TH",kxp:"kxp-Arab-PK",kxw:"kxw-Latn-ZZ",kxz:"kxz-Latn-ZZ",ky:"ky-Cyrl-KG","ky-Arab":"ky-Arab-CN","ky-CN":"ky-Arab-CN","ky-Latn":"ky-Latn-TR","ky-TR":"ky-Latn-TR",kye:"kye-Latn-ZZ",kyx:"kyx-Latn-ZZ",kzj:"kzj-Latn-MY",kzr:"kzr-Latn-ZZ",kzt:"kzt-Latn-MY",la:"la-Latn-VA",lab:"lab-Lina-GR",lad:"lad-Hebr-IL",lag:"lag-Latn-TZ",lah:"lah-Arab-PK",laj:"laj-Latn-UG",las:"las-Latn-ZZ",lb:"lb-Latn-LU",lbe:"lbe-Cyrl-RU",lbu:"lbu-Latn-ZZ",lbw:"lbw-Latn-ID",lcm:"lcm-Latn-ZZ",lcp:"lcp-Thai-CN",ldb:"ldb-Latn-ZZ",led:"led-Latn-ZZ",lee:"lee-Latn-ZZ",lem:"lem-Latn-ZZ",lep:"lep-Lepc-IN",leq:"leq-Latn-ZZ",leu:"leu-Latn-ZZ",lez:"lez-Cyrl-RU",lg:"lg-Latn-UG",lgg:"lgg-Latn-ZZ",li:"li-Latn-NL",lia:"lia-Latn-ZZ",lid:"lid-Latn-ZZ",lif:"lif-Deva-NP","lif-Limb":"lif-Limb-IN",lig:"lig-Latn-ZZ",lih:"lih-Latn-ZZ",lij:"lij-Latn-IT",lis:"lis-Lisu-CN",ljp:"ljp-Latn-ID",lki:"lki-Arab-IR",lkt:"lkt-Latn-US",lle:"lle-Latn-ZZ",lln:"lln-Latn-ZZ",lmn:"lmn-Telu-IN",lmo:"lmo-Latn-IT",lmp:"lmp-Latn-ZZ",ln:"ln-Latn-CD",lns:"lns-Latn-ZZ",lnu:"lnu-Latn-ZZ",lo:"lo-Laoo-LA",loj:"loj-Latn-ZZ",lok:"lok-Latn-ZZ",lol:"lol-Latn-CD",lor:"lor-Latn-ZZ",los:"los-Latn-ZZ",loz:"loz-Latn-ZM",lrc:"lrc-Arab-IR",lt:"lt-Latn-LT",ltg:"ltg-Latn-LV",lu:"lu-Latn-CD",lua:"lua-Latn-CD",luo:"luo-Latn-KE",luy:"luy-Latn-KE",luz:"luz-Arab-IR",lv:"lv-Latn-LV",lwl:"lwl-Thai-TH",lzh:"lzh-Hans-CN",lzz:"lzz-Latn-TR",mad:"mad-Latn-ID",maf:"maf-Latn-CM",mag:"mag-Deva-IN",mai:"mai-Deva-IN",mak:"mak-Latn-ID",man:"man-Latn-GM","man-GN":"man-Nkoo-GN","man-Nkoo":"man-Nkoo-GN",mas:"mas-Latn-KE",maw:"maw-Latn-ZZ",maz:"maz-Latn-MX",mbh:"mbh-Latn-ZZ",mbo:"mbo-Latn-ZZ",mbq:"mbq-Latn-ZZ",mbu:"mbu-Latn-ZZ",mbw:"mbw-Latn-ZZ",mci:"mci-Latn-ZZ",mcp:"mcp-Latn-ZZ",mcq:"mcq-Latn-ZZ",mcr:"mcr-Latn-ZZ",mcu:"mcu-Latn-ZZ",mda:"mda-Latn-ZZ",mde:"mde-Arab-ZZ",mdf:"mdf-Cyrl-RU",mdh:"mdh-Latn-PH",mdj:"mdj-Latn-ZZ",mdr:"mdr-Latn-ID",mdx:"mdx-Ethi-ZZ",med:"med-Latn-ZZ",mee:"mee-Latn-ZZ",mek:"mek-Latn-ZZ",men:"men-Latn-SL",mer:"mer-Latn-KE",met:"met-Latn-ZZ",meu:"meu-Latn-ZZ",mfa:"mfa-Arab-TH",mfe:"mfe-Latn-MU",mfn:"mfn-Latn-ZZ",mfo:"mfo-Latn-ZZ",mfq:"mfq-Latn-ZZ",mg:"mg-Latn-MG",mgh:"mgh-Latn-MZ",mgl:"mgl-Latn-ZZ",mgo:"mgo-Latn-CM",mgp:"mgp-Deva-NP",mgy:"mgy-Latn-TZ",mh:"mh-Latn-MH",mhi:"mhi-Latn-ZZ",mhl:"mhl-Latn-ZZ",mi:"mi-Latn-NZ",mif:"mif-Latn-ZZ",min:"min-Latn-ID",mis:"mis-Hatr-IQ","mis-Medf":"mis-Medf-NG",miw:"miw-Latn-ZZ",mk:"mk-Cyrl-MK",mki:"mki-Arab-ZZ",mkl:"mkl-Latn-ZZ",mkp:"mkp-Latn-ZZ",mkw:"mkw-Latn-ZZ",ml:"ml-Mlym-IN",mle:"mle-Latn-ZZ",mlp:"mlp-Latn-ZZ",mls:"mls-Latn-SD",mmo:"mmo-Latn-ZZ",mmu:"mmu-Latn-ZZ",mmx:"mmx-Latn-ZZ",mn:"mn-Cyrl-MN","mn-CN":"mn-Mong-CN","mn-Mong":"mn-Mong-CN",mna:"mna-Latn-ZZ",mnf:"mnf-Latn-ZZ",mni:"mni-Beng-IN",mnw:"mnw-Mymr-MM",mo:"mo-Latn-RO",moa:"moa-Latn-ZZ",moe:"moe-Latn-CA",moh:"moh-Latn-CA",mos:"mos-Latn-BF",mox:"mox-Latn-ZZ",mpp:"mpp-Latn-ZZ",mps:"mps-Latn-ZZ",mpt:"mpt-Latn-ZZ",mpx:"mpx-Latn-ZZ",mql:"mql-Latn-ZZ",mr:"mr-Deva-IN",mrd:"mrd-Deva-NP",mrj:"mrj-Cyrl-RU",mro:"mro-Mroo-BD",ms:"ms-Latn-MY","ms-CC":"ms-Arab-CC","ms-ID":"ms-Arab-ID",mt:"mt-Latn-MT",mtc:"mtc-Latn-ZZ",mtf:"mtf-Latn-ZZ",mti:"mti-Latn-ZZ",mtr:"mtr-Deva-IN",mua:"mua-Latn-CM",mur:"mur-Latn-ZZ",mus:"mus-Latn-US",mva:"mva-Latn-ZZ",mvn:"mvn-Latn-ZZ",mvy:"mvy-Arab-PK",mwk:"mwk-Latn-ML",mwr:"mwr-Deva-IN",mwv:"mwv-Latn-ID",mww:"mww-Hmnp-US",mxc:"mxc-Latn-ZW",mxm:"mxm-Latn-ZZ",my:"my-Mymr-MM",myk:"myk-Latn-ZZ",mym:"mym-Ethi-ZZ",myv:"myv-Cyrl-RU",myw:"myw-Latn-ZZ",myx:"myx-Latn-UG",myz:"myz-Mand-IR",mzk:"mzk-Latn-ZZ",mzm:"mzm-Latn-ZZ",mzn:"mzn-Arab-IR",mzp:"mzp-Latn-ZZ",mzw:"mzw-Latn-ZZ",mzz:"mzz-Latn-ZZ",na:"na-Latn-NR",nac:"nac-Latn-ZZ",naf:"naf-Latn-ZZ",nak:"nak-Latn-ZZ",nan:"nan-Hans-CN",nap:"nap-Latn-IT",naq:"naq-Latn-NA",nas:"nas-Latn-ZZ",nb:"nb-Latn-NO",nca:"nca-Latn-ZZ",nce:"nce-Latn-ZZ",ncf:"ncf-Latn-ZZ",nch:"nch-Latn-MX",nco:"nco-Latn-ZZ",ncu:"ncu-Latn-ZZ",nd:"nd-Latn-ZW",ndc:"ndc-Latn-MZ",nds:"nds-Latn-DE",ne:"ne-Deva-NP",neb:"neb-Latn-ZZ",new:"new-Deva-NP",nex:"nex-Latn-ZZ",nfr:"nfr-Latn-ZZ",ng:"ng-Latn-NA",nga:"nga-Latn-ZZ",ngb:"ngb-Latn-ZZ",ngl:"ngl-Latn-MZ",nhb:"nhb-Latn-ZZ",nhe:"nhe-Latn-MX",nhw:"nhw-Latn-MX",nif:"nif-Latn-ZZ",nii:"nii-Latn-ZZ",nij:"nij-Latn-ID",nin:"nin-Latn-ZZ",niu:"niu-Latn-NU",niy:"niy-Latn-ZZ",niz:"niz-Latn-ZZ",njo:"njo-Latn-IN",nkg:"nkg-Latn-ZZ",nko:"nko-Latn-ZZ",nl:"nl-Latn-NL",nmg:"nmg-Latn-CM",nmz:"nmz-Latn-ZZ",nn:"nn-Latn-NO",nnf:"nnf-Latn-ZZ",nnh:"nnh-Latn-CM",nnk:"nnk-Latn-ZZ",nnm:"nnm-Latn-ZZ",nnp:"nnp-Wcho-IN",no:"no-Latn-NO",nod:"nod-Lana-TH",noe:"noe-Deva-IN",non:"non-Runr-SE",nop:"nop-Latn-ZZ",nou:"nou-Latn-ZZ",nqo:"nqo-Nkoo-GN",nr:"nr-Latn-ZA",nrb:"nrb-Latn-ZZ",nsk:"nsk-Cans-CA",nsn:"nsn-Latn-ZZ",nso:"nso-Latn-ZA",nss:"nss-Latn-ZZ",ntm:"ntm-Latn-ZZ",ntr:"ntr-Latn-ZZ",nui:"nui-Latn-ZZ",nup:"nup-Latn-ZZ",nus:"nus-Latn-SS",nuv:"nuv-Latn-ZZ",nux:"nux-Latn-ZZ",nv:"nv-Latn-US",nwb:"nwb-Latn-ZZ",nxq:"nxq-Latn-CN",nxr:"nxr-Latn-ZZ",ny:"ny-Latn-MW",nym:"nym-Latn-TZ",nyn:"nyn-Latn-UG",nzi:"nzi-Latn-GH",oc:"oc-Latn-FR",ogc:"ogc-Latn-ZZ",okr:"okr-Latn-ZZ",okv:"okv-Latn-ZZ",om:"om-Latn-ET",ong:"ong-Latn-ZZ",onn:"onn-Latn-ZZ",ons:"ons-Latn-ZZ",opm:"opm-Latn-ZZ",or:"or-Orya-IN",oro:"oro-Latn-ZZ",oru:"oru-Arab-ZZ",os:"os-Cyrl-GE",osa:"osa-Osge-US",ota:"ota-Arab-ZZ",otk:"otk-Orkh-MN",ozm:"ozm-Latn-ZZ",pa:"pa-Guru-IN","pa-Arab":"pa-Arab-PK","pa-PK":"pa-Arab-PK",pag:"pag-Latn-PH",pal:"pal-Phli-IR","pal-Phlp":"pal-Phlp-CN",pam:"pam-Latn-PH",pap:"pap-Latn-AW",pau:"pau-Latn-PW",pbi:"pbi-Latn-ZZ",pcd:"pcd-Latn-FR",pcm:"pcm-Latn-NG",pdc:"pdc-Latn-US",pdt:"pdt-Latn-CA",ped:"ped-Latn-ZZ",peo:"peo-Xpeo-IR",pex:"pex-Latn-ZZ",pfl:"pfl-Latn-DE",phl:"phl-Arab-ZZ",phn:"phn-Phnx-LB",pil:"pil-Latn-ZZ",pip:"pip-Latn-ZZ",pka:"pka-Brah-IN",pko:"pko-Latn-KE",pl:"pl-Latn-PL",pla:"pla-Latn-ZZ",pms:"pms-Latn-IT",png:"png-Latn-ZZ",pnn:"pnn-Latn-ZZ",pnt:"pnt-Grek-GR",pon:"pon-Latn-FM",ppa:"ppa-Deva-IN",ppo:"ppo-Latn-ZZ",pra:"pra-Khar-PK",prd:"prd-Arab-IR",prg:"prg-Latn-001",ps:"ps-Arab-AF",pss:"pss-Latn-ZZ",pt:"pt-Latn-BR",ptp:"ptp-Latn-ZZ",puu:"puu-Latn-GA",pwa:"pwa-Latn-ZZ",qu:"qu-Latn-PE",quc:"quc-Latn-GT",qug:"qug-Latn-EC",rai:"rai-Latn-ZZ",raj:"raj-Deva-IN",rao:"rao-Latn-ZZ",rcf:"rcf-Latn-RE",rej:"rej-Latn-ID",rel:"rel-Latn-ZZ",res:"res-Latn-ZZ",rgn:"rgn-Latn-IT",rhg:"rhg-Arab-MM",ria:"ria-Latn-IN",rif:"rif-Tfng-MA","rif-NL":"rif-Latn-NL",rjs:"rjs-Deva-NP",rkt:"rkt-Beng-BD",rm:"rm-Latn-CH",rmf:"rmf-Latn-FI",rmo:"rmo-Latn-CH",rmt:"rmt-Arab-IR",rmu:"rmu-Latn-SE",rn:"rn-Latn-BI",rna:"rna-Latn-ZZ",rng:"rng-Latn-MZ",ro:"ro-Latn-RO",rob:"rob-Latn-ID",rof:"rof-Latn-TZ",roo:"roo-Latn-ZZ",rro:"rro-Latn-ZZ",rtm:"rtm-Latn-FJ",ru:"ru-Cyrl-RU",rue:"rue-Cyrl-UA",rug:"rug-Latn-SB",rw:"rw-Latn-RW",rwk:"rwk-Latn-TZ",rwo:"rwo-Latn-ZZ",ryu:"ryu-Kana-JP",sa:"sa-Deva-IN",saf:"saf-Latn-GH",sah:"sah-Cyrl-RU",saq:"saq-Latn-KE",sas:"sas-Latn-ID",sat:"sat-Latn-IN",sav:"sav-Latn-SN",saz:"saz-Saur-IN",sba:"sba-Latn-ZZ",sbe:"sbe-Latn-ZZ",sbp:"sbp-Latn-TZ",sc:"sc-Latn-IT",sck:"sck-Deva-IN",scl:"scl-Arab-ZZ",scn:"scn-Latn-IT",sco:"sco-Latn-GB",scs:"scs-Latn-CA",sd:"sd-Arab-PK","sd-Deva":"sd-Deva-IN","sd-Khoj":"sd-Khoj-IN","sd-Sind":"sd-Sind-IN",sdc:"sdc-Latn-IT",sdh:"sdh-Arab-IR",se:"se-Latn-NO",sef:"sef-Latn-CI",seh:"seh-Latn-MZ",sei:"sei-Latn-MX",ses:"ses-Latn-ML",sg:"sg-Latn-CF",sga:"sga-Ogam-IE",sgs:"sgs-Latn-LT",sgw:"sgw-Ethi-ZZ",sgz:"sgz-Latn-ZZ",shi:"shi-Tfng-MA",shk:"shk-Latn-ZZ",shn:"shn-Mymr-MM",shu:"shu-Arab-ZZ",si:"si-Sinh-LK",sid:"sid-Latn-ET",sig:"sig-Latn-ZZ",sil:"sil-Latn-ZZ",sim:"sim-Latn-ZZ",sjr:"sjr-Latn-ZZ",sk:"sk-Latn-SK",skc:"skc-Latn-ZZ",skr:"skr-Arab-PK",sks:"sks-Latn-ZZ",sl:"sl-Latn-SI",sld:"sld-Latn-ZZ",sli:"sli-Latn-PL",sll:"sll-Latn-ZZ",sly:"sly-Latn-ID",sm:"sm-Latn-WS",sma:"sma-Latn-SE",smj:"smj-Latn-SE",smn:"smn-Latn-FI",smp:"smp-Samr-IL",smq:"smq-Latn-ZZ",sms:"sms-Latn-FI",sn:"sn-Latn-ZW",snc:"snc-Latn-ZZ",snk:"snk-Latn-ML",snp:"snp-Latn-ZZ",snx:"snx-Latn-ZZ",sny:"sny-Latn-ZZ",so:"so-Latn-SO",sog:"sog-Sogd-UZ",sok:"sok-Latn-ZZ",soq:"soq-Latn-ZZ",sou:"sou-Thai-TH",soy:"soy-Latn-ZZ",spd:"spd-Latn-ZZ",spl:"spl-Latn-ZZ",sps:"sps-Latn-ZZ",sq:"sq-Latn-AL",sr:"sr-Cyrl-RS","sr-ME":"sr-Latn-ME","sr-RO":"sr-Latn-RO","sr-RU":"sr-Latn-RU","sr-TR":"sr-Latn-TR",srb:"srb-Sora-IN",srn:"srn-Latn-SR",srr:"srr-Latn-SN",srx:"srx-Deva-IN",ss:"ss-Latn-ZA",ssd:"ssd-Latn-ZZ",ssg:"ssg-Latn-ZZ",ssy:"ssy-Latn-ER",st:"st-Latn-ZA",stk:"stk-Latn-ZZ",stq:"stq-Latn-DE",su:"su-Latn-ID",sua:"sua-Latn-ZZ",sue:"sue-Latn-ZZ",suk:"suk-Latn-TZ",sur:"sur-Latn-ZZ",sus:"sus-Latn-GN",sv:"sv-Latn-SE",sw:"sw-Latn-TZ",swb:"swb-Arab-YT",swc:"swc-Latn-CD",swg:"swg-Latn-DE",swp:"swp-Latn-ZZ",swv:"swv-Deva-IN",sxn:"sxn-Latn-ID",sxw:"sxw-Latn-ZZ",syl:"syl-Beng-BD",syr:"syr-Syrc-IQ",szl:"szl-Latn-PL",ta:"ta-Taml-IN",taj:"taj-Deva-NP",tal:"tal-Latn-ZZ",tan:"tan-Latn-ZZ",taq:"taq-Latn-ZZ",tbc:"tbc-Latn-ZZ",tbd:"tbd-Latn-ZZ",tbf:"tbf-Latn-ZZ",tbg:"tbg-Latn-ZZ",tbo:"tbo-Latn-ZZ",tbw:"tbw-Latn-PH",tbz:"tbz-Latn-ZZ",tci:"tci-Latn-ZZ",tcy:"tcy-Knda-IN",tdd:"tdd-Tale-CN",tdg:"tdg-Deva-NP",tdh:"tdh-Deva-NP",tdu:"tdu-Latn-MY",te:"te-Telu-IN",ted:"ted-Latn-ZZ",tem:"tem-Latn-SL",teo:"teo-Latn-UG",tet:"tet-Latn-TL",tfi:"tfi-Latn-ZZ",tg:"tg-Cyrl-TJ","tg-Arab":"tg-Arab-PK","tg-PK":"tg-Arab-PK",tgc:"tgc-Latn-ZZ",tgo:"tgo-Latn-ZZ",tgu:"tgu-Latn-ZZ",th:"th-Thai-TH",thl:"thl-Deva-NP",thq:"thq-Deva-NP",thr:"thr-Deva-NP",ti:"ti-Ethi-ET",tif:"tif-Latn-ZZ",tig:"tig-Ethi-ER",tik:"tik-Latn-ZZ",tim:"tim-Latn-ZZ",tio:"tio-Latn-ZZ",tiv:"tiv-Latn-NG",tk:"tk-Latn-TM",tkl:"tkl-Latn-TK",tkr:"tkr-Latn-AZ",tkt:"tkt-Deva-NP",tl:"tl-Latn-PH",tlf:"tlf-Latn-ZZ",tlx:"tlx-Latn-ZZ",tly:"tly-Latn-AZ",tmh:"tmh-Latn-NE",tmy:"tmy-Latn-ZZ",tn:"tn-Latn-ZA",tnh:"tnh-Latn-ZZ",to:"to-Latn-TO",tof:"tof-Latn-ZZ",tog:"tog-Latn-MW",toq:"toq-Latn-ZZ",tpi:"tpi-Latn-PG",tpm:"tpm-Latn-ZZ",tpz:"tpz-Latn-ZZ",tqo:"tqo-Latn-ZZ",tr:"tr-Latn-TR",tru:"tru-Latn-TR",trv:"trv-Latn-TW",trw:"trw-Arab-ZZ",ts:"ts-Latn-ZA",tsd:"tsd-Grek-GR",tsf:"tsf-Deva-NP",tsg:"tsg-Latn-PH",tsj:"tsj-Tibt-BT",tsw:"tsw-Latn-ZZ",tt:"tt-Cyrl-RU",ttd:"ttd-Latn-ZZ",tte:"tte-Latn-ZZ",ttj:"ttj-Latn-UG",ttr:"ttr-Latn-ZZ",tts:"tts-Thai-TH",ttt:"ttt-Latn-AZ",tuh:"tuh-Latn-ZZ",tul:"tul-Latn-ZZ",tum:"tum-Latn-MW",tuq:"tuq-Latn-ZZ",tvd:"tvd-Latn-ZZ",tvl:"tvl-Latn-TV",tvu:"tvu-Latn-ZZ",twh:"twh-Latn-ZZ",twq:"twq-Latn-NE",txg:"txg-Tang-CN",ty:"ty-Latn-PF",tya:"tya-Latn-ZZ",tyv:"tyv-Cyrl-RU",tzm:"tzm-Latn-MA",ubu:"ubu-Latn-ZZ",udm:"udm-Cyrl-RU",ug:"ug-Arab-CN","ug-Cyrl":"ug-Cyrl-KZ","ug-KZ":"ug-Cyrl-KZ","ug-MN":"ug-Cyrl-MN",uga:"uga-Ugar-SY",uk:"uk-Cyrl-UA",uli:"uli-Latn-FM",umb:"umb-Latn-AO",und:"en-Latn-US","und-002":"en-Latn-NG","und-003":"en-Latn-US","und-005":"pt-Latn-BR","und-009":"en-Latn-AU","und-011":"en-Latn-NG","und-013":"es-Latn-MX","und-014":"sw-Latn-TZ","und-015":"ar-Arab-EG","und-017":"sw-Latn-CD","und-018":"en-Latn-ZA","und-019":"en-Latn-US","und-021":"en-Latn-US","und-029":"es-Latn-CU","und-030":"zh-Hans-CN","und-034":"hi-Deva-IN","und-035":"id-Latn-ID","und-039":"it-Latn-IT","und-053":"en-Latn-AU","und-054":"en-Latn-PG","und-057":"en-Latn-GU","und-061":"sm-Latn-WS","und-142":"zh-Hans-CN","und-143":"uz-Latn-UZ","und-145":"ar-Arab-SA","und-150":"ru-Cyrl-RU","und-151":"ru-Cyrl-RU","und-154":"en-Latn-GB","und-155":"de-Latn-DE","und-202":"en-Latn-NG","und-419":"es-Latn-419","und-AD":"ca-Latn-AD","und-Adlm":"ff-Adlm-GN","und-AE":"ar-Arab-AE","und-AF":"fa-Arab-AF","und-Aghb":"lez-Aghb-RU","und-Ahom":"aho-Ahom-IN","und-AL":"sq-Latn-AL","und-AM":"hy-Armn-AM","und-AO":"pt-Latn-AO","und-AQ":"und-Latn-AQ","und-AR":"es-Latn-AR","und-Arab":"ar-Arab-EG","und-Arab-CC":"ms-Arab-CC","und-Arab-CN":"ug-Arab-CN","und-Arab-GB":"ks-Arab-GB","und-Arab-ID":"ms-Arab-ID","und-Arab-IN":"ur-Arab-IN","und-Arab-KH":"cja-Arab-KH","und-Arab-MM":"rhg-Arab-MM","und-Arab-MN":"kk-Arab-MN","und-Arab-MU":"ur-Arab-MU","und-Arab-NG":"ha-Arab-NG","und-Arab-PK":"ur-Arab-PK","und-Arab-TG":"apd-Arab-TG","und-Arab-TH":"mfa-Arab-TH","und-Arab-TJ":"fa-Arab-TJ","und-Arab-TR":"az-Arab-TR","und-Arab-YT":"swb-Arab-YT","und-Armi":"arc-Armi-IR","und-Armn":"hy-Armn-AM","und-AS":"sm-Latn-AS","und-AT":"de-Latn-AT","und-Avst":"ae-Avst-IR","und-AW":"nl-Latn-AW","und-AX":"sv-Latn-AX","und-AZ":"az-Latn-AZ","und-BA":"bs-Latn-BA","und-Bali":"ban-Bali-ID","und-Bamu":"bax-Bamu-CM","und-Bass":"bsq-Bass-LR","und-Batk":"bbc-Batk-ID","und-BD":"bn-Beng-BD","und-BE":"nl-Latn-BE","und-Beng":"bn-Beng-BD","und-BF":"fr-Latn-BF","und-BG":"bg-Cyrl-BG","und-BH":"ar-Arab-BH","und-Bhks":"sa-Bhks-IN","und-BI":"rn-Latn-BI","und-BJ":"fr-Latn-BJ","und-BL":"fr-Latn-BL","und-BN":"ms-Latn-BN","und-BO":"es-Latn-BO","und-Bopo":"zh-Bopo-TW","und-BQ":"pap-Latn-BQ","und-BR":"pt-Latn-BR","und-Brah":"pka-Brah-IN","und-Brai":"fr-Brai-FR","und-BT":"dz-Tibt-BT","und-Bugi":"bug-Bugi-ID","und-Buhd":"bku-Buhd-PH","und-BV":"und-Latn-BV","und-BY":"be-Cyrl-BY","und-Cakm":"ccp-Cakm-BD","und-Cans":"cr-Cans-CA","und-Cari":"xcr-Cari-TR","und-CD":"sw-Latn-CD","und-CF":"fr-Latn-CF","und-CG":"fr-Latn-CG","und-CH":"de-Latn-CH","und-Cham":"cjm-Cham-VN","und-Cher":"chr-Cher-US","und-CI":"fr-Latn-CI","und-CL":"es-Latn-CL","und-CM":"fr-Latn-CM","und-CN":"zh-Hans-CN","und-CO":"es-Latn-CO","und-Copt":"cop-Copt-EG","und-CP":"und-Latn-CP","und-Cprt":"grc-Cprt-CY","und-CR":"es-Latn-CR","und-CU":"es-Latn-CU","und-CV":"pt-Latn-CV","und-CW":"pap-Latn-CW","und-CY":"el-Grek-CY","und-Cyrl":"ru-Cyrl-RU","und-Cyrl-AL":"mk-Cyrl-AL","und-Cyrl-BA":"sr-Cyrl-BA","und-Cyrl-GE":"ab-Cyrl-GE","und-Cyrl-GR":"mk-Cyrl-GR","und-Cyrl-MD":"uk-Cyrl-MD","und-Cyrl-RO":"bg-Cyrl-RO","und-Cyrl-SK":"uk-Cyrl-SK","und-Cyrl-TR":"kbd-Cyrl-TR","und-Cyrl-XK":"sr-Cyrl-XK","und-CZ":"cs-Latn-CZ","und-DE":"de-Latn-DE","und-Deva":"hi-Deva-IN","und-Deva-BT":"ne-Deva-BT","und-Deva-FJ":"hif-Deva-FJ","und-Deva-MU":"bho-Deva-MU","und-Deva-PK":"btv-Deva-PK","und-DJ":"aa-Latn-DJ","und-DK":"da-Latn-DK","und-DO":"es-Latn-DO","und-Dogr":"doi-Dogr-IN","und-Dupl":"fr-Dupl-FR","und-DZ":"ar-Arab-DZ","und-EA":"es-Latn-EA","und-EC":"es-Latn-EC","und-EE":"et-Latn-EE","und-EG":"ar-Arab-EG","und-Egyp":"egy-Egyp-EG","und-EH":"ar-Arab-EH","und-Elba":"sq-Elba-AL","und-Elym":"arc-Elym-IR","und-ER":"ti-Ethi-ER","und-ES":"es-Latn-ES","und-ET":"am-Ethi-ET","und-Ethi":"am-Ethi-ET","und-EU":"en-Latn-GB","und-EZ":"de-Latn-EZ","und-FI":"fi-Latn-FI","und-FO":"fo-Latn-FO","und-FR":"fr-Latn-FR","und-GA":"fr-Latn-GA","und-GE":"ka-Geor-GE","und-Geor":"ka-Geor-GE","und-GF":"fr-Latn-GF","und-GH":"ak-Latn-GH","und-GL":"kl-Latn-GL","und-Glag":"cu-Glag-BG","und-GN":"fr-Latn-GN","und-Gong":"wsg-Gong-IN","und-Gonm":"esg-Gonm-IN","und-Goth":"got-Goth-UA","und-GP":"fr-Latn-GP","und-GQ":"es-Latn-GQ","und-GR":"el-Grek-GR","und-Gran":"sa-Gran-IN","und-Grek":"el-Grek-GR","und-Grek-TR":"bgx-Grek-TR","und-GS":"und-Latn-GS","und-GT":"es-Latn-GT","und-Gujr":"gu-Gujr-IN","und-Guru":"pa-Guru-IN","und-GW":"pt-Latn-GW","und-Hanb":"zh-Hanb-TW","und-Hang":"ko-Hang-KR","und-Hani":"zh-Hani-CN","und-Hano":"hnn-Hano-PH","und-Hans":"zh-Hans-CN","und-Hant":"zh-Hant-TW","und-Hatr":"mis-Hatr-IQ","und-Hebr":"he-Hebr-IL","und-Hebr-CA":"yi-Hebr-CA","und-Hebr-GB":"yi-Hebr-GB","und-Hebr-SE":"yi-Hebr-SE","und-Hebr-UA":"yi-Hebr-UA","und-Hebr-US":"yi-Hebr-US","und-Hira":"ja-Hira-JP","und-HK":"zh-Hant-HK","und-Hluw":"hlu-Hluw-TR","und-HM":"und-Latn-HM","und-Hmng":"hnj-Hmng-LA","und-Hmnp":"mww-Hmnp-US","und-HN":"es-Latn-HN","und-HR":"hr-Latn-HR","und-HT":"ht-Latn-HT","und-HU":"hu-Latn-HU","und-Hung":"hu-Hung-HU","und-IC":"es-Latn-IC","und-ID":"id-Latn-ID","und-IL":"he-Hebr-IL","und-IN":"hi-Deva-IN","und-IQ":"ar-Arab-IQ","und-IR":"fa-Arab-IR","und-IS":"is-Latn-IS","und-IT":"it-Latn-IT","und-Ital":"ett-Ital-IT","und-Jamo":"ko-Jamo-KR","und-Java":"jv-Java-ID","und-JO":"ar-Arab-JO","und-JP":"ja-Jpan-JP","und-Jpan":"ja-Jpan-JP","und-Kali":"eky-Kali-MM","und-Kana":"ja-Kana-JP","und-KE":"sw-Latn-KE","und-KG":"ky-Cyrl-KG","und-KH":"km-Khmr-KH","und-Khar":"pra-Khar-PK","und-Khmr":"km-Khmr-KH","und-Khoj":"sd-Khoj-IN","und-KM":"ar-Arab-KM","und-Knda":"kn-Knda-IN","und-Kore":"ko-Kore-KR","und-KP":"ko-Kore-KP","und-KR":"ko-Kore-KR","und-Kthi":"bho-Kthi-IN","und-KW":"ar-Arab-KW","und-KZ":"ru-Cyrl-KZ","und-LA":"lo-Laoo-LA","und-Lana":"nod-Lana-TH","und-Laoo":"lo-Laoo-LA","und-Latn-AF":"tk-Latn-AF","und-Latn-AM":"ku-Latn-AM","und-Latn-CN":"za-Latn-CN","und-Latn-CY":"tr-Latn-CY","und-Latn-DZ":"fr-Latn-DZ","und-Latn-ET":"en-Latn-ET","und-Latn-GE":"ku-Latn-GE","und-Latn-IR":"tk-Latn-IR","und-Latn-KM":"fr-Latn-KM","und-Latn-MA":"fr-Latn-MA","und-Latn-MK":"sq-Latn-MK","und-Latn-MM":"kac-Latn-MM","und-Latn-MO":"pt-Latn-MO","und-Latn-MR":"fr-Latn-MR","und-Latn-RU":"krl-Latn-RU","und-Latn-SY":"fr-Latn-SY","und-Latn-TN":"fr-Latn-TN","und-Latn-TW":"trv-Latn-TW","und-Latn-UA":"pl-Latn-UA","und-LB":"ar-Arab-LB","und-Lepc":"lep-Lepc-IN","und-LI":"de-Latn-LI","und-Limb":"lif-Limb-IN","und-Lina":"lab-Lina-GR","und-Linb":"grc-Linb-GR","und-Lisu":"lis-Lisu-CN","und-LK":"si-Sinh-LK","und-LS":"st-Latn-LS","und-LT":"lt-Latn-LT","und-LU":"fr-Latn-LU","und-LV":"lv-Latn-LV","und-LY":"ar-Arab-LY","und-Lyci":"xlc-Lyci-TR","und-Lydi":"xld-Lydi-TR","und-MA":"ar-Arab-MA","und-Mahj":"hi-Mahj-IN","und-Maka":"mak-Maka-ID","und-Mand":"myz-Mand-IR","und-Mani":"xmn-Mani-CN","und-Marc":"bo-Marc-CN","und-MC":"fr-Latn-MC","und-MD":"ro-Latn-MD","und-ME":"sr-Latn-ME","und-Medf":"mis-Medf-NG","und-Mend":"men-Mend-SL","und-Merc":"xmr-Merc-SD","und-Mero":"xmr-Mero-SD","und-MF":"fr-Latn-MF","und-MG":"mg-Latn-MG","und-MK":"mk-Cyrl-MK","und-ML":"bm-Latn-ML","und-Mlym":"ml-Mlym-IN","und-MM":"my-Mymr-MM","und-MN":"mn-Cyrl-MN","und-MO":"zh-Hant-MO","und-Modi":"mr-Modi-IN","und-Mong":"mn-Mong-CN","und-MQ":"fr-Latn-MQ","und-MR":"ar-Arab-MR","und-Mroo":"mro-Mroo-BD","und-MT":"mt-Latn-MT","und-Mtei":"mni-Mtei-IN","und-MU":"mfe-Latn-MU","und-Mult":"skr-Mult-PK","und-MV":"dv-Thaa-MV","und-MX":"es-Latn-MX","und-MY":"ms-Latn-MY","und-Mymr":"my-Mymr-MM","und-Mymr-IN":"kht-Mymr-IN","und-Mymr-TH":"mnw-Mymr-TH","und-MZ":"pt-Latn-MZ","und-NA":"af-Latn-NA","und-Nand":"sa-Nand-IN","und-Narb":"xna-Narb-SA","und-Nbat":"arc-Nbat-JO","und-NC":"fr-Latn-NC","und-NE":"ha-Latn-NE","und-Newa":"new-Newa-NP","und-NI":"es-Latn-NI","und-Nkoo":"man-Nkoo-GN","und-NL":"nl-Latn-NL","und-NO":"nb-Latn-NO","und-NP":"ne-Deva-NP","und-Nshu":"zhx-Nshu-CN","und-Ogam":"sga-Ogam-IE","und-Olck":"sat-Olck-IN","und-OM":"ar-Arab-OM","und-Orkh":"otk-Orkh-MN","und-Orya":"or-Orya-IN","und-Osge":"osa-Osge-US","und-Osma":"so-Osma-SO","und-PA":"es-Latn-PA","und-Palm":"arc-Palm-SY","und-Pauc":"ctd-Pauc-MM","und-PE":"es-Latn-PE","und-Perm":"kv-Perm-RU","und-PF":"fr-Latn-PF","und-PG":"tpi-Latn-PG","und-PH":"fil-Latn-PH","und-Phag":"lzh-Phag-CN","und-Phli":"pal-Phli-IR","und-Phlp":"pal-Phlp-CN","und-Phnx":"phn-Phnx-LB","und-PK":"ur-Arab-PK","und-PL":"pl-Latn-PL","und-Plrd":"hmd-Plrd-CN","und-PM":"fr-Latn-PM","und-PR":"es-Latn-PR","und-Prti":"xpr-Prti-IR","und-PS":"ar-Arab-PS","und-PT":"pt-Latn-PT","und-PW":"pau-Latn-PW","und-PY":"gn-Latn-PY","und-QA":"ar-Arab-QA","und-QO":"en-Latn-DG","und-RE":"fr-Latn-RE","und-Rjng":"rej-Rjng-ID","und-RO":"ro-Latn-RO","und-Rohg":"rhg-Rohg-MM","und-RS":"sr-Cyrl-RS","und-RU":"ru-Cyrl-RU","und-Runr":"non-Runr-SE","und-RW":"rw-Latn-RW","und-SA":"ar-Arab-SA","und-Samr":"smp-Samr-IL","und-Sarb":"xsa-Sarb-YE","und-Saur":"saz-Saur-IN","und-SC":"fr-Latn-SC","und-SD":"ar-Arab-SD","und-SE":"sv-Latn-SE","und-Sgnw":"ase-Sgnw-US","und-Shaw":"en-Shaw-GB","und-Shrd":"sa-Shrd-IN","und-SI":"sl-Latn-SI","und-Sidd":"sa-Sidd-IN","und-Sind":"sd-Sind-IN","und-Sinh":"si-Sinh-LK","und-SJ":"nb-Latn-SJ","und-SK":"sk-Latn-SK","und-SM":"it-Latn-SM","und-SN":"fr-Latn-SN","und-SO":"so-Latn-SO","und-Sogd":"sog-Sogd-UZ","und-Sogo":"sog-Sogo-UZ","und-Sora":"srb-Sora-IN","und-Soyo":"cmg-Soyo-MN","und-SR":"nl-Latn-SR","und-ST":"pt-Latn-ST","und-Sund":"su-Sund-ID","und-SV":"es-Latn-SV","und-SY":"ar-Arab-SY","und-Sylo":"syl-Sylo-BD","und-Syrc":"syr-Syrc-IQ","und-Tagb":"tbw-Tagb-PH","und-Takr":"doi-Takr-IN","und-Tale":"tdd-Tale-CN","und-Talu":"khb-Talu-CN","und-Taml":"ta-Taml-IN","und-Tang":"txg-Tang-CN","und-Tavt":"blt-Tavt-VN","und-TD":"fr-Latn-TD","und-Telu":"te-Telu-IN","und-TF":"fr-Latn-TF","und-Tfng":"zgh-Tfng-MA","und-TG":"fr-Latn-TG","und-Tglg":"fil-Tglg-PH","und-TH":"th-Thai-TH","und-Thaa":"dv-Thaa-MV","und-Thai":"th-Thai-TH","und-Thai-CN":"lcp-Thai-CN","und-Thai-KH":"kdt-Thai-KH","und-Thai-LA":"kdt-Thai-LA","und-Tibt":"bo-Tibt-CN","und-Tirh":"mai-Tirh-IN","und-TJ":"tg-Cyrl-TJ","und-TK":"tkl-Latn-TK","und-TL":"pt-Latn-TL","und-TM":"tk-Latn-TM","und-TN":"ar-Arab-TN","und-TO":"to-Latn-TO","und-TR":"tr-Latn-TR","und-TV":"tvl-Latn-TV","und-TW":"zh-Hant-TW","und-TZ":"sw-Latn-TZ","und-UA":"uk-Cyrl-UA","und-UG":"sw-Latn-UG","und-Ugar":"uga-Ugar-SY","und-UY":"es-Latn-UY","und-UZ":"uz-Latn-UZ","und-VA":"it-Latn-VA","und-Vaii":"vai-Vaii-LR","und-VE":"es-Latn-VE","und-VN":"vi-Latn-VN","und-VU":"bi-Latn-VU","und-Wara":"hoc-Wara-IN","und-Wcho":"nnp-Wcho-IN","und-WF":"fr-Latn-WF","und-WS":"sm-Latn-WS","und-XK":"sq-Latn-XK","und-Xpeo":"peo-Xpeo-IR","und-Xsux":"akk-Xsux-IQ","und-YE":"ar-Arab-YE","und-Yiii":"ii-Yiii-CN","und-YT":"fr-Latn-YT","und-Zanb":"cmg-Zanb-MN","und-ZW":"sn-Latn-ZW",unr:"unr-Beng-IN","unr-Deva":"unr-Deva-NP","unr-NP":"unr-Deva-NP",unx:"unx-Beng-IN",uok:"uok-Latn-ZZ",ur:"ur-Arab-PK",uri:"uri-Latn-ZZ",urt:"urt-Latn-ZZ",urw:"urw-Latn-ZZ",usa:"usa-Latn-ZZ",utr:"utr-Latn-ZZ",uvh:"uvh-Latn-ZZ",uvl:"uvl-Latn-ZZ",uz:"uz-Latn-UZ","uz-AF":"uz-Arab-AF","uz-Arab":"uz-Arab-AF","uz-CN":"uz-Cyrl-CN",vag:"vag-Latn-ZZ",vai:"vai-Vaii-LR",van:"van-Latn-ZZ",ve:"ve-Latn-ZA",vec:"vec-Latn-IT",vep:"vep-Latn-RU",vi:"vi-Latn-VN",vic:"vic-Latn-SX",viv:"viv-Latn-ZZ",vls:"vls-Latn-BE",vmf:"vmf-Latn-DE",vmw:"vmw-Latn-MZ",vo:"vo-Latn-001",vot:"vot-Latn-RU",vro:"vro-Latn-EE",vun:"vun-Latn-TZ",vut:"vut-Latn-ZZ",wa:"wa-Latn-BE",wae:"wae-Latn-CH",waj:"waj-Latn-ZZ",wal:"wal-Ethi-ET",wan:"wan-Latn-ZZ",war:"war-Latn-PH",wbp:"wbp-Latn-AU",wbq:"wbq-Telu-IN",wbr:"wbr-Deva-IN",wci:"wci-Latn-ZZ",wer:"wer-Latn-ZZ",wgi:"wgi-Latn-ZZ",whg:"whg-Latn-ZZ",wib:"wib-Latn-ZZ",wiu:"wiu-Latn-ZZ",wiv:"wiv-Latn-ZZ",wja:"wja-Latn-ZZ",wji:"wji-Latn-ZZ",wls:"wls-Latn-WF",wmo:"wmo-Latn-ZZ",wnc:"wnc-Latn-ZZ",wni:"wni-Arab-KM",wnu:"wnu-Latn-ZZ",wo:"wo-Latn-SN",wob:"wob-Latn-ZZ",wos:"wos-Latn-ZZ",wrs:"wrs-Latn-ZZ",wsg:"wsg-Gong-IN",wsk:"wsk-Latn-ZZ",wtm:"wtm-Deva-IN",wuu:"wuu-Hans-CN",wuv:"wuv-Latn-ZZ",wwa:"wwa-Latn-ZZ",xav:"xav-Latn-BR",xbi:"xbi-Latn-ZZ",xcr:"xcr-Cari-TR",xes:"xes-Latn-ZZ",xh:"xh-Latn-ZA",xla:"xla-Latn-ZZ",xlc:"xlc-Lyci-TR",xld:"xld-Lydi-TR",xmf:"xmf-Geor-GE",xmn:"xmn-Mani-CN",xmr:"xmr-Merc-SD",xna:"xna-Narb-SA",xnr:"xnr-Deva-IN",xog:"xog-Latn-UG",xon:"xon-Latn-ZZ",xpr:"xpr-Prti-IR",xrb:"xrb-Latn-ZZ",xsa:"xsa-Sarb-YE",xsi:"xsi-Latn-ZZ",xsm:"xsm-Latn-ZZ",xsr:"xsr-Deva-NP",xwe:"xwe-Latn-ZZ",yam:"yam-Latn-ZZ",yao:"yao-Latn-MZ",yap:"yap-Latn-FM",yas:"yas-Latn-ZZ",yat:"yat-Latn-ZZ",yav:"yav-Latn-CM",yay:"yay-Latn-ZZ",yaz:"yaz-Latn-ZZ",yba:"yba-Latn-ZZ",ybb:"ybb-Latn-CM",yby:"yby-Latn-ZZ",yer:"yer-Latn-ZZ",ygr:"ygr-Latn-ZZ",ygw:"ygw-Latn-ZZ",yi:"yi-Hebr-001",yko:"yko-Latn-ZZ",yle:"yle-Latn-ZZ",ylg:"ylg-Latn-ZZ",yll:"yll-Latn-ZZ",yml:"yml-Latn-ZZ",yo:"yo-Latn-NG",yon:"yon-Latn-ZZ",yrb:"yrb-Latn-ZZ",yre:"yre-Latn-ZZ",yrl:"yrl-Latn-BR",yss:"yss-Latn-ZZ",yua:"yua-Latn-MX",yue:"yue-Hant-HK","yue-CN":"yue-Hans-CN","yue-Hans":"yue-Hans-CN",yuj:"yuj-Latn-ZZ",yut:"yut-Latn-ZZ",yuw:"yuw-Latn-ZZ",za:"za-Latn-CN",zag:"zag-Latn-SD",zdj:"zdj-Arab-KM",zea:"zea-Latn-NL",zgh:"zgh-Tfng-MA",zh:"zh-Hans-CN","zh-AU":"zh-Hant-AU","zh-BN":"zh-Hant-BN","zh-Bopo":"zh-Bopo-TW","zh-GB":"zh-Hant-GB","zh-GF":"zh-Hant-GF","zh-Hanb":"zh-Hanb-TW","zh-Hant":"zh-Hant-TW","zh-HK":"zh-Hant-HK","zh-ID":"zh-Hant-ID","zh-MO":"zh-Hant-MO","zh-MY":"zh-Hant-MY","zh-PA":"zh-Hant-PA","zh-PF":"zh-Hant-PF","zh-PH":"zh-Hant-PH","zh-SR":"zh-Hant-SR","zh-TH":"zh-Hant-TH","zh-TW":"zh-Hant-TW","zh-US":"zh-Hant-US","zh-VN":"zh-Hant-VN",zhx:"zhx-Nshu-CN",zia:"zia-Latn-ZZ",zlm:"zlm-Latn-TG",zmi:"zmi-Latn-MY",zne:"zne-Latn-ZZ",zu:"zu-Latn-ZA",zza:"zza-Latn-TR"}}},{main:{pl:{identity:{version:{_cldrVersion:"36"},language:"pl"},numbers:{defaultNumberingSystem:"latn",otherNumberingSystems:{native:"latn"},minimumGroupingDigits:"2","symbols-numberSystem-latn":{decimal:",",group:" ",list:";",percentSign:"%",plusSign:"+",minusSign:"-",exponential:"E",superscriptingExponent:"×",perMille:"‰",infinity:"∞",nan:"NaN",timeSeparator:":"},"decimalFormats-numberSystem-latn":{standard:"#,##0.###",long:{decimalFormat:{"1000-count-one":"0 tysiąc","1000-count-few":"0 tysiące","1000-count-many":"0 tysięcy","1000-count-other":"0 tysiąca","10000-count-one":"00 tysiąc","10000-count-few":"00 tysiące","10000-count-many":"00 tysięcy","10000-count-other":"00 tysiąca","100000-count-one":"000 tysiąc","100000-count-few":"000 tysiące","100000-count-many":"000 tysięcy","100000-count-other":"000 tysiąca","1000000-count-one":"0 milion","1000000-count-few":"0 miliony","1000000-count-many":"0 milionów","1000000-count-other":"0 miliona","********-count-one":"00 milion","********-count-few":"00 miliony","********-count-many":"00 milionów","********-count-other":"00 miliona","********0-count-one":"000 milion","********0-count-few":"000 miliony","********0-count-many":"000 milionów","********0-count-other":"000 miliona","**********-count-one":"0 miliard","**********-count-few":"0 miliardy","**********-count-many":"0 miliardów","**********-count-other":"0 miliarda","**********0-count-one":"00 miliard","**********0-count-few":"00 miliardy","**********0-count-many":"00 miliardów","**********0-count-other":"00 miliarda","**********00-count-one":"000 miliard","**********00-count-few":"000 miliardy","**********00-count-many":"000 miliardów","**********00-count-other":"000 miliarda","**********000-count-one":"0 bilion","**********000-count-few":"0 biliony","**********000-count-many":"0 bilionów","**********000-count-other":"0 biliona","**********0000-count-one":"00 bilion","**********0000-count-few":"00 biliony","**********0000-count-many":"00 bilionów","**********0000-count-other":"00 biliona","**********00000-count-one":"000 bilion","**********00000-count-few":"000 biliony","**********00000-count-many":"000 bilionów","**********00000-count-other":"000 biliona"}},short:{decimalFormat:{"1000-count-one":"0 tys'.'","1000-count-few":"0 tys'.'","1000-count-many":"0 tys'.'","1000-count-other":"0 tys'.'","10000-count-one":"00 tys'.'","10000-count-few":"00 tys'.'","10000-count-many":"00 tys'.'","10000-count-other":"00 tys'.'","100000-count-one":"000 tys'.'","100000-count-few":"000 tys'.'","100000-count-many":"000 tys'.'","100000-count-other":"000 tys'.'","1000000-count-one":"0 mln","1000000-count-few":"0 mln","1000000-count-many":"0 mln","1000000-count-other":"0 mln","********-count-one":"00 mln","********-count-few":"00 mln","********-count-many":"00 mln","********-count-other":"00 mln","********0-count-one":"000 mln","********0-count-few":"000 mln","********0-count-many":"000 mln","********0-count-other":"000 mln","**********-count-one":"0 mld","**********-count-few":"0 mld","**********-count-many":"0 mld","**********-count-other":"0 mld","**********0-count-one":"00 mld","**********0-count-few":"00 mld","**********0-count-many":"00 mld","**********0-count-other":"00 mld","**********00-count-one":"000 mld","**********00-count-few":"000 mld","**********00-count-many":"000 mld","**********00-count-other":"000 mld","**********000-count-one":"0 bln","**********000-count-few":"0 bln","**********000-count-many":"0 bln","**********000-count-other":"0 bln","**********0000-count-one":"00 bln","**********0000-count-few":"00 bln","**********0000-count-many":"00 bln","**********0000-count-other":"00 bln","**********00000-count-one":"000 bln","**********00000-count-few":"000 bln","**********00000-count-many":"000 bln","**********00000-count-other":"000 bln"}}},"scientificFormats-numberSystem-latn":{standard:"#E0"},"percentFormats-numberSystem-latn":{standard:"#,##0%"},"currencyFormats-numberSystem-latn":{currencySpacing:{beforeCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "},afterCurrency:{currencyMatch:"[:^S:]",surroundingMatch:"[:digit:]",insertBetween:" "}},standard:"#,##0.00 ¤",accounting:"#,##0.00 ¤;(#,##0.00 ¤)",short:{standard:{"1000-count-one":"0 tys'.' ¤","1000-count-few":"0 tys'.' ¤","1000-count-many":"0 tys'.' ¤","1000-count-other":"0 tys'.' ¤","10000-count-one":"00 tys'.' ¤","10000-count-few":"00 tys'.' ¤","10000-count-many":"00 tys'.' ¤","10000-count-other":"00 tys'.' ¤","100000-count-one":"000 tys'.' ¤","100000-count-few":"000 tys'.' ¤","100000-count-many":"000 tys'.' ¤","100000-count-other":"000 tys'.' ¤","1000000-count-one":"0 mln ¤","1000000-count-few":"0 mln ¤","1000000-count-many":"0 mln ¤","1000000-count-other":"0 mln ¤","********-count-one":"00 mln ¤","********-count-few":"00 mln ¤","********-count-many":"00 mln ¤","********-count-other":"00 mln ¤","********0-count-one":"000 mln ¤","********0-count-few":"000 mln ¤","********0-count-many":"000 mln ¤","********0-count-other":"000 mln ¤","**********-count-one":"0 mld ¤","**********-count-few":"0 mld ¤","**********-count-many":"0 mld ¤","**********-count-other":"0 mld ¤","**********0-count-one":"00 mld ¤","**********0-count-few":"00 mld ¤","**********0-count-many":"00 mld ¤","**********0-count-other":"00 mld ¤","**********00-count-one":"000 mld ¤","**********00-count-few":"000 mld ¤","**********00-count-many":"000 mld ¤","**********00-count-other":"000 mld ¤","**********000-count-one":"0 bln ¤","**********000-count-few":"0 bln ¤","**********000-count-many":"0 bln ¤","**********000-count-other":"0 bln ¤","**********0000-count-one":"00 bln ¤","**********0000-count-few":"00 bln ¤","**********0000-count-many":"00 bln ¤","**********0000-count-other":"00 bln ¤","**********00000-count-one":"000 bln ¤","**********00000-count-few":"000 bln ¤","**********00000-count-many":"000 bln ¤","**********00000-count-other":"000 bln ¤"}},"unitPattern-count-one":"{0} {1}","unitPattern-count-few":"{0} {1}","unitPattern-count-many":"{0} {1}","unitPattern-count-other":"{0} {1}"},"miscPatterns-numberSystem-latn":{approximately:"~{0}",atLeast:"{0}+",atMost:"≤{0}",range:"{0}–{1}"},minimalPairs:{"pluralMinimalPairs-count-one":"{0} miesiąc","pluralMinimalPairs-count-few":"{0} miesiące","pluralMinimalPairs-count-many":"{0} miesięcy","pluralMinimalPairs-count-other":"{0} miesiąca",other:"Skręć w {0} w prawo."}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},numberingSystems:{adlm:{_digits:"𞥐𞥑𞥒𞥓𞥔𞥕𞥖𞥗𞥘𞥙",_type:"numeric"},ahom:{_digits:"𑜰𑜱𑜲𑜳𑜴𑜵𑜶𑜷𑜸𑜹",_type:"numeric"},arab:{_digits:"٠١٢٣٤٥٦٧٨٩",_type:"numeric"},arabext:{_digits:"۰۱۲۳۴۵۶۷۸۹",_type:"numeric"},armn:{_rules:"armenian-upper",_type:"algorithmic"},armnlow:{_rules:"armenian-lower",_type:"algorithmic"},bali:{_digits:"᭐᭑᭒᭓᭔᭕᭖᭗᭘᭙",_type:"numeric"},beng:{_digits:"০১২৩৪৫৬৭৮৯",_type:"numeric"},bhks:{_digits:"𑱐𑱑𑱒𑱓𑱔𑱕𑱖𑱗𑱘𑱙",_type:"numeric"},brah:{_digits:"𑁦𑁧𑁨𑁩𑁪𑁫𑁬𑁭𑁮𑁯",_type:"numeric"},cakm:{_digits:"𑄶𑄷𑄸𑄹𑄺𑄻𑄼𑄽𑄾𑄿",_type:"numeric"},cham:{_digits:"꩐꩑꩒꩓꩔꩕꩖꩗꩘꩙",_type:"numeric"},cyrl:{_rules:"cyrillic-lower",_type:"algorithmic"},deva:{_digits:"०१२३४५६७८९",_type:"numeric"},ethi:{_rules:"ethiopic",_type:"algorithmic"},fullwide:{_digits:"０１２３４５６７８９",_type:"numeric"},geor:{_rules:"georgian",_type:"algorithmic"},gong:{_digits:"𑶠𑶡𑶢𑶣𑶤𑶥𑶦𑶧𑶨𑶩",_type:"numeric"},gonm:{_digits:"𑵐𑵑𑵒𑵓𑵔𑵕𑵖𑵗𑵘𑵙",_type:"numeric"},grek:{_rules:"greek-upper",_type:"algorithmic"},greklow:{_rules:"greek-lower",_type:"algorithmic"},gujr:{_digits:"૦૧૨૩૪૫૬૭૮૯",_type:"numeric"},guru:{_digits:"੦੧੨੩੪੫੬੭੮੯",_type:"numeric"},hanidays:{_rules:"zh/SpelloutRules/spellout-numbering-days",_type:"algorithmic"},hanidec:{_digits:"〇一二三四五六七八九",_type:"numeric"},hans:{_rules:"zh/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hansfin:{_rules:"zh/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hant:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal",_type:"algorithmic"},hantfin:{_rules:"zh_Hant/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},hebr:{_rules:"hebrew",_type:"algorithmic"},hmng:{_digits:"𖭐𖭑𖭒𖭓𖭔𖭕𖭖𖭗𖭘𖭙",_type:"numeric"},hmnp:{_digits:"𞅀𞅁𞅂𞅃𞅄𞅅𞅆𞅇𞅈𞅉",_type:"numeric"},java:{_digits:"꧐꧑꧒꧓꧔꧕꧖꧗꧘꧙",_type:"numeric"},jpan:{_rules:"ja/SpelloutRules/spellout-cardinal",_type:"algorithmic"},jpanfin:{_rules:"ja/SpelloutRules/spellout-cardinal-financial",_type:"algorithmic"},jpanyear:{_rules:"ja/SpelloutRules/spellout-numbering-year-latn",_type:"algorithmic"},kali:{_digits:"꤀꤁꤂꤃꤄꤅꤆꤇꤈꤉",_type:"numeric"},khmr:{_digits:"០១២៣៤៥៦៧៨៩",_type:"numeric"},knda:{_digits:"೦೧೨೩೪೫೬೭೮೯",_type:"numeric"},lana:{_digits:"᪀᪁᪂᪃᪄᪅᪆᪇᪈᪉",_type:"numeric"},lanatham:{_digits:"᪐᪑᪒᪓᪔᪕᪖᪗᪘᪙",_type:"numeric"},laoo:{_digits:"໐໑໒໓໔໕໖໗໘໙",_type:"numeric"},latn:{_digits:"0123456789",_type:"numeric"},lepc:{_digits:"᱀᱁᱂᱃᱄᱅᱆᱇᱈᱉",_type:"numeric"},limb:{_digits:"᥆᥇᥈᥉᥊᥋᥌᥍᥎᥏",_type:"numeric"},mathbold:{_digits:"𝟎𝟏𝟐𝟑𝟒𝟓𝟔𝟕𝟖𝟗",_type:"numeric"},mathdbl:{_digits:"𝟘𝟙𝟚𝟛𝟜𝟝𝟞𝟟𝟠𝟡",_type:"numeric"},mathmono:{_digits:"𝟶𝟷𝟸𝟹𝟺𝟻𝟼𝟽𝟾𝟿",_type:"numeric"},mathsanb:{_digits:"𝟬𝟭𝟮𝟯𝟰𝟱𝟲𝟳𝟴𝟵",_type:"numeric"},mathsans:{_digits:"𝟢𝟣𝟤𝟥𝟦𝟧𝟨𝟩𝟪𝟫",_type:"numeric"},mlym:{_digits:"൦൧൨൩൪൫൬൭൮൯",_type:"numeric"},modi:{_digits:"𑙐𑙑𑙒𑙓𑙔𑙕𑙖𑙗𑙘𑙙",_type:"numeric"},mong:{_digits:"᠐᠑᠒᠓᠔᠕᠖᠗᠘᠙",_type:"numeric"},mroo:{_digits:"𖩠𖩡𖩢𖩣𖩤𖩥𖩦𖩧𖩨𖩩",_type:"numeric"},mtei:{_digits:"꯰꯱꯲꯳꯴꯵꯶꯷꯸꯹",_type:"numeric"},mymr:{_digits:"၀၁၂၃၄၅၆၇၈၉",_type:"numeric"},mymrshan:{_digits:"႐႑႒႓႔႕႖႗႘႙",_type:"numeric"},mymrtlng:{_digits:"꧰꧱꧲꧳꧴꧵꧶꧷꧸꧹",_type:"numeric"},newa:{_digits:"𑑐𑑑𑑒𑑓𑑔𑑕𑑖𑑗𑑘𑑙",_type:"numeric"},nkoo:{_digits:"߀߁߂߃߄߅߆߇߈߉",_type:"numeric"},olck:{_digits:"᱐᱑᱒᱓᱔᱕᱖᱗᱘᱙",_type:"numeric"},orya:{_digits:"୦୧୨୩୪୫୬୭୮୯",_type:"numeric"},osma:{_digits:"𐒠𐒡𐒢𐒣𐒤𐒥𐒦𐒧𐒨𐒩",_type:"numeric"},rohg:{_digits:"𐴰𐴱𐴲𐴳𐴴𐴵𐴶𐴷𐴸𐴹",_type:"numeric"},roman:{_rules:"roman-upper",_type:"algorithmic"},romanlow:{_rules:"roman-lower",_type:"algorithmic"},saur:{_digits:"꣐꣑꣒꣓꣔꣕꣖꣗꣘꣙",_type:"numeric"},shrd:{_digits:"𑇐𑇑𑇒𑇓𑇔𑇕𑇖𑇗𑇘𑇙",_type:"numeric"},sind:{_digits:"𑋰𑋱𑋲𑋳𑋴𑋵𑋶𑋷𑋸𑋹",_type:"numeric"},sinh:{_digits:"෦෧෨෩෪෫෬෭෮෯",_type:"numeric"},sora:{_digits:"𑃰𑃱𑃲𑃳𑃴𑃵𑃶𑃷𑃸𑃹",_type:"numeric"},sund:{_digits:"᮰᮱᮲᮳᮴᮵᮶᮷᮸᮹",_type:"numeric"},takr:{_digits:"𑛀𑛁𑛂𑛃𑛄𑛅𑛆𑛇𑛈𑛉",_type:"numeric"},talu:{_digits:"᧐᧑᧒᧓᧔᧕᧖᧗᧘᧙",_type:"numeric"},taml:{_rules:"tamil",_type:"algorithmic"},tamldec:{_digits:"௦௧௨௩௪௫௬௭௮௯",_type:"numeric"},telu:{_digits:"౦౧౨౩౪౫౬౭౮౯",_type:"numeric"},thai:{_digits:"๐๑๒๓๔๕๖๗๘๙",_type:"numeric"},tibt:{_digits:"༠༡༢༣༤༥༦༧༨༩",_type:"numeric"},tirh:{_digits:"𑓐𑓑𑓒𑓓𑓔𑓕𑓖𑓗𑓘𑓙",_type:"numeric"},vaii:{_digits:"꘠꘡꘢꘣꘤꘥꘦꘧꘨꘩",_type:"numeric"},wara:{_digits:"𑣠𑣡𑣢𑣣𑣤𑣥𑣦𑣧𑣨𑣩",_type:"numeric"},wcho:{_digits:"𞋰𞋱𞋲𞋳𞋴𞋵𞋶𞋷𞋸𞋹",_type:"numeric"}}}},{main:{pl:{identity:{version:{_cldrVersion:"36"},language:"pl"},numbers:{currencies:{ADP:{displayName:"peseta andorska","displayName-count-one":"peseta andorska","displayName-count-few":"pesety andorskie","displayName-count-many":"peset andorskich","displayName-count-other":"peseta andorska",symbol:"ADP"},AED:{displayName:"dirham ZEA","displayName-count-one":"dirham ZEA","displayName-count-few":"dirhamy ZEA","displayName-count-many":"dirhamów ZEA","displayName-count-other":"dirhama ZEA",symbol:"AED"},AFA:{displayName:"afgani (1927–2002)","displayName-count-one":"afgani (1927–2002)","displayName-count-few":"afgani (1927–2002)","displayName-count-many":"afgani (1927–2002)","displayName-count-other":"afgani (1927–2002)",symbol:"AFA"},AFN:{displayName:"afgani afgańskie","displayName-count-one":"afgani afgańskie","displayName-count-few":"afgani afgańskie","displayName-count-many":"afgani afgańskich","displayName-count-other":"afgani afgańskiego",symbol:"AFN"},ALK:{displayName:"ALK",symbol:"ALK"},ALL:{displayName:"lek albański","displayName-count-one":"lek albański","displayName-count-few":"leki albańskie","displayName-count-many":"leków albańskich","displayName-count-other":"leka albańskiego",symbol:"ALL"},AMD:{displayName:"dram armeński","displayName-count-one":"dram ormiański","displayName-count-few":"dramy armeńskie","displayName-count-many":"dramów armeńskich","displayName-count-other":"dramy ormiańskiej",symbol:"AMD"},ANG:{displayName:"gulden antylski","displayName-count-one":"gulden antylski","displayName-count-few":"guldeny antylskie","displayName-count-many":"guldenów antylskich","displayName-count-other":"guldena antylskiego",symbol:"ANG"},AOA:{displayName:"kwanza angolska","displayName-count-one":"kwanza angolska","displayName-count-few":"kwanzy angolskie","displayName-count-many":"kwanz angolskich","displayName-count-other":"kwanzy angolskiej",symbol:"AOA","symbol-alt-narrow":"Kz"},AOK:{displayName:"kwanza angolańska (1977–1990)","displayName-count-one":"kwanza angolańska (1977–1990)","displayName-count-few":"kwanzy angolańskie (1977–1990)","displayName-count-many":"kwanz angolańskich (1977–1990)","displayName-count-other":"kwanza angolańska (1977–1990)",symbol:"AOK"},AON:{displayName:"nowa kwanza angolańska (1990–2000)","displayName-count-one":"nowa kwanza angolańska (1990–2000)","displayName-count-few":"nowe kwanzy angolańskie (1990–2000)","displayName-count-many":"nowych kwanz angolańskich (1990–2000)","displayName-count-other":"nowa kwanza angolańska (1990–2000)",symbol:"AON"},AOR:{displayName:"kwanza angolańska Reajustado (1995–1999)","displayName-count-one":"kwanza angolańska Reajustado (1995–1999)","displayName-count-few":"kwanzy angolańskie Reajustado (1995–1999)","displayName-count-many":"kwanz angolańskich Reajustado (1995–1999)","displayName-count-other":"kwanza angolańska Reajustado (1995–1999)",symbol:"AOR"},ARA:{displayName:"austral argentyński",symbol:"ARA"},ARL:{displayName:"ARL",symbol:"ARL"},ARM:{displayName:"ARM",symbol:"ARM"},ARP:{displayName:"peso argentyńskie (1983–1985)",symbol:"ARP"},ARS:{displayName:"peso argentyńskie","displayName-count-one":"peso argentyńskie","displayName-count-few":"pesos argentyńskie","displayName-count-many":"pesos argentyńskich","displayName-count-other":"peso argentyńskiego",symbol:"ARS","symbol-alt-narrow":"$"},ATS:{displayName:"szyling austriacki",symbol:"ATS"},AUD:{displayName:"dolar australijski","displayName-count-one":"dolar australijski","displayName-count-few":"dolary australijskie","displayName-count-many":"dolarów australijskich","displayName-count-other":"dolara australijskiego",symbol:"AUD","symbol-alt-narrow":"$"},AWG:{displayName:"florin arubański","displayName-count-one":"florin arubański","displayName-count-few":"floriny arubańskie","displayName-count-many":"florinów arubańskich","displayName-count-other":"florina arubańskiego",symbol:"AWG"},AZM:{displayName:"manat azerbejdżański","displayName-count-one":"manat azerbejdżański","displayName-count-few":"manat azerbejdżański","displayName-count-many":"manat azerbejdżański","displayName-count-other":"manat azerbejdżański",symbol:"AZM"},AZN:{displayName:"manat azerski","displayName-count-one":"manat azerski","displayName-count-few":"manaty azerskie","displayName-count-many":"manatów azerskich","displayName-count-other":"manata azerskiego",symbol:"AZN"},BAD:{displayName:"dinar Bośni i Hercegowiny",symbol:"BAD"},BAM:{displayName:"marka zamienna Bośni i Hercegowiny","displayName-count-one":"marka zamienna Bośni i Hercegowiny","displayName-count-few":"marki zamienne Bośni i Hercegowiny","displayName-count-many":"marek zamiennych Bośni i Hercegowiny","displayName-count-other":"marki zamiennej Bośni i Hercegowiny",symbol:"BAM","symbol-alt-narrow":"KM"},BAN:{displayName:"BAN",symbol:"BAN"},BBD:{displayName:"dolar Barbadosu","displayName-count-one":"dolar Barbadosu","displayName-count-few":"dolary Barbadosu","displayName-count-many":"dolarów Barbadosu","displayName-count-other":"dolara Barbadosu",symbol:"BBD","symbol-alt-narrow":"$"},BDT:{displayName:"taka bengalska","displayName-count-one":"taka bengalska","displayName-count-few":"taka bengalskie","displayName-count-many":"taka bengalskich","displayName-count-other":"taka bengalskiej",symbol:"BDT","symbol-alt-narrow":"৳"},BEC:{displayName:"frank belgijski (zamienny)","displayName-count-one":"frank belgijski (wymienialny)","displayName-count-few":"franki belgijskie (wymienialne)","displayName-count-many":"franków belgijskich (wymienialnych)","displayName-count-other":"frank belgijski (zamienny)",symbol:"BEC"},BEF:{displayName:"frank belgijski",symbol:"BEF"},BEL:{displayName:"frank belgijski (finansowy)",symbol:"BEL"},BGL:{displayName:"lew bułgarski wymienny","displayName-count-one":"lew bułgarski wymienny","displayName-count-few":"lewy bułgarskie wymienne","displayName-count-many":"lewów bułgarskich wymiennych","displayName-count-other":"lewa bułgarskiego wymiennego",symbol:"BGL"},BGM:{displayName:"lew bułgarski socjalistyczny","displayName-count-one":"lew bułgarski socjalistyczny","displayName-count-few":"lewy bułgarskie socjalistyczne","displayName-count-many":"lewów bułgarskich socjalistycznych","displayName-count-other":"lewa bułgarskiego socjalistycznego",symbol:"BGM"},BGN:{displayName:"lew bułgarski","displayName-count-one":"lew bułgarski","displayName-count-few":"lewy bułgarskie","displayName-count-many":"lewów bułgarskich","displayName-count-other":"lewa bułgarskiego",symbol:"BGN"},BGO:{displayName:"lew bułgarski (1879–1952)","displayName-count-one":"lew bułgarski (1879–1952)","displayName-count-few":"lewy bułgarskie (1879–1952)","displayName-count-many":"lewów bułgarskich (1879–1952)","displayName-count-other":"lewa bułgarskiego (1879–1952)",symbol:"BGO"},BHD:{displayName:"dinar bahrański","displayName-count-one":"dinar bahrański","displayName-count-few":"dinary bahrańskie","displayName-count-many":"dinarów bahrańskich","displayName-count-other":"dinara bahrańskiego",symbol:"BHD"},BIF:{displayName:"frank burundyjski","displayName-count-one":"frank burundyjski","displayName-count-few":"franki burundyjskie","displayName-count-many":"franków burundyjskich","displayName-count-other":"franka burundyjskiego",symbol:"BIF"},BMD:{displayName:"dolar bermudzki","displayName-count-one":"dolar bermudzki","displayName-count-few":"dolary bermudzkie","displayName-count-many":"dolarów bermudzkich","displayName-count-other":"dolara bermudzkiego",symbol:"BMD","symbol-alt-narrow":"$"},BND:{displayName:"dolar brunejski","displayName-count-one":"dolar brunejski","displayName-count-few":"dolary brunejskie","displayName-count-many":"dolarów brunejskich","displayName-count-other":"dolara brunejskiego",symbol:"BND","symbol-alt-narrow":"$"},BOB:{displayName:"boliviano boliwijskie","displayName-count-one":"boliviano boliwijskie","displayName-count-few":"boliviano boliwijskie","displayName-count-many":"boliviano boliwijskich","displayName-count-other":"boliviano boliwijskiego",symbol:"BOB","symbol-alt-narrow":"Bs"},BOL:{displayName:"BOL",symbol:"BOL"},BOP:{displayName:"peso boliwijskie",symbol:"BOP"},BOV:{displayName:"mvdol boliwijski",symbol:"BOV"},BRB:{displayName:"cruzeiro novo brazylijskie (1967–1986)",symbol:"BRB"},BRC:{displayName:"cruzado brazylijskie",symbol:"BRC"},BRE:{displayName:"cruzeiro brazylijskie (1990–1993)",symbol:"BRE"},BRL:{displayName:"real brazylijski","displayName-count-one":"real brazylijski","displayName-count-few":"reale brazylijskie","displayName-count-many":"reali brazylijskich","displayName-count-other":"reala brazylijskiego",symbol:"R$","symbol-alt-narrow":"R$"},BRN:{displayName:"nowe cruzado brazylijskie",symbol:"BRN"},BRR:{displayName:"cruzeiro brazylijskie",symbol:"BRR"},BRZ:{displayName:"BRZ",symbol:"BRZ"},BSD:{displayName:"dolar bahamski","displayName-count-one":"dolar bahamski","displayName-count-few":"dolary bahamskie","displayName-count-many":"dolarów bahamskich","displayName-count-other":"dolara bahamskiego",symbol:"BSD","symbol-alt-narrow":"$"},BTN:{displayName:"ngultrum bhutański","displayName-count-one":"ngultrum bhutański","displayName-count-few":"ngultrum bhutańskie","displayName-count-many":"ngultrum bhutańskich","displayName-count-other":"ngultrum bhutańskiego",symbol:"BTN"},BUK:{displayName:"kyat birmański",symbol:"BUK"},BWP:{displayName:"pula botswańska","displayName-count-one":"pula botswańska","displayName-count-few":"pule botswańskie","displayName-count-many":"pul botswańskich","displayName-count-other":"puli botswańskiej",symbol:"BWP","symbol-alt-narrow":"P"},BYB:{displayName:"rubel białoruski (1994–1999)",symbol:"BYB"},BYN:{displayName:"rubel białoruski","displayName-count-one":"rubel białoruski","displayName-count-few":"ruble białoruskie","displayName-count-many":"rubli białoruskich","displayName-count-other":"rubla białoruskiego",symbol:"BYN","symbol-alt-narrow":"р."},BYR:{displayName:"rubel białoruski (2000–2016)","displayName-count-one":"rubel białoruski (2000–2016)","displayName-count-few":"ruble białoruskie (2000–2016)","displayName-count-many":"rubli białoruskich (2000–2016)","displayName-count-other":"rubla białoruskiego (2000–2016)",symbol:"BYR"},BZD:{displayName:"dolar belizeński","displayName-count-one":"dolar belizeński","displayName-count-few":"dolary belizeńskie","displayName-count-many":"dolarów belizeńskich","displayName-count-other":"dolara belizeńskiego",symbol:"BZD","symbol-alt-narrow":"$"},CAD:{displayName:"dolar kanadyjski","displayName-count-one":"dolar kanadyjski","displayName-count-few":"dolary kanadyjskie","displayName-count-many":"dolarów kanadyjskich","displayName-count-other":"dolara kanadyjskiego",symbol:"CAD","symbol-alt-narrow":"$"},CDF:{displayName:"frank kongijski","displayName-count-one":"frank kongijski","displayName-count-few":"franki kongijskie","displayName-count-many":"franków kongijskich","displayName-count-other":"franka kongijskiego",symbol:"CDF"},CHE:{displayName:"CHE",symbol:"CHE"},CHF:{displayName:"frank szwajcarski","displayName-count-one":"frank szwajcarski","displayName-count-few":"franki szwajcarskie","displayName-count-many":"franków szwajcarskich","displayName-count-other":"franka szwajcarskiego",symbol:"CHF"},CHW:{displayName:"CHW",symbol:"CHW"},CLE:{displayName:"CLE",symbol:"CLE"},CLF:{displayName:"CLF",symbol:"CLF"},CLP:{displayName:"peso chilijskie","displayName-count-one":"peso chilijskie","displayName-count-few":"pesos chilijskie","displayName-count-many":"pesos chilijskich","displayName-count-other":"peso chilijskiego",symbol:"CLP","symbol-alt-narrow":"$"},CNH:{displayName:"juan chiński (rynek zewnętrzny)","displayName-count-one":"juan chiński (rynek zewnętrzny)","displayName-count-few":"juany chińskie (rynek zewnętrzny)","displayName-count-many":"juanów chińskich (rynek zewnętrzny)","displayName-count-other":"juana chińskiego (rynek zewnętrzny)",symbol:"CNH"},CNX:{displayName:"CNX",symbol:"CNX"},CNY:{displayName:"juan chiński","displayName-count-one":"juan chiński","displayName-count-few":"juany chińskie","displayName-count-many":"juanów chińskich","displayName-count-other":"juana chińskiego",symbol:"CNY","symbol-alt-narrow":"¥"},COP:{displayName:"peso kolumbijskie","displayName-count-one":"peso kolumbijskie","displayName-count-few":"pesos kolumbijskie","displayName-count-many":"pesos kolumbijskich","displayName-count-other":"peso kolumbijskiego",symbol:"COP","symbol-alt-narrow":"$"},COU:{displayName:"COU",symbol:"COU"},CRC:{displayName:"colon kostarykański","displayName-count-one":"colon kostarykański","displayName-count-few":"colony kostarykańskie","displayName-count-many":"colonów kostarykańskich","displayName-count-other":"colona kostarykańskiego",symbol:"CRC","symbol-alt-narrow":"₡"},CSD:{displayName:"stary dinar serbski",symbol:"CSD"},CSK:{displayName:"korona czechosłowacka","displayName-count-one":"korona czechosłowacka","displayName-count-few":"korony czechosłowackie","displayName-count-many":"koron czechosłowackich","displayName-count-other":"korona czechosłowacka",symbol:"CSK"},CUC:{displayName:"peso kubańskie wymienialne","displayName-count-one":"peso kubańskie wymienialne","displayName-count-few":"pesos kubańskie wymienialne","displayName-count-many":"pesos kubańskich wymienialnych","displayName-count-other":"peso kubańskiego wymienialnego",symbol:"CUC","symbol-alt-narrow":"$"},CUP:{displayName:"peso kubańskie","displayName-count-one":"peso kubańskie","displayName-count-few":"pesos kubańskie","displayName-count-many":"pesos kubańskich","displayName-count-other":"peso kubańskiego",symbol:"CUP","symbol-alt-narrow":"$"},CVE:{displayName:"escudo zielonoprzylądkowe","displayName-count-one":"escudo zielonoprzylądkowe","displayName-count-few":"escudo zielonoprzylądkowe","displayName-count-many":"escudo zielonoprzylądkowych","displayName-count-other":"escudo zielonoprzylądkowego",symbol:"CVE"},CYP:{displayName:"funt cypryjski",symbol:"CYP"},CZK:{displayName:"korona czeska","displayName-count-one":"korona czeska","displayName-count-few":"korony czeskie","displayName-count-many":"koron czeskich","displayName-count-other":"korony czeskiej",symbol:"CZK","symbol-alt-narrow":"Kč"},DDM:{displayName:"wschodnia marka wschodnioniemiecka",symbol:"DDM"},DEM:{displayName:"marka niemiecka","displayName-count-one":"marka niemiecka","displayName-count-few":"marki niemieckie","displayName-count-many":"marek niemieckich","displayName-count-other":"marka niemiecka",symbol:"DEM"},DJF:{displayName:"frank dżibutyjski","displayName-count-one":"frank dżibutyjski","displayName-count-few":"franki dżibutyjskie","displayName-count-many":"franków dżibutyjskich","displayName-count-other":"franka dżibutyjskiego",symbol:"DJF"},DKK:{displayName:"korona duńska","displayName-count-one":"korona duńska","displayName-count-few":"korony duńskie","displayName-count-many":"koron duńskich","displayName-count-other":"korony duńskiej",symbol:"DKK","symbol-alt-narrow":"kr"},DOP:{displayName:"peso dominikańskie","displayName-count-one":"peso dominikańskie","displayName-count-few":"pesos dominikańskie","displayName-count-many":"pesos dominikańskich","displayName-count-other":"peso dominikańskiego",symbol:"DOP","symbol-alt-narrow":"$"},DZD:{displayName:"dinar algierski","displayName-count-one":"dinar algierski","displayName-count-few":"dinary algierskie","displayName-count-many":"dinarów algierskich","displayName-count-other":"dinara algierskiego",symbol:"DZD"},ECS:{displayName:"sucre ekwadorski",symbol:"ECS"},ECV:{displayName:"ECV",symbol:"ECV"},EEK:{displayName:"korona estońska","displayName-count-one":"korona estońska","displayName-count-few":"korony estońskie","displayName-count-many":"koron estońskich","displayName-count-other":"korona estońska",symbol:"EEK"},EGP:{displayName:"funt egipski","displayName-count-one":"funt egipski","displayName-count-few":"funty egipskie","displayName-count-many":"funtów egipskich","displayName-count-other":"funta egipskiego",symbol:"EGP","symbol-alt-narrow":"E£"},ERN:{displayName:"nakfa erytrejska","displayName-count-one":"nakfa erytrejska","displayName-count-few":"nakfy erytrejskie","displayName-count-many":"nakf erytrejskich","displayName-count-other":"nakfy erytrejskiej",symbol:"ERN"},ESA:{displayName:"peseta hiszpańska (Konto A)",symbol:"ESA"},ESB:{displayName:"peseta hiszpańska (konto wymienne)","displayName-count-one":"peseta hiszpańska (konto wymienialne)","displayName-count-few":"pesety hiszpańskie (konto wymienialne)","displayName-count-many":"peset hiszpańskich (konto wymienialne)","displayName-count-other":"peseta hiszpańska (konto wymienne)",symbol:"ESB"},ESP:{displayName:"peseta hiszpańska",symbol:"ESP","symbol-alt-narrow":"₧"},ETB:{displayName:"birr etiopski","displayName-count-one":"birr etiopski","displayName-count-few":"birry etiopskie","displayName-count-many":"birrów etiopskich","displayName-count-other":"birra etiopskiego",symbol:"ETB"},EUR:{displayName:"euro","displayName-count-one":"euro","displayName-count-few":"euro","displayName-count-many":"euro","displayName-count-other":"euro",symbol:"€","symbol-alt-narrow":"€"},FIM:{displayName:"marka fińska",symbol:"FIM"},FJD:{displayName:"dolar fidżyjski","displayName-count-one":"dolar fidżyjski","displayName-count-few":"dolary fidżyjskie","displayName-count-many":"dolarów fidżyjskich","displayName-count-other":"dolara fidżyjskiego",symbol:"FJD","symbol-alt-narrow":"$"},FKP:{displayName:"funt falklandzki","displayName-count-one":"funt falklandzki","displayName-count-few":"funty falklandzkie","displayName-count-many":"funtów falklandzkich","displayName-count-other":"funta falklandzkiego",symbol:"FKP","symbol-alt-narrow":"£"},FRF:{displayName:"frank francuski","displayName-count-one":"frank francuski","displayName-count-few":"franki francuskie","displayName-count-many":"franków francuskich","displayName-count-other":"frank francuski",symbol:"FRF"},GBP:{displayName:"funt szterling","displayName-count-one":"funt szterling","displayName-count-few":"funty szterlingi","displayName-count-many":"funtów szterlingów","displayName-count-other":"funta szterlinga",symbol:"GBP","symbol-alt-narrow":"£"},GEK:{displayName:"kupon gruziński larit",symbol:"GEK"},GEL:{displayName:"lari gruzińskie","displayName-count-one":"lari gruzińskie","displayName-count-few":"lari gruzińskie","displayName-count-many":"lari gruzińskich","displayName-count-other":"lari gruzińskiego",symbol:"GEL","symbol-alt-narrow":"₾","symbol-alt-variant":"₾"},GHC:{displayName:"cedi ghańskie (1979–2007)",symbol:"GHC"},GHS:{displayName:"cedi ghańskie","displayName-count-one":"cedi ghańskie","displayName-count-few":"cedi ghańskie","displayName-count-many":"cedi ghańskich","displayName-count-other":"cedi ghańskiego",symbol:"GHS"},GIP:{displayName:"funt gibraltarski","displayName-count-one":"funt gibraltarski","displayName-count-few":"funty gibraltarskie","displayName-count-many":"funtów gibraltarskich","displayName-count-other":"funta gibraltarskiego",symbol:"GIP","symbol-alt-narrow":"£"},GMD:{displayName:"dalasi gambijskie","displayName-count-one":"dalasi gambijskie","displayName-count-few":"dalasi gambijskie","displayName-count-many":"dalasi gambijskich","displayName-count-other":"dalasi gambijskiego",symbol:"GMD"},GNF:{displayName:"frank gwinejski","displayName-count-one":"frank gwinejski","displayName-count-few":"franki gwinejskie","displayName-count-many":"franków gwinejskich","displayName-count-other":"franka gwinejskiego",symbol:"GNF","symbol-alt-narrow":"FG"},GNS:{displayName:"syli gwinejskie",symbol:"GNS"},GQE:{displayName:"ekwele gwinejskie Gwinei Równikowej",symbol:"GQE"},GRD:{displayName:"drachma grecka",symbol:"GRD"},GTQ:{displayName:"quetzal gwatemalski","displayName-count-one":"quetzal gwatemalski","displayName-count-few":"quetzale gwatemalskie","displayName-count-many":"quetzali gwatemalskich","displayName-count-other":"quetzala gwatemalskiego",symbol:"GTQ","symbol-alt-narrow":"Q"},GWE:{displayName:"escudo Gwinea Portugalska",symbol:"GWE"},GWP:{displayName:"peso Guinea-Bissau",symbol:"GWP"},GYD:{displayName:"dolar gujański","displayName-count-one":"dolar gujański","displayName-count-few":"dolary gujańskie","displayName-count-many":"dolarów gujańskich","displayName-count-other":"dolara gujańskiego",symbol:"GYD","symbol-alt-narrow":"$"},HKD:{displayName:"dolar hongkoński","displayName-count-one":"dolar hongkoński","displayName-count-few":"dolary hongkońskie","displayName-count-many":"dolarów hongkońskich","displayName-count-other":"dolara hongkońskiego",symbol:"HKD","symbol-alt-narrow":"$"},HNL:{displayName:"lempira honduraska","displayName-count-one":"lempira honduraska","displayName-count-few":"lempiry honduraskie","displayName-count-many":"lempir honduraskich","displayName-count-other":"lempiry honduraskiej",symbol:"HNL","symbol-alt-narrow":"L"},HRD:{displayName:"dinar chorwacki",symbol:"HRD"},HRK:{displayName:"kuna chorwacka","displayName-count-one":"kuna chorwacka","displayName-count-few":"kuny chorwackie","displayName-count-many":"kun chorwackich","displayName-count-other":"kuny chorwackiej",symbol:"HRK","symbol-alt-narrow":"kn"},HTG:{displayName:"gourde haitański","displayName-count-one":"gourde haitański","displayName-count-few":"gourde haitańskie","displayName-count-many":"gourde haitańskich","displayName-count-other":"gourde haitańskiego",symbol:"HTG"},HUF:{displayName:"forint węgierski","displayName-count-one":"forint węgierski","displayName-count-few":"forinty węgierskie","displayName-count-many":"forintów węgierskich","displayName-count-other":"forinta węgierskiego",symbol:"HUF","symbol-alt-narrow":"Ft"},IDR:{displayName:"rupia indonezyjska","displayName-count-one":"rupia indonezyjska","displayName-count-few":"rupie indonezyjskie","displayName-count-many":"rupii indonezyjskich","displayName-count-other":"rupii indonezyjskiej",symbol:"IDR","symbol-alt-narrow":"Rp"},IEP:{displayName:"funt irlandzki",symbol:"IEP"},ILP:{displayName:"funt izraelski",symbol:"ILP"},ILR:{displayName:"ILR",symbol:"ILR"},ILS:{displayName:"nowy szekel izraelski","displayName-count-one":"nowy szekel izraelski","displayName-count-few":"nowe szekle izraelskie","displayName-count-many":"nowych szekli izraelskich","displayName-count-other":"nowego szekla izraelskiego",symbol:"ILS","symbol-alt-narrow":"₪"},INR:{displayName:"rupia indyjska","displayName-count-one":"rupia indyjska","displayName-count-few":"rupie indyjskie","displayName-count-many":"rupii indyjskich","displayName-count-other":"rupii indyjskiej",symbol:"INR","symbol-alt-narrow":"₹"},IQD:{displayName:"dinar iracki","displayName-count-one":"dinar iracki","displayName-count-few":"dinary irackie","displayName-count-many":"dinarów irackich","displayName-count-other":"dinara irackiego",symbol:"IQD"},IRR:{displayName:"rial irański","displayName-count-one":"rial irański","displayName-count-few":"riale irańskie","displayName-count-many":"riali irańskich","displayName-count-other":"riala irańskiego",symbol:"IRR"},ISJ:{displayName:"ISJ",symbol:"ISJ"},ISK:{displayName:"korona islandzka","displayName-count-one":"korona islandzka","displayName-count-few":"korony islandzkie","displayName-count-many":"koron islandzkich","displayName-count-other":"korony islandzkiej",symbol:"ISK","symbol-alt-narrow":"kr"},ITL:{displayName:"lir włoski",symbol:"ITL"},JMD:{displayName:"dolar jamajski","displayName-count-one":"dolar jamajski","displayName-count-few":"dolary jamajskie","displayName-count-many":"dolarów jamajskich","displayName-count-other":"dolara jamajskiego",symbol:"JMD","symbol-alt-narrow":"$"},JOD:{displayName:"dinar jordański","displayName-count-one":"dinar jordański","displayName-count-few":"dinary jordańskie","displayName-count-many":"dinarów jordańskich","displayName-count-other":"dinara jordańskiego",symbol:"JOD"},JPY:{displayName:"jen japoński","displayName-count-one":"jen japoński","displayName-count-few":"jeny japońskie","displayName-count-many":"jenów japońskich","displayName-count-other":"jena japońskiego",symbol:"JPY","symbol-alt-narrow":"¥"},KES:{displayName:"szyling kenijski","displayName-count-one":"szyling kenijski","displayName-count-few":"szylingi kenijskie","displayName-count-many":"szylingów kenijskich","displayName-count-other":"szylinga kenijskiego",symbol:"KES"},KGS:{displayName:"som kirgiski","displayName-count-one":"som kirgiski","displayName-count-few":"somy kirgiskie","displayName-count-many":"somów kirgiskich","displayName-count-other":"soma kirgiskiego",symbol:"KGS"},KHR:{displayName:"riel kambodżański","displayName-count-one":"riel kambodżański","displayName-count-few":"riele kambodżańskie","displayName-count-many":"rieli kambodżańskich","displayName-count-other":"riela kambodżańskiego",symbol:"KHR","symbol-alt-narrow":"៛"},KMF:{displayName:"frank komoryjski","displayName-count-one":"frank komoryjski","displayName-count-few":"franki komoryjskie","displayName-count-many":"franków komoryjskich","displayName-count-other":"franka komoryjskiego",symbol:"KMF","symbol-alt-narrow":"CF"},KPW:{displayName:"won północnokoreański","displayName-count-one":"won północnokoreański","displayName-count-few":"wony północnokoreańskie","displayName-count-many":"wonów północnokoreańskich","displayName-count-other":"wona północnokoreańskiego",symbol:"KPW","symbol-alt-narrow":"₩"},KRH:{displayName:"KRH",symbol:"KRH"},KRO:{displayName:"KRO",symbol:"KRO"},KRW:{displayName:"won południowokoreański","displayName-count-one":"won południowokoreański","displayName-count-few":"wony południowokoreańskie","displayName-count-many":"wonów południowokoreańskich","displayName-count-other":"wona południowokoreańskiego",symbol:"KRW","symbol-alt-narrow":"₩"},KWD:{displayName:"dinar kuwejcki","displayName-count-one":"dinar kuwejcki","displayName-count-few":"dinary kuwejckie","displayName-count-many":"dinarów kuwejckich","displayName-count-other":"dinara kuwejckiego",symbol:"KWD"},KYD:{displayName:"dolar kajmański","displayName-count-one":"dolar kajmański","displayName-count-few":"dolary kajmańskie","displayName-count-many":"dolarów kajmańskich","displayName-count-other":"dolara kajmańskiego",symbol:"KYD","symbol-alt-narrow":"$"},KZT:{displayName:"tenge kazachskie","displayName-count-one":"tenge kazachskie","displayName-count-few":"tenge kazachskie","displayName-count-many":"tenge kazachskich","displayName-count-other":"tenge kazachskiego",symbol:"KZT","symbol-alt-narrow":"₸"},LAK:{displayName:"kip laotański","displayName-count-one":"kip laotański","displayName-count-few":"kipy laotańskie","displayName-count-many":"kipów laotańskich","displayName-count-other":"kipa laotańskiego",symbol:"LAK","symbol-alt-narrow":"₭"},LBP:{displayName:"funt libański","displayName-count-one":"funt libański","displayName-count-few":"funty libańskie","displayName-count-many":"funtów libańskich","displayName-count-other":"funta libańskiego",symbol:"LBP","symbol-alt-narrow":"L£"},LKR:{displayName:"rupia lankijska","displayName-count-one":"rupia lankijska","displayName-count-few":"rupie lankijskie","displayName-count-many":"rupii lankijskich","displayName-count-other":"rupii lankijskiej",symbol:"LKR","symbol-alt-narrow":"Rs"},LRD:{displayName:"dolar liberyjski","displayName-count-one":"dolar liberyjski","displayName-count-few":"dolary liberyjskie","displayName-count-many":"dolarów liberyjskich","displayName-count-other":"dolara liberyjskiego",symbol:"LRD","symbol-alt-narrow":"$"},LSL:{displayName:"loti Lesoto",symbol:"LSL"},LTL:{displayName:"lit litewski","displayName-count-one":"lit litewski","displayName-count-few":"lity litewskie","displayName-count-many":"litów litewskich","displayName-count-other":"lita litewskiego",symbol:"LTL","symbol-alt-narrow":"Lt"},LTT:{displayName:"talon litewski",symbol:"LTT"},LUC:{displayName:"LUC",symbol:"LUC"},LUF:{displayName:"frank luksemburski",symbol:"LUF"},LUL:{displayName:"LUL",symbol:"LUL"},LVL:{displayName:"łat łotewski","displayName-count-one":"łat łotewski","displayName-count-few":"łaty łotewskie","displayName-count-many":"łatów łotewskich","displayName-count-other":"łata łotewskiego",symbol:"LVL","symbol-alt-narrow":"Ls"},LVR:{displayName:"rubel łotewski",symbol:"LVR"},LYD:{displayName:"dinar libijski","displayName-count-one":"dinar libijski","displayName-count-few":"dinary libijskie","displayName-count-many":"dinarów libijskich","displayName-count-other":"dinara libijskiego",symbol:"LYD"},MAD:{displayName:"dirham marokański","displayName-count-one":"dirham marokański","displayName-count-few":"dirhamy marokańskie","displayName-count-many":"dirhamów marokańskich","displayName-count-other":"dirhama marokańskiego",symbol:"MAD"},MAF:{displayName:"frank marokański","displayName-count-one":"frank marokański","displayName-count-few":"franki marokańskie","displayName-count-many":"franków marokańskich","displayName-count-other":"frank marokański",symbol:"MAF"},MCF:{displayName:"MCF",symbol:"MCF"},MDC:{displayName:"MDC",symbol:"MDC"},MDL:{displayName:"lej mołdawski","displayName-count-one":"lej mołdawski","displayName-count-few":"leje mołdawskie","displayName-count-many":"lejów mołdawskich","displayName-count-other":"leja mołdawskiego",symbol:"MDL"},MGA:{displayName:"ariary malgaski","displayName-count-one":"ariary malgaski","displayName-count-few":"ariary malgaskie","displayName-count-many":"ariary malgaskich","displayName-count-other":"ariary malgaskiego",symbol:"MGA","symbol-alt-narrow":"Ar"},MGF:{displayName:"frank malgaski",symbol:"MGF"},MKD:{displayName:"denar macedoński","displayName-count-one":"denar macedoński","displayName-count-few":"denary macedońskie","displayName-count-many":"denarów macedońskich","displayName-count-other":"denara macedońskiego",symbol:"MKD"},MKN:{displayName:"MKN",symbol:"MKN"},MLF:{displayName:"frank malijski",symbol:"MLF"},MMK:{displayName:"kiat birmański","displayName-count-one":"kiat birmański","displayName-count-few":"kiaty birmańskie","displayName-count-many":"kiatów birmańskich","displayName-count-other":"kiata birmańskiego",symbol:"MMK","symbol-alt-narrow":"K"},MNT:{displayName:"tugrik mongolski","displayName-count-one":"tugrik mongolski","displayName-count-few":"tugriki mongolskie","displayName-count-many":"tugrików mongolskich","displayName-count-other":"tugrika mongolskiego",symbol:"MNT","symbol-alt-narrow":"₮"},MOP:{displayName:"pataca Makau","displayName-count-one":"pataca Makau","displayName-count-few":"pataca Makau","displayName-count-many":"pataca Makau","displayName-count-other":"pataca Makau",symbol:"MOP"},MRO:{displayName:"ouguiya mauretańska (1973–2017)","displayName-count-one":"ouguiya mauretańska (1973–2017)","displayName-count-few":"ouguiya mauretańskie (1973–2017)","displayName-count-many":"ouguiya mauretańskich (1973–2017)","displayName-count-other":"ouguiya mauretańskiej (1973–2017)",symbol:"MRO"},MRU:{displayName:"ugija mauretańska","displayName-count-one":"ugija mauretańska","displayName-count-few":"ugija mauretańskie","displayName-count-many":"ugija mauretańskich","displayName-count-other":"ugija mauretańskiej",symbol:"MRU"},MTL:{displayName:"lira maltańska",symbol:"MTL"},MTP:{displayName:"funt maltański",symbol:"MTP"},MUR:{displayName:"rupia maurytyjska","displayName-count-one":"rupia maurytyjska","displayName-count-few":"rupie maurytyjskie","displayName-count-many":"rupii maurytyjskich","displayName-count-other":"rupii maurytyjskiej",symbol:"MUR","symbol-alt-narrow":"Rs"},MVP:{displayName:"MVP",symbol:"MVP"},MVR:{displayName:"rupia malediwska","displayName-count-one":"rupia malediwska","displayName-count-few":"rupie malediwskie","displayName-count-many":"rupii malediwskich","displayName-count-other":"rupii malediwskiej",symbol:"MVR"},MWK:{displayName:"kwacha malawijska","displayName-count-one":"kwacha malawijska","displayName-count-few":"kwacha malawijskie","displayName-count-many":"kwacha malawijskich","displayName-count-other":"kwacha malawijskiej",symbol:"MWK"},MXN:{displayName:"peso meksykańskie","displayName-count-one":"peso meksykańskie","displayName-count-few":"pesos meksykańskie","displayName-count-many":"pesos meksykańskich","displayName-count-other":"peso meksykańskiego",symbol:"MXN","symbol-alt-narrow":"$"},MXP:{displayName:"peso srebrne meksykańskie (1861–1992)",symbol:"MXP"},MXV:{displayName:"MXV",symbol:"MXV"},MYR:{displayName:"ringgit malezyjski","displayName-count-one":"ringgit malezyjski","displayName-count-few":"ringgity malezyjskie","displayName-count-many":"ringgitów malezyjskich","displayName-count-other":"ringgita malezyjskiego",symbol:"MYR","symbol-alt-narrow":"RM"},MZE:{displayName:"escudo mozambickie",symbol:"MZE"},MZM:{displayName:"metical Mozambik",symbol:"MZM"},MZN:{displayName:"metical mozambicki","displayName-count-one":"metical mozambicki","displayName-count-few":"meticale mozambickie","displayName-count-many":"meticali mozambickich","displayName-count-other":"meticala mozambickiego",symbol:"MZN"},NAD:{displayName:"dolar namibijski","displayName-count-one":"dolar namibijski","displayName-count-few":"dolary namibijskie","displayName-count-many":"dolarów namibijskich","displayName-count-other":"dolara namibijskiego",symbol:"NAD","symbol-alt-narrow":"$"},NGN:{displayName:"naira nigeryjska","displayName-count-one":"naira nigeryjska","displayName-count-few":"nairy nigeryjskie","displayName-count-many":"nair nigeryjskich","displayName-count-other":"nairy nigeryjskiej",symbol:"NGN","symbol-alt-narrow":"₦"},NIC:{displayName:"cordoba nikaraguańska (1988–1991)","displayName-count-one":"cordoba nikaraguańska (1988–1991)","displayName-count-few":"cordoby nikaraguańskie (1988–1991)","displayName-count-many":"cordob nikaraguańskich (1988–1991)","displayName-count-other":"cordoby nikaraguańskiej (1988–1991)",symbol:"NIC"},NIO:{displayName:"cordoba nikaraguańska","displayName-count-one":"cordoba nikaraguańska","displayName-count-few":"cordoby nikaraguańskie","displayName-count-many":"cordob nikaraguańskich","displayName-count-other":"cordoby nikaraguańskiej",symbol:"NIO","symbol-alt-narrow":"C$"},NLG:{displayName:"gulden holenderski",symbol:"NLG"},NOK:{displayName:"korona norweska","displayName-count-one":"korona norweska","displayName-count-few":"korony norweskie","displayName-count-many":"koron norweskich","displayName-count-other":"korony norweskiej",symbol:"NOK","symbol-alt-narrow":"kr"},NPR:{displayName:"rupia nepalska","displayName-count-one":"rupia nepalska","displayName-count-few":"rupie nepalskie","displayName-count-many":"rupii nepalskich","displayName-count-other":"rupii nepalskiej",symbol:"NPR","symbol-alt-narrow":"Rs"},NZD:{displayName:"dolar nowozelandzki","displayName-count-one":"dolar nowozelandzki","displayName-count-few":"dolary nowozelandzkie","displayName-count-many":"dolarów nowozelandzkich","displayName-count-other":"dolara nowozelandzkiego",symbol:"NZD","symbol-alt-narrow":"$"},OMR:{displayName:"rial omański","displayName-count-one":"rial omański","displayName-count-few":"riale omańskie","displayName-count-many":"riali omańskich","displayName-count-other":"riala omańskiego",symbol:"OMR"},PAB:{displayName:"balboa panamski","displayName-count-one":"balboa panamski","displayName-count-few":"balboa panamskie","displayName-count-many":"balboa panamskich","displayName-count-other":"balboa panamskiego",symbol:"PAB"},PEI:{displayName:"inti peruwiański",symbol:"PEI"},PEN:{displayName:"sol peruwiański","displayName-count-one":"sol peruwiański","displayName-count-few":"sole peruwiańskie","displayName-count-many":"soli peruwiańskich","displayName-count-other":"sola peruwiańskiego",symbol:"PEN"},PES:{displayName:"sol peruwiański (1863–1965)","displayName-count-one":"sol peruwiański (1863–1965)","displayName-count-few":"sole peruwiańskie (1863–1965)","displayName-count-many":"soli peruwiańskich (1863–1965)","displayName-count-other":"sola peruwiańskiego (1863–1965)",symbol:"PES"},PGK:{displayName:"kina papuańska","displayName-count-one":"kina papuaska","displayName-count-few":"kina papuaskie","displayName-count-many":"kina papuaskich","displayName-count-other":"kina papuaskiej",symbol:"PGK"},PHP:{displayName:"peso filipińskie","displayName-count-one":"peso filipińskie","displayName-count-few":"pesos filipińskie","displayName-count-many":"pesos filipińskich","displayName-count-other":"peso filipińskiego",symbol:"PHP","symbol-alt-narrow":"₱"},PKR:{displayName:"rupia pakistańska","displayName-count-one":"rupia pakistańska","displayName-count-few":"rupie pakistańskie","displayName-count-many":"rupii pakistańskich","displayName-count-other":"rupii pakistańskiej",symbol:"PKR","symbol-alt-narrow":"Rs"},PLN:{displayName:"złoty polski","displayName-count-one":"złoty polski","displayName-count-few":"złote polskie","displayName-count-many":"złotych polskich","displayName-count-other":"złotego polskiego",symbol:"zł","symbol-alt-narrow":"zł"},PLZ:{displayName:"złoty polski (1950–1995)","displayName-count-one":"złoty polski (1950–1995)","displayName-count-few":"złote polskie (1950–1995)","displayName-count-many":"złotych polskich (1950–1995)","displayName-count-other":"złotego polskiego (1950–1995)",symbol:"PLZ"},PTE:{displayName:"escudo portugalskie",symbol:"PTE"},PYG:{displayName:"guarani paragwajskie","displayName-count-one":"guarani paragwajskie","displayName-count-few":"guarani paragwajskie","displayName-count-many":"guarani paragwajskich","displayName-count-other":"guarani paragwajskiego",symbol:"PYG","symbol-alt-narrow":"₲"},QAR:{displayName:"rial katarski","displayName-count-one":"rial katarski","displayName-count-few":"riale katarskie","displayName-count-many":"riali katarskich","displayName-count-other":"riala katarskiego",symbol:"QAR"},RHD:{displayName:"dolar rodezyjski",symbol:"RHD"},ROL:{displayName:"lej rumuński (1952–2006)","displayName-count-one":"lej rumuński (1952–2006)","displayName-count-few":"lei rumuńskie (1952–2006)","displayName-count-many":"lei rumuńskich (1952–2006)","displayName-count-other":"leja rumuńskiego (1952–2006)",symbol:"ROL"},RON:{displayName:"lej rumuński","displayName-count-one":"lej rumuński","displayName-count-few":"leje rumuńskie","displayName-count-many":"lejów rumuńskich","displayName-count-other":"leja rumuńskiego",symbol:"RON","symbol-alt-narrow":"lej"},RSD:{displayName:"dinar serbski","displayName-count-one":"dinar serbski","displayName-count-few":"dinary serbskie","displayName-count-many":"dinarów serbskich","displayName-count-other":"dinara serbskiego",symbol:"RSD"},RUB:{displayName:"rubel rosyjski","displayName-count-one":"rubel rosyjski","displayName-count-few":"ruble rosyjskie","displayName-count-many":"rubli rosyjskich","displayName-count-other":"rubla rosyjskiego",symbol:"RUB","symbol-alt-narrow":"₽"},RUR:{displayName:"rubel rosyjski (1991–1998)","displayName-count-one":"rubel rosyjski (1991–1998)","displayName-count-few":"ruble rosyjskie (1991–1998)","displayName-count-many":"rubli rosyjskich (1991–1998)","displayName-count-other":"rubla rosyjskiego (1991–1998)",symbol:"RUR","symbol-alt-narrow":"р."},RWF:{displayName:"frank ruandyjski","displayName-count-one":"frank ruandyjski","displayName-count-few":"franki ruandyjskie","displayName-count-many":"franków ruandyjskich","displayName-count-other":"franka ruandyjskiego",symbol:"RWF","symbol-alt-narrow":"RF"},SAR:{displayName:"rial saudyjski","displayName-count-one":"rial saudyjski","displayName-count-few":"riale saudyjskie","displayName-count-many":"riali saudyjskich","displayName-count-other":"riala saudyjskiego",symbol:"SAR"},SBD:{displayName:"dolar Wysp Salomona","displayName-count-one":"dolar Wysp Salomona","displayName-count-few":"dolary Wysp Salomona","displayName-count-many":"dolarów Wysp Salomona","displayName-count-other":"dolara Wysp Salomona",symbol:"SBD","symbol-alt-narrow":"$"},SCR:{displayName:"rupia seszelska","displayName-count-one":"rupia seszelska","displayName-count-few":"rupie seszelskie","displayName-count-many":"rupii seszelskich","displayName-count-other":"rupii seszelskiej",symbol:"SCR"},SDD:{displayName:"dinar sudański",symbol:"SDD"},SDG:{displayName:"funt sudański","displayName-count-one":"funt sudański","displayName-count-few":"funty sudańskie","displayName-count-many":"funtów sudańskich","displayName-count-other":"funta sudańskiego",symbol:"SDG"},SDP:{displayName:"funt sudański (1957–1998)","displayName-count-one":"funt sudański (1957–1998)","displayName-count-few":"funty sudańskie (1957–1998)","displayName-count-many":"funtów sudańskich (1957–1998)","displayName-count-other":"funta sudańskiego (1957–1998)",symbol:"SDP"},SEK:{displayName:"korona szwedzka","displayName-count-one":"korona szwedzka","displayName-count-few":"korony szwedzkie","displayName-count-many":"koron szwedzkich","displayName-count-other":"korony szwedzkiej",symbol:"SEK","symbol-alt-narrow":"kr"},SGD:{displayName:"dolar singapurski","displayName-count-one":"dolar singapurski","displayName-count-few":"dolary singapurskie","displayName-count-many":"dolarów singapurskich","displayName-count-other":"dolara singapurskiego",symbol:"SGD","symbol-alt-narrow":"$"},SHP:{displayName:"funt Świętej Heleny","displayName-count-one":"funt Świętej Heleny","displayName-count-few":"funty Świętej Heleny","displayName-count-many":"funtów Świętej Heleny","displayName-count-other":"funta Świętej Heleny",symbol:"SHP","symbol-alt-narrow":"£"},SIT:{displayName:"tolar słoweński","displayName-count-one":"tolar słoweński","displayName-count-few":"tolary słoweńskie","displayName-count-many":"tolarów słoweńskich","displayName-count-other":"tolar słoweński",symbol:"SIT"},SKK:{displayName:"korona słowacka","displayName-count-one":"korona słowacka","displayName-count-few":"korony słowackie","displayName-count-many":"koron słowackich","displayName-count-other":"korona słowacka",symbol:"SKK"},SLL:{displayName:"leone sierraleoński","displayName-count-one":"leone sierraleoński","displayName-count-few":"leone sierraleońskie","displayName-count-many":"leone sierraleońskich","displayName-count-other":"leone sierraleońskiego",symbol:"SLL"},SOS:{displayName:"szyling somalijski","displayName-count-one":"szyling somalijski","displayName-count-few":"szylingi somalijskie","displayName-count-many":"szylingów somalijskich","displayName-count-other":"szylinga somalijskiego",symbol:"SOS"},SRD:{displayName:"dolar surinamski","displayName-count-one":"dolar surinamski","displayName-count-few":"dolary surinamskie","displayName-count-many":"dolarów surinamskich","displayName-count-other":"dolara surinamskiego",symbol:"SRD","symbol-alt-narrow":"$"},SRG:{displayName:"gulden surinamski",symbol:"SRG"},SSP:{displayName:"funt południowosudański","displayName-count-one":"funt południowosudański","displayName-count-few":"funty południowosudańskie","displayName-count-many":"funtów południowosudańskich","displayName-count-other":"funta południowosudańskiego",symbol:"SSP","symbol-alt-narrow":"£"},STD:{displayName:"dobra Wysp Świętego Tomasza i Książęcej (1977–2017)","displayName-count-one":"dobra Wysp Świętego Tomasza i Książęcej (1977–2017)","displayName-count-few":"dobry Wysp Świętego Tomasza i Książęcej (1977–2017)","displayName-count-many":"dobr Wysp Świętego Tomasza i Książęcej (1977–2017)","displayName-count-other":"dobry Wysp Świętego Tomasza i Książęcej (1977–2017)",symbol:"STD"},STN:{displayName:"dobra Wysp Świętego Tomasza i Książęcej","displayName-count-one":"dobra Wysp Świętego Tomasza i Książęcej","displayName-count-few":"dobry Wysp Świętego Tomasza i Książęcej","displayName-count-many":"dobr Wysp Świętego Tomasza i Książęcej","displayName-count-other":"dobry Wysp Świętego Tomasza i Książęcej",symbol:"STN","symbol-alt-narrow":"Db"},SUR:{displayName:"rubel radziecki","displayName-count-one":"rubel radziecki","displayName-count-few":"ruble radzieckie","displayName-count-many":"rubli radzieckich","displayName-count-other":"rubel radziecki",symbol:"SUR"},SVC:{displayName:"colon salwadorski",symbol:"SVC"},SYP:{displayName:"funt syryjski","displayName-count-one":"funt syryjski","displayName-count-few":"funty syryjskie","displayName-count-many":"funtów syryjskich","displayName-count-other":"funta syryjskiego",symbol:"SYP","symbol-alt-narrow":"£"},SZL:{displayName:"lilangeni Suazi","displayName-count-one":"lilangeni Suazi","displayName-count-few":"emalangeni Suazi","displayName-count-many":"emalangeni Suazi","displayName-count-other":"emalangeni Suazi",symbol:"SZL"},THB:{displayName:"baht tajski","displayName-count-one":"baht tajski","displayName-count-few":"bahty tajskie","displayName-count-many":"bahtów tajskich","displayName-count-other":"bahta tajskiego",symbol:"THB","symbol-alt-narrow":"฿"},TJR:{displayName:"rubel tadżycki",symbol:"TJR"},TJS:{displayName:"somoni tadżyckie","displayName-count-one":"somoni tadżyckie","displayName-count-few":"somoni tadżyckie","displayName-count-many":"somoni tadżyckich","displayName-count-other":"somoni tadżyckiego",symbol:"TJS"},TMM:{displayName:"manat turkmeński (1993–2009)","displayName-count-one":"manat turkmeński (1993–2009)","displayName-count-few":"manaty turkmeńskie (1993–2009)","displayName-count-many":"manatów turkmeńskich (1993–2009)","displayName-count-other":"manata turkmeńskiego (1993–2009)",symbol:"TMM"},TMT:{displayName:"manat turkmeński","displayName-count-one":"manat turkmeński","displayName-count-few":"manaty turkmeńskie","displayName-count-many":"manatów turkmeńskich","displayName-count-other":"manata turkmeńskiego",symbol:"TMT"},TND:{displayName:"dinar tunezyjski","displayName-count-one":"dinar tunezyjski","displayName-count-few":"dinary tunezyjskie","displayName-count-many":"dinarów tunezyjskich","displayName-count-other":"dinara tunezyjskiego",symbol:"TND"},TOP:{displayName:"pa’anga tongijska","displayName-count-one":"pa’anga tongijska","displayName-count-few":"pa’anga tongijskie","displayName-count-many":"pa’anga tongijskich","displayName-count-other":"pa’anga tongijskiej",symbol:"TOP","symbol-alt-narrow":"T$"},TPE:{displayName:"escudo timorskie",symbol:"TPE"},TRL:{displayName:"lira turecka (1922–2005)","displayName-count-one":"lira turecka (1922–2005)","displayName-count-few":"liry tureckie (1922–2005)","displayName-count-many":"lir tureckich (1922–2005)","displayName-count-other":"liry tureckiej (1922–2005)",symbol:"TRL"},TRY:{displayName:"lira turecka","displayName-count-one":"lira turecka","displayName-count-few":"liry tureckie","displayName-count-many":"lir tureckich","displayName-count-other":"liry tureckiej",symbol:"TRY","symbol-alt-narrow":"₺","symbol-alt-variant":"TL"},TTD:{displayName:"dolar Trynidadu i Tobago","displayName-count-one":"dolar Trynidadu i Tobago","displayName-count-few":"dolary Trynidadu i Tobago","displayName-count-many":"dolarów Trynidadu i Tobago","displayName-count-other":"dolara Trynidadu i Tobago",symbol:"TTD","symbol-alt-narrow":"$"},TWD:{displayName:"nowy dolar tajwański","displayName-count-one":"nowy dolar tajwański","displayName-count-few":"nowe dolary tajwańskie","displayName-count-many":"nowych dolarów tajwańskich","displayName-count-other":"nowego dolara tajwańskiego",symbol:"TWD","symbol-alt-narrow":"NT$"},TZS:{displayName:"szyling tanzański","displayName-count-one":"szyling tanzański","displayName-count-few":"szylingi tanzańskie","displayName-count-many":"szylingów tanzańskich","displayName-count-other":"szylinga tanzańskiego",symbol:"TZS"},UAH:{displayName:"hrywna ukraińska","displayName-count-one":"hrywna ukraińska","displayName-count-few":"hrywny ukraińskie","displayName-count-many":"hrywien ukraińskich","displayName-count-other":"hrywny ukraińskiej",symbol:"UAH","symbol-alt-narrow":"₴"},UAK:{displayName:"karbowaniec ukraiński","displayName-count-one":"karbowaniec ukraiński","displayName-count-few":"karbowańce ukraińskie","displayName-count-many":"karbowańców ukraińskich","displayName-count-other":"karbowaniec ukraiński",symbol:"UAK"},UGS:{displayName:"szyling ugandyjski (1966–1987)",symbol:"UGS"},UGX:{displayName:"szyling ugandyjski","displayName-count-one":"szyling ugandyjski","displayName-count-few":"szylingi ugandyjskie","displayName-count-many":"szylingów ugandyjskich","displayName-count-other":"szylinga ugandyjskiego",symbol:"UGX"},USD:{displayName:"dolar amerykański","displayName-count-one":"dolar amerykański","displayName-count-few":"dolary amerykańskie","displayName-count-many":"dolarów amerykańskich","displayName-count-other":"dolara amerykańskiego",symbol:"USD","symbol-alt-narrow":"$"},USN:{displayName:"USN",symbol:"USN"},USS:{displayName:"USS",symbol:"USS"},UYI:{displayName:"UYI",symbol:"UYI"},UYP:{displayName:"peso urugwajskie (1975–1993)",symbol:"UYP"},UYU:{displayName:"peso urugwajskie","displayName-count-one":"peso urugwajskie","displayName-count-few":"pesos urugwajskie","displayName-count-many":"pesos urugwajskich","displayName-count-other":"peso urugwajskiego",symbol:"UYU","symbol-alt-narrow":"$"},UYW:{displayName:"UYW",symbol:"UYW"},UZS:{displayName:"som uzbecki","displayName-count-one":"som uzbecki","displayName-count-few":"somy uzbeckie","displayName-count-many":"somów uzbeckich","displayName-count-other":"soma uzbeckiego",symbol:"UZS"},VEB:{displayName:"boliwar wenezuelski (1871–2008)","displayName-count-one":"boliwar wenezuelski (1871–2008)","displayName-count-few":"boliwary wenezuelskie (1871–2008)","displayName-count-many":"boliwarów wenezuelskich (1871–2008)","displayName-count-other":"boliwary wenezuelskiego (1871–2008)",symbol:"VEB"},VEF:{displayName:"boliwar wenezuelski (2008–2018)","displayName-count-one":"boliwar wenezuelski (2008–2018)","displayName-count-few":"boliwary wenezuelskie (2008–2018)","displayName-count-many":"boliwarów wenezuelskich (2008–2018)","displayName-count-other":"boliwara wenezuelskiego (2008–2018)",symbol:"VEF","symbol-alt-narrow":"Bs"},VES:{displayName:"boliwar wenezuelski","displayName-count-one":"boliwar wenezuelski","displayName-count-few":"boliwary wenezuelskie","displayName-count-many":"boliwarów wenezuelskich","displayName-count-other":"boliwara wenezuelskiego",symbol:"VES"},VND:{displayName:"dong wietnamski","displayName-count-one":"dong wietnamski","displayName-count-few":"dongi wietnamskie","displayName-count-many":"dongów wietnamskich","displayName-count-other":"donga wietnamskiego",symbol:"VND","symbol-alt-narrow":"₫"},VNN:{displayName:"VNN",symbol:"VNN"},VUV:{displayName:"vatu wanuackie","displayName-count-one":"vatu wanuackie","displayName-count-few":"vatu wanuackie","displayName-count-many":"vatu wanuackich","displayName-count-other":"vatu wanuackiego",symbol:"VUV"},WST:{displayName:"tala samoańskie","displayName-count-one":"tala samoańskie","displayName-count-few":"tala samoańskie","displayName-count-many":"tala samoańskich","displayName-count-other":"tala samoańskiego",symbol:"WST"},XAF:{displayName:"frank CFA BEAC","displayName-count-one":"frank CFA BEAC","displayName-count-few":"franki CFA BEAC","displayName-count-many":"franków CFA BEAC","displayName-count-other":"franka CFA BEAC",symbol:"FCFA"},XAG:{displayName:"srebro",symbol:"XAG"},XAU:{displayName:"złoto",symbol:"XAU"},XBA:{displayName:"XBA",symbol:"XBA"},XBB:{displayName:"XBB",symbol:"XBB"},XBC:{displayName:"europejska jednostka rozrachunkowa (XBC)",symbol:"XBC"},XBD:{displayName:"europejska jednostka rozrachunkowa (XBD)",symbol:"XBD"},XCD:{displayName:"dolar wschodniokaraibski","displayName-count-one":"dolar wschodniokaraibski","displayName-count-few":"dolary wschodniokaraibskie","displayName-count-many":"dolarów wschodniokaraibskich","displayName-count-other":"dolara wschodniokaraibskiego",symbol:"EC$","symbol-alt-narrow":"$"},XDR:{displayName:"specjalne prawa ciągnienia",symbol:"XDR"},XEU:{displayName:"ECU",symbol:"XEU"},XFO:{displayName:"frank złoty francuski",symbol:"XFO"},XFU:{displayName:"UIC-frank francuski",symbol:"XFU"},XOF:{displayName:"frank CFA","displayName-count-one":"frank CFA","displayName-count-few":"franki CFA","displayName-count-many":"franków CFA","displayName-count-other":"franka CFA",symbol:"CFA"},XPD:{displayName:"pallad",symbol:"XPD"},XPF:{displayName:"frank CFP","displayName-count-one":"frank CFP","displayName-count-few":"franki CFP","displayName-count-many":"franków CFP","displayName-count-other":"franka CFP",symbol:"CFPF"},XPT:{displayName:"platyna",symbol:"XPT"},XRE:{displayName:"XRE",symbol:"XRE"},XSU:{displayName:"XSU",symbol:"XSU"},XTS:{displayName:"testowy kod waluty",symbol:"XTS"},XUA:{displayName:"XUA",symbol:"XUA"},XXX:{displayName:"nieznana waluta","displayName-count-one":"(nieznana waluta)","displayName-count-few":"(nieznana waluta)","displayName-count-many":"(nieznana waluta)","displayName-count-other":"(nieznana waluta)",symbol:"¤"},YDD:{displayName:"dinar jemeński",symbol:"YDD"},YER:{displayName:"rial jemeński","displayName-count-one":"rial jemeński","displayName-count-few":"riale jemeńskie","displayName-count-many":"riali jemeńskich","displayName-count-other":"riala jemeńskiego",symbol:"YER"},YUD:{displayName:"YUD",symbol:"YUD"},YUM:{displayName:"nowy dinar jugosławiański",symbol:"YUM"},YUN:{displayName:"dinar jugosławiański wymienny",symbol:"YUN"},YUR:{displayName:"YUR",symbol:"YUR"},ZAL:{displayName:"rand południowoafrykański (finansowy)",symbol:"ZAL"},ZAR:{displayName:"rand południowoafrykański","displayName-count-one":"rand południowoafrykański","displayName-count-few":"randy południowoafrykańskie","displayName-count-many":"randów południowoafrykańskich","displayName-count-other":"randa południowoafrykańskiego",symbol:"ZAR","symbol-alt-narrow":"R"},ZMK:{displayName:"kwacha zambijska (1968–2012)","displayName-count-one":"kwacha zambijska (1968–2012)","displayName-count-few":"kwacha zambijskie (1968–2012)","displayName-count-many":"kwacha zambijskich (1968–2012)","displayName-count-other":"kwacha zambijskiej (1968–2012)",symbol:"ZMK"},ZMW:{displayName:"kwacha zambijska","displayName-count-one":"kwacha zambijska","displayName-count-few":"kwachy zambijskie","displayName-count-many":"kwach zambijskich","displayName-count-other":"kwachy zambijskiej",symbol:"ZMW","symbol-alt-narrow":"ZK"},ZRN:{displayName:"nowy zair zairski",symbol:"ZRN"},ZRZ:{displayName:"zair zairski",symbol:"ZRZ"},ZWD:{displayName:"dolar Zimbabwe (1980–2008)",symbol:"ZWD"},ZWL:{displayName:"dolar Zimbabwe (2009)",symbol:"ZWL"},ZWR:{displayName:"dolar Zimbabwe (2008)",symbol:"ZWR"}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},currencyData:{fractions:{ADP:{_rounding:"0",_digits:"0"},AFN:{_rounding:"0",_digits:"0"},ALL:{_rounding:"0",_digits:"0"},AMD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},BHD:{_rounding:"0",_digits:"3"},BIF:{_rounding:"0",_digits:"0"},BYN:{_rounding:"0",_digits:"2"},BYR:{_rounding:"0",_digits:"0"},CAD:{_rounding:"0",_digits:"2",_cashRounding:"5"},CHF:{_rounding:"0",_digits:"2",_cashRounding:"5"},CLF:{_rounding:"0",_digits:"4"},CLP:{_rounding:"0",_digits:"0"},COP:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CRC:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},CZK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},DEFAULT:{_rounding:"0",_digits:"2"},DJF:{_rounding:"0",_digits:"0"},DKK:{_rounding:"0",_digits:"2",_cashRounding:"50"},ESP:{_rounding:"0",_digits:"0"},GNF:{_rounding:"0",_digits:"0"},GYD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},HUF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IDR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},IQD:{_rounding:"0",_digits:"0"},IRR:{_rounding:"0",_digits:"0"},ISK:{_rounding:"0",_digits:"0"},ITL:{_rounding:"0",_digits:"0"},JOD:{_rounding:"0",_digits:"3"},JPY:{_rounding:"0",_digits:"0"},KMF:{_rounding:"0",_digits:"0"},KPW:{_rounding:"0",_digits:"0"},KRW:{_rounding:"0",_digits:"0"},KWD:{_rounding:"0",_digits:"3"},LAK:{_rounding:"0",_digits:"0"},LBP:{_rounding:"0",_digits:"0"},LUF:{_rounding:"0",_digits:"0"},LYD:{_rounding:"0",_digits:"3"},MGA:{_rounding:"0",_digits:"0"},MGF:{_rounding:"0",_digits:"0"},MMK:{_rounding:"0",_digits:"0"},MNT:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},MRO:{_rounding:"0",_digits:"0"},MUR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},NOK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},OMR:{_rounding:"0",_digits:"3"},PKR:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},PYG:{_rounding:"0",_digits:"0"},RSD:{_rounding:"0",_digits:"0"},RWF:{_rounding:"0",_digits:"0"},SEK:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},SLL:{_rounding:"0",_digits:"0"},SOS:{_rounding:"0",_digits:"0"},STD:{_rounding:"0",_digits:"0"},SYP:{_rounding:"0",_digits:"0"},TMM:{_rounding:"0",_digits:"0"},TND:{_rounding:"0",_digits:"3"},TRL:{_rounding:"0",_digits:"0"},TWD:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},TZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},UGX:{_rounding:"0",_digits:"0"},UYI:{_rounding:"0",_digits:"0"},UYW:{_rounding:"0",_digits:"4"},UZS:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VEF:{_rounding:"0",_digits:"2",_cashRounding:"0",_cashDigits:"0"},VND:{_rounding:"0",_digits:"0"},VUV:{_rounding:"0",_digits:"0"},XAF:{_rounding:"0",_digits:"0"},XOF:{_rounding:"0",_digits:"0"},XPF:{_rounding:"0",_digits:"0"},YER:{_rounding:"0",_digits:"0"},ZMK:{_rounding:"0",_digits:"0"},ZWD:{_rounding:"0",_digits:"0"}},region:{AC:[{SHP:{_from:"1976-01-01"}}],AD:[{ESP:{_from:"1873-01-01",_to:"2002-02-28"}},{ADP:{_from:"1936-01-01",_to:"2001-12-31"}},{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],AE:[{AED:{_from:"1973-05-19"}}],AF:[{AFA:{_from:"1927-03-14",_to:"2002-12-31"}},{AFN:{_from:"2002-10-07"}}],AG:[{XCD:{_from:"1965-10-06"}}],AI:[{XCD:{_from:"1965-10-06"}}],AL:[{ALK:{_from:"1946-11-01",_to:"1965-08-16"}},{ALL:{_from:"1965-08-16"}}],AM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-22"}},{AMD:{_from:"1993-11-22"}}],AO:[{AOK:{_from:"1977-01-08",_to:"1991-03-01"}},{AON:{_from:"1990-09-25",_to:"2000-02-01"}},{AOR:{_from:"1995-07-01",_to:"2000-02-01"}},{AOA:{_from:"1999-12-13"}}],AQ:[{XXX:{_tender:"false"}}],AR:[{ARM:{_from:"1881-11-05",_to:"1970-01-01"}},{ARL:{_from:"1970-01-01",_to:"1983-06-01"}},{ARP:{_from:"1983-06-01",_to:"1985-06-14"}},{ARA:{_from:"1985-06-14",_to:"1992-01-01"}},{ARS:{_from:"1992-01-01"}}],AS:[{USD:{_from:"1904-07-16"}}],AT:[{ATS:{_from:"1947-12-04",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],AU:[{AUD:{_from:"1966-02-14"}}],AW:[{ANG:{_from:"1940-05-10",_to:"1986-01-01"}},{AWG:{_from:"1986-01-01"}}],AX:[{EUR:{_from:"1999-01-01"}}],AZ:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-01-01"}},{AZM:{_from:"1993-11-22",_to:"2006-12-31"}},{AZN:{_from:"2006-01-01"}}],BA:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-01"}},{YUR:{_from:"1992-07-01",_to:"1993-10-01"}},{BAD:{_from:"1992-07-01",_to:"1994-08-15"}},{BAN:{_from:"1994-08-15",_to:"1997-07-01"}},{BAM:{_from:"1995-01-01"}}],BB:[{XCD:{_from:"1965-10-06",_to:"1973-12-03"}},{BBD:{_from:"1973-12-03"}}],BD:[{INR:{_from:"1835-08-17",_to:"1948-04-01"}},{PKR:{_from:"1948-04-01",_to:"1972-01-01"}},{BDT:{_from:"1972-01-01"}}],BE:[{NLG:{_from:"1816-12-15",_to:"1831-02-07"}},{BEF:{_from:"1831-02-07",_to:"2002-02-28"}},{BEC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{BEL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],BF:[{XOF:{_from:"1984-08-04"}}],BG:[{BGO:{_from:"1879-07-08",_to:"1952-05-12"}},{BGM:{_from:"1952-05-12",_to:"1962-01-01"}},{BGL:{_from:"1962-01-01",_to:"1999-07-05"}},{BGN:{_from:"1999-07-05"}}],BH:[{BHD:{_from:"1965-10-16"}}],BI:[{BIF:{_from:"1964-05-19"}}],BJ:[{XOF:{_from:"1975-11-30"}}],BL:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],BM:[{BMD:{_from:"1970-02-06"}}],BN:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{BND:{_from:"1967-06-12"}}],BO:[{BOV:{_tender:"false"}},{BOL:{_from:"1863-06-23",_to:"1963-01-01"}},{BOP:{_from:"1963-01-01",_to:"1986-12-31"}},{BOB:{_from:"1987-01-01"}}],BQ:[{ANG:{_from:"2010-10-10",_to:"2011-01-01"}},{USD:{_from:"2011-01-01"}}],BR:[{BRZ:{_from:"1942-11-01",_to:"1967-02-13"}},{BRB:{_from:"1967-02-13",_to:"1986-02-28"}},{BRC:{_from:"1986-02-28",_to:"1989-01-15"}},{BRN:{_from:"1989-01-15",_to:"1990-03-16"}},{BRE:{_from:"1990-03-16",_to:"1993-08-01"}},{BRR:{_from:"1993-08-01",_to:"1994-07-01"}},{BRL:{_from:"1994-07-01"}}],BS:[{BSD:{_from:"1966-05-25"}}],BT:[{INR:{_from:"1907-01-01"}},{BTN:{_from:"1974-04-16"}}],BU:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}}],BV:[{NOK:{_from:"1905-06-07"}}],BW:[{ZAR:{_from:"1961-02-14",_to:"1976-08-23"}},{BWP:{_from:"1976-08-23"}}],BY:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1994-11-08"}},{BYB:{_from:"1994-08-01",_to:"2000-12-31"}},{BYR:{_from:"2000-01-01",_to:"2017-01-01"}},{BYN:{_from:"2016-07-01"}}],BZ:[{BZD:{_from:"1974-01-01"}}],CA:[{CAD:{_from:"1858-01-01"}}],CC:[{AUD:{_from:"1966-02-14"}}],CD:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-01"}},{CDF:{_from:"1998-07-01"}}],CF:[{XAF:{_from:"1993-01-01"}}],CG:[{XAF:{_from:"1993-01-01"}}],CH:[{CHE:{_tender:"false"}},{CHW:{_tender:"false"}},{CHF:{_from:"1799-03-17"}}],CI:[{XOF:{_from:"1958-12-04"}}],CK:[{NZD:{_from:"1967-07-10"}}],CL:[{CLF:{_tender:"false"}},{CLE:{_from:"1960-01-01",_to:"1975-09-29"}},{CLP:{_from:"1975-09-29"}}],CM:[{XAF:{_from:"1973-04-01"}}],CN:[{CNY:{_from:"1953-03-01"}},{CNX:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{CNH:{_tender:"false",_from:"2010-07-19"}}],CO:[{COU:{_tender:"false"}},{COP:{_from:"1905-01-01"}}],CP:[{XXX:{_tender:"false"}}],CR:[{CRC:{_from:"1896-10-26"}}],CS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-06-03"}},{EUR:{_from:"2003-02-04",_to:"2006-06-03"}}],CU:[{CUP:{_from:"1859-01-01"}},{USD:{_from:"1899-01-01",_to:"1959-01-01"}},{CUC:{_from:"1994-01-01"}}],CV:[{PTE:{_from:"1911-05-22",_to:"1975-07-05"}},{CVE:{_from:"1914-01-01"}}],CW:[{ANG:{_from:"2010-10-10"}}],CX:[{AUD:{_from:"1966-02-14"}}],CY:[{CYP:{_from:"1914-09-10",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],CZ:[{CSK:{_from:"1953-06-01",_to:"1993-03-01"}},{CZK:{_from:"1993-01-01"}}],DD:[{DDM:{_from:"1948-07-20",_to:"1990-10-02"}}],DE:[{DEM:{_from:"1948-06-20",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],DG:[{USD:{_from:"1965-11-08"}}],DJ:[{DJF:{_from:"1977-06-27"}}],DK:[{DKK:{_from:"1873-05-27"}}],DM:[{XCD:{_from:"1965-10-06"}}],DO:[{USD:{_from:"1905-06-21",_to:"1947-10-01"}},{DOP:{_from:"1947-10-01"}}],DZ:[{DZD:{_from:"1964-04-01"}}],EA:[{EUR:{_from:"1999-01-01"}}],EC:[{ECS:{_from:"1884-04-01",_to:"2000-10-02"}},{ECV:{_tender:"false",_from:"1993-05-23",_to:"2000-01-09"}},{USD:{_from:"2000-10-02"}}],EE:[{SUR:{_from:"1961-01-01",_to:"1992-06-20"}},{EEK:{_from:"1992-06-21",_to:"2010-12-31"}},{EUR:{_from:"2011-01-01"}}],EG:[{EGP:{_from:"1885-11-14"}}],EH:[{MAD:{_from:"1976-02-26"}}],ER:[{ETB:{_from:"1993-05-24",_to:"1997-11-08"}},{ERN:{_from:"1997-11-08"}}],ES:[{ESP:{_from:"1868-10-19",_to:"2002-02-28"}},{ESB:{_tender:"false",_from:"1975-01-01",_to:"1994-12-31"}},{ESA:{_tender:"false",_from:"1978-01-01",_to:"1981-12-31"}},{EUR:{_from:"1999-01-01"}}],ET:[{ETB:{_from:"1976-09-15"}}],EU:[{XEU:{_tender:"false",_from:"1979-01-01",_to:"1998-12-31"}},{EUR:{_from:"1999-01-01"}}],FI:[{FIM:{_from:"1963-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],FJ:[{FJD:{_from:"1969-01-13"}}],FK:[{FKP:{_from:"1901-01-01"}}],FM:[{JPY:{_from:"1914-10-03",_to:"1944-01-01"}},{USD:{_from:"1944-01-01"}}],FO:[{DKK:{_from:"1948-01-01"}}],FR:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GA:[{XAF:{_from:"1993-01-01"}}],GB:[{GBP:{_from:"1694-07-27"}}],GD:[{XCD:{_from:"1967-02-27"}}],GE:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-06-11"}},{GEK:{_from:"1993-04-05",_to:"1995-09-25"}},{GEL:{_from:"1995-09-23"}}],GF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GG:[{GBP:{_from:"1830-01-01"}}],GH:[{GHC:{_from:"1979-03-09",_to:"2007-12-31"}},{GHS:{_from:"2007-07-03"}}],GI:[{GIP:{_from:"1713-01-01"}}],GL:[{DKK:{_from:"1873-05-27"}}],GM:[{GMD:{_from:"1971-07-01"}}],GN:[{GNS:{_from:"1972-10-02",_to:"1986-01-06"}},{GNF:{_from:"1986-01-06"}}],GP:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],GQ:[{GQE:{_from:"1975-07-07",_to:"1986-06-01"}},{XAF:{_from:"1993-01-01"}}],GR:[{GRD:{_from:"1954-05-01",_to:"2002-02-28"}},{EUR:{_from:"2001-01-01"}}],GS:[{GBP:{_from:"1908-01-01"}}],GT:[{GTQ:{_from:"1925-05-27"}}],GU:[{USD:{_from:"1944-08-21"}}],GW:[{GWE:{_from:"1914-01-01",_to:"1976-02-28"}},{GWP:{_from:"1976-02-28",_to:"1997-03-31"}},{XOF:{_from:"1997-03-31"}}],GY:[{GYD:{_from:"1966-05-26"}}],HK:[{HKD:{_from:"1895-02-02"}}],HM:[{AUD:{_from:"1967-02-16"}}],HN:[{HNL:{_from:"1926-04-03"}}],HR:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1991-12-23"}},{HRD:{_from:"1991-12-23",_to:"1995-01-01"}},{HRK:{_from:"1994-05-30"}}],HT:[{HTG:{_from:"1872-08-26"}},{USD:{_from:"1915-01-01"}}],HU:[{HUF:{_from:"1946-07-23"}}],IC:[{EUR:{_from:"1999-01-01"}}],ID:[{IDR:{_from:"1965-12-13"}}],IE:[{GBP:{_from:"1800-01-01",_to:"1922-01-01"}},{IEP:{_from:"1922-01-01",_to:"2002-02-09"}},{EUR:{_from:"1999-01-01"}}],IL:[{ILP:{_from:"1948-08-16",_to:"1980-02-22"}},{ILR:{_from:"1980-02-22",_to:"1985-09-04"}},{ILS:{_from:"1985-09-04"}}],IM:[{GBP:{_from:"1840-01-03"}}],IN:[{INR:{_from:"1835-08-17"}}],IO:[{USD:{_from:"1965-11-08"}}],IQ:[{EGP:{_from:"1920-11-11",_to:"1931-04-19"}},{INR:{_from:"1920-11-11",_to:"1931-04-19"}},{IQD:{_from:"1931-04-19"}}],IR:[{IRR:{_from:"1932-05-13"}}],IS:[{DKK:{_from:"1873-05-27",_to:"1918-12-01"}},{ISJ:{_from:"1918-12-01",_to:"1981-01-01"}},{ISK:{_from:"1981-01-01"}}],IT:[{ITL:{_from:"1862-08-24",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],JE:[{GBP:{_from:"1837-01-01"}}],JM:[{JMD:{_from:"1969-09-08"}}],JO:[{JOD:{_from:"1950-07-01"}}],JP:[{JPY:{_from:"1871-06-01"}}],KE:[{KES:{_from:"1966-09-14"}}],KG:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-05-10"}},{KGS:{_from:"1993-05-10"}}],KH:[{KHR:{_from:"1980-03-20"}}],KI:[{AUD:{_from:"1966-02-14"}}],KM:[{KMF:{_from:"1975-07-06"}}],KN:[{XCD:{_from:"1965-10-06"}}],KP:[{KPW:{_from:"1959-04-17"}}],KR:[{KRO:{_from:"1945-08-15",_to:"1953-02-15"}},{KRH:{_from:"1953-02-15",_to:"1962-06-10"}},{KRW:{_from:"1962-06-10"}}],KW:[{KWD:{_from:"1961-04-01"}}],KY:[{JMD:{_from:"1969-09-08",_to:"1971-01-01"}},{KYD:{_from:"1971-01-01"}}],KZ:[{KZT:{_from:"1993-11-05"}}],LA:[{LAK:{_from:"1979-12-10"}}],LB:[{LBP:{_from:"1948-02-02"}}],LC:[{XCD:{_from:"1965-10-06"}}],LI:[{CHF:{_from:"1921-02-01"}}],LK:[{LKR:{_from:"1978-05-22"}}],LR:[{LRD:{_from:"1944-01-01"}}],LS:[{ZAR:{_from:"1961-02-14"}},{LSL:{_from:"1980-01-22"}}],LT:[{SUR:{_from:"1961-01-01",_to:"1992-10-01"}},{LTT:{_from:"1992-10-01",_to:"1993-06-25"}},{LTL:{_from:"1993-06-25",_to:"2014-12-31"}},{EUR:{_from:"2015-01-01"}}],LU:[{LUF:{_from:"1944-09-04",_to:"2002-02-28"}},{LUC:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{LUL:{_tender:"false",_from:"1970-01-01",_to:"1990-03-05"}},{EUR:{_from:"1999-01-01"}}],LV:[{SUR:{_from:"1961-01-01",_to:"1992-07-20"}},{LVR:{_from:"1992-05-07",_to:"1993-10-17"}},{LVL:{_from:"1993-06-28",_to:"2013-12-31"}},{EUR:{_from:"2014-01-01"}}],LY:[{LYD:{_from:"1971-09-01"}}],MA:[{MAF:{_from:"1881-01-01",_to:"1959-10-17"}},{MAD:{_from:"1959-10-17"}}],MC:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{MCF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MD:[{MDC:{_from:"1992-06-01",_to:"1993-11-29"}},{MDL:{_from:"1993-11-29"}}],ME:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{DEM:{_from:"1999-10-02",_to:"2002-05-15"}},{EUR:{_from:"2002-01-01"}}],MF:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MG:[{MGF:{_from:"1963-07-01",_to:"2004-12-31"}},{MGA:{_from:"1983-11-01"}}],MH:[{USD:{_from:"1944-01-01"}}],MK:[{MKN:{_from:"1992-04-26",_to:"1993-05-20"}},{MKD:{_from:"1993-05-20"}}],ML:[{XOF:{_from:"1958-11-24",_to:"1962-07-02"}},{MLF:{_from:"1962-07-02",_to:"1984-08-31"}},{XOF:{_from:"1984-06-01"}}],MM:[{BUK:{_from:"1952-07-01",_to:"1989-06-18"}},{MMK:{_from:"1989-06-18"}}],MN:[{MNT:{_from:"1915-03-01"}}],MO:[{MOP:{_from:"1901-01-01"}}],MP:[{USD:{_from:"1944-01-01"}}],MQ:[{FRF:{_from:"1960-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],MR:[{XOF:{_from:"1958-11-28",_to:"1973-06-29"}},{MRO:{_from:"1973-06-29",_to:"2018-06-30"}},{MRU:{_from:"2018-01-01"}}],MS:[{XCD:{_from:"1967-02-27"}}],MT:[{MTP:{_from:"1914-08-13",_to:"1968-06-07"}},{MTL:{_from:"1968-06-07",_to:"2008-01-31"}},{EUR:{_from:"2008-01-01"}}],MU:[{MUR:{_from:"1934-04-01"}}],MV:[{MVP:{_from:"1947-01-01",_to:"1981-07-01"}},{MVR:{_from:"1981-07-01"}}],MW:[{MWK:{_from:"1971-02-15"}}],MX:[{MXV:{_tender:"false"}},{MXP:{_from:"1822-01-01",_to:"1992-12-31"}},{MXN:{_from:"1993-01-01"}}],MY:[{MYR:{_from:"1963-09-16"}}],MZ:[{MZE:{_from:"1975-06-25",_to:"1980-06-16"}},{MZM:{_from:"1980-06-16",_to:"2006-12-31"}},{MZN:{_from:"2006-07-01"}}],NA:[{ZAR:{_from:"1961-02-14"}},{NAD:{_from:"1993-01-01"}}],NC:[{XPF:{_from:"1985-01-01"}}],NE:[{XOF:{_from:"1958-12-19"}}],NF:[{AUD:{_from:"1966-02-14"}}],NG:[{NGN:{_from:"1973-01-01"}}],NI:[{NIC:{_from:"1988-02-15",_to:"1991-04-30"}},{NIO:{_from:"1991-04-30"}}],NL:[{NLG:{_from:"1813-01-01",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],NO:[{SEK:{_from:"1873-05-27",_to:"1905-06-07"}},{NOK:{_from:"1905-06-07"}}],NP:[{INR:{_from:"1870-01-01",_to:"1966-10-17"}},{NPR:{_from:"1933-01-01"}}],NR:[{AUD:{_from:"1966-02-14"}}],NU:[{NZD:{_from:"1967-07-10"}}],NZ:[{NZD:{_from:"1967-07-10"}}],OM:[{OMR:{_from:"1972-11-11"}}],PA:[{PAB:{_from:"1903-11-04"}},{USD:{_from:"1903-11-18"}}],PE:[{PES:{_from:"1863-02-14",_to:"1985-02-01"}},{PEI:{_from:"1985-02-01",_to:"1991-07-01"}},{PEN:{_from:"1991-07-01"}}],PF:[{XPF:{_from:"1945-12-26"}}],PG:[{AUD:{_from:"1966-02-14",_to:"1975-09-16"}},{PGK:{_from:"1975-09-16"}}],PH:[{PHP:{_from:"1946-07-04"}}],PK:[{INR:{_from:"1835-08-17",_to:"1947-08-15"}},{PKR:{_from:"1948-04-01"}}],PL:[{PLZ:{_from:"1950-10-28",_to:"1994-12-31"}},{PLN:{_from:"1995-01-01"}}],PM:[{FRF:{_from:"1972-12-21",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],PN:[{NZD:{_from:"1969-01-13"}}],PR:[{ESP:{_from:"1800-01-01",_to:"1898-12-10"}},{USD:{_from:"1898-12-10"}}],PS:[{JOD:{_from:"1950-07-01",_to:"1967-06-01"}},{ILP:{_from:"1967-06-01",_to:"1980-02-22"}},{ILS:{_from:"1985-09-04"}},{JOD:{_from:"1996-02-12"}}],PT:[{PTE:{_from:"1911-05-22",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],PW:[{USD:{_from:"1944-01-01"}}],PY:[{PYG:{_from:"1943-11-01"}}],QA:[{QAR:{_from:"1973-05-19"}}],RE:[{FRF:{_from:"1975-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],RO:[{ROL:{_from:"1952-01-28",_to:"2006-12-31"}},{RON:{_from:"2005-07-01"}}],RS:[{YUM:{_from:"1994-01-24",_to:"2002-05-15"}},{CSD:{_from:"2002-05-15",_to:"2006-10-25"}},{RSD:{_from:"2006-10-25"}}],RU:[{RUR:{_from:"1991-12-25",_to:"1998-12-31"}},{RUB:{_from:"1999-01-01"}}],RW:[{RWF:{_from:"1964-05-19"}}],SA:[{SAR:{_from:"1952-10-22"}}],SB:[{AUD:{_from:"1966-02-14",_to:"1978-06-30"}},{SBD:{_from:"1977-10-24"}}],SC:[{SCR:{_from:"1903-11-01"}}],SD:[{EGP:{_from:"1889-01-19",_to:"1958-01-01"}},{GBP:{_from:"1889-01-19",_to:"1958-01-01"}},{SDP:{_from:"1957-04-08",_to:"1998-06-01"}},{SDD:{_from:"1992-06-08",_to:"2007-06-30"}},{SDG:{_from:"2007-01-10"}}],SE:[{SEK:{_from:"1873-05-27"}}],SG:[{MYR:{_from:"1963-09-16",_to:"1967-06-12"}},{SGD:{_from:"1967-06-12"}}],SH:[{SHP:{_from:"1917-02-15"}}],SI:[{SIT:{_from:"1992-10-07",_to:"2007-01-14"}},{EUR:{_from:"2007-01-01"}}],SJ:[{NOK:{_from:"1905-06-07"}}],SK:[{CSK:{_from:"1953-06-01",_to:"1992-12-31"}},{SKK:{_from:"1992-12-31",_to:"2009-01-01"}},{EUR:{_from:"2009-01-01"}}],SL:[{GBP:{_from:"1808-11-30",_to:"1966-02-04"}},{SLL:{_from:"1964-08-04"}}],SM:[{ITL:{_from:"1865-12-23",_to:"2001-02-28"}},{EUR:{_from:"1999-01-01"}}],SN:[{XOF:{_from:"1959-04-04"}}],SO:[{SOS:{_from:"1960-07-01"}}],SR:[{NLG:{_from:"1815-11-20",_to:"1940-05-10"}},{SRG:{_from:"1940-05-10",_to:"2003-12-31"}},{SRD:{_from:"2004-01-01"}}],SS:[{SDG:{_from:"2007-01-10",_to:"2011-09-01"}},{SSP:{_from:"2011-07-18"}}],ST:[{STD:{_from:"1977-09-08",_to:"2017-12-31"}},{STN:{_from:"2018-01-01"}}],SU:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}}],SV:[{SVC:{_from:"1919-11-11",_to:"2001-01-01"}},{USD:{_from:"2001-01-01"}}],SX:[{ANG:{_from:"2010-10-10"}}],SY:[{SYP:{_from:"1948-01-01"}}],SZ:[{SZL:{_from:"1974-09-06"}}],TA:[{GBP:{_from:"1938-01-12"}}],TC:[{USD:{_from:"1969-09-08"}}],TD:[{XAF:{_from:"1993-01-01"}}],TF:[{FRF:{_from:"1959-01-01",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],TG:[{XOF:{_from:"1958-11-28"}}],TH:[{THB:{_from:"1928-04-15"}}],TJ:[{RUR:{_from:"1991-12-25",_to:"1995-05-10"}},{TJR:{_from:"1995-05-10",_to:"2000-10-25"}},{TJS:{_from:"2000-10-26"}}],TK:[{NZD:{_from:"1967-07-10"}}],TL:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}},{USD:{_from:"1999-10-20"}}],TM:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1993-11-01"}},{TMM:{_from:"1993-11-01",_to:"2009-01-01"}},{TMT:{_from:"2009-01-01"}}],TN:[{TND:{_from:"1958-11-01"}}],TO:[{TOP:{_from:"1966-02-14"}}],TP:[{TPE:{_from:"1959-01-02",_to:"2002-05-20"}},{IDR:{_from:"1975-12-07",_to:"2002-05-20"}}],TR:[{TRL:{_from:"1922-11-01",_to:"2005-12-31"}},{TRY:{_from:"2005-01-01"}}],TT:[{TTD:{_from:"1964-01-01"}}],TV:[{AUD:{_from:"1966-02-14"}}],TW:[{TWD:{_from:"1949-06-15"}}],TZ:[{TZS:{_from:"1966-06-14"}}],UA:[{SUR:{_from:"1961-01-01",_to:"1991-12-25"}},{RUR:{_from:"1991-12-25",_to:"1992-11-13"}},{UAK:{_from:"1992-11-13",_to:"1993-10-17"}},{UAH:{_from:"1996-09-02"}}],UG:[{UGS:{_from:"1966-08-15",_to:"1987-05-15"}},{UGX:{_from:"1987-05-15"}}],UM:[{USD:{_from:"1944-01-01"}}],US:[{USN:{_tender:"false"}},{USS:{_tender:"false",_to:"2014-03-01"}},{USD:{_from:"1792-01-01"}}],UY:[{UYI:{_tender:"false"}},{UYW:{_tender:"false"}},{UYP:{_from:"1975-07-01",_to:"1993-03-01"}},{UYU:{_from:"1993-03-01"}}],UZ:[{UZS:{_from:"1994-07-01"}}],VA:[{ITL:{_from:"1870-10-19",_to:"2002-02-28"}},{EUR:{_from:"1999-01-01"}}],VC:[{XCD:{_from:"1965-10-06"}}],VE:[{VEB:{_from:"1871-05-11",_to:"2008-06-30"}},{VEF:{_from:"2008-01-01",_to:"2018-08-20"}},{VES:{_from:"2018-08-20"}}],VG:[{USD:{_from:"1833-01-01"}},{GBP:{_from:"1833-01-01",_to:"1959-01-01"}}],VI:[{USD:{_from:"1837-01-01"}}],VN:[{VNN:{_from:"1978-05-03",_to:"1985-09-14"}},{VND:{_from:"1985-09-14"}}],VU:[{VUV:{_from:"1981-01-01"}}],WF:[{XPF:{_from:"1961-07-30"}}],WS:[{WST:{_from:"1967-07-10"}}],XK:[{YUM:{_from:"1994-01-24",_to:"1999-09-30"}},{DEM:{_from:"1999-09-01",_to:"2002-03-09"}},{EUR:{_from:"2002-01-01"}}],YD:[{YDD:{_from:"1965-04-01",_to:"1996-01-01"}}],YE:[{YER:{_from:"1990-05-22"}}],YT:[{KMF:{_from:"1975-01-01",_to:"1976-02-23"}},{FRF:{_from:"1976-02-23",_to:"2002-02-17"}},{EUR:{_from:"1999-01-01"}}],YU:[{YUD:{_from:"1966-01-01",_to:"1990-01-01"}},{YUN:{_from:"1990-01-01",_to:"1992-07-24"}},{YUM:{_from:"1994-01-24",_to:"2002-05-15"}}],ZA:[{ZAR:{_from:"1961-02-14"}},{ZAL:{_tender:"false",_from:"1985-09-01",_to:"1995-03-13"}}],ZM:[{ZMK:{_from:"1968-01-16",_to:"2013-01-01"}},{ZMW:{_from:"2013-01-01"}}],ZR:[{ZRZ:{_from:"1971-10-27",_to:"1993-11-01"}},{ZRN:{_from:"1993-11-01",_to:"1998-07-31"}}],ZW:[{RHD:{_from:"1970-02-17",_to:"1980-04-18"}},{ZWD:{_from:"1980-04-18",_to:"2008-08-01"}},{ZWR:{_from:"2008-08-01",_to:"2009-02-02"}},{ZWL:{_from:"2009-02-02",_to:"2009-04-12"}},{USD:{_from:"2009-04-12"}}],ZZ:[{XAG:{_tender:"false"}},{XAU:{_tender:"false"}},{XBA:{_tender:"false"}},{XBB:{_tender:"false"}},{XBC:{_tender:"false"}},{XBD:{_tender:"false"}},{XDR:{_tender:"false"}},{XPD:{_tender:"false"}},{XPT:{_tender:"false"}},{XSU:{_tender:"false"}},{XTS:{_tender:"false"}},{XUA:{_tender:"false"}},{XXX:{_tender:"false"}},{XRE:{_tender:"false",_to:"1999-11-30"}},{XFU:{_tender:"false",_to:"2013-11-30"}},{XFO:{_tender:"false",_from:"1930-01-01",_to:"2003-04-01"}}]}}}},{main:{pl:{identity:{version:{_cldrVersion:"36"},language:"pl"},dates:{calendars:{gregorian:{months:{format:{abbreviated:{1:"sty",2:"lut",3:"mar",4:"kwi",5:"maj",6:"cze",7:"lip",8:"sie",9:"wrz",10:"paź",11:"lis",12:"gru"},narrow:{1:"s",2:"l",3:"m",4:"k",5:"m",6:"c",7:"l",8:"s",9:"w",10:"p",11:"l",12:"g"},wide:{1:"stycznia",2:"lutego",3:"marca",4:"kwietnia",5:"maja",6:"czerwca",7:"lipca",8:"sierpnia",9:"września",10:"października",11:"listopada",12:"grudnia"}},"stand-alone":{abbreviated:{1:"sty",2:"lut",3:"mar",4:"kwi",5:"maj",6:"cze",7:"lip",8:"sie",9:"wrz",10:"paź",11:"lis",12:"gru"},narrow:{1:"S",2:"L",3:"M",4:"K",5:"M",6:"C",7:"L",8:"S",9:"W",10:"P",11:"L",12:"G"},wide:{1:"styczeń",2:"luty",3:"marzec",4:"kwiecień",5:"maj",6:"czerwiec",7:"lipiec",8:"sierpień",9:"wrzesień",10:"październik",11:"listopad",12:"grudzień"}}},days:{format:{abbreviated:{sun:"niedz.",mon:"pon.",tue:"wt.",wed:"śr.",thu:"czw.",fri:"pt.",sat:"sob."},narrow:{sun:"n",mon:"p",tue:"w",wed:"ś",thu:"c",fri:"p",sat:"s"},short:{sun:"nie",mon:"pon",tue:"wto",wed:"śro",thu:"czw",fri:"pią",sat:"sob"},wide:{sun:"niedziela",mon:"poniedziałek",tue:"wtorek",wed:"środa",thu:"czwartek",fri:"piątek",sat:"sobota"}},"stand-alone":{abbreviated:{sun:"niedz.",mon:"pon.",tue:"wt.",wed:"śr.",thu:"czw.",fri:"pt.",sat:"sob."},narrow:{sun:"N",mon:"P",tue:"W",wed:"Ś",thu:"C",fri:"P",sat:"S"},short:{sun:"nie",mon:"pon",tue:"wto",wed:"śro",thu:"czw",fri:"pią",sat:"sob"},wide:{sun:"niedziela",mon:"poniedziałek",tue:"wtorek",wed:"środa",thu:"czwartek",fri:"piątek",sat:"sobota"}}},quarters:{format:{abbreviated:{1:"I kw.",2:"II kw.",3:"III kw.",4:"IV kw."},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"I kwartał",2:"II kwartał",3:"III kwartał",4:"IV kwartał"}},"stand-alone":{abbreviated:{1:"I kw.",2:"II kw.",3:"III kw.",4:"IV kw."},narrow:{1:"1",2:"2",3:"3",4:"4"},wide:{1:"I kwartał",2:"II kwartał",3:"III kwartał",4:"IV kwartał"}}},dayPeriods:{format:{abbreviated:{midnight:"o północy",am:"AM",noon:"w południe",pm:"PM",morning1:"rano",morning2:"przed południem",afternoon1:"po południu",evening1:"wieczorem",night1:"w nocy"},narrow:{midnight:"o półn.",am:"a",noon:"w poł.",pm:"p",morning1:"rano",morning2:"przed poł.",afternoon1:"po poł.",evening1:"wiecz.",night1:"w nocy"},wide:{midnight:"o północy",am:"AM",noon:"w południe",pm:"PM",morning1:"rano",morning2:"przed południem",afternoon1:"po południu",evening1:"wieczorem",night1:"w nocy"}},"stand-alone":{abbreviated:{midnight:"północ",am:"AM",noon:"południe",pm:"PM",morning1:"rano",morning2:"przedpołudnie",afternoon1:"popołudnie",evening1:"wieczór",night1:"noc"},narrow:{midnight:"półn.",am:"a",noon:"poł.",pm:"p",morning1:"rano",morning2:"przedpoł.",afternoon1:"popoł.",evening1:"wiecz.",night1:"noc"},wide:{midnight:"północ",am:"AM",noon:"południe",pm:"PM",morning1:"rano",morning2:"przedpołudnie",afternoon1:"popołudnie",evening1:"wieczór",night1:"noc"}}},eras:{eraNames:{0:"przed naszą erą",1:"naszej ery","0-alt-variant":"p.n.e.","1-alt-variant":"n.e."},eraAbbr:{0:"p.n.e.",1:"n.e.","0-alt-variant":"BCE","1-alt-variant":"CE"},eraNarrow:{0:"p.n.e.",1:"n.e.","0-alt-variant":"BCE","1-alt-variant":"CE"}},dateFormats:{full:"EEEE, d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},timeFormats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},dateTimeFormats:{full:"{1} {0}",long:"{1} {0}",medium:"{1}, {0}",short:"{1}, {0}",availableFormats:{Bh:"h B",Bhm:"h:mm B",Bhms:"h:mm:ss B",d:"d",E:"ccc",EBhm:"E h:mm B",EBhms:"E h:mm:ss B",Ed:"E, d",Ehm:"E, h:mm a",EHm:"E, HH:mm",Ehms:"E, h:mm:ss a",EHms:"E, HH:mm:ss",Gy:"y G",GyMMM:"MMM y G",GyMMMd:"d MMM y G",GyMMMEd:"E, d MMM y G",GyMMMM:"LLLL y G",GyMMMMd:"d MMMM y G",GyMMMMEd:"E, d MMMM y G",h:"h a",H:"HH",hm:"h:mm a",Hm:"HH:mm",hms:"h:mm:ss a",Hms:"HH:mm:ss",hmsv:"h:mm:ss a v",Hmsv:"HH:mm:ss v",hmv:"h:mm a v",Hmv:"HH:mm v",M:"L",Md:"d.MM",MEd:"E, d.MM",MMM:"LLL",MMMd:"d MMM",MMMEd:"E, d MMM",MMMMd:"d MMMM",MMMMEd:"E, d MMMM","MMMMW-count-one":"LLLL, 'tydz'. W","MMMMW-count-few":"LLLL, 'tydz'. W","MMMMW-count-many":"LLLL, 'tydz'. W","MMMMW-count-other":"LLLL, 'tydz'. W",ms:"mm:ss",y:"y",yM:"MM.y",yMd:"d.MM.y",yMEd:"E, d.MM.y",yMMM:"LLL y",yMMMd:"d MMM y",yMMMEd:"E, d MMM y",yMMMM:"LLLL y",yMMMMd:"d MMMM y",yMMMMEd:"E, d MMMM y",yQQQ:"QQQ y",yQQQQ:"QQQQ y","yw-count-one":"Y, 'tydz'. w","yw-count-few":"Y, 'tydz'. w","yw-count-many":"Y, 'tydz'. w","yw-count-other":"Y, 'tydz'. w"},appendItems:{Day:"{0} ({2}: {1})","Day-Of-Week":"{0} {1}",Era:"{1} {0}",Hour:"{0} ({2}: {1})",Minute:"{0} ({2}: {1})",Month:"{0} ({2}: {1})",Quarter:"{0} ({2}: {1})",Second:"{0} ({2}: {1})",Timezone:"{0} {1}",Week:"{0} ({2}: {1})",Year:"{1} {0}"},intervalFormats:{intervalFormatFallback:"{0}–{1}",Bh:{B:"h B – h B",h:"h–h B"},Bhm:{B:"h:mm B – h:mm B",h:"h:mm–h:mm B",m:"h:mm–h:mm B"},d:{d:"d–d"},Gy:{G:"y G – y G",y:"y–y G"},GyM:{G:"M.y GGGGG – M.y GGGGG",M:"M.y – M.y GGGGG",y:"M.y – M.y GGGGG"},GyMd:{d:"d.M.y – d.M.y GGGGG",G:"d.M.y GGGGG – d.M.y GGGGG",M:"d.M.y – d.M.y GGGGG",y:"d.M.y – d.M.y GGGGG"},GyMEd:{d:"E, d.M.y – E, d.M.y GGGGG",G:"E, d.M.y GGGGG – E, d.M.y GGGGG",M:"E, d.M.y – E, d.M.y GGGGG",y:"E, d.M.y – E, d.M.y GGGGG"},GyMMM:{G:"MMM y G – MMM y G",M:"MMM – MMM y G",y:"MMM y – MMM y G"},GyMMMd:{d:"d–d MMM y G",G:"d MMM y G – d MMM y G",M:"d MMM – d MMM y G",y:"d MMM y – d MMM y G"},GyMMMEd:{d:"E, d MMM – E, d MMM y G",G:"E, d MMM y G – E, d MMM y G",M:"E, d MMM – E, d MMM y G",y:"E, d MMM y – E, d MMM y G"},h:{a:"h a–h a",h:"h–h a"},H:{H:"HH–HH"},hm:{a:"h:mm a–h:mm a",h:"h:mm–h:mm a",m:"h:mm–h:mm a"},Hm:{H:"HH:mm–HH:mm",m:"HH:mm–HH:mm"},hmv:{a:"h:mm a–h:mm a v",h:"h:mm–h:mm a v",m:"h:mm–h:mm a v"},Hmv:{H:"HH:mm–HH:mm v",m:"HH:mm–HH:mm v"},hv:{a:"h a – h a v",h:"h–h a v"},Hv:{H:"HH–HH v"},M:{M:"MM–MM"},Md:{d:"dd.MM–dd.MM",M:"dd.MM–dd.MM"},MEd:{d:"E, dd.MM–E, dd.MM",M:"E, dd.MM–E, dd.MM"},MMM:{M:"LLL–LLL"},MMMd:{d:"d–d MMM",M:"d MMM–d MMM"},MMMEd:{d:"E, d MMM–E, d MMM",M:"E, d MMM–E, d MMM"},MMMMd:{d:"d–d MMMM",M:"d MMMM – d MMMM"},MMMMEd:{d:"E, d MMMM – E, d MMMM",M:"E, d MMMM – E, d MMMM"},y:{y:"y–y"},yM:{M:"MM.y–MM.y",y:"MM.y–MM.y"},yMd:{d:"dd–dd.MM.y",M:"dd.MM–dd.MM.y",y:"dd.MM.y–dd.MM.y"},yMEd:{d:"E, dd.MM.y–E, dd.MM.y",M:"E, dd.MM.y–E, dd.MM.y",y:"E, dd.MM.y–E, dd.MM.y"},yMMM:{M:"LLL–LLL y",y:"LLL y–LLL y"},yMMMd:{d:"d–d MMM y",M:"d MMM–d MMM y",y:"d MMM y–d MMM y"},yMMMEd:{d:"E, d–E, d MMM y",M:"E, d MMM y–E, d MMM y",y:"E, d MMM y–E, d MMM y"},yMMMM:{M:"LLLL–LLLL y",y:"LLLL y–LLLL y"},yMMMMd:{d:"d–d MMMM y",M:"d MMMM – d MMMM y",y:"d MMMM y – d MMMM y"},yMMMMEd:{d:"E, d – E, d MMMM y",M:"E, d MMMM – E, d MMMM y",y:"E, d MMMM y – E, d MMMM y"}}}}}}}}},{main:{pl:{identity:{version:{_cldrVersion:"36"},language:"pl"},dates:{fields:{era:{displayName:"era"},"era-short":{displayName:"era"},"era-narrow":{displayName:"era"},year:{displayName:"rok","relative-type--1":"w zeszłym roku","relative-type-0":"w tym roku","relative-type-1":"w przyszłym roku","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} rok","relativeTimePattern-count-few":"za {0} lata","relativeTimePattern-count-many":"za {0} lat","relativeTimePattern-count-other":"za {0} roku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} rok temu","relativeTimePattern-count-few":"{0} lata temu","relativeTimePattern-count-many":"{0} lat temu","relativeTimePattern-count-other":"{0} roku temu"}},"year-short":{displayName:"r.","relative-type--1":"w zeszłym roku","relative-type-0":"w tym roku","relative-type-1":"w przyszłym roku","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} rok","relativeTimePattern-count-few":"za {0} lata","relativeTimePattern-count-many":"za {0} lat","relativeTimePattern-count-other":"za {0} roku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} rok temu","relativeTimePattern-count-few":"{0} lata temu","relativeTimePattern-count-many":"{0} lat temu","relativeTimePattern-count-other":"{0} roku temu"}},"year-narrow":{displayName:"r.","relative-type--1":"w zeszłym roku","relative-type-0":"w tym roku","relative-type-1":"w przyszłym roku","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} rok","relativeTimePattern-count-few":"za {0} lata","relativeTimePattern-count-many":"za {0} lat","relativeTimePattern-count-other":"za {0} roku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} rok temu","relativeTimePattern-count-few":"{0} lata temu","relativeTimePattern-count-many":"{0} lat temu","relativeTimePattern-count-other":"{0} roku temu"}},quarter:{displayName:"kwartał","relative-type--1":"w zeszłym kwartale","relative-type-0":"w tym kwartale","relative-type-1":"w przyszłym kwartale","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} kwartał","relativeTimePattern-count-few":"za {0} kwartały","relativeTimePattern-count-many":"za {0} kwartałów","relativeTimePattern-count-other":"za {0} kwartału"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} kwartał temu","relativeTimePattern-count-few":"{0} kwartały temu","relativeTimePattern-count-many":"{0} kwartałów temu","relativeTimePattern-count-other":"{0} kwartału temu"}},"quarter-short":{displayName:"kw.","relative-type--1":"w zeszłym kwartale","relative-type-0":"w tym kwartale","relative-type-1":"w przyszłym kwartale","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} kw.","relativeTimePattern-count-few":"za {0} kw.","relativeTimePattern-count-many":"za {0} kw.","relativeTimePattern-count-other":"za {0} kw."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} kw. temu","relativeTimePattern-count-few":"{0} kw. temu","relativeTimePattern-count-many":"{0} kw. temu","relativeTimePattern-count-other":"{0} kw. temu"}},"quarter-narrow":{displayName:"kw.","relative-type--1":"w zeszłym kwartale","relative-type-0":"w tym kwartale","relative-type-1":"w przyszłym kwartale","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} kw.","relativeTimePattern-count-few":"za {0} kw.","relativeTimePattern-count-many":"za {0} kw.","relativeTimePattern-count-other":"za {0} kw."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} kw. temu","relativeTimePattern-count-few":"{0} kw. temu","relativeTimePattern-count-many":"{0} kw. temu","relativeTimePattern-count-other":"{0} kw. temu"}},month:{displayName:"miesiąc","relative-type--1":"w zeszłym miesiącu","relative-type-0":"w tym miesiącu","relative-type-1":"w przyszłym miesiącu","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} miesiąc","relativeTimePattern-count-few":"za {0} miesiące","relativeTimePattern-count-many":"za {0} miesięcy","relativeTimePattern-count-other":"za {0} miesiąca"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} miesiąc temu","relativeTimePattern-count-few":"{0} miesiące temu","relativeTimePattern-count-many":"{0} miesięcy temu","relativeTimePattern-count-other":"{0} miesiąca temu"}},"month-short":{displayName:"mies.","relative-type--1":"w zeszłym mies.","relative-type-0":"w tym mies.","relative-type-1":"w przyszłym mies.","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} mies.","relativeTimePattern-count-few":"za {0} mies.","relativeTimePattern-count-many":"za {0} mies.","relativeTimePattern-count-other":"za {0} mies."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} mies. temu","relativeTimePattern-count-few":"{0} mies. temu","relativeTimePattern-count-many":"{0} mies. temu","relativeTimePattern-count-other":"{0} mies. temu"}},"month-narrow":{displayName:"mc","relative-type--1":"w zeszłym mies.","relative-type-0":"w tym mies.","relative-type-1":"w przyszłym mies.","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} mies.","relativeTimePattern-count-few":"za {0} mies.","relativeTimePattern-count-many":"za {0} mies.","relativeTimePattern-count-other":"za {0} mies."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} mies. temu","relativeTimePattern-count-few":"{0} mies. temu","relativeTimePattern-count-many":"{0} mies. temu","relativeTimePattern-count-other":"{0} mies. temu"}},week:{displayName:"tydzień","relative-type--1":"w zeszłym tygodniu","relative-type-0":"w tym tygodniu","relative-type-1":"w przyszłym tygodniu","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} tydzień","relativeTimePattern-count-few":"za {0} tygodnie","relativeTimePattern-count-many":"za {0} tygodni","relativeTimePattern-count-other":"za {0} tygodnia"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} tydzień temu","relativeTimePattern-count-few":"{0} tygodnie temu","relativeTimePattern-count-many":"{0} tygodni temu","relativeTimePattern-count-other":"{0} tygodnia temu"},relativePeriod:"tydzień {0}"},"week-short":{displayName:"tydz.","relative-type--1":"w zeszłym tyg.","relative-type-0":"w tym tyg.","relative-type-1":"w przyszłym tyg.","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} tydz.","relativeTimePattern-count-few":"za {0} tyg.","relativeTimePattern-count-many":"za {0} tyg.","relativeTimePattern-count-other":"za {0} tyg."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} tydz. temu","relativeTimePattern-count-few":"{0} tyg. temu","relativeTimePattern-count-many":"{0} tyg. temu","relativeTimePattern-count-other":"{0} tyg. temu"},relativePeriod:"tydzień {0}"},"week-narrow":{displayName:"tydz.","relative-type--1":"w zeszłym tyg.","relative-type-0":"w tym tyg.","relative-type-1":"w przyszłym tyg.","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} tydz.","relativeTimePattern-count-few":"za {0} tyg.","relativeTimePattern-count-many":"za {0} tyg.","relativeTimePattern-count-other":"za {0} tyg."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} tydz. temu","relativeTimePattern-count-few":"{0} tyg. temu","relativeTimePattern-count-many":"{0} tyg. temu","relativeTimePattern-count-other":"{0} tyg. temu"},relativePeriod:"tydzień {0}"},weekOfMonth:{displayName:"tydzień miesiąca"},"weekOfMonth-short":{displayName:"tydz. mies."},"weekOfMonth-narrow":{displayName:"tydz. mies."},day:{displayName:"dzień","relative-type--2":"przedwczoraj","relative-type--1":"wczoraj","relative-type-0":"dzisiaj","relative-type-1":"jutro","relative-type-2":"pojutrze","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} dzień","relativeTimePattern-count-few":"za {0} dni","relativeTimePattern-count-many":"za {0} dni","relativeTimePattern-count-other":"za {0} dnia"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} dzień temu","relativeTimePattern-count-few":"{0} dni temu","relativeTimePattern-count-many":"{0} dni temu","relativeTimePattern-count-other":"{0} dnia temu"}},"day-short":{displayName:"dz.","relative-type--2":"przedwczoraj","relative-type--1":"wczoraj","relative-type-0":"dzisiaj","relative-type-1":"jutro","relative-type-2":"pojutrze","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} dzień","relativeTimePattern-count-few":"za {0} dni","relativeTimePattern-count-many":"za {0} dni","relativeTimePattern-count-other":"za {0} dnia"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} dzień temu","relativeTimePattern-count-few":"{0} dni temu","relativeTimePattern-count-many":"{0} dni temu","relativeTimePattern-count-other":"{0} dnia temu"}},"day-narrow":{displayName:"d.","relative-type--2":"przedwczoraj","relative-type--1":"wcz.","relative-type-0":"dziś","relative-type-1":"jutro","relative-type-2":"pojutrze","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} dzień","relativeTimePattern-count-few":"za {0} dni","relativeTimePattern-count-many":"za {0} dni","relativeTimePattern-count-other":"za {0} dnia"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} dzień temu","relativeTimePattern-count-few":"{0} dni temu","relativeTimePattern-count-many":"{0} dni temu","relativeTimePattern-count-other":"{0} dnia temu"}},dayOfYear:{displayName:"dzień roku"},"dayOfYear-short":{displayName:"dz. roku"},"dayOfYear-narrow":{displayName:"dz. r."},weekday:{displayName:"dzień tygodnia"},"weekday-short":{displayName:"dzień tyg."},"weekday-narrow":{displayName:"dz. tyg."},weekdayOfMonth:{displayName:"dzień miesiąca"},"weekdayOfMonth-short":{displayName:"dzień mies."},"weekdayOfMonth-narrow":{displayName:"dz. mies."},sun:{"relative-type--1":"w zeszłą niedzielę","relative-type-0":"w tę niedzielę","relative-type-1":"w przyszłą niedzielę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} niedzielę","relativeTimePattern-count-few":"za {0} niedziele","relativeTimePattern-count-many":"za {0} niedziel","relativeTimePattern-count-other":"za {0} niedzieli"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} niedzielę temu","relativeTimePattern-count-few":"{0} niedziele temu","relativeTimePattern-count-many":"{0} niedziel temu","relativeTimePattern-count-other":"{0} niedzieli temu"}},"sun-short":{"relative-type--1":"w zeszłą niedzielę","relative-type-0":"w tę niedzielę","relative-type-1":"w przyszłą niedzielę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} niedzielę","relativeTimePattern-count-few":"za {0} niedziele","relativeTimePattern-count-many":"za {0} niedziel","relativeTimePattern-count-other":"za {0} niedzieli"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} niedzielę temu","relativeTimePattern-count-few":"{0} niedziele temu","relativeTimePattern-count-many":"{0} niedziel temu","relativeTimePattern-count-other":"{0} niedzieli temu"}},"sun-narrow":{"relative-type--1":"w zeszłą niedzielę","relative-type-0":"w tę niedzielę","relative-type-1":"w przyszłą niedzielę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} niedzielę","relativeTimePattern-count-few":"za {0} niedziele","relativeTimePattern-count-many":"za {0} niedziel","relativeTimePattern-count-other":"za {0} niedzieli"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} niedzielę temu","relativeTimePattern-count-few":"{0} niedziele temu","relativeTimePattern-count-many":"{0} niedziel temu","relativeTimePattern-count-other":"{0} niedzieli temu"}},mon:{"relative-type--1":"w zeszły poniedziałek","relative-type-0":"w ten poniedziałek","relative-type-1":"w przyszły poniedziałek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} poniedziałek","relativeTimePattern-count-few":"za {0} poniedziałki","relativeTimePattern-count-many":"za {0} poniedziałków","relativeTimePattern-count-other":"za {0} poniedziałku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} poniedziałek temu","relativeTimePattern-count-few":"{0} poniedziałki temu","relativeTimePattern-count-many":"{0} poniedziałków temu","relativeTimePattern-count-other":"{0} poniedziałku temu"}},"mon-short":{"relative-type--1":"w zeszły poniedziałek","relative-type-0":"w ten poniedziałek","relative-type-1":"w przyszły poniedziałek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} poniedziałek","relativeTimePattern-count-few":"za {0} poniedziałki","relativeTimePattern-count-many":"za {0} poniedziałków","relativeTimePattern-count-other":"za {0} poniedziałku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} poniedziałek temu","relativeTimePattern-count-few":"{0} poniedziałki temu","relativeTimePattern-count-many":"{0} poniedziałków temu","relativeTimePattern-count-other":"{0} poniedziałku temu"}},"mon-narrow":{"relative-type--1":"w zeszły poniedziałek","relative-type-0":"w ten poniedziałek","relative-type-1":"w przyszły poniedziałek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} poniedziałek","relativeTimePattern-count-few":"za {0} poniedziałki","relativeTimePattern-count-many":"za {0} poniedziałków","relativeTimePattern-count-other":"za {0} poniedziałku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} poniedziałek temu","relativeTimePattern-count-few":"{0} poniedziałki temu","relativeTimePattern-count-many":"{0} poniedziałków temu","relativeTimePattern-count-other":"{0} poniedziałku temu"}},tue:{"relative-type--1":"w zeszły wtorek","relative-type-0":"w ten wtorek","relative-type-1":"w przyszły wtorek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} wtorek","relativeTimePattern-count-few":"za {0} wtorki","relativeTimePattern-count-many":"za {0} wtorków","relativeTimePattern-count-other":"za {0} wtorku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} wtorek temu","relativeTimePattern-count-few":"{0} wtorki temu","relativeTimePattern-count-many":"{0} wtorków temu","relativeTimePattern-count-other":"{0} wtorku temu"}},"tue-short":{"relative-type--1":"w zeszły wtorek","relative-type-0":"w ten wtorek","relative-type-1":"w przyszły wtorek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} wtorek","relativeTimePattern-count-few":"za {0} wtorki","relativeTimePattern-count-many":"za {0} wtorków","relativeTimePattern-count-other":"za {0} wtorku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} wtorek temu","relativeTimePattern-count-few":"{0} wtorki temu","relativeTimePattern-count-many":"{0} wtorków temu","relativeTimePattern-count-other":"{0} wtorku temu"}},"tue-narrow":{"relative-type--1":"w zeszły wtorek","relative-type-0":"w ten wtorek","relative-type-1":"w przyszły wtorek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} wtorek","relativeTimePattern-count-few":"za {0} wtorki","relativeTimePattern-count-many":"za {0} wtorków","relativeTimePattern-count-other":"za {0} wtorku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} wtorek temu","relativeTimePattern-count-few":"{0} wtorki temu","relativeTimePattern-count-many":"{0} wtorków temu","relativeTimePattern-count-other":"{0} wtorku temu"}},wed:{"relative-type--1":"w zeszłą środę","relative-type-0":"w tę środę","relative-type-1":"w przyszłą środę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} środę","relativeTimePattern-count-few":"za {0} środy","relativeTimePattern-count-many":"za {0} śród","relativeTimePattern-count-other":"za {0} środy"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} środę temu","relativeTimePattern-count-few":"{0} środy temu","relativeTimePattern-count-many":"{0} śród temu","relativeTimePattern-count-other":"{0} środy temu"}},"wed-short":{"relative-type--1":"w zeszłą środę","relative-type-0":"w tę środę","relative-type-1":"w przyszłą środę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} środę","relativeTimePattern-count-few":"za {0} środy","relativeTimePattern-count-many":"za {0} śród","relativeTimePattern-count-other":"za {0} środy"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} środę temu","relativeTimePattern-count-few":"{0} środy temu","relativeTimePattern-count-many":"{0} śród temu","relativeTimePattern-count-other":"{0} środy temu"}},"wed-narrow":{"relative-type--1":"w zeszłą środę","relative-type-0":"w tę środę","relative-type-1":"w przyszłą środę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} środę","relativeTimePattern-count-few":"za {0} środy","relativeTimePattern-count-many":"za {0} śród","relativeTimePattern-count-other":"za {0} środy"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} środę temu","relativeTimePattern-count-few":"{0} środy temu","relativeTimePattern-count-many":"{0} śród temu","relativeTimePattern-count-other":"{0} środy temu"}},thu:{"relative-type--1":"w zeszły czwartek","relative-type-0":"w ten czwartek","relative-type-1":"w przyszły czwartek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} czwartek","relativeTimePattern-count-few":"za {0} czwartki","relativeTimePattern-count-many":"za {0} czwartków","relativeTimePattern-count-other":"za {0} czwartku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} czwartek temu","relativeTimePattern-count-few":"{0} czwartki temu","relativeTimePattern-count-many":"{0} czwartków temu","relativeTimePattern-count-other":"{0} czwartku temu"}},"thu-short":{"relative-type--1":"w zeszły czwartek","relative-type-0":"w ten czwartek","relative-type-1":"w przyszły czwartek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} czwartek","relativeTimePattern-count-few":"za {0} czwartki","relativeTimePattern-count-many":"za {0} czwartków","relativeTimePattern-count-other":"za {0} czwartku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} czwartek temu","relativeTimePattern-count-few":"{0} czwartki temu","relativeTimePattern-count-many":"{0} czwartków temu","relativeTimePattern-count-other":"{0} czwartku temu"}},"thu-narrow":{"relative-type--1":"w zeszły czwartek","relative-type-0":"w ten czwartek","relative-type-1":"w przyszły czwartek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} czwartek","relativeTimePattern-count-few":"za {0} czwartki","relativeTimePattern-count-many":"za {0} czwartków","relativeTimePattern-count-other":"za {0} czwartku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} czwartek temu","relativeTimePattern-count-few":"{0} czwartki temu","relativeTimePattern-count-many":"{0} czwartków temu","relativeTimePattern-count-other":"{0} czwartku temu"}},fri:{"relative-type--1":"w zeszły piątek","relative-type-0":"w ten piątek","relative-type-1":"w przyszły piątek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} piątek","relativeTimePattern-count-few":"za {0} piątki","relativeTimePattern-count-many":"za {0} piątków","relativeTimePattern-count-other":"za {0} piątku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} piątek temu","relativeTimePattern-count-few":"{0} piątki temu","relativeTimePattern-count-many":"{0} piątków temu","relativeTimePattern-count-other":"{0} piątku temu"}},"fri-short":{"relative-type--1":"w zeszły piątek","relative-type-0":"w ten piątek","relative-type-1":"w przyszły piątek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} piątek","relativeTimePattern-count-few":"za {0} piątki","relativeTimePattern-count-many":"za {0} piątków","relativeTimePattern-count-other":"za {0} piątku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} piątek temu","relativeTimePattern-count-few":"{0} piątki temu","relativeTimePattern-count-many":"{0} piątków temu","relativeTimePattern-count-other":"{0} piątku temu"}},"fri-narrow":{"relative-type--1":"w zeszły piątek","relative-type-0":"w ten piątek","relative-type-1":"w przyszły piątek","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} piątek","relativeTimePattern-count-few":"za {0} piątki","relativeTimePattern-count-many":"za {0} piątków","relativeTimePattern-count-other":"za {0} piątku"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} piątek temu","relativeTimePattern-count-few":"{0} piątki temu","relativeTimePattern-count-many":"{0} piątków temu","relativeTimePattern-count-other":"{0} piątku temu"}},sat:{"relative-type--1":"w zeszłą sobotę","relative-type-0":"w tę sobotę","relative-type-1":"w przyszłą sobotę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} sobotę","relativeTimePattern-count-few":"za {0} soboty","relativeTimePattern-count-many":"za {0} sobót","relativeTimePattern-count-other":"za {0} soboty"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sobotę temu","relativeTimePattern-count-few":"{0} soboty temu","relativeTimePattern-count-many":"{0} sobót temu","relativeTimePattern-count-other":"{0} soboty temu"}},"sat-short":{"relative-type--1":"w zeszłą sobotę","relative-type-0":"w tę sobotę","relative-type-1":"w przyszłą sobotę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} sobotę","relativeTimePattern-count-few":"za {0} soboty","relativeTimePattern-count-many":"za {0} sobót","relativeTimePattern-count-other":"za {0} soboty"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sobotę temu","relativeTimePattern-count-few":"{0} soboty temu","relativeTimePattern-count-many":"{0} sobót temu","relativeTimePattern-count-other":"{0} soboty temu"}},"sat-narrow":{"relative-type--1":"w zeszłą sobotę","relative-type-0":"w tę sobotę","relative-type-1":"w przyszłą sobotę","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} sobotę","relativeTimePattern-count-few":"za {0} soboty","relativeTimePattern-count-many":"za {0} sobót","relativeTimePattern-count-other":"za {0} soboty"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sobotę temu","relativeTimePattern-count-few":"{0} soboty temu","relativeTimePattern-count-many":"{0} sobót temu","relativeTimePattern-count-other":"{0} soboty temu"}},"dayperiod-short":{displayName:"rano / po południu / wieczorem"},dayperiod:{displayName:"rano / po południu / wieczorem"},"dayperiod-narrow":{displayName:"rano / po poł. / wiecz."},hour:{displayName:"godzina","relative-type-0":"ta godzina","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} godzinę","relativeTimePattern-count-few":"za {0} godziny","relativeTimePattern-count-many":"za {0} godzin","relativeTimePattern-count-other":"za {0} godziny"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} godzinę temu","relativeTimePattern-count-few":"{0} godziny temu","relativeTimePattern-count-many":"{0} godzin temu","relativeTimePattern-count-other":"{0} godziny temu"}},"hour-short":{displayName:"godz.","relative-type-0":"ta godzina","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} godz.","relativeTimePattern-count-few":"za {0} godz.","relativeTimePattern-count-many":"za {0} godz.","relativeTimePattern-count-other":"za {0} godz."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} godz. temu","relativeTimePattern-count-few":"{0} godz. temu","relativeTimePattern-count-many":"{0} godz. temu","relativeTimePattern-count-other":"{0} godz. temu"}},"hour-narrow":{displayName:"g.","relative-type-0":"ta godzina","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} g.","relativeTimePattern-count-few":"za {0} g.","relativeTimePattern-count-many":"za {0} g.","relativeTimePattern-count-other":"za {0} g."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} g. temu","relativeTimePattern-count-few":"{0} g. temu","relativeTimePattern-count-many":"{0} g. temu","relativeTimePattern-count-other":"{0} g. temu"}},minute:{displayName:"minuta","relative-type-0":"ta minuta","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} minutę","relativeTimePattern-count-few":"za {0} minuty","relativeTimePattern-count-many":"za {0} minut","relativeTimePattern-count-other":"za {0} minuty"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} minutę temu","relativeTimePattern-count-few":"{0} minuty temu","relativeTimePattern-count-many":"{0} minut temu","relativeTimePattern-count-other":"{0} minuty temu"}},"minute-short":{displayName:"min","relative-type-0":"ta minuta","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} min","relativeTimePattern-count-few":"za {0} min","relativeTimePattern-count-many":"za {0} min","relativeTimePattern-count-other":"za {0} min"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} min temu","relativeTimePattern-count-few":"{0} min temu","relativeTimePattern-count-many":"{0} min temu","relativeTimePattern-count-other":"{0} min temu"}},"minute-narrow":{displayName:"min","relative-type-0":"ta minuta","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} min","relativeTimePattern-count-few":"za {0} min","relativeTimePattern-count-many":"za {0} min","relativeTimePattern-count-other":"za {0} min"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} min temu","relativeTimePattern-count-few":"{0} min temu","relativeTimePattern-count-many":"{0} min temu","relativeTimePattern-count-other":"{0} min temu"}},second:{displayName:"sekunda","relative-type-0":"teraz","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} sekundę","relativeTimePattern-count-few":"za {0} sekundy","relativeTimePattern-count-many":"za {0} sekund","relativeTimePattern-count-other":"za {0} sekundy"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sekundę temu","relativeTimePattern-count-few":"{0} sekundy temu","relativeTimePattern-count-many":"{0} sekund temu","relativeTimePattern-count-other":"{0} sekundy temu"}},"second-short":{displayName:"sek.","relative-type-0":"teraz","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} sek.","relativeTimePattern-count-few":"za {0} sek.","relativeTimePattern-count-many":"za {0} sek.","relativeTimePattern-count-other":"za {0} sek."},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} sek. temu","relativeTimePattern-count-few":"{0} sek. temu","relativeTimePattern-count-many":"{0} sek. temu","relativeTimePattern-count-other":"{0} sek. temu"}},"second-narrow":{displayName:"s","relative-type-0":"teraz","relativeTime-type-future":{"relativeTimePattern-count-one":"za {0} s","relativeTimePattern-count-few":"za {0} s","relativeTimePattern-count-many":"za {0} s","relativeTimePattern-count-other":"za {0} s"},"relativeTime-type-past":{"relativeTimePattern-count-one":"{0} s temu","relativeTimePattern-count-few":"{0} s temu","relativeTimePattern-count-many":"{0} s temu","relativeTimePattern-count-other":"{0} s temu"}},zone:{displayName:"strefa czasowa"},"zone-short":{displayName:"str. czasowa"},"zone-narrow":{displayName:"str. czas."}}}}}},{supplemental:{version:{_unicodeVersion:"12.1.0",_cldrVersion:"36"},"plurals-type-cardinal":{af:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ak:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},am:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},an:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ar:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ars:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n % 100 = 3..10 @integer 3~10, 103~110, 1003, … @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 103.0, 1003.0, …","pluralRule-count-many":"n % 100 = 11..99 @integer 11~26, 111, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 100~102, 200~202, 300~302, 400~402, 500~502, 600, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},as:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},asa:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ast:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},az:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},be:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..4 and n % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 2.0, 3.0, 4.0, 22.0, 23.0, 24.0, 32.0, 33.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 10 = 0 or n % 10 = 5..9 or n % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":"   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …"},bem:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bez:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bho:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bm:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},br:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11,71,91 @integer 1, 21, 31, 41, 51, 61, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 81.0, 101.0, 1001.0, …","pluralRule-count-two":"n % 10 = 2 and n % 100 != 12,72,92 @integer 2, 22, 32, 42, 52, 62, 82, 102, 1002, … @decimal 2.0, 22.0, 32.0, 42.0, 52.0, 62.0, 82.0, 102.0, 1002.0, …","pluralRule-count-few":"n % 10 = 3..4,9 and n % 100 != 10..19,70..79,90..99 @integer 3, 4, 9, 23, 24, 29, 33, 34, 39, 43, 44, 49, 103, 1003, … @decimal 3.0, 4.0, 9.0, 23.0, 24.0, 29.0, 33.0, 34.0, 103.0, 1003.0, …","pluralRule-count-many":"n != 0 and n % 1000000 = 0 @integer 1000000, … @decimal 1000000.0, 1000000.00, 1000000.000, …","pluralRule-count-other":" @integer 0, 5~8, 10~20, 100, 1000, 10000, 100000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, …"},brx:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},bs:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ca:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ce:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ceb:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},cgg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},chr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ckb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},cs:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},cy:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3 @integer 3 @decimal 3.0, 3.00, 3.000, 3.0000","pluralRule-count-many":"n = 6 @integer 6 @decimal 6.0, 6.00, 6.000, 6.0000","pluralRule-count-other":" @integer 4, 5, 7~20, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},da:{"pluralRule-count-one":"n = 1 or t != 0 and i = 0,1 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0~3.4, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},de:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dv:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},dz:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ee:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},el:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},en:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},es:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},et:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},eu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fa:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ff:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fil:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},fo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fr:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fur:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},fy:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ga:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-few":"n = 3..6 @integer 3~6 @decimal 3.0, 4.0, 5.0, 6.0, 3.00, 4.00, 5.00, 6.00, 3.000, 4.000, 5.000, 6.000, 3.0000, 4.0000, 5.0000, 6.0000","pluralRule-count-many":"n = 7..10 @integer 7~10 @decimal 7.0, 8.0, 9.0, 10.0, 7.00, 8.00, 9.00, 10.00, 7.000, 8.000, 9.000, 10.000, 7.0000, 8.0000, 9.0000, 10.0000","pluralRule-count-other":" @integer 0, 11~25, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gd:{"pluralRule-count-one":"n = 1,11 @integer 1, 11 @decimal 1.0, 11.0, 1.00, 11.00, 1.000, 11.000, 1.0000","pluralRule-count-two":"n = 2,12 @integer 2, 12 @decimal 2.0, 12.0, 2.00, 12.00, 2.000, 12.000, 2.0000","pluralRule-count-few":"n = 3..10,13..19 @integer 3~10, 13~19 @decimal 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 3.00","pluralRule-count-other":" @integer 0, 20~34, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gsw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},guw:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},gv:{"pluralRule-count-one":"v = 0 and i % 10 = 1 @integer 1, 11, 21, 31, 41, 51, 61, 71, 101, 1001, …","pluralRule-count-two":"v = 0 and i % 10 = 2 @integer 2, 12, 22, 32, 42, 52, 62, 72, 102, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 0,20,40,60,80 @integer 0, 20, 40, 60, 80, 100, 120, 140, 1000, 10000, 100000, 1000000, …","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 3~10, 13~19, 23, 103, 1003, …"},ha:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},haw:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},he:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hsb:{"pluralRule-count-one":"v = 0 and i % 100 = 1 or f % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-two":"v = 0 and i % 100 = 2 or f % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, … @decimal 0.2, 1.2, 2.2, 3.2, 4.2, 5.2, 6.2, 7.2, 10.2, 100.2, 1000.2, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or f % 100 = 3..4 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.3, 0.4, 1.3, 1.4, 2.3, 2.4, 3.3, 3.4, 4.3, 4.4, 5.3, 5.4, 6.3, 6.4, 7.3, 7.4, 10.3, 100.3, 1000.3, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},hy:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ia:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},id:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ig:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ii:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},in:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},io:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},is:{"pluralRule-count-one":"t = 0 and i % 10 = 1 and i % 100 != 11 or t != 0 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1~1.6, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},it:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iu:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},iw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-two":"i = 2 and v = 0 @integer 2","pluralRule-count-many":"v = 0 and n != 0..10 and n % 10 = 0 @integer 20, 30, 40, 50, 60, 70, 80, 90, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":" @integer 0, 3~17, 101, 1001, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ja:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jbo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ji:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jmc:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jv:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},jw:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ka:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kab:{"pluralRule-count-one":"i = 0,1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kaj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kcg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kde:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kea:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kkj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kl:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},km:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kn:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ko:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ks:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ksh:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ku:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},kw:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n % 100 = 2,22,42,62,82 or n % 1000 = 0 and n % 100000 = 1000..20000,40000,60000,80000 or n != 0 and n % 1000000 = 100000 @integer 2, 22, 42, 62, 82, 102, 122, 142, 1000, 10000, 100000, … @decimal 2.0, 22.0, 42.0, 62.0, 82.0, 102.0, 122.0, 142.0, 1000.0, 10000.0, 100000.0, …","pluralRule-count-few":"n % 100 = 3,23,43,63,83 @integer 3, 23, 43, 63, 83, 103, 123, 143, 1003, … @decimal 3.0, 23.0, 43.0, 63.0, 83.0, 103.0, 123.0, 143.0, 1003.0, …","pluralRule-count-many":"n != 1 and n % 100 = 1,21,41,61,81 @integer 21, 41, 61, 81, 101, 121, 141, 161, 1001, … @decimal 21.0, 41.0, 61.0, 81.0, 101.0, 121.0, 141.0, 161.0, 1001.0, …","pluralRule-count-other":" @integer 4~19, 100, 1004, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.1, 1000000.0, …"},ky:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lag:{"pluralRule-count-zero":"n = 0 @integer 0 @decimal 0.0, 0.00, 0.000, 0.0000","pluralRule-count-one":"i = 0,1 and n != 0 @integer 1 @decimal 0.1~1.6","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lg:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lkt:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ln:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lt:{"pluralRule-count-one":"n % 10 = 1 and n % 100 != 11..19 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 1.0, 21.0, 31.0, 41.0, 51.0, 61.0, 71.0, 81.0, 101.0, 1001.0, …","pluralRule-count-few":"n % 10 = 2..9 and n % 100 != 11..19 @integer 2~9, 22~29, 102, 1002, … @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 22.0, 102.0, 1002.0, …","pluralRule-count-many":"f != 0   @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},lv:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},mas:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mg:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mgo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.2~1.0, 1.2~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ml:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mo:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},mr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ms:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},mt:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-few":"n = 0 or n % 100 = 2..10 @integer 0, 2~10, 102~107, 1002, … @decimal 0.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 10.0, 102.0, 1002.0, …","pluralRule-count-many":"n % 100 = 11..19 @integer 11~19, 111~117, 1011, … @decimal 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 111.0, 1011.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},my:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nah:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},naq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nb:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ne:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nnh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},no:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nqo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nso:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ny:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},nyn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},om:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},or:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},os:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},osa:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pap:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pl:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i != 1 and i % 10 = 0..1 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 12..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},prg:{"pluralRule-count-zero":"n % 10 = 0 or n % 100 = 11..19 or v = 2 and f % 100 = 11..19 @integer 0, 10~20, 30, 40, 50, 60, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-one":"n % 10 = 1 and n % 100 != 11 or v = 2 and f % 10 = 1 and f % 100 != 11 or v != 2 and f % 10 = 1 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.0, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-other":" @integer 2~9, 22~29, 102, 1002, … @decimal 0.2~0.9, 1.2~1.9, 10.2, 100.2, 1000.2, …"},ps:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},pt:{"pluralRule-count-one":"i = 0..1 @integer 0, 1 @decimal 0.0~1.5","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 2.0~3.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},"pt-PT":{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rm:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ro:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"v != 0 or n = 0 or n % 100 = 2..19 @integer 0, 2~16, 102, 1002, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 20~35, 100, 1000, 10000, 100000, 1000000, …"},rof:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},root:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ru:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},rwk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sah:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},saq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sc:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},scn:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sd:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sdh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},se:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},seh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ses:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sg:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sh:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},shi:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-few":"n = 2..10 @integer 2~10 @decimal 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 2.00, 3.00, 4.00, 5.00, 6.00, 7.00, 8.00","pluralRule-count-other":" @integer 11~26, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~1.9, 2.1~2.7, 10.1, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},si:{"pluralRule-count-one":"n = 0,1 or i = 0 and f = 1 @integer 0, 1 @decimal 0.0, 0.1, 1.0, 0.00, 0.01, 1.00, 0.000, 0.001, 1.000, 0.0000, 0.0001, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.2~0.9, 1.1~1.8, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sk:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-few":"i = 2..4 and v = 0 @integer 2~4","pluralRule-count-many":"v != 0   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sl:{"pluralRule-count-one":"v = 0 and i % 100 = 1 @integer 1, 101, 201, 301, 401, 501, 601, 701, 1001, …","pluralRule-count-two":"v = 0 and i % 100 = 2 @integer 2, 102, 202, 302, 402, 502, 602, 702, 1002, …","pluralRule-count-few":"v = 0 and i % 100 = 3..4 or v != 0 @integer 3, 4, 103, 104, 203, 204, 303, 304, 403, 404, 503, 504, 603, 604, 703, 704, 1003, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …"},sma:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smi:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smj:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},smn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sms:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-two":"n = 2 @integer 2 @decimal 2.0, 2.00, 2.000, 2.0000","pluralRule-count-other":" @integer 0, 3~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},so:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sq:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sr:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 or f % 10 = 1 and f % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, … @decimal 0.1, 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 10.1, 100.1, 1000.1, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 or f % 10 = 2..4 and f % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, … @decimal 0.2~0.4, 1.2~1.4, 2.2~2.4, 3.2~3.4, 4.2~4.4, 5.2, 10.2, 100.2, 1000.2, …","pluralRule-count-other":" @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0, 0.5~1.0, 1.5~2.0, 2.5~2.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ss:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ssy:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},st:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},su:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sv:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},sw:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},syr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ta:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},te:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},teo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},th:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ti:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tig:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tk:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tl:{"pluralRule-count-one":"v = 0 and i = 1,2,3 or v = 0 and i % 10 != 4,6,9 or v != 0 and f % 10 != 4,6,9 @integer 0~3, 5, 7, 8, 10~13, 15, 17, 18, 20, 21, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.3, 0.5, 0.7, 0.8, 1.0~1.3, 1.5, 1.7, 1.8, 2.0, 2.1, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …","pluralRule-count-other":" @integer 4, 6, 9, 14, 16, 19, 24, 26, 104, 1004, … @decimal 0.4, 0.6, 0.9, 1.4, 1.6, 1.9, 2.4, 2.6, 10.4, 100.4, 1000.4, …"},tn:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},to:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tr:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ts:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},tzm:{"pluralRule-count-one":"n = 0..1 or n = 11..99 @integer 0, 1, 11~24 @decimal 0.0, 1.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0","pluralRule-count-other":" @integer 2~10, 100~106, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ug:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uk:{"pluralRule-count-one":"v = 0 and i % 10 = 1 and i % 100 != 11 @integer 1, 21, 31, 41, 51, 61, 71, 81, 101, 1001, …","pluralRule-count-few":"v = 0 and i % 10 = 2..4 and i % 100 != 12..14 @integer 2~4, 22~24, 32~34, 42~44, 52~54, 62, 102, 1002, …","pluralRule-count-many":"v = 0 and i % 10 = 0 or v = 0 and i % 10 = 5..9 or v = 0 and i % 100 = 11..14 @integer 0, 5~19, 100, 1000, 10000, 100000, 1000000, …","pluralRule-count-other":"   @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ur:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},uz:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},ve:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vi:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vo:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},vun:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wa:{"pluralRule-count-one":"n = 0..1 @integer 0, 1 @decimal 0.0, 1.0, 0.00, 1.00, 0.000, 1.000, 0.0000, 1.0000","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 0.1~0.9, 1.1~1.7, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wae:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},wo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xh:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},xog:{"pluralRule-count-one":"n = 1 @integer 1 @decimal 1.0, 1.00, 1.000, 1.0000","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~0.9, 1.1~1.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yi:{"pluralRule-count-one":"i = 1 and v = 0 @integer 1","pluralRule-count-other":" @integer 0, 2~16, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yo:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},yue:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zh:{"pluralRule-count-other":" @integer 0~15, 100, 1000, 10000, 100000, 1000000, … @decimal 0.0~1.5, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"},zu:{"pluralRule-count-one":"i = 0 or n = 1 @integer 0, 1 @decimal 0.0~1.0, 0.00~0.04","pluralRule-count-other":" @integer 2~17, 100, 1000, 10000, 100000, 1000000, … @decimal 1.1~2.6, 10.0, 100.0, 1000.0, 10000.0, 100000.0, 1000000.0, …"}}}},{main:{pl:{identity:{version:{_cldrVersion:"36"},language:"pl"},units:{long:{per:{compoundUnitPattern:"{0} na {1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"stała grawitacji","unitPattern-count-other":"{0} G"},"acceleration-meter-per-second-squared":{displayName:"metry na sekundę do kwadratu","unitPattern-count-one":"{0} metr na sekundę do kwadratu","unitPattern-count-few":"{0} metry na sekundę do kwadratu","unitPattern-count-many":"{0} metrów na sekundę do kwadratu","unitPattern-count-other":"{0} metra na sekundę do kwadratu"},"angle-revolution":{displayName:"obrót","unitPattern-count-one":"{0} obrót","unitPattern-count-few":"{0} obroty","unitPattern-count-many":"{0} obrotów","unitPattern-count-other":"{0} obrotu"},"angle-radian":{displayName:"radiany","unitPattern-count-one":"{0} radian","unitPattern-count-few":"{0} radiany","unitPattern-count-many":"{0} radianów","unitPattern-count-other":"{0} radiana"},"angle-degree":{displayName:"stopnie","unitPattern-count-one":"{0} stopień","unitPattern-count-few":"{0} stopnie","unitPattern-count-many":"{0} stopni","unitPattern-count-other":"{0} stopnia"},"angle-arc-minute":{displayName:"minuty kątowe","unitPattern-count-one":"{0} minuta kątowa","unitPattern-count-few":"{0} minuty kątowe","unitPattern-count-many":"{0} minut kątowych","unitPattern-count-other":"{0} minuty kątowej"},"angle-arc-second":{displayName:"sekundy kątowe","unitPattern-count-one":"{0} sekunda kątowa","unitPattern-count-few":"{0} sekundy kątowe","unitPattern-count-many":"{0} sekund kątowych","unitPattern-count-other":"{0} sekundy kątowej"},"area-square-kilometer":{displayName:"kilometry kwadratowe","unitPattern-count-one":"{0} kilometr kwadratowy","unitPattern-count-few":"{0} kilometry kwadratowe","unitPattern-count-many":"{0} kilometrów kwadratowych","unitPattern-count-other":"{0} kilometra kwadratowego",perUnitPattern:"{0} na kilometr kwadratowy"},"area-hectare":{displayName:"hektary","unitPattern-count-one":"{0} hektar","unitPattern-count-few":"{0} hektary","unitPattern-count-many":"{0} hektarów","unitPattern-count-other":"{0} hektara"},"area-square-meter":{displayName:"metry kwadratowe","unitPattern-count-one":"{0} metr kwadratowy","unitPattern-count-few":"{0} metry kwadratowe","unitPattern-count-many":"{0} metrów kwadratowych","unitPattern-count-other":"{0} metra kwadratowego",perUnitPattern:"{0} na metr kwadratowy"},"area-square-centimeter":{displayName:"centymetry kwadratowe","unitPattern-count-one":"{0} centymetr kwadratowy","unitPattern-count-few":"{0} centymetry kwadratowe","unitPattern-count-many":"{0} centymetrów kwadratowych","unitPattern-count-other":"{0} centymetra kwadratowego",perUnitPattern:"{0} na centymetr kwadratowy"},"area-square-mile":{displayName:"mile kwadratowe","unitPattern-count-one":"{0} mila kwadratowa","unitPattern-count-few":"{0} mile kwadratowe","unitPattern-count-many":"{0} mil kwadratowych","unitPattern-count-other":"{0} mili kwadratowej",perUnitPattern:"{0} na milę kwadratową"},"area-acre":{displayName:"akry","unitPattern-count-one":"{0} akr","unitPattern-count-few":"{0} akry","unitPattern-count-many":"{0} akrów","unitPattern-count-other":"{0} akra"},"area-square-yard":{displayName:"jardy kwadratowe","unitPattern-count-one":"{0} jard kwadratowy","unitPattern-count-few":"{0} jardy kwadratowe","unitPattern-count-many":"{0} jardów kwadratowych","unitPattern-count-other":"{0} jarda kwadratowego"},"area-square-foot":{displayName:"stopy kwadratowe","unitPattern-count-one":"{0} stopa kwadratowa","unitPattern-count-few":"{0} stopy kwadratowe","unitPattern-count-many":"{0} stóp kwadratowych","unitPattern-count-other":"{0} stopy kwadratowej"},"area-square-inch":{displayName:"cale kwadratowe","unitPattern-count-one":"{0} cal kwadratowy","unitPattern-count-few":"{0} cale kwadratowe","unitPattern-count-many":"{0} cali kwadratowych","unitPattern-count-other":"{0} cala kwadratowego",perUnitPattern:"{0} na cal kwadratowy"},"area-dunam":{displayName:"dunamy","unitPattern-count-one":"{0} dunam","unitPattern-count-few":"{0} dunamy","unitPattern-count-many":"{0} dunamów","unitPattern-count-other":"{0} dunama"},"concentr-karat":{displayName:"karaty","unitPattern-count-one":"{0} karat","unitPattern-count-few":"{0} karaty","unitPattern-count-many":"{0} karatów","unitPattern-count-other":"{0} karata"},"concentr-milligram-per-deciliter":{displayName:"miligramy na decylitr","unitPattern-count-one":"{0} miligram na decylitr","unitPattern-count-few":"{0} miligramy na decylitr","unitPattern-count-many":"{0} miligramów na decylitr","unitPattern-count-other":"{0} miligrama na decylitr"},"concentr-millimole-per-liter":{displayName:"milimole na litr","unitPattern-count-one":"{0} milimol na litr","unitPattern-count-few":"{0} milimole na litr","unitPattern-count-many":"{0} milimoli na litr","unitPattern-count-other":"{0} milimola na litr"},"concentr-part-per-million":{displayName:"części na milion","unitPattern-count-one":"{0} część na milion","unitPattern-count-few":"{0} części na milion","unitPattern-count-many":"{0} części na milion","unitPattern-count-other":"{0} części na milion"},"concentr-percent":{displayName:"procent","unitPattern-count-one":"{0} procent","unitPattern-count-few":"{0} procent","unitPattern-count-many":"{0} procent","unitPattern-count-other":"{0} procent"},"concentr-permille":{displayName:"promil","unitPattern-count-one":"{0} promil","unitPattern-count-few":"{0} promile","unitPattern-count-many":"{0} promili","unitPattern-count-other":"{0} promila"},"concentr-permyriad":{displayName:"punkt bazowy","unitPattern-count-one":"{0} punkt bazowy","unitPattern-count-few":"{0} punkty bazowe","unitPattern-count-many":"{0} punktów bazowych","unitPattern-count-other":"{0} punktu bazowego"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-few":"{0} mole","unitPattern-count-many":"{0} moli","unitPattern-count-other":"{0} mola"},"consumption-liter-per-kilometer":{displayName:"litry na kilometr","unitPattern-count-one":"{0} litr na kilometr","unitPattern-count-few":"{0} litry na kilometr","unitPattern-count-many":"{0} litrów na kilometr","unitPattern-count-other":"{0} litra na kilometr"},"consumption-liter-per-100kilometers":{displayName:"litry na 100 kilometrów","unitPattern-count-one":"{0} litr na 100 kilometrów","unitPattern-count-few":"{0} litry na 100 kilometrów","unitPattern-count-many":"{0} litrów na 100 kilometrów","unitPattern-count-other":"{0} litra na 100 kilometrów"},"consumption-mile-per-gallon":{displayName:"mile na galon","unitPattern-count-one":"{0} mila na galon","unitPattern-count-few":"{0} mile na galon","unitPattern-count-many":"{0} mili na galon","unitPattern-count-other":"{0} mili na galon"},"consumption-mile-per-gallon-imperial":{displayName:"mile na galon angielski","unitPattern-count-one":"{0} mila na galon angielski","unitPattern-count-few":"{0} mile na galon angielski","unitPattern-count-many":"{0} mil na galon angielski","unitPattern-count-other":"{0} mili na galon angielski"},"digital-petabyte":{displayName:"petabajty","unitPattern-count-one":"{0} petabajt","unitPattern-count-few":"{0} petabajty","unitPattern-count-many":"{0} petabajtów","unitPattern-count-other":"{0} petabajta"},"digital-terabyte":{displayName:"terabajty","unitPattern-count-one":"{0} terabajt","unitPattern-count-few":"{0} terabajty","unitPattern-count-many":"{0} terabajtów","unitPattern-count-other":"{0} terabajta"},"digital-terabit":{displayName:"terabity","unitPattern-count-one":"{0} terabit","unitPattern-count-few":"{0} terabity","unitPattern-count-many":"{0} terabitów","unitPattern-count-other":"{0} terabitu"},"digital-gigabyte":{displayName:"gigabajty","unitPattern-count-one":"{0} gigabajt","unitPattern-count-few":"{0} gigabajty","unitPattern-count-many":"{0} gigabajtów","unitPattern-count-other":"{0} gigabajta"},"digital-gigabit":{displayName:"gigabity","unitPattern-count-one":"{0} gigabit","unitPattern-count-few":"{0} gigabity","unitPattern-count-many":"{0} gigabitów","unitPattern-count-other":"{0} gigabitu"},"digital-megabyte":{displayName:"megabajty","unitPattern-count-one":"{0} megabajt","unitPattern-count-few":"{0} megabajty","unitPattern-count-many":"{0} megabajtów","unitPattern-count-other":"{0} megabajta"},"digital-megabit":{displayName:"megabity","unitPattern-count-one":"{0} megabit","unitPattern-count-few":"{0} megabity","unitPattern-count-many":"{0} megabitów","unitPattern-count-other":"{0} megabitu"},"digital-kilobyte":{displayName:"kilobajty","unitPattern-count-one":"{0} kilobajt","unitPattern-count-few":"{0} kilobajty","unitPattern-count-many":"{0} kilobajtów","unitPattern-count-other":"{0} kilobajta"},"digital-kilobit":{displayName:"kilobity","unitPattern-count-one":"{0} kilobit","unitPattern-count-few":"{0} kilobity","unitPattern-count-many":"{0} kilobitów","unitPattern-count-other":"{0} kilobitu"},"digital-byte":{displayName:"bajty","unitPattern-count-one":"{0} bajt","unitPattern-count-few":"{0} bajty","unitPattern-count-many":"{0} bajtów","unitPattern-count-other":"{0} bajta"},"digital-bit":{displayName:"bity","unitPattern-count-one":"{0} bit","unitPattern-count-few":"{0} bity","unitPattern-count-many":"{0} bitów","unitPattern-count-other":"{0} bitu"},"duration-century":{displayName:"wieki","unitPattern-count-one":"{0} wiek","unitPattern-count-few":"{0} wieki","unitPattern-count-many":"{0} wieków","unitPattern-count-other":"{0} wieku"},"duration-decade":{displayName:"dekady","unitPattern-count-one":"{0} dekada","unitPattern-count-few":"{0} dekady","unitPattern-count-many":"{0} dekad","unitPattern-count-other":"{0} dekady"},"duration-year":{displayName:"lata","unitPattern-count-one":"{0} rok","unitPattern-count-few":"{0} lata","unitPattern-count-many":"{0} lat","unitPattern-count-other":"{0} roku",perUnitPattern:"{0} na rok"},"duration-month":{displayName:"miesiące","unitPattern-count-one":"{0} miesiąc","unitPattern-count-few":"{0} miesiące","unitPattern-count-many":"{0} miesięcy","unitPattern-count-other":"{0} miesiąca",perUnitPattern:"{0} na miesiąc"},"duration-week":{displayName:"tygodnie","unitPattern-count-one":"{0} tydzień","unitPattern-count-few":"{0} tygodnie","unitPattern-count-many":"{0} tygodni","unitPattern-count-other":"{0} tygodnia",perUnitPattern:"{0} na tydzień"},"duration-day":{displayName:"dni","unitPattern-count-one":"{0} dzień","unitPattern-count-few":"{0} dni","unitPattern-count-many":"{0} dni","unitPattern-count-other":"{0} dnia",perUnitPattern:"{0} na dzień"},"duration-hour":{displayName:"godziny","unitPattern-count-one":"{0} godzina","unitPattern-count-few":"{0} godziny","unitPattern-count-many":"{0} godzin","unitPattern-count-other":"{0} godziny",perUnitPattern:"{0} na godzinę"},"duration-minute":{displayName:"minuty","unitPattern-count-one":"{0} minuta","unitPattern-count-few":"{0} minuty","unitPattern-count-many":"{0} minut","unitPattern-count-other":"{0} minuty",perUnitPattern:"{0} na minutę"},"duration-second":{displayName:"sekundy","unitPattern-count-one":"{0} sekunda","unitPattern-count-few":"{0} sekundy","unitPattern-count-many":"{0} sekund","unitPattern-count-other":"{0} sekundy",perUnitPattern:"{0} na sekundę"},"duration-millisecond":{displayName:"milisekundy","unitPattern-count-one":"{0} milisekunda","unitPattern-count-few":"{0} milisekundy","unitPattern-count-many":"{0} milisekund","unitPattern-count-other":"{0} milisekundy"},"duration-microsecond":{displayName:"mikrosekundy","unitPattern-count-one":"{0} mikrosekunda","unitPattern-count-few":"{0} mikrosekundy","unitPattern-count-many":"{0} mikrosekund","unitPattern-count-other":"{0} mikrosekundy"},"duration-nanosecond":{displayName:"nanosekundy","unitPattern-count-one":"{0} nanosekunda","unitPattern-count-few":"{0} nanosekundy","unitPattern-count-many":"{0} nanosekund","unitPattern-count-other":"{0} nanosekundy"},"electric-ampere":{displayName:"ampery","unitPattern-count-one":"{0} amper","unitPattern-count-few":"{0} ampery","unitPattern-count-many":"{0} amperów","unitPattern-count-other":"{0} ampera"},"electric-milliampere":{displayName:"miliampery","unitPattern-count-one":"{0} miliamper","unitPattern-count-few":"{0} miliampery","unitPattern-count-many":"{0} miliamperów","unitPattern-count-other":"{0} miliampera"},"electric-ohm":{displayName:"omy","unitPattern-count-one":"{0} om","unitPattern-count-few":"{0} omy","unitPattern-count-many":"{0} omów","unitPattern-count-other":"{0} oma"},"electric-volt":{displayName:"wolty","unitPattern-count-one":"{0} wolt","unitPattern-count-few":"{0} wolty","unitPattern-count-many":"{0} woltów","unitPattern-count-other":"{0} wolta"},"energy-kilocalorie":{displayName:"kilokalorie","unitPattern-count-one":"{0} kilokaloria","unitPattern-count-few":"{0} kilokalorie","unitPattern-count-many":"{0} kilokalorii","unitPattern-count-other":"{0} kilokalorii"},"energy-calorie":{displayName:"kalorie","unitPattern-count-one":"{0} kaloria","unitPattern-count-few":"{0} kalorie","unitPattern-count-many":"{0} kalorii","unitPattern-count-other":"{0} kalorii"},"energy-foodcalorie":{displayName:"kalorie","unitPattern-count-one":"{0} kaloria","unitPattern-count-few":"{0} kalorie","unitPattern-count-many":"{0} kalorii","unitPattern-count-other":"{0} kalorii"},"energy-kilojoule":{displayName:"kilodżule","unitPattern-count-one":"{0} kilodżul","unitPattern-count-few":"{0} kilodżule","unitPattern-count-many":"{0} kilodżuli","unitPattern-count-other":"{0} kilodżula"},"energy-joule":{displayName:"dżule","unitPattern-count-one":"{0} dżul","unitPattern-count-few":"{0} dżule","unitPattern-count-many":"{0} dżuli","unitPattern-count-other":"{0} dżula"},"energy-kilowatt-hour":{displayName:"kilowatogodziny","unitPattern-count-one":"{0} kilowatogodzina","unitPattern-count-few":"{0} kilowatogodziny","unitPattern-count-many":"{0} kilowatogodzin","unitPattern-count-other":"{0} kilowatogodziny"},"energy-electronvolt":{displayName:"elektronowolty","unitPattern-count-one":"{0} elektronowolt","unitPattern-count-few":"{0} elektronowolty","unitPattern-count-many":"{0} elektronowoltów","unitPattern-count-other":"{0} elektronowolta"},"energy-british-thermal-unit":{displayName:"brytyjska jednostka ciepła","unitPattern-count-one":"{0} brytyjska jednostka ciepła","unitPattern-count-few":"{0} brytyjskie jednostki ciepła","unitPattern-count-many":"{0} brytyjskich jednostek ciepła","unitPattern-count-other":"{0} brytyjskiej jednostki ciepła"},"energy-therm-us":{displayName:"thermy amerykańskie","unitPattern-count-one":"{0} therm amerykański","unitPattern-count-few":"{0} thermy amerykańskie","unitPattern-count-many":"{0} thermów amerykańskich","unitPattern-count-other":"{0} therma amerykańskiego"},"force-pound-force":{displayName:"funt-siła","unitPattern-count-one":"{0} funt-siła","unitPattern-count-few":"{0} funty-siły","unitPattern-count-many":"{0} funtów-siły","unitPattern-count-other":"{0} funta-siły"},"force-newton":{displayName:"niutony","unitPattern-count-one":"{0} niuton","unitPattern-count-few":"{0} niutony","unitPattern-count-many":"{0} niutonów","unitPattern-count-other":"{0} niutona"},"frequency-gigahertz":{displayName:"gigaherce","unitPattern-count-one":"{0} gigaherc","unitPattern-count-few":"{0} gigaherce","unitPattern-count-many":"{0} gigaherców","unitPattern-count-other":"{0} gigaherca"},"frequency-megahertz":{displayName:"megaherce","unitPattern-count-one":"{0} megaherc","unitPattern-count-few":"{0} megaherce","unitPattern-count-many":"{0} megaherców","unitPattern-count-other":"{0} megaherca"},"frequency-kilohertz":{displayName:"kiloherce","unitPattern-count-one":"{0} kiloherc","unitPattern-count-few":"{0} kiloherce","unitPattern-count-many":"{0} kiloherców","unitPattern-count-other":"{0} kiloherca"},"frequency-hertz":{displayName:"herce","unitPattern-count-one":"{0} herc","unitPattern-count-few":"{0} herce","unitPattern-count-many":"{0} herców","unitPattern-count-other":"{0} herca"},"graphics-em":{displayName:"firety","unitPattern-count-one":"{0} firet","unitPattern-count-few":"{0} firety","unitPattern-count-many":"{0} firetów","unitPattern-count-other":"{0} firetu"},"graphics-pixel":{displayName:"piksele","unitPattern-count-one":"{0} piksel","unitPattern-count-few":"{0} piksele","unitPattern-count-many":"{0} pikseli","unitPattern-count-other":"{0} piksela"},"graphics-megapixel":{displayName:"megapiksele","unitPattern-count-one":"{0} megapiksel","unitPattern-count-few":"{0} megapiksele","unitPattern-count-many":"{0} megapikseli","unitPattern-count-other":"{0} megapiksela"},"graphics-pixel-per-centimeter":{displayName:"ppcm","unitPattern-count-other":"{0} ppcm"},"graphics-pixel-per-inch":{displayName:"ppi","unitPattern-count-other":"{0} ppi"},"graphics-dot-per-centimeter":{displayName:"dpcm","unitPattern-count-other":"{0} dpcm"},"graphics-dot-per-inch":{displayName:"dpi","unitPattern-count-other":"{0} dpi"},"length-kilometer":{displayName:"kilometry","unitPattern-count-one":"{0} kilometr","unitPattern-count-few":"{0} kilometry","unitPattern-count-many":"{0} kilometrów","unitPattern-count-other":"{0} kilometra",perUnitPattern:"{0} na kilometr"},"length-meter":{displayName:"metry","unitPattern-count-one":"{0} metr","unitPattern-count-few":"{0} metry","unitPattern-count-many":"{0} metrów","unitPattern-count-other":"{0} metra",perUnitPattern:"{0} na metr"},"length-decimeter":{displayName:"decymetry","unitPattern-count-one":"{0} decymetr","unitPattern-count-few":"{0} decymetry","unitPattern-count-many":"{0} decymetrów","unitPattern-count-other":"{0} decymetra"},"length-centimeter":{displayName:"centymetry","unitPattern-count-one":"{0} centymetr","unitPattern-count-few":"{0} centymetry","unitPattern-count-many":"{0} centymetrów","unitPattern-count-other":"{0} centymetra",perUnitPattern:"{0} na centymetr"},"length-millimeter":{displayName:"milimetry","unitPattern-count-one":"{0} milimetr","unitPattern-count-few":"{0} milimetry","unitPattern-count-many":"{0} milimetrów","unitPattern-count-other":"{0} milimetra"},"length-micrometer":{displayName:"mikrometry","unitPattern-count-one":"{0} mikrometr","unitPattern-count-few":"{0} mikrometry","unitPattern-count-many":"{0} mikrometrów","unitPattern-count-other":"{0} mikrometra"},"length-nanometer":{displayName:"nanometry","unitPattern-count-one":"{0} nanometr","unitPattern-count-few":"{0} nanometry","unitPattern-count-many":"{0} nanometrów","unitPattern-count-other":"{0} nanometra"},"length-picometer":{displayName:"pikometry","unitPattern-count-one":"{0} pikometr","unitPattern-count-few":"{0} pikometry","unitPattern-count-many":"{0} pikometrów","unitPattern-count-other":"{0} pikometra"},"length-mile":{displayName:"mile","unitPattern-count-one":"{0} mila","unitPattern-count-few":"{0} mile","unitPattern-count-many":"{0} mil","unitPattern-count-other":"{0} mili"},"length-yard":{displayName:"jardy","unitPattern-count-one":"{0} jard","unitPattern-count-few":"{0} jardy","unitPattern-count-many":"{0} jardów","unitPattern-count-other":"{0} jarda"},"length-foot":{displayName:"stopy","unitPattern-count-one":"{0} stopa","unitPattern-count-few":"{0} stopy","unitPattern-count-many":"{0} stóp","unitPattern-count-other":"{0} stopy",perUnitPattern:"{0} na stopę"},"length-inch":{displayName:"cale","unitPattern-count-one":"{0} cal","unitPattern-count-few":"{0} cale","unitPattern-count-many":"{0} cali","unitPattern-count-other":"{0} cala",perUnitPattern:"{0} na cal"},"length-parsec":{displayName:"parseki","unitPattern-count-one":"{0} parsek","unitPattern-count-few":"{0} parseki","unitPattern-count-many":"{0} parseków","unitPattern-count-other":"{0} parseka"},"length-light-year":{displayName:"lata świetlne","unitPattern-count-one":"{0} rok świetlny","unitPattern-count-few":"{0} lata świetlne","unitPattern-count-many":"{0} lat świetlnych","unitPattern-count-other":"{0} roku świetlnego"},"length-astronomical-unit":{displayName:"jednostki astronomiczne","unitPattern-count-one":"{0} jednostka astronomiczna","unitPattern-count-few":"{0} jednostki astronomiczne","unitPattern-count-many":"{0} jednostek astronomicznych","unitPattern-count-other":"{0} jednostki astronomicznej"},"length-furlong":{displayName:"fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"mile morskie","unitPattern-count-one":"{0} mila morska","unitPattern-count-few":"{0} mile morskie","unitPattern-count-many":"{0} mil morskich","unitPattern-count-other":"{0} mili morskiej"},"length-mile-scandinavian":{displayName:"mila skandynawska","unitPattern-count-one":"{0} mila skandynawska","unitPattern-count-few":"{0} mile skandynawskie","unitPattern-count-many":"{0} mil skandynawskich","unitPattern-count-other":"{0} mili skandynawskiej"},"length-point":{displayName:"punkty","unitPattern-count-one":"{0} punkt","unitPattern-count-few":"{0} punkty","unitPattern-count-many":"{0} punktów","unitPattern-count-other":"{0} punktu"},"length-solar-radius":{displayName:"promienie Słońca","unitPattern-count-one":"{0} promień Słońca","unitPattern-count-few":"{0} promienie Słońca","unitPattern-count-many":"{0} promieni Słońca","unitPattern-count-other":"{0} promienia Słońca"},"light-lux":{displayName:"luksy","unitPattern-count-one":"{0} luks","unitPattern-count-few":"{0} luksy","unitPattern-count-many":"{0} luksów","unitPattern-count-other":"{0} luksu"},"light-solar-luminosity":{displayName:"jasności Słońca","unitPattern-count-one":"{0} jasność Słońca","unitPattern-count-few":"{0} jasności Słońca","unitPattern-count-many":"{0} jasności Słońca","unitPattern-count-other":"{0} jasności Słońca"},"mass-metric-ton":{displayName:"tony","unitPattern-count-one":"{0} tona","unitPattern-count-few":"{0} tony","unitPattern-count-many":"{0} ton","unitPattern-count-other":"{0} tony"},"mass-kilogram":{displayName:"kilogramy","unitPattern-count-one":"{0} kilogram","unitPattern-count-few":"{0} kilogramy","unitPattern-count-many":"{0} kilogramów","unitPattern-count-other":"{0} kilograma",perUnitPattern:"{0} na kilogram"},"mass-gram":{displayName:"gramy","unitPattern-count-one":"{0} gram","unitPattern-count-few":"{0} gramy","unitPattern-count-many":"{0} gramów","unitPattern-count-other":"{0} grama",perUnitPattern:"{0} na gram"},"mass-milligram":{displayName:"miligramy","unitPattern-count-one":"{0} miligram","unitPattern-count-few":"{0} miligramy","unitPattern-count-many":"{0} miligramów","unitPattern-count-other":"{0} miligrama"},"mass-microgram":{displayName:"mikrogramy","unitPattern-count-one":"{0} mikrogram","unitPattern-count-few":"{0} mikrogramy","unitPattern-count-many":"{0} mikrogramów","unitPattern-count-other":"{0} mikrograma"},"mass-ton":{displayName:"krótkie tony","unitPattern-count-one":"{0} krótka tona","unitPattern-count-few":"{0} krótkie tony","unitPattern-count-many":"{0} krótkich ton","unitPattern-count-other":"{0} krótkiej tony"},"mass-stone":{displayName:"st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"funty","unitPattern-count-one":"{0} funt","unitPattern-count-few":"{0} funty","unitPattern-count-many":"{0} funtów","unitPattern-count-other":"{0} funta",perUnitPattern:"{0} na funt"},"mass-ounce":{displayName:"uncje","unitPattern-count-one":"{0} uncja","unitPattern-count-few":"{0} uncje","unitPattern-count-many":"{0} uncji","unitPattern-count-other":"{0} uncji",perUnitPattern:"{0} na uncję"},"mass-ounce-troy":{displayName:"uncja trojańska","unitPattern-count-one":"{0} uncja trojańska","unitPattern-count-few":"{0} uncje trojańskie","unitPattern-count-many":"{0} uncji trojańskich","unitPattern-count-other":"{0} uncji trojańskiej"},"mass-carat":{displayName:"karaty","unitPattern-count-one":"{0} karat","unitPattern-count-few":"{0} karaty","unitPattern-count-many":"{0} karatów","unitPattern-count-other":"{0} karata"},"mass-dalton":{displayName:"daltony","unitPattern-count-one":"{0} dalton","unitPattern-count-few":"{0} daltony","unitPattern-count-many":"{0} daltonów","unitPattern-count-other":"{0} daltona"},"mass-earth-mass":{displayName:"masy Ziemi","unitPattern-count-one":"{0} masa Ziemi","unitPattern-count-few":"{0} masy Ziemi","unitPattern-count-many":"{0} mas Ziemi","unitPattern-count-other":"{0} masa Ziemi"},"mass-solar-mass":{displayName:"masy Słońca","unitPattern-count-one":"{0} masa Słońca","unitPattern-count-few":"{0} masy Słońca","unitPattern-count-many":"{0} mas Słońca","unitPattern-count-other":"{0} masy Słońca"},"power-gigawatt":{displayName:"gigawaty","unitPattern-count-one":"{0} gigawat","unitPattern-count-few":"{0} gigawaty","unitPattern-count-many":"{0} gigawatów","unitPattern-count-other":"{0} gigawata"},"power-megawatt":{displayName:"megawaty","unitPattern-count-one":"{0} megawat","unitPattern-count-few":"{0} megawaty","unitPattern-count-many":"{0} megawatów","unitPattern-count-other":"{0} megawata"},"power-kilowatt":{displayName:"kilowaty","unitPattern-count-one":"{0} kilowat","unitPattern-count-few":"{0} kilowaty","unitPattern-count-many":"{0} kilowatów","unitPattern-count-other":"{0} kilowata"},"power-watt":{displayName:"waty","unitPattern-count-one":"{0} wat","unitPattern-count-few":"{0} waty","unitPattern-count-many":"{0} watów","unitPattern-count-other":"{0} wata"},"power-milliwatt":{displayName:"miliwaty","unitPattern-count-one":"{0} miliwat","unitPattern-count-few":"{0} miliwaty","unitPattern-count-many":"{0} miliwatów","unitPattern-count-other":"{0} miliwata"},"power-horsepower":{displayName:"konie mechaniczne","unitPattern-count-one":"{0} koń mechaniczny","unitPattern-count-few":"{0} konie mechaniczne","unitPattern-count-many":"{0} koni mechanicznych","unitPattern-count-other":"{0} konia mechanicznego"},"pressure-millimeter-of-mercury":{displayName:"milimetry słupa rtęci","unitPattern-count-one":"{0} milimetr słupa rtęci","unitPattern-count-few":"{0} milimetry słupa rtęci","unitPattern-count-many":"{0} milimetrów słupa rtęci","unitPattern-count-other":"{0} milimetra słupa rtęci"},"pressure-pound-per-square-inch":{displayName:"funty na cal kwadratowy","unitPattern-count-one":"{0} funt na cal kwadratowy","unitPattern-count-few":"{0} funty na cal kwadratowy","unitPattern-count-many":"{0} funtów na cal kwadratowy","unitPattern-count-other":"{0} funta na cal kwadratowy"},"pressure-inch-hg":{displayName:"cale słupa rtęci","unitPattern-count-one":"{0} cal słupa rtęci","unitPattern-count-few":"{0} cale słupa rtęci","unitPattern-count-many":"{0} cali słupa rtęci","unitPattern-count-other":"{0} cala słupa rtęci"},"pressure-bar":{displayName:"bary","unitPattern-count-one":"{0} bar","unitPattern-count-few":"{0} bary","unitPattern-count-many":"{0} barów","unitPattern-count-other":"{0} bara"},"pressure-millibar":{displayName:"milibary","unitPattern-count-one":"{0} millibar","unitPattern-count-few":"{0} millibary","unitPattern-count-many":"{0} millibarów","unitPattern-count-other":"{0} millibara"},"pressure-atmosphere":{displayName:"atmosfery","unitPattern-count-one":"{0} atmosfera","unitPattern-count-few":"{0} atmosfery","unitPattern-count-many":"{0} atmosfer","unitPattern-count-other":"{0} atmosfery"},"pressure-pascal":{displayName:"paskale","unitPattern-count-one":"{0} paskal","unitPattern-count-few":"{0} paskale","unitPattern-count-many":"{0} paskali","unitPattern-count-other":"{0} paskala"},"pressure-hectopascal":{displayName:"hektopaskale","unitPattern-count-one":"{0} hektopaskal","unitPattern-count-few":"{0} hektopaskale","unitPattern-count-many":"{0} hektopaskali","unitPattern-count-other":"{0} hektopaskala"},"pressure-kilopascal":{displayName:"kilopaskale","unitPattern-count-one":"{0} kilopaskal","unitPattern-count-few":"{0} kilopaskale","unitPattern-count-many":"{0} kilopaskali","unitPattern-count-other":"{0} kilopaskala"},"pressure-megapascal":{displayName:"megapaskale","unitPattern-count-one":"{0} megapaskal","unitPattern-count-few":"{0} megapaskale","unitPattern-count-many":"{0} megapaskali","unitPattern-count-other":"{0} megapaskala"},"speed-kilometer-per-hour":{displayName:"kilometry na godzinę","unitPattern-count-one":"{0} kilometr na godzinę","unitPattern-count-few":"{0} kilometry na godzinę","unitPattern-count-many":"{0} kilometrów na godzinę","unitPattern-count-other":"{0} kilometra na godzinę"},"speed-meter-per-second":{displayName:"metry na sekundę","unitPattern-count-one":"{0} metr na sekundę","unitPattern-count-few":"{0} metry na sekundę","unitPattern-count-many":"{0} metrów na sekundę","unitPattern-count-other":"{0} metra na sekundę"},"speed-mile-per-hour":{displayName:"mile na godzinę","unitPattern-count-one":"{0} mila na godzinę","unitPattern-count-few":"{0} mile na godzinę","unitPattern-count-many":"{0} mil na godzinę","unitPattern-count-other":"{0} mili na godzinę"},"speed-knot":{displayName:"węzeł","unitPattern-count-one":"{0} węzeł","unitPattern-count-few":"{0} węzły","unitPattern-count-many":"{0} węzłów","unitPattern-count-other":"{0} węzła"},"temperature-generic":{displayName:"stopnie","unitPattern-count-one":"{0} stopień","unitPattern-count-few":"{0} stopnie","unitPattern-count-many":"{0} stopni","unitPattern-count-other":"{0} stopnia"},"temperature-celsius":{displayName:"stopnie Celsjusza","unitPattern-count-one":"{0} stopień Celsjusza","unitPattern-count-few":"{0} stopnie Celsjusza","unitPattern-count-many":"{0} stopni Celsjusza","unitPattern-count-other":"{0} stopnia Celsjusza"},"temperature-fahrenheit":{displayName:"stopnie Fahrenheita","unitPattern-count-one":"{0} stopień Fahrenheita","unitPattern-count-few":"{0} stopnie Fahrenheita","unitPattern-count-many":"{0} stopni Fahrenheita","unitPattern-count-other":"{0} stopnia Fahrenheita"},"temperature-kelvin":{displayName:"kelwiny","unitPattern-count-one":"{0} kelwin","unitPattern-count-few":"{0} kelwiny","unitPattern-count-many":"{0} kelwinów","unitPattern-count-other":"{0} kelwina"},"torque-pound-foot":{displayName:"stopofunty","unitPattern-count-one":"{0} stopofunt","unitPattern-count-few":"{0} stopofunty","unitPattern-count-many":"{0} stopofuntów","unitPattern-count-other":"{0} stopofunt"},"torque-newton-meter":{displayName:"niutonometry","unitPattern-count-one":"{0} niutonometr","unitPattern-count-few":"{0} niutonometry","unitPattern-count-many":"{0} niutonometrów","unitPattern-count-other":"{0} niutonometra"},"volume-cubic-kilometer":{displayName:"kilometry sześcienne","unitPattern-count-one":"{0} kilometr sześcienny","unitPattern-count-few":"{0} kilometry sześcienne","unitPattern-count-many":"{0} kilometrów sześciennych","unitPattern-count-other":"{0} kilometra sześciennego"},"volume-cubic-meter":{displayName:"metry sześcienne","unitPattern-count-one":"{0} metr sześcienny","unitPattern-count-few":"{0} metry sześcienne","unitPattern-count-many":"{0} metrów sześciennych","unitPattern-count-other":"{0} metra sześciennego",perUnitPattern:"{0} na metr sześcienny"},"volume-cubic-centimeter":{displayName:"centymetry sześcienne","unitPattern-count-one":"{0} centymetr sześcienny","unitPattern-count-few":"{0} centymetry sześcienne","unitPattern-count-many":"{0} centymetrów sześciennych","unitPattern-count-other":"{0} centymetra sześciennego",perUnitPattern:"{0} na centymetr sześcienny"},"volume-cubic-mile":{displayName:"mile sześcienne","unitPattern-count-one":"{0} mila sześcienna","unitPattern-count-few":"{0} mile sześcienne","unitPattern-count-many":"{0} mil sześciennych","unitPattern-count-other":"{0} mili sześciennej"},"volume-cubic-yard":{displayName:"jardy sześcienne","unitPattern-count-one":"{0} jard sześcienny","unitPattern-count-few":"{0} jardy sześcienne","unitPattern-count-many":"{0} jardów sześciennych","unitPattern-count-other":"{0} jarda sześciennego"},"volume-cubic-foot":{displayName:"stopy sześcienne","unitPattern-count-one":"{0} stopa sześcienna","unitPattern-count-few":"{0} stopy sześcienne","unitPattern-count-many":"{0} stóp sześciennych","unitPattern-count-other":"{0} stopy sześciennej"},"volume-cubic-inch":{displayName:"cale sześcienne","unitPattern-count-one":"{0} cal sześcienny","unitPattern-count-few":"{0} cale sześcienne","unitPattern-count-many":"{0} cali sześciennych","unitPattern-count-other":"{0} cala sześciennego"},"volume-megaliter":{displayName:"megalitry","unitPattern-count-one":"{0} megalitr","unitPattern-count-few":"{0} megalitry","unitPattern-count-many":"{0} megalitrów","unitPattern-count-other":"{0} megalitra"},"volume-hectoliter":{displayName:"hektolitry","unitPattern-count-one":"{0} hektolitr","unitPattern-count-few":"{0} hektolitry","unitPattern-count-many":"{0} hektolitrów","unitPattern-count-other":"{0} hektolitra"},"volume-liter":{displayName:"litry","unitPattern-count-one":"{0} litr","unitPattern-count-few":"{0} litry","unitPattern-count-many":"{0} litrów","unitPattern-count-other":"{0} litra",perUnitPattern:"{0} na litr"},"volume-deciliter":{displayName:"decylitry","unitPattern-count-one":"{0} decylitr","unitPattern-count-few":"{0} decylitry","unitPattern-count-many":"{0} decylitrów","unitPattern-count-other":"{0} decylitra"},"volume-centiliter":{displayName:"centylitry","unitPattern-count-one":"{0} centylitr","unitPattern-count-few":"{0} centylitry","unitPattern-count-many":"{0} centylitrów","unitPattern-count-other":"{0} centylitra"},"volume-milliliter":{displayName:"mililitry","unitPattern-count-one":"{0} mililitr","unitPattern-count-few":"{0} mililitry","unitPattern-count-many":"{0} mililitrów","unitPattern-count-other":"{0} mililitra"},"volume-pint-metric":{displayName:"półkwarty metryczne","unitPattern-count-one":"{0} półkwarta metryczna","unitPattern-count-few":"{0} półkwarty metryczne","unitPattern-count-many":"{0} półkwart metrycznych","unitPattern-count-other":"{0} półkwarty metrycznej"},"volume-cup-metric":{displayName:"ćwierćkwarty metryczne","unitPattern-count-one":"{0} ćwierćkwarta metryczna","unitPattern-count-few":"{0} ćwierćkwarty metryczne","unitPattern-count-many":"{0} ćwierćkwart metrycznych","unitPattern-count-other":"{0} ćwierćkwarty metrycznej"},"volume-acre-foot":{displayName:"akrostopy","unitPattern-count-one":"{0} akrostopa","unitPattern-count-few":"{0} akrostopy","unitPattern-count-many":"{0} akrostóp","unitPattern-count-other":"{0} akrostopy"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"galony","unitPattern-count-one":"{0} galon","unitPattern-count-few":"{0} galony","unitPattern-count-many":"{0} galonów","unitPattern-count-other":"{0} galona",perUnitPattern:"{0} na galon"},"volume-gallon-imperial":{displayName:"galony angielskie","unitPattern-count-one":"{0} galon angielski","unitPattern-count-few":"{0} galony angielskie","unitPattern-count-many":"{0} galonów angielskich","unitPattern-count-other":"{0} galona angielskiego",perUnitPattern:"{0} na galon angielski"},"volume-quart":{displayName:"kwarty","unitPattern-count-one":"{0} kwarta","unitPattern-count-few":"{0} kwarty","unitPattern-count-many":"{0} kwart","unitPattern-count-other":"{0} kwarty"},"volume-pint":{displayName:"półkwarty","unitPattern-count-one":"{0} półkwarta","unitPattern-count-few":"{0} półkwarty","unitPattern-count-many":"{0} półkwart","unitPattern-count-other":"{0} półkwarty"},"volume-cup":{displayName:"ćwierćkwarty","unitPattern-count-one":"{0} ćwierćkwarta","unitPattern-count-few":"{0} ćwierćkwarty","unitPattern-count-many":"{0} ćwierćkwart","unitPattern-count-other":"{0} ćwierćkwarty"},"volume-fluid-ounce":{displayName:"uncje płynu","unitPattern-count-one":"{0} uncja płynu","unitPattern-count-few":"{0} uncje płynu","unitPattern-count-many":"{0} uncji płynu","unitPattern-count-other":"{0} uncji płynu"},"volume-fluid-ounce-imperial":{displayName:"uncje płynu imp.","unitPattern-count-one":"{0} uncja płynu imp.","unitPattern-count-few":"{0} uncje płynu imp.","unitPattern-count-many":"{0} uncji płynu imp.","unitPattern-count-other":"{0} uncji płynu imp."},"volume-tablespoon":{displayName:"łyżki stołowe","unitPattern-count-one":"{0} łyżka stołowa","unitPattern-count-few":"{0} łyżki stołowe","unitPattern-count-many":"{0} łyżek stołowych","unitPattern-count-other":"{0} łyżki stołowej"},"volume-teaspoon":{displayName:"łyżeczki","unitPattern-count-one":"{0} łyżeczka","unitPattern-count-few":"{0} łyżeczki","unitPattern-count-many":"{0} łyżeczek","unitPattern-count-other":"{0} łyżeczki"},"volume-barrel":{displayName:"baryłki","unitPattern-count-one":"{0} baryłka","unitPattern-count-few":"{0} baryłki","unitPattern-count-many":"{0} baryłek","unitPattern-count-other":"{0} baryłki"},coordinateUnit:{displayName:"kierunek świata",east:"{0} długości geograficznej wschodniej",north:"{0} szerokości geograficznej północnej",south:"{0} szerokości geograficznej południowej",west:"{0} długości geograficznej zachodniej"}},short:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"G","unitPattern-count-other":"{0} G"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"obr.","unitPattern-count-one":"{0} obr.","unitPattern-count-few":"{0} obr.","unitPattern-count-many":"{0} obr.","unitPattern-count-other":"{0} obr."},"angle-radian":{displayName:"rad","unitPattern-count-other":"{0} rad"},"angle-degree":{displayName:"stopnie","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"minuty","unitPattern-count-other":"{0}′"},"angle-arc-second":{displayName:"sekundy","unitPattern-count-other":"{0}″"},"area-square-kilometer":{displayName:"km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"ha","unitPattern-count-other":"{0} ha"},"area-square-meter":{displayName:"m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"mile kw.","unitPattern-count-one":"{0} mila kw.","unitPattern-count-few":"{0} mile kw.","unitPattern-count-many":"{0} mil kw.","unitPattern-count-other":"{0} mili kw.",perUnitPattern:"{0}/mila kw."},"area-acre":{displayName:"akry","unitPattern-count-one":"{0} akr","unitPattern-count-few":"{0} akry","unitPattern-count-many":"{0} akrów","unitPattern-count-other":"{0} akra"},"area-square-yard":{displayName:"jardy kw.","unitPattern-count-one":"{0} jard kw.","unitPattern-count-few":"{0} jardy kw.","unitPattern-count-many":"{0} jardów kw.","unitPattern-count-other":"{0} jarda kw."},"area-square-foot":{displayName:"stopy kw.","unitPattern-count-one":"{0} stopa kw.","unitPattern-count-few":"{0} stopy kw.","unitPattern-count-many":"{0} stóp kw.","unitPattern-count-other":"{0} stopy kw."},"area-square-inch":{displayName:"cale kw.","unitPattern-count-one":"{0} cal kw.","unitPattern-count-few":"{0} cale kw.","unitPattern-count-many":"{0} cali kw.","unitPattern-count-other":"{0} cala kw.",perUnitPattern:"{0}/cal kw."},"area-dunam":{displayName:"dunamy","unitPattern-count-one":"{0} dunam","unitPattern-count-few":"{0} dunamy","unitPattern-count-many":"{0} dunamów","unitPattern-count-other":"{0} dunama"},"concentr-karat":{displayName:"karaty","unitPattern-count-other":"{0} kt"},"concentr-milligram-per-deciliter":{displayName:"mg/dl","unitPattern-count-one":"{0} mg/dl","unitPattern-count-few":"{0} mg/dl","unitPattern-count-many":"{0} mg/dl","unitPattern-count-other":"{0} mg/dl"},"concentr-millimole-per-liter":{displayName:"mmol/l","unitPattern-count-one":"{0} mmol/l","unitPattern-count-few":"{0} mmol/l","unitPattern-count-many":"{0} mmol/l","unitPattern-count-other":"{0} mmol/l"},"concentr-part-per-million":{displayName:"części/milion","unitPattern-count-other":"{0} ppm"},"concentr-percent":{displayName:"%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"‱","unitPattern-count-one":"{0}‱","unitPattern-count-few":"{0}‱","unitPattern-count-many":"{0}‱","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-few":"{0} mole","unitPattern-count-many":"{0} moli","unitPattern-count-other":"{0} mola"},"consumption-liter-per-kilometer":{displayName:"l/km","unitPattern-count-one":"{0} l/km","unitPattern-count-few":"{0} l/km","unitPattern-count-many":"{0} l/km","unitPattern-count-other":"{0} l/km"},"consumption-liter-per-100kilometers":{displayName:"l/100 km","unitPattern-count-one":"{0} l/100 km","unitPattern-count-few":"{0} l/100 km","unitPattern-count-many":"{0} l/100 km","unitPattern-count-other":"{0} l/100 km"},"consumption-mile-per-gallon":{displayName:"mpg","unitPattern-count-one":"{0} mpg","unitPattern-count-few":"{0} mpg","unitPattern-count-many":"{0} mpg","unitPattern-count-other":"{0} mpg"},"consumption-mile-per-gallon-imperial":{displayName:"mile/gal ang.","unitPattern-count-one":"{0} mi/gal ang.","unitPattern-count-few":"{0} mi/gal ang.","unitPattern-count-many":"{0} mi/gal ang.","unitPattern-count-other":"{0} mi/gal ang."},"digital-petabyte":{displayName:"PB","unitPattern-count-other":"{0} PB"},"digital-terabyte":{displayName:"TB","unitPattern-count-other":"{0} TB"},"digital-terabit":{displayName:"Tb","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"GB","unitPattern-count-other":"{0} GB"},"digital-gigabit":{displayName:"Gb","unitPattern-count-other":"{0} Gb"},"digital-megabyte":{displayName:"MB","unitPattern-count-other":"{0} MB"},"digital-megabit":{displayName:"Mb","unitPattern-count-other":"{0} Mb"},"digital-kilobyte":{displayName:"kB","unitPattern-count-other":"{0} kB"},"digital-kilobit":{displayName:"kb","unitPattern-count-other":"{0} kb"},"digital-byte":{displayName:"B","unitPattern-count-one":"{0} B","unitPattern-count-few":"{0} B","unitPattern-count-many":"{0} B","unitPattern-count-other":"{0} B"},"digital-bit":{displayName:"b","unitPattern-count-one":"{0} b","unitPattern-count-few":"{0} b","unitPattern-count-many":"{0} b","unitPattern-count-other":"{0} b"},"duration-century":{displayName:"w.","unitPattern-count-one":"{0} w.","unitPattern-count-few":"{0} w.","unitPattern-count-many":"{0} w.","unitPattern-count-other":"{0} w."},"duration-decade":{displayName:"dek","unitPattern-count-one":"{0} dek","unitPattern-count-few":"{0} dek","unitPattern-count-many":"{0} dek","unitPattern-count-other":"{0} dek"},"duration-year":{displayName:"lata","unitPattern-count-one":"{0} rok","unitPattern-count-few":"{0} lata","unitPattern-count-many":"{0} lat","unitPattern-count-other":"{0} roku",perUnitPattern:"{0}/rok"},"duration-month":{displayName:"miesiące","unitPattern-count-one":"{0} mies.","unitPattern-count-few":"{0} mies.","unitPattern-count-many":"{0} mies.","unitPattern-count-other":"{0} mies.",perUnitPattern:"{0}/mies."},"duration-week":{displayName:"tyg.","unitPattern-count-one":"{0} tydz.","unitPattern-count-few":"{0} tyg.","unitPattern-count-many":"{0} tyg.","unitPattern-count-other":"{0} tyg.",perUnitPattern:"{0}/tydz."},"duration-day":{displayName:"dni","unitPattern-count-one":"{0} dzień","unitPattern-count-few":"{0} dni","unitPattern-count-many":"{0} dni","unitPattern-count-other":"{0} dnia",perUnitPattern:"{0}/dzień"},"duration-hour":{displayName:"godz.","unitPattern-count-one":"{0} godz.","unitPattern-count-few":"{0} godz.","unitPattern-count-many":"{0} godz.","unitPattern-count-other":"{0} godz.",perUnitPattern:"{0}/godz."},"duration-minute":{displayName:"min","unitPattern-count-other":"{0} min",perUnitPattern:"{0}/min"},"duration-second":{displayName:"sek.","unitPattern-count-one":"{0} sek.","unitPattern-count-few":"{0} sek.","unitPattern-count-many":"{0} sek.","unitPattern-count-other":"{0} sek.",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"ms","unitPattern-count-other":"{0} ms"},"duration-microsecond":{displayName:"μs","unitPattern-count-other":"{0} μs"},"duration-nanosecond":{displayName:"ns","unitPattern-count-other":"{0} ns"},"electric-ampere":{displayName:"A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kcal","unitPattern-count-other":"{0} kcal"},"energy-calorie":{displayName:"cal","unitPattern-count-other":"{0} cal"},"energy-foodcalorie":{displayName:"cal","unitPattern-count-one":"{0} cal","unitPattern-count-few":"{0} cal","unitPattern-count-many":"{0} cal","unitPattern-count-other":"{0} cal"},"energy-kilojoule":{displayName:"kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"elektronowolt","unitPattern-count-one":"{0} eV","unitPattern-count-few":"{0} eV","unitPattern-count-many":"{0} eV","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0} Btu","unitPattern-count-few":"{0} Btu","unitPattern-count-many":"{0} Btu","unitPattern-count-other":"{0} Btu"},"energy-therm-us":{displayName:"thermy amer.","unitPattern-count-one":"{0} therm amer.","unitPattern-count-few":"{0} thermy amer.","unitPattern-count-many":"{0} thermów amer.","unitPattern-count-other":"{0} therma amer."},"force-pound-force":{displayName:"funt-siła","unitPattern-count-one":"{0} lbf","unitPattern-count-few":"{0} lbf","unitPattern-count-many":"{0} lbf","unitPattern-count-other":"{0} lbf"},"force-newton":{displayName:"N","unitPattern-count-one":"{0} N","unitPattern-count-few":"{0} N","unitPattern-count-many":"{0} N","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"firet","unitPattern-count-one":"{0} firet","unitPattern-count-few":"{0} firety","unitPattern-count-many":"{0} firetów","unitPattern-count-other":"{0} firetu"},"graphics-pixel":{displayName:"px","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"MP","unitPattern-count-other":"{0} MP"},"graphics-pixel-per-centimeter":{displayName:"ppcm","unitPattern-count-other":"{0} ppcm"},"graphics-pixel-per-inch":{displayName:"ppi","unitPattern-count-other":"{0} ppi"},"graphics-dot-per-centimeter":{displayName:"dpcm","unitPattern-count-other":"{0} dpcm"},"graphics-dot-per-inch":{displayName:"dpi","unitPattern-count-other":"{0} dpi"},"length-kilometer":{displayName:"km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-other":"{0} pm"},"length-mile":{displayName:"mile","unitPattern-count-one":"{0} mila","unitPattern-count-few":"{0} mile","unitPattern-count-many":"{0} mil","unitPattern-count-other":"{0} mili"},"length-yard":{displayName:"jardy","unitPattern-count-one":"{0} jard","unitPattern-count-few":"{0} jardy","unitPattern-count-many":"{0} jardów","unitPattern-count-other":"{0} jarda"},"length-foot":{displayName:"stopy","unitPattern-count-one":"{0} stopa","unitPattern-count-few":"{0} stopy","unitPattern-count-many":"{0} stóp","unitPattern-count-other":"{0} stopy",perUnitPattern:"{0}/stopa"},"length-inch":{displayName:"cale","unitPattern-count-one":"{0} cal","unitPattern-count-few":"{0} cale","unitPattern-count-many":"{0} cali","unitPattern-count-other":"{0} cala",perUnitPattern:"{0}/cal"},"length-parsec":{displayName:"pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"ly","unitPattern-count-other":"{0} ly"},"length-astronomical-unit":{displayName:"j.a.","unitPattern-count-one":"{0} j.a.","unitPattern-count-few":"{0} j.a.","unitPattern-count-many":"{0} j.a.","unitPattern-count-other":"{0} j.a."},"length-furlong":{displayName:"fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"Mm","unitPattern-count-one":"{0} Mm","unitPattern-count-few":"{0} Mm","unitPattern-count-many":"{0} Mm","unitPattern-count-other":"{0} Mm"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"pkt.","unitPattern-count-one":"{0} pkt.","unitPattern-count-few":"{0} pkt.","unitPattern-count-many":"{0} pkt.","unitPattern-count-other":"{0} pkt."},"length-solar-radius":{displayName:"R☉","unitPattern-count-one":"{0} R☉","unitPattern-count-few":"{0} R☉","unitPattern-count-many":"{0} R☉","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"L☉","unitPattern-count-one":"{0} L☉","unitPattern-count-few":"{0} L☉","unitPattern-count-many":"{0} L☉","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"krótkie tony","unitPattern-count-one":"{0} krótka tona","unitPattern-count-few":"{0} krótkie tony","unitPattern-count-many":"{0} krótkich ton","unitPattern-count-other":"{0} krótkiej tony"},"mass-stone":{displayName:"st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"funty","unitPattern-count-one":"{0} funt","unitPattern-count-few":"{0} funty","unitPattern-count-many":"{0} funtów","unitPattern-count-other":"{0} funta",perUnitPattern:"{0}/funt"},"mass-ounce":{displayName:"oz","unitPattern-count-other":"{0} oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"karaty","unitPattern-count-one":"{0} kt","unitPattern-count-few":"{0} kt","unitPattern-count-many":"{0} kt","unitPattern-count-other":"{0} kt"},"mass-dalton":{displayName:"daltony","unitPattern-count-one":"{0} Da","unitPattern-count-few":"{0} Da","unitPattern-count-many":"{0} Da","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"M⊕","unitPattern-count-one":"{0} M⊕","unitPattern-count-few":"{0} M⊕","unitPattern-count-many":"{0} M⊕","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"M☉","unitPattern-count-one":"{0} M☉","unitPattern-count-few":"{0} M☉","unitPattern-count-many":"{0} M☉","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-other":"{0} kW"},"power-watt":{displayName:"waty","unitPattern-count-other":"{0} W"},"power-milliwatt":{displayName:"mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"KM","unitPattern-count-one":"{0} KM","unitPattern-count-few":"{0} KM","unitPattern-count-many":"{0} KM","unitPattern-count-other":"{0} KM"},"pressure-millimeter-of-mercury":{displayName:"mm Hg","unitPattern-count-other":"{0} mm Hg"},"pressure-pound-per-square-inch":{displayName:"psi","unitPattern-count-other":"{0} psi"},"pressure-inch-hg":{displayName:"inHg","unitPattern-count-other":"{0} inHg"},"pressure-bar":{displayName:"bar","unitPattern-count-other":"{0} bar"},"pressure-millibar":{displayName:"mbar","unitPattern-count-other":"{0} mbar"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-other":"{0} atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-other":"{0} hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0} kPa","unitPattern-count-few":"{0} kPa","unitPattern-count-many":"{0} kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0} MPa","unitPattern-count-few":"{0} MPa","unitPattern-count-many":"{0} MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"m/s","unitPattern-count-other":"{0} m/s"},"speed-mile-per-hour":{displayName:"mile/h","unitPattern-count-one":"{0} mila/h","unitPattern-count-few":"{0} mile/h","unitPattern-count-many":"{0} mil/h","unitPattern-count-other":"{0} mili/h"},"speed-knot":{displayName:"w.","unitPattern-count-one":"{0} w.","unitPattern-count-few":"{0} w.","unitPattern-count-many":"{0} w.","unitPattern-count-other":"{0} w."},"temperature-generic":{displayName:"°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"st. C","unitPattern-count-one":"{0} st. C","unitPattern-count-few":"{0} st. C","unitPattern-count-many":"{0} st. C","unitPattern-count-other":"{0} st. C"},"temperature-fahrenheit":{displayName:"°F","unitPattern-count-other":"{0}°F"},"temperature-kelvin":{displayName:"K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-one":"{0} lbf⋅ft","unitPattern-count-few":"{0} lbf⋅ft","unitPattern-count-many":"{0} lbf⋅ft","unitPattern-count-other":"{0} lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-one":"{0} N⋅m","unitPattern-count-few":"{0} N⋅m","unitPattern-count-many":"{0} N⋅m","unitPattern-count-other":"{0} N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-other":"{0} km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mile sześc.","unitPattern-count-one":"{0} mila sześc.","unitPattern-count-few":"{0} mile sześc.","unitPattern-count-many":"{0} mil sześc.","unitPattern-count-other":"{0} mili sześc."},"volume-cubic-yard":{displayName:"jardy sześc.","unitPattern-count-one":"{0} jard sześc.","unitPattern-count-few":"{0} jardy sześc.","unitPattern-count-many":"{0} jardów sześc.","unitPattern-count-other":"{0} jarda sześc."},"volume-cubic-foot":{displayName:"stopy sześc.","unitPattern-count-one":"{0} stopa sześc.","unitPattern-count-few":"{0} stopy sześc.","unitPattern-count-many":"{0} stóp sześc.","unitPattern-count-other":"{0} stopy sześc."},"volume-cubic-inch":{displayName:"cale sześc.","unitPattern-count-one":"{0} cal sześc.","unitPattern-count-few":"{0} cale sześc.","unitPattern-count-many":"{0} cali sześc.","unitPattern-count-other":"{0} cala sześc."},"volume-megaliter":{displayName:"Ml","unitPattern-count-one":"{0} Ml","unitPattern-count-few":"{0} Ml","unitPattern-count-many":"{0} Ml","unitPattern-count-other":"{0} Ml"},"volume-hectoliter":{displayName:"hl","unitPattern-count-one":"{0} hl","unitPattern-count-few":"{0} hl","unitPattern-count-many":"{0} hl","unitPattern-count-other":"{0} hl"},"volume-liter":{displayName:"litry","unitPattern-count-other":"{0} l",perUnitPattern:"{0}/l"},"volume-deciliter":{displayName:"dl","unitPattern-count-one":"{0} dl","unitPattern-count-few":"{0} dl","unitPattern-count-many":"{0} dl","unitPattern-count-other":"{0} dl"},"volume-centiliter":{displayName:"cl","unitPattern-count-one":"{0} cl","unitPattern-count-few":"{0} cl","unitPattern-count-many":"{0} cl","unitPattern-count-other":"{0} cl"},"volume-milliliter":{displayName:"ml","unitPattern-count-one":"{0} ml","unitPattern-count-few":"{0} ml","unitPattern-count-many":"{0} ml","unitPattern-count-other":"{0} ml"},"volume-pint-metric":{displayName:"półkwarty metr.","unitPattern-count-one":"{0} półkwarta metr.","unitPattern-count-few":"{0} półkwarty metr.","unitPattern-count-many":"{0} półkwart metr.","unitPattern-count-other":"{0} półkwarty metr."},"volume-cup-metric":{displayName:"ćwierćkwarty metr.","unitPattern-count-one":"{0} ćwierćkwarta metr.","unitPattern-count-few":"{0} ćwierćkwarty metr.","unitPattern-count-many":"{0} ćwierćkwart metr.","unitPattern-count-other":"{0} ćwierćkwarty metr."},"volume-acre-foot":{displayName:"akrostopy","unitPattern-count-one":"{0} akrostopa","unitPattern-count-few":"{0} akrostopy","unitPattern-count-many":"{0} akrostóp","unitPattern-count-other":"{0} akrostopy"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gal","unitPattern-count-one":"{0} gal","unitPattern-count-few":"{0} gal","unitPattern-count-many":"{0} gal","unitPattern-count-other":"{0} gal",perUnitPattern:"{0}/gal"},"volume-gallon-imperial":{displayName:"gal ang.","unitPattern-count-one":"{0} gal ang.","unitPattern-count-few":"{0} gal ang.","unitPattern-count-many":"{0} gal ang.","unitPattern-count-other":"{0} gal ang.",perUnitPattern:"{0}/gal ang."},"volume-quart":{displayName:"kwarty","unitPattern-count-one":"{0} kwarta","unitPattern-count-few":"{0} kwarty","unitPattern-count-many":"{0} kwart","unitPattern-count-other":"{0} kwarty"},"volume-pint":{displayName:"półkwarty","unitPattern-count-one":"{0} półkwarta","unitPattern-count-few":"{0} półkwarty","unitPattern-count-many":"{0} półkwart","unitPattern-count-other":"{0} półkwarty"},"volume-cup":{displayName:"ćwierćkwarty","unitPattern-count-one":"{0} ćwierćkwarta","unitPattern-count-few":"{0} ćwierćkwarty","unitPattern-count-many":"{0} ćwierćkwart","unitPattern-count-other":"{0} ćwierćkwarty"},"volume-fluid-ounce":{displayName:"fl oz","unitPattern-count-one":"{0} fl oz","unitPattern-count-few":"{0} fl oz","unitPattern-count-many":"{0} fl oz","unitPattern-count-other":"{0} fl oz"},"volume-fluid-ounce-imperial":{displayName:"fl oz imp.","unitPattern-count-one":"{0} fl oz imp.","unitPattern-count-few":"{0} fl oz imp.","unitPattern-count-many":"{0} fl oz imp.","unitPattern-count-other":"{0} fl oz imp."},"volume-tablespoon":{displayName:"ł. stoł.","unitPattern-count-one":"{0} ł. stoł.","unitPattern-count-few":"{0} ł. stoł.","unitPattern-count-many":"{0} ł. stoł.","unitPattern-count-other":"{0} ł. stoł."},"volume-teaspoon":{displayName:"łyżeczki","unitPattern-count-one":"{0} łyżeczka","unitPattern-count-few":"{0} łyżeczki","unitPattern-count-many":"{0} łyżeczek","unitPattern-count-other":"{0} łyżeczki"},"volume-barrel":{displayName:"baryłki","unitPattern-count-one":"{0} bbl","unitPattern-count-few":"{0} bbl","unitPattern-count-many":"{0} bbl","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"kierunek",east:"{0}E",north:"{0}N",south:"{0}S",west:"{0}W"}},narrow:{per:{compoundUnitPattern:"{0}/{1}"},times:{compoundUnitPattern:"{0}⋅{1}"},"acceleration-g-force":{displayName:"G","unitPattern-count-one":"{0} G","unitPattern-count-few":"{0} G","unitPattern-count-many":"{0} G","unitPattern-count-other":"{0} G"},"acceleration-meter-per-second-squared":{displayName:"m/s²","unitPattern-count-other":"{0} m/s²"},"angle-revolution":{displayName:"obr.","unitPattern-count-one":"{0} obr.","unitPattern-count-few":"{0} obr.","unitPattern-count-many":"{0} obr.","unitPattern-count-other":"{0} obr."},"angle-radian":{displayName:"rad","unitPattern-count-other":"{0} rad"},"angle-degree":{displayName:"stopnie","unitPattern-count-other":"{0}°"},"angle-arc-minute":{displayName:"minuty","unitPattern-count-one":"{0}′","unitPattern-count-few":"{0}′","unitPattern-count-many":"{0}′","unitPattern-count-other":"{0}′"},"angle-arc-second":{displayName:"sekundy","unitPattern-count-one":"{0}″","unitPattern-count-few":"{0}″","unitPattern-count-many":"{0}″","unitPattern-count-other":"{0}″"},"area-square-kilometer":{displayName:"km²","unitPattern-count-other":"{0} km²",perUnitPattern:"{0}/km²"},"area-hectare":{displayName:"ha","unitPattern-count-other":"{0} ha"},"area-square-meter":{displayName:"m²","unitPattern-count-other":"{0} m²",perUnitPattern:"{0}/m²"},"area-square-centimeter":{displayName:"cm²","unitPattern-count-other":"{0} cm²",perUnitPattern:"{0}/cm²"},"area-square-mile":{displayName:"mile kw.","unitPattern-count-one":"{0} mila kw.","unitPattern-count-few":"{0} mile kw.","unitPattern-count-many":"{0} mil kw.","unitPattern-count-other":"{0} mili kw.",perUnitPattern:"{0}/mila kw."},"area-acre":{displayName:"akry","unitPattern-count-one":"{0} akr","unitPattern-count-few":"{0} akry","unitPattern-count-many":"{0} akrów","unitPattern-count-other":"{0} akra"},"area-square-yard":{displayName:"jardy kw.","unitPattern-count-one":"{0} jard kw.","unitPattern-count-few":"{0} jardy kw.","unitPattern-count-many":"{0} jardów kw.","unitPattern-count-other":"{0} jarda kw."},"area-square-foot":{displayName:"stopy kw.","unitPattern-count-one":"{0} stopa kw.","unitPattern-count-few":"{0} stopy kw.","unitPattern-count-many":"{0} stóp kw.","unitPattern-count-other":"{0} stopy kw."},"area-square-inch":{displayName:"cale kw.","unitPattern-count-one":"{0} cal kw.","unitPattern-count-few":"{0} cale kw.","unitPattern-count-many":"{0} cali kw.","unitPattern-count-other":"{0} cala kw.",perUnitPattern:"{0}/cal kw."},"area-dunam":{displayName:"dunamy","unitPattern-count-one":"{0} dunam","unitPattern-count-few":"{0} dunamy","unitPattern-count-many":"{0} dunamów","unitPattern-count-other":"{0} dunama"},"concentr-karat":{displayName:"karaty","unitPattern-count-other":"{0} kt"},"concentr-milligram-per-deciliter":{displayName:"mg/dl","unitPattern-count-one":"{0} mg/dl","unitPattern-count-few":"{0} mg/dl","unitPattern-count-many":"{0} mg/dl","unitPattern-count-other":"{0} mg/dl"},"concentr-millimole-per-liter":{displayName:"mmol/l","unitPattern-count-one":"{0} mmol/l","unitPattern-count-few":"{0} mmol/l","unitPattern-count-many":"{0} mmol/l","unitPattern-count-other":"{0} mmol/l"},"concentr-part-per-million":{displayName:"części/milion","unitPattern-count-other":"{0} ppm"},"concentr-percent":{displayName:"%","unitPattern-count-other":"{0}%"},"concentr-permille":{displayName:"‰","unitPattern-count-other":"{0}‰"},"concentr-permyriad":{displayName:"‱","unitPattern-count-one":"{0}‱","unitPattern-count-few":"{0}‱","unitPattern-count-many":"{0}‱","unitPattern-count-other":"{0}‱"},"concentr-mole":{displayName:"mol","unitPattern-count-one":"{0} mol","unitPattern-count-few":"{0} mole","unitPattern-count-many":"{0} moli","unitPattern-count-other":"{0} mola"},"consumption-liter-per-kilometer":{displayName:"l/km","unitPattern-count-one":"{0} l/km","unitPattern-count-few":"{0} l/km","unitPattern-count-many":"{0} l/km","unitPattern-count-other":"{0} l/km"},"consumption-liter-per-100kilometers":{displayName:"l/100 km","unitPattern-count-one":"{0} l/100 km","unitPattern-count-few":"{0} l/100 km","unitPattern-count-many":"{0} l/100 km","unitPattern-count-other":"{0} l/100 km"},"consumption-mile-per-gallon":{displayName:"mpg","unitPattern-count-one":"{0} mpg","unitPattern-count-few":"{0} mpg","unitPattern-count-many":"{0} mpg","unitPattern-count-other":"{0} mpg"},"consumption-mile-per-gallon-imperial":{displayName:"mile/gal ang.","unitPattern-count-one":"{0} mi/gal ang.","unitPattern-count-few":"{0} mi/gal ang.","unitPattern-count-many":"{0} mi/gal ang.","unitPattern-count-other":"{0} mi/gal ang."},"digital-petabyte":{displayName:"PB","unitPattern-count-other":"{0} PB"},"digital-terabyte":{displayName:"TB","unitPattern-count-other":"{0} TB"},"digital-terabit":{displayName:"Tb","unitPattern-count-other":"{0} Tb"},"digital-gigabyte":{displayName:"GB","unitPattern-count-other":"{0} GB"},"digital-gigabit":{displayName:"Gb","unitPattern-count-other":"{0} Gb"},"digital-megabyte":{displayName:"MB","unitPattern-count-other":"{0} MB"},"digital-megabit":{displayName:"Mb","unitPattern-count-other":"{0} Mb"},"digital-kilobyte":{displayName:"kB","unitPattern-count-other":"{0} kB"},"digital-kilobit":{displayName:"kb","unitPattern-count-other":"{0} kb"},"digital-byte":{displayName:"B","unitPattern-count-one":"{0} B","unitPattern-count-few":"{0} B","unitPattern-count-many":"{0} B","unitPattern-count-other":"{0} B"},"digital-bit":{displayName:"b","unitPattern-count-one":"{0} b","unitPattern-count-few":"{0} b","unitPattern-count-many":"{0} b","unitPattern-count-other":"{0} b"},"duration-century":{displayName:"w.","unitPattern-count-one":"{0} w.","unitPattern-count-few":"{0} w.","unitPattern-count-many":"{0} w.","unitPattern-count-other":"{0} w."},"duration-decade":{displayName:"dek","unitPattern-count-one":"{0} dek","unitPattern-count-few":"{0} dek","unitPattern-count-many":"{0} dek","unitPattern-count-other":"{0} dek"},"duration-year":{displayName:"r.","unitPattern-count-one":"{0} r.","unitPattern-count-few":"{0} l.","unitPattern-count-many":"{0} l.","unitPattern-count-other":"{0} r.",perUnitPattern:"{0}/rok"},"duration-month":{displayName:"m-c","unitPattern-count-one":"{0} m-c","unitPattern-count-few":"{0} m-ce","unitPattern-count-many":"{0} m-cy","unitPattern-count-other":"{0} m-ca",perUnitPattern:"{0}/mies."},"duration-week":{displayName:"tydz.","unitPattern-count-one":"{0} tydz.","unitPattern-count-few":"{0} tyg.","unitPattern-count-many":"{0} tyg.","unitPattern-count-other":"{0} tyg.",perUnitPattern:"{0}/tydz."},"duration-day":{displayName:"dzień","unitPattern-count-one":"{0} d.","unitPattern-count-few":"{0} dn.","unitPattern-count-many":"{0} dn.","unitPattern-count-other":"{0} dn.",perUnitPattern:"{0}/d."},"duration-hour":{displayName:"g.","unitPattern-count-one":"{0} g.","unitPattern-count-few":"{0} g.","unitPattern-count-many":"{0} g.","unitPattern-count-other":"{0} g.",perUnitPattern:"{0}/godz."},"duration-minute":{displayName:"min","unitPattern-count-other":"{0} min",perUnitPattern:"{0}/min"},"duration-second":{displayName:"s","unitPattern-count-one":"{0} s","unitPattern-count-few":"{0} s","unitPattern-count-many":"{0} s","unitPattern-count-other":"{0} s",perUnitPattern:"{0}/s"},"duration-millisecond":{displayName:"ms","unitPattern-count-other":"{0} ms"},"duration-microsecond":{displayName:"μs","unitPattern-count-other":"{0} μs"},"duration-nanosecond":{displayName:"ns","unitPattern-count-other":"{0} ns"},"electric-ampere":{displayName:"A","unitPattern-count-other":"{0} A"},"electric-milliampere":{displayName:"mA","unitPattern-count-other":"{0} mA"},"electric-ohm":{displayName:"Ω","unitPattern-count-other":"{0} Ω"},"electric-volt":{displayName:"V","unitPattern-count-other":"{0} V"},"energy-kilocalorie":{displayName:"kcal","unitPattern-count-other":"{0} kcal"},"energy-calorie":{displayName:"cal","unitPattern-count-other":"{0} cal"},"energy-foodcalorie":{displayName:"cal","unitPattern-count-one":"{0} cal","unitPattern-count-few":"{0} cal","unitPattern-count-many":"{0} cal","unitPattern-count-other":"{0} cal"},"energy-kilojoule":{displayName:"kJ","unitPattern-count-other":"{0} kJ"},"energy-joule":{displayName:"J","unitPattern-count-other":"{0} J"},"energy-kilowatt-hour":{displayName:"kWh","unitPattern-count-other":"{0} kWh"},"energy-electronvolt":{displayName:"elektronowolt","unitPattern-count-one":"{0} eV","unitPattern-count-few":"{0} eV","unitPattern-count-many":"{0} eV","unitPattern-count-other":"{0} eV"},"energy-british-thermal-unit":{displayName:"BTU","unitPattern-count-one":"{0} Btu","unitPattern-count-few":"{0} Btu","unitPattern-count-many":"{0} Btu","unitPattern-count-other":"{0} Btu"},"energy-therm-us":{displayName:"thermy amer.","unitPattern-count-one":"{0} therm amer.","unitPattern-count-few":"{0} thermy amer.","unitPattern-count-many":"{0} thermów amer.","unitPattern-count-other":"{0} therma amer."},"force-pound-force":{displayName:"funt-siła","unitPattern-count-one":"{0} lbf","unitPattern-count-few":"{0} lbf","unitPattern-count-many":"{0} lbf","unitPattern-count-other":"{0} lbf"},"force-newton":{displayName:"N","unitPattern-count-one":"{0} N","unitPattern-count-few":"{0} N","unitPattern-count-many":"{0} N","unitPattern-count-other":"{0} N"},"frequency-gigahertz":{displayName:"GHz","unitPattern-count-other":"{0} GHz"},"frequency-megahertz":{displayName:"MHz","unitPattern-count-other":"{0} MHz"},"frequency-kilohertz":{displayName:"kHz","unitPattern-count-other":"{0} kHz"},"frequency-hertz":{displayName:"Hz","unitPattern-count-other":"{0} Hz"},"graphics-em":{displayName:"firet","unitPattern-count-one":"{0} firet","unitPattern-count-few":"{0} firety","unitPattern-count-many":"{0} firetów","unitPattern-count-other":"{0} firetu"},"graphics-pixel":{displayName:"px","unitPattern-count-other":"{0} px"},"graphics-megapixel":{displayName:"MP","unitPattern-count-other":"{0} MP"},"graphics-pixel-per-centimeter":{displayName:"ppcm","unitPattern-count-other":"{0} ppcm"},"graphics-pixel-per-inch":{displayName:"ppi","unitPattern-count-other":"{0} ppi"},"graphics-dot-per-centimeter":{displayName:"dpcm","unitPattern-count-other":"{0} dpcm"},"graphics-dot-per-inch":{displayName:"dpi","unitPattern-count-other":"{0} dpi"},"length-kilometer":{displayName:"km","unitPattern-count-other":"{0} km",perUnitPattern:"{0}/km"},"length-meter":{displayName:"m","unitPattern-count-other":"{0} m",perUnitPattern:"{0}/m"},"length-decimeter":{displayName:"dm","unitPattern-count-other":"{0} dm"},"length-centimeter":{displayName:"cm","unitPattern-count-other":"{0} cm",perUnitPattern:"{0}/cm"},"length-millimeter":{displayName:"mm","unitPattern-count-other":"{0} mm"},"length-micrometer":{displayName:"µm","unitPattern-count-other":"{0} µm"},"length-nanometer":{displayName:"nm","unitPattern-count-other":"{0} nm"},"length-picometer":{displayName:"pm","unitPattern-count-other":"{0} pm"},"length-mile":{displayName:"mile","unitPattern-count-one":"{0} mila","unitPattern-count-few":"{0} mile","unitPattern-count-many":"{0} mil","unitPattern-count-other":"{0} mili"},"length-yard":{displayName:"jardy","unitPattern-count-one":"{0} jard","unitPattern-count-few":"{0} jardy","unitPattern-count-many":"{0} jardów","unitPattern-count-other":"{0} jarda"},"length-foot":{displayName:"stopy","unitPattern-count-one":"{0} stopa","unitPattern-count-few":"{0} stopy","unitPattern-count-many":"{0} stóp","unitPattern-count-other":"{0} stopy",perUnitPattern:"{0}/stopa"},"length-inch":{displayName:"cale","unitPattern-count-one":"{0}″","unitPattern-count-few":"{0}″","unitPattern-count-many":"{0}″","unitPattern-count-other":"{0}″",perUnitPattern:"{0}/cal"},"length-parsec":{displayName:"pc","unitPattern-count-other":"{0} pc"},"length-light-year":{displayName:"ly","unitPattern-count-other":"{0} ly"},"length-astronomical-unit":{displayName:"j.a.","unitPattern-count-one":"{0} j.a.","unitPattern-count-few":"{0} j.a.","unitPattern-count-many":"{0} j.a.","unitPattern-count-other":"{0} j.a."},"length-furlong":{displayName:"fur","unitPattern-count-other":"{0} fur"},"length-fathom":{displayName:"fm","unitPattern-count-other":"{0} fth"},"length-nautical-mile":{displayName:"Mm","unitPattern-count-one":"{0} Mm","unitPattern-count-few":"{0} Mm","unitPattern-count-many":"{0} Mm","unitPattern-count-other":"{0} Mm"},"length-mile-scandinavian":{displayName:"smi","unitPattern-count-other":"{0} smi"},"length-point":{displayName:"pkt.","unitPattern-count-one":"{0} pkt.","unitPattern-count-few":"{0} pkt.","unitPattern-count-many":"{0} pkt.","unitPattern-count-other":"{0} pkt."},"length-solar-radius":{displayName:"R☉","unitPattern-count-one":"{0} R☉","unitPattern-count-few":"{0} R☉","unitPattern-count-many":"{0} R☉","unitPattern-count-other":"{0} R☉"},"light-lux":{displayName:"lx","unitPattern-count-other":"{0} lx"},"light-solar-luminosity":{displayName:"L☉","unitPattern-count-one":"{0} L☉","unitPattern-count-few":"{0} L☉","unitPattern-count-many":"{0} L☉","unitPattern-count-other":"{0} L☉"},"mass-metric-ton":{displayName:"t","unitPattern-count-other":"{0} t"},"mass-kilogram":{displayName:"kg","unitPattern-count-other":"{0} kg",perUnitPattern:"{0}/kg"},"mass-gram":{displayName:"g","unitPattern-count-other":"{0} g",perUnitPattern:"{0}/g"},"mass-milligram":{displayName:"mg","unitPattern-count-other":"{0} mg"},"mass-microgram":{displayName:"µg","unitPattern-count-other":"{0} µg"},"mass-ton":{displayName:"krótkie tony","unitPattern-count-one":"{0} krótka tona","unitPattern-count-few":"{0} krótkie tony","unitPattern-count-many":"{0} krótkich ton","unitPattern-count-other":"{0} krótkiej tony"},"mass-stone":{displayName:"st","unitPattern-count-other":"{0} st"},"mass-pound":{displayName:"funty","unitPattern-count-one":"{0} funt","unitPattern-count-few":"{0} funty","unitPattern-count-many":"{0} funtów","unitPattern-count-other":"{0} funta",perUnitPattern:"{0}/funt"},"mass-ounce":{displayName:"oz","unitPattern-count-other":"{0} oz",perUnitPattern:"{0}/oz"},"mass-ounce-troy":{displayName:"oz t","unitPattern-count-other":"{0} oz t"},"mass-carat":{displayName:"karaty","unitPattern-count-one":"{0} kt","unitPattern-count-few":"{0} kt","unitPattern-count-many":"{0} kt","unitPattern-count-other":"{0} kt"},"mass-dalton":{displayName:"daltony","unitPattern-count-one":"{0} Da","unitPattern-count-few":"{0} Da","unitPattern-count-many":"{0} Da","unitPattern-count-other":"{0} Da"},"mass-earth-mass":{displayName:"M⊕","unitPattern-count-one":"{0} M⊕","unitPattern-count-few":"{0} M⊕","unitPattern-count-many":"{0} M⊕","unitPattern-count-other":"{0} M⊕"},"mass-solar-mass":{displayName:"M☉","unitPattern-count-one":"{0} M☉","unitPattern-count-few":"{0} M☉","unitPattern-count-many":"{0} M☉","unitPattern-count-other":"{0} M☉"},"power-gigawatt":{displayName:"GW","unitPattern-count-other":"{0} GW"},"power-megawatt":{displayName:"MW","unitPattern-count-other":"{0} MW"},"power-kilowatt":{displayName:"kW","unitPattern-count-other":"{0} kW"},"power-watt":{displayName:"waty","unitPattern-count-other":"{0} W"},"power-milliwatt":{displayName:"mW","unitPattern-count-other":"{0} mW"},"power-horsepower":{displayName:"KM","unitPattern-count-one":"{0} KM","unitPattern-count-few":"{0} KM","unitPattern-count-many":"{0} KM","unitPattern-count-other":"{0} KM"},"pressure-millimeter-of-mercury":{displayName:"mm Hg","unitPattern-count-other":"{0} mm Hg"},"pressure-pound-per-square-inch":{displayName:"psi","unitPattern-count-other":"{0} psi"},"pressure-inch-hg":{displayName:"inHg","unitPattern-count-other":"{0} inHg"},"pressure-bar":{displayName:"bar","unitPattern-count-other":"{0} bar"},"pressure-millibar":{displayName:"mbar","unitPattern-count-other":"{0} mbar"},"pressure-atmosphere":{displayName:"atm","unitPattern-count-other":"{0} atm"},"pressure-pascal":{displayName:"Pa","unitPattern-count-other":"{0} Pa"},"pressure-hectopascal":{displayName:"hPa","unitPattern-count-other":"{0} hPa"},"pressure-kilopascal":{displayName:"kPa","unitPattern-count-one":"{0} kPa","unitPattern-count-few":"{0} kPa","unitPattern-count-many":"{0} kPa","unitPattern-count-other":"{0} kPa"},"pressure-megapascal":{displayName:"MPa","unitPattern-count-one":"{0} MPa","unitPattern-count-few":"{0} MPa","unitPattern-count-many":"{0} MPa","unitPattern-count-other":"{0} MPa"},"speed-kilometer-per-hour":{displayName:"km/h","unitPattern-count-other":"{0} km/h"},"speed-meter-per-second":{displayName:"m/s","unitPattern-count-other":"{0} m/s"},"speed-mile-per-hour":{displayName:"mile/h","unitPattern-count-one":"{0} mila/h","unitPattern-count-few":"{0} mile/h","unitPattern-count-many":"{0} mil/h","unitPattern-count-other":"{0} mili/h"},"speed-knot":{displayName:"w.","unitPattern-count-one":"{0} w.","unitPattern-count-few":"{0} w.","unitPattern-count-many":"{0} w.","unitPattern-count-other":"{0} w."},"temperature-generic":{displayName:"°","unitPattern-count-other":"{0}°"},"temperature-celsius":{displayName:"°C","unitPattern-count-one":"{0}°C","unitPattern-count-few":"{0}°C","unitPattern-count-many":"{0}°C","unitPattern-count-other":"{0}°C"},"temperature-fahrenheit":{displayName:"°F","unitPattern-count-other":"{0}°F"},"temperature-kelvin":{displayName:"K","unitPattern-count-other":"{0} K"},"torque-pound-foot":{displayName:"lbf⋅ft","unitPattern-count-one":"{0} lbf⋅ft","unitPattern-count-few":"{0} lbf⋅ft","unitPattern-count-many":"{0} lbf⋅ft","unitPattern-count-other":"{0} lbf⋅ft"},"torque-newton-meter":{displayName:"N⋅m","unitPattern-count-one":"{0} N⋅m","unitPattern-count-few":"{0} N⋅m","unitPattern-count-many":"{0} N⋅m","unitPattern-count-other":"{0} N⋅m"},"volume-cubic-kilometer":{displayName:"km³","unitPattern-count-other":"{0} km³"},"volume-cubic-meter":{displayName:"m³","unitPattern-count-other":"{0} m³",perUnitPattern:"{0}/m³"},"volume-cubic-centimeter":{displayName:"cm³","unitPattern-count-other":"{0} cm³",perUnitPattern:"{0}/cm³"},"volume-cubic-mile":{displayName:"mile sześc.","unitPattern-count-one":"{0} mila sześc.","unitPattern-count-few":"{0} mile sześc.","unitPattern-count-many":"{0} mil sześc.","unitPattern-count-other":"{0} mili sześc."},"volume-cubic-yard":{displayName:"jardy sześc.","unitPattern-count-one":"{0} jard sześc.","unitPattern-count-few":"{0} jardy sześc.","unitPattern-count-many":"{0} jardów sześc.","unitPattern-count-other":"{0} jarda sześc."},"volume-cubic-foot":{displayName:"stopy sześc.","unitPattern-count-one":"{0} stopa sześc.","unitPattern-count-few":"{0} stopy sześc.","unitPattern-count-many":"{0} stóp sześc.","unitPattern-count-other":"{0} stopy sześc."},"volume-cubic-inch":{displayName:"cale sześc.","unitPattern-count-one":"{0} cal sześc.","unitPattern-count-few":"{0} cale sześc.","unitPattern-count-many":"{0} cali sześc.","unitPattern-count-other":"{0} cala sześc."},"volume-megaliter":{displayName:"Ml","unitPattern-count-one":"{0} Ml","unitPattern-count-few":"{0} Ml","unitPattern-count-many":"{0} Ml","unitPattern-count-other":"{0} Ml"},"volume-hectoliter":{displayName:"hl","unitPattern-count-one":"{0} hl","unitPattern-count-few":"{0} hl","unitPattern-count-many":"{0} hl","unitPattern-count-other":"{0} hl"},"volume-liter":{displayName:"l","unitPattern-count-other":"{0} l",perUnitPattern:"{0}/l"},"volume-deciliter":{displayName:"dl","unitPattern-count-one":"{0} dl","unitPattern-count-few":"{0} dl","unitPattern-count-many":"{0} dl","unitPattern-count-other":"{0} dl"},"volume-centiliter":{displayName:"cl","unitPattern-count-one":"{0} cl","unitPattern-count-few":"{0} cl","unitPattern-count-many":"{0} cl","unitPattern-count-other":"{0} cl"},"volume-milliliter":{displayName:"ml","unitPattern-count-one":"{0} ml","unitPattern-count-few":"{0} ml","unitPattern-count-many":"{0} ml","unitPattern-count-other":"{0} ml"},"volume-pint-metric":{displayName:"półkwarty metr.","unitPattern-count-one":"{0} półkwarta metr.","unitPattern-count-few":"{0} półkwarty metr.","unitPattern-count-many":"{0} półkwart metr.","unitPattern-count-other":"{0} półkwarty metr."},"volume-cup-metric":{displayName:"ćwierćkwarty metr.","unitPattern-count-one":"{0} ćwierćkwarta metr.","unitPattern-count-few":"{0} ćwierćkwarty metr.","unitPattern-count-many":"{0} ćwierćkwart metr.","unitPattern-count-other":"{0} ćwierćkwarty metr."},"volume-acre-foot":{displayName:"akrostopy","unitPattern-count-one":"{0} akrostopa","unitPattern-count-few":"{0} akrostopy","unitPattern-count-many":"{0} akrostóp","unitPattern-count-other":"{0} akrostopy"},"volume-bushel":{displayName:"bu","unitPattern-count-other":"{0} bu"},"volume-gallon":{displayName:"gal","unitPattern-count-one":"{0} gal","unitPattern-count-few":"{0} gal","unitPattern-count-many":"{0} gal","unitPattern-count-other":"{0} gal",perUnitPattern:"{0}/gal"},"volume-gallon-imperial":{displayName:"gal ang.","unitPattern-count-one":"{0} gal ang.","unitPattern-count-few":"{0} gal ang.","unitPattern-count-many":"{0} gal ang.","unitPattern-count-other":"{0} gal ang.",perUnitPattern:"{0}/gal ang."},"volume-quart":{displayName:"kwarty","unitPattern-count-one":"{0} kwarta","unitPattern-count-few":"{0} kwarty","unitPattern-count-many":"{0} kwart","unitPattern-count-other":"{0} kwarty"},"volume-pint":{displayName:"półkwarty","unitPattern-count-one":"{0} półkwarta","unitPattern-count-few":"{0} półkwarty","unitPattern-count-many":"{0} półkwart","unitPattern-count-other":"{0} półkwarty"},"volume-cup":{displayName:"ćwierćkwarty","unitPattern-count-one":"{0} ćwierćkwarta","unitPattern-count-few":"{0} ćwierćkwarty","unitPattern-count-many":"{0} ćwierćkwart","unitPattern-count-other":"{0} ćwierćkwarty"},"volume-fluid-ounce":{displayName:"fl oz","unitPattern-count-one":"{0} fl oz","unitPattern-count-few":"{0} fl oz","unitPattern-count-many":"{0} fl oz","unitPattern-count-other":"{0} fl oz"},"volume-fluid-ounce-imperial":{displayName:"fl oz imp.","unitPattern-count-one":"{0} fl oz imp.","unitPattern-count-few":"{0} fl oz imp.","unitPattern-count-many":"{0} fl oz imp.","unitPattern-count-other":"{0} fl oz imp."},"volume-tablespoon":{displayName:"ł. stoł.","unitPattern-count-one":"{0} ł. stoł.","unitPattern-count-few":"{0} ł. stoł.","unitPattern-count-many":"{0} ł. stoł.","unitPattern-count-other":"{0} ł. stoł."},"volume-teaspoon":{displayName:"łyżeczki","unitPattern-count-one":"{0} łyżeczka","unitPattern-count-few":"{0} łyżeczki","unitPattern-count-many":"{0} łyżeczek","unitPattern-count-other":"{0} łyżeczki"},"volume-barrel":{displayName:"baryłki","unitPattern-count-one":"{0} bbl","unitPattern-count-few":"{0} bbl","unitPattern-count-many":"{0} bbl","unitPattern-count-other":"{0} bbl"},coordinateUnit:{displayName:"kierunek",east:"{0}E",north:"{0}N",south:"{0}S",west:"{0}W"}},"durationUnit-type-hm":{durationUnitPattern:"h:mm"},"durationUnit-type-hms":{durationUnitPattern:"h:mm:ss"},"durationUnit-type-ms":{durationUnitPattern:"m:ss"}}}}}];