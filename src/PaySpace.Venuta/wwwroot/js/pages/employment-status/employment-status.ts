/// <reference path="../../definitions.d.ts" />

LocalizationModule.useLocalization("EmploymentStatus");
const formatMessage = DevExpress.localization.formatMessage;

namespace EmploymentStatus {

    export let Settings: {
        PreviouslyTerminated: boolean;
        HasPendingInboxes: boolean;
        IsSouthAfrica: boolean;
        IsCanada: boolean;
        IsUnitedKingdom: boolean;
        ClearEmploymentDates: boolean;
        IdTypes: IdentityType[];
        TaxStatuses: TaxStatus[];
        NaturePersons: NatureOfPerson[];
        DentalBenefits: DentalBenefit[];
        LeaveBalance: number;
        LeaveBalanceValue: number;
        DentalBenefitId: number;
        EmploymentStatusId: number;
        TaxReferenceNumberValidationMessage: string;
        ChangedField: string;
        DefaultIdString: string;
        IdNumber: string;
        EnableStability: boolean;
        IsIreland: boolean;
        Urls: {
            TerminationReasonsUrl: string;
            CompanyRunsUrl: string;
            RecordUrl: string;
            GetBalanceUrl: string;
            ValidateIdNumberUrl: string;
            ValidateEmploymentStabilityUrl: string;
            IsActiveLeavePresentOnTerminationUrl: string;
            GetArrearsComponentsUrl: string;
            SeveranceDaysUrl: string;
        }
    };

    export const PersonWithID: string = "A";
    export let PersonWithNoID: string = "B";
    const AsylumNatureOfPerson: string = "M";
    export let FormData: EmploymentStatusModel;
    let selectedTab: string = "";

    interface EmploymentStatusModel {
        IdentityTypeId: number;
        IdNumber: string;
        NatureOfPersonId: number;
        TaxStatusId: number;
        EmploymentAction: EmploymentAction;
        TerminateEmployee: boolean;
        GroupJoinDate: Date;
        PermitIssued: number;
        PassportIssued: number;
        DeemedRecoveryMonthly: boolean;
        DeemedMonthlyRemuneration: number;
        Deemed75Indicator: boolean;
        PercentageAmount: PercentAmount;
        TempWorker: boolean;
        TerminationLeave: boolean;
        TerminationReasonId: number;
        CompanyRunId: number;
        PassportCountryId: number;
        AcceptedStabilities: string;
        TerminationPaySeverance: boolean;
        SeveranceDayId: number;
    }

    interface NatureOfPerson {
        natureOfPersonId: number;
        naturePersonCode: string;
    }

    export interface TaxStatus {
        taxStatusId: number;
        taxStatusCode: string;
    }

    export interface IdentityType {
        identityTypeId: number;
        identityCode: string;
        identityDescription: string;
    }

    interface DentalBenefit {
        dentalBenefitId: number;
        dentalBeneficCode: string;
        dentalBenefitDescription: string;
    }

    export enum EmploymentAction {
        Terminate = 2,
        ReInNoBreak = 3,
        ReInBreak = 5
    }

    enum PercentAmount {
        Percentage = 1,
        Amount = 2
    }

    export class ViewModel {
        private FormData: any;

        public DuplicateIds: Array<any>;
        public StabilityWarnings: Array<string>;
        public ArrearsComponents: Array<string>;

        protected get SelectedNatureOfPerson() {
            return Settings.NaturePersons.find(f => {
                return f.natureOfPersonId === FormData?.NatureOfPersonId
            })
        };

        public OnInitialized = (e: { component: DevExpress.ui.dxForm; element: any; }): void => {
            if (this.FormData == null) {
                this.FormData = e.component.option("formData");
            }

            if (!Settings.EmploymentStatusId && Settings.ClearEmploymentDates) {
                e.component.updateData("EmploymentDate", null);
                e.component.updateData("GroupJoinDate", null);
            }
        }

        public OnEditingStart = (e: { component: DevExpress.ui.dxDataGrid, key: number, cancel: boolean }): void => {
            e.cancel = true;
            window.location.href = `${EmploymentStatus.Settings.Urls.RecordUrl}/${e.key}#${selectedTab}`;
        }

        public OnRowRemoved = (e: { component: DevExpress.ui.dxDataGrid, key: number }): void => {
            if (e.key === EmploymentStatus.Settings.EmploymentStatusId) {
                window.location.href = EmploymentStatus.Settings.Urls.RecordUrl
            }
        }

        public OnCellPrepared = (e: { component: DevExpress.ui.dxDataGrid, columnIndex: number, rowType: string, data: { employmentStatusId: number }, cellElement: JQuery }): void => {
            if (e.cellElement.find(".dx-link-edit").length && e.rowType == "data") {
                if (e.data.employmentStatusId === EmploymentStatus.Settings.EmploymentStatusId) {
                    e.cellElement.find(".dx-link-edit").css("visibility", "hidden");
                }
            }
        }

        public OnSelectionChanged = (e: any): void => {
            // hide the footer save-button when on a custom form tab
            let hideSave = !!e.addedItems[0]?.hideSave;
            $("#btn-save").dxButton({
                visible: !hideSave
            });

            // Save the tab name to redirect using URL fragments
            selectedTab = e.addedItems[0]?.title;
        }

        protected GetFilteredIdentityTypes = (natureOfPersonCode: string, isSouthAfrica: boolean = false): IdentityType[] => {
            switch (natureOfPersonCode) {
                case "D":
                case "E":
                case "H":
                case "K":
                    return EmploymentStatus.Settings.IdTypes.filter(type => type.identityCode === "CC/Comp/Tr");
                case "M":
                    return EmploymentStatus.Settings.IdTypes.filter(type => type.identityCode === "Asylum");
                case "R":
                    return EmploymentStatus.Settings.IdTypes.filter(function (type) {
                        if (isSouthAfrica) {
                            return type.identityCode === "Refugee";
                        } else {
                            return type.identityCode === "ID" ||
                                type.identityCode === "Work" ||
                                type.identityCode === "Asylum" ||
                                type.identityCode === "Refugee";
                        }
                    });
                default:
                    return EmploymentStatus.Settings.IdTypes.filter(type => type.identityCode === "ID" ||
                        type.identityCode === "Work" || type.identityCode === "Others");
            }
        }

        protected ShowIdNumber = (natureOfPersonCode: string): boolean => {
            return natureOfPersonCode !== PersonWithNoID
        }

        private GetLeaveBalance = ()=> {
            const runId = FormData.CompanyRunId;
            if (runId > 0) {
                $.ajax({
                    url: EmploymentStatus.Settings.Urls.GetBalanceUrl,
                    dataType: "json",
                    async: false,
                    data: { CompanyRunId: runId },
                    success: function (result) {
                        EmploymentStatus.Settings.LeaveBalance = result.balance.toFixed(2);
                        EmploymentStatus.Settings.LeaveBalanceValue = result.value.toFixed(2);
                    }
                });
            }
        }

        private DisplayLeavePopupOnTermination = () => {
            const url = `${EmploymentStatus.Settings.Urls.IsActiveLeavePresentOnTerminationUrl}?terminationDate=${$("[name='TerminationDate']").val()}`;

            utils.http.get(url).then(response => {
                if (response) {
                    $("#active-leave-applications-popup").dxPopup("instance").show();
                }
                else {
                    this.ValidateIdNumber();
                }
            })
        }

        protected GetFilteredStatusesByNatureOfPersonCode = (natureOfPersonCode: string): TaxStatus[] => {
            let TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses;
            switch (natureOfPersonCode) {
                case "C":
                    TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses.filter(status => status.taxStatusCode === "Director" || status.taxStatusCode === "Directive" || status.taxStatusCode === "NonStand");
                    break;
                case "H":
                    if (EmploymentStatus.Settings.IsSouthAfrica) {
                        TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses.filter(status => status.taxStatusCode === "PersonalSe" || status.taxStatusCode === "Directive" || status.taxStatusCode === "Trust");
                    }
                    else{
                        TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses.filter(status => status.taxStatusCode === "PersonalSe");
                    }
                    break;
                case "K":
                    TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses.filter(status => status.taxStatusCode === "Trust");
                    break;
                case "B":
                    TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses.filter(status => status.taxStatusCode !== "PersonalSe" && status.taxStatusCode !== "Trust");
                    break;
                case "M":
                case "N":
                case "R":
                case "F":
                case "G":
                    if (EmploymentStatus.Settings.IsSouthAfrica) {
                        TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses.filter(status => status.taxStatusCode !== "Director" && status.taxStatusCode !== "PersonalSe" && status.taxStatusCode !== "Trust");
                    }
                    break;
                case "D":
                case "E":
                    if (EmploymentStatus.Settings.IsSouthAfrica) {
                        TaxStatusFilter = EmploymentStatus.Settings.TaxStatuses.filter(status => status.taxStatusCode !== "Director");
                    }
                    break;
                default:
                    break;
            }

            return TaxStatusFilter;
        }

        protected GetSelectedStatusByNatureOfPersonCode = (natureOfPersonCode: string): number => {
            const TaxStatusFilter = this.GetFilteredStatusesByNatureOfPersonCode(natureOfPersonCode);
            let selectedStatus: number = -1;
            if (TaxStatusFilter.length === 1 && TaxStatusFilter[0].taxStatusId === FormData.TaxStatusId) {
                selectedStatus = TaxStatusFilter[0].taxStatusId;
                FormData.TaxStatusId = selectedStatus;
            }

            if (natureOfPersonCode === "C" && (!FormData.TaxStatusId || FormData.TaxStatusId === -1 || !TaxStatusFilter.some(status => status.taxStatusId === FormData.TaxStatusId))) {
                selectedStatus = TaxStatusFilter.find(status => status.taxStatusCode === "Director").taxStatusId;
            }

            if (selectedStatus === -1 && TaxStatusFilter.some(status => status.taxStatusId === FormData.TaxStatusId)) {
                selectedStatus = FormData.TaxStatusId;
            }

            return selectedStatus;
        }

        protected SetSelectedIdentityType = (natureOfPersonId: number): void => {
            const natureOfPerson = Settings.NaturePersons.find(f => {
                return f.natureOfPersonId === natureOfPersonId
            });
            const natureCode = natureOfPerson != null ? natureOfPerson.naturePersonCode : null;
            const idIdentityType = EmploymentStatus.Settings.IdTypes.filter(type => type.identityCode === "ID")[0].identityTypeId;
            const defaultIdTypeNatureCodes = ["A", "C", "F", "G", "N"];
            let selectedIdType: number = -1;
            if (natureCode === PersonWithNoID) {
                selectedIdType = -1;
                FormData.IdentityTypeId = -1;
            } else {
                const IdTypeFilter = this.GetFilteredIdentityTypes(natureCode);
                if (IdTypeFilter.length === 1) {
                    selectedIdType = IdTypeFilter[0].identityTypeId;
                } else if (natureCode == "R" && EmploymentStatus.Settings.ChangedField === "NatureOfPersonId") {
                    selectedIdType = IdTypeFilter.filter(_ => _.identityCode == "Refugee")[0].identityTypeId;
                } else if (defaultIdTypeNatureCodes.includes(natureCode) && !IdTypeFilter.some(idtype => idtype.identityTypeId === FormData.IdentityTypeId) && EmploymentStatus.Settings.ChangedField === "NatureOfPersonId") {
                    selectedIdType = idIdentityType;
                } else {
                    selectedIdType = FormData.IdentityTypeId;
                }
            }

            if (FormData.IdentityTypeId != selectedIdType) {
                $("#form-EmploymentStatusViewModel").dxForm("instance").updateData("IdentityTypeId", selectedIdType);
            }
        }

        public OnFieldDataChanged = (e: { component: DevExpress.ui.dxForm, dataField: string, value: any }): void => {
            const repaintOn = ["TerminateEmployee", "IdentityTypeId", "NatureOfPersonId", "TaxStatusId", "EmploymentAction", "PermitIssued", "PassportIssued", "GroupJoinDate", "Deemed75Indicator",
                "DeemedRecoveryMonthly", "PercentageAmount", "CompanyRunId", ...(EmploymentStatus.Settings.IsCanada ? ["IdNumber"] : []), "TerminationPaySeverance"];

            if (JSON.stringify(FormData) === JSON.stringify(e.component.option("formData"))) {
                return;
            }

            if (!repaintOn.some(ro => ro === e.dataField)) {
                return;
            }

            // Get the virtical position of the document
            const htmlElement = document.querySelector('html');
            const verticalPosition = htmlElement.scrollTop;

            EmploymentStatus.Settings.ChangedField = e.dataField;

            switch (e.dataField) {
                case "NatureOfPersonId":
                    this.SetSelectedIdentityType(e.value);
                    break;
                case "CompanyRunId":
                    FormData.CompanyRunId = e.value;
                    this.GetLeaveBalance();
                    break;
                case "TerminateEmployee":
                    this.GetLeaveBalance();
                    $("#inbox-info").toggleClass("collapse", !e.value || !EmploymentStatus.Settings.HasPendingInboxes);
                    break;
                case "EmploymentAction":
                    //Check to show warning when NotEmployable employee gets 'reinstated' via radio button option on EmploymentAction 3 or 5
                    //Check to show warning when employee gets 'reinstated' with no break in service and isUnitedKingdom
                    const isReInAction = e.value === EmploymentAction.ReInNoBreak || e.value === EmploymentAction.ReInBreak;
                    const isNotReEmployable = FormData?.["NotReEmployable"];

                    this.ToggleReinstateWarning(isReInAction && isNotReEmployable);
                    this.ToggleReinstateWithBreakWarning(isReInAction && FormData && Settings.IsUnitedKingdom);
                    break;
            }

            // Repaint the form
            e.component.repaint();

            // Set the vertical position of the document to what it was before the repaint
            htmlElement.scrollTop = verticalPosition;
        }

        protected CustomizeItemInternal(e: DevExpress.ui.dxFormItem): void {
            this.InitFormData();

            const natureOfPerson = this.SelectedNatureOfPerson;
            const natureCode = natureOfPerson != null ? natureOfPerson.naturePersonCode : null;
            const selectedStatus = this.GetSelectedStatusByNatureOfPersonCode(natureCode);
            const taxStatus = Settings.TaxStatuses.find(f => {
                return f.taxStatusId === selectedStatus
            });
            const statusCode = taxStatus != null ? taxStatus.taxStatusCode : null;

            if (FormData.IdentityTypeId <= 0 && EmploymentStatus.Settings.IdTypes.length) {
                FormData.IdentityTypeId = EmploymentStatus.Settings.IdTypes[0].identityTypeId;
            }

            const idType = EmploymentStatus.Settings.IdTypes.find(f => {
                return f.identityTypeId === FormData.IdentityTypeId
            });

            let idCode: string = "ID";
            let idDescription: string;
            if (idType) {
                idCode = idType.identityCode;
                idDescription = formatMessage("IdNumber", idType.identityDescription);

                if (natureCode !== PersonWithNoID && idType.identityCode == "ID"
                    && (EmploymentStatus.Settings.ChangedField === "NatureOfPersonId"
                        || EmploymentStatus.Settings.ChangedField === "IdentityTypeId"
                        || !EmploymentStatus.Settings.ChangedField) && !FormData.IdNumber) {
                    const birthdateString = EmploymentStatus.Settings.DefaultIdString;
                    if (!EmploymentStatus.Settings.IsCanada) {
                        $("#form-EmploymentStatusViewModel").dxForm("instance").updateData("IdNumber", birthdateString);
                    }
                }
            }

            switch (e.name) {
                case "group-Termination":
                    e.visible = FormData.TerminateEmployee || FormData.EmploymentAction === EmploymentAction.Terminate;
                    this.SetAllFormGroupItemsReadOnly(e, EmploymentStatus.Settings.PreviouslyTerminated);
                    break;
                case "group-Employment":
                    e.visible = !FormData.TerminateEmployee;
                    this.SetAllFormGroupItemsReadOnly(e, FormData.TerminateEmployee || FormData.EmploymentAction === EmploymentAction.ReInNoBreak);
                    break;
                case "group-Identification":
                    e.visible = !FormData.TerminateEmployee;
                    this.SetAllFormGroupItemsReadOnly(e, FormData.EmploymentAction === EmploymentAction.ReInNoBreak);
                    break;
                case "LeaveBalance":
                    if (EmploymentStatus.Settings.LeaveBalance) {
                        e.visible = true;
                        e.template = '<label class="dx-field-item-label-text">' + Localization["EmploymentStatus"].lblLeaveBalance + '</label><br />' +
                            '<div class="form-control-static">' +
                            EmploymentStatus.Settings.LeaveBalance +
                            '</div>';
                    }
                    break;
                case "LeaveBalanceValue":
                    if (EmploymentStatus.Settings.LeaveBalanceValue) {
                        e.visible = true;
                        e.template = '<label class="dx-field-item-label-text">' + Localization["EmploymentStatus"].lblLeaveBalanceValue + '</label><br />' +
                            '<div class="form-control-static">' +
                            EmploymentStatus.Settings.LeaveBalanceValue +
                            '</div>';
                    }
                    break;
            }

            if (e.dataField === "NatureOfPersonId") {
                e.editorOptions =
                {
                    dataSource: new DevExpress.data.DataSource({
                        store: EmploymentStatus.Settings.NaturePersons,
                        key: "natureOfPersonId"
                    }),
                    displayExpr: "naturePersonDescription",
                    valueExpr: "natureOfPersonId",
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                };
            }

            if (e.dataField === "IdentityTypeId") {
                e.visible = natureCode !== PersonWithNoID;
                e.isRequired = natureCode !== PersonWithNoID;
                if (natureCode === PersonWithNoID) {
                    FormData.IdentityTypeId = -1;
                } else {
                    const IdTypeFilter = this.GetFilteredIdentityTypes(natureCode, EmploymentStatus.Settings.IsSouthAfrica);

                    $.extend(e.editorOptions, {
                        dataSource: new DevExpress.data.DataSource({
                            store: IdTypeFilter,
                            key: "identityTypeId"
                        }),
                        displayExpr: "identityDescription",
                        valueExpr: "identityTypeId",
                        disabled: IdTypeFilter.length === 1,
                        readOnly: EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                    });
                }
            }

            if (e.dataField === "TaxStatusId") {
                switch (natureCode) {
                    case "H":
                        if (!EmploymentStatus.Settings.IsSouthAfrica) {
                            e.disabled = true;
                        }
                        break;
                    case "K":
                        e.disabled = true;
                        break;
                    default:
                        break;
                }

                $.extend(e.editorOptions, {
                    dataSource: new DevExpress.data.DataSource({
                        store: this.GetFilteredStatusesByNatureOfPersonCode(natureCode),
                        key: "taxStatusId",
                        sort: "taxStatusCode"
                    }),
                    displayExpr: "taxStatusDescription",
                    valueExpr: "taxStatusId",
                    value: selectedStatus === -1 ? null : selectedStatus
                });
            }

            if (e.dataField === "GroupJoinDate") {
                e.disabled = FormData.TerminateEmployee || FormData.EmploymentAction === EmploymentAction.Terminate;
                $.extend(e.editorOptions, {
                    readOnly: (EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction !== EmploymentAction.ReInBreak) || FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                });
            }

            // Tax Status optional fields
            if (e.dataField === "TaxDirectiveNumber") {
                e.visible = statusCode === "Directive" || statusCode === "IndConDir" || statusCode === "NEDTD" || statusCode === "NonExMuEm" || statusCode === "FixedRate";
            }

            if (e.dataField === "PercentageAmount") {
                e.visible = statusCode === "Directive" || statusCode === "IndConDir" || statusCode === "NEDTD" || statusCode === "NonExMuEm" || statusCode === "FixedRate" || (statusCode === "Director" && FormData.DeemedRecoveryMonthly === true && !FormData.Deemed75Indicator);
            }

            if (e.dataField === "Percentage") {
                e.visible = FormData.PercentageAmount === PercentAmount.Percentage && (statusCode === "Directive" || (statusCode === "Director" && !FormData.Deemed75Indicator) || statusCode === "IndConDir" || statusCode === "NEDTD" || statusCode === "NonExMuEm" || statusCode === "FixedRate");
            }

            if (e.dataField === "Amount") {
                e.visible = FormData.PercentageAmount === PercentAmount.Amount && (statusCode === "Directive" || (statusCode === "Director" && !FormData.Deemed75Indicator) || statusCode === "IndConDir" || statusCode === "NEDTD" || statusCode === "NonExMuEm" || statusCode === "FixedRate");
            }

            if (e.dataField === "Irp30") {
                e.visible = statusCode === "Labour";
            }

            if (e.dataField === "TempWorker") {
                $.extend(e.editorOptions, {
                    value: e.visible ? !!FormData.TempWorker : null
                });
            }

            if (e.dataField === "TerminationDate") {
                $.extend(e.editorOptions, {
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated
                });
            }

            if (e.dataField === "TerminationReasonId") {
                $.extend(e.editorOptions, {
                    dataSource: `${EmploymentStatus.Settings.Urls.TerminationReasonsUrl}?taxStatusId=${FormData.TaxStatusId}`,
                    displayExpr: "text",
                    valueExpr: "value",
                    value: FormData.TerminationReasonId === -1 ? null : FormData.TerminationReasonId,
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated
                });
            }

            if (e.dataField === "CompanyRunId") {
                $.extend(e.editorOptions, {
                    dataSource: {
                        store: DevExpress.data.AspNet.createStore({
                            loadUrl: EmploymentStatus.Settings.Urls.CompanyRunsUrl,
                            key: "value"
                        }),
                    },
                    displayExpr: "text",
                    valueExpr: "value",
                    value: FormData.CompanyRunId,
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated
                });
            }

            if (e.dataField === "TerminationLeave") {
                $.extend(e.editorOptions, {
                    value: !!FormData.TerminationLeave,
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated
                });
            }

            // Identity optional fields
            if (e.dataField === "PassportCountryId") {
                const visible = natureCode !== PersonWithNoID;
                if (visible) {
                    $.extend(e.editorOptions, {
                        value: FormData.PassportCountryId === -1 ? null : FormData.PassportCountryId,
                        readOnly: EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                    });
                }
                e.isRequired = idCode === "Work" || ((idCode === "Refugee" || idCode == "Asylum") && natureCode === "A");
                e.visible = visible;
            }

            if (e.dataField === "PermitIssued" || e.dataField === "PermitExpiry") {
                e.visible = idCode === "Work" && natureCode != PersonWithNoID;
            }

            if (e.dataField === "PassportNumber") {
                const notRequiredCodes = ["C", "F", "G", "N", "R"];
                if (idCode === "Work" && !notRequiredCodes.some(codes => codes === natureCode)) {
                    e.isRequired = EmploymentStatus.Settings.IsSouthAfrica;
                    if (Localization["EmploymentStatus"].lblPassportNumberIsRequired && EmploymentStatus.Settings.IsSouthAfrica) {
                        e.validationRules = [{
                            type: 'required',
                            message: Localization["EmploymentStatus"].lblPassportNumberIsRequired
                        }];
                    }
                }
            }

            if (e.dataField === "PassportNumber" || e.dataField === "PassportIssued" || e.dataField === "PassportExpiry") {
                e.visible = natureCode !== PersonWithNoID;
                $.extend(e.editorOptions, {
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                });
            }

            if (e.dataField === "EmploymentDate") {
                e.disabled = FormData.GroupJoinDate === null || FormData.EmploymentAction === EmploymentAction.Terminate;
                $.extend(e.editorOptions, {
                    min: new Date(FormData.GroupJoinDate),
                    readOnly: (EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction !== EmploymentAction.ReInBreak) || FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                });
            }

            if (e.dataField === "AdditionalDate" || e.dataField === "AdditionalDate1" || e.dataField === "TaxStatusId" || e.dataField === "TaxReferenceNumber" || e.dataField === "ReferenceNumber") {
                $.extend(e.editorOptions, {
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                });
            }

            if (e.dataField === "NotReEmployable") {
                $.extend(e.editorOptions, {
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                });
                e.cssClass = EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak ? "not-allowed-cursor" : "";
            }

            // Limit the dates to not be older than 150 years
            if (e.dataField === "AdditionalDate" || e.dataField === "AdditionalDate1" || e.dataField === "GroupJoinDate") {
                let date = new Date();
                $.extend(e.editorOptions, {
                    min: date.setFullYear(date.getFullYear() - 150),
                });
            }

            if (e.dataField === "PermitIssued" || e.dataField === "PassportIssued" || e.dataField === "PermitExpiry" || e.dataField === "PassportExpiry") {
                $.extend(e.editorOptions, {
                    width: "100%",
                });
            }

            if (e.dataField === "PermitExpiry") {
                e.disabled = FormData.PermitIssued === null;
            }

            if (e.dataField === "PassportExpiry") {
                e.disabled = FormData.PassportIssued === null;
            }

            if (e.dataField === "IdNumber") {
                const idNumberNotRequiredOn = ["Work", "Refugee", "Others"];
                e.visible = this.ShowIdNumber(natureCode);
                e.isRequired = !idNumberNotRequiredOn.some(s => s == idCode);
                e.label.text = idDescription;
                e.editorOptions = {
                    placeholder: idDescription
                };
                if (natureCode == AsylumNatureOfPerson) {
                    e.validationRules = [{
                        type: 'required',
                        message: Localization["EmploymentStatus"].errAsylumIdNumberRequired,
                    }]
                }
                $.extend(e.editorOptions, {
                    readOnly: EmploymentStatus.Settings.PreviouslyTerminated && FormData.EmploymentAction === EmploymentAction.ReInNoBreak
                });
            }

            if (e.dataField === "IdNumberExpiryDate") {
                // Canada Temp Social Insurance Number prefix: '9'
                e.visible = natureCode !== PersonWithNoID && FormData.IdNumber?.startsWith('9');
                e.isRequired = true;
            }

            if (e.dataField === "DentalBenefitId") {
                e.editorOptions =
                {
                    dataSource: new DevExpress.data.DataSource({
                        store: EmploymentStatus.Settings.DentalBenefits,
                        key: "dentalBenefitCode"
                    }),
                    displayExpr: "dentalBenefitDescription",
                    valueExpr: "dentalBenefitCode",
                    value: `${EmploymentStatus.Settings.DentalBenefitId}`
                };
            }

            if (e.name === "LeaveBalance" && EmploymentStatus.Settings.LeaveBalance) {
                e.visible = true;
                e.template = '<label class="dx-field-item-label-text">' + Localization["EmploymentStatus"].lblLeaveBalance + '</label><br />' +
                    '<div class="form-control-static">' +
                    EmploymentStatus.Settings.LeaveBalance +
                    '</div>';
            }
        }

        protected InitFormData() {
            if (FormData == null || JSON.stringify(FormData) !== JSON.stringify($("#form-EmploymentStatusViewModel").dxForm("instance").option("formData"))) {
                FormData = { ...$("#form-EmploymentStatusViewModel").dxForm("instance").option("formData")};
            }
        }

        private SetAllFormGroupItemsReadOnly = (groupItem: DevExpress.ui.dxFormGroupItem, readOnly: boolean) => {
            if (groupItem.items) {
                groupItem.items.forEach((item: DevExpress.ui.dxFormSimpleItem) => {
                    $.extend(item.editorOptions, {
                        readOnly
                    });
                });
            }
        }

        private ToggleReinstateWarning = (show: boolean): void => {
            $("#lblNotReEmployableWarning").toggle(show);
        }

        private ToggleReinstateWithBreakWarning = (show: boolean): void => {
            $("#lblReinstateWithBreakNotAllowed").toggle(show);
        }

        public OnUpdateClicked = (e: any) => {
            e.event.preventDefault();

            this.ValidateTerminationConditions();
        }

        public OnArrearsComponentsTerminationAccepted = () => {
            $("#arrears-components-popup").dxPopup("instance").hide();

            this.DisplayLeavePopupOnTermination();
        }

        private ValidateTerminationConditions = (): void => {
            let terminationDate = $("[name='TerminationDate']").val();
            let terminationReasonId = $("[name='TerminationReasonId']").val();

            if (FormData.TerminateEmployee && FormData.CompanyRunId && terminationDate && terminationReasonId) {
                this.DisplayArrearsComponentsOnTermination();
            }
            else {
                this.ValidateIdNumber();
            }
        }

        public OnIdNumberValidationAccepted = () =>  {
            $("#validate-id-number-popup").dxPopup("instance").hide();
            this.ValidateStability();
        }

        public OnStabilityValidationAccepted = () => {
            $("[name='AcceptedStabilities']").val(this.StabilityWarnings)
            this.SaveChanges();
        }

        public onActiveLeaveApplicationsAccepted = () => {
            $("#active-leave-applications-popup").dxPopup("instance").hide();
            this.ValidateIdNumber();
        }

        public ValidateIdNumber = (): void => {
            const idNumber = $("[name='IdNumber']").val();
            if (idNumber && Settings.IdNumber != idNumber && !Settings.IsIreland) {
                const url = Settings.Urls.ValidateIdNumberUrl + "?IdNumber=" + idNumber;
                utils.http.post(url).then(response => {
                    this.DuplicateIds = response;

                    if (response.length > 0) {
                        $("#validate-id-number-popup").dxPopup("instance").show();
                    } else {
                        this.ValidateStability();
                    }
                });
            } else {
                this.ValidateStability();
            }
        }

        public ValidateStability = () => {
            if (Settings.EnableStability && $("[name='TerminateEmployee']").val() == 'true') {
                let isValid = $("#form-EmploymentStatusViewModel").dxForm("instance").validate().isValid;
                if (!isValid) {
                    return;
                }

                const url = `${Settings.Urls.ValidateEmploymentStabilityUrl}?effectiveDate=${$("[name='TerminationDate']").val()}`;

                utils.http.post(url).then(response => {
                    if (response.length > 0) {
                        this.StabilityWarnings = response;
                        $("#validate-stability-popup").dxPopup("instance").show();
                    } else {
                        this.SaveChanges();
                    }
                })
            } else {
                this.SaveChanges();
            }
        }

        public SaveChanges = (): void => {
            $("#validate-id-number-popup").dxPopup("instance").hide();

            if (Settings.EnableStability) {
                $("#validate-stability-popup").dxPopup("instance").hide();
            }

            // repaint detaches the custom field hidden input from the parent form.
            // this method re-attaches them
            this.UpdateFormCustomFieldValues();

            $("#form-EmploymentStatusViewModel").closest("form").trigger("submit");
        }

        public CancelSubmit = (e: any): void => {
            $("#active-leave-applications-popup").dxPopup("instance").hide();
            $("#validate-id-number-popup").dxPopup("instance").hide();
            $("#arrears-components-popup").dxPopup("instance").hide();

            if (Settings.EnableStability) {
                $("#validate-stability-popup").dxPopup("instance").hide();
            }
        }

        public CustomizeItem = (e: DevExpress.ui.dxFormItem): void => {
            this.CustomizeItemInternal(e);
        }

        private UpdateFormCustomFieldValues = () => {
            const form = $("#form-EmploymentStatusViewModel").closest("form");
            const customFieldsRoot = $("custom-form-fields")[0];
            if (!customFieldsRoot) {
                return;
            }
            const shadowRoot = customFieldsRoot.shadowRoot;
            const hiddenInputs = shadowRoot.querySelectorAll('input[type="hidden"][name^="CustomFields["]');
            hiddenInputs.forEach(input => {
                form.append(input);  // Append hidden input back to form before submit
            });
        }

        private DisplayArrearsComponentsOnTermination = () => {
            const url = `${Settings.Urls.GetArrearsComponentsUrl}?companyRunId=${FormData.CompanyRunId}`;
            utils.http.get(url).then(response => {
                if (response.length > 0) {
                    this.ArrearsComponents = response
                    $("#arrears-components-popup").dxPopup("instance").show();
                }
                else {
                    this.DisplayLeavePopupOnTermination();
                }
            });
        }
    }

    export let App = new ViewModel();
}