/// <reference path="../definitions.d.ts" />

interface Window {
    waiting: string[];
    approved: string[];
    holidays: string[];
    disabled: string[];
}

namespace Leave {
    enum LeaveType {
        Annual = 1,
        Sick = 2,
        FamilyResponsibility = 3,
        Study = 4,
        Special = 5
    }

    enum LeaveStatus {
        Waiting = 3
    }

    enum RunStatus {
        Closed = 1
    }

    export let Urls: {
        postBack: string;
        edit: string;
        new: string;
        buckets: string;
        reasons: string;
        record: string;
        view: string;
    };

    export let Selectors: {
        CompanyLeaveSetupId: string;
    };

    export let Config: {
        InactiveWorkingDays: number[];
        MinDays: number;
        MinHours: number;
        MaxDays: number;
        MaxHours: number;
        WorkingHours: number;
        ConsecutiveDays: number;
        TotalDaysSell: number;
        DisabledStartDays: number[];
        DisabledStartDates: string[];
    };

    export let Settings: {
        FormData: any;
        DeltaFormData: any;
        HistoryGrid: DevExpress.ui.dxDataGrid;
        Form: DevExpress.ui.dxForm;
        ShowBuckets: boolean;
        ShowReasons: boolean;
        ShowHours: boolean;
        AllowTimeOfDay: boolean;
        IsAdminUser: boolean;
    }

    export function getColCount() {
        if (window.innerWidth < 992)
            return 2;
        return 3;
    }

    class ViewModel {
        public OnInitialized = (e: { component: DevExpress.ui.dxForm; }): void => {
            Settings.Form = e.component;
            Settings.FormData = e.component.option("formData");

            let data = e.component.option("formData");
            if (data.LeaveType == LeaveType.Special && data.CompanyLeaveSetupId > 0) {
                data.LeaveType = data.CompanyLeaveSetupId;
            }
        }

        public async OnFieldDataChanged(e: { component: DevExpress.ui.dxForm, dataField: string, name: string, value: any }) {
            if (e.component.getEditor(e.dataField) != null) {
                Settings.DeltaFormData[e.dataField] = e.value;

                if (e.dataField === "CompanyLeaveSetupId") {
                    if (Settings.FormData.LeaveAdjustmentId == 0) {
                        Settings.FormData.StartDate = null;
                        Settings.FormData.EndDate = null;

                        delete Settings.DeltaFormData.StartDate;
                        delete Settings.DeltaFormData.EndDate;
                    }

                    App.Update();
                }

                if (e.dataField === "SellVacationDays") {
                    Settings.FormData.TotalDaysSell = e.value ? Config.TotalDaysSell : null;
                    App.Update();
                }

                if (e.dataField === "StartDate") {
                    Settings.FormData.EndDate = null;

                    delete Settings.DeltaFormData.EndDate;

                    App.Update().then(() => Settings.Form.updateDimensions());
                }

                if (e.dataField === "EndDate") {
                    if (!App.IsDateDisabled(e.value, false)) {
                        App.Update();
                    } else {
                        e.component.updateData(Settings.FormData);
                    }
                }

                if (e.dataField === "Days") {
                    const maxDays = Math.ceil(Math.abs(Settings.FormData.EndDate - Settings.FormData.StartDate) / (1000 * 60 * 60 * 24));
                    Settings.ShowHours = e.value < maxDays;

                    if (0 < e.value && e.value < 1 && Settings.AllowTimeOfDay) {
                        if (!Settings.FormData.TimeOfDayId) {
                            Settings.FormData.TimeOfDayId = 1;
                            Settings.DeltaFormData.TimeOfDayId = 1;
                            e.component.updateData(Settings.FormData);
                        }
                    }
                    else {
                        if (Settings.FormData.TimeOfDayId) {
                            Settings.FormData.TimeOfDayId = null;
                            Settings.DeltaFormData.TimeOfDayId = null;
                            e.component.updateData(Settings.FormData);
                        }
                    }

                    e.component.repaint();
                }

                if (e.dataField === "Hours") {
                    e.component.repaint();
                }

                if (e.dataField === "SkipWorkflow" || e.dataField === "SkipValidation") {
                    App.Update();
                }

                if (e.dataField === "Status") {
                    if (e.value != LeaveStatus.Waiting) {
                        Settings.FormData.SkipWorkflow = true
                    }
                    else {
                        Settings.FormData.SkipWorkflow = false
                    }

                    e.component.updateData(Settings.FormData);
                    e.component.repaint();
                }

                if (e.dataField === "AutoGenerateRun") {
                    e.component.repaint();
                }
            }
        }

        public CustomizeItem(e: DevExpress.ui.dxFormItem): void {
            if (Settings.DeltaFormData == null) {
                Settings.DeltaFormData = new Object;
            }

            if (Settings.FormData.LeaveType > 0 && e.fieldGroup) {
                const groupItem = e as DevExpress.ui.dxForm.GroupItem;
                groupItem.visible = false;

                const leaveType = Settings.FormData.LeaveType;
                e.visible = e.fieldGroup === LeaveType[leaveType > 5 ? LeaveType.Special : leaveType] || e.fieldGroup == (leaveType > 5 ? LeaveType.Special : leaveType) || e.fieldGroup === "none";
            }
            else if (e.fieldGroup) {
                e.visible = false;
            }

            if (e.dataField === "LeaveType") {
                $.extend(e.editorOptions,
                    {
                        onValueChanged: App.OnLeaveTypeChanged,
                        readOnly: Settings.FormData.LeaveAdjustmentId > 0
                    });
            }

            if (e.dataField === "TotalDaysSell") {
                e.visible = Settings.FormData.SellVacationDays;
            }

            if (e.dataField === "CompanyLeaveSetupId") {
                $.extend(e.editorOptions, {
                    dataSource: DevExpress.data.AspNet.createStore({
                        key: "value",
                        loadUrl: Urls.buckets,
                        loadParams: {
                            LeaveType: Settings.FormData.LeaveType > LeaveType.Special ? LeaveType.Special : Settings.FormData.LeaveType
                        },
                        onBeforeSend: App.BeforeSend
                    })
                });

                e.visible = Settings.ShowBuckets && Settings.FormData.LeaveType > 0 && Settings.FormData.LeaveType <= 5
            }

            if (e.dataField === "LeaveReasonId") {
                e.visible = Settings.ShowReasons && Settings.FormData.LeaveType > 0;

                $.extend(e.editorOptions, {
                    dataSource: App.CreateDataSource(Urls.reasons, {
                        LeaveType: Settings.FormData.LeaveType > LeaveType.Special ? LeaveType.Special : Settings.FormData.LeaveType,
                        CompanyLeaveSetupId: Settings.FormData.CompanyLeaveSetupId
                    }),
                });
            }

            if (e.dataField === "StartDate") {
                $.extend(e.editorOptions, {
                    pickerType: "calendar",
                    disabledDates: function (e: { view: string, date: Date }) {
                        return e.view === "month" && Leave.App.IsDateDisabled(e.date, true);
                    },
                    onContentReady: function (e: any) {
                        setTimeout(function () {
                            e.component.option('inputAttr', { readonly: true });
                        });
                    },
                    onOpened: App.OnCalendarOpened,
                    dateSerializationFormat: "yyyy-MM-dd",
                    validationRules: [
                        {
                            type: 'custom',
                            validationCallback: function (options: { value: Date }) {
                                return !App.IsDateDisabled(new Date(options.value), true);
                            }
                        }]
                });

                e.visible = Settings.FormData.LeaveType > 0;
            }

            if (e.dataField === "EndDate") {
                $.extend(e.editorOptions, {
                    pickerType: "calendar",
                    min: Settings.FormData.StartDate,
                    disabledDates: function (e: { view: string, date: Date }) {
                        return e.view === "month" && Leave.App.IsDateDisabled(e.date, false);
                    },
                    dateSerializationFormat: "yyyy-MM-dd",
                    onOpened: Leave.App.OnCalendarOpened,
                    onContentReady: function (e: any) {
                        setTimeout(function () {
                            e.component.option('inputAttr', { readonly: true });
                        });
                    }
                })

                e.visible = Settings.FormData.StartDate != null;
            }

            if (e.dataField === "Days") {
                if (Config.MinDays) {
                    $.extend(e.editorOptions, {
                        min: Config.MinDays,
                    });
                }

                if (Config.MaxDays) {
                    $.extend(e.editorOptions, {
                        max: Config.MaxDays,
                    });
                }
            }

            if (e.dataField === "Hours") {
                if (Config.MaxHours) {
                    $.extend(e.editorOptions, {
                        max: Config.MaxHours,
                    });
                }

                if (Settings.ShowHours) {
                    $.extend(e.editorOptions, {
                        max: Config.MaxHours,
                        min: Config.MinHours
                    });

                    e.visible = true;
                }
            }

            if (e.dataField === "TimeOfDayId") {
                e.visible = Settings.AllowTimeOfDay && 0 < Settings.FormData.Days && Settings.FormData.Days < 1;
                e.isRequired = Settings.AllowTimeOfDay && 0 < Settings.FormData.Days && Settings.FormData.Days < 1;
            }

            if (e.dataField === "SkipWorkflow") {
                e.editorOptions.readOnly = Settings.FormData.Status !== LeaveStatus.Waiting
            }

            if (e.dataField == "CompanyRunId") {
                e.visible = Settings.FormData.LeaveAdjustmentId > 0 || !Settings.FormData.AutoGenerateRun;
            }

            if (e.dataField == "AutoGenerateRun") {
                e.visible = Settings.FormData.LeaveAdjustmentId == 0;
            }
        }

        public OnCellPrepared(e: { component: DevExpress.ui.dxDataGrid, columnIndex: number, rowType: string, data: any, cellElement: JQuery }): void {
            if (Settings.HistoryGrid == null) {
                Settings.HistoryGrid = e.component;
            }

            if (e.cellElement.find(".dx-link-edit").length && e.rowType === "data") {
                if (e.data.runStatus == RunStatus.Closed && e.data.status != LeaveStatus.Waiting) {
                    e.cellElement.find(".dx-link-edit").css("visibility", "hidden");
                }

                if ((e.data.runStatus == RunStatus.Closed && e.data.status != LeaveStatus.Waiting) || e.data.componentEmployeeId) {
                    e.cellElement.find(".dx-link-delete").css("visibility", "hidden");
                    e.cellElement.find(".dx-link-cancel").css("visibility", "hidden");
                }

                if (Settings.FormData != null && e.data.leaveAdjustmentId === Settings.FormData.LeaveAdjustmentId ) {
                    e.cellElement.find(".dx-link-edit").css("visibility", "hidden");
                }
            }
        }

        public OnLeaveTypeChanged(e: { component: DevExpress.ui.dxLookup, value: number, previousValue: number }): void {
            if (e.value > LeaveType.Special) {
                let value = e.component.option("selectedItem");
                if (value != null) {
                    $.extend(Settings.FormData,
                        {
                            CompanyLeaveSetupId: value.companyLeaveSetupId,
                        });
                    $.extend(Settings.DeltaFormData,
                        {
                            CompanyLeaveSetupId: value.companyLeaveSetupId,
                        });
                }
            }
            else {
                delete Settings.FormData.CompanyLeaveSetupId;
                delete Settings.DeltaFormData.CompanyLeaveSetupId
            }

            if (e.previousValue != LeaveType.Special) {
                Settings.FormData.StartDate = null;
                Settings.FormData.EndDate = null;

                delete Settings.DeltaFormData.StartDate;
                delete Settings.DeltaFormData.EndDate;
                App.Update();
            }
        }

        public OnAttachmentChanged(e: { component: DevExpress.ui.dxFileUploader, value: File[] }): void {
            let name = e.value[0].name;
            if (e.value && e.value.length > 0) {
                let reader = new FileReader();
                reader.read(e.value).done(function (data) {
                    $.extend(Settings.FormData, {
                        AttachmentName: name,
                        Attachment: utils.base64.raw(data)
                    });

                    $.extend(Settings.DeltaFormData, {
                        AttachmentName: name,
                        Attachment: utils.base64.raw(data)
                    });
                });
            }
            else {
                delete Settings.FormData.AttachmentName;
                delete Settings.FormData.Attachment;

                delete Settings.DeltaFormData.AttachmentName;
                delete Settings.DeltaFormData.Attachment;
            }
        }

        public OnCalcHalfDayClicked(e: any): void {
            let halfHours = Leave.Config.WorkingHours / 2;
            Settings.Form.updateData("Hours", Math.min(halfHours, Leave.Config.MaxHours));
        }

        public CreateDataSource(url: string, loadParams: any) {
            return DevExpress.data.AspNet.createStore({
                key: "value",
                loadUrl: url,
                loadParams: loadParams,
            })
        }

        public BeforeSend(_method:string, options:any){
            if (Settings.FormData.StartDate) {
                $.extend(options.data, {
                    effectiveDate: Settings.FormData.StartDate
                });
            }
        }

        private disable(day: string): void {
            $(`td.dx-calendar-cell[aria-label *= '${day}']`).addClass("dx-calendar-empty-cell").on("click",() => false);
        };

        private dayOfWeek(): void {
            if (Leave.Config.InactiveWorkingDays.indexOf(1) > -1) {
                this.disable("Monday");
            }

            if (Leave.Config.InactiveWorkingDays.indexOf(2) > -1) {
                this.disable("Tueday");
            }

            if (Leave.Config.InactiveWorkingDays.indexOf(3) > -1) {
                this.disable("Wednesday");
            }

            if (Leave.Config.InactiveWorkingDays.indexOf(4) > -1) {
                this.disable("Thursday");
            }

            if (Leave.Config.InactiveWorkingDays.indexOf(5) > -1) {
                this.disable("Friday");
            }

            if (Leave.Config.InactiveWorkingDays.indexOf(6) > -1) {
                this.disable("Saturday");
            }

            if (Leave.Config.InactiveWorkingDays.indexOf(0) > -1) {
                this.disable("Sunday");
            }
        };

        private style(): void {
            let _this = this;

            $("td.dx-calendar-cell").each(function () {
                let date = this.getAttribute("data-value");
                let d = _this.FormatDate(_this.GetDate(new Date(date)));

                if (window.waiting.indexOf(d) > -1) {
                    $(this).addClass("reserved reserved-waiting");
                }

                if (window.approved.indexOf(d) > -1) {
                    $(this).addClass("reserved reserved-approved");
                }

                if (window.holidays.indexOf(d) > -1) {
                    $(this).addClass("reserved reserved-holiday");
                }
            });
        };

        private isDayOfWeekDisabled(dayOfWeek: number): boolean {
            return Leave.Config.InactiveWorkingDays.indexOf(dayOfWeek) > -1;
        }

        public OnCalendarOpened = (): void => {
            $(".dx-calendar-navigator-previous-view,.dx-calendar-navigator-next-view").dxButton().on("click", () => {
                this.dayOfWeek();
                this.style();
            });

            this.dayOfWeek();
            this.style();
        }

        public IsDateDisabled = (date: Date, isStartDate: boolean): boolean => {
            let setting = window.waiting.concat(window.approved);
            let d = this.GetDate(date);
            if (Leave.Config.ConsecutiveDays != null) {
                return setting.indexOf(this.FormatDate(d)) > -1;
            }

            const dayOfWeek = d.getDay();
            if (this.isDayOfWeekDisabled(dayOfWeek)) {
                return true;
            }

            setting = setting.concat(window.holidays);
            setting = setting.concat(window.disabled);

            if (isStartDate) {
                if (Config.DisabledStartDates && Config.DisabledStartDays) {
                    return setting.concat(Config.DisabledStartDates).indexOf(this.FormatDate(d)) > -1 || Config.DisabledStartDays.includes(dayOfWeek);
                }

                return setting.indexOf(this.FormatDate(d)) > -1;
            }
            else {
                return setting.indexOf(this.FormatDate(d)) > -1;
            }
        }

        public Update = () => {
            $(".validation-summary-errors").empty();

            if (Settings.FormData.LeaveType > LeaveType.Special) {
                Settings.FormData.LeaveType = LeaveType.Special;
            }

            return $("#partial").load(Leave.Urls.postBack, Settings.FormData)
                .done(() => this.SetLeaveType())
                .fail((error: string) => {
                    $(Leave.Selectors.CompanyLeaveSetupId).dxSelectBox({
                        disabled: true
                    });

                    DevExpress.ui.dialog.custom({ message: error }).show();
                });
        }

        public SetLeaveType = (): void => {
            if (Settings.FormData.LeaveType == LeaveType.Special && Settings.FormData.CompanyLeaveSetupId > 0) {
                Settings.Form.getEditor("LeaveType").option("value", Settings.FormData.CompanyLeaveSetupId);
            }
        }
        public OnEditingStart = (e: { component: DevExpress.ui.dxDataGrid, key: number, data: any, cancel: boolean }): void => {
            e.cancel = true;
            App.ReloadForm(e.key);
        }

        public OnRowRemoved(e: { component: DevExpress.ui.dxDataGrid }): void {
            window.location.href = Urls.new;
        }

        public OnRowPrepared(e: { component: DevExpress.ui.dxDataGrid, rowType: string, rowElement: any, data: any }): void {
            if (e.rowType === 'data' && e.data.leaveEntryType === 3) {
                e.rowElement.addClass("alert alert-danger");
            }
        }

        public OnRowClick(e: DevExpress.ui.dxDataGrid.RowClickEvent): void {
            if (e.rowType !== "group") {
                window.location = e.data.url;
            }
        }

        public ReloadForm = (key?: number): void => {
            const url = new URL(`${window.location.protocol}//${window.location.host}${Urls.edit}`);
            url.searchParams.append('leaveAdjustmentId', key.toString());
            window.location.href = url.toString();
        }

        public async Submit() {
            let validationResult = $(this).dxValid();
            if (!validationResult.isValid) {
                validationResult.show();
                return;
            }

            ShowLoadPanel();

            let data = Object.assign({}, Settings.FormData);
            if (data.LeaveType > LeaveType.Special) {
                data.LeaveType = LeaveType.Special;
            }

            if (Settings.FormData.LeaveAdjustmentId) {
                $.extend(Settings.DeltaFormData, { CustomFields: Settings.FormData.CustomFields });
                Settings.DeltaFormData.Days = Settings.FormData.Days;
                Settings.DeltaFormData.Hours = Settings.FormData.Hours;
                Settings.DeltaFormData.Signature = Settings.FormData.Signature;

                data = Object.assign({}, Settings.DeltaFormData);
                if (data.LeaveType > LeaveType.Special) {
                    data.LeaveType = LeaveType.Special;
                }

                $.ajax({
                    url: `${Urls.record}?leaveAdjustmentId=${Settings.FormData.LeaveAdjustmentId}`,
                    type: 'PATCH',
                    data: Settings.DeltaFormData
                })
                    .done(function () {
                        window.location.href = Urls.new;
                    })
                    .fail(function () {
                        HideLoadPanel();
                    });
            }
            else {
                App.Post(data);
            }
        }

        private Post(data: any) {
            $.ajax({
                url: Urls.record,
                type: 'POST',
                data: data
            })
                .done(function (leaveAdjustmentId: number) {
                    if (!Settings.IsAdminUser) {
                        window.location.href = `${Urls.view}?leaveAdjustmentId=${leaveAdjustmentId}`;
                    }
                    else {
                        location.reload();
                    }
                })
                .fail(function () {
                    HideLoadPanel();
                });
        }

        private GetDate(date: Date) {
            let d = new Date(date);
            d.setHours(0, 0, 0, 0);

            return d;
        }

        private FormatDate(date: Date) {
            return DevExpress.localization.formatDate(date, "yyyy-MM-dd");
        }
    }

    export const App = new ViewModel();
}