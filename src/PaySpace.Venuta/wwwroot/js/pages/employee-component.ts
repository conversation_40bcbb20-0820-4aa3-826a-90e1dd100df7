/// <reference path="../definitions.d.ts" />

interface IRecurringComponent {
    Form?: DevExpress.ui.dxForm;
    PayslipAction?: string;
    FieldCode?: string;
    SpecialComponentCode?: string;
    HasComponentValues?: boolean;
    ComponentSettings?: object;
    CompanyComponentDetails?: any;
    IsCustomFieldComponent?: boolean;

    CustomizeItem(item: DevExpress.ui.dxFormItem): void;

    OnFieldDataChanged(e: { component: DevExpress.ui.dxForm; dataField: string }): void;

    OnInitialized(e: { component: DevExpress.ui.dxForm }): void;

    IsGroupedLookUp(fieldName: string): boolean;

    IsOrderedLookUp(fieldName: string): boolean;

    OnDataLoad?(component?: any);
}

interface ICustomFieldComponent extends IRecurringComponent {
    GetCustomFieldForm(details: ComponentCompanyDetail): any;
}

class ComponentSettings {
    medicalReferenceNumberRequired: boolean;
    employmentDate: Date;
    taxCountryCode: string;
    dependantsCountUrl: string;
    dependantsUrl: string;
    medicalFundsUrl: string;
    apiUrl: string;
    hasTaxSpread: boolean;
}

interface ComponentCompanyDetail {
    ComponentId: number;
    Action: string;
    ComponentType: string;
    ComponentCode: string;
    TableBuilderCategory: string;
    TableBuilderCategoryId: number;
    IsTableBuilderCategoryRestricted: boolean;
    IsTableBuilderBalanceComponent: boolean;
    FormulaTableType: string;
    ComponentLines: Array<any>;
    InPackage: boolean;
    EnforcePackageRule: boolean;
    FieldCode: string;
    EnableBcoe: boolean;
    MedicalCategory: string;
    ComponentCategory: string;
    IsMonthlyByWeeks: boolean;
    Value: string;
    Description: string;

    // Onceoff fields
    SpecialComponentCode: string;
    IsOnceOffComponent: boolean;
}

namespace EmployeeComponents {
    export let Components: {
        [key: string]: IRecurringComponent;
    } = {};

    export class ViewModel extends HistoryGrid {
        protected formHelper: ComponentFormHelper;

        constructor(
            protected apiUrl: string,
            protected metadataUrl: string,
            protected headers: object,
            protected employeeId: string,
            protected frequencyId: string,
            protected runId: string,
            protected action: string,
            protected companySettings: object,
            protected auditUrl: string) {
            super(
                apiUrl,
                metadataUrl,
                headers,
                frequencyId,
                runId,
                action);
            this.formHelper = new ComponentFormHelper(apiUrl, action, headers);
        }

        protected OnSelectionChanged = async (e: { selectedItem?: any; }) => {
            $("#loader").dxLoadIndicator({ visible: true });

            let componentType = this.GetComponentType(e.selectedItem.ComponentType);
            let component = await this.GetComponent(componentType, e.selectedItem.ComponentId, true);
            await this.AddForm(component, e.selectedItem);
        }

        public AddForm = async (component: any, detail: ComponentCompanyDetail): Promise<void> => {
            detail.ComponentType = this.GetComponentType(detail.ComponentType);
            let apiUrl = new URL(this.apiUrl + detail.ComponentType);
            apiUrl.searchParams.append("action", detail.Action);
            apiUrl.searchParams.append("componentCompanyId", `${detail.ComponentId}`);

            // set the popup title
            $("#select-component-modal .modal-title").html(detail.Description);
            $('#select-component-modal .modal-dialog').addClass("modal-md").removeClass("modal-sm");
            this.AuditButton(this.auditUrl, component);

            let componentLines = DevExpress.data.query(detail.ComponentLines || []).sortBy("Description").toArray();
            component = component ? component : {
                ComponentCompany: detail.Value,
                InPackage: detail.InPackage,
                Bcoepercentage: detail.EnableBcoe ? 100 : null,
                ComponentCompanyId: detail.ComponentId,
                CustomFields: []
            };

            // needed for Edit Payslip
            component.SpecialComponentCode = detail.SpecialComponentCode;
            component.IsOnceOffComponent = detail.IsOnceOffComponent;

            component.Values = this.MergeComponentLines(componentLines, component.Values);

            let employeeComponent: IRecurringComponent = Components[detail.ComponentType];
            employeeComponent.PayslipAction = this.action;
            employeeComponent.FieldCode = detail.FieldCode;
            employeeComponent.SpecialComponentCode = detail.SpecialComponentCode;
            employeeComponent.HasComponentValues = component.Values.length > 0;
            employeeComponent.ComponentSettings = this.companySettings;
            employeeComponent.CompanyComponentDetails = detail;

            let keys = await this.GetKeysForType(detail.ComponentType);
            this.GetHistoryUrl(keys, component, this.apiUrl, detail, this.employeeId);

            this.GetFormMetadata(detail, detail.ComponentLines, component).then(config => {
                let element = $("#form-component");
                if (element.length && element.data("dxForm")) {
                    // We need to make sure that the form is new every time, so that the `OnInitialized` method gets executad.
                    let instance = element.dxForm("instance");
                    instance.dispose();
                }

                element.dxForm({
                    formData: component,
                    items: config.items,
                    customizeItem: employeeComponent.CustomizeItem,
                    onFieldDataChanged: employeeComponent.OnFieldDataChanged,
                    onInitialized: employeeComponent.OnInitialized,
                } as DevExpress.ui.dxForm.Properties);

                let form = element.dxForm("instance");
                this.CommitMethod(apiUrl.toString(), "POST", form)

                // Update cascading editors that rely on fixed values when the form is initialized.
                form.on("fieldDataChanged", (e: { dataField: string; value: any; }) => {
                    if (e.value) {
                        let field = config.entity.find(x => x.Name === e.dataField);
                        if (field && field.Hidden) {
                            let dependentField = this.formHelper.GetDependentField(field);
                            if (dependentField) {
                                this.UpdateCascadingEditor(form, field.Name, dependentField.Name, e.value, dependentField.Lookup);
                            }
                        }
                    }
                });

                $("#loader").dxLoadIndicator({ visible: false });
            });
        }

        public EditForm = async (componentCompanyId: number, componentCompany: string): Promise<void> => {
            let details = await this.GetComponentDetail(componentCompanyId);
            let detail = details.value[0];

            $("#select-component-modal .modal-title").html(componentCompany);
            $('#select-component-modal .modal-dialog').addClass("modal-md").removeClass("modal-sm");

            detail.ComponentType = this.GetComponentType(detail.ComponentType);
            let component = await this.GetComponent(detail.ComponentType, componentCompanyId, false);

            let keys = await this.GetKeysForType(detail.ComponentType);

            // Cater for scenario that no record exists for the row opened but the row opened has a record in the future
            if (!component || component[keys[0]] <= 0) {
                await this.AddForm(component, detail);
                return;
            }

            let apiUrl: URL = this.GetComponentUrl(keys, detail.ComponentType, detail.Action, component, componentCompanyId);
            this.GetHistoryUrl(keys, component, this.apiUrl, detail, this.employeeId);

            let employeeComponent: IRecurringComponent = Components[detail.ComponentType];
            employeeComponent.PayslipAction = this.action;
            employeeComponent.FieldCode = detail.FieldCode;
            employeeComponent.SpecialComponentCode = detail.SpecialComponentCode;
            employeeComponent.ComponentSettings = this.companySettings;
            employeeComponent.CompanyComponentDetails = detail;

            let componentLines = DevExpress.data.query(detail.ComponentLines || []).sortBy("Description").toArray();
            component.Values = this.MergeComponentLines(componentLines, component.Values);
            employeeComponent.HasComponentValues = component.Values.length > 0
            this.AuditButton(this.auditUrl, component);

            if (detail.EnforcePackageRule && detail.InPackage != component.InPackage) {
                component.InPackage = detail.InPackage;
            }

            await this.GetFormMetadata(detail, componentLines, component).then(config => {
                if (typeof Components[detail.ComponentType].OnDataLoad === 'function') {
                    Components[detail.ComponentType].OnDataLoad(component);
                }

                let element = $("#form-component").dxForm({
                    formData: component,
                    items: config.items,
                    customizeItem: employeeComponent.CustomizeItem,
                    onFieldDataChanged: employeeComponent.OnFieldDataChanged,
                    onInitialized: employeeComponent.OnInitialized,
                    repaintChangesOnly: true
                } as DevExpress.ui.dxForm.Properties);

                let form = element.dxForm("instance");
                this.CommitMethod(apiUrl.toString(), "PATCH", form);

                $("#loader").dxLoadIndicator({ visible: false })
            });
        }

        protected async GetFormMetadata(detail: ComponentCompanyDetail, componentLines: any[], component?: any) {
            let metadata = await MetadataClient.fetchAndParseAsync(this.metadataUrl);
            let entity = metadata.Entities[detail.ComponentType];
            let isReadonly = metadata.Entities["EmployeeComponent"].ReadOnly;
            this.formHelper.SetEntity(entity);

            let config = await this.GetFormControls(detail, componentLines, isReadonly, entity, metadata, component);

            let lines = componentLines.filter(cl => cl.CalculationElement != "Indicator").map((line, index) => this.GetComponentValueFormItem(line, component.Values, index, isReadonly));

            let result = new Array<DevExpress.ui.dxForm.GroupItem>();

            if (lines.length > 0) {
                result.push({
                    itemType: "group",
                    colCount: 2,
                    items: lines
                });
            }

            result.push({
                itemType: "group",
                colCount: 2,
                items: config
            });

            await this.GetCustomFieldFormControls(detail)
            result.push({
                itemType: "group",
                colSpan: 2,
                items: [{
                    dataField: "CustomFields",
                    colSpan: 2,
                    template(e, element)  {
                        const el = document.createElement('custom-form-fields') as any;
                        el.entity =  entity.Name.includes("TableBuilder") ? "TableBuilder" : entity.Name;
                        el.readOnly = false;
                        el.categoryId = detail.TableBuilderCategoryId;
                        el.colCount = 2;
                        el.isCompanyTableBuilder = false;
                        el.isEmployeeTableBuilder = true;
                        element.append(el);
                        if (el._instance) {
                            el._instance.proxy.init(e).then(_ => {
                                const dirtyCustomFieldsMap = new Map<string, any>();
                                e.component.option("formData").CustomFields?.forEach(field => {
                                    if (field.Value) {
                                        dirtyCustomFieldsMap.set(field.Code, field.Value);
                                    }
                                });

                                e.component.option('dirtyCustomFieldsMap', dirtyCustomFieldsMap);
                            });
                        }

                        return element
                    }
                } as DevExpress.ui.dxForm.SimpleItem]
            });

            let countryCode = this.companySettings['taxCountryCode'];
            let historyConfig = this.AddHistoryGrid(config, component, detail.ComponentType, detail.ComponentId, countryCode);
            if (historyConfig) {
                result.push({
                    itemType: "group",
                    name: "history-Grid",
                    cssClass: "mt-20",
                    caption: Localization[ComponentConstants.BaseSystemArea].lblHistory,
                    colCount: 2,
                    items: historyConfig
                });
            }

            result.push({
                itemType: "group",
                name: "extra-fields",
                colCount: 2,
                template: (data: any, itemElement: JQuery) => {
                    return this.formHelper.ExtraFieldsAccordionTemplate(data, itemElement, isReadonly);
                }
            });

            if (!isReadonly) {
                result.push({
                    itemType: "group",
                    items: [{
                        itemType: "button",
                        buttonOptions: {
                            type: "default",
                            text: Localization[ComponentConstants.PageSystemArea].btnSave,
                            onClick: this.formHelper.AddOrUpdate
                        }
                    }]
                } as DevExpress.ui.dxForm.GroupItem);
            }

            return { entity: entity, items: result };
        }

        protected GetFormControls = async (detail: ComponentCompanyDetail, componentLines: any[], isReadonly: boolean, entity: MetadataClient.IEdmEntity, metadata: MetadataClient.IEdmModel, component?: any): Promise<Array<DevExpress.ui.dxForm.SimpleItem>> => {
            return entity.filter(x => x.Name !== "StartDate" && x.Name !== "EndDate" && x.Name !== "Comments").map(field => ({
                itemType: "simple",
                dataField: field.Name,
                editorType: this.formHelper.EditorType(field),
                editorOptions: this.EditorOptions(metadata, field, detail.ComponentType, componentLines, component),
                isRequired: !field.Nullable && field.DataType !== "boolean",
                visible: this.formHelper.IsFieldVisible(field, componentLines, detail.InPackage, detail.EnableBcoe),
                disabled: isReadonly || field.ReadOnly || field.Name == "InPackage" && detail.EnforcePackageRule,
                visibleIndex: field.Order,
                label: {
                    text: this.FieldCaption(field)
                }
            } as DevExpress.ui.dxForm.SimpleItem));
        }

        protected GetCustomFieldFormControls = (detail: ComponentCompanyDetail) => {
            if (Components[detail.ComponentType].IsCustomFieldComponent == true) {
                let customFieldComponent = Components[detail.ComponentType] as ICustomFieldComponent;

                return customFieldComponent.GetCustomFieldForm(detail);
            }

            return null;
        }

        private GetComponentValueFormItem = (componentValue: any, savedValues: any[], index: number, isReadOnly: boolean): DevExpress.ui.dxForm.SimpleItem => {
            if (this.IsHtml(componentValue.Description)) {
                // Getting default display value for numberbox, if there is a saved value for this field, use that instead.
                let value = 0;

                if (savedValues && savedValues.some(_ => _.Description === componentValue.Description)) {
                    value = savedValues.find(_ => _.Description === componentValue.Description).ComponentValue;
                }

                return ({
                    itemType: "simple",
                    dataField: `Values[${index}].ComponentValue`,
                    editorType: "dxNumberBox",
                    disabled: isReadOnly,
                    label: {
                        text: componentValue.Description
                    },
                    template: (data: any, itemElement: HTMLElement) => {
                        // Setup changing label to render HTML.
                        let label = $(itemElement).parent().find('.dx-field-item-label-text');
                        label.html(componentValue.Description);

                        // Editor Setup To Set Value On Form. As in custom fields implementation.
                        // Secondary reference in this suggestion https://www.devexpress.com/Support/Center/Question/Details/T398317/dxform-how-to-use-dxtagbox-inside-an-item-template
                        $("<div>").dxNumberBox({
                            name: data.editorOptions.name,
                            value: value,
                            disabled: isReadOnly,
                            onValueChanged: (e: any) => {
                                $("#form-component").dxForm("updateData", data.dataField, e.value);
                            }
                        } as DevExpress.ui.dxNumberBox.Properties).appendTo(itemElement);

                        return itemElement;
                    }
                } as DevExpress.ui.dxForm.SimpleItem);
            }

            return ({
                itemType: "simple",
                dataField: `Values[${index}].ComponentValue`,
                editorType: "dxNumberBox",
                disabled: isReadOnly,
                label: {
                    text: componentValue.Description
                }
            } as DevExpress.ui.dxForm.SimpleItem);
        }

        private IsHtml = (label: string): boolean => {
            let baseTag = document.createElement('div');

            baseTag.innerHTML = label;

            let tagHolder = baseTag.childNodes;
            for (let i = tagHolder.length - 1; i > 0; i--) {
                if (tagHolder[i].nodeType == 1) {
                    return true;
                }
            }

            return false;
        }

        private FieldCaption = (field: MetadataClient.IEdmProperty): string => {
            switch (field.Name) {
                case "BcoePercentage": {
                    return Localization[ComponentConstants.BaseSystemArea].BcoePercentage;
                }
                case "InPackage": {
                    return Localization[ComponentConstants.BaseSystemArea].InPackage;
                }
                default: {
                    return field.Caption
                }
            }
            ;
        }

        protected GetComponentDetail = async (componentCompanyId: number) => {
            let detailUrl = new URL("Lookup/ComponentCompanyDetail", this.apiUrl);
            detailUrl.searchParams.append("action", this.action);
            detailUrl.searchParams.append("frequency", this.frequencyId);
            detailUrl.searchParams.append("run", this.runId);
            detailUrl.searchParams.append("$filter", `ComponentId eq ${componentCompanyId}`);

            let response = await utils.fetch.get(detailUrl);
            if (response.ok) {
                return await response.json();
            }

            return null;
        }

        protected GetComponent = async (componentType: string, componentId: number, initialize: boolean) => {
            let entityUrl = new URL(componentType + "/" + this.employeeId + "/component(" + componentId + ")", this.apiUrl);
            entityUrl.searchParams.append("action", this.action);
            entityUrl.searchParams.append("frequency", this.frequencyId);
            entityUrl.searchParams.append("run", this.runId);
            entityUrl.searchParams.append("initialize", initialize.toString());

            let response = await utils.fetch.get(entityUrl);
            if (response.ok) {
                return await response.json();
            }

            return null;
        }

        protected CommitMethod(apiUrl: string, httpMethod: string, form: DevExpress.ui.dxForm) {
            form.option("recordUrl", apiUrl);
            form.option("CommitMethod", httpMethod);
        }

        private MergeComponentLines = (componentLines: any[], componentValues: any[]) => {
            return componentLines.filter(cl => cl.CalculationElement != "Indicator").map(function (x) {
                let componentValue = componentValues ? componentValues.find(_ => _.ValueType === x.ValueType) : null;
                return {
                    Description: x.Description,
                    ValueType: x.ValueType,
                    ComponentValue: componentValue ? componentValue.ComponentValue : x.ComponentValue,
                    ComponentValueId: componentValue ? componentValue.ComponentValueId : x.ComponentValueId || 0
                }
            });
        }

        protected EditorOptions(metadata: MetadataClient.IEdmModel, field: MetadataClient.IEdmProperty, componentType: string, componentLines: any[], component?: any) {
            if (field.Enum) {
                return {
                    dataSource: metadata.Enums[field.Enum],
                    showClearButton: field.Nullable,
                    displayExpr: "Name",
                    valueExpr: "Value",
                    readOnly: field.ReadOnly
                } as DevExpress.ui.dxSelectBox.Properties;
            }

            if (field.Name === "ComponentIndicatorLine" && componentLines.find(cl => cl.CalculationElement == "Indicator")) {
                return {
                    dataSource: componentLines.find(cl => cl.CalculationElement == "Indicator").Indicators,
                    showClearButton: field.Nullable,
                    displayExpr: "Description",
                    valueExpr: "Description",
                    readOnly: field.ReadOnly
                } as DevExpress.ui.dxSelectBox.Properties;
            }

            if (field.IsDecimal) {
                if (field.IsPercentage) {
                    if (component.ComponentCompanyId == null) {
                        return {
                            showClearButton: field.Nullable,
                            value: null,
                            readOnly: field.ReadOnly,
                        }
                    }
                    return {
                        showClearButton: field.Nullable,
                        format: window.User.PercentageFormat,
                        readOnly: field.ReadOnly,
                    }
                }
                if (component.ComponentCompanyId == null) {
                    return {
                        showClearButton: field.Nullable,
                        value: null,
                        readOnly: field.ReadOnly
                    }
                }
                return {
                    showClearButton: field.Nullable,
                    format: window.User.DecimalFormat,
                    readOnly: field.ReadOnly
                }
            }

            if (field.Lookup) {
                let employeeComponent: IRecurringComponent = Components[componentType];
                let config: DevExpress.ui.dxLookup.Properties;
                let isCascading = this.formHelper.IsCascadingField(field);
                const isGrouped = employeeComponent && employeeComponent.IsGroupedLookUp(field.Name);
                const isOrdered = employeeComponent && employeeComponent.IsOrderedLookUp(field.Name);

                if (isCascading) {
                    let parentPropertyName = this.formHelper.GetParentPropertyName(field);
                    let parentValue = component && component[parentPropertyName];
                    config = {
                        dataSource: component && parentValue ? this.formHelper.CreateDataSource(this.apiUrl + "lookup/" + field.Lookup, parentPropertyName, parentValue) : undefined,
                        showClearButton: field.Nullable,
                        displayExpr: "Description",
                        valueExpr: "Value",
                        searchExpr: ["Value", "Description"],
                        cleanSearchOnOpening: true,
                        disabled: this.formHelper.IsParentLookup(field) && component === undefined,
                        readOnly: field.ReadOnly,
                        dropDownOptions: { container: "#select-component-modal .modal-body"}
                    } as DevExpress.ui.dxLookup.Properties;
                } else {
                    config = {
                        dataSource: this.formHelper.CreateDataSource(this.apiUrl + "lookup/" + field.Lookup, null, null, isGrouped, isOrdered),
                        showClearButton: field.Nullable,
                        displayExpr: "Description",
                        valueExpr: "Value",
                        searchExpr: ["Value", "Description"],
                        readOnly: field.ReadOnly,
                        grouped: isGrouped,
                        dropDownOptions: { container: "#select-component-modal .modal-body" }
                    } as DevExpress.ui.dxLookup.Properties;
                }

                let dependentField = this.formHelper.GetDependentField(field);
                if (dependentField) {
                    config.onValueChanged = (e) => {
                        let form = e.element.parents(".dx-form").dxForm("instance");
                        this.UpdateCascadingEditor(form, field.Name, dependentField.Name, e.value, dependentField.Lookup);
                    };
                }

                return config;
            }

            // Checking field name instead of editor type because some that have the same editor type should not be clearable.
            if (field.Name == "StartDate" || field.Name == "EndDate") {
                return { "showClearButton": true, "dateSerializationFormat": "yyyy-MM-dd" };
            }

            if (field.DataType == "date")
            {
                return { "dateSerializationFormat": "yyyy-MM-dd" };
            }

            return undefined;
        }

        private UpdateCascadingEditor(form: DevExpress.ui.dxForm, parentField: string, cascadingField: string, parentValue: any, fieldLookup: string) {
            let ds = this.formHelper.CreateDataSource(this.apiUrl + "lookup/" + fieldLookup, parentField, parentValue);

            let dependentEditor = form.getEditor(cascadingField) as DevExpress.ui.dxLookup;
            dependentEditor.beginUpdate();
            dependentEditor.option("dataSource", ds);
            dependentEditor.option("disabled", false);
            dependentEditor.endUpdate();
            dependentEditor.reset();

            // Persist the data source for when the form is repainted.
            let o = form.itemOption(cascadingField);
            $.extend(o.editorOptions, { dataSource: ds, disabled: false });
            form.repaint();
        }

        private GetComponentType = (componentType: string) => {
            // The form uses the old Implementation of the component, The new implementation does not return a list of records
            return componentType === "EmployeeHousePayment" ? "EmployeeFinancialHousePayments" : componentType;
        }
    }
}