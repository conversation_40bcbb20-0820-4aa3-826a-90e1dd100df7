/// <reference path="../../definitions.d.ts" />

namespace AddEmployeeProfile {
    export let Settings: {
        SendEssRegistrationEmail: boolean;
        taxCountryCode: string;
        taxCountryId: number;
    };

    enum AddressType {
        Street = 4
    }

    export let Urls: {
        GetProvinces: string;
        GetStandardIndustryCodeSubs: string;
        municipality: string;
        addressStreetType: string;
    };

    export class ViewModel {
        private FormData: any;
        private ExemptionFormData: any;

        public OnInitialized = (e: { component: DevExpress.ui.dxForm; element: any; }): void => {
            const id = e.element.attr("id");

            if (id === "form-address") {
                this.FormData = e.component.option("formData");
            } else if (id === "form-exemptions") {
                this.ExemptionFormData = e.component.option("formData");
            }
        }

        public OnFieldDataChanged = (e: { component: DevExpress.ui.dxForm; element: any; dataField: string; value: any; }): void => {
            const id = e.element.attr("id");
            const scrollPos = window.scrollY;
            if (id === "form-address") {
                this.FormData = e.component.option("formData");
            } else if (id === "form-exemptions") {
                this.ExemptionFormData = e.component.option("formData");
            }

            if (e.dataField && (e.dataField.includes("CountryId") ||
                e.dataField.includes("PostalCountryId") ||
                e.dataField == "Address[1].AddressType" ||
                e.dataField == "Address[1].SameAsPostal" ||
                e.dataField == "Address[1].IsCareofAddress" ||
                e.dataField == "StandardIndustryCodeHeader")) {
                if (e.dataField == "Address[1].SameAsPostal" && e.value == true) {
                    this.FormData["Address"][1].AddressLine1 = this.FormData["Address"][0].AddressLine1;
                    this.FormData["Address"][1].PostalAddressLine1 = this.FormData["Address"][0].AddressLine1;

                    this.FormData["Address"][1].AddressLine2 = this.FormData["Address"][0].AddressLine2;
                    this.FormData["Address"][1].PostalAddressLine2 = this.FormData["Address"][0].AddressLine2;

                    this.FormData["Address"][1].AddressLine3 = this.FormData["Address"][0].AddressLine3;
                    this.FormData["Address"][1].PostalAddressLine3 = this.FormData["Address"][0].AddressLine3;

                    this.FormData["Address"][1].AddressCode = this.FormData["Address"][0].AddressCode;
                    this.FormData["Address"][1].PostalAddressCode = this.FormData["Address"][0].AddressCode;

                    this.FormData["Address"][1].ProvinceId = this.FormData["Address"][0].ProvinceId;
                    this.FormData["Address"][1].PostalProvinceId = this.FormData["Address"][0].ProvinceId;
                    this.FormData["Address"][1].PostalCountryId = this.FormData["Address"][0].CountryId;

                    if (Settings.taxCountryCode != TaxCountryCode.UK) {
                        this.FormData["Address"][1].UnitNumber = this.FormData["Address"][0].UnitNumber;
                        this.FormData["Address"][1].Complex = this.FormData["Address"][0].Complex;
                    }

                    this.FormData["Address"][1].StreetNumber = this.FormData["Address"][0].StreetNumber;

                    this.FormData["Address"][1].AddressStreetTypeId = this.FormData["Address"][0].AddressStreetTypeId;
                    this.FormData["Address"][1].MunicipalityId = this.FormData["Address"][0].MunicipalityId;
                }

                if (e.dataField == "Address[1].AddressType" && e.value === AddressType.Street) {
                    this.FormData["Address"][1].PostalAddressLine1 = null;
                    this.FormData["Address"][1].PostalAddressLine2 = null;
                    this.FormData["Address"][1].PostalAddressLine3 = null;
                } else {
                    this.FormData["Address"][1].AddressLine1 = null;
                    this.FormData["Address"][1].AddressLine2 = null;
                    this.FormData["Address"][1].AddressLine3 = null;
                }

                e.component.repaint();
                window.scrollTo(window.scrollX, scrollPos); // hack to fix form jumping to top
            }

            if (e.dataField == "Birthday") {
                const limit = this.FormData["TaxCountryLegalWorkAge"];

                if (limit !== null) {
                    const age = this.CalculateAge(e.value);

                    this.ToggleDateWarning(age < limit);
                }
            }

            if (e.dataField == "Email") {
                const emailAddressPrompt = e.component.itemOption("EmailPrompt.EmailEntered");
                emailAddressPrompt.visible = Settings.SendEssRegistrationEmail;

                e.component.repaint();
                window.scrollTo(window.scrollX, scrollPos); // hack to fix form jumping to top
            }
        }

        private CalculateAge(birthday: Date): number {
            const ageDifMs = Date.now() - birthday.getTime();
            const ageDate = new Date(ageDifMs);
            return Math.abs(ageDate.getUTCFullYear() - 1970);
        }

        public CustomizeItem = (item: DevExpress.ui.dxFormItem): void => {
            if (!item.dataField) {
                return;
            }

            if (item.dataField === "SubStandardIndustryCodeId") {
                item.editorType = "dxLookup";
                item.editorOptions = item.editorOptions || {};
                $.extend(item.editorOptions, this.GetSelectBox(`${Urls.GetStandardIndustryCodeSubs}?StandardIndustryCodeHeaderId=${this.ExemptionFormData["StandardIndustryCodeHeader"]}`, "value", this.ExemptionFormData["SubStandardIndustryCodeId"]));
            }

            if (item.dataField && (item.dataField.includes("ProvinceId") || item.dataField.includes("PostalProvinceId"))) {
                const addressNum = item.dataField.match(/\d+/)[0];

                const countryIdField = addressNum == "1" ? "PostalCountryId" : "CountryId";
                const provinceIdField = addressNum == "1" ? "PostalProvinceId" : "ProvinceId";

                const countryId = this.FormData["Address"][addressNum][countryIdField];
                const provinceId = this.FormData["Address"][addressNum][provinceIdField];

                item.editorType = "dxLookup";
                item.disabled = !countryId;

                $.extend(item.editorOptions, this.GetSelectBox(`${Urls.GetProvinces}?CountryId=${countryId}`, "value", provinceId));
            }

            if (item.dataField && (item.dataField.includes("MunicipalityId"))) {
                const addressNum = item.dataField.match(/\d+/)[0];

                const countryIdField = addressNum == "1" ? "PostalCountryId" : "CountryId";

                const address = this.FormData["Address"][addressNum];
                const countryId = address[countryIdField];
                const municipalityId = address["MunicipalityId"];

                const countryCodesHavingMunicipality = [TaxCountryCode.BR, TaxCountryCode.ES, TaxCountryCode.FR];
                const addressCountryIdsHavingMunicipality = [AddressCountryId.Brazil, AddressCountryId.Spain, AddressCountryId.France];
                const showMunicipalityField = countryCodesHavingMunicipality.includes(<TaxCountryCode>Settings.taxCountryCode)
                    && addressCountryIdsHavingMunicipality.includes(address[countryIdField]);

                item.editorType = "dxLookup";
                item.disabled = !countryId;
                item.visible = addressNum == "1"
                    ? !address.SameAsPostal && showMunicipalityField
                    : showMunicipalityField;

                $.extend(item.editorOptions, this.GetSelectBox(`${Urls.municipality}?CountryId=${Settings.taxCountryId}`, "value", municipalityId));
            }

            if (item.dataField === "Address[0].ProvinceId" && Settings.taxCountryCode == TaxCountryCode.SG) {
                item.visible = this.FormData["Address"][0]["CountryId"] != AddressCountryId.SG
            }

            if (item.dataField && (item.dataField.includes("AddressStreetTypeId"))) {
                const addressNum = item.dataField.match(/\d+/)[0];

                const countryIdField = addressNum == "1" ? "PostalCountryId" : "CountryId";

                const countryId = this.FormData["Address"][addressNum][countryIdField];
                const addressStreetTypeId = this.FormData["Address"][addressNum]["AddressStreetTypeId"];

                item.editorType = "dxLookup";
                item.disabled = !countryId;
                item.visible = addressNum == "1"
                    ? (Settings.taxCountryCode == TaxCountryCode.BR &&
                        !this.FormData["Address"][1].SameAsPostal &&
                        this.FormData["Address"][1][countryIdField] == AddressCountryId.Brazil)
                    : (Settings.taxCountryCode == TaxCountryCode.BR &&
                        this.FormData["Address"][0][countryIdField] == AddressCountryId.Brazil)

                $.extend(item.editorOptions, this.GetSelectBox(`${Urls.addressStreetType}?CountryId=${countryId}`, "value", addressStreetTypeId));
            }

            if (item.dataField === "Address[1].AddressType") {
                    item.visible = !this.FormData["Address"][1].SameAsPostal;
                }

            if ((item.dataField === "Address[1].UnitNumber" && Settings.taxCountryCode != TaxCountryCode.UK)
                || (item.dataField === "Address[1].Complex" && Settings.taxCountryCode != TaxCountryCode.UK)
                || item.dataField === "Address[1].StreetNumber"
                || item.dataField === "Address[1].AddressLine1"
                || item.dataField === "Address[1].AddressLine2"
                || item.dataField === "Address[1].AddressLine3"
                || item.dataField === "Address[1].AddressCode"
                || item.dataField === "Address[1].ProvinceId") {
                item.visible = !this.FormData["Address"][1].SameAsPostal && this.FormData["Address"][1].AddressType === AddressType.Street;

                if (item.dataField === "Address[1].ProvinceId"
                    && Settings.taxCountryCode == TaxCountryCode.SG
                    && this.FormData["Address"][1].AddressType === AddressType.Street) {
                    item.visible = this.FormData["Address"][1]["PostalCountryId"] != AddressCountryId.SG
                }
            }

            if (item.dataField === "Address[1].PostalAddressLine1"
                || item.dataField === "Address[1].PostalAddressLine2"
                || item.dataField === "Address[1].PostalAddressLine3"
                || item.dataField === "Address[1].SpecialServices"
                || item.dataField === "Address[1].PostalAddressCode"
                || item.dataField === "Address[1].PostalProvinceId") {
                item.visible = !this.FormData["Address"][1].SameAsPostal && this.FormData["Address"][1].AddressType !== AddressType.Street;

                if (item.dataField === "Address[1].PostalProvinceId"
                    && Settings.taxCountryCode == TaxCountryCode.SG
                    && this.FormData["Address"][1].AddressType !== AddressType.Street) {
                    item.visible = this.FormData["Address"][1]["PostalCountryId"] != AddressCountryId.SG
                }
            }

            if (item.dataField === "Address[1].PostalCountryId") {
                item.visible = !this.FormData["Address"][1].SameAsPostal;
            }

            if (item.dataField === "Address[1].CareOfIntermediary") {
                item.visible = this.FormData["Address"][1].IsCareofAddress != null && this.FormData["Address"][1].IsCareofAddress;
            }
        }

        private GetSelectBox(url: string, key: string, value: number): DevExpress.ui.dxSelectBox.Properties {
            return {
                dataSource: {
                    store: DevExpress.data.AspNet.createStore({
                        loadUrl: url,
                        key: key
                    })
                },
                displayExpr: "text",
                valueExpr: "value",
                value: value
            };
        }

        private ToggleDateWarning(show: boolean): void {
            if (show) {
                $("#employeeLegalWorkAge").removeClass("d-none");
            } else {
                $("#employeeLegalWorkAge").addClass("d-none");
            }
        }
    }

    export let App = new ViewModel();
}