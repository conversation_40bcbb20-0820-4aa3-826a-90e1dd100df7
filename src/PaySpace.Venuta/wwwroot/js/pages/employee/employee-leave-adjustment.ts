/// <reference path="../../definitions.d.ts" />

namespace EmployeeLeaveAdjustment {
    enum LeaveType {
        Annual = 1,
        Special = 5
    }

    enum RunType {
        Interim = 2
    }

    export let Settings: {
        LeaveTypePostback: string;
        CompanyRunPostback: string;
        LeaveTypeUrl: string;
        LeaveBucketsUrl: string;
        LeaveReasonLookupUrl: string;
        LeaveReasonUrl: string;
        LengthOfServiceUrl: string;
        LeaveBalanceUrl: string;
        RunTypeUrl: string;
        IsReadOnly: boolean;
        RecordUrl: string;
        FormData: any;
        DeltaFormData: any;
        HistoryGrid: DevExpress.ui.dxDataGrid;
        Form: DevExpress.ui.dxForm;
    }

    export class ViewModel {
        public async OnFieldDataChanged(e: { component: DevExpress.ui.dxForm, dataField: string, name: string, value: any }) {
            if (e.component.getEditor(e.dataField) != null) {
                Settings.FormData[e.dataField] = e.value;

                Settings.DeltaFormData[e.dataField] = e.value;
                if (e.dataField === "LeaveType") {
                    let formData = e.component.option("formData");
                    Settings.FormData.LeaveReasonId = null;
                    formData.CompanyLeaveSetupId = null;

                    if (e.value >= LeaveType.Special) {
                        await App.GetLeaveBalance();
                        formData.LeaveBalance = Settings.FormData.LeaveBalance;
                    }

                    e.component.updateData(formData);
                    e.component.repaint();
                }

                if (e.dataField === "CompanyLeaveSetupId" || e.dataField === "HistoricalConcessionId") {
                    await App.GetLeaveBalance();
                    e.component.repaint();
                }

                if (e.dataField === "CompanyRunId") {
                    let formData = e.component.option("formData");
                    formData.LeaveType = null;
                    e.component.updateData(formData);
                    await App.GetLengthOfService();
                    e.component.repaint();
                }
            }
        }

        public async OnRunChanged() {
            let url = new URL(Settings.RunTypeUrl, `${window.location.protocol}//${window.location.host}`)
            url.searchParams.append("runId", Settings.FormData.CompanyRunId);

            let runType = await App.GetData(url);
            if (runType == RunType.Interim) {
                $("#lblRunWarning").show();
            }
            else {
                $("#lblRunWarning").hide();
            }
        }
        public OnAttachmentChanged = (e: { value: any }): void => {
            if (e.value && e.value.length > 0) {
                let reader = new FileReader();
                reader.read(e.value).done(function (data) {
                    $.extend(Settings.DeltaFormData, {
                        AttachmentName: e.value[0].name,
                        Attachment: utils.base64.raw(data)
                    });
                });
            }
            else {
                $.extend(Settings.DeltaFormData, {
                    Attachment: null,
                    AttachmentName: null
                });
            }
        }

        public async GetLengthOfService() {
            if (Settings.FormData.CompanyRunId) {
                let url = new URL(Settings.LengthOfServiceUrl, `${window.location.protocol}//${window.location.host}`);
                url.searchParams.append("RunId", Settings.FormData.CompanyRunId);
                let lengthOfService = await App.GetData(url);
                let options = Settings.Form.itemOption("LengthOfService");
                options.editorOptions.value = lengthOfService;
            }
        }

        public async GetLeaveBalance() {
            if (Settings.FormData.LeaveType && Settings.FormData.CompanyRunId && (Settings.FormData.CompanyLeaveSetupId || Settings.FormData.LeaveType >= LeaveType.Special)) {
                let url = new URL(Settings.LeaveBalanceUrl, `${window.location.protocol}//${window.location.host}`);
                url.searchParams.append("LeaveType", Settings.FormData.LeaveType > LeaveType.Special ? LeaveType.Special : Settings.FormData.LeaveType);
                url.searchParams.append("RunId", Settings.FormData.CompanyRunId);
                url.searchParams.append("CompanyLeaveSetupId", Settings.FormData.CompanyLeaveSetupId);
                if (Settings.FormData.HistoricalConcessionId > 0) {
                    url.searchParams.append("historicalConcessionId", Settings.FormData.HistoricalConcessionId);
                }

                let leaveBalance = await App.GetData(url);
                let options = Settings.Form.itemOption("LeaveBalance");
                Settings.FormData.LeaveBalance = leaveBalance;
                options.editorOptions.value = leaveBalance;
            }
        }

        public CustomizeItem(e: DevExpress.ui.dxForm.SimpleItem): void {
            if (Settings.FormData == null) {
                Settings.Form = $("#leaveAdjustmentForm").dxForm("instance");
                Settings.FormData = Object.assign({}, Settings.Form.option("formData"));
            }

            if (Settings.DeltaFormData == null) {
                Settings.DeltaFormData = new Object;
            }

            if (e.dataField === "LeaveType") {
                if (Settings.FormData.CompanyRunId > 0) {
                    let periodEndDate = Settings.Form.itemOption('CompanyRunId')['selectedPeriod'];

                    $.extend(e.editorOptions, {
                        dataSource: App.CreateDataSource(Settings.LeaveTypeUrl, "companyLeaveSetupId", {
                            EffectiveDate: periodEndDate
                        })
                    });
                }
                $.extend(e.editorOptions,
                    {
                        onValueChanged: App.OnLeaveTypeChanged,
                        readOnly: e.editorOptions.securityReadOnly || Settings.IsReadOnly || Settings.FormData.LeaveAdjustmentId > 0 || !(Settings.FormData.CompanyRunId > 0)
                    });
            }

            if (e.dataField === "CompanyRunId") {
                $.extend(e.editorOptions,
                    {
                        readOnly: e.editorOptions.securityReadOnly || Settings.IsReadOnly,
                        onSelectionChanged: App.OnSelectionChanged
                    });
            }

            if (e.dataField === "CompanyLeaveSetupId") {
                if (Settings.FormData.CompanyRunId > 0) {
                    let periodEndDate = Settings.Form.itemOption('CompanyRunId')['selectedPeriod'];

                    $.extend(e.editorOptions, {
                        dataSource: App.CreateDataSource(Settings.LeaveBucketsUrl, "value", {
                            LeaveType: Settings.FormData.LeaveType > LeaveType.Special ? LeaveType.Special : Settings.FormData.LeaveType,
                            EffectiveDate: periodEndDate
                        }),
                        readOnly: e.editorOptions.securityReadOnly || Settings.IsReadOnly || Settings.FormData.LeaveType < 1
                    });
                }
                e.visible = Settings.FormData.LeaveType > 0 && Settings.FormData.LeaveType <= 5 && Settings.FormData.CompanyRunId > 0
            }

            if (e.dataField === "HistoricalConcessionId") {
                e.visible = Settings.FormData.LeaveType == LeaveType.Annual && window.User.Country == "BR";
            }

            if (e.name === "LengthOfService") {
                e.visible = Settings.FormData.CompanyRunId > 0;
            }

            if (e.name === "LeaveBalance") {
                if (Settings.FormData.LeaveType <= LeaveType.Special && Settings.FormData.CompanyLeaveSetupId == null) {
                    $.extend(e.editorOptions, { value: null });
                }
                else{
                    $.extend(e.editorOptions, { value: Settings.FormData.LeaveBalance });
                }

                if (Settings.FormData.CompanyRunId && (Settings.FormData.CompanyLeaveSetupId || Settings.FormData.LeaveType > LeaveType.Special)) {
                    let run = Settings.Form.itemOption('CompanyRunId');
                    let runText = run.selectedText != null ? run.selectedText : run.editorOptions.placeholder;
                    $.extend(e.label, { text: Localization['Leave.Adjustment'].lblBalance.replace('{0}', runText) });
                }
                else {
                    e.visible = false;
                }
            }

            if (e.dataField === "OverrideBalance") {
                $.extend(e.editorOptions, { value: Settings.FormData.OverrideBalance });
            }
        }

        public OnCellPrepared(e: { component: DevExpress.ui.dxDataGrid, columnIndex: number, rowType: string, data: any, cellElement: JQuery }): void {
            if (Settings.HistoryGrid == null) {
                Settings.HistoryGrid = e.component;
            }

            if (e.cellElement.find(".dx-link-edit").length && e.rowType === "data") {
                if (e.data.runClosed) {
                    e.cellElement.find(".dx-link-delete").remove();
                    e.cellElement.find(".dx-link-edit").attr("title", Localization["Payroll.PayRates"].lblViewTitle).removeClass("dx-icon-edit").html("<i class='fa fa-eye'></i>");
                }

                if (Settings.FormData != null && e.data.leaveAdjustmentId === Settings.FormData.LeaveAdjustmentId) {
                    e.cellElement.find(".dx-link-edit").remove();
                }

                if (!e.data.isEditable) {
                    e.cellElement.find(".dx-link-edit").remove();
                    e.cellElement.find(".dx-link-delete.fa-trash").remove();
                }
            }
        }

        public async OnEditingStart(e: { component: DevExpress.ui.dxDataGrid, key: number, data: any, cancel: boolean }) {
            e.cancel = true;
            await App.ReloadForm(e.key);
            e.component.repaint();
        }

        public OnRowRemoved(e: { component: DevExpress.ui.dxDataGrid, key: number }): void {
            if (Settings.FormData.LeaveAdjustmentId === e.key) {
                App.ReloadForm();
            }
        }

        public OnSelectionChanged = (e: { component: DevExpress.ui.dxLookup, selectedItem: any }): void => {
            let options = Settings.Form.itemOption('CompanyRunId');
            if (e.selectedItem) {
                options = $.extend(options,
                    {
                        selectedText: e.selectedItem.text,
                        selectedPeriod: e.selectedItem.periodEndDate
                    });
            }
        }

        public GetData(url: URL) {
            return $.get(url.toString());
        }

        public CreateDataSource(url: string, key: string, loadParams: any) {
            return DevExpress.data.AspNet.createStore({
                key: key,
                loadUrl: url,
                loadParams: loadParams
            })
        }

        public ReloadForm = (key?: number): void => {
            ShowLoadPanel();
            $.get(`${Settings.RecordUrl}?leaveadjustmentId=${key ?? 0}`)
                .done(function (result: any) {
                    Settings.FormData = null;
                    Settings.DeltaFormData = null;
                    $("#partial").html(result);
                })
                .then(App.FinalizeAction);
        }

        public async Submit() {
            let validationResult = Settings.Form.validate();
            await Notifications.closeMessage(window.Route.NotificationId);
            if (!validationResult.isValid) {
                Notifications.showErrors((validationResult.brokenRules as any[]).map(_ => _.message), window.Route.NotificationId);
                return;
            }

            ShowLoadPanel();
            if (Settings.FormData.LeaveType > LeaveType.Special) {
                Settings.FormData.LeaveType = LeaveType.Special;
            }

            if (Settings.FormData.LeaveAdjustmentId) {
                $.ajax({
                    url: `${Settings.RecordUrl}?leaveadjustmentId=${Settings.FormData.LeaveAdjustmentId}`,
                    type: 'PATCH',
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(JSON.stringify(Settings.FormData))
                })
                    .done(async function (result: any) {
                        Settings.FormData = null;
                        Settings.DeltaFormData = null;
                        $("#partial").html(result);
                        await App.FinalizeAction();
                    })
                    .fail(function () {
                        HideLoadPanel();
                    });
            }
            else {
                $.ajax({
                    url: Settings.RecordUrl,
                    type: 'POST',
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(JSON.stringify(Settings.FormData))
                })
                    .done(async function (result: any) {
                        Settings.FormData = null;
                        Settings.DeltaFormData = null;
                        $("#partial").html(result);
                        await App.FinalizeAction();
                    })
                    .fail(function () {
                        HideLoadPanel();
                    });
            }
        }

        public async FinalizeAction() {
            Settings.HistoryGrid.refresh();
            if (Settings.FormData.LeaveAdjustmentId && Settings.FormData.RunStatus == 1) {
                let runOptions = Settings.Form.itemOption("CompanyRunId");
                runOptions.editorOptions.placeholder = Settings.FormData.RunDescription;
            }

            await App.GetLengthOfService();
            await App.GetLeaveBalance();
            Settings.Form.repaint();
            HideLoadPanel();
        }

        public OnLeaveTypeChanged(e: { component: DevExpress.ui.dxLookup, value: number }): void {
            if (e.value > LeaveType.Special) {
                let value = e.component.option("selectedItem");
                Settings.DeltaFormData.CompanyLeaveSetupId = value.companyLeaveSetupId;
                Settings.FormData.CompanyLeaveSetupId = value.companyLeaveSetupId;
            }
            else {
                Settings.DeltaFormData.CompanyLeaveSetupId = null;
                Settings.FormData.CompanyLeaveSetupId = null;
            }
        }
    }

    export let App = new ViewModel();
}