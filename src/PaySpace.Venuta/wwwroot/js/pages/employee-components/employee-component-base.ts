/// <reference path="../../definitions.d.ts" />

namespace EmployeeComponents {
    export enum ValueType {
        Amount = 14
    }

    export class ComponentConstants {
        static PageSystemArea: string = "Payroll.Components";
        static BaseSystemArea: string = "Components.Base";
        static MedicalSystemArea: string = "Components.Medical";
    }

    export class ComponentValueData {
        ComponentValueId: number;
        AliasDescription: string;
        Description: string;
        ValueType: ValueType;
        ComponentValue: number;
    }

    class Component {
        ComponentEmployeeId: number;
        ComponentCompanyId: number;
    }

    export class EmployeeComponentsBase {
        constructor(
            protected apiUrl: string,
            protected metadataUrl: string,
            protected action: string) {
        }

        public GetComponentUrl = (keys: string[], componentType: string, action: string, component: any, componentCompanyId: number): URL => {
            let apiUrl: URL = new URL(componentType + "(" + keys.map(key => component[key]).join(',') + ")", this.apiUrl);
            apiUrl.searchParams.append("action", action);
            apiUrl.searchParams.append("componentCompanyId", `${componentCompanyId}`);
            return apiUrl;
        }

        public async GetKeysForType(component: string): Promise<string[]> {
            let metadata = await MetadataClient.fetchAndParseAsync(this.metadataUrl);
            return metadata.Entities[component].Keys;
        }

        public AuditButton = (auditUrl: string, component: Component): void => {
            let auditButton = $("#component-audit-button");
            if (component) {
                let url = new URL(auditUrl, `${window.location.protocol}//${window.location.host}`);
                url.searchParams.set("RecurringComponentId", `${component.ComponentCompanyId}`);
                url.searchParams.set("url", `${window.location.pathname}/api/component/${component.ComponentCompanyId}/audit`);

                auditButton
                    .attr("data-bs-target", "#audit-modal")
                    .attr("data-bs-backdrop", "static")
                    .attr("data-bs-keyboard", "false")
                    .attr("tabindex", "-1")
                    .attr("aria-hidden", "true")
                    .attr("data-bs-toggle", "modal")
                    .attr("data-load-always", "true")
                    .attr("data-remote", url.toString())
                    .removeClass("collapse");

                let auditModal = document.querySelector("#audit-modal button");
                auditModal.setAttribute("data-bs-target", "#select-component-modal")
                auditModal.setAttribute("data-bs-toggle", "modal")
                auditModal.removeAttribute("data-bs-dismiss")
                auditModal.setAttribute("data-bs-title", "title")
            }
            else {
                auditButton.addClass("collapse");
            }

            auditButton.attr("title", Localization['Subordinate'].AuditTitle);
        }
    }

    export class ComponentFormHelper extends FormHelper.MetadataFormHelper {
        public EditorType(field: MetadataClient.IEdmProperty) {
            if (field.Name === "Comments") {
                return "dxTextArea";
            }
            else if (field.Name === "ComponentIndicatorLine") {
                return "dxSelectBox";
            }

            return field.EditorType;
        }

        public IsFieldVisible(field: MetadataClient.IEdmProperty, componentLines: any[], inPackage: boolean, enableBcoe: boolean): boolean {
            return !field.Hidden && !field.FormHidden &&
                (field.Name !== 'ComponentIndicatorLine' || (field.Name === 'ComponentIndicatorLine' && componentLines.some(i => i.Indicators != null && i.Indicators.length > 0))) &&
                (field.Name !== 'InPackage' || (field.Name === 'InPackage' && inPackage === true)) &&
                (field.Name !== 'BcoePercentage' || (field.Name === 'BcoePercentage' && enableBcoe === true));
        }

        public AddOrUpdate = async (e: { element: JQuery }) => {
            let form = e.element.parents(".dx-form").dxForm("instance");
            if (await this.ValidateData(form)) {
                await this.SaveChanges(form);
            }
        }

        public ExtraFieldsAccordionTemplate = (data: any, itemElement: JQuery, isReadOnly: boolean): void => {
            let editorContainer: JQuery = $("<div id='component-extra-fields-accordion'>");
            itemElement.append(editorContainer);
            editorContainer.dxAccordion({
                collapsible: true,
                onContentReady: function (e: any) {
                    e.component.collapseItem(0);
                },
                items: [{
                    title: Localization[ComponentConstants.BaseSystemArea].lblExtraFields,
                    data: data.formData,
                    template: (e: any, data: any, itemElement: JQuery): void => {
                        this.ExtraFieldsAFormTemplate(e, data, itemElement, isReadOnly);
                    }
                }]
            });
        }

        public ExtraFieldsAFormTemplate = (e: any, data: any, itemElement: JQuery, isReadOnly: boolean): void => {
            let editorContainer: JQuery = $("<div id='component-extra-fields-form'>");
            itemElement.append(editorContainer);
            editorContainer.dxForm({
                disabled: isReadOnly,
                formData: e.data,
                items: this.ExtraFields(),
                colCount: 2
            });
        }

        public ExtraFields = (): DevExpress.ui.dxForm.SimpleItem[] => {
            return this.entity.filter(x => x.Name === "StartDate" || x.Name === "EndDate" || x.Name === "Comments").map(field => ({
                itemType: "simple",
                dataField: field.Name,
                editorType: this.EditorType(field),
                editorOptions: field.Name == "StartDate" || field.Name == "EndDate" ? { "showClearButton": true, "dateSerializationFormat": "yyyy-MM-dd" } : undefined,
                visibleIndex: field.Order,
                label: {
                    text: Localization[ComponentConstants.BaseSystemArea][field.Name]
                }
            } as DevExpress.ui.dxForm.SimpleItem));
        }

        private ValidateData = async (form: DevExpress.ui.dxForm): Promise<boolean> => {
            let result = form.validate();
            if (!result.isValid || await this.ValidateDataGrid() === false) {
                return false;
            }

            return true;
        }

        private ValidateDataGrid = async (): Promise<boolean> => {
            if ($(".components-cell-edit-grid").length) {
                let grid = $(".components-cell-edit-grid").dxDataGrid("instance");
                await grid.saveEditData();
                if (grid.option("isValid") === false) {
                    return false;
                }
            }

            return true;
        }

        private SaveChanges = async (form: DevExpress.ui.dxForm) => {
            ShowLoadPanel();
            const url: string = form.option("recordUrl") as string;
            const commitmethod: string = form.option("CommitMethod") as string;

            const response = await fetch(url, {
                method: commitmethod,
                headers: {
                    Authorization: `Bearer ${window.Auth.AccessToken}`,
                    "Content-Type": "application/json",
                    ...this.headers
                },
                body: JSON.stringify(this.SanitizeFormData(form))
            });

            HideLoadPanel();

            if (response.ok) {
                window.dispatchEvent(new Event("Component:Saved"));
                const grid = $(`#grid-components-${this.action}`).dxDataGrid("instance");
                grid.option("loadPanel", { enabled: false });
                if (grid.option("actionId") === PayslipAction.Deduction) {
                    grid.option('componentIdsPrefilter', null);
                    grid.clearFilter();
                }

                grid.refresh();
                grid.option("loadPanel", { enabled: "auto" });
                $("#select-component-modal").modal("hide");
            } else {
                const errors = await response.json();
                Notifications.showErrors(errors.Message);
            }
        }

        private SanitizeFormData = (form: DevExpress.ui.dxForm): object => {
            let formData = form.option("formData");
            if (form.option("formData").CustomFields) {
                formData.CustomFields = formData.CustomFields.filter(_ => _);
            }

            return formData;
        }
    }
}