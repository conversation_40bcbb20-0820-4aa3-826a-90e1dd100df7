/// <reference path="../definitions.d.ts" />

namespace Notifications {
    export let getMessage: (id: string) => any;

    export function closeMessage(id: string): Promise<void> {
        return new Promise(resolve => {
            window.dispatchEvent(new CustomEvent<any>("notifications.close", { detail: { id: id, onClosed: resolve }, bubbles: true, composed: true }));
        });
    }

    export function showErrors(errors: string | string[], id?: string, title?: string) {
        window.dispatchEvent(new CustomEvent<any>("notifications.showErrors", { detail: { id: id || window.Route.NotificationId, title: title, errors: errors }, bubbles: true, composed: true }));
    }

    export function showSuccess(description: string, id?: string) {
        window.dispatchEvent(new CustomEvent<any>("notifications.showSuccess", { detail: { id: id, description: description }, bubbles: true, composed: true }));
    }

    export function showProgress(description: string, id?: string) {
        window.dispatchEvent(new CustomEvent<any>("notifications.showProgress", { detail: { id: id, description: description }, bubbles: true, composed: true }));
    }

    export function showWarning(description: string, id?: string, title?: string) {
        window.dispatchEvent(new CustomEvent<any>("notifications.showWarning", { detail: { id: id || window.Route.NotificationId, description: description, title: title }, bubbles: true, composed: true }));
    }
}

namespace LocalizationModule {
    const loadMessages = DevExpress.localization.loadMessages;
    export function useLocalization(...params) {
        const lang = document.documentElement.lang;
        const UserId = window.User.UserId;
        const areas = params.join(',');

        DevExpress.localization.locale(lang);

        jQuery.ajax({
            url: `/nextgen/api/metadata?lang=${lang}&userId=${UserId}&areas=${areas}`,
            type: 'GET',
            async: false,
            success: function (response) {
                params.forEach(param => {
                    loadMessages({
                        [document.documentElement.lang]: response[param]
                    })
                });
            }
        });
    }
}

namespace Dialog {
    if(window.User?.UserId){
        LocalizationModule.useLocalization("General");
    }
    export function confirm(messageHtml: string, title?: string) {
        let showTitle = !!title;
        const dialogOptions = {
            title,
            messageHtml,
            showTitle,
            buttons: [
                {
                    text: DevExpress.localization.formatMessage("lblCancel"),
                    onClick: () => {
                        return false;
                    }
                },
                {
                    text: DevExpress.localization.formatMessage("Continue"),
                    onClick: () => {
                        return true;
                    }
                }
            ]
        };
        return DevExpress.ui.dialog.custom(dialogOptions).show();
    }
}

$(function () {
    let $document = $(document);
    let $window = $(window);
    const DESKTOP_MIN_WIDTH = 1199;

    $document.ajaxStart(() => {
        $("input[type='submit'], button[type='submit']").addClass("disabled").attr("disabled", "disabled");
        $(".dx-widget.dx-button").each(function () {
            let dxButton = DevExpress.ui.dxButton.getInstance(this);
            if (dxButton && dxButton.option("useSubmitBehavior")) {
                dxButton.option("disabled", true);
            }
        });
    });

    $document.ajaxComplete(() => {
        $("input[type='submit'], button[type='submit']").removeClass("disabled").removeAttr("disabled");
        $(".dx-widget.dx-button").each(function () {
            let dxButton = DevExpress.ui.dxButton.getInstance(this);
            if (dxButton && dxButton.option("useSubmitBehavior")) {
                dxButton.option("disabled", false);
            }
        });
    });

    $document.on("submit", "form", async function (e: JQuery.SubmitEvent) {
        const validationResult = $(this).dxValid();
        if (validationResult.isValid) {
            await validationResult.hide();
        }

        if (this.getAttribute("data-ajax")) {
            // ajaxStart, ajaxComplete events handle the disable.
            return;
        }

        let o = e.originalEvent as SubmitEvent;
        if (o && "submitter" in o) {
            let widget = $(o.submitter).closest(".dx-widget.dx-button");
            let dxButton = widget.length > 0 && DevExpress.ui.dxButton.getInstance(widget);
            if (dxButton) {
                dxButton.option("disabled", true);
            } else {
                $(o.submitter).addClass("disabled").attr("disabled", "disabled");
            }
        } else {
            // Fallback in case "submitter" property does not exist.
            $(this).find("input[type='submit'], button[type='submit']").each(function () {
                let widget = $(this).closest(".dx-widget.dx-button");
                let dxButton = widget.length > 0 && DevExpress.ui.dxButton.getInstance(widget);
                if (dxButton) {
                    dxButton.option("disabled", true);
                } else {
                    $(this).addClass("disabled").attr("disabled", "disabled");
                }
            });
        }

        // Safeguard against duplicate submit events that may get through before disabling submit buttons
        if (this.getAttribute("data-submitted")) {
            e.preventDefault();
            return;
        }

        this.setAttribute("data-submitted", true)
    });

    $document.on("click", "[data-bs-toggle='modal'][data-remote]", function (this: HTMLElement) {
        let target = $(this.getAttribute("data-bs-target") + " .modal-body");
        let reload = target.html() === "" || this.getAttribute("data-load-always");
        if (reload) {
            target.load(this.getAttribute("data-remote"));
        }
    });

    $document.on("click", "[data-navigate-disable]", function (this: HTMLAnchorElement) {
        $(this).addClass("disabled").attr("disabled", "disabled");
    });

    $document.on("click", "[data-navigate-confirm]", function (this: HTMLAnchorElement, e: JQuery.Event) {
        e.preventDefault();

        let href = this.href;
        let message = this.getAttribute("data-navigate-confirm");

        DevExpress.ui.dialog.confirm(message, "", false).done(result => {
            if (result) {
                window.location.href = href;
            }
        });

        return false;
    });

    /*
     * Update dx grids when the menu and tab pane changes.
     */
    $document.on("shown.bs.tab", 'a[data-bs-toggle="tab"]', function (e) {
        setTimeout(function () {
            let target = $(e.target).attr("href");
            $(target).find(".dx-widget").has(".dx-datagrid").dxDataGrid("updateDimensions");
        });
    });

    $('[data-toggle="menubar"]').click(function () {
        setTimeout(function () {
            $(".dx-widget").has(".dx-datagrid").dxDataGrid("updateDimensions");
        });
    });

    /*
     * Tooltips
     */
    $('[data-bs-toggle="tooltip"]').tooltip();

    /*
     * Keep scrollbar focused on remaining modal if another one was closed.
     */
    const $modal = $('.modal:not(.fullscreen)');
    $modal.on('hidden.bs.modal', () => {
        if ($modal.is(":visible")) {
            $('body').addClass('modal-open');
        }
    });

    $(".modal[data-clear-on-close=true]").on('hidden.bs.modal', (e) => {
        $(e.target).removeData('bs.modal').find(".modal-body").html('');
    });

    // Trigger a single resize event when resizing from Mobile to Dekstop or Dekstop to Mobile based on DESKTOP_MIN_WIDTH
    let resizeTimeout;
    let previousWidth = $('body').width();
    $window.on("resize", function () {
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
            resizeTimeout = false;
        }
        // Check user preference when resizing to only toggle if desired
        resizeTimeout = setTimeout(function () {
            const _bodyWidth = $('body').width();
            // Trigger event based on mobile vs desktop
            if (previousWidth <= DESKTOP_MIN_WIDTH && _bodyWidth > DESKTOP_MIN_WIDTH) {
                $document.trigger('mobileToDesktopResize');
            } else if (previousWidth > DESKTOP_MIN_WIDTH && _bodyWidth <= DESKTOP_MIN_WIDTH) {
                $document.trigger('destktopToMobileResize');
            }
            previousWidth = _bodyWidth;
        }, 100);
    });
});

jQuery.fn.loading = function (this: JQuery, display: boolean): JQuery {
    if (display === true) {
        let loading = this.children(".loading");
        if (loading.length === 0) {
            let text = this.text();

            this.wrapInner("<span class='collapse'></span>");
            this.append("<span class='loading'><i class='fas fa-spinner fa-spin'></i> " + text + "</span>");
            this.attr("disabled", "disabled");
        }
    } else {
        this.children(".loading").remove();
        this.find(".collapse > *").unwrap();
        this.removeAttr("disabled");
    }

    return this;
};

(function (jQuery) {
    let orig_fn_load = jQuery.fn.load;
    (<any>jQuery.fn.load) = function (this: JQuery, url: string, data: JQuery.PlainObject, options: { showLoadPanel?: boolean; }): JQueryPromise<{ responseText: string, textStatus: JQuery.Ajax.TextStatus, jqXHR: JQuery.jqXHR }> {

        if (options === undefined) {
            options = {
                showLoadPanel: true
            };
        }

        if (options.showLoadPanel === undefined) {
            options.showLoadPanel = true;
        }

        let updateValidation = () => {
            let form = this.parents("form");
            form.updateValidation();
        };

        let loadPanel: DevExpress.ui.dxLoadPanel;
        let timeout = setTimeout(function () {
            if (options.showLoadPanel) {
                loadPanel = ShowLoadPanel();
            }
        }, 500);

        let d = $.Deferred();

        orig_fn_load.apply(this,
            [
                url,
                data,
                function (responseText: string, textStatus: JQuery.Ajax.TextStatus, jqXHR: JQuery.jqXHR) {
                    updateValidation();

                    if (loadPanel) {
                        HideLoadPanel();
                    } else {
                        clearTimeout(timeout);
                    }

                    if (textStatus === "error") {
                        d.reject(jqXHR.responseText);
                    } else {
                        d.resolve(responseText, textStatus, jqXHR);
                    }
                }
            ]);

        return d.promise();
    };
}(jQuery));

function NumberFormatter(value: number): string {
    if (!value) {
        value = 0;
    }

    return new Intl.NumberFormat(undefined, { minimumFractionDigits: window.User.DecimalPlaces, maximumFractionDigits: window.User.DecimalPlaces }).format(value);
}

function DateTimeFormatter(value: string | Date): string {
    if (value) {
        if (typeof value === "string") {
            value = new Date(value);
        }

        if (value.getHours() === 0 && value.getMinutes() === 0 && value.getSeconds() === 0) {
            return new Intl.DateTimeFormat(undefined, { dateStyle: "short" }).format(value);
        }

        return new Intl.DateTimeFormat(undefined, { dateStyle: "short", timeZone: window.User.Timezone }).format(value);
    }

    return undefined;
}

function GetEmptyDate(): Date {
    let d = new Date();
    d.setHours(0, 0, 0, 0);

    return d;
}

function ShowLoadPanel(): DevExpress.ui.dxLoadPanel {
    return $("#load-panel").dxLoadPanel({
        visible: true,
        shadingColor: "rgba(255,255,255, .7)"
    }).dxLoadPanel("instance");
}

function HideLoadPanel() {
    $("#load-panel").dxLoadPanel("hide");
}