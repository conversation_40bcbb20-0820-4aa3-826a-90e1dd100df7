/// <reference path="../../node_modules/devextreme-dist/ts/dx.all.d.ts" />
/// <reference path="../../node_modules/devextreme-aspnet-data/js/dx.aspnet.data.d.ts" />
/// <reference path="../../node_modules/knockout/build/types/knockout.d.ts" />

/// <reference path="../../node_modules/@types/bootstrap/index.d.ts" />
/// <reference path="../../node_modules/@types/globalize/index.d.ts" />
/// <reference path="../../node_modules/@types/jquery/index.d.ts" />
/// <reference path="../../node_modules/@types/jquery.validation/index.d.ts" />

/// <reference path="definitions.devextreme.d.ts" />

declare const Localization: {
    [key: string]: {
        [key: string]: string;
    };
};

interface Window {
    Auth: {
        AccessToken: string;
    };
    User: {
        UserId: number;
        UserType: UserType;
        Country: string;
        Timezone: string;
        Locale: string;
        SessionId: string;
        DecimalFormat: string;
        DecimalPlaces: number;
        PercentageFormat: string;
        Theme?: string;
    };
    Route: {
        Area: string;
        Action: string;
        Controller: string;
        CompanyId?: number;
        EmployeeId?: number;
        FrequencyId?: number;
        NotificationId: string;
    };
    urls: {
        logout: string;
        timeout: string;
    };
}

interface JQueryStatic {
    validator: JQueryValidation.ValidatorStatic;
}

interface JQuery {
    load(url: string, data: JQuery.PlainObject): JQueryPromise<{ responseText: string, textStatus: JQuery.Ajax.TextStatus, jqXHR: JQuery.jqXHR }>;
    loading(display: boolean): void;
    formBuilder(formOptions: object): any;
}

declare module JQueryValidation {
    export interface ValidatorStatic {
        unobtrusive: {
            options: ValidationOptions;
            parse(selector: JQuery): void;
        };
    }
}

declare module DevExpress.ui {
    export interface dxToolbarItem {
        name?: string;
    }

    export interface dxFormItem extends dxFormSimpleItem, dxFormTabbedItem, dxFormEmptyItem, dxFormButtonItem {
        disabled?: boolean;
        editorType?: any;
        fieldGroup?: string;
    }
}

declare module DevExpress.ui.dialog {
    export function confirm (
        messageHtml: string,
        title: string,
        showTitle: boolean
    ): DevExpress.core.utils.DxPromise<boolean>;
    export function custom(options: {
        messageHtml: string;
        title: string;
        showTitle: boolean;
        buttons: Array<{ text: string; onClick: () => boolean }>;
    }): {show: () => DevExpress.core.utils.DxPromise<boolean>};
}

declare module DevExpress.ui.dxDataGrid {
    export interface DataRow extends DevExpress.ui.dxDataGrid.Row {
        removed: boolean
    }
}

declare module DevExpress.data {
    export interface DataError extends Error {
        httpStatus: number;
    }
}

declare module ExcelJS {
    export class Workbook{
        xlsx: any;
        addWorksheet(sheetName: string): any;
        worksheets: any[];
    }
}

interface Crypto {
    randomUUID(): string;
}