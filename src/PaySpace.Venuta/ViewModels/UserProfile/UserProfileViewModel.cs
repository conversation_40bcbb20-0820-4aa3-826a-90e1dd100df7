namespace PaySpace.Venuta.ViewModels.UserProfile
{
    public class UserProfileViewModel
    {
        public string ApiUrl { get; set; }

        public string DataUrl { get; set; }

        public string SelectedContactTypeUrl { get; set; }

        public int UserTypeId { get; set; }

        public bool AllowEdit { get; set; }

        public bool IsBureauScreen { get; set; }

        public bool IsBureauUser { get; set; }

        public bool ShowPasswordField { get; set; }

        public string Flags { get; set; }

        public string CompletedContactTypes { get; set; }

        public bool IsTrainingUser { get; set; }

        public string EditUrl { get; set; }

        public string ReplicateUserUrl { get; set; }

        public bool CanReplicateUsers { get; set; }
    }
}