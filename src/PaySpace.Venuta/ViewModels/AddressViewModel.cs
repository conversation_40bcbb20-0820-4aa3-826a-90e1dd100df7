namespace PaySpace.Venuta.Areas.Employees.ViewModels
{
    using System.ComponentModel.DataAnnotations;

    using Maddalena;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Validation.Annotations;

    public abstract class AddressViewModel
    {
        public long AddressId { get; set; }

        [CountryNotRequired(CountryCode.SG)]
        [DropDownList]
        [Display(Name = "lblAddressType")]
        public AddressType? AddressType { get; set; }

        public long? EmployeeId { get; set; }

        [CountryNotRequired(CountryCode.SG)]
        [Display(Name = "lblStreetName")]
        [CountryRequired(CountryCode.IN, CountryCode.IE)]
        public string AddressLine1 { get; set; }

        [CountryRequired(CountryCode.AU)]
        [Display(Name = "lblSuburb")]
        public string AddressLine2 { get; set; }

        [CountryRequired(CountryCode.MY, CountryCode.IN, CountryCode.IE)]
        [Display(Name = "lblCityTown")]
        public string AddressLine3 { get; set; }

        [CountryNotRequired(CountryCode.SG)]
        [Display(Name = "lblAddressPostal1")]
        [CountryRequired(CountryCode.IN)]
        public string PostalAddressLine1 { get; set; }

        [Display(Name = "lblAddressPostal2")]
        public string PostalAddressLine2 { get; set; }

        [CountryNotRequired(CountryCode.SG)]
        [Display(Name = "lblAddressPostal3")]
        [CountryRequired(CountryCode.IN)]
        public string PostalAddressLine3 { get; set; }

        [CountryRequired(CountryCode.MY, CountryCode.IN, CountryCode.IE, CountryCode.AU)]
        [Display(Name = "lblCode")]
        public string AddressCode { get; set; }

        [CountryRequired(CountryCode.MY, CountryCode.IN, CountryCode.IE, CountryCode.AU)]
        [Display(Name = "lblCodePostal")]
        public string PostalAddressCode { get; set; }

        [DropDownList]
        [Display(Name = "lblProvince")]
        [CountryNotRequired(CountryCode.SG)]
        public int? ProvinceId { get; set; }

        [DropDownList]
        [Display(Name = "lblProvincePostal")]
        [CountryNotRequired(CountryCode.SG)]
        public int? PostalProvinceId { get; set; }

        [CountryNotRequired(CountryCode.SG)]
        [DropDownList]
        [Display(Name = "lblCountry")]
        public int? CountryId { get; set; }

        [CountryNotRequired(CountryCode.SG)]
        [DropDownList]
        [Display(Name = "lblCountryPostal")]
        public int? PostalCountryId { get; set; }

        [StringLength(8)]
        [Display(Name = "lblUnitNumber")]
        public string UnitNumber { get; set; }

        [Display(Name = "lblComplexName")]
        public string Complex { get; set; }

        [CountryRequired(CountryCode.IE, CountryCode.AU)]
        [Display(Name = "lblStreetNum")]
        public string StreetNumber { get; set; }

        [Display(Name = "lblSameAsPostal")]
        public bool? SameAsPostal { get; set; }

        [Display(Name = "lblIsCareofAddress")]
        public bool? IsCareofAddress { get; set; }

        [StringLength(21)]
        [Display(Name = "lblCareofAddress")]
        public string? CareOfIntermediary { get; set; }

        [Display(Name = "lblSpecialServices")]
        public string SpecialServices { get; set; }

        [DropDownList]
        [CountryRequired(CountryCode.BR, CountryCode.ES)]
        [CountryVisible(CountryCode.BR, CountryCode.ES, CountryCode.FR)]
        [Display(Name = "lblMunicipality")]
        public long? MunicipalityId { get; set; }

        [DropDownList]
        [CountryRequired(CountryCode.ES)]
        [CountryVisible(CountryCode.BR, CountryCode.ES)]
        [Display(Name = "lblAddressStreetType")]
        public long? AddressStreetTypeId { get; set; }

        public int? TaxAuthorityId { get; set; }
    }
}