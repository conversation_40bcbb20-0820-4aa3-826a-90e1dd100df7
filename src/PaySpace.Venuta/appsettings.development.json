{"Identity": {"Authority": "https://uat-identity.payspace.com/", "ClientId": "nextgen", "ClientSecret": "f7442f2a-bd04-4bf1-8b31-f1f1f2182e02"}, "ClientSettings": {"ApiUrl": "https://localhost:44393", "NotificationUrl": "https://localhost:44365", "CalcWebApiBaseUrl": "https://payspaceuat-calc-webapi.azurewebsites.net/api", "CalcCacheWebApiBaseUrl": "https://payspaceuat-calc-cacheapi.azurewebsites.net/api", "RunGenerationWebApiBaseUrl": "https://payspaceuat-rungeneration-webapi.azurewebsites.net/api", "EsocialDashboardUrl": "https://localhost:5001/authenticate", "RtiUrl": "https://uk.navigator.uat.payspace.com/authenticate", "IrasUrl": "https://sg.navigator.uat.payspace.com/authenticate", "GovernmentHubUrl": "https://app-uat-in1-ca-navigator-san.azurewebsites.net/authenticate", "UserRegionUrl": "https://func-uat-userupdate-san.azurewebsites.net/api", "DynamicFormBuilderApiUrl": "https://localhost:7002", "IntegrationApiUrl": "https://func-uat-in1-ie-navigator-san.azurewebsites.net"}, "SsrsSettings": {"Url": "http://**********/reportserver/ReportExecution2005.asmx", "Username": "reports", "Password": "tB*54eB,J?;@h^D"}, "ElasticSearch": {"Urls": ["http://localhost:9200"]}, "RedisSettings": {"Instance": "NextGen_Dev:", "DefaultConnection": "localhost:6379"}, "AzureConnections": {"ServiceBusConnection": "", "EUWServiceBusConnection": "", "StorageConnection": "DefaultEndpointsProtocol=https;AccountName=websiteattachmentsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "CosmosConnection": "AccountEndpoint=https://localhost:8081/;AccountKey=****************************************************************************************;", "CalcStorageConnection": "DefaultEndpointsProtocol=https;AccountName=payspacetables;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "TaxBreakdownConnection": "DefaultEndpointsProtocol=https;AccountName=taxcalcbreakdownuatsan;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "BankAccountValidatorClientSettings": {"ServiceKey": "6b896a6a-663b-45e3-b2e6-ab10e431b1e8"}, "MscoaSettings": {"Url": "https://bcx-mscoa.payus.co.za/"}, "ZohoAuthentication": {"Url": "https://desk.zoho.com/api/v1/"}, "IntegrationSettings": {"FunctionsKey": "02546c85-ab3e-460f-a298-b5ac9231f51f"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}}