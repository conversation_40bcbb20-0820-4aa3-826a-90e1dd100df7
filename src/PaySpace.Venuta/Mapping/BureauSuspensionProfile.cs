namespace PaySpace.Venuta.Mapping
{
    using System.Linq;

    using AutoMapper;

    using PaySpace.Venuta.Areas.Bureau.ViewModels;
    using PaySpace.Venuta.Data.Models.Bureau;

    public class BureauSuspensionProfile : Profile
    {
        public BureauSuspensionProfile()
        {
            this.CreateMap<BureauSuspensionModel, BureauSuspension>();

            this.CreateMap<BureauSuspensionModel, BureauSuspensionHistory>()
                .ForMember(_ => _.BureauSuspensionId, opts => opts.MapFrom(src => src.SuspensionId))
                .ForMember(_ => _.HistoryEntityDetailId, opts => opts.MapFrom(src => src.HistoryEntityId));

            this.CreateMap<BureauSuspensionHistory, BureauSuspensionModel>()
                .ForMember(_ => _.SuspensionId, opts => opts.MapFrom(src => src.BureauSuspensionId))
                .ForMember(_ => _.SuspensionReasonId, opts => opts.MapFrom(src => src.BureauSuspension.SuspensionReasonId))
                .ForMember(_ => _.HistoryEntityId, opts => opts.MapFrom(src => src.HistoryEntityDetailId));
        }
    }
}
