namespace PaySpace.Venuta.Mapping
{
    using AutoMapper;

    using PaySpace.Venuta.Areas.Bureau.ViewModels;
    using PaySpace.Venuta.Data.Models.Bureau;

    public class ConfigSettingsProfile : Profile
    {
        public ConfigSettingsProfile()
        {
            this.CreateMap<BureauConfigSettingModel, BureauConfigSetting>()
                .ForMember(dest => dest.BureauConfigName, opt => opt.MapFrom(src => src.BureauConfigName.Trim()))
                .ForMember(dest => dest.BureauConfigCode, opt => opt.MapFrom(src => src.BureauConfigCode.Trim()))
                .ForMember(dest => dest.BureauConfigValue, opt => opt.MapFrom(src => src.BureauConfigValue.Trim()));
            this.CreateMap<BureauConfigSetting, BureauConfigSettingModel>();
        }
    }
}