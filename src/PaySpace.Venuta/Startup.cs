namespace PaySpace.Venuta
{
    using System.Globalization;
    using System.Threading.Tasks;

    using DevExpress.AspNetCore;
    using DevExpress.AspNetCore.Reporting;

    using FluentValidation;
    using FluentValidation.AspNetCore;

    using IdentityModel;

    using Mapster;

    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Authentication.JwtBearer;
    using Microsoft.AspNetCore.Authentication.OpenIdConnect;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Localization;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.Routing.Constraints;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using Microsoft.Net.Http.Headers;

    using Newtonsoft.Json.Serialization;

    using PaySpace.Cache.Distributed;
    using PaySpace.Integrations.Acumatica;
    using PaySpace.Integrations.AwsLogs;
    using PaySpace.Integrations.QuickBooks;
    using PaySpace.Venuta.Authentication;
    using PaySpace.Venuta.Authorization;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Health;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Infrastructure.Handlers;
    using PaySpace.Venuta.Localization;
    using PaySpace.Venuta.Logging;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.CompanySettings;
    using PaySpace.Venuta.Modules.Component.SubCodes;
    using PaySpace.Venuta.Modules.Components;
    using PaySpace.Venuta.Modules.CustomForms;
    using PaySpace.Venuta.Modules.Dashboard;
    using PaySpace.Venuta.Modules.DraftValues;
    using PaySpace.Venuta.Modules.DynamicFormBuilder;
    using PaySpace.Venuta.Modules.Employee.Claims;
    using PaySpace.Venuta.Modules.Employee.Inbox;
    using PaySpace.Venuta.Modules.Employee.Positions;
    using PaySpace.Venuta.Modules.Employee.SuspensionSnapshot;
    using PaySpace.Venuta.Modules.EmployeeHistory;
    using PaySpace.Venuta.Modules.EmployeeTakeOns;
    using PaySpace.Venuta.Modules.EmploymentStability;
    using PaySpace.Venuta.Modules.GeneralLedger;
    using PaySpace.Venuta.Modules.Leave;
    using PaySpace.Venuta.Modules.Organization;
    using PaySpace.Venuta.Modules.OrgChart;
    using PaySpace.Venuta.Modules.PayRate;
    using PaySpace.Venuta.Modules.Payslips;
    using PaySpace.Venuta.Modules.PensionEnrolment;
    using PaySpace.Venuta.Modules.PublicHolidays;
    using PaySpace.Venuta.Modules.RecordOfEmployment;
    using PaySpace.Venuta.Modules.SecurityRoles;
    using PaySpace.Venuta.Modules.UserOrgPermissions;
    using PaySpace.Venuta.Modules.Users;
    using PaySpace.Venuta.Search;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authentication;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Services.Reports;
    using PaySpace.Venuta.Shared;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Telemetry;
    using PaySpace.Venuta.Validation;
    using PaySpace.Venuta.Workflow;
    using PaySpace.Venuta.Workflow.Activities;

    public class Startup
    {
        private readonly IHostEnvironment env;

        public Startup(IConfiguration configuration, IHostEnvironment env)
        {
            this.Configuration = configuration;
            this.env = env;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            TypeAdapterConfig.GlobalSettings.Scan(this.GetType().Assembly);

            services.AddApplicationInsightsTelemetry(opt => opt.EnableAdaptiveSampling = true);

            if (this.Configuration.GetValue<bool?>("EnableAllLogging") != true)
            {
                services.AddApplicationInsightsTelemetryProcessor<SuppressTelemetryFilter>();
            }

            services.ConfigureOptions<FormOptions>();
            services.ConfigureOptions<MvcOptions>();
            services.ConfigureOptions<JsonOptions>();
            services.ConfigureOptions<NextGenOpenIdConnectOptions>();
            services.ConfigureOptions<NextGenCookieAuthenticationOptions>();

            // Add config.
            var identitySettings = this.Configuration.GetSection("Identity").Get<IdentitySettings>();
            services.AddSingleton(Options.Create(identitySettings));

            services.Configure<ProductBoardSettings>(this.Configuration.GetSection("ProductBoardSettings"));

            // Add framework services.
            services.AddLocalization();
            services.AddResponseCaching();
            services.AddResponseCompression(options => options.EnableForHttps = true);

            services.AddMvc()
                .AddNewtonsoftJson()
                .AddViewLocalization()
                .AddDataAnnotationsLocalization()
                .AddFluentValidation(options =>
                {
                    options.RegisterValidatorsFromAssembly(typeof(Startup).Assembly);
                    options.ImplicitlyValidateChildProperties = true;
                });

            var mvc = services.AddControllersWithViews()
                .AddJsonOptions("PascalCase", options =>
                {
                    options.SerializerSettings.ContractResolver = new DefaultContractResolver();
                });

            services.AddRouting(opt =>
            {
                opt.ConstraintMap["long"] = typeof(CustomLongRouteConstraint);
                opt.ConstraintMap["int"] = typeof(CustomIntRouteConstraint);
            });

            if (this.env.IsDevelopment())
            {
                // https://learn.microsoft.com/en-us/aspnet/core/mvc/views/view-compilation?view=aspnetcore-9.0&tabs=visual-studio#enable-runtime-compilation-for-all-environments
                mvc.AddRazorRuntimeCompilation();

                services.AddLogging(loggingBuilder =>
                {
                    loggingBuilder.AddSeq();
                });
            }

            // DevExpress.
            services.AddDevExpressControls();
            services.ConfigureReportingServices(_ =>
            {
                _.DisableCheckForCustomControllers();
            });

            // Security.
            services.AddSecurity(this.Configuration)
                .AddTokenRefresh()
                .AddPolicies();

            services.AddAntiforgery(options =>
            {
                options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            });

            services.AddWebSecurity();

            services.AddAuthentication(options =>
                {
                    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                    options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
                    options.DefaultSignOutScheme = OpenIdConnectDefaults.AuthenticationScheme;
                })
                .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme)
                .AddOpenIdConnect(OpenIdConnectDefaults.AuthenticationScheme, options => { })
                .AddJwtBearer(
                    JwtBearerDefaults.AuthenticationScheme,
                    options =>
                    {
                        options.Authority = identitySettings.Authority;
                        options.Audience = ApiResources.NextGen;

                        options.MapInboundClaims = false;
                        options.TokenValidationParameters.NameClaimType = JwtClaimTypes.Email;
                        options.TokenValidationParameters.RoleClaimType = JwtClaimTypes.Role;

                        options.Events = new JwtBearerEvents
                        {
                            OnMessageReceived = e =>
                            {
                                e.Token = TokenRetrieval.FromQueryStringOrHeader(e.Request);
                                return Task.CompletedTask;
                            },
                            OnTokenValidated = e =>
                            {
                                // Bug Fix: temporary backwards compatibility.
                                e.Properties.Items.Add(".Token.expires_at", e.SecurityToken.ValidTo.ToString("o", CultureInfo.InvariantCulture));
                                return Task.CompletedTask;
                            }
                        };
                    })
                .AddCookie("idp");

            services.PostConfigure<CookieAuthenticationOptions>("idp", options =>
            {
                // Override the protector with a simple name - allow Identity and Nextgen to decode state.
                var dataProtector = options.DataProtectionProvider.CreateProtector("idp");
                options.TicketDataFormat = new TicketDataFormat(dataProtector);
            });

            var failIfNotAuthenticatedHandler = new RequireAuthenticatedUserRequirement();

            services.AddAuthorizationBuilder()
                .SetInvokeHandlersAfterFailure(false)
                .AddFallbackPolicy("FallbackPolicy", policy => policy.AddRequirements(failIfNotAuthenticatedHandler))
                .AddPolicy(Policies.CompanyGroup, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new CompanyGroupViewRequirement()))
                .AddPolicy(Policies.Company, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new CompanyViewRequirement()))
                .AddPolicy(Policies.ActiveCompany, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new CompanyViewRequirement { Active = true }))
                .AddPolicy(Policies.Employee, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new EmployeeViewRequirement()))
                .AddPolicy(Policies.Frequency, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new FrequencyViewRequirement()))
                .AddPolicy(Policies.OnBehalfOf, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new OnBehalfOfViewRequirement()))
                .AddPolicy(Policies.BulkUpload, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new BulkUploadRequirement()))
                .AddPolicy(Policies.Audit, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new AuditMenuRequirement()))
                .AddPolicy(Policies.Workflow, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new WorkflowViewRequirement()))
                .AddPolicy(Policies.PerformanceManagement, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new PerformanceManagementViewRequirement()))
                .AddPolicy(Policies.EmployeeEvaluation, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new EmployeeEvaluationsViewRequirement()))
                // Nextgen.
                .AddPolicy("Images", policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .AddRequirements(new CompanyGroupViewRequirement { Image = true }))
                .AddPolicy(Policies.Replicate, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .RequireAssertion(context => (this.env.IsProduction() && context.User.IsInRole(Roles.Insights))
                    || (!this.env.IsProduction() && context.User.IsInRole(UserTypeCodes.Bureau))
                    || this.env.IsDevelopment()
                    || (this.env.IsStaging() && context.User.IsInRole(Roles.TopLevel))))
                .AddPolicy("CAB", policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .RequireRole(UserTypeCodes.Bureau, UserTypeCodes.Agency, UserTypeCodes.Company))
                .AddPolicy(UserTypeCodes.Bureau, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .RequireRole(UserTypeCodes.Bureau))
                .AddPolicy(UserTypeCodes.Agency, policy => policy
                    .AddRequirements(failIfNotAuthenticatedHandler)
                    .RequireRole(UserTypeCodes.Agency));

            // Add application services.
            services.AddValidation();
            services.AddValidators();

            services.AddCustomFieldDataServices(this.Configuration.GetConnectionString("DefaultConnection"));
            services.AddLoggingServices(this.Configuration.GetConnectionString("DefaultConnection"))
                .AddPublishers();

            services.AddApplicationServices();
            services.AddLocalizationServices()
                .AddViewLocalization()
                .AddMetadataProviders();

            services.AddSearch(this.Configuration.GetSection("ElasticSearch"));
            services.AddExternalServices(this.Configuration);
            services.AddCalculationServices(this.Configuration);

            // Add infrastructure services.
            services.AddDistributedCache(this.Configuration.GetSection("RedisSettings"))
                .AddRedisClient();
            services.AddStorage(this.Configuration, this.env.IsDevelopment());
            services.AddMessageBus(this.Configuration, this.env.IsDevelopment())
                .AddRegionMessageBus(this.Configuration, this.env.IsDevelopment());

            // Modules.
            services.AddWorkflow();
            services.AddWorkflowActivities();

            services.AddComponentModules();
            services.AddPayslipModules();
            services.AddLeaveModules();
            services.AddPayRateModules();
            services.AddEmployeePositionsModules();
            services.AddJobManagementPositionModules();
            services.AddEmployeeTakeOnModules();
            services.AddGeneralLedgerModules();
            services.AddAcumaticaIntegration();
            services.AddQuickBooksIntegration();
            services.AddXeroIntegration();
            services.AddCalcBreakdown(this.Configuration["AzureConnections:CalcStorageConnection"], this.env.EnvironmentName);
            services.AddReportServices();
            services.AddDashboardModules();
            services.AddClaimModules();
            services.AddCustomFormModules();
            services.AddSubCodeModules();
            services.AddEmployeeHistoryModules();
            services.AddOrgChartServices();
            services.AddStabilityModule();
            services.AddSuspensionSnapshotModule();
            services.AddUserProfileModules();
            services.AddPublicHolidayModules();
            services.AddSecurityRoleModules();
            services.AddAwsLogSearchServices(this.Configuration.GetSection("AmazonSettings"));
            services.AddCompanySettingsModules();
            services.AddDynamicFormBuilderModuleServices(this.Configuration);
            services.AddOrganizationModules();
            services.AddInboxModules();
            services.AddPensionEnrolmentModules();
            services.AddRecordOfEmploymentModules();
            services.AddUserOrgPermissionsModules();
            services.AddDraftValuesModule();

            // Add web specific implementations.
            services.AddWebServices();

            if (this.env.IsDevelopment())
            {
                services.AddMessageHandler<DevUserUpdateHandler>();
            }

            // Health monitoring. Used with azure to gracefully restart
            services.AddCoreCustomHealthChecks()
                .AddRedisServiceHealthChecks()
                .AddDbContextHealthChecks<AuditDbContext>()
                .AddDbContextHealthChecks<ReadOnlyDbContext>();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IModelMetadataProvider metadataProvider, TelemetryConfiguration telemetryConfiguration)
        {
            this.ConfigureFluentValidationLocalization(metadataProvider);

            app.UseForwardedHeaders();

            if (this.env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();

                // Disable Application Insights Telemetry in output window.
                telemetryConfiguration.DisableTelemetry = true;
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");

                telemetryConfiguration.EnableCustomAdaptiveSampling();
            }

            app.UseHttpsRedirection();
            app.UseResponseCaching();
            app.UseResponseCompression();
            app.UseXssQueryFilterMiddleware();
            app.Use((context, next) =>
            {
                var headers = context.Response.GetTypedHeaders();
                headers.CacheControl = new CacheControlHeaderValue() { NoCache = true, NoStore = true };

                return next();
            });
            app.UseCachedStaticFiles(this.env);
            app.UseHttpMethodOverride();

            app.UseCors();

            // Health monitoring. Used with azure to gracefully restart
            app.UseCustomHealthChecks();

            app.UseAuthentication();
            app.UseEmployeeValidator();
            app.UseSecurity();
            app.UseTenantTelemetry();
            app.UseRouting();

            app.UseRequestLocalization(this.GetLocalizationOptions());
            app.Use((context, next) =>
            {
                if (CultureData.ShouldOverrideDecimalSeperator())
                {
                    var culture = new CultureInfo(CultureInfo.CurrentCulture.Name);
                    culture.NumberFormat.NumberDecimalSeparator = ".";

                    CultureInfo.CurrentCulture = culture;
                    CultureInfo.CurrentUICulture = culture;
                }

                return next();
            });

            app.UseUserSession();
            app.UseAdminPermissions();
            app.UseAuthorization();

            app.UseDevExpressControls();

            app.UseEndpoints(Routing.ConfigureRoutes);
        }

        private void ConfigureFluentValidationLocalization(IModelMetadataProvider metadataProvider)
        {
            ValidatorOptions.Global.DisplayNameResolver = (type, memberInfo, expression) =>
            {
                if (memberInfo == null)
                {
                    return null;
                }

                var property = metadataProvider.GetMetadataForType(type).Properties[memberInfo.Name];
                if (property == null)
                {
                    return null;
                }

                return property.DisplayName;
            };
        }

        private RequestLocalizationOptions GetLocalizationOptions()
        {
            var options = new RequestLocalizationOptions
            {
                SupportedCultures = CultureData.SupportedCultures,
                SupportedUICultures = CultureData.SupportedCultures,
                DefaultRequestCulture = new RequestCulture(CultureData.DefaultCulture)
            };

            options.RequestCultureProviders.Insert(1, new AuthenticatedCultureProvider());
            return options;
        }
    }
}