namespace PaySpace.Venuta.Controllers.Api
{
    using System;
    using System.ComponentModel;
    using System.Linq;
    using System.Threading.Tasks;

    using DevExtreme.AspNet.Data;
    using DevExtreme.AspNet.Mvc;

    using Humanizer;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Localization;

    using Newtonsoft.Json.Linq;

    using PaySpace.Configuration;
    using PaySpace.Venuta.Data.Models.Agency;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Bureau;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Services.Organization;

    [AllowAll]
    [DisplayName("Lookup")]
    [Route("{companyId?}/[controller]")]
    public class LookupController : ApiController
    {
        private readonly IConfiguration configuration;
        private readonly IAgencyService agencyService;
        private readonly IStringLocalizerFactory localizerFactory;
        private readonly IStringLocalizer localizer;
        private readonly ICompanyService companyService;
        private readonly IEnumService enumService;
        private readonly IBankAccountValidatorClient bankAccountValidatorClient;
        private readonly IBureauBillingCountryService bureauBillingCountryService;
        private readonly IOrganizationRegionService organizationRegionService;
        private readonly IOrganizationService organizationService;
        private readonly ICompanySettingService companySettingService;

        public LookupController(
            IConfiguration configuration,
            IAgencyService agencyService,
            IStringLocalizerFactory localizerFactory,
            ICompanyService companyService,
            IEnumService enumService,
            IBankAccountValidatorClient bankAccountValidatorClient,
            IBureauBillingCountryService bureauBillingCountryService,
            IOrganizationRegionService organizationRegionService,
            IOrganizationService organizationService,
            ICompanySettingService companySettingService)
        {
            this.configuration = configuration;
            this.agencyService = agencyService;
            this.localizer = localizerFactory.Create(typeof(Employee));
            this.localizerFactory = localizerFactory;
            this.companyService = companyService;
            this.enumService = enumService;
            this.bankAccountValidatorClient = bankAccountValidatorClient;
            this.bureauBillingCountryService = bureauBillingCountryService;
            this.organizationRegionService = organizationRegionService;
            this.organizationService = organizationService;
            this.companySettingService = companySettingService;
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/titles")]
        public async Task<object> GetTitlesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var languages = (await this.enumService.GetTitlesAsync(taxCountryId))
                .OrderBy(_ => _.TitleDescription)
                .Select(_ => new { value = _.TitleId, text = _.TitleDescription });

            return DataSourceLoader.Load(languages, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/languages")]
        public async Task<object> GetLanguagesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var languages = (await this.enumService.GetLanguagesAsync(taxCountryId))
                .OrderBy(_ => _.LanguageDescription)
                .Select(_ => new { value = _.LanguageId, text = _.LanguageDescription });

            return DataSourceLoader.Load(languages, loadOptions);
        }

        [HttpGet("api/provinces/{CountryId?}")]
        public async Task<object> GetProvincesAsync(DataSourceLoadOptions loadOptions, int countryId)
        {
            var provinces = await this.enumService.GetProvincesAsync(countryId);
            var result = provinces.OrderBy(_ => _.ProvinceDescription)
                .Select(_ => new { value = _.ProvinceId, text = _.ProvinceDescription });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("api/address-countries")]
        public async Task<object> GetAddressCountriesAsync(DataSourceLoadOptions loadOptions)
        {
            var countries = (await this.enumService.GetAddressCountriesAsync())
                .OrderBy(_ => _.AddressCountryDescription)
                .Select(_ => new { value = _.AddressCountryId, text = _.AddressCountryDescription });

            return DataSourceLoader.Load(countries, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/sdl")]
        public async Task<object> GetSdlExemptionsAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var sdlExemptions = (await this.enumService.GetSDLExemptionsAsync(taxCountryId))
                .OrderBy(_ => _.ExemptionDescription)
                .Select(_ => new { value = _.ExemptionId, text = _.ExemptionDescription });

            return DataSourceLoader.Load(sdlExemptions, loadOptions);
        }

        [HttpGet("api/standard/industry/code/header")]
        public async Task<object> GetStandardIndustryCodeHeadersAsync(DataSourceLoadOptions loadOptions)
        {
            var standardIndustryCodeHeaders = (await this.enumService.GetStandardIndustryCodeHeadersAsync())
                                              .OrderBy(_ => _.StandardIndustryCodeDescription)
                                              .Select(_ => new { value = _.StandardIndustryCodeHeaderId, text = _.StandardIndustryCodeDescription })
                                              .ToList();
            standardIndustryCodeHeaders.Add(new { value = 0, text = this.localizer.GetString("lblInheritFromCompany").Value });

            return DataSourceLoader.Load(standardIndustryCodeHeaders, loadOptions);
        }

        [HttpGet("api/standard/industry/code/sub")]
        public async Task<object> GetStandardIndustryCodeSubsAsync(DataSourceLoadOptions loadOptions, int standardIndustryCodeHeaderId)
        {
            var standardIndustryCodeSubs = (await this.enumService.GetStandardIndustryCodeSubsAsync(standardIndustryCodeHeaderId, StandardIndustryCodeLevel.Employee))
                                           .OrderBy(_ => _.SubStandardIndustryCodeDescription)
                                           .Select(_ => new { value = _.SubStandardIndustryCodeId, text = _.SubStandardIndustryCodeDescription })
                                           .ToList();

            if (standardIndustryCodeHeaderId == 0)
            {
                standardIndustryCodeSubs.Add(new { value = 0, text = this.localizer.GetString("lblInheritFromCompany").Value });
            }

            return DataSourceLoader.Load(standardIndustryCodeSubs, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/report/paths")]
        public async Task<object> GetReportPathsAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var reportPaths = (await this.enumService.GetReportPathsAsync(companyId, true))
                .Select(_ => new { value = _.ReportId, text = _.ReportName });

            return DataSourceLoader.Load(reportPaths, loadOptions);
        }

        [HttpGet("api/currencies")]
        public async Task<object> GetCurrenciesAsync(DataSourceLoadOptions loadOptions)
        {
            var currencies = (await this.enumService.GetCurrenciesAsync())
                .OrderBy(_ => _.Currency)
                .Select(_ => new { value = _.CurrencyId, text = _.Currency });

            return DataSourceLoader.Load(currencies, loadOptions);
        }

        [HttpGet("api/tax-country-statuses")]
        public async Task<object> GetTaxCountryStatusesAsync(DataSourceLoadOptions loadOptions)
        {
            var taxCountryStatuses = (await this.enumService.GetTaxCountryStatusesAsync())
                .OrderBy(_ => _.Description)
                .Select(_ => new { value = _.TaxCountryStatusId, text = _.Description });

            return DataSourceLoader.Load(taxCountryStatuses, loadOptions);
        }

        [HttpGet("api/agency-types")]
        public async Task<object> GetAgencyTypesAsync(DataSourceLoadOptions loadOptions)
        {
            var agencies = (await this.enumService.GetAgenciesAsync())
                .OrderBy(_ => _.AgencyTypeDescription)
                .Select(_ => new { value = _.AgencyTypeId, text = _.AgencyTypeDescription });

            return DataSourceLoader.Load(agencies, loadOptions);
        }

        [HttpGet("api/mfa-options")]
        public async Task<object> GetMfaOptionsAsync(DataSourceLoadOptions loadOptions)
        {
            var mfaOptions = (await this.enumService.GetMfaOptionsAsync())
                .OrderBy(_ => _.MfaDescription)
                .Select(_ => new { value = _.MfaOptionId, text = _.MfaDescription });

            return DataSourceLoader.Load(mfaOptions, loadOptions);
        }

        [HttpGet("api/bank-names")]
        public async Task<object> GetBankNamesAsync(DataSourceLoadOptions loadOptions)
        {
            var query = loadOptions.Filter?.OfType<object>().Last()?.ToString();
            var hasFiltering = loadOptions.Filter?.OfType<JArray>()?.Values<string>()?.Any() ?? false;

            var bankNames = (await this.bankAccountValidatorClient.GetBankStringListAsync(BankAccountValidatorClient.BankValidationCountries.SouthAfrica))
                        .ToList();

            if (!hasFiltering
                && !string.IsNullOrWhiteSpace(query)
                && !bankNames.Any(_ => _.Equals(query, StringComparison.InvariantCultureIgnoreCase)))
            {
                bankNames.Add(query);
            }

            var result = bankNames.Select(bankName => new { Value = bankName.ToUpper(), Text = bankName.ToUpper() });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("api/account-types")]
        public async Task<object> GetAccountTypesAsync(DataSourceLoadOptions loadOptions)
        {
            var accountTypes = (await this.enumService.GetAccountTypesAsync())
                .OrderBy(_ => _.AccountTypeDescription)
                .Select(_ => new { value = _.AccountTypeId, text = _.AccountTypeDescription });

            return DataSourceLoader.Load(accountTypes, loadOptions);
        }

        [HttpGet("api/bureau-billing-countries")]
        public async Task<object> GetBureauBillingCountriesAsync(DataSourceLoadOptions loadOptions)
        {
            var result = await this.bureauBillingCountryService.GetBureauBillingCountries()
                .OrderBy(_ => _.Country)
                .Select(_ => new { value = _.BureauBillingCountryId, text = _.Country })
                .ToListAsync();

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("api/themes")]
        public async Task<object> GetThemesAsync(DataSourceLoadOptions loadOptions)
        {
            var agencyLocalizer = this.localizerFactory.Create(typeof(Agency));
            var themes = (await this.enumService.GetThemesAsync())
                .OrderBy(_ => _.ThemeDescription)
                .Select(_ => new { value = _.ThemeDescription, text = agencyLocalizer.GetString(_.ThemeDescription) });

            return DataSourceLoader.Load(themes, loadOptions);
        }

        [HttpGet("api/theme-colours")]
        public async Task<object> GetThemeColoursAsync(DataSourceLoadOptions loadOptions)
        {
            var themeColours = (await this.enumService.GetThemeColoursAsync())
                .OrderBy(_ => _.ThemeColourDescription)
                .Select(_ => new { value = _.ThemeColourCode, text = _.ThemeColourDescription });

            return DataSourceLoader.Load(themeColours, loadOptions);
        }

        [HttpGet("api/billing-days")]
        public object GetBillingDays(DataSourceLoadOptions loadOptions)
        {
            var billingDays = Enumerable.Range(1, 7).Select(_ => new { value = _, text = _.Ordinalize() });

            return DataSourceLoader.Load(billingDays, loadOptions);
        }

        [HttpGet("api/municipality")]
        public async Task<object> GetMunicipalityAsync(DataSourceLoadOptions loadOptions, int countryId)
        {
            var municipality = (await this.enumService.GetMunicipalityAsync(countryId))
                                    .Select(_ => new { value = _.MunicipalityId, text = _.FieldCodeDescription });

            return DataSourceLoader.Load(municipality, loadOptions);
        }

        [HttpGet("api/addressStreetType")]
        public async Task<object> GetAddressStreetTypeAsync(DataSourceLoadOptions loadOptions, int countryId)
        {
            var addressStreetType = (await this.enumService.GetAddressStreetTypeAsync(countryId))
                                        .Select(_ => new { value = _.AddressStreetTypeId, text = _.FieldCodeDescription });

            return DataSourceLoader.Load(addressStreetType, loadOptions);
        }

        [HttpGet("api/monthsOfYear")]
        public async Task<object> GetMonthsOfYearAsync(DataSourceLoadOptions loadOptions)
        {
            var monthsOfYear = (await this.enumService.GetMonthsOfYearAsync())
                .OrderBy(_ => _.MonthId)
                .Select(_ => new { value = _.MonthId, text = _.MonthDescription });

            return DataSourceLoader.Load(monthsOfYear, loadOptions);
        }

        [HttpGet("api/taxCountries")]
        public async Task<object> GetTaxCountryAsync(DataSourceLoadOptions loadOptions)
        {
            var taxCountryStatuses = (await this.enumService.GetTaxCountryStatusesAsync())
                .ToDictionary(_ => _.TaxCountryStatusId, _ => _.Description);
            var taxCountries = (await this.enumService.GetTaxCountriesAsync())
                .OrderBy(_ => _.CountryDescription)
                .Select(_ => new
                {
                    value = _.CountryId,
                    text = _.CountryDescription +
                            (_.TaxCountryStatusId.HasValue ? $" ({taxCountryStatuses[_.TaxCountryStatusId.Value]})" : string.Empty),
                    iso3code = _.Iso3DigitCode,
                    iso2code = _.Iso2DigitCode
                });

            return DataSourceLoader.Load(taxCountries, loadOptions);
        }

        [HttpGet("api/feePaymentMethods")]
        public async Task<object> GetFeePaymentMethodsAsync(DataSourceLoadOptions loadOptions)
        {
            var feesPaymentMethods = (await this.enumService.GetFeesPaymentMethodsAsync())
                .OrderBy(_ => _.FeesMethodDescription)
                .Select(_ => new { value = _.FeesMethodID, text = _.FeesMethodDescription });

            return DataSourceLoader.Load(feesPaymentMethods, loadOptions);
        }

        [HttpGet("api/mainPaymentModules")]
        public async Task<object> GetMainPaymentModulesAsync(DataSourceLoadOptions loadOptions)
        {
            var paymentModuels = (await this.enumService.GetPaymentModulesAsync())
                .Where(_ => _.IsMainPaymentOption == true)
                .OrderBy(_ => _.PaymentModuleDescription)
                .Select(_ => new { value = _.PaymentModuleId, text = _.PaymentModuleDescription });

            return DataSourceLoader.Load(paymentModuels, loadOptions);
        }

        [HttpGet("api/mainPaymentModulesWithInfo")]
        public async Task<object> GetMainPaymentModulesWithInfoAsync(DataSourceLoadOptions loadOptions)
        {
            var paymentModuels = (await this.enumService.GetPaymentModulesAsync())
                .Where(_ => _.IsMainPaymentOption == true && _.IsCompanyInfoEdition == true)
                .OrderBy(_ => _.PaymentModuleDescription)
                .Select(_ => new { value = _.PaymentModuleId, text = _.PaymentModuleDescription });

            return DataSourceLoader.Load(paymentModuels, loadOptions);
        }

        [HttpGet("api/commissionEarners")]
        public object GetComissionEarnersAsync(DataSourceLoadOptions loadOptions)
        {
            var commissionEarners = this.companyService.GetCommissionEarners()
                .OrderBy(_ => _.Name)
                .Select(_ => new { value = _.CommisionId, text = _.Name })
                .ToList();

            return DataSourceLoader.Load(commissionEarners, loadOptions);
        }

        [HttpGet("api/agencies")]
        public object GetAgency(DataSourceLoadOptions loadOptions)
        {
            var mfaOption = this.agencyService.GetAllAgencies()
                .Where(_ => string.IsNullOrEmpty(_.Region) || _.Region == this.configuration.GetRegion())
                .OrderBy(_ => _.AgencyName)
                .Select(_ => new { value = _.AgencyId, text = _.AgencyName });

            return DataSourceLoader.Load(mfaOption, loadOptions);
        }

        [HttpGet("api/calculationMethods/{countryId}")]
        public async Task<object> GetCalculationMethodsAsync(DataSourceLoadOptions loadOptions, long countryId)
        {
            var taxCountry = (await this.enumService.GetTaxCountriesAsync()).SingleOrDefault(_ => _.CountryId == countryId);

            if (taxCountry == null)
            {
                return null;
            }

            var mfaOption = (await this.enumService.GetCalculationMethodsAsync()).AsEnumerable();

            if (taxCountry.NonAnnualizedYtd != true)
            {
                mfaOption = mfaOption.Where(_ => _.CalculationMethodCode != "NonAnnualizedYtd");
            }

            if (taxCountry.NonCumulativeTaxMethod != true)
            {
                mfaOption = mfaOption.Where(_ => _.CalculationMethodCode != "NonCumulativeTaxMethod");
            }

            if (taxCountry.CumulativeTaxMethod != true)
            {
                mfaOption = mfaOption.Where(_ => _.CalculationMethodCode != "CumulativeTaxMethod");
            }

            if (taxCountry.AverageTaxMethod != true)
            {
                mfaOption = mfaOption.Where(_ => _.CalculationMethodCode != "AverageTaxMethod");
            }

            if (taxCountry.PayslipTaxMethod != true)
            {
                mfaOption = mfaOption.Where(_ => _.CalculationMethodCode != "PayslipTaxMethod");
            }

            if (taxCountry.ForecastingTaxMethod != true)
            {
                mfaOption = mfaOption.Where(_ => _.CalculationMethodCode != "ForecastingTaxMethod");
            }

            var result = mfaOption.Select(_ => new { value = _.CalculationMethodCode, text = _.CalculationMethodDescription }).ToList();
            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("api/addressCountries/{countryId}")]
        public async Task<object> GetAddressCountryAsync(DataSourceLoadOptions loadOptions, long countryId)
        {
            var addressCountries = await this.enumService.GetAddressCountriesAsync();

            if (countryId == (int)TaxCountry.UnitedKingdom)
            {
                var taxCountries = await this.enumService.GetTaxCountriesAsync();
                var iso3Code = taxCountries.First(_ => _.CountryId == countryId).Iso3DigitCode;
                addressCountries = addressCountries.Where(_ => _.Iso3DigitCode == iso3Code).ToList();
            }

            var result = addressCountries.OrderBy(_ => _.AddressCountryDescription).Select(_ => new { value = _.AddressCountryId, text = _.AddressCountryDescription });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("api/inbox-statuses")]
        public async Task<object> GetInboxStatusesAsync(DataSourceLoadOptions loadOptions)
        {
            var inboxStatuses = (await this.enumService.GetInboxStatusesAsync())
                .Select(_ => new { value = _.InboxStatusId, text = _.InboxStatusDescription });

            return DataSourceLoader.Load(inboxStatuses, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/race")]
        public async Task<object> GetRaceAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var race = (await this.enumService.GetRaceAsync(taxCountryId))
                .OrderBy(_ => _.RaceDescription)
                .Select(_ => new { value = _.RaceId, text = _.RaceDescription });

            return DataSourceLoader.Load(race, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/gender")]
        public async Task<object> GetGenderAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var gender = (await this.enumService.GetGendersAsync(taxCountryId))
                .OrderBy(_ => _.GenderDescription)
                .Select(_ => new { value = _.GenderId, text = _.GenderDescription });

            return DataSourceLoader.Load(gender, loadOptions);
        }

        [HttpGet("api/contactGender")]
        public async Task<object> GetCompanyContactGenderAsync(DataSourceLoadOptions loadOptions, int taxCountryId)
        {
            var gender = (await this.enumService.GetGendersAsync(taxCountryId))
                .OrderBy(_ => _.GenderDescription)
                .Select(_ => new { value = _.GenderId, text = _.GenderDescription });
            return DataSourceLoader.Load(gender, loadOptions);
        }

        [HttpGet("api/localizations")]
        public async Task<object> GetLocalizationsAsync(DataSourceLoadOptions loadOptions)
        {
            var localizations = (await this.enumService.GetLocalizationAsync())
                 .OrderBy(_ => _.LocalizationDescription)
                 .Select(_ => new { value = _.LocalizationId, text = _.LocalizationDescription });

            return DataSourceLoader.Load(localizations, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/positiontypes")]
        public async Task<object> GetPositionTypes(DataSourceLoadOptions loadOptions, long companyId)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var positionTypes = (await this.enumService.GetPositionTypesAsync(countryId))
                .OrderBy(_ => _.PositionTypeDesc)
                .Select(_ => new { value = _.PositionTypeId, text = _.PositionTypeDesc });

            return DataSourceLoader.Load(positionTypes, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/ofocodes")]
        public async Task<object> GetOfoCodesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var setaId = this.companyService.GetSetaId(companyId) ?? 0;
            var ofoCodes = (await this.enumService.GetOfoLevelsAsync(setaId))
                .OrderBy(_ => _.LevelCode)
                .Select(_ => new { value = _.OfoLevelId, text = _.LevelCode + " - " + _.LevelDescription });

            return DataSourceLoader.Load(ofoCodes, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/occupationallevels")]
        public async Task<object> GetOccupationalLevelsAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var occupationalLevels = (await this.enumService.GetOccupationalLevelsAsync(countryId))
                .OrderBy(_ => _.Order ?? int.MaxValue)
                .ThenBy(_ => _.LevelDescription)
                .Select(_ => new { value = _.OccupationalLevelId, text = _.LevelDescription });

            return DataSourceLoader.Load(occupationalLevels, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/incident-types")]
        public async Task<object> GetIncidentTypesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var incidentTypes = (await this.enumService.GetIncidentTypesAsync())
                                    .OrderBy(_ => _.IncidentTypeDescription)
                                    .Select(_ => new { value = _.IncidentTypeId, text = _.IncidentTypeDescription, code = _.IncidentTypeCode });

            return DataSourceLoader.Load(incidentTypes, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/appeal-reasons")]
        public async Task<object> GetAppealReasonsAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var appealReasons = (await this.enumService.GetAppealReasonsAsync())
                                    .OrderBy(_ => _.AppealDescription)
                                    .Select(_ => new { value = _.AppealCode, text = _.AppealDescription });

            return DataSourceLoader.Load(appealReasons, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/appeal-outcomes")]
        public async Task<object> GetAppealOutcomesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var appealOutcome = (await this.enumService.GetAppealOutcomesAsync())
                                    .OrderBy(_ => _.AppealOutcomeDescription)
                                    .Select(_ => new { value = _.AppealOutcomeCode, text = _.AppealOutcomeDescription });

            return DataSourceLoader.Load(appealOutcome, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/settlement-reinstate-arbitration")]
        public async Task<object> GetSettlementReinstateArbitrationAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var settlementReinstatement = (await this.enumService.GetSettlementReinstatementsAsync())
                                            .Where(_ => _.SettlementReinstateCode != SettlementReinstatement.N.ToString())
                                            .OrderBy(_ => _.SettlementReinstateDescription)
                                            .Select(_ => new { value = _.SettlementReinstateCode, text = _.SettlementReinstateDescription });

            return DataSourceLoader.Load(settlementReinstatement, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/settlement-reinstate-concilliation")]
        public async Task<object> GetSettlementReinstateConcilliationAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var settlementReinstatement = (await this.enumService.GetSettlementReinstatementsAsync())
                                            .Where(_ => _.SettlementReinstateCode != SettlementReinstatement.A.ToString())
                                            .OrderBy(_ => _.SettlementReinstateDescription)
                                            .Select(_ => new { value = _.SettlementReinstateCode, text = _.SettlementReinstateDescription });

            return DataSourceLoader.Load(settlementReinstatement, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/award-favour")]
        public async Task<object> GetAwardFavourAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var awardFavour = (await this.enumService.GetAwardFavoursAsync())
                                .OrderBy(_ => _.AwardFavourDescription)
                                .Select(_ => new { value = _.AwardFavourCode, text = _.AwardFavourDescription });

            return DataSourceLoader.Load(awardFavour, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/offence-outcomes")]
        public async Task<object> GetOffenceOutcomesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var offenceOutcomes = (await this.enumService.GetOffenceOutcomesAsync())
                                    .OrderBy(_ => _.OutcomeDescription)
                                    .Select(_ => new { value = _.OutcomeCode, text = _.OutcomeDescription });

            return DataSourceLoader.Load(offenceOutcomes, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/other-outcomes")]
        public async Task<object> GetOtherOutcomesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var otherOutcomes = (await this.enumService.GetOtherOutcomesAsync())
                                    .OrderBy(_ => _.OtherOutcomeDescription)
                                    .Select(_ => new { value = _.OtherOutcomeCode, text = _.OtherOutcomeDescription });

            return DataSourceLoader.Load(otherOutcomes, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/offence-categories")]
        public async Task<object> GetOffenceCategoriesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var offenceCategories = (await this.enumService.GetOffenceCategoriesAsync())
                                        .OrderBy(_ => _.OffenceCategoryDescription)
                                        .Select(_ => new { value = _.OffenceCategoryCode, text = _.OffenceCategoryDescription });

            return DataSourceLoader.Load(offenceCategories, loadOptions);
        }

        [HttpGet("api/bureau-taxability-options/{CountryId?}")]
        public async Task<object> GetBureauTaxabilityOptionsAsync(DataSourceLoadOptions loadOptions, int countryId, long companyId)
        {
            var isActive = this.companySettingService.IsActive(companyId, CompanySettingCode.PayRate.CanadaTaxabilityOptions);

            var taxabilityOptions = (await this.enumService.GetEnumBureauTaxabilityOptionsAsync())
                .Where(_ => _.CountryId == countryId);

            if (!isActive
                && countryId == (int)TaxCountry.Canada
                && !this.User.IsInRole(UserTypeCodes.Bureau))
            {
                taxabilityOptions = taxabilityOptions.Where(_ => !_.Description.Contains(nameof(TaxCountry.Canada), StringComparison.InvariantCultureIgnoreCase)); // Filter out the outside Canada values. 
            }

            var result = taxabilityOptions
                .OrderBy(_ => _.Description)
                .Select(_ => new { value = _.BureauTaxabilityOptionId, text = _.Description });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/expected-return-types")]
        public async Task<object> GetExpectedReturnTypesAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var expectedReturnTypes = (await this.enumService.GetExpectedReturnTypesAsync(taxCountryId))
                .Select(_ => new { value = _.ExpectedReturnTypeId, text = _.ExpectedReturnTypeDescription });

            return DataSourceLoader.Load(expectedReturnTypes, loadOptions);
        }

        [HttpGet("api/allowedTaxCountries/{UserId?}")]
        public async Task<object> GetAllowedTaxCountriesAsync(DataSourceLoadOptions loadOptions, int userId)
        {
            var taxCountryStatuses = (await this.enumService.GetTaxCountryStatusesAsync())
                .ToDictionary(_ => _.TaxCountryStatusId, _ => _.Description);
            var taxCountries = (await this.enumService.GetUserAllowedTaxCountriesAsync(userId))
                .OrderBy(_ => _.CountryDescription)
                .Select(_ => new
                {
                    value = _.CountryId,
                    text = _.CountryDescription +
                            (_.TaxCountryStatusId.HasValue ? $" ({taxCountryStatuses[_.TaxCountryStatusId.Value]})" : string.Empty),
                    iso3code = _.Iso3DigitCode,
                    iso2code = _.Iso2DigitCode
                });

            return DataSourceLoader.Load(taxCountries, loadOptions);
        }

        [HttpGet("api/org-regions")]
        [Authorize(Policies.Company)]
        public async Task<object> GetOrganizationRegions(DataSourceLoadOptions loadOptions, long companyId)
        {
            var organizationRegions = this.organizationRegionService.GetRegions(companyId)
                                                                    .Select(_ => new
                                                                    {
                                                                        value = _.RegionId,
                                                                        text = _.Description,
                                                                        inactive = _.InactiveDate.HasValue
                                                                    });

            return await DataSourceLoader.LoadAsync(organizationRegions, loadOptions);
        }

        [HttpGet("api/org-units")]
        [Authorize(Policies.Company)]
        public async Task<object> GetOrganizationUnits(DataSourceLoadOptions loadOptions, long companyId)
        {
            var organizationUnits = this.organizationService.GetByCompanyId(companyId)
                                                            .Select(_ => new
                                                            {
                                                                value = _.OrganizationGroupId,
                                                                text = _.Description + " - " + _.OrganizationLevel.Description,
                                                                inactive = _.InactiveDate.HasValue,
                                                                parentId = _.ParentOrganizationGroupId
                                                            });

            return await DataSourceLoader.LoadAsync(organizationUnits, loadOptions);
        }

        [HttpGet("api/user-status")]
        public async Task<object> GetUserStatus(DataSourceLoadOptions loadOptions)
        {
            var userStatuses = await this.enumService.GetUserStatusAsync();
            var result = userStatuses.OrderBy(_ => _.StatusDescription)
                .Select(_ => new { value = _.StatusId, text = _.StatusDescription });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [HttpGet("api/roster-types")]
        public async Task<object> GetRosterTypes(DataSourceLoadOptions loadOptions)
        {
            var rosterTypes = await this.enumService.GetRosterTypesAsync();
            var result = rosterTypes.Select(_ => new { value = _.RosterTypeID, text = _.Description });

            return DataSourceLoader.Load(result, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/employeePaymentMethod")]
        public async Task<object> GetEmployeePaymentMethodAsync(DataSourceLoadOptions loadOptions, long companyId)
        {
            var taxCountryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var gender = (await this.enumService.GetEmployeePaymentMethodAsync(taxCountryId))
                .OrderBy(_ => _.PayMethodDescription)
                .Select(_ => new { value = _.PayMethodId, text = _.PayMethodDescription });

            return DataSourceLoader.Load(gender, loadOptions);
        }

        [HttpGet("api/ee-functions")]
        public async Task<object> GetEEFunctionsAsync(DataSourceLoadOptions loadOptions)
        {
            var eeFunctions = (await this.enumService.GetEEFunctionsAsync())
                .OrderBy(_ => _.EEFunctionDescription)
                .Select(_ => new { value = _.EEFunctionId, text = _.EEFunctionDescription });

            return DataSourceLoader.Load(eeFunctions, loadOptions);
        }

        [HttpGet("api/bee-levels")]
        public async Task<object> GetBEELevelsAsync(DataSourceLoadOptions loadOptions)
        {
            var beeLevels = (await this.enumService.GetBEELevelsAsync())
                .OrderBy(_ => _.Order ?? int.MaxValue)
                .ThenBy(_ => _.BeeLevelDescription)
                .Select(_ => new { value = _.BeeLevelId, text = _.BeeLevelDescription });

            return DataSourceLoader.Load(beeLevels, loadOptions);
        }

        [Authorize(Policies.Company)]
        [HttpGet("api/org-units/effective/{effectiveDate}")]
        public async Task<object> GetActiveOrganizationUnits(DataSourceLoadOptions loadOptions, long companyId, DateTime effectiveDate)
        {
            var activeOrgGroups = this.organizationService.GetActiveOrganizationGroups(companyId);

            // The children with inactive parents are filtered out
            var filteredOrgGroups = activeOrgGroups
                .Where(_ =>
                    _.ParentOrganizationGroupId == -1 ||
                    activeOrgGroups.Any(org => org.OrganizationGroupId == _.ParentOrganizationGroupId))
                .OrderBy(_ => _.Description)
                .Select(_ => new
                {
                    _.OrganizationGroupId,
                    _.ParentOrganizationGroupId,
                    _.Description,
                    ParentOrganizationGroup = _.ParentOrganizationGroup.UploadCode,
                    OrganizationGroup = _.UploadCode,
                    CostCentre = _.CostCentre == true,
                    _.InactiveDate,
                    Inactive = _.InactiveDate <= effectiveDate,
                    Expanded = true
                });

            return await DataSourceLoader.LoadAsync(filteredOrgGroups, loadOptions);
        }

        [HttpGet("api/split-options")]
        public async Task<object> GetBasedOnSplitOptionsAsync(DataSourceLoadOptions loadOptions)
        {
            var splitOptions = (await this.enumService.GetBasedOnSplitOptionsAsync())
                .OrderBy(_ => _.BasedOnSplitOptionDescription)
                .Select(_ => new { value = _.BasedOnSplitOptionCode, text = _.BasedOnSplitOptionDescription });

            return DataSourceLoader.Load(splitOptions, loadOptions);
        }
    }
}