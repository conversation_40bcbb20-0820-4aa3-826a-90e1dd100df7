namespace PaySpace.Venuta.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Globalization;
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Duende.IdentityModel;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authentication.Cookies;
    using Microsoft.AspNetCore.Authentication.JwtBearer;
    using Microsoft.AspNetCore.Authentication.OpenIdConnect;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.DataProtection;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Localization;
    using Microsoft.Extensions.Logging;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Authentication;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Filters;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.Security.Identity;
    using PaySpace.Venuta.ViewModels;

    [AllowAll]
    public class AccountController : Controller
    {
        private readonly ILogger logger;
        private readonly IStringLocalizer localizer;
        private readonly IHostEnvironment env;
        private readonly IManagerService managerService;

        public AccountController(
            ILogger<AccountController> logger,
            IStringLocalizer<CompleteMessageFilter> localizer,
            IHostEnvironment env,
            IManagerService managerService)
        {
            this.logger = logger;
            this.localizer = localizer;
            this.env = env;
            this.managerService = managerService;
        }

        [DisplayName(SystemAreas.Account.Access)]
        public async Task<IActionResult> AccessDenied(bool invalidEmployeeFrequency = false)
        {
            var vm = new AccessDeniedViewModel
            {
                IsCompanyInActive = this.TempData.TryGetValue("isCompanyInactive", out _)
            };

            if (invalidEmployeeFrequency && this.managerService.TryGetUserSessionSettings(this.HttpContext, this.User, out var session) && session.EmployeeId.HasValue)
            {
                await this.ErrorMessageAsync(this.localizer.GetString(SystemAreas.Notification.Keys.PayRateRequired));
                return this.RedirectToAction("Index", "Contact", new { area = string.Empty, session.EmployeeId });
            }

            if (this.managerService.TryGetSubordinate(this.HttpContext, out var subordinate))
            {
                return this.RedirectToAction("Index", "Contact", new { area = string.Empty, subordinate.EmployeeId });
            }

            return this.View(vm);
        }

        [AllowAnonymous]
        public IActionResult SignIn(string? idp, [ModelBinder(Name = "login_hint")] string? loginHint, long? sid, string? returnUrl)
        {
            var properties = this.CreateAuthenticationProperties(returnUrl);

            if (!string.IsNullOrEmpty(idp))
            {
                properties.SetParameter(OpenIdConnectParameterNames.IdentityProvider, idp);
            }

            if (!string.IsNullOrEmpty(loginHint))
            {
                properties.SetParameter(OpenIdConnectParameterNames.LoginHint, loginHint);
            }

            if (sid.HasValue)
            {
                properties.SetParameter(OpenIdConnectParameterNames.Sid, sid);
            }

            return this.Challenge(properties);
        }

        public IActionResult SignOut()
        {
            return this.SignOut(
                new AuthenticationProperties
                {
                    RedirectUri = this.Url.Action(nameof(this.SignIn))
                },
                CookieAuthenticationDefaults.AuthenticationScheme,
                OpenIdConnectDefaults.AuthenticationScheme);
        }

        [AllowAnonymous]
        public IActionResult Timeout(string returnUrl)
        {
            var properties = this.CreateAuthenticationProperties(returnUrl);
            //properties.SetParameter(OpenIdConnectParameterNames.Prompt, OpenIdConnectPrompt.Login);

            return this.Challenge(properties);
        }

        public async Task<IActionResult> Renew([FromServices] TokenValidator tokenValidator, string? returnUrl)
        {
            var result = await this.HttpContext.AuthenticateAsync();
            if (result.Succeeded)
            {
                var renewedToken = await tokenValidator.TryRefreshAsync(result.Properties);

                if (!string.IsNullOrEmpty(returnUrl))
                {
                    result.Properties.RedirectUri = this.SanitizeReturnUrl(returnUrl);
                }

                if (this.managerService.TryGetSubordinate(this.HttpContext, out var subordinate))
                {
                    result.Properties.SetString("MSS", Convert.ToString(subordinate.EmployeeId));
                }

                if (string.IsNullOrEmpty(returnUrl))
                {
                    result.Properties.SetString("disableClearCache", "true");
                }

                await this.HttpContext.SignInAsync(result.Principal, result.Properties);
                return this.View(new RenewViewModel { Succeeded = renewedToken.Succeeded, Error = renewedToken.Error, ReturnUrl = returnUrl });
            }

            return this.View(new RenewViewModel { Succeeded = false, Error = result.Failure?.Message });
        }

        public async Task<IActionResult> Switch(string returnUrl)
        {
            await this.managerService.ResetSubordinateAsync(this.HttpContext, this.User);

            UserType switchToUserType;
            if (this.User.IsInRole(Roles.ASS))
            {
                switchToUserType = this.User.IsInRole(UserTypeCodes.Employee) ? UserType.Agency : UserType.Employee;
            }
            else
            {
                switchToUserType = this.User.IsInRole(UserTypeCodes.Employee) ? UserType.Company : UserType.Employee;
            }

            await this.HttpContext.UpdateUserTypeAsync(switchToUserType);

            var messageDescription = switchToUserType == UserType.Employee
                 ? SystemAreas.Notification.Keys.UserTypeSwitchEss
                 : SystemAreas.Notification.Keys.UserTypeSwitchCss;

            await this.InfoMessageAsync(this.localizer.GetString(messageDescription));

            if (!string.IsNullOrEmpty(returnUrl))
            {
                return this.Redirect(returnUrl);
            }

            return this.RedirectToAction("Index", "Home", new { returnUrl });
        }

        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> Token([FromServices] IDataProtectionProvider dataProtectionProvider, [FromServices] TokenEndpointService tokenEndpointService, string access_token, string? state, string? returnUrl)
        {
            await this.managerService.ResetSessionAsync(this.HttpContext, this.User); // Session must be cleared first so its not used in reset subordinate.
            await this.managerService.ResetSubordinateAsync(this.HttpContext, this.User);

            var region = this.User.FindFirstValue(PaySpaceClaimTypes.Region);
            if (!string.IsNullOrEmpty(region))
            {
                this.Response.Cookies.Append("region", region);
            }

            var profile = await tokenEndpointService.GetUserInfoAsync(access_token);
            if (!profile.IsError)
            {
                var identity = this.User.Identities.First();
                foreach (var claim in profile.Claims)
                {
                    if (!this.User.HasClaim(claim.Type, claim.Value))
                    {
                        if (claim.Type == JwtClaimTypes.Role && this.User.HasClaim(JwtClaimTypes.Scope, "nextgen.ess"))
                        {
                            // Only use the role claims from the token eg. Pacey
                            // Otherwise a dual role user that is Employee and Company level has both roles added to the principal.
                            continue;
                        }

                        identity.AddClaim(claim);
                    }
                }
            }
            else
            {
                if (profile.Exception != null && !string.IsNullOrEmpty(profile.Error))
                {
                    this.logger.LogError(profile.Exception, profile.Error);
                }
                else if (!string.IsNullOrEmpty(profile.Error))
                {
                    this.logger.LogError(profile.Error);
                }
            }

            var result = await this.HttpContext.AuthenticateAsync(JwtBearerDefaults.AuthenticationScheme);
            if (result.Succeeded)
            {
                var properties = new AuthenticationProperties
                {
                    IsPersistent = this.env.IsDevelopment(),
                    ExpiresUtc = result.Properties.ExpiresUtc
                };

                var tokens = new List<AuthenticationToken>
                {
                    new() { Name = OpenIdConnectParameterNames.AccessToken, Value = access_token },
                    new() { Name = "expires_at", Value = result.Properties.ExpiresUtc?.ToString("o", CultureInfo.InvariantCulture) }
                };

                if (!string.IsNullOrEmpty(state))
                {
                    var refresh_token = dataProtectionProvider.CreateProtector("RefreshToken").Unprotect(state);
                    tokens.Add(new AuthenticationToken { Name = OpenIdConnectParameterNames.RefreshToken, Value = refresh_token });
                }

                properties.StoreTokens(tokens);

                await this.HttpContext.SignInAsync(this.User, properties);
                return this.Redirect(this.SanitizeReturnUrl(returnUrl)); // https://github.com/dotnet/aspnetcore/blob/cca7c46964f89ddccd222363f054e7b0e23c9f44/src/Security/Authentication/Cookies/src/CookieAuthenticationHandler.cs#L286 - Only redirect on the login path. Manually redirect.
            }

            return this.Unauthorized();
        }

        private AuthenticationProperties CreateAuthenticationProperties(string? returnUrl)
        {
            return new AuthenticationProperties
            {
                IsPersistent = this.env.IsDevelopment(),
                RedirectUri = this.SanitizeReturnUrl(returnUrl)
            };
        }

        private string SanitizeReturnUrl(string? returnUrl)
        {
            if (!string.IsNullOrEmpty(returnUrl) && this.Url.IsLocalUrl(returnUrl))
            {
                return returnUrl;
            }
            else
            {
                return this.Url.Action("Index", "Home");
            }
        }
    }
}