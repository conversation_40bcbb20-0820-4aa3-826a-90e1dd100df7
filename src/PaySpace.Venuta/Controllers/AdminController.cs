namespace PaySpace.Venuta.Controllers
{
    using System.Linq;
    using System.Security.Claims;
    using System.Threading.Tasks;

    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Hosting;
    using Microsoft.IdentityModel.Protocols.OpenIdConnect;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Security.Authorization;
    using PaySpace.Venuta.ViewModels;

    [AllowAll]
    [Authorize(Policies.Replicate)]
    public class AdminController : Controller
    {
        private readonly ApplicationContext context;
        private readonly IHostEnvironment env;
        private readonly IUserService userService;
        private readonly IUserSecurityService userSecurityService;

        public AdminController(ApplicationContext context, IHostEnvironment env, IUserService userService, IUserSecurityService userSecurityService)
        {
            this.context = context;
            this.env = env;
            this.userService = userService;
            this.userSecurityService = userSecurityService;
        }

        public async Task<IActionResult> ReplicateUser(string email = default)
        {
            if (!string.IsNullOrEmpty(email))
            {
                return await this.ReplicateUser(new ReplicateUserViewModel { Email = email });
            }

            // Accessible only if current environment IS NOT production (for dev, uat, staging purposes) and the user is Bureau level,
            // or if current environment IS production, AND the user is inserted into the BureauInsightUsers table.
            // or if the user is a TopLevelAgency User(Super BP) and is in staging environment.
            return this.View(new ReplicateUserViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReplicateUser(ReplicateUserViewModel model)
        {
            if (!this.TryValidateModel(model)) // Avoid calling the DB if the email format is invalid
            {
                return this.View(model);
            }

            var user = await this.userService.FindByEmailAsync(model.Email);
            if (user == null || user.UserType is UserType.Bureau)
            {
                this.ModelState.AddModelError(string.Empty, "Invalid User");
            }

            if (this.ModelState.IsValid)
            {
                var companyId = await this.context.Set<User>()
                    .Where(_ => _.UserId == user.UserId)
                    .Select(_ => _.EmployeeId.HasValue
                        ? _.Employee.CompanyId
                        : _.CompanyGroupId.HasValue
                            ? _.CompanyGroup.CompanyLinks.Select(x => x.CompanyId).FirstOrDefault()
                            : 0)
                    .SingleAsync();

                if (this.env.IsStaging() && this.User.IsInRole(Roles.TopLevel))
                {
                    var userToReplicateIsEmployeeOrCompanyUser = user.UserType is UserType.Company or UserType.Employee;
                    var availableCompanies = await this.userSecurityService.GetUserAgencyLevelCompanyIdsAsync(this.User.GetUserId());
                    if (availableCompanies.All(i => i != companyId) || !userToReplicateIsEmployeeOrCompanyUser)
                    {
                        this.ModelState.AddModelError(string.Empty, "No user exists");
                        return this.View(model);
                    }
                }

                if (user.UserStatus == UserStatus.Inactive)
                {
                    this.ModelState.AddModelError(string.Empty, "User is inactive.");
                    return this.View(model);
                }

                return this.Challenge(new AuthenticationProperties
                {
                    Parameters = { { OpenIdConnectParameterNames.IdentityProvider, PaySpaceConstants.Idp.Replicate }, { OpenIdConnectParameterNames.LoginHint, model.Email } },
                    RedirectUri = this.Url.Action("Index", "Home")
                });
            }

            return this.View(model);
        }
    }
}