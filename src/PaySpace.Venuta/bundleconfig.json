[{"outputFileName": "wwwroot/bundles/reports.min.js", "inputFiles": ["node_modules/@devexpress/analytics-core/dist/js/dx-analytics-core.min.js", "node_modules/devexpress-reporting/dist/js/dx-webdocumentviewer.min.js"], "minify": {"enabled": false}}, {"outputFileName": "wwwroot/bundles/reports.min.css", "inputFiles": ["node_modules/@devexpress/analytics-core/dist/css/dx-analytics.common.css", "node_modules/@devexpress/analytics-core/dist/css/dx-analytics.material.blue.light.compact.css", "node_modules/devexpress-reporting/dist/css/dx-webdocumentviewer.css"]}, {"outputFileName": "wwwroot/bundles/dashboard.min.js", "inputFiles": ["node_modules/@devexpress/analytics-core/dist/js/dx-analytics-core.min.js", "node_modules/@devexpress/analytics-core/dist/js/dx-querybuilder.min.js", "node_modules/devexpress-dashboard/dist/js/dx-dashboard.min.js"], "minify": {"enabled": false}}, {"outputFileName": "wwwroot/bundles/dashboard.min.css", "inputFiles": ["node_modules/@devexpress/analytics-core/dist/css/dx-analytics.common.css", "node_modules/@devexpress/analytics-core/dist/css/dx-analytics.material.blue.light.compact.css", "node_modules/@devexpress/analytics-core/dist/css/dx-querybuilder.css", "node_modules/devexpress-dashboard/dist/css/dx-dashboard.light.min.css"]}, {"outputFileName": "wwwroot/bundles/devextreme.min.js", "inputFiles": ["node_modules/knockout/build/output/knockout-latest.js", "wwwroot/lib/pivotgrid/exceljs.js", "node_modules/devextreme-dist/js/FileSaver.min.js", "node_modules/devextreme-dist/js/dx.all.js", "node_modules/devextreme-dist/js/dx.aspnet.mvc.js", "node_modules/devextreme-aspnet-data/js/dx.aspnet.data.js", "node_modules/devextreme-dist/js/localization/dx.**.js", "wwwroot/localization/dx.**.js", "../../devextreme-license-bundle.js"], "minify": {"enabled": false}}, {"outputFileName": "wwwroot/bundles/validation.min.js", "inputFiles": ["wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "wwwroot/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "wwwroot/lib/jquery-ajax-unobtrusive/dist/jquery.unobtrusive-ajax.min.js"], "minify": {"enabled": false}}, {"outputFileName": "wwwroot/bundles/ace.min.js", "inputFiles": ["wwwroot/lib/ace-builds/src-min-noconflict/ace.min.js", "wwwroot/lib/ace-builds/src-min-noconflict/ext-language_tools.min.js", "wwwroot/lib/ace-builds/src-min-noconflict/theme-dreamweaver.min.js", "wwwroot/lib/ace-builds/src-min-noconflict/theme-ambiance.min.js"], "minify": {"enabled": false}}, {"outputFileName": "wwwroot/js/site.min.js", "inputFiles": ["node_modules/bootstrap/dist/js/bootstrap.bundle.js", "wwwroot/js/site/utils.js", "wwwroot/js/site/site.js", "wwwroot/js/site/site.shared.js", "wwwroot/js/site/payspace.dx.defaults.js", "wwwroot/js/site/site.dx.js", "wwwroot/js/site/prototypes.js", "wwwroot/js/site/validation.js", "wwwroot/js/site/layout/topbar.js", "wwwroot/js/site/responsive-helper.js", "wwwroot/js/localization-helper.js"]}, {"outputFileName": "wwwroot/js/pages/company/bulkupload.min.js", "inputFiles": ["wwwroot/js/pages/form-helpers/custom-field-helper.js", "wwwroot/js/pages/company/bulkuploads/bulk-upload-error-array.js", "wwwroot/js/pages/company/bulkuploads/bulk-upload-component-filter.js", "wwwroot/js/pages/company/bulkuploads/bulk-upload-filter.js", "wwwroot/js/pages/company/bulkuploads/bulk-upload-grid.js", "wwwroot/js/pages/company/bulkuploads/bulk-upload-init.js", "wwwroot/js/pages/company/bulkuploads/dynamic-form-templates.js", "wwwroot/js/pages/form-helpers/component-api-helper.js"]}, {"outputFileName": "wwwroot/js/site/metadata.min.js", "inputFiles": ["wwwroot/lib/xmltojson/lib/xmlToJSON.min.js", "wwwroot/js/site/metadata.js"]}]