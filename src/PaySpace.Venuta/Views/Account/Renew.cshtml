@model RenewViewModel
@{
    Layout = null;
}

<script>
    window.localStorage.setItem("access_token", "@await Context.GetTokenAsync(OpenIdConnectParameterNames.AccessToken)");

    @if (!string.IsNullOrEmpty(Model.ReturnUrl))
    {
        @:window.location.href = "@Html.Raw(Model.ReturnUrl)";
    }

    @if (!Model.Succeeded)
    {
        @:console.log("Failed to renew token");
    }

    @if (!string.IsNullOrEmpty(Model.Error))
    {
        @:console.error("@Model.Error");
    }
</script>