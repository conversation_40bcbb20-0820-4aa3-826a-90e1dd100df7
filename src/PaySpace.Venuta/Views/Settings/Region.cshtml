@model RegionViewModel
@{
    Layout = "Index";
    ViewBag.Title = Localizer.GetString("TitleRegion");
}

@section scripts {
    <script>
        var cldr_path = "@Url.Content("~/lib/cldr-data/main/")";

        function onSubmit(form) {
            var culture = $("#@Html.IdFor(_ => _.Culture)").dxSelectBox("instance").option("value");

            var url = new URL(form.action);
            var params = new URLSearchParams(url.search);
            params.delete("culture");
            params.append("culture", culture);

            url.search = params.toString();
            form.action = url.toString();
        }

        document.addEventListener("DOMContentLoaded", function (event) {
            // Your code to run since DOM is loaded and ready
            var culture = $("#@Html.IdFor(_ => _.Culture)").dxSelectBox("instance").option("value");
            var d_formatter = Globalize(culture).dateFormatter();
            
            $("#date").val(d_formatter(new Date()));
        });
    </script>
}

<form method="post" onsubmit="onSubmit(this)">
    <div class="panel__body">
        <div class="row">
            <div class="col-sm-8">
                @if (Model.Culture == null)
                {
                    <alert-message type="warning" class="text-wrap">
                        @Localizer.GetString("SelectLanguage")
                    </alert-message>
                }

                <div class="form-group">
                    @Html.EditorFor(_ => _.Culture, new { Model.Cultures })
                    <script>
                        $(function() {
                            $("#@Html.IdFor(_ => _.Culture)").dxSelectBox({
                                onValueChanged: function(e) {
                                    var culture = e.value;

                                    $.when(
                                        $.getJSON(cldr_path + culture + "/numbers.json"),
                                        $.getJSON(cldr_path + culture + "/currencies.json"),
                                        $.getJSON(cldr_path + culture + "/ca-gregorian.json"),
                                        $.getJSON(cldr_path + culture + "/dateFields.json"),
                                        $.getJSON(cldr_path + culture + "/units.json")
                                    ).then(utils.normalize)
                                    .done(Globalize.load)
                                    .done(function() {
                                        var d_formatter = Globalize(culture).dateFormatter();
                                        var n_formatter = Globalize(culture).numberFormatter();

                                        $("#date").val(d_formatter(new Date()));
                                        $("#number").val(n_formatter(1234567.89));
                                    });
                                }
                            });
                        });
                    </script>
                </div>

                <div class="row py-10">
                    <div class="form-group col-md-6">
                        @Html.TextBox("date", string.Empty, new { @class = "form-control", @readonly = "readonly" })
                    </div>
                    <div class="form-group col-md-6">
                        @Html.TextBox("number", 123456.789.ToString("n", CultureInfo.CurrentUICulture), new { @class = "form-control", @readonly = "readonly" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.EditorFor(_ => _.Timezone, new { Model.Timezones })
                </div>

            </div>
        </div>
    </div>

    <page-footer>
        <btn-update />
    </page-footer>
</form>