@model RegionViewModel
@{
    Layout = "Index";
    ViewBag.Title = Localizer.GetString("TitleRegion");
}

@section scripts {
    <script>
        function onSubmit(form) {
            var culture = $("#@Html.IdFor(_ => _.Culture)").dxSelectBox("instance").option("value");

            var url = new URL(form.action);
            var params = new URLSearchParams(url.search);
            params.delete("culture");
            params.append("culture", culture);

            url.search = params.toString();
            form.action = url.toString();
        }
    </script>
}

<form method="post" onsubmit="onSubmit(this)">
    <div class="panel__body">
        <div class="row">
            <div class="col-sm-8">
                @if (Model.Culture == null)
                {
                    <alert-message type="warning" class="text-wrap">
                        @Localizer.GetString("SelectLanguage")
                    </alert-message>
                }

                <div class="form-group">
                    @Html.EditorFor(_ => _.Culture, new { Model.Cultures })
                    <script>
                        $(function() {
                            $("#@Html.IdFor(_ => _.Culture)").dxSelectBox({
                                onValueChanged: function(e) {
                                    var culture = e.value;

                                    var d_formatter = new Intl.DateTimeFormat(culture,
                                    {
                                      year: 'numeric',
                                      month: '2-digit',
                                      day: '2-digit',
                                      hour: '2-digit',
                                      minute: '2-digit',
                                      second: '2-digit',
                                      hour12: false,
                                    });
                                    var n_formatter = new Intl.NumberFormat(culture);

                                    $("#date").val(d_formatter.format(new Date()));
                                    $("#number").val(n_formatter.format(1234567.89));
                                }
                            });
                        });
                    </script>
                </div>

                <div class="row py-10">
                    <div class="form-group col-md-6">
                        @Html.TextBox("date", DateTime.Now.ToString(CultureInfo.CurrentUICulture), new { @class = "form-control", @readonly = "readonly" })
                    </div>
                    <div class="form-group col-md-6">
                        @Html.TextBox("number", 123456.789.ToString("n", CultureInfo.CurrentUICulture), new { @class = "form-control", @readonly = "readonly" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.EditorFor(_ => _.Timezone, new { Model.Timezones })
                </div>

            </div>
        </div>
    </div>

    <page-footer>
        <btn-update />
    </page-footer>
</form>