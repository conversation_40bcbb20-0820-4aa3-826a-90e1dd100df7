@using Microsoft.Extensions.Configuration
@using Microsoft.Extensions.Hosting
@using PaySpace.Configuration

@inject IHostEnvironment env
@inject IConfiguration configuration
@{
    var region = configuration.GetRegion();
    var apiUrl = configuration.GetValue<Uri>("ClientSettings:ApiUrl");
    var dynamicFomBuilderApiUrl = configuration.GetValue<Uri>("ClientSettings:DynamicFormBuilderApiUrl");
    var integrationApiUrl = configuration.GetValue<Uri>("ClientSettings:IntegrationApiUrl");
    var functionsKey = configuration.GetValue<string>("IntegrationSettings:FunctionsKey");
}

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentUICulture.Name">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />
    <meta name="robots" content="disallow">
    <base href="@Url.Content("~/")" />
    <title>@ViewData["Title"]</title>

    <meta name="ps:region" content="@region" />
    <meta name="ps:env" content="@env.EnvironmentName" />
    <meta name="ps:api" content="@apiUrl" />
    <meta name="ps:dynamicFormBuilderApi" content="@dynamicFomBuilderApiUrl" />
    <meta name="ps:integrationApi" content="@integrationApiUrl" />
    <meta name="ps:functionsKey" content="@functionsKey" />

    <partial name="_Layout-Auth-Header" />
</head>
<body>
    <vc:nav-minimal />

    <div class="page">
        @RenderBody()
    </div>

    <partial name="_Layout-Auth-Footer" />

    @RenderSection("scripts", required: false)

    <vc:notifications />
</body>
</html>
