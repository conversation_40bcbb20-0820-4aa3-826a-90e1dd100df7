<environment names="Production">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-T9GL23NRP5"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-T9GL23NRP5');
    </script>
</environment>

<!--site stylesheet -->
<environment names="Development">
    <link id="site-stylesheet" href="~/css/site.min.css" rel="stylesheet" asp-append-version="true" />
</environment>
<environment exclude="Development">
    <link id="site-stylesheet" href="~/_content/PaySpace.Venuta.Shared/css/site.min.css" rel="stylesheet" asp-append-version="true" />
</environment>

<!-- theme stylesheet -->
<skin id="skin-stylesheet" rel="stylesheet" />

<environment names="Development">
    <script src="~/lib/jquery/jquery.min.js"></script>

    <!--globalize-->
    <script src="~/bundles/globalize.min.js"></script>

    <script src="~/lib/vue/dist/vue.min.js"></script>

    <!--devextreme-->
    <script src="~/bundles/devextreme.min.js"></script>
</environment>
<environment exclude="Development">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

    <!--globalize-->
    <script src="~/bundles/globalize.min.js" asp-append-version="true"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.6.11/vue.min.js"></script>

    <!--devextreme-->
    <script src="~/bundles/devextreme.min.js" asp-append-version="true"></script>
</environment>

@*BUG: Rather do file check to see if specific culture file exists*@
@if (CultureInfo.CurrentUICulture.Name.ToLowerInvariant() == "pt-br")
{
    <script src="~/data/culture-values.@(CultureInfo.CurrentUICulture.Name).js" asp-append-version="true"></script>
}
else
{
    <script src="~/data/culture-values.@(CultureInfo.CurrentUICulture.TwoLetterISOLanguageName).js" asp-append-version="true"></script>
}

<auth-session access-token="@await Context.GetTokenAsync(OpenIdConnectParameterNames.AccessToken)" />
<script>
    $.ajaxSetup({
        headers: {
            "Accept-Language": "@CultureInfo.CurrentUICulture.Name",
            "Authorization": "Bearer " + window.Auth.AccessToken
        },
        beforeSend: function(method, ajaxOptions) {
            if (ajaxOptions.method === "GET") {
                // WAF - https://supportcenter.devexpress.com/ticket/details/t996776/waf-rules-is-blocking-get-request
                let encrypt = function(data) {
                    return data.replace('selector', 'dx-slctr').replace("contains", "dx-cntns");
                };

                if (ajaxOptions.data) {
                    ajaxOptions.data = JSON.parse(encrypt(JSON.stringify(ajaxOptions.data)))
                }
                else {
                    ajaxOptions.url = encrypt(ajaxOptions.url)
                }
            }
        },
        statusCode: {
            401: function() {
                window.location.href = urls.timeout;
            }
        }
    });
</script>