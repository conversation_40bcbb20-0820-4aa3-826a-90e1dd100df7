@using PaySpace.Venuta.ViewModels.UserProfile
@using PaySpace.Venuta.Modules.Users.Abstractions.Models
@model UserProfileViewModel

@inject IStringLocalizerFactory stringLocalizerFactory
@{
    ViewBag.Title = Localizer.GetString(SystemAreas.UserProfiles.Keys.PageHeader);
    var localizer = stringLocalizerFactory.Create(typeof(UserResult));
}

@section scripts {
    <script type="module" src="~/pages/user-profile.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.UserProfiles.Keys.PageHeader)">
        <page-header-toolbar-content>
            <button asp-visible="Model.IsBureauUser" class="btn btn-outline-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasUserSearch" aria-controls="offcanvasUserSearch">
                <i class="fa-solid fa-user-magnifying-glass"></i> @localizer.GetString("lblUserSearch")
            </button>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>
<page-content>
    <card>
        <user-profile base-url="@Model.ApiUrl"
                        data-url="@Model.DataUrl"
                        selected-contact-type-url="@Model.SelectedContactTypeUrl"
                        user-type-id="@Model.UserTypeId"
                        allow-edit="@Model.AllowEdit"
                        flags="@Model.Flags"
                        contact-types="@Model.CompletedContactTypes"
                        show-password-field="@Model.ShowPasswordField"
                        edit-url="@Model.EditUrl"
                        is-bureau-screen="@Model.IsBureauScreen"
                        is-bureau-user="@Model.IsBureauUser"
                        replicate-user-url="@Model.ReplicateUserUrl"
                        can-replicate-users="@Model.CanReplicateUsers" />
    </card>
</page-content>