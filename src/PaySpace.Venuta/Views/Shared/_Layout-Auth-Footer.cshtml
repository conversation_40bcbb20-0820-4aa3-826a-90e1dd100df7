@using System.Runtime.InteropServices
@using Microsoft.Extensions.Configuration
@using PaySpace.Venuta.Shared.TagHelpers

@inject IClaimsTenantProvider TenantProvider
@inject IConfiguration Configuration
@{
    var isSlot = Configuration.GetValue<string>("AppConfiguration:Slot");
    var routeValues = new RouteValues(ViewContext.RouteData.Values);
}

<script>
    function renewToken() {
        var renewIFrame = document.getElementById("frame-renew");
        renewIFrame.src = "@Url.Action("Renew", "Account", new { area = string.Empty, disableClearCache = true })";
    }
</script>
<iframe id="frame-renew" height="0" frameborder="0"></iframe>

<script>
    window.urls = {
        logout: "@Url.Action("SignOut", "Account", new { area = string.Empty })",
        timeout: "@Url.Action("Timeout", "Account", new { area = string.Empty })"
    };

    window.User = {
        UserId: @User.GetUserId(),
        UserType: "@User.GetUserType()",
        Country: "@TenantProvider.GetTaxCountryCode(User)",
        CountryId: "@TenantProvider.GetTaxCountryId()",
        Timezone: "@Html.Raw(TimezoneHelper.GetUserTimezone(User))",
        Locale: "@User.GetLocale()",
        SessionId: "@User.GetSessionId()",
        DecimalFormat: "#0.@(new string('0', TenantProvider.GetDecimalPlaces(User)))",
        DecimalPlaces: @TenantProvider.GetDecimalPlaces(User),
        PercentageFormat: "#0.@(new string('0', TenantProvider.GetDecimalPlaces(User)))######",
        IsMSS: @Json.Serialize(Context.Request.IsManagerSelfServiceActive()),
        IsESS: @Json.Serialize(Context.Request.IsEmployeeSelfServiceActive()),
        Theme: @Json.Serialize(User.FindFirstValue(PaySpaceClaimTypes.ThemePrimary))
    };

    window.Route = {
        Area: "@routeValues.Area",
        Action: "@routeValues.Action",
        Controller: "@routeValues.Controller",
        CompanyId: @Json.Serialize(Context.GetCompanyId()),
        EmployeeId: @Json.Serialize(Context.GetEmployeeId()),
        FrequencyId: @Json.Serialize(Context.GetFrequencyId()),
        NotificationId: "@User.GetUserId()-@routeValues.Controller-result"
    };
</script>

<script src="~/bundles/validation.min.js" asp-append-version="true"></script>

<!--site-->
<script src="~/js/site.min.js" asp-append-version="true"></script>
<script type="module" src="~/pages/custom-elements.js" asp-append-version="true"></script>

@if (CultureData.ShouldOverrideDecimalSeperator())
{
    <script>
        (function() {
            const OriginalNumberFormat = Intl.NumberFormat;
            Intl.NumberFormat = new Proxy(OriginalNumberFormat, {
              construct(target, args) {
                const instance = new target(...args);
                const originalFormat = instance.format;

                instance.format = function (value) {
                  const result = originalFormat.call(this, value);
                  const decimalSeparator = originalFormat.call(this, 1.1).replace(/\d/g, '')[0];
                  return result.replace(decimalSeparator, ".");
                };

                return instance;
              }
            });
        })();
    </script>
}
<script>
    var currency = "@User.FindFirstValue(ClaimTypes.System)";
    if (currency && currency.length > 0) {
        DevExpress.config({ defaultCurrency: currency });
    }

    /* Dropdowns to auto close when scrolling happens */
    document.querySelector(".page").addEventListener("scroll", () => {
        document.querySelector("html").dispatchEvent(new Event("scroll"));
    });
</script>

<environment exclude="Development">
    @if (string.IsNullOrEmpty(isSlot))
    {
        <script src="https://l.getsitecontrol.com/k4y285jw.js" async></script>
    }
</environment>