@model ReportViewerViewModel

<link href="~/bundles/reports.min.css" rel="stylesheet" asp-append-version="true" />
<script src="~/bundles/reports.min.js" asp-append-version="true"></script>

<style>
    .dx-field-label label {
        display: unset; /*<PERSON>tra<PERSON> sets inline-block breaking text-overflow ellipses*/
    }
</style>

<script>
    DevExpress.Analytics.Utils.fetchSetup.fetchSettings = {
        beforeSend(requestParameters) {
            if (requestParameters.method === "GET") {
                delete requestParameters.headers["Content-Type"];
            }
        }
    };
    DevExpress.Analytics.Utils.fetchSetup.fetchSettings.headers = DevExpress.Analytics.Utils.ajaxSetup.ajaxSettings.headers || {};
    DevExpress.Analytics.Utils.fetchSetup.fetchSettings.headers.Authorization = "Bearer " + window.Auth.AccessToken;

    function WebDocumentViewer_BeforeRender(s, e) {
        $(window).on('beforeunload', function() {
            s.Close();
        });
    }

    function WebDocumentViewer_DocumentReady(reportViewer) {
        @if (!Model.ShowParameters)
        {
            @:reportViewer.previewModel.parametersModel.initialize(null);
        }
    }

    function CustomizeParameterEditors(sender, e) {
        @if (Model.EditorOptions != null)
        {
            @foreach (var editor in Model.EditorOptions)
            {
                <text>
                if (e.parameter.name === "@editor.Key") {
                    var template = @Json.Serialize(editor.Value.Template);
                    if (template)
                    {
                        e.info.editor = { header: template };
                    }

                    e.info.visible = @Json.Serialize(editor.Value.Visible);
                }
                </text>
            }
        }
    }

    function OnExport(s, e) {
        e.FormData["access_token"] = window.Auth.AccessToken;
    }
</script>

<div style="width:100%; height:100%" id="report-viewer" data-bind="dxReportViewer: viewerOptions"></div>
<script>
    ko.applyBindings({
        viewerOptions: {
            reportUrl: "@Html.Raw(Model.ReportName + "?" + Model.Parameters)",
            handlerUri: "@Model.HandlerUrl",
            callbacks: {
                CustomizeParameterEditors: CustomizeParameterEditors,
                BeforeRender: WebDocumentViewer_BeforeRender,
                DocumentReady: WebDocumentViewer_DocumentReady,
                OnExport: OnExport
            }
        }
    }, document.getElementById('report-viewer'));
</script>