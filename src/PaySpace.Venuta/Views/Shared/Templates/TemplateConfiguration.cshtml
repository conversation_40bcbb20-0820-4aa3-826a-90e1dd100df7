@using PaySpace.Venuta.ViewModels.Templates
@model TemplateConfigurationViewModel

@section scripts {
    <script type="module" src="~/pages/template-configuration.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.TemplateConfiguration.Keys.PageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <template-configuration api-controller-name="@Model.ApiControllerName"
                            allow-edit="@Model.AllowEdit"
                            edit-url="@Model.EditUrl"
                            is-company="@(Model.IsCompany)">
    </template-configuration>
</page-content>