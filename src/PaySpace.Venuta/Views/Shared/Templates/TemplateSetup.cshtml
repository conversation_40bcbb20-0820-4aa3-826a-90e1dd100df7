@using PaySpace.Venuta.ViewModels.Templates
@model TemplateSetupViewModel

@section scripts {
    <script type="module" src="~/pages/template-setup.js" asp-append-version="true"></script>
    <script type="module" src="~/pages/component-variable.js" asp-append-version="true"></script>
}
 
<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.TemplateSetup.Keys.PageHeader)">
        <page-header-toolbar-content>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    @(Html.DevExtreme().TabPanel().Items(_ =>
    {
        if (!Model.HideTemplateTab)
        {
            _.Add()
                .Title(@Localizer.GetString("TemplatesTab"))
                .Template(@<text><template-setup api-controller-name="@Model.TemplateApiControllerName" allow-edit="@Model.AllowEdit"></template-setup></text>);
        }

        _.Add()
            .Title(@Localizer.GetString("ComponentVariablesTab"))
            .Template(@<text><component-variable api-controller-name="@Model.ComponentVariableApiControllerName" allow-edit="@Model.AllowEdit"></component-variable></text>);
    }))
</page-content>