@using Newtonsoft.Json
@using PaySpace.Venuta.ViewModels.Templates
@model TemplateConfigurationEditViewModel

@section scripts {
    <script type="module" src="~/pages/template-configuration-edit.js" asp-append-version="true"></script>
}

<page-header>
    <page-header-toolbar title="@Localizer.GetString(SystemAreas.TemplateConfiguration.Keys.PageHeader)">
        <page-header-toolbar-content>
            <a class="btn btn-outline-primary" data_tc="btn-add-template-configuration" href="@Model.EditUrl" asp-visible="@Model.AllowEdit && @Model.TemplateConfigurationId > 0">
                <i class="far fa-plus" aria-hidden="true"></i>
                @Localizer.GetString("TemplateConfiguration")
            </a>
            <vc:audit-trail />
        </page-header-toolbar-content>
    </page-header-toolbar>
</page-header>

<page-content>
    <card>
        <template-configuration-edit api-controller-name="@Model.ApiControllerName"
                                     templates-api-controller-name="@Model.TemplatesApiControllerName"
                                     component-variables-api-controller-name="@Model.ComponentVariablesApiControllerName"
                                     template-configuration-id="@Model.TemplateConfigurationId"
                                     base-url="@Model.BaseUrl"
                                     edit-url="@Model.EditUrl"
                                     allow-edit="@Model.AllowEdit"
                                     allow-override="@Model.AllowOverride"
                                     is-agency="@(Model.IsAgency)"
                                     is-company="@(Model.IsCompany)"
                                     is-employee="@(Model.IsEmployee)">
        </template-configuration-edit>
    </card>
</page-content>