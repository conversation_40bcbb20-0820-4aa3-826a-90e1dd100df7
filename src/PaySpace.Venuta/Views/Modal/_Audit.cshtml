@model AuditModalViewModel

@inject IStringLocalizerFactory StringLocalizerFactory
@{
    var localizer = StringLocalizerFactory.Create(SystemAreas.EmployeeDashboard.Area, null);

    var auditLocalizer = StringLocalizerFactory.Create("Audit", null);
    var auditActions = new[]
    {
        new { value = "New", text = auditLocalizer["New"].Value },
        new { value = "Edit", text = auditLocalizer["Edit"].Value },
        new { value = "Delete", text = auditLocalizer["Delete"].Value },
    };
}

@(Html.DevExtreme().DataGrid()
    .ID("grid-audit")
    .DataSource(d => d.RemoteController().LoadUrl(@Model.AuditUrl))
    .RemoteOperations(true)
    .FilterRow(_ => _.Visible(true))
    .Height("100%")
    .Columns(columns =>
        {
            columns.Add()
                .Caption(localizer.GetString("lblChangedBy"))
                .DataField("changedBy")
                .DataType(GridColumnDataType.String);
            columns.Add()
                .Caption(localizer.GetString("lblAliasName"))
                .DataField("aliasName")
                .DataType(GridColumnDataType.String)
                .CellTemplate(@<text><p class="text-pre-line"><%- data.aliasName %></p></text>);
            columns.Add()
                .Caption(localizer.GetString("lblOldValue"))
                .DataField("oldValue")
                .DataType(GridColumnDataType.String)
                .CellTemplate(@<text><p class="text-pre-line"><%- data.oldValue %></p></text>);
            columns.Add()
                .Caption(localizer.GetString("lblNewValue"))
                .DataField("newValue")
                .DataType(GridColumnDataType.String)
                .CellTemplate(@<text><p class="text-pre-line"><%- data.newValue %></p></text>);
            columns.Add()
                .Caption(localizer.GetString("lblDateOccured"))
                .DataField("dateOccured")
                .DataType(GridColumnDataType.DateTime)
                .SortOrder(SortOrder.Desc)
                .Width(175);
            columns.Add()
                .Caption(localizer.GetString("lblAuditAction"))
                .DataField("auditAction")
                .DataType(GridColumnDataType.String)
                .Lookup(lookup => lookup.DataSource(auditActions, "value").ValueExpr("value").DisplayExpr("text"))
                .Width(100);
            columns.Add()
                .Caption(localizer.GetString("lblRunOccured"))
                .DataField("runOccured")
                .DataType(GridColumnDataType.String)
                .SortOrder(SortOrder.Desc)
                .Width(175);
        }))