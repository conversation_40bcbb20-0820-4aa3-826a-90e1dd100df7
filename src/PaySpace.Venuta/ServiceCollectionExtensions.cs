namespace PaySpace.Venuta.Authentication
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using AutoMapper.EquivalencyExpression;

    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Authentication;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc.Routing;
    using Microsoft.AspNetCore.Routing;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.DependencyInjection.Extensions;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Areas.Company.ViewModelBuilder;
    using PaySpace.Venuta.Areas.Company.ViewModelBuilder.CompanyTableBuilder;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.EmploymentStatus;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Leave;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Profile;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Suspension;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.Tax;
    using PaySpace.Venuta.Areas.Employees.ViewModelBuilder.UK;
    using PaySpace.Venuta.Authorization;
    using PaySpace.Venuta.Converters;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Telemetry.TelemetryInitializers;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddWebSecurity(this IServiceCollection services)
        {
            services.AddScoped<IPaySpaceClassicService, PaySpaceClassicService>();

            // https://andrewlock.net/how-to-register-a-service-with-multiple-interfaces-for-in-asp-net-core-di.
            services.AddSingleton<ManagerTenantProvider>();
            services.AddSingleton<ITenantProvider>(sp => sp.GetRequiredService<ManagerTenantProvider>());
            services.AddSingleton<IClaimsTenantProvider>(sp => sp.GetRequiredService<ManagerTenantProvider>());

            // Authentication.
            services.AddScoped<NextGenCookieAuthenticationOptions.CustomCookieAuthenticationEvents>();
            services.AddScoped<IClaimsTransformation, ThemeClaimsTransformation>();

            services.AddSingleton<ICookieSerializer, CookieSerializer>();
            services.AddScoped<IManagerService, ManagerService>();
            services.AddScoped<IMenuUrlBuilder, NextGenMenuUrlBuilder>();
            services.AddScoped<IUserSessionService, UserSessionService>();

            // Authorization.
            services.AddScoped<IAuthorizationHandler, EmployeeEvaluationsAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, MenuAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, BulkUploadAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, OnBehalfOfAuthorizationHandler>();
            services.AddSingleton<IAuthorizationHandler, PermissionAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, WorkflowAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, ImageAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, PerformanceManagementAuthorizationHandler>();
            services.AddScoped<IAuthorizationHandler, PaymentModuleAuthorizationHandler>();

            services.AddSingleton<MatcherPolicy>(sp => CreateMatcherPolicy<DependantHistoryAttribute>(sp, PaySpaceConstants.DependantHistoryCountries));
            services.AddSingleton<MatcherPolicy>(sp => CreateMatcherPolicy<EmployeeHistoryAttribute>(sp, PaySpaceConstants.EmployeeHistoryCountries));

            return services;
        }

        public static IServiceCollection AddWebServices(this IServiceCollection services)
        {
            services.AddAutoMapper(
                config =>
                {
                    config.AddMaps(typeof(ServiceCollectionExtensions).Assembly);
                    config.AddCollectionMappers();
                },
                typeof(ServiceCollectionExtensions).Assembly);

            services.AddSingleton<ITelemetryInitializer, AuthenticatedTelemetryInitializer>();
            services.AddSingleton<IUrlHelperFactory, AmbientUrlHelperFactory>();
            services.AddSingleton<IRouteHelper, RouteHelper>();
            services.AddScoped(typeof(ICustomFieldConverterService<>), typeof(CustomFieldConverterService<>));
            services.AddScoped<ICustomFieldConverterService<TableBuilderCustomFieldValue>, TableBuilderConverterService>();
            services.AddScoped<ICustomFieldConverterService<EmployeeCustomFormFieldValue>, CustomFormConverterService<EmployeeCustomFormFieldValue>>();
            services.AddScoped<ICustomFieldConverterService<CompanyCustomFormFieldValue>, CustomFormConverterService<CompanyCustomFormFieldValue>>();
            services.AddScoped<ILeaveViewModelFactory, LeaveViewModelFactory>();

            services.AddCountryServices<IProfileViewModelBuilder>();
            services.AddCountryServices<ISuspensionViewModelBuilder>();
            services.AddCountryServices<ITaxViewModelBuilder>();
            services.AddCountryServices<ICompanyTableBuilderViewModelBuilder>();
            services.AddCountryServices<ICompanyProfileViewModelBuilder>();
            services.AddCountryServices<IAddCompanyProfileViewModelBuilder>();
            services.AddCountryServices<IPayRateViewModelBuilder>();
            services.AddCountryServices<IEmploymentStatusViewModelBuilder>();
            services.AddCountryServices<IPositionsViewModelBuilder>();
            services.AddCountryServices<IEditPayslipViewModelBuilder>();
            services.AddCountryServices<IPayslipViewModelBuilder>();
            services.AddCountryServices<IReportViewModelBuilder>();

            // Converters.
            services.AddSingleton<CustomFieldConverter>();
            services.AddSingleton<DateTimeConverter>();

            foreach (var type in GetBuilderTypes())
            {
                foreach (var interfaceType in type.GetInterfaces())
                {
                    services.TryAddScoped(interfaceType, type);
                }
            }

            return services;
        }

        private static IEnumerable<Type> GetBuilderTypes()
        {
            return typeof(ServiceCollectionExtensions).Assembly.GetTypes().Where(t => !t.IsInterface && !t.IsAbstract && t.Name.EndsWith("Builder"));
        }

        private static MatcherPolicy CreateMatcherPolicy<T>(IServiceProvider serviceProvider, string[] countryCodes)
            where T : Attribute
        {
            var tenantProvider = serviceProvider.GetRequiredService<IClaimsTenantProvider>();

            return new HistoryEndpointMatcherPolicy<T>(tenantProvider, countryCodes);
        }
    }
}