{"name": "payspace", "version": "1.0.0", "private": true, "type": "module", "scripts": {"prebuild": "node setup.js", "build": "npm run ts", "ts": "tsc --build"}, "dependencies": {"@devexpress/analytics-core": "24.2.7", "bootstrap": "5.3.3", "devexpress-dashboard": "24.2.7", "devexpress-reporting": "24.2.7", "devextreme-aspnet-data": "5.0.0", "devextreme-dist": "24.2.7", "file-saver": "2.0.5", "knockout": "3.5.1"}, "devDependencies": {"@types/bootstrap": "5.2.6", "@types/file-saver": "2.0.5", "@types/jquery": "3.5.16", "@types/jquery.validation": "1.16.7", "@types/node": "20.4.2", "gulp": "4.0.2", "gulp-concat": "2.6.1", "gulp-cssmin": "0.2.0", "merge-stream": "2.0.0", "typescript": "5.2.2"}}