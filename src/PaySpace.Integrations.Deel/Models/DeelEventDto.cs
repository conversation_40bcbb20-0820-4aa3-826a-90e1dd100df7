namespace PaySpace.Integrations.Deel.Models
{
    using System;

    using PaySpace.Venuta.Messaging.Abstractions;

    [Queue("deel-events")]
    public sealed class DeelEventMessage
    {
        public DateTime TimeStamp { get; set; }

        public string? Entity { get; set; }

        public long? EntityId { get; set; }

        public string? Source { get; set; }

        public string? EmployeeNumber { get; set; }

        public long? EmployeeId { get; set; }

        public long? CompanyId { get; set; }

        public string? AgencyName { get; set; }

        public string? CompanyName { get; set; }

        public string? ComponentCode { get; set; }

        public string? FrequencyName { get; set; }

        public string? RunDescription { get; set; }

        public long? RunId { get; set; }

        public string? CustomFormCategory { get; set; }

        public string? UserEmail { get; set; }

        public string? UpdateType { get; set; }

        public string? Region { get; set; }
    }
}