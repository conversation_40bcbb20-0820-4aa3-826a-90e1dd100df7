namespace PaySpace.Integrations.Deel
{
    using System;
    using System.Net.Http;
    using System.Net.Http.Json;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;

    using PaySpace.Integrations.Deel.Handlers;
    using PaySpace.Integrations.Deel.Models;
    using PaySpace.Integrations.Deel.Services;
    using PaySpace.Venuta.Messaging;

    public static class ServiceCollectionExtensions
    {
        public static void AddDeelEventIntegration(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient();
            var deelEventUrl = configuration.GetValue<string?>("ClientSettings:DeelEventUrl");

            if (!string.IsNullOrEmpty(deelEventUrl))
            {
                services.AddHttpClient<IDeelEventService, DeelEventService>();

                services.AddMessageHandler<DeelEventAuditMessageHandler>();
                services.AddMessageHandler<DeelEventHandler>();

                services.AddScoped<IDeelEventDataService, DeelEventDataService>();
                services.AddSingleton<IDeelEventService, DeelEventService>();
            }
        }
    }
}