namespace PaySpace.Integrations.Deel.Handlers
{
    using System;
    using System.Collections.Generic;
    using System.Collections.Immutable;
    using System.Net.Http;
    using System.Threading.Tasks;

    using MassTransit;

    using Microsoft.Extensions.Logging;

    using PaySpace.Integrations.Deel.Models;
    using PaySpace.Integrations.Deel.Services;
    using PaySpace.Venuta.Messaging;

    internal sealed class DeelEventHandler : QueueHandlerBase<DeelEventMessage>
    {
        private readonly ILogger<DeelEventHandler> logger;

        private static readonly ImmutableDictionary<int, TimeSpan> RetryTimes = new Dictionary<int, TimeSpan>()
        {
            { 1, TimeSpan.FromSeconds(2) },
            { 2, TimeSpan.FromSeconds(5) },
            { 3, TimeSpan.FromSeconds(10) }
        }.ToImmutableDictionary();

        public DeelEventHandler(ILogger<DeelEventHandler> logger)
        {
            this.logger = logger;
        }

        public override string QueueName => "deel_event";

        public override TimeSpan? DefaultMessageTimeToLive => TimeSpan.MaxValue;

        public override async Task Consume(ConsumeContext<DeelEventMessage> context)
        {
            try
            {
                var deelEventService = context.GetServiceOrCreateInstance<IDeelEventService>();
                await deelEventService.SendEventAsync(context.Message, context.CancellationToken);
            }
            catch (HttpRequestException e)
            {
                this.logger.LogError(e, "Error sending Deel Event");

                var retryCount = context.GetRedeliveryCount();
                if (retryCount < RetryTimes.Count)
                {
                    await context.Redeliver(RetryTimes[retryCount + 1]);
                }
                else
                {
                    // DeadLetter the message
                    throw;
                }
            }
        }
    }
}