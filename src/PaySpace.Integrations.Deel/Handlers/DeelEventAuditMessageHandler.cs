namespace PaySpace.Integrations.Deel.Handlers
{
    using System;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using MassTransit;

    using Microsoft.Extensions.Configuration;

    using PaySpace.Configuration;
    using PaySpace.Integrations.Deel.Models;
    using PaySpace.Integrations.Deel.Services;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Messaging;

    internal sealed class DeelEventAuditMessageHandler : MessageHandlerBase<AuditSaveMessage>
    {

        private readonly IConfiguration configuration;

        public DeelEventAuditMessageHandler(IConfiguration configuration)
        {
            this.configuration = configuration;
        }

        public override string SubscriptionName => "deel_event";

        public override string SubscriptionFilter => this.GetSupportedTypeFilter();

        public override string SubscriptionFilterName => "deel_event_filter";

        public override TimeSpan AutoDeleteOnIdle => TimeSpan.FromDays(2);

        public override async Task Consume(ConsumeContext<AuditSaveMessage> context)
        {
            if (!IsValidMessage(context))
            {
                return;
            }

            var eventDataService = context.GetServiceOrCreateInstance<IDeelEventDataService>();
            var messageBus = context.GetServiceOrCreateInstance<IMessageBus>();

            var message = context.Message;

            var entity = EntityTypeMap.GetEntityType(message);
            if (string.IsNullOrEmpty(entity) || !EntityTypeMap.SupportedTypes.Contains(message.EntityType))
            {
                return;
            }

            var evtMessage = new DeelEventMessage
            {
                Entity = entity,
                CompanyId = message.CompanyId,
                EmployeeId = message.EmployeeId,
                EntityId = message.Key ?? 0,
                TimeStamp = message.DateOccured,
                UpdateType = eventDataService.GetActionType(message),
                Region = this.configuration.GetRegion(),
                Source = this.GetSource(message.UpdateSource)
            };

            if (entity == "EditPayslip" && !await eventDataService.TryAddComponentDetailsAsync(message, evtMessage))
            {
                return;
            }

            evtMessage.AgencyName = await eventDataService.GetAgencyNameAsync(message);
            evtMessage.CompanyName = await eventDataService.GetCompanyNameAsync(message);
            evtMessage.UserEmail = await eventDataService.GetUserEmailAsync(message);
            evtMessage.EmployeeNumber = await eventDataService.GetEmployeeNumberAsync(message);
            evtMessage.CustomFormCategory = await eventDataService.GetCustomFormCategoryAsync(message);

            if (message.EntityType == nameof(EmployeeBankHeader) && evtMessage.EntityId > 0)
            {
                // when it is EmployeeBankHeader then the EntityId is the bank header id, we need to return the bank details id
                evtMessage.EntityId = await eventDataService.GetBankDetailsIdAsync(evtMessage.EntityId.Value);
            }

            await messageBus.PublishMessageAsync(evtMessage);
        }

        private static bool IsValidMessage(ConsumeContext<AuditSaveMessage> context)
        {
            return EntityTypeMap.SupportedTypes.Contains(context.Message.EntityType) && context.Message is { EmployeeId: > 0, IsDeelAgency: true };
        }

        private string GetSource(string source)
        {
            switch (source)
            {
                case RequestSource.UIApi:
                    return RequestSource.UI;
                default:
                    return source;
            }
        }

        protected override Task<bool> ProcessMessageAsync(AuditSaveMessage message, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        private string GetSupportedTypeFilter()
        {
            return $"EntityType in ({string.Join(",", EntityTypeMap.SupportedTypes.Select(type => $"'{type}'"))}) AND (Exists(EmployeeId) AND EmployeeId > -1) AND (Exists(IsDeelAgency) AND IsDeelAgency = true)";
        }
    }
}