namespace PaySpace.Integrations.Deel.Services
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using PaySpace.Integrations.Deel.Models;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Bureau;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Data.Models.Users;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;

    internal interface IDeelEventDataService
    {
        Task<string> GetUserEmailAsync(AuditSaveMessage message);

        Task<bool> TryAddComponentDetailsAsync(AuditSaveMessage message, DeelEventMessage evtMessage);

        string GetActionType(AuditSaveMessage message);

        Task<string> GetEmployeeNumberAsync(AuditSaveMessage message);

        Task<string> GetCompanyNameAsync(AuditSaveMessage message);

        Task<string> GetAgencyNameAsync(AuditSaveMessage message);

        Task<string?> GetCustomFormCategoryAsync(AuditSaveMessage message);

        Task<long?> GetBankDetailsIdAsync(long employeeBankHeaderId);
    }

    internal sealed class DeelEventDataService : IDeelEventDataService
    {
        private readonly ApplicationContext context;
        private readonly IDistributedCache distributedCache;

        public DeelEventDataService(ApplicationContext context, IDistributedCache distributedCache)
        {
            this.context = context;
            this.distributedCache = distributedCache;
        }

        public Task<string?> GetUserEmailAsync(AuditSaveMessage message)
        {
            if (message.UpdateSource == RequestSource.System)
            {
                return Task.FromResult<string?>(null);
            }

            return this.distributedCache.GetOrCreateAsync<string>(
                CacheKeys.UserEmail(message.UserId),
                () => this.context.Set<User>()
                    .Where(_ => _.UserId == message.UserId)
                    .Select(_ => _.Email)
                    .SingleAsync());
        }

        public async Task<bool> TryAddComponentDetailsAsync(AuditSaveMessage message, DeelEventMessage evtMessage)
        {
            var component = await this.GetComponentDetailsAsync(message);
            if (component == default)
            {
                return false;
            }

            evtMessage.ComponentCode = component.ComponentCode;
            evtMessage.RunId = component.RunId;

            var runDetails = await this.GetRunDetailsAsync(component.RunId);

            evtMessage.RunDescription = runDetails?.RunDescription;
            evtMessage.FrequencyName = runDetails?.FrequencyName;

            if (message.EntityType is (nameof(ComponentValues)) or (nameof(PayslipHeader)) or (nameof(PayslipLine)))
            {
                // when values are added/edited on the edit payslip screen, it is not saved in the BulkCaptureCode table, we need to get that id
                evtMessage.EntityId = await this.context.Set<BulkCaptureCode>()
                    .Where(_ => _.ComponentId == component.ComponentId && _.CompanyRunId == component.RunId)
                    .Where(_ => _.EmployeeId == message.EmployeeId)
                    .Select(_ => _.BulkCaptureId)
                    .FirstOrDefaultAsync();
            }

            return true;
        }

        public string GetActionType(AuditSaveMessage message)
        {
            return message.ActionType == "New" ? "Create" : message.ActionType;
        }

        public Task<string> GetEmployeeNumberAsync(AuditSaveMessage message)
        {
            if (message.EmployeeId > 0)
            {
                return this.distributedCache.GetOrCreateAsync(
                    CacheKeys.EmployeeNumberById(message.EmployeeId.Value),
                    () => this.context.Set<Employee>()
                        .Where(_ => _.EmployeeId == message.EmployeeId)
                        .Select(_ => _.EmployeeNumber)
                        .FirstOrDefaultAsync());
            }

            return Task.FromResult<string?>(null);
        }

        public Task<string> GetCompanyNameAsync(AuditSaveMessage message)
        {
            if (message.CompanyId > 0)
            {
                return this.distributedCache.GetOrCreateAsync(
                    CacheKeys.CompanyName(message.CompanyId.Value),
                    () => this.context.Set<Company>()
                        .Where(_ => _.CompanyId == message.CompanyId)
                        .Select(_ => _.CompanyName)
                        .FirstOrDefaultAsync());
            }

            return Task.FromResult<string?>(null);
        }

        public Task<string> GetAgencyNameAsync(AuditSaveMessage message)
        {
            if (message.AgencyId > 0)
            {
                return this.distributedCache.GetOrCreateAsync(
                    CacheKeys.AgencyName(message.AgencyId.Value),
                    () => this.context.Set<Company>()
                        .Where(_ => _.CompanyId == message.CompanyId)
                        .Select(_ => _.Agency.AgencyName)
                        .FirstOrDefaultAsync());
            }

            return Task.FromResult<string?>(null);
        }

        public Task<string?> GetCustomFormCategoryAsync(AuditSaveMessage message)
        {
            if (message.EntityType == nameof(EmployeeCustomForm))
            {
                var companyCustomFormCategoryId = message.AuditTrails!.Where(_ => _.CompanyCustomFormCategoryId > 0)
                    .Select(_ => _.CompanyCustomFormCategoryId.Value)
                    .FirstOrDefault();
                if (companyCustomFormCategoryId > 0)
                {
                    return this.distributedCache.GetOrCreateAsync(
                        CacheKeys.CustomFormCategoryCode("C", companyCustomFormCategoryId),
                        () => this.context.Set<CompanyCustomFormCategory>()
                            .Where(_ => _.CustomFormCategoryId == companyCustomFormCategoryId)
                            .Select(_ => _.Code + "_C")
                            .FirstOrDefaultAsync());
                }

                var bureauCustomFormCategoryId = message.AuditTrails!.Where(_ => _.BureauCustomFormCategoryId > 0)
                    .Select(_ => _.BureauCustomFormCategoryId.Value)
                    .FirstOrDefault();
                if (bureauCustomFormCategoryId > 0)
                {
                    return this.distributedCache.GetOrCreateAsync(
                        CacheKeys.CustomFormCategoryCode("B", bureauCustomFormCategoryId),
                        () => this.context.Set<BureauCustomFormCategory>()
                            .Where(_ => _.CustomFormCategoryId == companyCustomFormCategoryId)
                            .Select(_ => _.Code + "_B")
                            .FirstOrDefaultAsync());
                }
            }

            return Task.FromResult<string?>(null);
        }

        public Task<long?> GetBankDetailsIdAsync(long employeeBankHeaderId)
        {
            return this.context.Set<EmployeeBankDetail>()
                .Where(_ => _.EmployeeBankHeaderId == employeeBankHeaderId)
                .Select(_ => (long?)_.BankDetailId)
                .FirstOrDefaultAsync();
        }

        private Task<RunDetails?> GetRunDetailsAsync(long runId)
        {
            if (runId == 0)
            {
                return Task.FromResult<RunDetails?>(null);
            }

            return this.distributedCache.GetOrCreateAsync(
                $"Deel:Event:RunDetails:{runId}",
                () => this.context.Set<CompanyRun>()
                    .Where(_ => _.RunId == runId)
                    .Select(_ => new RunDetails
                    {
                        FrequencyName = _.CompanyFrequency.FrequencyName,
                        RunDescription = _.RunDescription
                    })
                    .FirstAsync());
        }

        private async Task<(string ComponentCode, long RunId, long ComponentId)> GetComponentDetailsAsync(AuditSaveMessage message)
        {
            var runId = message.AuditTrails.Where(_ => _.CompanyRunId != null).Select(_ => _.CompanyRunId.Value).FirstOrDefault();

            if (message.EntityType == nameof(BulkCaptureCode))
            {
                return await this.GetBulkCaptureCodeComponentDetails(message, runId);
            }

            if (message.EntityType == nameof(PayslipHeader))
            {
                return await this.GetPayslipHeaderComponentDetailsAsync(message, runId);
            }

            if (message.EntityType == nameof(PayslipLine))
            {
                return await this.GetPayslipLineComponentDetailsAsync(message, runId);
            }

            if (message.EntityType == nameof(ComponentValues))
            {
                return await this.ComponentValueComponentDetailsAsync(message, runId);
            }

            return default;
        }

        private async Task<(string ComponentCode, long RunId, long ComponentId)> GetPayslipLineComponentDetailsAsync(AuditSaveMessage message, long runId)
        {
            var componentEmployeeId = message.AuditTrails.Where(_ => _.EmployeeComponentId.HasValue).Select(_ => _.EmployeeComponentId.Value).FirstOrDefault();
            if (componentEmployeeId > 0)
            {
                var component = await this.context.Set<ComponentEmployee>()
                    .Where(_ => _.EmployeeId == message.EmployeeId)
                    .Where(_ => _.ComponentId == componentEmployeeId)
                    .Select(_ => new
                    {
                        ComponentCode = !string.IsNullOrEmpty(_.ComponentCompany.ComponentCode) ? _.ComponentCompany.ComponentCode : _.ComponentCompany.AliasDescription,
                        _.ComponentId
                    })
                    .FirstOrDefaultAsync();

                return (component?.ComponentCode, runId, component?.ComponentId ?? 0L);
            }

            return default;
        }

        private async Task<(string ComponentCode, long RunId, long ComponentId)> GetPayslipHeaderComponentDetailsAsync(AuditSaveMessage message, long runId)
        {
            var jsonValues = JsonConvert.DeserializeObject<JObject>(message.AuditTrails.First().ValueAsJson);
            var isBasicPayComponent = jsonValues.TryGetValue("IsBasicPayComponent", out var isBasicPayComponentValue) && isBasicPayComponentValue.Value<bool>();
            if (isBasicPayComponent)
            {
                var component = await this.context.Set<ComponentEmployee>()
                    .Where(_ => _.EmployeeId == message.EmployeeId)
                    .Where(_ => _.ComponentCompany.ComponentBureau.SpecialComponentType.SpecialComponentCode == SpecialComponentCodes.BasicSalary)
                    .Select(_ => new
                    {
                        ComponentCode = !string.IsNullOrEmpty(_.ComponentCompany.ComponentCode) ? _.ComponentCompany.ComponentCode : _.ComponentCompany.AliasDescription,
                        _.ComponentId
                    })
                    .FirstOrDefaultAsync();

                return (component?.ComponentCode, runId, component?.ComponentId ?? 0L);
            }

            return default;
        }

        private async Task<(string ComponentCode, long RunId, long ComponentId)> ComponentValueComponentDetailsAsync(AuditSaveMessage message, long runId)
        {
            var componentEmployeeId = message.AuditTrails.Where(_ => _.EmployeeComponentId.HasValue)
                .Select(_ => _.EmployeeComponentId.Value)
                .FirstOrDefault();
            if (componentEmployeeId == 0)
            {
                return default;
            }

            var component = await this.context.Set<ComponentValues>()
                .Where(_ => _.ComponentEmployeeId == componentEmployeeId && _.IsOnceOffValue == true)
                .Select(_ => _.ComponentEmployee)
                .Select(_ => new
                {
                    ComponentCode = !string.IsNullOrEmpty(_.ComponentCompany.ComponentCode) ? _.ComponentCompany.ComponentCode : _.ComponentCompany.AliasDescription,
                    _.ComponentId
                })
                .FirstOrDefaultAsync();

            return (component?.ComponentCode, runId, component?.ComponentId ?? 0L);
        }

        private async Task<(string ComponentCode, long RunId, long ComponentId)> GetBulkCaptureCodeComponentDetails(AuditSaveMessage message, long runId)
        {
            var jsonValues = JsonConvert.DeserializeObject<JObject>(message.AuditTrails.First().ValueAsJson);
            var componentName = jsonValues.TryGetValue("Component", out var componentValue) ? componentValue.Value<string>() : null;

            if (componentName == RecurringComponentConstants.ComponentPercentageCosting)
            {
                return (RecurringComponentConstants.ComponentPercentageCosting, runId, -1L);
            }

            if (message.ActionType == "Edit" && string.IsNullOrEmpty(componentName))
            {
                var bulkCaptureCodeId = message.AuditTrails.Where(_ => _.AlternativeId != null).Select(_ => _.AlternativeId.Value).FirstOrDefault();
                var component = await this.context.Set<BulkCaptureCode>()
                    .Where(_ => _.BulkCaptureId == bulkCaptureCodeId)
                    .Select(_ => new
                    {
                        ComponentCode = !string.IsNullOrEmpty(_.Component.ComponentCompany.ComponentCode) ? _.Component.ComponentCompany.ComponentCode : _.Component.ComponentCompany.AliasDescription,
                        _.ComponentId
                    })
                    .FirstOrDefaultAsync();

                return (component?.ComponentCode, runId, component?.ComponentId ?? 0L);
            }
            else
            {
                var componentId = jsonValues.TryGetValue("ComponentId", out var componentIdValue) ? componentIdValue.Value<long>() : 0;

                var component = await this.context.Set<ComponentEmployee>()
                    .Where(_ => _.ComponentId == componentId)
                    .Select(_ => new
                    {
                        ComponentCode = !string.IsNullOrEmpty(_.ComponentCompany.ComponentCode) ? _.ComponentCompany.ComponentCode : _.ComponentCompany.AliasDescription,
                        _.ComponentId
                    })
                    .FirstOrDefaultAsync();

                return (component?.ComponentCode, runId, component?.ComponentId ?? 0L);
            }
        }

        private sealed class RunDetails
        {
            public string RunDescription { get; set; } = string.Empty;
            public string FrequencyName { get; set; } = string.Empty;
        }
    }
}