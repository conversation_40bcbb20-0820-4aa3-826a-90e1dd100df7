namespace PaySpace.Integrations.Deel.Services
{
    using System;
    using System.Net.Http;
    using System.Net.Http.Json;
    using System.Threading;
    using System.Threading.Tasks;

    using Microsoft.Extensions.Configuration;

    using PaySpace.Integrations.Deel.Models;

    internal interface IDeelEventService
    {
        Task SendEventAsync(DeelEventMessage message, CancellationToken cancellationToken);
    }

    public class DeelEventException(string message) : Exception(message);

    internal sealed class DeelEventService : IDeelEventService
    {
        private readonly HttpClient client;
        private readonly IConfiguration configuration;

        public DeelEventService(HttpClient client, IConfiguration configuration)
        {
            this.client = client;
            this.configuration = configuration;
        }

        public async Task SendEventAsync(DeelEventMessage message, CancellationToken cancellationToken)
        {
            var result = await this.client.PostAsJsonAsync(this.configuration.GetValue<string?>("ClientSettings:DeelEventUrl"), message, cancellationToken);
            result.EnsureSuccessStatusCode();
        }
    }
}