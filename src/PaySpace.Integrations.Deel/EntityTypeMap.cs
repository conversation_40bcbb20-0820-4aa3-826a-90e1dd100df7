namespace PaySpace.Integrations.Deel
{
    using System.Collections.Generic;
    using System.Collections.Immutable;

    using PaySpace.Venuta.Data.Models;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Payslips;
    using PaySpace.Venuta.Data.Models.Workflow;
    using PaySpace.Venuta.Logging.Abstractions;

    internal static class EntityTypeMap
    {
        public static readonly string[] SupportedTypes =
        [
            nameof(Address),
            nameof(EmployeeCustomForm),
            nameof(Employee),
            nameof(EmployeeEmploymentStatus),
            nameof(EmployeePayRate),
            nameof(EmployeePosition),
            nameof(EmployeeBankDetail),
            nameof(EmployeeBankHeader),
            nameof(EmployeeDependant),
            nameof(EmployeeAttachment),
            nameof(EmployeeProject),
            nameof(EmployeeAsset),
            nameof(EmployeeIncident),
            nameof(EmployeeTakeOn),
            nameof(ClaimBatch),
            nameof(ClaimComponent),
            nameof(EmployeeSuspension),
            nameof(EmployeeNote),
            nameof(EmployeeWorkflowSetting),
            nameof(EmployeeTraining),
            nameof(EmployeeQualification),
            nameof(EmployeeSkill),
            nameof(BulkCaptureCode),
            nameof(ComponentValues),
            nameof(PayslipHeader),
            nameof(PayslipLine)
        ];

        private static readonly ImmutableDictionary<string, string> EntityTYpeMap = new Dictionary<string, string>
        {
            [nameof(Address)] = "Employee",
            [nameof(ClaimBatch)] = "EmployeeClaim",
            [nameof(ClaimComponent)] = "EmployeeClaim",
            [nameof(EmployeeBankHeader)] = "EmployeeBankDetail",
            [nameof(EmployeeWorkflowSetting)] = "EmployeeOutOfOffice",
            [nameof(BulkCaptureCode)] = "EditPayslip",
            [nameof(ComponentValues)] = "EditPayslip",
            [nameof(PayslipHeader)] = "EditPayslip",
            [nameof(PayslipLine)] = "EditPayslip"
        }.ToImmutableDictionary();

        public static string? GetEntityType(AuditSaveMessage message)
        {
            if (message.EntityType == nameof(Address) && !message.EmployeeId.HasValue)
            {
                return null;
            }

            return EntityTYpeMap.TryGetValue(message.EntityType, out var entityType) ? entityType : message.EntityType;
        }
    }
}