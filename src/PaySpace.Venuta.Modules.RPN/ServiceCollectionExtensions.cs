namespace PaySpace.Venuta.Modules.RPN
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using FluentValidation;

    using Microsoft.Extensions.DependencyInjection;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddRPNModules(this IServiceCollection services)
        {
            // Register Validators
            foreach (var type in GetValidatorTypes())
            {
                foreach (var interfaceType in type.GetInterfaces())
                {
                    services.AddTransient(interfaceType, type);
                }
            }

            return services;
        }

        private static IEnumerable<Type> GetValidatorTypes()
        {
            return typeof(ServiceCollectionExtensions).Assembly.GetTypes()
            .Where(t => !t.IsAbstract && t.IsPublic && typeof(IValidator).IsAssignableFrom(t) &&
                        !t.ContainsGenericParameters);
        }
    }
}