namespace PaySpace.Venuta.Excel.Components.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;
    using AutoMapper.QueryableExtensions;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Components;
    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Dto.Components;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Excel.Abstractions.Lookup;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Components.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Bureau;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Components;

    internal class TableBuilderComponentService : ComponentService<EmployeeTableBuilderDto>, IFilterableComponent<EmployeeTableBuilderDto>
    {
        private readonly ApplicationContext applicationContext;
        private readonly ICompanyRunService companyRunService;
        private readonly IUserSecurityService securityService;
        private readonly ITableBuilderService tableBuilderService;
        private readonly ITableBuilderCategoryService tableBuilderCategoryService;
        private readonly IMapper mapper;

        public TableBuilderComponentService(
            ApplicationContext applicationContext,
            IEmployeeComponentService employeeComponentService,
            IComponentValueService componentValueService,
            IEmployeeComponentStore employeeComponentStore,
            ICompanyRunService companyRunService,
            ICalcSchedulingService calcSchedulingService,
            ICalculationWebApiClient calculationApiClient,
            IComponentCompanyDetailLookupStrategy componentCompanyDetailLookupStrategy,
            IUserSecurityService securityService,
            ITableBuilderService tableBuilderService,
            ITableBuilderCategoryService tableBuilderCategoryService,
            IMapper mapper)
            : base(applicationContext, employeeComponentService, componentValueService, employeeComponentStore, companyRunService, calcSchedulingService, calculationApiClient, componentCompanyDetailLookupStrategy)
        {
            this.applicationContext = applicationContext;
            this.companyRunService = companyRunService;
            this.securityService = securityService;
            this.tableBuilderService = tableBuilderService;
            this.tableBuilderCategoryService = tableBuilderCategoryService;
            this.mapper = mapper;
        }

        public Dictionary<string, string> InnerQueryFieldMap => new(StringComparer.InvariantCultureIgnoreCase)
            {
                { "EmployeeNumber", "e.EmpNumber" },
                { "ComponentCompanyId", "cc.pkComponentCompanyID" },
                { "EmployeeId", "ce.fkEmpID" }
            };

        public override IQueryable<EmployeeTableBuilderDto> Get(MutatorContext context)
        {
            return this.GetData(context)
                       .Where(_ => _.Active)
                       .ProjectTo<EmployeeTableBuilderDto>(this.mapper.ConfigurationProvider);
        }

        public override IQueryable<EmployeeTableBuilderDto> GetByComponentCompany(MutatorContext context, long componentCompanyId, long employeeId, bool initialize)
        {
            var data = this.GetData(context, componentCompanyId: componentCompanyId, employeeId: employeeId, includeTerminated: true)
                .ProjectTo<EmployeeTableBuilderDto>(this.mapper.ConfigurationProvider).ToList();
            if (data.Count == 0)
            {
                return data.AsQueryable();
            }

            var creationRunId = this.GetEarliestTableBuilderBalanceComponentRunId(employeeId, componentCompanyId);
            if (creationRunId == context.RunId!.Value)
            {
                data.ForEach(_ => _.IsCreationRun = true);
            }

            return data.AsQueryable();
        }

        public override IQueryable<ComponentHistoryDto> GetHistory(MutatorContext context, long employeeId, long companyComponentId)
        {
            return this.applicationContext.Set<EmployeeTableBuilder>()
                .Where(_ => _.ComponentEmployee.ComponentCompanyId == companyComponentId && _.ComponentEmployee.EmployeeId == employeeId)
                .OrderByDescending(_ => _.EffectiveDate)
                .Select(_ => new ComponentHistoryDto
                {
                    Description = _.TableBuilderCode,
                    EffectiveDate = _.EffectiveDate,
                    Id = _.EmployeeTableBuilderId
                });
        }

        public IQueryable<EmployeeTableBuilderDto> GetFilteredComponents(long userId, long companyId, long frequencyId, long runId, FilterCondition filter)
        {
            var allowedOrganizationGroups = this.securityService.GetUserAllowedOrganizationGroupIds(userId, companyId);
            var allowedOrganizationRegions = this.securityService.GetUserAllowedOrganizationRegionIds(userId, companyId);

            var componentQuery = this.GetParametersAndSql(companyId, frequencyId, runId, null, false, null, null, out var parameters, filter);

            return this.applicationContext.Set<EmployeeTableBuilderResult>()
                .FromSqlRaw(componentQuery, parameters.ToArray())
                .Where(_ => _.Active)
                .Where(_ => (!allowedOrganizationRegions.Any() || _.RegionId == null || allowedOrganizationRegions.Contains(_.RegionId.Value))
                            && (!allowedOrganizationGroups.Any() || _.OrganizationGroupId == null || allowedOrganizationGroups.Contains(_.OrganizationGroupId.Value)))
                .ProjectTo<EmployeeTableBuilderDto>(this.mapper.ConfigurationProvider);
        }

        protected override async Task<long> AddComponentAsync(MutatorContext context, EmployeeTableBuilderDto dto)
        {
            var frequencyId = context.FrequencyId!.Value;
            var runId = context.RunId!.Value;

            var employeeTableBuilder = await this.applicationContext.Set<EmployeeTableBuilder>()
                .Include(_ => _.CustomFields)
                .FirstOrDefaultAsync(_ => _.ComponentEmployeeId == dto.ComponentEmployeeId && _.EffectiveDate == dto.EffectiveDate);

            await this.UpdateEmployeeTableBuilderBalanceAsync(dto, runId);

            if (employeeTableBuilder != null)
            {
                this.mapper.Map(dto, employeeTableBuilder, (opt) =>
                {
                    opt.Items.Add(PaySpaceConstants.ComponentCompanyId, dto.ComponentCompanyId);
                    opt.Items.Add(PaySpaceConstants.RunId, runId);
                    opt.Items.Add(PaySpaceConstants.FrequencyId, frequencyId);
                });
            }
            else
            {
                employeeTableBuilder = this.mapper.Map<EmployeeTableBuilder>(dto, (opt) =>
                {
                    opt.Items.Add(PaySpaceConstants.ComponentCompanyId, dto.ComponentCompanyId);
                    opt.Items.Add(PaySpaceConstants.RunId, runId);
                    opt.Items.Add(PaySpaceConstants.FrequencyId, frequencyId);
                });

                await this.applicationContext.Set<EmployeeTableBuilder>().AddAsync(employeeTableBuilder);
            }

            await this.applicationContext.SaveChangesAsync();

            return employeeTableBuilder.EmployeeTableBuilderId;
        }

        protected override async Task UpdateComponentAsync(MutatorContext context, EmployeeTableBuilderDto dto)
        {
            var frequencyId = context.FrequencyId!.Value;
            var runId = context.RunId!.Value;

            var employeeTableBuilder = await this.applicationContext.Set<EmployeeTableBuilder>()
                .Include(_ => _.CustomFields)
                .FirstAsync(_ => _.EmployeeTableBuilderId == dto.EmployeeTableBuilderId);

            await this.UpdateEmployeeTableBuilderBalanceAsync(dto, runId);

            if (employeeTableBuilder.EffectiveDate != dto.EffectiveDate)
            {
                dto.EmployeeTableBuilderId = 0;
                await this.AddComponentAsync(context, dto);
            }
            else
            {
                this.mapper.Map(dto, employeeTableBuilder, (opt) =>
                {
                    opt.Items.Add(PaySpaceConstants.ComponentCompanyId, dto.ComponentCompanyId);
                    opt.Items.Add(PaySpaceConstants.RunId, runId);
                    opt.Items.Add(PaySpaceConstants.FrequencyId, frequencyId);
                });

                await this.applicationContext.SaveChangesAsync();
            }
        }

        public override async Task DeleteHistoryAsync(MutatorContext context, long employeeId, long key)
        {
            var tableBuilderCustomFieldValues = await (
                from tbcfv in this.applicationContext.Set<TableBuilderCustomFieldValue>().TagWithSource()
                join etb in this.applicationContext.Set<EmployeeTableBuilder>() on tbcfv.EmployeeTableBuilderId equals etb.EmployeeTableBuilderId
                where tbcfv.EmployeeTableBuilderId == key
                    && etb != null
                    && etb.ComponentEmployee.EmployeeId == employeeId
                    && etb.ComponentEmployee.ComponentCompany.CompanyFrequency.CompanyId == context.CompanyId
                select tbcfv
            ).ToListAsync();
            this.applicationContext.RemoveRange(tableBuilderCustomFieldValues);

            var employeeTableBuilder = await this.applicationContext.Set<EmployeeTableBuilder>()
                .SingleOrDefaultAsync(_ =>
                    _.EmployeeTableBuilderId == key
                    && _.ComponentEmployee.EmployeeId == employeeId
                    && _.ComponentEmployee.ComponentCompany.CompanyFrequency.CompanyId == context.CompanyId);
            this.applicationContext.Remove(employeeTableBuilder);

            await this.applicationContext.SaveChangesAsync();
        }

        private async Task UpdateEmployeeTableBuilderBalanceAsync(EmployeeTableBuilderDto dto, long runId)
        {
            if (!await this.IsBalanceComponentAsync(dto.ComponentCompanyId))
            {
                return;
            }

            var employeeTableBuilderBalance = await this.applicationContext.Set<EmployeeTableBuilderBalance>()
                                                                           .TagWithSource()
                                                                           .SingleOrDefaultAsync(_ => _.ComponentEmployeeId == dto.ComponentEmployeeId && _.CompanyRunId == runId);

            if (employeeTableBuilderBalance is not null)
            {
                employeeTableBuilderBalance.IncreaseDecrease = dto.IncreaseDecrease;

                var creationRunId = await this.GetEarliestTableBuilderBalanceComponentRunIdAsync(dto.EmployeeId, dto.ComponentCompanyId);

                if (creationRunId == runId)
                {
                    employeeTableBuilderBalance.InitialBalance = dto.InitialBalance;
                }

                return;
            }

            await this.applicationContext.Set<EmployeeTableBuilderBalance>().AddAsync(new()
            {
                InitialBalance = dto.InitialBalance,
                IncreaseDecrease = dto.IncreaseDecrease ?? 0,
                Balance = dto.Balance,
                ComponentEmployeeId = dto.ComponentEmployeeId,
                CompanyRunId = runId
            });
        }

        private async Task<bool> IsBalanceComponentAsync(long componentCompanyId)
        {
            var categoryId = await this.tableBuilderService.GetCategoryIdByComponentCompanyIdAsync(componentCompanyId);
            return await this.tableBuilderCategoryService.IsTableBuilderBalanceComponentAsync(categoryId);
        }

        protected override IQueryable<EmployeeTableBuilderDto> Get(MutatorContext context, long key)
        {
            return this.GetData(context, key: key, includeTerminated: true)
                       .ProjectTo<EmployeeTableBuilderDto>(this.mapper.ConfigurationProvider);
        }

        private IQueryable<EmployeeTableBuilderResult> GetData(
            MutatorContext context,
            long? key = null,
            bool includeTerminated = false,
            long? componentCompanyId = null,
            long? employeeId = null)
        {
            var userId = context.UserId;
            var companyId = context.CompanyId;
            var frequencyId = context.FrequencyId!.Value;
            var runId = context.RunId!.Value;

            var allowedOrganizationGroups = this.securityService.GetUserAllowedOrganizationGroupIds(userId, companyId);
            var allowedOrganizationRegions = this.securityService.GetUserAllowedOrganizationRegionIds(userId, companyId);

            var componentQuery = this.GetParametersAndSql(companyId, frequencyId, runId, key, includeTerminated, componentCompanyId, employeeId, out var parameters, null);

            return this.applicationContext.Set<EmployeeTableBuilderResult>()
                .FromSqlRaw(componentQuery, parameters.ToArray())
                .AsNoTracking()
                .Where(_ => (!allowedOrganizationRegions.Any() || _.RegionId == null || allowedOrganizationRegions.Contains(_.RegionId.Value))
                            && (!allowedOrganizationGroups.Any() || _.OrganizationGroupId == null || allowedOrganizationGroups.Contains(_.OrganizationGroupId.Value)));
        }

        private string GetParametersAndSql(
            long companyId,
            long frequencyId,
            long runId,
            long? key,
            bool includeTerminated,
            long? componentCompanyId,
            long? employeeId,
            out List<object> parameters,
            FilterCondition? filter)
        {
            var run = this.companyRunService.GetRunSummary(runId);

            parameters = [frequencyId, run.PeriodEndDate, key is null ? includeTerminated : key, run.PeriodStartDate, companyId];

            filter = this.AddFilters(componentCompanyId, employeeId, filter);

            var sqlFile = key == null ? "Sql.EmployeeTableBuilder.sql" : "Sql.EmployeeTableBuilderByKey.sql";
            var sql = ResourceHelper.GetEmbeddedContent(sqlFile);

            if (filter != null)
            {
                return filter.Apply(sql, parameters);
            }

            return sql.Replace("{ODataFilter}", string.Empty);
        }

        private FilterCondition? AddFilters(long? componentCompanyId, long? employeeId, FilterCondition? filter)
        {
            if (componentCompanyId.HasValue && employeeId.HasValue)
            {
                // manually create the filter for these values, the query is much better when we apply it to the inner query
                filter ??= new FilterCondition(this.InnerQueryFieldMap);

                filter.Left = new FilterCondition(this.InnerQueryFieldMap)
                {
                    Left = "ComponentCompanyId",
                    OperatorType = "EQUAL",
                    Right = componentCompanyId
                };

                filter.Right = new FilterCondition(this.InnerQueryFieldMap)
                {
                    Left = "EmployeeId",
                    OperatorType = "EQUAL",
                    Right = employeeId
                };

                filter.OperatorType = "AND";
            }

            return filter;
        }

        private long GetEarliestTableBuilderBalanceComponentRunId(long employeeId, long componentCompanyId)
        {
            return (from et in this.applicationContext.Set<EmployeeTableBuilderBalance>().TagWithSource()
                    join ce in this.applicationContext.Set<ComponentEmployee>() on et.ComponentEmployeeId equals ce.ComponentId
                    join cr in this.applicationContext.Set<CompanyRun>() on et.CompanyRunId equals cr.RunId
                    where ce.EmployeeId == employeeId && ce.ComponentCompanyId == componentCompanyId
                    orderby cr.PeriodEndDate
                    select et.CompanyRunId).FirstOrDefault();
        }

        private Task<long> GetEarliestTableBuilderBalanceComponentRunIdAsync(long employeeId, long componentCompanyId)
        {
            return (from et in this.applicationContext.Set<EmployeeTableBuilderBalance>().TagWithSource()
                    join ce in this.applicationContext.Set<ComponentEmployee>() on et.ComponentEmployeeId equals ce.ComponentId
                    join cr in this.applicationContext.Set<CompanyRun>() on et.CompanyRunId equals cr.RunId
                    where ce.EmployeeId == employeeId && ce.ComponentCompanyId == componentCompanyId
                    orderby cr.PeriodEndDate
                    select et.CompanyRunId).FirstOrDefaultAsync();
        }
    }
}