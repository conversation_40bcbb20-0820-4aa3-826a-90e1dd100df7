namespace PaySpace.Venuta.Excel.Components.Validators
{
    using FluentValidation;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Excel.Validation;

    internal sealed class EmployeeGarnisheeEntityValidator : EntityValidator<EmployeeGarnisheeResult, EmployeeGarnisheeResult>
    {
        public EmployeeGarnisheeEntityValidator(
            IValidator<EmployeeGarnisheeResult> validator,
            IDtoValidator<EmployeeGarnisheeResult> dtoValidator,
            IModelMetadataProvider metadataProvider)
            : base(validator, dtoValidator, metadataProvider)
        {
        }

        protected override bool CanUpdateProperty<T>(string propertyName, EmployeeGarnisheeResult originalDto, EmployeeGarnisheeResult dto)
        {
            var canUpdate = base.CanUpdateProperty<T>(propertyName, originalDto, dto);
            if (!canUpdate && propertyName.Equals(nameof(EmployeeGarnisheeResult.CapitalBalance)) && dto.IsCreationRun)
            {
                return true;
            }

            return canUpdate;
        }
    }
}