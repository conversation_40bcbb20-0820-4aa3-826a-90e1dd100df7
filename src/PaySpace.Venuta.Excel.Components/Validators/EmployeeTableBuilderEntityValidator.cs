namespace PaySpace.Venuta.Excel.Components.Validators
{
    using FluentValidation;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Excel.Validation;

    internal sealed class EmployeeTableBuilderEntityValidator : EntityValidator<EmployeeTableBuilderResult, EmployeeTableBuilderResult>
    {
        public EmployeeTableBuilderEntityValidator(
            IValidator<EmployeeTableBuilderResult> validator,
            IDtoValidator<EmployeeTableBuilderResult> dtoValidator,
            IModelMetadataProvider metadataProvider)
            : base(validator, dtoValidator, metadataProvider)
        {
        }

        protected override bool CanUpdateProperty<T>(string propertyName, EmployeeTableBuilderResult originalDto, EmployeeTableBuilderResult dto)
        {
            var canUpdate = base.CanUpdateProperty<T>(propertyName, originalDto, dto);
            if (!canUpdate && propertyName.Equals(nameof(EmployeeTableBuilderResult.InitialBalance)) && dto.IsCreationRun)
            {
                return true;
            }

            return canUpdate;
        }
    }
}
