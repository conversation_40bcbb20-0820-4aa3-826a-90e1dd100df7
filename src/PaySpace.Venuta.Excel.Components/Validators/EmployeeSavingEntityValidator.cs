namespace PaySpace.Venuta.Excel.Components.Validators
{
    using FluentValidation;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Excel.Validation;

    internal sealed class EmployeeSavingEntityValidator : EntityValidator<EmployeeSavingResult, EmployeeSavingResult>
    {
        public EmployeeSavingEntityValidator(
            IValidator<EmployeeSavingResult> validator,
            IDtoValidator<EmployeeSavingResult> dtoValidator,
            IModelMetadataProvider metadataProvider)
            : base(validator, dtoValidator, metadataProvider)
        {
        }

        protected override bool CanUpdateProperty<T>(string propertyName, EmployeeSavingResult originalDto, EmployeeSavingResult dto)
        {
            var canUpdate = base.CanUpdateProperty<T>(propertyName, originalDto, dto);
            if (!canUpdate && propertyName.Equals(nameof(EmployeeSavingResult.SavingAmount)) && dto.IsCreationRun)
            {
                return true;
            }

            return canUpdate;
        }
    }
}