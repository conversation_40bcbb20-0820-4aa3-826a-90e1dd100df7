namespace PaySpace.Venuta.Excel.Components.Validators
{
    using FluentValidation;

    using Microsoft.AspNetCore.Mvc.ModelBinding;

    using PaySpace.Venuta.Data.Models.Components.Results;
    using PaySpace.Venuta.Excel.Validation;

    internal sealed class EmployeeLoanEntityValidator : EntityValidator<EmployeeLoanResult, EmployeeLoanResult>
    {
        public EmployeeLoanEntityValidator(
            IValidator<EmployeeLoanResult> validator,
            IDtoValidator<EmployeeLoanResult> dtoValidator,
            IModelMetadataProvider metadataProvider)
            : base(validator, dtoValidator, metadataProvider)
        {
        }

        protected override bool CanUpdateProperty<T>(string propertyName, EmployeeLoanResult originalDto, EmployeeLoanResult dto)
        {
            var canUpdate = base.CanUpdateProperty<T>(propertyName, originalDto, dto);
            if (!canUpdate && propertyName.Equals(nameof(EmployeeLoanResult.LoanAmount)) && dto.IsCreationRun)
            {
                return true;
            }

            return canUpdate;
        }
    }
}