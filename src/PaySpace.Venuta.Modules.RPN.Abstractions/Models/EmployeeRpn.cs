namespace PaySpace.Venuta.Modules.RPN.Abstractions.Models
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Country;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;

    public class EmployeeRpn : IAuditEntity
    {
        [Key]
        [Column("pkEmployeeRpn")]
        public long EmployeeRpnId { get; set; }

        public Guid RequestId { get; set; }

        public string RpnNumber { get; set; }

        public DateTime RpnIssueDate { get; set; }

        public string RpnFirstName { get; set; }

        public string RpnFamilyName { get; set; }

        public string EmployeePpsn { get; set; }

        public string? PreviousEmployeePpsn { get; set; }

        public string? RpnEmploymentId { get; set; }

        public string? EmployerReference { get; set; }

        [Column("fkIncomeTaxCalculationBasis")]
        public int IncomeTaxCalculationBasisId { get; set; }

        public EnumIncomeTaxCalculationBasisRpn IncomeTaxCalculationBasis { get; set; }

        public bool? ExclusionOrder { get; set; }

        public DateTime RpnEffectiveDate { get; set; }

        public DateTime EndDate { get; set; }

        public decimal PayForIncomeTaxToDate { get; set; }

        public decimal IncomeTaxDeductedToDate { get; set; }

        public decimal YearlyTaxCredit { get; set; }

        public decimal TaxRate1Percent { get; set; }

        public decimal? YearlyRate1CutOff { get; set; }

        public decimal TaxRate2Percent { get; set; }

        public bool? EmployeeIsExemptFromPrsiInIreland { get; set; }

        public string? PrsiClassAndSubclass { get; set; }

        public bool UscStatus { get; set; }

        public decimal UscRate1Percent { get; set; }

        public decimal? YearlyUscRate1CutOff { get; set; }

        public decimal UscRate2Percent { get; set; }

        public decimal? YearlyUscRate2CutOff { get; set; }

        public decimal UscRate3Percent { get; set; }

        public decimal? YearlyUscRate3CutOff { get; set; }

        public decimal UscRate4Percent { get; set; }

        public decimal? YearlyUscRate4CutOff { get; set; }

        public decimal PayForUscToDate { get; set; }

        public decimal UscDeductedToDate { get; set; }

        public decimal? LptToBeDeducted { get; set; }

        public bool? StatePensionContribution { get; set; }

        [Column("fkEmpID")]
        public long EmployeeId { get; set; }

        public Employee Employee { get; set; }

        public string? SourceType { get; set; }

        public DateTime? Timestamp { get; set; }

        public DateTime EffectiveDate { get; set; }

        [Column("fkUserId")]
        public long UserId { get; set; }

        public string? BlobStorageId { get; set; }

        [Column("fkTaxYearId")]
        public int TaxYearId { get; set; }

        public CountryTaxYear TaxYear { get; set; }

        [NotMapped]
        public bool ShouldAuditEntity { get; set; } = true;
    }
}
