import { HubConnection, HubConnectionBuilder, HubConnectionState } from "@microsoft/signalr";
import Guid from 'devextreme/core/guid';
import { isJwtExpired } from 'jwt-check-expiration';
import * as auth from "./auth";
import { Events, MessageStatus, ProgressMessage, ProgressStatus, StatusMessage } from "./notifications";

const connectionStateMessageId = new Guid().toString();
const showConnectingDelay = 15000;
const retryDelay = 3000;

export default class ConnectionManager {
    private readonly connection: HubConnection;
    private readonly accessTokenExpired: () => boolean;

    constructor(readonly url: string, accessTokenFactory: () => string) {
        this.accessTokenExpired = () => isJwtExpired(accessTokenFactory());

        let connectingTimeout: NodeJS.Timeout;

        const connection = new HubConnectionBuilder()
            .withUrl(url, {
                accessTokenFactory: accessTokenFactory
            })
            .withAutomaticReconnect({
                nextRetryDelayInMilliseconds() {
                    return retryDelay;
                }
            })
            .build();

        window.addEventListener(auth.Events.AccessTokenExpired, () => {
            if (connection.state === HubConnectionState.Connected) {
                connection.stop();
            }
        });

        connection.on(Events.FileScanStatus, (fileName: string, result: boolean) => {
            window.dispatchEvent(new CustomEvent(Events.FileScanCompleted, {
                detail: {
                    fileName: fileName,
                    result: result
                },
                bubbles: true,
                composed: true
            }));
        });

        connection.on(Events.Redirect, (redirectUrl: string) => {
            window.location.href = redirectUrl;
        });

        connection.onclose(async () => {
            clearTimeout(connectingTimeout);

            if (!this.accessTokenExpired()) {
                await this.start();
            } else {
                this.notifyConnectionLost();
            }
        });

        connection.onreconnecting(async () => {
            if (this.accessTokenExpired()) {
                await connection.stop();
            } else {
                connectingTimeout = setTimeout(() => {
                    this.notifyConnecting();
                }, showConnectingDelay);
            }
        });

        connection.onreconnected(() => {
            clearTimeout(connectingTimeout);
            this.notifyConnectionRestored();
        });

        this.connection = connection;
    }

    public start = async () => {
        if (this.accessTokenExpired()) {
            return;
        }

        try {
            await this.connection.start();
        } catch (e) {
            setTimeout(() => this.start(), retryDelay);
        }
    };

    public send(methodName: string, ...args: any[]) {
        return this.connection.send(methodName, ...args);
    }

    public on(methodName: string, newMethod: (...args: any[]) => void) {
        return this.connection.on(methodName, newMethod);
    }

    private notifyConnectionLost() {
        window.dispatchEvent(new CustomEvent<StatusMessage>(Events.module.show, {
            detail: {
                id: connectionStateMessageId,
                status: MessageStatus.failed,
                title: "Disconnected",
                description: "Connection lost. Please refresh the page to restore the connection"
            } as StatusMessage,
            bubbles: true,
            composed: true
        }));
    }

    private notifyConnecting() {
        window.dispatchEvent(new CustomEvent<ProgressMessage>(Events.module.show, {
            detail: {
                id: connectionStateMessageId,
                status: ProgressStatus.progress,
                title: "Connection lost",
                description: "Please wait while we try re-connect to the server"
            } as ProgressMessage,
            bubbles: true,
            composed: true
        }));
    }

    private notifyConnectionRestored() {
        window.dispatchEvent(new CustomEvent<StatusMessage>(Events.module.show, {
            detail: {
                id: connectionStateMessageId,
                status: MessageStatus.completed,
                title: "Connected",
                description: "Connection restored",
                autohide: true
            } as StatusMessage,
            bubbles: true,
            composed: true
        }));
    }
}