import { licenseKey } from "./../../../../devextreme-license";

import config from "devextreme/core/config";
import { createApp, defineCustomElement, h } from "vue";
import wrapper from "vue3-webcomponent-wrapper";
import FileManager from "./Components/azure-filemanager.ce.vue";
import FileUploader from "./Components/azure-fileuploader.ce.vue";
import Counter from "./Components/counter.ce.vue";
import NotificationsPresenter from "./Components/notification-presenter.vue";
import SessionMonitor from "./session-monitor";

config({ licenseKey });

window.customElements.define('notifications-presenter', wrapper(NotificationsPresenter, createApp, h));
window.customElements.define('countdown-counter', defineCustomElement(Counter));
window.customElements.define('file-manager', defineCustomElement(FileManager));
window.customElements.define('file-uploader', defineCustomElement(FileUploader));

window.SessionMonitor = new SessionMonitor({
    accessTokenFactory: () => window.Auth.AccessToken,
    sessionWarningCountdown: 120
});