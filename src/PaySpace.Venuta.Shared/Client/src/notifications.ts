export const delay = 5000;

export const Events = {
    FileScanStatus: "FileScanStatus",
    FileScanCompleted: "FileScanCompleted",

    Redirect: "Redirect",

    Notification: "Notification",
    Progress: "Progress",
    Status: "Status",

    module: {
        showErrors: "notifications.showErrors",
        showSuccess: "notifications.showSuccess",
        showProgress: "notifications.showProgress",
        showWarning: "notifications.showWarning",
        show: "notifications.show",
        close: "notifications.close"
    }
};

export enum MessageType {
    information = 1,
    success = 2,
    warning = 3,
    error = 4
}

export enum ProgressStatus {
    queued = 1,
    progress = 2
}

export enum MessageStatus {
    completed = 3,
    failed = 4,
    warning = 5
}

export interface Message {
    id: string;
    title?: string;
    description?: string;
    messageCreated?: string | Date;

    collapsed?: boolean;
}

export interface UserMessage extends Message {
    messageType: MessageType;
    autohide: boolean;
}

export interface ProgressMessage extends Message {
    status: ProgressStatus;
    indeterminate: boolean;
    progressPercentage?: number;
}

export interface StatusMessage extends Message {
    status: MessageStatus | ProgressStatus;
    errors?: string[];
    autohide: boolean;
    uri: string;
    uriDisplayText: string;
}

export function isUserMessage(message: UserMessage | ProgressMessage | StatusMessage): message is UserMessage {
    return "messageType" in message;
}

export function isProgressMessage(message: any): message is ProgressMessage {
    return "status" in message && (message.status == ProgressStatus.queued || message.status == ProgressStatus.progress);
}

export function isStatusMessage(message: any): message is StatusMessage {
    return "status" in message && (message.status == MessageStatus.completed || message.status == MessageStatus.warning || message.status == MessageStatus.failed);
}