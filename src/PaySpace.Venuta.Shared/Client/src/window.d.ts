import { UserType } from "./auth";
import SessionMonitor from "./session-monitor";

declare global {
    const $: any;
    const Notifications: any;

    const Localization: {
        [key: string]: {
            [key: string]: string;
        };
    };

    interface Window {
        Notifications: any;
        SessionMonitor: SessionMonitor;
        Auth: {
            AccessToken: string;
        };
        User: {
            UserId: number;
            UserType: UserType;
            Country: string;
            Timezone: string;
            Locale: string;
            SessionId: string;
            DecimalFormat: string;
            DecimalPlaces: number;
            PercentageFormat: string;
        };
        urls: {
            attachments: {
                validate: string;
            },
            logout: string;
            timeout: string;
        };
        renewToken?: () => void;
    }
}