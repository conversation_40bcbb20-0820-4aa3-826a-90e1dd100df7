<template>
    <Suspense>
        <template v-if="visible">
            <div class="dx-filemanager">
                <!--<div style="text-align:right">
        <DxButton icon="refresh" @click="files.reload()" />
    </div>-->
                <DxList :dataSource="files" :show-selection-controls="false" selection-mode="none">
                    <template #item="{ data }">
                        <div class="attachment-grid">
                            <div :title="formatDate(new Date(data.dateModified), 'shortDateShortTime')"><i class="icon dx-icon-file" /></div>
                            <div class="text-truncate" :title="data.name">
                                {{data.name}}
                            </div>
                            <div :title="data.container">
                                <i :class="[data.entity == 'tempattachments' ? 'dx-icon-upload' : 'dx-icon-check']"></i>
                            </div>
                            <div class="justify-content-end">
                                <DxButton icon="more" @click="showMenu($event, data)" />
                            </div>
                        </div>
                        <strong v-if="strToBool(displayUploadedBy)">{{data.userName}}</strong>
                    </template>
                </DxList>
                <DxContextMenu ref="menuRef" show-event="none" :items="[{ text: 'Download', action: downloadBlob }, { text: 'Delete', visible: canDeleteFocusedItem, action: deleteBlob }]"
                               @item-click="onMenuItemClick" />

                <div class="d-flex justify-content-end flex-wrap">
                    <DxButton icon="download" :text="getLocalization('lblDownloadAllFiles')" @click="downloadAll" type="normal" />
                </div>
            </div>
        </template>
    </Suspense>
</template>

<script setup lang="ts">
    import Guid from "devextreme/core/guid";
    import { formatDate, loadMessages, locale } from "devextreme/localization";
    import { computed, defineAsyncComponent, onMounted, ref } from "vue";

    import azureBlobFileManager from "../BlobStorage/azure-blob-file-manager";
    import { Events, MessageType, UserMessage } from "../notifications";

    import CustomStore from "devextreme/data/custom_store";
    import DataSource from "devextreme/data/data_source";

    locale(window.DevExpress.localization.locale());
    loadMessages(window.DevExpress.localization.message._dictionary);

    const DxButton = defineAsyncComponent(() => import('devextreme-vue/button'));
    const DxContextMenu = defineAsyncComponent(() => import('devextreme-vue/context-menu'));
    const DxList = defineAsyncComponent(() => import('devextreme-vue/list'));

    interface ItemData {
        key: string;
        entity: string;
        name: string;
        container: string;
        dateModified: Date;
        userName: string;
    }

    const props = defineProps({
        companyId: [Number, String],
        employeeId: [Number, String],
        rowItemKey: String,
        fileManagerUrl: String,
        entityName: String,
        propertyName: String,
        storageContainer: String,
        customFieldType: String,
        customFieldId: Number,
        readOnly: [Boolean, String],
        displayUploadedBy: [Boolean, String],
        allowOnlyTempDelete: [Boolean, String]
    });

    const emit = defineEmits<{
        cleared: [propertyName: string],
        loaded: [propertyName: string, items : Array<string>]
    }>();

    const files = new DataSource({
        store: new CustomStore({
            key: "key",
            async load() {
                return azureBlobFileManager.getItems(props).then(items => {
                    items.forEach((_: { menuVisible: boolean }) => _.menuVisible = false);
                    return items;
                });
            }
        })
    });

    const menuRef = ref();
    const visible = ref(false);
    const focusedItem = ref();
    const canDeleteFocusedItem = computed(() => {
        if (strToBool(props.readOnly)) {
            return false;
        }

        if (strToBool(props.allowOnlyTempDelete)) {
            return focusedItem.value?.entity === "tempattachments";
        }

        return true;
    });

    onMounted(async () => {
        await files.load();
        checkAndEmitFileManagerStatus();
    });

    window.addEventListener(Events.FileScanCompleted, async () => {
        await files.reload();
        checkAndEmitFileManagerStatus();

        // Preventing impatient users from saving the uploads before the
        // virus scanner is complete.
        toggleButtonDisplay();
    });

    function toggleButtonDisplay() {
        const buttonSelectors = [
            '.dx-button[aria-label="Save"]',
            '.dx-button[aria-label="Update"]',
            '.dx-button[aria-label="Submit"]',
            '[data_tc="btn-save"]',
            '[data_tc="btn-submit"]',
            '[data_tc="add-claim"]'
        ];
        for (const selector of buttonSelectors) {
            const element = document.querySelector<HTMLElement>(selector);
            if (element != null) {
                element.removeAttribute("style");
            }
        }
    }

    function strToBool(str: boolean | string | undefined) {
        if (str == null) {
            return false;
        }

        if (typeof str === "string") {
            return str?.toLowerCase() === "true";
        }

        return str;
    }

    function getLocalization(key: string) {
        return Localization["General.Attachments"][key] ?? key;
    }

    function showMenu(e: {element: HTMLElement}, data) {
        focusedItem.value = data;

        menuRef.value.instance.option("target", e.element);
        menuRef.value.instance.show();
    }

    function onMenuItemClick(e: { itemData: { action: Function } }) {
        e.itemData.action({ itemData: focusedItem.value });
    }

    function downloadBlob(e: { itemData: ItemData }) {
        azureBlobFileManager.getDownloadBlobUri(e.itemData.key, e.itemData.entity, props).then(response => {
            azureBlobFileManager.downloadItem(response.url, e.itemData.name)
        });

        showNotification();
    }

    async function downloadAll() {
        const blobUrls: { fileName: string, url: string }[] = [];

        for (const file of files.items()) {
            const res = await azureBlobFileManager.getDownloadBlobUri(file.key, file.entity, props);
            blobUrls.push({ fileName: file.name, url: res.url });
        };

        azureBlobFileManager.downloadAll(blobUrls).then(() => {
            showNotification();
        });
    }

    async function deleteBlob(e: { itemData: ItemData }) {
        await azureBlobFileManager.deleteItem(e.itemData.key, e.itemData.entity, props);
        await files.reload();

        checkAndEmitFileManagerStatus();
    }

    function showNotification() {
        window.dispatchEvent(new CustomEvent<UserMessage>("notifications.show", {
            detail: {
                id: new Guid().toString(),
                messageType: MessageType.information,
                title: "Downloading Attachment",
                description: "Downloading Attachment",
                autohide: true,
                messageCreated: new Date(),
            },
            bubbles: true,
            composed: true
        }));
    }

    function checkAndEmitFileManagerStatus() {
        const fileItems = files.items();
        const hasFiles = fileItems.length > 0;

        visible.value = hasFiles;

        const tempItems = fileItems.filter(_ => _.entity === "tempattachments");

        if(!hasFiles) {
            emit("cleared", props.propertyName ?? "");
        } else if(tempItems.length > 0) {
            emit("loaded", props.propertyName ?? "", tempItems.map(_ => _.prefix));
        }
    }
</script>

<style lang="scss">
    @import "../../../node_modules/bootstrap/scss/functions";
    @import "../../../node_modules/bootstrap/scss/variables";
    @import "../../../node_modules/bootstrap/scss/maps";
    @import "../../../node_modules/bootstrap/scss/mixins";
    @import "../../../node_modules/bootstrap/scss/utilities";
    @import "../../../node_modules/bootstrap/scss/utilities/api";
    @import "../../../node_modules/bootstrap/scss/helpers";

    .dx-filemanager {
        height: auto;
    }

    .dx-list-item-content {
        padding-top: 0;
        padding-bottom: 0;
    }

    .dx-button {
        padding: unset !important;
    }

    .attachment-grid {
        display: grid;
        align-items: center;
        grid-template-columns: min-content auto min-content min-content;
        gap: 5px;
        & > div {
            display: flex;
            padding: .1rem .2rem;
        }
    }

</style>