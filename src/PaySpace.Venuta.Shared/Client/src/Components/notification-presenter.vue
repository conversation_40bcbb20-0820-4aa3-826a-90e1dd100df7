<template>
    <div class="notifications-presenter" style="z-index: 1999;">
        <Notifications v-for="message in messages" :key="message.id" :message="message"
                       @closeMessage="removeMessage"
                       @showErrors="showErrors" />
    </div>

    <DxPopup title="Errors" v-model:visible="showErrorsPopup" :show-close-button="true" :width="500" height="auto" max-height="90%">
        <DxList :data-source="errors" :search-enabled="true" search-mode="contains" search-expr="html" />
    </DxPopup>
</template>

<script lang="ts">
    import { defineComponent } from "vue";
    import DxPopup from "devextreme-vue/popup";
    import DxList from "devextreme-vue/list";
    import Notifications from "./notifications.vue";
    import { delay, Events, MessageType, ProgressStatus, MessageStatus, Message, UserMessage, ProgressMessage, StatusMessage, isUserMessage, isStatusMessage, isProgressMessage } from "../notifications";
    import ConnectionManager from "../notifications.connection";
    import Guid from 'devextreme/core/guid';

    function acknowledgeMessage(connectionManager: ConnectionManager, message: UserMessage | ProgressMessage | StatusMessage): Promise<void> {
        if (isUserMessage(message)) {
            return connectionManager.send("AcknowledgeUserMessage", message.id);
        }

        if (isProgressMessage(message)) {
            return connectionManager.send("AcknowledgeProgressMessage", message.id);
        }

        if (isStatusMessage(message)) {
            return connectionManager.send("AcknowledgeStatusMessage", message.id);
        }

        throw new Error("Unsupported message type");
    }

    function collapseMessage(connectionManager: ConnectionManager, message: Message): Promise<void> {
        return connectionManager.send("CollapseMessage", window.User.UserId, message.id);
    }

    function expandMessage(connectionManager: ConnectionManager, message: Message): Promise<void> {
        return connectionManager.send("ExpandMessage", window.User.UserId, message.id);
    }

    function closeMessage(id: string): Promise<void> {
        return new Promise(resolve => {
            const element = $("#message-" + id);
            if (element.length > 0) {
                element.on('hidden.bs.toast', () => {
                    resolve();
                });

                element.toast("hide");
            } else {
                resolve();
            }
        });
    }

    window.addEventListener("notifications.close", async (e: CustomEvent<NotificationCloseEvent>) => {
        await closeMessage(e.detail.id);
        if (e.detail.onClosed) {
            e.detail.onClosed();
        }
    });

    export default defineComponent({
        name: "notifications-presenter",
        props: {
            hub: {
                type: String,
                required: true
            }
        },
        data() {
            return {
                messages: new Array<Message>(),
                errors: new Array<{ html: string }>(),
                showErrorsPopup: false
            };
        },
        created() {
            let connectionManager = new ConnectionManager(this.hub, () => window.Auth.AccessToken);

            // TODO: Remove when migration is complete.
            if (window.Notifications) {
                window.Notifications.getMessage = this.getMessage;
            }

            // TODO: Rather make a new instance.
            function appendMethods(message: any) {
                message.acknowledge = () => acknowledgeMessage(connectionManager, message);
                message.collapse = () => collapseMessage(connectionManager, message);
                message.expand = () => expandMessage(connectionManager, message);
            }

            window.addEventListener("notifications.showErrors", (e: CustomEvent<NotificationErrorsEvent>) => {
                let errors = e.detail.errors;

                if (!Array.isArray(errors)) {
                    errors = [errors];
                }

                let message = {
                    id: e.detail.id || new Guid().toString(),
                    status: MessageStatus.failed,
                    title: e.detail.title || Localization && Localization["System.Notification"]["Generic.Failure"],
                    messageCreated: new Date(),
                    errors: errors
                } as StatusMessage;
                appendMethods(message);

                this.addMessage(message);
            });

            window.addEventListener("notifications.showSuccess", (e: CustomEvent<NotificationSuccessEvent>) => {
                let message = {
                    id: e.detail.id || new Guid().toString(),
                    messageType: MessageType.success,
                    status: MessageStatus.completed,
                    title: Localization && Localization["System.Notification"]["Generic.Success"],
                    description: e.detail.description,
                    autohide: true,
                    uri: e.detail.uri,
                    uriDisplayText: e.detail.uriDisplayText,
                    messageCreated: new Date(),
                } as StatusMessage;
                appendMethods(message);

                this.addMessage(message);
            });

            window.addEventListener("notifications.showProgress", (e: CustomEvent<NotificationProgressEvent>) => {
                let message = {
                    id: e.detail.id || new Guid().toString(),
                    status: ProgressStatus.progress,
                    title: Localization && Localization["System.Notification"]["Generic.Processing"],
                    description: e.detail.description,
                    autohide: false,
                    uri: e.detail.uri,
                    uriDisplayText: e.detail.uriDisplayText,
                    messageCreated: new Date(),
                } as StatusMessage;
                appendMethods(message);

                this.addMessage(message);
            });

            window.addEventListener("notifications.showWarning", (e: CustomEvent<NotificationWarningEvent>) => {
                let message = {
                    id: e.detail.id || new Guid().toString(),
                    messageType: MessageType.warning,
                    status: MessageStatus.warning,
                    title: Localization && Localization["System.Notification"]["Generic.Warning"],
                    description: e.detail.description,
                    uri: e.detail.uri,
                    uriDisplayText: e.detail.uriDisplayText,
                    autohide: true,
                    messageCreated: new Date(),
                } as StatusMessage;
                appendMethods(message);

                this.addMessage(message);
            });

            window.addEventListener("notifications.show", (e: CustomEvent<Message>) => {
                let message = e.detail;
                appendMethods(message);

                this.addMessage(message);
            });

            connectionManager.on(Events.Notification, (message: UserMessage, collapsed: boolean) => {
                appendMethods(message);
                message.collapsed = collapsed;

                this.addMessage(message);
            });

            connectionManager.on(Events.Progress, (message: ProgressMessage, collapsed: boolean) => {
                appendMethods(message);
                message.collapsed = collapsed;

                this.addMessage(message);
            });

            connectionManager.on(Events.Status, (message: StatusMessage, collapsed: boolean) => {
                appendMethods(message);
                message.collapsed = collapsed;

                this.addMessage(message);
            });

            connectionManager.start();
        },
        methods: {
            showErrors(errors: string[]) {
                this.errors = errors.map(error => ({ html: error }));
                this.showErrorsPopup = true;
            },
            addMessage(message: any) {
                if (!message.id) {
                    throw Error("A message id is required.");
                }

                let currentMessage = this.getMessage(message.id);
                if (currentMessage) {
                    this.updateMessage(currentMessage, message);
                }
                else {
                    this.messages.push(message);
                }

                if (message.event) {
                    window.dispatchEvent(new Event(message.event));
                }
            },
            updateMessage(currentMessage: any, message: any) {
                currentMessage.status = message.status;
                currentMessage.indeterminate = message.indeterminate;

                if (message.title) {
                    currentMessage.title = message.title;
                }

                if (message.description) {
                    currentMessage.description = message.description;
                }

                if(message.uri) {
                    currentMessage.uri = message.uri;
                }

                if(message.uriDisplayText) {
                    currentMessage.uriDisplayText = message.uriDisplayText;
                }

                if (!currentMessage.autohide && message.autohide) {
                    currentMessage.autohide = true;
                    currentMessage.collapsed = false;

                    currentMessage.timeout = setTimeout(() => {
                        $("#message-" + message.id).toast("hide");
                    }, delay);
                }

                if (currentMessage.autohide && !message.autohide) {
                    currentMessage.autohide = false;

                    if (currentMessage.timeout) {
                        clearTimeout(currentMessage.timeout);
                        currentMessage.timeout = null;
                    }

                    let toast = $("#message-" + message.id).addClass("show").data("bs.toast");
                    if (toast._timeout) {
                        clearTimeout(toast._timeout);
                        toast._timeout = null;
                    }
                }

                switch (message.status) {
                    case ProgressStatus.queued:
                        currentMessage.progressPercentage = 0;
                        currentMessage.errors = [];
                        break;
                    case ProgressStatus.progress:
                        currentMessage.progressPercentage = message.progressPercentage;
                        currentMessage.errors = [];
                        break;
                    case MessageStatus.warning:
                    case MessageStatus.completed:
                        currentMessage.progressPercentage = 100;
                        currentMessage.errors = [];
                        break;
                    case MessageStatus.failed:
                        currentMessage.progressPercentage = 100;
                        currentMessage.errors = message.errors;
                        break;
                }
            },
            removeMessage(messageId: string) {
                let messageIndex = this.messages.findIndex(_ => _.id == messageId);
                if (messageIndex > -1) {
                    this.messages.splice(messageIndex, 1);
                }
            },
            getMessage(messageId: string): any {
                return this.messages.find(_ => _.id == messageId);
            }
        },
        components: {
            DxPopup,
            DxList,
            Notifications: Notifications
        }
    });
</script>

<style scoped>
    .notifications-presenter {
        position: fixed;
        top: 40px;
        right: -300px;
        width: 300px;
    }

    .notifications-presenter .toast {
        position: relative;
        left: -390px;
    }

            .notifications-presenter .toast.collapsed {
                left: -27px !important;
            }

                .notifications-presenter .toast.collapsed .toast-header {
                    border-bottom: none;
                }

                .notifications-presenter .toast.collapsed .toast-body {
                    display: none;
                }

        .notifications-presenter .progress-bar {
            padding: 0 2px;
        }
</style>