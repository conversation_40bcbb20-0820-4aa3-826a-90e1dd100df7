import { Properties as dxButtonOptions } from "devextreme/ui/button";
import { alert, custom } from "devextreme/ui/dialog";
import * as jose from 'jose';
import { isJwtExpired } from 'jwt-check-expiration';
import { Events } from "./auth";

export default class SessionMonitor extends EventTarget {
    private expireTimeout?: NodeJS.Timeout;

    constructor(private readonly options: {
        accessTokenFactory: () => string;
        sessionWarningCountdown: number;
    }) {
        super();

        document.addEventListener("visibilitychange", () => {
            if (document.visibilityState === 'visible') {
                if (isJwtExpired(this.options.accessTokenFactory())) {
                    window.location.href = window.urls.timeout;
                }
            }
        });

        window.addEventListener("storage", event => {
            if (event.key === "access_token") {
                this.renewSession();
            }
        });

        if (options.accessTokenFactory()) {
            this.subscribeTimeout();
        }
    }

    public renewSession() {
        console.log("Access token renewed. Subscribing to new session.");

        clearTimeout(this.expireTimeout);
        this.subscribeTimeout();
    }

    private subscribeTimeout() {
        let expireWarningDelay = (this.getExpireTimeSpan(this.options.accessTokenFactory()) - this.options.sessionWarningCountdown) * 1000; // Convert to milliseconds.
        if (expireWarningDelay < 0) {
            // Temp work around for when decoding a token fails.
            expireWarningDelay = 1080000; // 18 minutes
        }

        const minutes = expireWarningDelay / 60000; // Convert to minutes.
        console.log("Subscribed to session. Warning in " + expireWarningDelay + " milliseconds (" + minutes + " minutes).");

        this.expireTimeout = setTimeout(() => {
            this.showSessionWarning();
        }, expireWarningDelay);
    }

    private showSessionWarning() {
        let lblExpireTitle = "Session expire warning";
        let lblSessionExpireIn = "Your session will expire in";
        let lblSeconds = "seconds";
        let lblBtnLogOff = "Log off";
        let lblBtnStayLoggedIn = "Stay Logged on";

        if (typeof Localization !== "undefined" && Localization.Dashboard) {
            lblExpireTitle = Localization.Dashboard.lblExpireTitle || lblExpireTitle;
            lblSessionExpireIn = Localization.Dashboard.lblSessionExpireIn || lblSessionExpireIn;
            lblSeconds = Localization.Dashboard.lblSeconds || lblSeconds;
            lblBtnStayLoggedIn = Localization.Dashboard.lblBtnStayLoggedIn || lblBtnStayLoggedIn;
        }

        let buttons: dxButtonOptions[] = [{
            text: lblBtnLogOff,
            onClick() {
                window.location.href = window.urls.logout;
            }
        }, {
            type: "default",
            text: lblBtnStayLoggedIn,
            onClick() {
                const counter = document.getElementById("session-countdown") as any;
                counter._instance.proxy.cancel();

                if (window.renewToken) {
                    window.renewToken();
                } else {
                    alert("Renewing of session is not supported.", lblExpireTitle);
                }
            }
        }];

        if (!window.urls?.logout) {
            buttons = [{
                text: "OK",
                onClick() {
                    const counter = document.getElementById("session-countdown") as any;
                    counter._instance.proxy.cancel();

                    expiredWarningDialog.hide();
                }
            }];
        }

        const expiredWarningDialog = custom({
            title: lblExpireTitle,
            messageHtml: `${lblSessionExpireIn} <countdown-counter id='session-countdown' value='${this.options.sessionWarningCountdown}' />`,
            buttons: buttons
        });

        expiredWarningDialog.show();

        const counter = document.getElementById("session-countdown") as any;
        counter.addEventListener("complete", () => {
            let evt = new Event(Events.AccessTokenExpired);
            window.dispatchEvent(evt);
            this.dispatchEvent(evt)

            expiredWarningDialog.hide();
            this.showExpiredSession();
        }, { once: true });
    }

    private getExpireTimeSpan(accessToken: string) {
        if (!accessToken) {
            return 0;
        }

        const claims = jose.decodeJwt(accessToken);
        console.debug(claims);

        const current_time = Date.now() / 1000; // Convert to seconds.
        return Math.trunc(claims.exp! - current_time);
    }

    private async showExpiredSession() {
        console.log("Session expired.");

        let lblExpiredTitle = "Session expired";
        let lblSessionExpired = "Your session has expired. Please sign in again to continue.";

        if (typeof Localization !== "undefined" && Localization.Dashboard) {
            lblExpiredTitle = Localization.Dashboard.lblExpiredTitle || lblExpiredTitle;
            lblSessionExpired = Localization.Dashboard.lblSessionExpired || lblSessionExpired;
        }

        await alert(lblSessionExpired, lblExpiredTitle);
        window.location.href = window.urls.timeout;
    }
}