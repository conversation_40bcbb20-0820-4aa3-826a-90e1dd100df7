namespace PaySpace.Venuta.Modules.CustomForms.Bureau
{
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache.Distributed;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Bureau;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Abstractions.Messages;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;

    public class BureauCustomFormConfigService : CustomFormConfigService<BureauCustomFormCategory, CountryCustomFieldGroup, int>, IBureauCustomFormConfigService
    {
        private readonly IRedisService redisService;
        private readonly IMessageBus messageBus;

        public BureauCustomFormConfigService(
            IDbContextRepository<BureauCustomFormCategory> repository,
            IRedisService redisService,
            IMessageBus messageBus)
            : base(repository)
        {
            this.redisService = redisService;
            this.messageBus = messageBus;
        }

        public override IQueryable<BureauCustomFormCategory> GetCustomForms(int discriminatorId)
        {
            return base.GetCustomForms(discriminatorId)
                .Where(_ => _.CountryId == discriminatorId);
        }

        public override IQueryable<BureauCustomFormCategory> GetUnlinkedCustomFormsByType(int discriminatorId, CustomFormType customFormType, long? customFieldId = default)
        {
            return from cfm in this.GetCustomForms(discriminatorId)
                   where cfm.FormType == customFormType && !this.Repository.Context.Set<BureauCustomField>().Any(_ => _.CustomFormCode == cfm.Code + "_B" && _.CustomFieldId != customFieldId)
                   select cfm;
        }

        public override Task<CustomFormScreenTypes?> GetCustomFormScreenTypeAsync(int discriminatorId, long key)
        {
            return this.GetCustomForms(discriminatorId)
                .Where(_ => _.CountryId == discriminatorId && _.CustomFormCategoryId == key)
                .Select(_ => _.CustomFormScreenType)
                .FirstOrDefaultAsync();
        }

        public override CustomFormType? GetCustomFormType(int discriminatorId, long key)
        {
            return this.GetCustomForms(discriminatorId)
                .Where(_ => _.CountryId == discriminatorId && _.CustomFormCategoryId == key)
                .Select(_ => _.FormType)
                .FirstOrDefault();
        }

        public override Task<CustomFormType?> GetCustomFormTypeAsync(int discriminatorId, long key)
        {
            return this.GetCustomForms(discriminatorId)
                .Where(_ => _.CountryId == discriminatorId && _.CustomFormCategoryId == key)
                .Select(_ => _.FormType)
                .FirstOrDefaultAsync();
        }

        public override Task<BureauCustomFormCategory> GetWithNavigationAsync(int discriminatorId, long key)
        {
            return this.Repository.Set
                .Include(_ => _.CustomFields)
                .Include(_ => _.CompanyCustomForms)
                .ThenInclude(_ => _.CustomFields)
                .Include(_ => _.EmployeeCustomForms)
                .ThenInclude(_ => _.CustomFields)
                .SingleOrDefaultAsync(_ => _.CountryId == discriminatorId && _.CustomFormCategoryId == key);
        }

        public override IQueryable<CountryCustomFieldGroup> GetCustomFieldGroups(int discriminatorId, long? customFormCategoryId)
        {
            return this.Repository.Context.Set<CountryCustomFieldGroup>()
                .AsNoTracking()
                .TagWithSource()
                .Where(_ => _.CountryId == discriminatorId)
                .Where(_ => _.FormId == SystemAreas.CustomFields.CustomFieldEntity.CustomForm)
                .Where(_ => _.CustomFormCategoryId == customFormCategoryId)
                .OrderBy(_ => _.Order);
        }

        public Task<string?> GetBureauCustomFormCategoryCodeAsync(long? bureauCustomFormCategoryId)
        {
            return this.Repository.Context.Set<BureauCustomFormCategory>().TagWithSource()
                .Where(_ => _.CustomFormCategoryId == bureauCustomFormCategoryId)
                .Select(_ => _.Code)
                .FirstOrDefaultAsync();
        }

        protected override async Task ClearCustomFormCacheAsync(BureauCustomFormCategory entity, string originalScreenType)
        {
            var companyIds = await this.Repository.Context.Set<Company>()
                .Where(_ => _.TaxCountryId == entity.CountryId)
                .Select(_ => _.CompanyId)
                .ToListAsync();

            var keys = companyIds.Select(_ => CacheKeys.CustomFieldDetails(_, nameof(CustomForm), $"{entity.Code}_B", null, null, null)).ToList();
            keys.AddRange(companyIds.Select(_ => CacheKeys.CustomFormCategory(_, $"{entity.Code}_B")));

            keys.AddRange(companyIds.Select(_ => CacheKeys.UserCustomFormIds(_, true, false)));
            keys.AddRange(companyIds.Select(_ => CacheKeys.UserCustomFormIds(_, false, true)));

            var screenType = entity.CustomFormScreenType.ToString();

            keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Company", string.Empty, false)));
            keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Company", screenType, false)));

            keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Employee", string.Empty, false)));
            keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Employee", screenType, false)));

            keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Employee", string.Empty, true)));
            keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Employee", screenType, true)));

            keys.AddRange(companyIds.Select(_ => CacheKeys.HasEmployeeCustomForms(_, screenType)));
            keys.AddRange(companyIds.Select(_ => CacheKeys.HasCompanyCustomForms(_, screenType)));

            keys.Add(CacheKeys.BureauCustomFormCategoryShouldCalculate(entity.CustomFormCategoryId));

            if (originalScreenType != null)
            {
                keys.AddRange(companyIds.Select(_ => CacheKeys.HasEmployeeCustomForms(_, originalScreenType)));
                keys.AddRange(companyIds.Select(_ => CacheKeys.HasCompanyCustomForms(_, originalScreenType)));
                keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Company", originalScreenType, false)));
                keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Employee", originalScreenType, false)));
                keys.AddRange(companyIds.Select(_ => CacheKeys.CustomForms(_, "Employee", originalScreenType, true)));
            }

            await this.redisService.RemoveAllAsync(keys);
        }

        protected override Task PublishUpdateCustomFormMessageAsync(BureauCustomFormCategory entity)
        {
            return this.messageBus.PublishMessageAsync(new UpdateCustomFieldsMessage
            {
                CountryId = entity.CountryId
            });
        }

        protected override Task DeleteCustomFieldOptionsAsync(BureauCustomFormCategory entity)
        {
            var customFields = entity.CustomFields
                .Where(_ => _.EditorType == "lookup")
                .Select(_ => _.CustomFieldId)
                .ToList();

            return this.Repository.Context.Set<CustomFieldOption>()
                .Where(_ => customFields.Contains(_.CountryCustomFieldId.Value))
                .ExecuteDeleteAsync();
        }
    }
}