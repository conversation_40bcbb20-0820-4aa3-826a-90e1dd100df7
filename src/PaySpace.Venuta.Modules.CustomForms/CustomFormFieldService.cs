namespace PaySpace.Venuta.Modules.CustomForms
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;

    public class CustomFormFieldService : ICustomFormFieldService
    {
        private readonly ReadOnlyContext readOnlyContext;
        private readonly ICompanyService companyService;
        private readonly IScopedCache scopedCache;

        public CustomFormFieldService(ReadOnlyContext readOnlyContext, ICompanyService companyService, IScopedCache scopedCache)
        {
            this.readOnlyContext = readOnlyContext;
            this.companyService = companyService;
            this.scopedCache = scopedCache;
        }

        public List<CustomFieldFormField> GetCustomFormFields(long companyId, long categoryId, string customFieldType)
        {
            return this.scopedCache.GetOrCreate(
                $"CustomFieldFormFields:CustomForm:{companyId}:{categoryId}:{customFieldType}",
                () => this.GetCustomFormFieldsInternal(companyId, categoryId, customFieldType).ToList());
        }

        public Task<List<CustomFieldFormField>> GetCustomFormFieldsAsync(long companyId, long categoryId, string customFieldType)
        {
            return this.scopedCache.GetOrCreateAsync(
                $"CustomFieldFormFields:CustomForm:{companyId}:{categoryId}:{customFieldType}",
                () => this.GetCustomFormFieldsInternal(companyId, categoryId, customFieldType)
                    .ToListAsync());
        }

        public async Task<IQueryable<CustomFieldOption>> GetFieldOptionsAsync(long companyId, string fieldCode, string categoryCode)
        {
            var customFieldType = "C";

            // We append the custom field type to the category on DTOs,
            // We can then find it by looking at the last 2 chars of the category when needed
            // If the category does not end in `_B` it's safe to assume that it ends with `_C`
            if (categoryCode.EndsWith("_B"))
            {
                customFieldType = "B";
            }

            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            var code = categoryCode[..^2];
            return this.readOnlyContext.Set<CustomFieldOption>()
                .Where(_ => (customFieldType == "C" && _.CompanyCustomField.CompanyId == companyId && _.CompanyCustomField.CompanyCustomFormCategory.Code == code && _.CompanyCustomField.FieldCode == fieldCode)
                            || (customFieldType == "B" && _.CountryCustomField.CountryId == countryId && _.CountryCustomField.BureauCustomFormCategory.Code == code && _.CountryCustomField.FieldCode == fieldCode));
        }

        public async Task<IQueryable<CustomFieldOption>> GetFieldOptionsAsync(long companyId, string fieldCode, string categoryCode, string parentValue, bool filterParentByCode)
        {
            var customFieldType = "C";

            // We append the custom field type to the category on DTOs,
            // We can then find it by looking at the last 2 chars of the category when needed
            // If the category does not end in `_B` it's safe to assume that it ends with `_C`
            if (categoryCode.EndsWith("_B"))
            {
                customFieldType = "B";
            }

            var parentCode = parentValue;
            if (!filterParentByCode)
            {
                var fieldQuery = await this.GetFieldOptionsAsync(companyId, fieldCode, categoryCode);
                parentCode = await fieldQuery.Where(_ => _.Description == parentValue).Select(_ => _.Code).SingleOrDefaultAsync();
            }

            var code = categoryCode[..^2];
            var countryId = await this.companyService.GetTaxCountryIdAsync(companyId);
            return this.readOnlyContext.Set<CustomFieldOption>()
                .Where(_ => (customFieldType == "C" && _.CompanyCustomField.CompanyId == companyId && _.CompanyCustomField.CompanyCustomFormCategory.Code == code && _.CompanyCustomField.ParentFieldCode == fieldCode)
                            || (customFieldType == "B" && _.CountryCustomField.CountryId == countryId && _.CountryCustomField.BureauCustomFormCategory.Code == code && _.CountryCustomField.ParentFieldCode == fieldCode))
                .Where(_ => _.ParentCode == parentCode);
        }

        public async Task<IQueryable<CustomFieldOption>> GetFieldOptionByCodeAsync(long companyId, string fieldCode, string categoryCode, string code)
        {
            return (await this.GetFieldOptionsAsync(companyId, fieldCode, categoryCode))
                .Where(_ => _.Code == code);
        }

        public async Task<IQueryable<CustomFieldOption>> GetFieldOptionByCodeAsync(long companyId, string fieldCode, string categoryCode, string parentValue, bool filterParentByCode, string code)
        {
            return (await this.GetFieldOptionsAsync(companyId, fieldCode, categoryCode, parentValue, filterParentByCode))
                .Where(_ => _.Code == code);
        }

        private IQueryable<CustomFieldFormField> GetCustomFormFieldsInternal(long companyId, long categoryId, string customFieldType)
        {
            return this.readOnlyContext.Set<CustomFieldFormField>()
                .Where(_ => _.CompanyId == companyId && _.CustomFormId == categoryId)
                .Where(_ => _.CustomFieldType == customFieldType)
                .Where(_ => _.Entity == "CustomForm")
                .OrderBy(_ => _.OrderNumber);
        }

        public async Task<long> GetCustomFormIdForCollectionCode(long companyId, string customFieldType, string categoryCode)
        {
            return await this.readOnlyContext.Set<CustomFieldFormField>()
                    .Where(_ => _.CompanyId == companyId && _.CustomFieldType == customFieldType &&
                                _.CollectionCode == categoryCode && _.Entity == "CustomForm")
                    .Select(_ => _.CustomFormId.Value)
                    .FirstAsync();
        }
    }
}