namespace PaySpace.Venuta.Modules.CustomForms.Company
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;

    using PaySpace.Cache.Distributed;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Abstractions.Messages;
    using PaySpace.Venuta.Modules.CustomForms.Abstractions;

    public class CompanyCustomFormConfigService : CustomFormConfigService<CompanyCustomFormCategory, CompanyCustomFieldGroup, long>, ICompanyCustomFormConfigService
    {
        private readonly IRedisService redisService;
        private readonly IMessageBus messageBus;

        public CompanyCustomFormConfigService(
            IRedisService redisService,
            IDbContextRepository<CompanyCustomFormCategory> repository,
            IMessageBus messageBus)
            : base(repository)
        {
            this.redisService = redisService;
            this.messageBus = messageBus;
        }

        public override IQueryable<CompanyCustomFormCategory> GetCustomForms(long discriminatorId)
        {
            return base.GetCustomForms(discriminatorId)
                .Where(_ => _.CompanyId == discriminatorId && _.JobLevel != true);
        }

        public override IQueryable<CompanyCustomFormCategory> GetUnlinkedCustomFormsByType(long discriminatorId, CustomFormType customFormType, long? customFieldId = default)
        {
            return from cfm in this.GetCustomForms(discriminatorId)
                   where cfm.FormType == customFormType && !this.Repository.Context.Set<CompanyCustomField>().Any(_ => _.CustomFormCode == cfm.Code + "_C" && _.CustomFieldId != customFieldId)
                   select cfm;
        }

        public override Task<CustomFormScreenTypes?> GetCustomFormScreenTypeAsync(long discriminatorId, long key)
        {
            return this.GetCustomForms(discriminatorId)
                .Where(_ => _.CompanyId == discriminatorId && _.CustomFormCategoryId == key)
                .Select(_ => _.CustomFormScreenType)
                .FirstOrDefaultAsync();
        }

        public override CustomFormType? GetCustomFormType(long discriminatorId, long key)
        {
            return this.GetCustomForms(discriminatorId)
                .Where(_ => _.CompanyId == discriminatorId && _.CustomFormCategoryId == key)
                .Select(_ => _.FormType)
                .FirstOrDefault();
        }

        public override Task<CustomFormType?> GetCustomFormTypeAsync(long discriminatorId, long key)
        {
            return this.GetCustomForms(discriminatorId)
                .Where(_ => _.CompanyId == discriminatorId && _.CustomFormCategoryId == key)
                .Select(_ => _.FormType)
                .FirstOrDefaultAsync();
        }

        public override Task<CompanyCustomFormCategory> GetWithNavigationAsync(long discriminatorId, long key)
        {
            // Only include CustomFields, as dependency records are not allowed to be deleted from here
            return this.Repository.Set
                .Include(_ => _.CustomFields)
                .SingleOrDefaultAsync(_ => _.CompanyId == discriminatorId && _.CustomFormCategoryId == key);
        }

        public override IQueryable<CompanyCustomFieldGroup> GetCustomFieldGroups(long discriminatorId, long? customFormCategoryId)
        {
            return this.Repository.Context.Set<CompanyCustomFieldGroup>()
                .AsNoTracking()
                .TagWithSource()
                .Where(_ => _.CompanyId == discriminatorId)
                .Where(_ => _.FormId == SystemAreas.CustomFields.CustomFieldEntity.CustomForm)
                .Where(_ => _.CustomFormCategoryId == customFormCategoryId)
                .OrderBy(_ => _.Order);
        }

        protected override async Task ClearCustomFormCacheAsync(CompanyCustomFormCategory entity, string originalScreenType)
        {
            var keys = new List<string>();
            var screenType = entity.CustomFormScreenType.ToString();
            var companyId = entity.CompanyId;

            keys.Add(CacheKeys.CustomForms(companyId, "Company", string.Empty, false));
            keys.Add(CacheKeys.CustomForms(companyId, "Company", screenType, false));

            keys.Add(CacheKeys.CustomForms(companyId, "Employee", string.Empty, false));
            keys.Add(CacheKeys.CustomForms(companyId, "Employee", screenType, false));

            keys.Add(CacheKeys.CustomForms(companyId, "Employee", string.Empty, true));
            keys.Add(CacheKeys.CustomForms(companyId, "Employee", screenType, true));

            keys.Add(CacheKeys.HasEmployeeCustomForms(companyId, screenType));
            keys.Add(CacheKeys.HasCompanyCustomForms(companyId, screenType));

            keys.Add(CacheKeys.UserCustomFormIds(companyId, true, false));
            keys.Add(CacheKeys.UserCustomFormIds(companyId, false, true));

            if (originalScreenType != null)
            {
                keys.Add(CacheKeys.HasEmployeeCustomForms(companyId, originalScreenType));
                keys.Add(CacheKeys.HasCompanyCustomForms(companyId, originalScreenType));
                keys.Add(CacheKeys.CustomForms(companyId, "Company", originalScreenType, false));
                keys.Add(CacheKeys.CustomForms(companyId, "Employee", originalScreenType, false));
                keys.Add(CacheKeys.CustomForms(companyId, "Employee", originalScreenType, true));
            }

            keys.Add(CacheKeys.CustomFormCategory(companyId, $"{entity.Code}_C"));
            keys.Add(CacheKeys.CustomFieldDetails(companyId, nameof(CustomForm), $"{entity.Code}_C", null, null, null));

            await this.redisService.RemoveAllAsync(keys);
        }

        protected override Task PublishUpdateCustomFormMessageAsync(CompanyCustomFormCategory entity)
        {
            return this.messageBus.PublishMessageAsync(new UpdateCustomFieldsMessage
            {
                CompanyId = entity.CompanyId
            });
        }

        protected override Task DeleteCustomFieldOptionsAsync(CompanyCustomFormCategory entity)
        {
            var customFields = entity.CustomFields
                .Where(_ => _.EditorType == "lookup")
                .Select(_ => _.CustomFieldId)
                .ToList();

            return this.Repository.Context.Set<CustomFieldOption>()
                .Where(_ => customFields.Contains(_.CompanyCustomFieldId.Value))
                .ExecuteDeleteAsync();
        }
    }
}