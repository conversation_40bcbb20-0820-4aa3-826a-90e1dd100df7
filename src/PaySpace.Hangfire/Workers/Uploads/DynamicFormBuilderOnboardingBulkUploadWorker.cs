namespace PaySpace.Hangfire.Workers.Uploads
{
    using System;
    using System.IdentityModel.Tokens.Jwt;
    using System.IO;
    using System.Linq;
    using System.Reflection;
    using System.Text.Json;
    using System.Threading;
    using System.Threading.Tasks;

    using global::Hangfire.Console;
    using global::Hangfire.Server;

    using GrpcBulkUploadService;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Logging;

    using PaySpace.Hangfire.Services;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.BulkUploads;
    using PaySpace.Venuta.Excel.Abstractions;
    using PaySpace.Venuta.Excel.Abstractions.Messages;
    using PaySpace.Venuta.Excel.Extensions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Abstractions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Exceptions;
    using PaySpace.Venuta.Modules.DynamicFormBuilder.Model;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Security.Identity;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Validation.Annotations;

    public class DynamicFormBuilderOnboardingBulkUploadWorker : BulkUploadWorker
    {
        private readonly ApplicationContext context;
        private readonly IDynamicFormBuilderService dynamicFormBuilderService;
        private readonly TokenEndpointService tokenEndpointService;

        public DynamicFormBuilderOnboardingBulkUploadWorker(
            IBulkUploadServiceContainer container,
            ISecurityProfileFactory securityProfileFactory,
            IUserService userService,
            IAttachmentStorageService attachmentStorageService,
            ApplicationContext context,
            IDynamicFormBuilderService dynamicFormBuilderService,
            TokenEndpointService tokenEndpointService)
            : base(container, securityProfileFactory, userService, attachmentStorageService)
        {
            this.dynamicFormBuilderService = dynamicFormBuilderService;
            this.context = context;
            this.tokenEndpointService = tokenEndpointService;
        }

        protected override Task<FileUploadResult> UploadFileAsync(
            ISecurityProfile profile,
            Stream stream,
            BulkUploadMessage message,
            IServiceProvider serviceProvider,
            CancellationToken cancellationToken)
        {
            // This functions should not be implemented for Quick Onboarding, because we are calling DFB API to do the processing work.
            // If this work was done here we would have performance issues and possibly would be sending excel file packages back and frouth between
            // Venuta and DFB API. To stop that we just pass the message to DFB API and it handles the file processing on its own.
            // See this.ProcessFileUpload(...) for more info
            throw new NotImplementedException();
        }

        protected override async Task ProcessFileUpload(
            PerformContext context,
            BulkUploadMessage message,
            DateTime uploadDate,
            IServiceProvider serviceProvider,
            ISecurityProfile profile,
            Func<ISecurityProfile, Stream, BulkUploadMessage, IServiceProvider, CancellationToken, Task<FileUploadResult>> uploadAsync,
            CancellationToken cancellationToken)
        {
            context.WriteLine("ProcessFileUpload : Starting DFB Upload");

            var uploadTypeName = await this.GetUploadTypeAsync(serviceProvider, message);
            var utcUploadTime = uploadDate.ToUniversalTime();
            var bulkUploadProcessingRequest = new BulkUploadProcessRequest
            {
                NotificationId = message.NotificationId.ToString(),
                UserId = message.UserId,
                FrequencyId = message.FrequencyId,
                RunId = message.RunId,
                CompanyId = message.CompanyId,
                BlobUri = message.BlobUri.ToString(),
                AttachmentName = message.AttachmentName,
                AccessToken = await this.GetExtendedTokenAsync(message.AccessToken),
                UploadDate = Google.Protobuf.WellKnownTypes.Timestamp.FromDateTime(utcUploadTime),
                UploadTypeName = uploadTypeName,
                ModuleCode = message.UploadDtoType.GetCustomAttribute<NavigationControllerNameAttribute>()?.Names[0]
                             ?? message.UploadDtoType.Name.TrimDto(),
                Values = JsonSerializer.Serialize(message.Values)
            };

            var bulkUploadResponse = await this.dynamicFormBuilderService.ProcessBulkUploadAndSaveUploadHistoryAsync(
                new HangfireLogger(context),
                bulkUploadProcessingRequest,
                cancellationToken);

            context.WriteLine("ProcessFileUpload : Received bulkUploadResponse: " + JsonSerializer.Serialize(bulkUploadResponse));

            // Propagate issues so they are handled accordingly
            switch (bulkUploadResponse.FailureExceptionType)
            {
                case BulkUploadProcessFailureType.DyplicateFieldException:
                    throw new DuplicateFieldException(bulkUploadResponse.ExceptionMessage);
                case BulkUploadProcessFailureType.AddUploadHistoryException:
                    await this.attachmentStorageService.DeleteIfExistsAsync(message.BlobUri, cancellationToken);
                    throw new BulkUploadProcessingException(bulkUploadResponse.ExceptionMessage);
                case BulkUploadProcessFailureType.Exception:
                    throw new BulkUploadProcessingException(bulkUploadResponse.ExceptionMessage);
                default:
                    break;
            }

            // Send Notifications from AddUploadHistory
            // Get companyBulkUpload by the provided ID from API response

            var companyBulkUpload = await this.context.Set<CompanyBulkUpload>()
                .AsNoTracking()
                .FirstOrDefaultAsync(_ => _.BulkEntryId == bulkUploadResponse.CreatedCompanyBulkUploadId, cancellationToken);

            if (companyBulkUpload is not null)
            {
                context.WriteLine("ProcessFileUpload : companyBulkUpload found.");

                var fileUploadResult = UploadResult.ToFileUploadResult(bulkUploadResponse.UploadResult);
                await this.SendStatusEmailAsync(serviceProvider, companyBulkUpload, uploadTypeName, fileUploadResult);
                await SendNotificationAsync(serviceProvider, companyBulkUpload, fileUploadResult, message.NotificationId);
                context.WriteLine("ProcessFileUpload : notifications sent.");
            }

            // Delete existing blob
            await this.attachmentStorageService.DeleteIfExistsAsync(message.BlobUri, cancellationToken);
        }

        private async Task<string?> GetExtendedTokenAsync(string accessToken)
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtSecurityToken = handler.ReadJwtToken(accessToken);
            var scopeClaims = jwtSecurityToken.Claims.Where(c => c.Type is "scope").Select(c => c.Value).ToList();
            var scopeSep = string.Join(" ", scopeClaims);
            var extendedToken = await this.tokenEndpointService.ExchangeAsync(accessToken, scopeSep, "extend", default);
            return extendedToken?.AccessToken;
        }

        protected sealed class HangfireLogger : ILogger
        {
            private readonly PerformContext context;

            public HangfireLogger(PerformContext context)
            {
                this.context = context;
            }
            public IDisposable BeginScope<TState>(TState state)
            {
                return default;
            }

            public bool IsEnabled(LogLevel logLevel)
            {
                return logLevel != LogLevel.None;
            }

            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                this.context.WriteLine(state);
            }
        }
    }
}