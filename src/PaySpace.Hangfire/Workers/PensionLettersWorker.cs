namespace PaySpace.Hangfire.Workers
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using global::Hangfire;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Hangfire.Abstractions.Filters.Annotations;
    using PaySpace.Hangfire.Services;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Modules.Payslips.Abstractions.Messages;
    using PaySpace.Venuta.Modules.PensionEnrolment.Abstractions;
    using PaySpace.Venuta.Modules.PensionEnrolment.Abstractions.Messages;
    using PaySpace.Venuta.Modules.PensionEnrolment.Abstractions.Models;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Storage;

    public class PensionLettersWorker
    {
        private readonly IMessageBus messageBus;
        private readonly IDocumentService documentService;
        private readonly IFileExportMessageService messageService;
        private readonly IEmployeePensionLetterService employeePensionLetterService;
        private readonly IStringLocalizer<EmployeePensionLetter> localizer;

        private sealed record PensionLetterFile(string FileName, PensionLetterDetail LetterDetail, byte[] Bytes);

        private sealed record PensionLetterDetail
        {
            public long EmployeePensionLetterId { get; set; }

            public long EmployeeId { get; set; }

            public long? EmployeeAssessmentId { get; set; }

            public string EmployeeNumber { get; init; }

            public string CompanyName { get; set; }

            public string LetterDescription { get; init; }

            public PensionLetterType PensionLetterType { get; set; }

            public DateTime RaisedDate { get; set; }

            public DateTime AssessmentDate { get; set; }
        }

        public PensionLettersWorker(
            IMessageBus messageBus,
            IDocumentService documentService,
            IFileExportMessageService messageService,
            IEmployeePensionLetterService employeePensionLetterService,
            IStringLocalizer<EmployeePensionLetter> localizer)
        {
            this.messageBus = messageBus;
            this.documentService = documentService;
            this.messageService = messageService;
            this.employeePensionLetterService = employeePensionLetterService;
            this.localizer = localizer;
        }

        [PreventDuplicate]
        [AutomaticRetry(Attempts = 0)]
        [JobDisplayName("Report: Pension Letter Email")]
        public async Task EmailLetters(EmailPensionLetterMessage message, CancellationToken cancellationToken)
        {
            try
            {
                await this.messageService.NotifyProgressAsync(message.UserId, message.NotificiationId);

                var sendDate = DateTime.Today;
                var letterFiles = await this.GetDocuments(message, cancellationToken);

                if (letterFiles.Count == 0)
                {
                    throw new InvalidOperationException("No files to email");
                }

                await this.employeePensionLetterService.MarkLettersAsSentAsync(
                    message.CompanyId,
                    sendDate,
                    letterFiles.Select(_ => _.LetterDetail.EmployeePensionLetterId).ToList());

                foreach (var letter in letterFiles)
                {
                    var parameters = new EmailParameters();
                    parameters.TryAdd("CompanyName", letter.LetterDetail.CompanyName);

                    BackgroundJob.Enqueue<EmailWorker>(_ => _.EmailAsync(
                        null,
                        EmailTemplates.PensionLetterEmail,
                        letter.LetterDetail.EmployeeId,
                        message.Culture.Name,
                        parameters,
                        new EmailDetail(),
                        new[] { new RawAttachment { FileName = letter.FileName, Bytes = letter.Bytes } },
                        cancellationToken));
                }

                await this.messageService.NotifyCompleteAsync(message.UserId, message.NotificiationId);
            }
            catch
            {
                // reset the status if letters cannot be sent
                await this.employeePensionLetterService.MarkLettersAsPendingAsync(message.CompanyId, PensionLetterStatus.Sending, message.LetterIds);

                await this.messageService.NotifyErrorAsync(message.UserId, message.NotificiationId, this.localizer.GetString("lblLetterGenerationError"));
                throw;
            }
        }

        [PreventDuplicate]
        [AutomaticRetry(Attempts = 0)]
        [JobDisplayName("Report: Pension Letter Download")]
        public async Task DownloadLetters(DownloadPensionLetterMessage message, CancellationToken cancellationToken)
        {
            var key = Guid.NewGuid().ToString();

            try
            {
                await this.messageService.NotifyProgressAsync(message.UserId, message.NotificiationId);

                var letterFiles = await this.GetDocuments(message, cancellationToken);

                var letterNotificationMessage = this.localizer.GetString("lblLettersMessage").Value;
                switch (letterFiles.Count)
                {
                    case 1:
                        var letter = letterFiles.First();
                        await this.documentService.StoreBlobAsync(StorageContainers.TmpReports, key, message.UserId, letter.FileName, letter.Bytes, cancellationToken);
                        letterNotificationMessage = letter.FileName;
                        break;
                    case > 1:
                        var bytes = await Archive.ZipAsync(letterFiles.Select(_ => new KeyValuePair<string, byte[]>(_.FileName, _.Bytes)));
                        await this.documentService.StoreBlobAsync(StorageContainers.TmpReports, key, message.UserId, $"{this.localizer.GetString("lblLettersMessage")}.zip", bytes, cancellationToken);
                        break;
                    default:
                        await this.messageService.NotifyErrorAsync(message.UserId, message.NotificiationId, this.localizer.GetString("lblEmptyDownload"));
                        return;
                }

                await this.messageService.NotifyCompleteAsync(message.UserId, message.NotificiationId);
                await this.messageService.NotifyFileDownloadAsync(message.UserId, key, letterNotificationMessage, message.Tenant);
            }
            catch
            {
                await this.messageService.NotifyErrorAsync(message.UserId, message.NotificiationId, this.localizer.GetString("lblLetterGenerationError"));
                throw;
            }
        }

        private async Task<List<PensionLetterFile>> GetDocuments(
            PensionLetterMessageBase message,
            CancellationToken cancellationToken)
        {
            var letterDetails = await this.employeePensionLetterService.GetPensionLetters(message.CompanyId)
                .Where(_ => _.Employee.CompanyId == message.CompanyId
                            && message.LetterIds.Contains(_.EmployeePensionLetterId))
                .Select(_ => new PensionLetterDetail
                {
                    EmployeePensionLetterId = _.EmployeePensionLetterId,
                    EmployeeId = _.EmployeeId,
                    EmployeeAssessmentId = _.EmployeePensionAssessmentId,
                    LetterDescription = _.PensionLetterType.Description,
                    EmployeeNumber = _.Employee.EmployeeNumber,
                    CompanyName = _.Employee.Company.CompanyName,
                    PensionLetterType = (PensionLetterType)_.PensionLetterTypeId,
                    RaisedDate = _.RaisedDate,
                    AssessmentDate = _.EmployeePensionAssessment.AssessmentDate ?? DateTime.Today
                })
                .ToListAsync(cancellationToken: cancellationToken);

            var letterFiles = new List<PensionLetterFile>();
            foreach (var letter in letterDetails)
            {
                var reportPath = GetReportPath(letter.PensionLetterType);
                var fileName = $"{letter.LetterDescription}_{letter.EmployeeNumber}_{letter.RaisedDate:yyyy-MM-dd}.pdf";

                letterFiles.Add(new PensionLetterFile(fileName, letter, await this.GetReportFileAsync(message, letter, reportPath, cancellationToken)));
            }

            return letterFiles;
        }

        private static string GetReportPath(PensionLetterType pensionLetterType)
        {
            var reportPath = pensionLetterType switch
            {
                PensionLetterType.AUTO_ENROLLED => "3080da89-8b81-4620-831e-6710f9695e84.repx",
                PensionLetterType.AUTO_ENROLLED_NO_TAX => "b8a41dd6-99cf-4ac0-8acd-fc00f2cc96ce.repx",
                PensionLetterType.NOT_AUTO_ENROLLED => "8f13bb5c-298e-40f2-9687-1ed4c68a6af7.repx",
                PensionLetterType.POSTPONED => "5084a94e-5f5e-493f-8c85-80ecfd94cf69.repx",
                PensionLetterType.RE_ENROLLED => "4e998f06-5f48-4026-a882-0818a93c5289.repx",
                _ => string.Empty
            };

            return reportPath;
        }

        private async Task<byte[]> GetReportFileAsync(
            PensionLetterMessageBase message,
            PensionLetterDetail letter,
            string reportPath,
            CancellationToken cancellationToken)
        {
            var parameters = new Dictionary<string, object>
            {
                { "CompanyId", message.CompanyId },
                { "CompanyIds", message.CompanyId },
                { "EffectiveDate", letter.RaisedDate },
                { "PensionDate", letter.AssessmentDate },
                { "EmployeeNumber", letter.EmployeeNumber }
            };

            var report = await this.messageBus.RequestAsync<ExportCustomReportMessage, ReportResponse>(
                new ExportCustomReportMessage
                {
                    Background = false,
                    AccessToken = message.AccessToken,
                    NotificationId = ExportReportMessage.GetHash(parameters),
                    UserId = message.UserId,
                    CompanyId = message.CompanyId,
                    ReportUrl = reportPath + $"?CompanyId={message.CompanyId}",
                    Parameters = parameters,
                    Format = "pdf",
                    Culture = CultureInfo.CurrentCulture
                },
                cancellationToken);

            return report.Document.HasValue ? await report.Document.Value : null;
        }
    }
}