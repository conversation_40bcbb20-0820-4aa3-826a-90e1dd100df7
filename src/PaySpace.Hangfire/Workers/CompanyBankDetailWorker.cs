namespace PaySpace.Hangfire.Workers
{
    using System;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using global::Hangfire;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Caching.Distributed;

    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Agency;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Logging.Abstractions;

    [Queue("default")]
    public class CompanyBankDetailWorker
    {
        private readonly ApplicationContext context;
        private readonly IAuditPublisherService auditPublisherService;
        private readonly IDistributedCache distributedCache;

        public CompanyBankDetailWorker(
            ApplicationContext context,
            IAuditPublisherService auditPublisherService,
            IDistributedCache distributedCache)
        {
            this.context = context;
            this.auditPublisherService = auditPublisherService;
            this.distributedCache = distributedCache;
        }

        [AutomaticRetry(Attempts = 0)]
        [DisableConcurrentExecution(60 * 60 * 24)]
        public async Task UpdateCompanyBankDetailsAsync(long userId, long agencyId, CancellationToken cancellationToken)
        {
            var agencyBankDetail = await this.context.Set<AgencyBankDetail>()
                                    .AsNoTracking()
                                    .SingleOrDefaultAsync(_ => _.AgencyId == agencyId, cancellationToken);

            if (agencyBankDetail != null)
            {
                await this.AddCompanyBankDetailsAsync(userId, agencyId, agencyBankDetail, cancellationToken);
            }
            else
            {
                await this.DeleteCompanyBankDetailsAsync(userId, agencyId, cancellationToken);
            }
        }

        private async Task AddCompanyBankDetailsAsync(long userId, long agencyId, AgencyBankDetail agencyBankDetail, CancellationToken cancellationToken)
        {
            // Get all linked companyIds as new records will need to be added for bank details if they don't exist
            var linkedCompanyIds = await this.context.Set<Company>()
                                .Where(_ => _.AgencyId == agencyId)
                                .Select(_ => _.CompanyId)
                                .ToListAsync(cancellationToken);

            var companyBankDetails = await this.context.Set<CompanyBankDetail>()
                                    .Where(_ => linkedCompanyIds.Contains(_.CompanyId))
                                    .ToListAsync(cancellationToken);

            foreach (var companyId in linkedCompanyIds)
            {
                var companyBankDetail = companyBankDetails.SingleOrDefault(_ => _.CompanyId == companyId);
                if (companyBankDetail != null)
                {
                    this.SetBankValues(agencyBankDetail, companyBankDetail);
                }
                else
                {
                    companyBankDetail = new CompanyBankDetail { CompanyId = companyId };
                    this.SetBankValues(agencyBankDetail, companyBankDetail);
                    this.context.Add(companyBankDetail);
                }

                await this.distributedCache.RemoveAsync(CacheKeys.HasAgencyBankDetails(companyId), cancellationToken);
            }

            // Get all the records to be audited.
            var changes = this.auditPublisherService.RecordChanges((DbContext)this.context);

            await this.context.SaveChangesAsync(cancellationToken: cancellationToken);

            // Audit changes.
            await this.auditPublisherService.PublishAuditAsync(changes, userId, DateTime.UtcNow, cancellationToken);
        }

        private async Task DeleteCompanyBankDetailsAsync(long userId, long agencyId, CancellationToken cancellationToken)
        {
            var linkedCompanyIds = await this.context.Set<Company>()
                                    .Where(_ => _.AgencyId == agencyId)
                                    .Select(_ => _.CompanyId)
                                    .ToListAsync(cancellationToken);

            var companyBankDetails = await this.context.Set<CompanyBankDetail>()
                                    .Where(_ => _.Company.AgencyId == agencyId)
                                    .ToListAsync(cancellationToken);

            this.context.RemoveRange(companyBankDetails);

            foreach (var companyId in linkedCompanyIds)
            {
                await this.distributedCache.RemoveAsync(CacheKeys.HasAgencyBankDetails(companyId), cancellationToken);
            }

            // Get all the records to be audited.
            var changes = this.auditPublisherService.RecordChanges((DbContext)this.context);

            await this.context.SaveChangesAsync(cancellationToken: cancellationToken);

            // Audit changes.
            await this.auditPublisherService.PublishAuditAsync(changes, userId, DateTime.UtcNow, cancellationToken);
        }

        private void SetBankValues(AgencyBankDetail agencyBankDetail, CompanyBankDetail companyBankDetail)
        {
            companyBankDetail.BankAccountNo = agencyBankDetail.BankAccountNo;
            companyBankDetail.BankBranchNo = agencyBankDetail.BankBranchNo;
            companyBankDetail.BankName = agencyBankDetail.BankName;
            companyBankDetail.AccountType = (AccountType)agencyBankDetail.AccountTypeId;
        }
    }
}