namespace PaySpace.Hangfire.Workers
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Globalization;
    using System.Linq;
    using System.Resources;
    using System.Threading;
    using System.Threading.Tasks;

    using global::Hangfire;
    using global::Hangfire.Console;
    using global::Hangfire.Server;

    using Microsoft.AspNetCore.Http;
    using Microsoft.Extensions.Hosting;

    using Nager.PublicSuffix;
    using Nager.PublicSuffix.RuleProviders;

    using PaySpace.Hangfire.Abstractions.Filters;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.External;

    using SmartFormat;

    using IEmailClient = IEmailClient;

    [Queue("email")]
    [InvalidEmailIntercepter]
    public class EmailWorker
    {
        private static readonly string[] AllowAllEnvironmentsTemplates = [EmailTemplates.Otp, EmailTemplates.Forgot];

        private readonly IEmailClient reportClient;
        private readonly ITenantService tenantService;
        private readonly IUserService userService;
        private readonly IEmployeeService employeeService;
        private readonly IEmailAddressService emailAddressService;
        private readonly IHostEnvironment env;
        private readonly IRuleProvider ruleProvider;

        public EmailWorker(
            IEmailClient reportClient,
            ITenantService tenantService,
            IUserService userService,
            IEmployeeService employeeService,
            IEmailAddressService emailAddressService,
            IHostEnvironment env,
            IRuleProvider ruleProvider)
        {
            this.reportClient = reportClient;
            this.tenantService = tenantService;
            this.userService = userService;
            this.employeeService = employeeService;
            this.emailAddressService = emailAddressService;
            this.env = env;
            this.ruleProvider = ruleProvider;
        }

        private static IReadOnlyDictionary<string, string> TemplateMapping { get; } = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                { EmailTemplates.BulkUploadStatus, MailTemplates.BulkUpload },
                { EmailTemplates.Register, MailTemplates.Url },
                { EmailTemplates.Deregister, MailTemplates.Url },
                { EmailTemplates.Forgot, MailTemplates.Url },
                { EmailTemplates.Otp, MailTemplates.Information },
                { EmailTemplates.Payslip, MailTemplates.Information },
                { EmailTemplates.Invoice, MailTemplates.Information },
                { EmailTemplates.OutStatements, MailTemplates.Information },
                { EmailTemplates.CutOffReminder, MailTemplates.Information },
                { EmailTemplates.PayslipScheduled, MailTemplates.Url },
                { EmailTemplates.WorkflowSubmission, MailTemplates.Workflow },
                { EmailTemplates.WorkflowActioned, MailTemplates.Workflow },
                { EmailTemplates.WorkflowDeleted, MailTemplates.Workflow },
                { EmailTemplates.WorkflowApprover, MailTemplates.Workflow },
                { EmailTemplates.BankDetailUpdated, MailTemplates.Workflow },
                { EmailTemplates.CloudRoomAssignedToUserChanged, MailTemplates.Workflow },
                { EmailTemplates.CloudRoomStatusCompleted, MailTemplates.Workflow },
                { EmailTemplates.CloudRoomCommentAdded, MailTemplates.Workflow },
                { EmailTemplates.CloudRoomItemCreated, MailTemplates.Workflow },
                { EmailTemplates.AppraisalAccepted, MailTemplates.Information },
                { EmailTemplates.AppraisalApprovalRequired, MailTemplates.Information },
                { EmailTemplates.AppraisalFinished, MailTemplates.Information },
                { EmailTemplates.AppraisalRejected, MailTemplates.Information },
                { EmailTemplates.KpaReviewAccepted, MailTemplates.Information },
                { EmailTemplates.KpaReviewRejected, MailTemplates.Information },
                { EmailTemplates.KpaReviewAcceptedWithoutComment, MailTemplates.Information },
                { EmailTemplates.KpaReviewRejectedWithoutComment, MailTemplates.Information },
                { EmailTemplates.NewEngagement, MailTemplates.Information },
                { EmailTemplates.KpaReviewSubmission, MailTemplates.Audit },
                { EmailTemplates.OnboardingNotification, MailTemplates.Workflow },
                { EmailTemplates.TerminationNotification, MailTemplates.Workflow },
                { EmailTemplates.Report, MailTemplates.Information },
                { EmailTemplates.EmployeeDeleted, MailTemplates.Workflow },
                { EmailTemplates.PayRateNotification, MailTemplates.Information },
                { EmailTemplates.EmailAdded, MailTemplates.Url },
                { EmailTemplates.XeroContact, MailTemplates.Information },
                { EmailTemplates.LeaveExpiry, MailTemplates.Workflow },
                { EmailTemplates.WebhookErrors, MailTemplates.WebhookErrors },
                { EmailTemplates.UserAdded, MailTemplates.Url },
                { EmailTemplates.JobCreated, MailTemplates.Information },
                { EmailTemplates.JobCopied, MailTemplates.Information },
                { EmailTemplates.JobTransferred, MailTemplates.Information },
                { EmailTemplates.PensionLetterEmail, MailTemplates.Information },
                { EmailTemplates.CompanyReset, MailTemplates.Information },
                { EmailTemplates.LeaveNotification, MailTemplates.Notification }
            };

        [DisplayName("Email: {1} - User {2}")]
        public async Task EmailAsync(PerformContext context, string name, long userId, EmailParameters parameters, EmailDetail detail, IList<RawAttachment> attachments, CancellationToken cancellationToken)
        {
            var user = await this.userService.GetUserNameByUserIdAsync(userId);
            parameters.TryAdd("Name", user.FirstName);

            var tenant = await this.tenantService.GetTenantForUserAsync(userId);

            context.WriteLine("Host: {0}", tenant.Host);
            context.WriteLine("Email: {0}", user.Email);

            parameters.TryAdd("Agency", tenant.AgencyName);
            parameters.TryAdd("AgencyLogo", tenant.AgencyLogoUri?.ToString());
            parameters.TryAdd("SignatureUrl", "https://" + tenant.Host);
            parameters.TryAdd("Url", string.Empty);

            await this.PostAsync(context, name, await this.GetFromAsync(tenant.AgencyName, tenant.Host), user.Email, user.GetLocale(), parameters, detail, attachments, cancellationToken);
        }

        [DisplayName("Email: {1} - Employee {2}")]
        public async Task EmailAsync(
            PerformContext context,
            string name,
            long employeeId,
            string culture,
            EmailParameters parameters,
            EmailDetail detail,
            IList<RawAttachment> attachments,
            CancellationToken cancellationToken)
        {
            var employee = await this.employeeService.GetEmployeeNameAsync(employeeId);
            parameters.TryAdd("Name", employee.FirstName);

            var tenant = await this.tenantService.GetTenantForEmployeeAsync(employeeId);

            context.WriteLine("Host: {0}", tenant.Host);
            context.WriteLine("Email: {0}", employee.Email);

            if (string.IsNullOrWhiteSpace(employee.Email))
            {
                return;
            }

            parameters.TryAdd("Agency", tenant.AgencyName);
            parameters.TryAdd("AgencyLogo", tenant.AgencyLogoUri?.ToString());
            parameters.TryAdd("SignatureUrl", "https://" + tenant.Host);
            parameters.TryAdd("Url", string.Empty);

            await this.PostAsync(context, name, await this.GetFromAsync(tenant.AgencyName, tenant.Host), employee.Email, new CultureInfo(culture), parameters, detail, attachments, cancellationToken);
        }

        [DisplayName("Email: {1} - {2}")]
        public async Task EmailAsync(
            PerformContext context,
            string name,
            long? agencyId,
            string email,
            string culture,
            EmailParameters parameters,
            EmailDetail detail,
            IList<RawAttachment> attachments,
            CancellationToken cancellationToken)
        {
            email = email?.Trim();

            var tenant = await this.tenantService.GetTenantForEmailAsync(email, agencyId);

            var employee = await this.employeeService.GetEmployeeNameAsync(email);
            if (employee != null)
            {
                parameters.TryAdd("Name", employee.FirstName);
            }
            else
            {
                var user = await this.userService.GetUserNameByEmailAsync(email);
                if (user != null)
                {
                    parameters.TryAdd("Name", user.FirstName);
                }
            }

            context.WriteLine("Host: {0}", tenant.Host);
            context.WriteLine("Email: {0}", email);

            parameters.TryAdd("Agency", tenant.AgencyName);
            parameters.TryAdd("AgencyLogo", tenant.AgencyLogoUri?.ToString());
            parameters.TryAdd("SignatureUrl", "https://" + tenant.Host);
            parameters.TryAdd("Url", string.Empty);

            await this.PostAsync(context, name, await this.GetFromAsync(tenant.AgencyName, tenant.Host), email, new CultureInfo(culture), parameters, detail, attachments, cancellationToken);
        }

        private async Task<string> GetFromAsync(string agencyName, HostString host)
        {
            if (this.env.IsDevelopment() && host.Host == "localhost")
            {
                return $"{agencyName} <donotreply@localhost>";
            }

            if (this.ruleProvider.GetDomainDataStructure() == null)
            {
                await this.ruleProvider.BuildAsync();
            }

            var domainParser = new DomainParser(this.ruleProvider);
            var domainInfo = domainParser.Parse(host.Value);

            return $"{agencyName} <donotreply@{domainInfo.RegistrableDomain}>";
        }

        private IDictionary<string, string> GetParams(string name, CultureInfo culture, IDictionary<string, string> parameters)
        {
            // Get parameters where they have not been supplied locally.
            var @params = new Dictionary<string, string>(parameters);

            foreach (DictionaryEntry item in EmailResourceManager.GetResourceSet("SHARED", culture))
            {
                if (!parameters.Keys.Contains(item.Key))
                {
                    @params.Add(Convert.ToString(item.Key), Convert.ToString(item.Value));
                }
            }

            try
            {
                foreach (DictionaryEntry item in EmailResourceManager.GetResourceSet(name, culture))
                {
                    if (!parameters.Keys.Contains(item.Key))
                    {
                        @params.Add(Convert.ToString(item.Key), Convert.ToString(item.Value));
                    }
                }
            }
            catch (MissingManifestResourceException)
            {
                // Do nothing.
            }

            // There may be shared translations but not for the specified culture. Each email template should have the subject translated.
            if (@params.Count == 0 || !@params.ContainsKey("Subject"))
            {
                // If no translation exists; fallback to English.
                return this.GetParams(name, CultureData.DefaultCulture, parameters);
            }

            return @params;
        }

        private static DataTable GetDataTable(IEnumerable<KeyValuePair<string, string>> parameters)
        {
            var vars = new DataTable("parameters");
            vars.Columns.AddRange(parameters.Select(_ => new DataColumn(_.Key)).ToArray());
            vars.Rows.Add(parameters.Select(_ => _.Value).ToArray());

            return vars;
        }

        private static DataTable GetDetailDataTable(EmailDetail detail, string templateName)
        {
            var det = new DataTable("detail");
            if (detail.Count > 0)
            {
                var type = detail[0].GetType();
                var properties = type.GetProperties().Select(_ => new DataColumn(_.Name)).ToArray();
                det.Columns.AddRange(properties);

                foreach (var row in detail)
                {
                    var datarow = det.NewRow();
                    foreach (var property in properties)
                    {
                        datarow[property] = type.GetProperty(property.ColumnName).GetValue(row);
                    }

                    det.Rows.Add(datarow);
                }
            }

            if (templateName == EmailTemplates.LeaveNotification)
            {
                return GetDetailDataTableNotification(det);
            }

            return det;
        }

        private static DataTable GetDetailDataTableNotification(DataTable det)
        {
            // Transpose it into a one - row table
            var transposed = new DataTable("detail");

            // Step 1: Add 4 columns to the transposed table using the first 4 entries from det
            for (var i = 0; i < 4 && i < det.Rows.Count; i++)
            {
                transposed.Columns.Add(det.Rows[i]["Name"].ToString());
            }

            // Step 2: Calculate total number of rows needed
            var totalRows = (int)Math.Ceiling((double)det.Rows.Count / transposed.Columns.Count);

            // Step 3: Fill rows with values from det
            for (var rowIndex = 0; rowIndex < totalRows; rowIndex++)
            {
                var newRow = transposed.NewRow();
                for (var colIndex = 0; colIndex < transposed.Columns.Count; colIndex++)
                {
                    var detIndex = (rowIndex * transposed.Columns.Count) + colIndex;
                    if (detIndex < det.Rows.Count)
                    {
                        newRow[colIndex] = det.Rows[detIndex]["Value"];
                    }
                }

                transposed.Rows.Add(newRow);
            }

            return transposed;
        }

        private async Task PostAsync(
            PerformContext context,
            string templateName,
            string from,
            string email,
            CultureInfo culture,
            EmailParameters parameters,
            EmailDetail detail,
            IList<RawAttachment> attachments,
            CancellationToken cancellationToken)
        {
            if (!TemplateMapping.ContainsKey(templateName))
            {
                throw new InvalidOperationException("Template could not be found for '" + templateName + "'. Templates available: " + string.Join(",", TemplateMapping.Keys));
            }

            var canSendOnAllEnvironments = AllowAllEnvironmentsTemplates.Any(template => template.Equals(templateName, StringComparison.CurrentCultureIgnoreCase));

            // Do not send the email, if the email address status is flagged as invalid.
            var (canSendEmail, errorMessage) = await this.emailAddressService.CanSendEmail(email, canSendOnAllEnvironments);
            if (!canSendEmail)
            {
                context.WriteLine(errorMessage);
                return;
            }

            var @params = this.GetParams(templateName, culture, parameters);

            using (var ds = new DataSet())
            {
                ds.Tables.Add(GetDataTable(@params));
                ds.Tables.Add(GetDetailDataTable(detail, templateName));

                await this.reportClient.SendEmailAsync(
                    TemplateMapping[templateName],
                    from,
                    email,
                    Smart.Format(@params["Subject"], parameters),
                    ds,
                    attachments,
                    cancellationToken);
            }
        }
    }
}