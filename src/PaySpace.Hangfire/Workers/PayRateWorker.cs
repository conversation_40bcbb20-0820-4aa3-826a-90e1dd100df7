namespace PaySpace.Hangfire.Workers
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using global::Hangfire;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.Extensions.Localization;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Logging.Abstractions;
    using PaySpace.Venuta.Modules.PayRate.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.External;

    [Queue("default")]
    public class PayRateWorker
    {
        private readonly IStringLocalizer localizer;
        private readonly ICompanyService companyService;
        private readonly IPayRateService payRateService;
        private readonly ApplicationContext context;
        private readonly ICalcSchedulingService calcSchedulingService;
        private readonly IAuditPublisherService auditPublisherService;

        public PayRateWorker(
            IStringLocalizerFactory stringLocalizerFactory,
            ICompanyService companyService,
            IPayRateService payRateService,
            ApplicationContext context,
            ICalcSchedulingService calcSchedulingService,
            IAuditPublisherService auditPublisherService)
        {
            this.localizer = stringLocalizerFactory.Create(typeof(OrganizationCategory));
            this.companyService = companyService;
            this.payRateService = payRateService;
            this.context = context;
            this.calcSchedulingService = calcSchedulingService;
            this.auditPublisherService = auditPublisherService;
        }

        [AutomaticRetry(Attempts = 0)]
        [DisableConcurrentExecution(60 * 60 * 24)]
        public async Task UpdateEmployeePayRateCategoriesAsync(ProcessPayRateMessage message, CancellationToken cancellationToken)
        {
            var detail = new PayRateCategoryDetails(message.CategoryCode, message.Description, message.EffectiveDate, message.Package, message.HoursPerDay, message.DaysPerPeriod, message.IncreaseReason);
            var result = await this.payRateService.GetPayRatesToAddAsync(message.CompanyId, detail);

            this.context.AddRange(result.PayRates);

            // Get all the records to be audited.
            var changes = this.auditPublisherService.RecordChanges((DbContext)this.context);

            await this.context.SaveChangesAsync(cancellationToken: cancellationToken);

            // Only new pay rate entries will be audited.
            await this.auditPublisherService.PublishAuditAsync(changes, message.UserId, DateTime.Now, cancellationToken);

            foreach (var payRate in result.PayRates)
            {
                await this.calcSchedulingService.RecalculatePayslip(payRate.EmployeeId, CalcSource.PayRates);
            }

            await this.SendPayRateNotificationEmailAsync(
                message.UserId,
                message.CompanyId,
                message.Description,
                result.TotalEmployees - result.NotUpdated.Count,
                result.NotUpdated);
        }

        private async Task SendPayRateNotificationEmailAsync(long userId, long companyId, string description, int totalEmployeePayRatesUpdated, List<(string Number, string FullName)> notUpdated)
        {
            var companyName = await this.companyService.GetCompanyNameAsync(companyId);

            var parameters = new EmailParameters();
            parameters.TryAdd("PayRateDescription", description);
            parameters.TryAdd("CompanyName", companyName);

            var detail = new EmailDetail
            {
                new EmailDetailValue("Company", companyName),
                new EmailDetailValue("Employee Pay Rate Records Created Count", Convert.ToString(totalEmployeePayRatesUpdated)),
                new EmailDetailValue("Employee Pay Rate Records NOT Created Count", Convert.ToString(notUpdated.Count))
            };

            if (notUpdated.Count > 0)
            {
                detail.Add(new EmailDetailValue(this.localizer.GetString("Employee Pay Rate Records NOT Created"), string.Join("; ", notUpdated.Select(_ => $"{_.Number} - {_.FullName}"))));
            }

            BackgroundJob.Enqueue<EmailWorker>(_ => _.EmailAsync(
                null,
                EmailTemplates.PayRateNotification,
                userId,
                parameters,
                detail,
                null,
                CancellationToken.None));
        }
    }
}