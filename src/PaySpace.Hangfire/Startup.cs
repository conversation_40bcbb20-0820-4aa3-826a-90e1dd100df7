namespace PaySpace.Hangfire
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;

    using Elasticsearch.Net;

    using global::Hangfire;
    using global::Hangfire.Common;
    using global::Hangfire.Console;
    using global::Hangfire.MAMQSqlExtension;
    using global::Hangfire.SqlServer;
    using global::Hangfire.Throttling;

    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Authentication.OpenIdConnect;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Routing;
    using Microsoft.Data.SqlClient;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Diagnostics;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;

    using Nager.PublicSuffix.RuleProviders;
    using Nager.PublicSuffix.RuleProviders.CacheProviders;

    using Nest;

    using Newtonsoft.Json;

    using PaySpace.Cache.Distributed;
    using PaySpace.Hangfire.Abstractions;
    using PaySpace.Hangfire.Abstractions.Filters.Providers;
    using PaySpace.Hangfire.Abstractions.Messages;
    using PaySpace.Hangfire.Filters;
    using PaySpace.Hangfire.MessageHandlers;
    using PaySpace.Hangfire.Services;
    using PaySpace.Hangfire.Workers;
    using PaySpace.Integrations.Acumatica;
    using PaySpace.Integrations.QuickBooks;
    using PaySpace.Integrations.Webhooks;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Mappers;
    using PaySpace.Venuta.Excel.Components;
    using PaySpace.Venuta.Excel.Components.Downloads;
    using PaySpace.Venuta.Excel.Employees;
    using PaySpace.Venuta.Excel.Services;
    using PaySpace.Venuta.Localization;
    using PaySpace.Venuta.Logging;
    using PaySpace.Venuta.Lookups;
    using PaySpace.Venuta.Messaging;
    using PaySpace.Venuta.Messaging.Users;
    using PaySpace.Venuta.Modules.CompanySettings;
    using PaySpace.Venuta.Modules.CompanyShiftPatterns;
    using PaySpace.Venuta.Modules.Components;
    using PaySpace.Venuta.Modules.Components.Employee;
    using PaySpace.Venuta.Modules.CustomForms;
    using PaySpace.Venuta.Modules.Dashboard.Excel.Services;
    using PaySpace.Venuta.Modules.DynamicFormBuilder;
    using PaySpace.Venuta.Modules.Employee.Claims;
    using PaySpace.Venuta.Modules.Employee.Inbox;
    using PaySpace.Venuta.Modules.Employee.Positions;
    using PaySpace.Venuta.Modules.Employee.SuspensionSnapshot;
    using PaySpace.Venuta.Modules.Employee.SuspensionSnapshot.Abstractions.Messages;
    using PaySpace.Venuta.Modules.EmploymentStability;
    using PaySpace.Venuta.Modules.EmploymentStability.Abstractions.Messages;
    using PaySpace.Venuta.Modules.GeneralLedger;
    using PaySpace.Venuta.Modules.Leave;
    using PaySpace.Venuta.Modules.Organization;
    using PaySpace.Venuta.Modules.OrgChart;
    using PaySpace.Venuta.Modules.PayRate;
    using PaySpace.Venuta.Modules.Payslips;
    using PaySpace.Venuta.Modules.PensionEnrolment;
    using PaySpace.Venuta.Modules.UserOrgPermissions;
    using PaySpace.Venuta.Search;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Security.Identity;
    using PaySpace.Venuta.Serialization;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.External;
    using PaySpace.Venuta.Storage;
    using PaySpace.Venuta.Telemetry;
    using PaySpace.Venuta.Workflow.Services;

    public class Startup
    {
        private readonly IWebHostEnvironment env;

        public Startup(IConfiguration configuration, IWebHostEnvironment env)
        {
            this.Configuration = configuration;
            this.env = env;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddApplicationInsightsTelemetryWorkerService();
            services.AddApplicationInsightsTelemetryProcessor<SuppressTelemetryFilter>();
            services.AddSingleton<ITelemetryInitializer>(new CloudRoleNameTelemetryInitializer("Hangfire"));

            services.AddSingleton<LinkGenerator, DefaultLinkGenerator>();

            // Add config.
            var elasticSettings = this.Configuration.GetSection("ElasticSearch").Get<ElasticSettings>();
            var pool = new StaticConnectionPool(elasticSettings.Urls);
            services.AddSingleton<IElasticClient>(new ElasticClient(new ConnectionSettings(pool)));

            var clientSettings = this.Configuration.GetSection("ClientSettings").Get<ClientSettings>();
            services.AddSingleton(Options.Create(clientSettings));
            services.Configure<EmailSettings>(this.Configuration.GetSection("EmailSettings"));
            services.Configure<IdentitySettings>(this.Configuration.GetSection("Identity"));

            services.AddAmazonNotificationClient(this.Configuration.GetSection("AmazonSettings"));

            // Add framework services.
            services.AddMemoryCache();
            services.AddLocalization();
            services.AddMvcCore()
                .AddDataAnnotationsLocalization();

            // Security.
            services.AddSingleton<TokenEndpointService>();

            services.AddAuthentication()
                .AddOpenIdConnect(OpenIdConnectDefaults.AuthenticationScheme, options =>
                {
                    var identitySettings = this.Configuration.GetSection("Identity").Get<IdentitySettings>();

                    options.Authority = identitySettings.Authority;
                    options.ClientId = identitySettings.ClientId;
                    options.ClientSecret = identitySettings.ClientSecret;
                });

            // Add application services.
            var connectionStringBuilder = new SqlConnectionStringBuilder
            {
                ConnectionString = this.Configuration.GetConnectionString("DefaultConnection"),
                ApplicationName = "Hangfire"
            };

            services.AddDefaultDataServices(connectionStringBuilder.ToString());
            services.AddSecurityCore(this.Configuration, applicationName: "Hangfire");
            services.AddLoggingServices(connectionStringBuilder.ToString())
                .AddPublishers()
                .AddMessageHandlers();

            services.AddApplicationServices();
            services.AddLocalizationServices()
               .AddLocalizedDisplay();

            services.AddExternalServices(this.Configuration);

            // Add infrastructure services.
            services.AddDistributedCache(this.Configuration.GetSection("RedisSettings"));
            services.AddStorage(this.Configuration, this.env.IsDevelopment());
            services.AddMessageBus(this.Configuration, this.env.IsDevelopment())
                    .AddRegionMessageBus(this.Configuration, this.env.IsDevelopment());

            // Excel notification Service
            services.AddSingleton<IExcelMessageService, ExcelMessageService>();
            services.AddScoped<IDashboardDownloadService, DashboardDownloadService>();
            services.AddScoped<ICopyConfigurationDownloadService, CopyConfigurationDownloadService>();

            // Message handlers.
            services.AddMessageHandler<CompanyDashboardMessageHandler>();
            services.AddMessageHandler<AttachmentsMessageHandler>();
            services.AddMessageHandler<SmsMessageHandler>();
            services.AddMessageHandler<EmailMessageHandler>();
            services.AddMessageHandler<EmployeeDependantQuickAddMessageHandler>();
            services.AddMessageHandler<ExportPayslipMessageHandler>();
            services.AddMessageHandler<ExportCompletedClaimsMessageHandler>();
            services.AddMessageHandler<ExportTaxBreakdownMessageHandler>();
            services.AddMessageHandler<BulkUploadMessageHandler>();
            services.AddMessageHandler<BudgetArchiveEmployeeExtractionHandler>();
            services.AddMessageHandler<ComponentExtractionHandler>();
            services.AddMessageHandler<ClearAgencyThemeCacheMessageHandler>();
            services.AddMessageHandler<ClearCompanyCacheIndexMessageHandler>();
            services.AddMessageHandler<ClearCompanyCacheThumbnailMessageHandler>();
            services.AddMessageHandler<ClearCompanyThemeCacheMessageHandler>();
            services.AddMessageHandler<ClearEmployeeCacheIndexMessageHandler>();
            services.AddMessageHandler<ClearEmployeeCacheThumbnailMessageHandler>();
            services.AddMessageHandler<CreateEmployeeProfileMessageHandler>();
            services.AddMessageHandler<CreateEmployeeProfileThumbnailMessageHandler>();
            services.AddMessageHandler<UpdateEmployeeProfileIndexMessageHandler>();
            services.AddMessageHandler<UpdateEmployeeProfileThumbnailMessageHandler>();
            services.AddMessageHandler<DeleteEmployeeProfileMessageHandler>();
            services.AddMessageHandler<ProcessGLDataMessageHandler>();
            services.AddMessageHandler<ProcessGLDataCompleteMessageHandler>();
            services.AddMessageHandler<ProcessPayRateMessageHandler>();
            services.AddMessageHandler<EmployeePositionSubordinateUpdateMessageHandler>();
            services.AddMessageHandler<OrgChartExtractionHandler>();
            services.AddMessageHandler<UpdateCompanyBankDetailMessageHandler>();
            services.AddMessageHandler<UpdateAdvancedSettingsScreenMessageHandler>();
            services.AddMessageHandler<UpdateAdvancedSettingsOptionCodeMessageHandler>();
            services.AddMessageHandler<UpdateCustomFieldLookupParentCodeMessageHandler>();
            services.AddMessageHandler<UpdateCustomFieldLookupValuesMessageHandler>();
            services.AddMessageHandler<UpdateCustomFieldValuesMessageHandler>();
            services.AddMessageHandler<UpdateEmployeePositionOrgUnitMessageHandler>();
            services.AddMessageHandler<TrainingCompanyMessageHandler>();
            services.AddMessageHandler<CopyConfigurationMessageHandler>();
            services.AddMessageHandler<StopHangfireRequestMessageHandler>();

            services.AddMessageHandler<UpsertEmployeeStabilityRulesMessageHandler<EmployeeSuspensionStabilityUpsertMessage>>();
            services.AddMessageHandler<UpsertEmployeeStabilityRulesMessageHandler<EmployeeLeaveStabilityUpsertMessage>>();
            services.AddMessageHandler<UpsertEmployeeStabilityRulesMessageHandler<EmployeePositionStabilityUpsertMessage>>();

            services.AddMessageHandler<UpsertEmployeeSuspensionSnapshotMessageHandler<EmployeeSuspensionSnapshotUpsertMessage>>();

            services.AddMessageHandler<EmailPensionLetterMessageHandler>();
            services.AddMessageHandler<DownloadPensionLetterMessageHandler>();

            services.AddMessageHandler<UpdateCompanyGroupMessageHandler>();

            services.AddMessageHandler<CalcNotificationsHandler>();

            if (this.env.IsDevelopment())
            {
                services.AddLogging(loggingBuilder =>
                {
                    loggingBuilder.AddSeq();
                });
            }

            // Local services.
            services.AddTransient<IUserMessageWorker, UserMessageWorker>();

            services.AddTransient<IIndexClient, IndexClient>();
            services.AddTransient<ISmsService, AmazonSmsService>();
            services.AddTransient<IAmazonSesService, AmazonSesService>();
            services.AddTransient<IFileExportMessageService, FileExportMessageService>();
            services.AddTransient<IIntegrationsMessageService, IntegrationsMessageService>();
            services.AddTransient<IAuditDeadLetterService, AuditDeadLetterService>();
            services.AddTransient<IUserWorkflowStepService, UserWorkflowStepService>();
            services.AddTransient<IEmployeeStatutoryInformationService, EmployeeStatutoryInformationService>();
            services.AddTransient<IProbationService, ProbationService>();

            if (this.env.IsDevelopment())
            {
                services.AddTransient<IEmailClient, DevelopmentEmailClient>();
            }
            else
            {
                services.AddTransient<IEmailClient, EmailClient>();
            }

            // Modules.
            services.AddComponentModules();
            services.AddComponentExtractionServices();
            services.AddDataExtractionServices();
            services.AddPayslipModules();
            services.AddLeaveModules();
            services.AddPayRateModules();
            services.AddEmployeePositionsModules();
            services.AddGeneralLedgerModules();
            services.AddAcumaticaIntegration();
            services.AddQuickBooksIntegration();
            services.AddXeroIntegration();
            services.AddClaimModules();
            services.AddComponentServices();
            services.AddLookupServices();
            services.AddOrgChartServices();
            services.AddWebhookErrorServices();
            services.AddCompanySettingsModules();
            services.AddStabilityModule();
            services.AddSuspensionSnapshotModule();
            services.AddOrganizationModules();
            services.AddInboxModules();
            services.AddPensionEnrolmentModules();
            services.AddCustomFormModules();
            services.AddDtoMaps();
            services.AddUserOrgPermissionsModules();
            services.AddUserUpdateMessaging();
            services.AddDynamicFormBuilderModuleServices(this.Configuration);
            services.AddShiftPatternModules();

            services.AddSingleton<ISerializerSettingsFactory, SerializerSettingsFactory>();

            // Hangfire.
            services.AddHangfire(options => options
                .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
                .UseFilter(new PreserveQueueFilter())
                .UseSerializerSettings(new JsonSerializerSettings { TypeNameHandling = TypeNameHandling.All })
                .UseSimpleAssemblyNameTypeSerializer()
                .UseMAMQSqlServerStorage(
                    this.Configuration.GetConnectionString("HangfireConnection"),
                    new SqlServerStorageOptions
                    {
                        UsePageLocksOnDequeue = true,
                        DisableGlobalLocks = true
                    },
                    this.GetQueues())
                        .WithJobExpirationTimeout(TimeSpan.FromDays(14))
                .UseBatches()
                .UseConsole()
                .UseThrottling(ThrottlingAction.RetryJob, TimeSpan.FromSeconds(15))); // Min retry timespan is 15s

            services.AddDbContext<HangfireContext>(options => options
                .UseSqlServer(connectionStringBuilder.ToString(), sql => sql.EnableRetryOnFailure())
                .ConfigureWarnings(warnings =>
                {
                    warnings.Ignore(CoreEventId.ContextInitialized);
                    warnings.Ignore(CoreEventId.ContextDisposed);
                }));

            services.AddSingleton<TypeJobFilterProvider>();

            services.AddSingleton<IBulkUploadServiceContainer, BulkUploadServiceContainer>();

            services.AddAutoMapper();

            services.AddHttpClient(); //Required for CachedHttpRuleProvider
            services.AddSingleton<ICacheProvider, LocalFileSystemCacheProvider>();
            services.AddSingleton<IRuleProvider, CachedHttpRuleProvider>();

            services.AddRouting();
        }

        public void Configure(IApplicationBuilder app, TypeJobFilterProvider filterProvider, TelemetryConfiguration telemetryConfiguration)
        {
            if (this.env.IsDevelopment())
            {
                // Disable Application Insights Telemetry in output window.
                telemetryConfiguration.DisableTelemetry = true;
            }

            // services.AddHangfireServer does not listen to app shutdowns.
            // Adding UseHangfireServer here ensure that the Hangfire job service is shutdown properly.
            app.UseHangfireServer(new BackgroundJobServerOptions
            {
                Queues = this.GetQueues(),
                ShutdownTimeout = TimeSpan.FromMinutes(10),
                ServerName = this.Configuration.GetValue<string>("Hangfire:InstanceName")
            });

            app.UseRouting();
            app.UseEndpoints(
                endpoints =>
                {
                    endpoints.MapHangfireDashboard(
                        string.Empty,
                        new DashboardOptions
                        {
                            AppPath = null,
                            Authorization = new[] { new AnonymousAuthorizationFilter() }
                        });

                    endpoints.MapGet("/ProcessingCount", this.GetJobCount);
                    endpoints.MapGet("/Stop", this.StopServiceBus);

                    endpoints.MapFallback(context =>
                    {
                        context.Response.StatusCode = 404;
                        return context.Response.WriteAsync("");
                    });
                });

            JobFilterProviders.Providers.Add(filterProvider);
        }

        private async Task StopServiceBus(HttpContext context)
        {
            var monitoringApi = JobStorage.Current.GetMonitoringApi();
            var instanceName = context.Request.Query["instance"];
            var messageBus = context.RequestServices.GetRequiredService<IMessageBus>();
            var servers = monitoringApi.Servers().Where(_ => _.Name.StartsWith(instanceName, StringComparison.InvariantCultureIgnoreCase));

            await messageBus.PublishMessageAsync(new StopHangfireRequestMessage
            {
                Servers = servers.Select(_ => _.Name).ToArray()
            });

            await context.Response.WriteAsJsonAsync(new { status = "complete" });
        }

        private async Task GetJobCount(HttpContext context)
        {
            var instanceName = context.Request.Query["instance"];
            var monitoringApi = JobStorage.Current.GetMonitoringApi();

            if (string.IsNullOrEmpty(instanceName))
            {
                var count = monitoringApi.ProcessingCount();
                await context.Response.WriteAsJsonAsync(new { count });
                return;
            }

            var servers = monitoringApi.Servers().Where(_ => _.Name.StartsWith(instanceName, StringComparison.InvariantCultureIgnoreCase));
            var jobCount = monitoringApi.ProcessingJobs(0, 1000).Count(j => servers.Any(s => s.Name == j.Value.ServerId));

            await context.Response.WriteAsJsonAsync(new { count = jobCount });
        }

        private string[] GetQueues()
        {
            return this.Configuration.GetSection("Hangfire:Queues").Get<string[]>();
        }
    }
}