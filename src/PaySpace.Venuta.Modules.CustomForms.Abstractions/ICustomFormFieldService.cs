namespace PaySpace.Venuta.Modules.CustomForms.Abstractions
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;

    public interface ICustomFormFieldService
    {
        List<CustomFieldFormField> GetCustomFormFields(long companyId, long categoryId, string customFieldType);

        Task<List<CustomFieldFormField>> GetCustomFormFieldsAsync(long companyId, long categoryId, string customFieldType);

        Task<IQueryable<CustomFieldOption>> GetFieldOptionsAsync(long companyId, string fieldCode, string categoryCode);

        Task<IQueryable<CustomFieldOption>> GetFieldOptionsAsync(long companyId, string fieldCode, string categoryCode, string parentValue, bool filterParentByCode);

        Task<IQueryable<CustomFieldOption>> GetFieldOptionByCodeAsync(long companyId, string fieldCode, string categoryCode, string code);

        Task<IQueryable<CustomFieldOption>> GetFieldOptionByCodeAsync(long companyId, string fieldCode, string categoryCode, string parentValue, bool filterParentByCode, string code);

        Task<long> GetCustomFormIdForCollectionCode(long companyId, string customFieldType, string categoryCode);
    }
}
