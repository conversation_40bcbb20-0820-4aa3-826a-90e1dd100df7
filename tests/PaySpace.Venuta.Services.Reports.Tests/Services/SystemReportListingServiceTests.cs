namespace PaySpace.Venuta.Services.Reports.Tests.Services
{
    using System.Collections.Generic;

    using Microsoft.AspNetCore.Http;

    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports.Enums;
    using PaySpace.Venuta.Services.Reports.Models.ContextLevelReportSources;
    using PaySpace.Venuta.Services.Reports.Services;
    using PaySpace.Venuta.TestHarness.Reports.Builders.Services;
    using PaySpace.Venuta.TestHarness.Shared;
    using PaySpace.Venuta.TestHarness.Shared.Builders.Abstractions;
    using PaySpace.Venuta.TestHarness.Shared.RandomHelpers;

    using Shouldly;

    using Xunit;

    public class SystemReportListingServiceTests
    {
        private static readonly string TaxCountryFolderName = "TaxCountry";
        public class TaxBreakdownSystemReport
        {
            [Fact]
            public async Task GivenTaxBreakdownReportExistsOnTaxCountryLevel_ShouldReturnTaxCountryReport()
            {
                // Arrange
                var hostString = RandomWeb.HostString();
                var companyId = RandomModel.Id.Long();
                var frequencyId = RandomModel.Id.Long();
                var reportId = ReportConstants.SystemReports.TaxBreakdown;
                var bureauPath = ReportContextLevel.Bureau.ToString();

                var companySummary = CompanySummaryTestBuilder.CreateWithRandomProps().WithCompanyId(companyId).Build();
                var companyService = CompanyServiceStubBuilder.Create().WithCompanySummary(companyId, companySummary).Build();
                var taxCountryPath = BureauTaxCountryReportSource.CreateForRootFolder(string.Empty, companySummary.TaxCountryId).ReportPath;

                var reportStorageService = ReportStorageServiceStubBuilder.Create()
                    .WithReportsInDirectory(bureauPath, new List<string> { Path.Combine(bureauPath, reportId) })
                    .WithReportsInDirectory(taxCountryPath, new List<string> { Path.Combine(taxCountryPath, reportId) })
                    .Build();

                var sut = CreateSutFixtureBuilderWithEssentialMocking(companyId, hostString)
                    .WithCompanySummaryService(companyService)
                    .WithReportStorageService(reportStorageService)
                    .Build();

                // Act
                var results = await sut.GetSystemReportsAsync(hostString, companyId, frequencyId);

                // Assert
                results.ShouldNotBeEmpty();
                results.Count.ShouldBe(1);
                results.First().ReportSource.ReportPath.ShouldContain(TaxCountryFolderName);
            }

            [Fact]
            public async Task GivenTaxBreakdownReportDoesNotExistsOnTaxCountryLevel_ShouldReturnEmptyList()
            {
                // Arrange
                var hostString = RandomWeb.HostString();
                var companyId = RandomModel.Id.Long();
                var frequencyId = RandomModel.Id.Long();
                var reportId = ReportConstants.SystemReports.TaxBreakdown;
                var bureauPath = ReportContextLevel.Bureau.ToString();

                var reportStorageService = ReportStorageServiceStubBuilder.Create()
                    .WithReportsInDirectory(bureauPath, new List<string> { Path.Combine(bureauPath, reportId) })
                    .Build();

                var sut = CreateSutFixtureBuilderWithEssentialMocking(companyId, hostString)
                    .WithReportStorageService(reportStorageService)
                    .Build();

                // Act
                var results = await sut.GetSystemReportsAsync(hostString, companyId, frequencyId);

                // Assert
                results.ShouldBeEmpty();
                results.Count.ShouldBe(0);
            }
        }

        [Fact]
        public async Task GivenNoSystemReportsListingsAvailable_ShouldReturnEmptyList()
        {
            // Arrange
            var hostString = RandomWeb.HostString();
            var companyId = RandomModel.Id.Long();
            var frequencyId = RandomModel.Id.Long();
            var bureauPath = ReportContextLevel.Bureau.ToString();

            var reportStorageService = ReportStorageServiceStubBuilder.Create()
                .WithReportsInDirectory(bureauPath, null)
                .Build();

            var sut = CreateSutFixtureBuilderWithEssentialMocking(companyId, hostString)
                .WithReportStorageService(reportStorageService)
                .Build();

            // Act
            var results = await sut.GetSystemReportsAsync(hostString, companyId, frequencyId);

            // Assert
            results.AsEnumerable().ShouldBeEmpty();
        }

        [Fact]
        public async Task GivenStorageHasListedNonSystemReports_ShouldReturnReportsDefinedInCodeAsSystem()
        {
            // Arrange
            var hostString = RandomWeb.HostString();
            var companyId = RandomModel.Id.Long();
            var frequencyId = RandomModel.Id.Long();
            var sysReportId = ReportConstants.SystemReports.TaxBreakdown;
            var nonSysReportID = RandomModel.Id.String();

            var companySummary = CompanySummaryTestBuilder.CreateWithRandomProps().WithCompanyId(companyId).Build();
            var companyService = CompanyServiceStubBuilder.Create().WithCompanySummary(companyId, companySummary).Build();
            var taxCountryPath = BureauTaxCountryReportSource.CreateForRootFolder(string.Empty, companySummary.TaxCountryId).ReportPath;

            var reportStorageService = ReportStorageServiceStubBuilder.Create()
                .WithReportsInDirectory(taxCountryPath, new List<string> { sysReportId, nonSysReportID })
                .Build();

            var sut = CreateSutFixtureBuilderWithEssentialMocking(companyId, hostString)
                .WithCompanySummaryService(companyService)
                .WithReportStorageService(reportStorageService)
                .Build();

            // Act
            var results = await sut.GetSystemReportsAsync(hostString, companyId, frequencyId);

            // Assert
            results.ShouldNotBeEmpty();
            results.Count.ShouldBe(1);
            results.Single().ReportId.ShouldBe(sysReportId);
        }

        [Fact]
        public async Task GivenSystemReportsListingsAvailable_ShouldReturnSystemReportCategoryTags()
        {
            // Arrange
            var hostString = RandomWeb.HostString();
            var companyId = RandomModel.Id.Long();
            var frequencyId = RandomModel.Id.Long();
            var sysReportId = ReportConstants.SystemReports.TaxBreakdown;

            var companySummary = CompanySummaryTestBuilder.CreateWithRandomProps().WithCompanyId(companyId).Build();
            var companyService = CompanyServiceStubBuilder.Create().WithCompanySummary(companyId, companySummary).Build();
            var taxCountryPath = BureauTaxCountryReportSource.CreateForRootFolder(string.Empty, companySummary.TaxCountryId).ReportPath;

            var reportStorageService = ReportStorageServiceStubBuilder.Create()
                .WithReportsInDirectory(taxCountryPath, new List<string> { sysReportId })
                .Build();

            var sut = CreateSutFixtureBuilderWithEssentialMocking(companyId, hostString)
                .WithCompanySummaryService(companyService)
                .WithReportStorageService(reportStorageService)
                .Build();

            // Act
            var results = await sut.GetSystemReportsAsync(hostString, companyId, frequencyId);

            // Assert
            results.ShouldNotBeEmpty();
            results.Count.ShouldBe(1);
            var report = results.Single();
            report.ReportId.ShouldBe(sysReportId);
            report.ReportCategoryId.ShouldBe(ReportConstants.SystemReportsCategoryId);
        }

        private static SutFixtureBuilder CreateSutFixtureBuilderWithEssentialMocking(long companyId, HostString hostString)
        {
            var companySummary = CompanySummaryTestBuilder.CreateWithRandomProps().WithCompanyId(companyId).Build();
            var companyService = CompanyServiceStubBuilder.Create().WithCompanySummary(companyId, companySummary).Build();
            var reportUrlResolverService = ReportUrlResolverStubBuilder.Create().WithResolve(hostString).Build();
            return SutFixtureBuilder.Create()
                .WithCompanySummaryService(companyService)
                .WithReportUrlResolver(reportUrlResolverService);
        }

        private class SutFixtureBuilder : ITestBuilder<SystemReportListingService>
        {
            private IReportStorageService reportStorageService = ReportStorageServiceStubBuilder.Create().Build();
            private IReportUrlResolver reportUrlResolver = ReportUrlResolverStubBuilder.Create().Build();
            private ICompanyService companyService = CompanyServiceStubBuilder.Create().Build();

            public SystemReportListingService Build()
            {
                return new SystemReportListingService(this.reportStorageService, this.reportUrlResolver, this.companyService);
            }

            public static SutFixtureBuilder Create() => new();

            public SutFixtureBuilder WithReportStorageService(IReportStorageService service)
            {
                this.reportStorageService = service;
                return this;
            }

            public SutFixtureBuilder WithCompanySummaryService(ICompanyService service)
            {
                this.companyService = service;
                return this;
            }

            public SutFixtureBuilder WithReportUrlResolver(IReportUrlResolver resolver)
            {
                this.reportUrlResolver = resolver;
                return this;
            }
        }
    }
}
