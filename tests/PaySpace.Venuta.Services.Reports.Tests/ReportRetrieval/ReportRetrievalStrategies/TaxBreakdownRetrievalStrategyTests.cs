namespace PaySpace.Venuta.Services.Reports.Tests.ReportRetrieval.ReportRetrievalStrategies
{
    using System.Globalization;

    using NSubstitute;

    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Reports.ReportRetrieval.ReportRetrievalStrategies.TaxBreakdown;
    using PaySpace.Venuta.Services.Reports.Services;
    using PaySpace.Venuta.TestHarness.Reports.Builders.DevExpressReports;
    using PaySpace.Venuta.TestHarness.Reports.Builders.Models;
    using PaySpace.Venuta.TestHarness.Reports.Builders.Security;
    using PaySpace.Venuta.TestHarness.Reports.Builders.Services;
    using PaySpace.Venuta.TestHarness.Shared;
    using PaySpace.Venuta.TestHarness.Shared.Builders.Abstractions;
    using PaySpace.Venuta.TestHarness.Shared.RandomHelpers;

    using Shouldly;

    using Xunit;

    public class TaxBreakdownRetrievalStrategyTests
    {
        private const string ParentFolderPath = "";
        private const string ReportId = "TaxBreakdown";

        public class GetReportInfoForGenerationAsync
        {
            [Fact]
            public async Task GivenReportDetails_ShouldReturnReportInfo()
            {
                // Arrange
                var companyId = RandomModel.Id.Long();
                var reportPath = ReportPathTestBuilder.CreateWithRandomProps().WithReportId(ReportId).WithCompanyId(companyId).Build();
                var accessToken = RandomWeb.AccessToken();
                var userId = RandomModel.Id.Long();

                var companySummary = CompanySummaryTestBuilder.CreateWithRandomProps()
                    .WithCompanyId(companyId)
                    .Build();
                var companyService = CompanyServiceStubBuilder.Create()
                    .WithCompanySummary(companyId, companySummary)
                    .Build();

                var companyReportSource = CompanyReportSourceTestBuilder.CreateWithAllProps(companySummary.CompanyId, ParentFolderPath, reportPath.ReportId).Build();
                var report = XtraReportTestBuilder.Create().Build();
                var reportStorageService = ReportStorageServiceStubBuilder.Create()
                    .WithExistingBaseReportVersion(companyReportSource.ReportPath, report)
                    .Build();

                var sut = SutFixtureBuilder.Create()
                    .WithCompanyService(companyService)
                    .WithReportStorageService(reportStorageService)
                    .Build();

                // Act
                var result = await sut.GetReportInfoForGenerationAsync(userId, reportPath, CultureInfo.CurrentCulture, accessToken, CancellationToken.None);

                // Assert
                var expectedReportInfo = ReportInfoTestBuilder.CreateWithAllProps(companyReportSource, report).Build();
                result.ShouldBeEquivalentTo(expectedReportInfo);
            }
        }

        public class GetReportInfoForDesignAsync
        {
            [Fact]
            public async Task GivenReportDetails_ShouldReturnReportInfo()
            {
                // Arrange
                var userId = RandomModel.Id.Long();
                var companyId = RandomModel.Id.Long();
                var accessToken = RandomWeb.AccessToken();
                var report = XtraReportTestBuilder.Create().Build();
                var companySummary = CompanySummaryTestBuilder.CreateWithRandomProps()
                    .WithCompanyId(companyId)
                    .Build();

                var companyService = CompanyServiceStubBuilder.Create().WithCompanySummary(companyId, companySummary).Build();

                var reportPath = ReportPathTestBuilder.CreateWithRandomProps().WithReportId(ReportId).WithCompanyId(companyId).Build();

                var companyReportSource = CompanyReportSourceTestBuilder.CreateWithAllProps(companySummary.CompanyId, ParentFolderPath, reportPath.ReportId).Build();

                var reportStorageService = ReportStorageServiceStubBuilder.Create()
                    .WithExistingBaseReportVersion(companyReportSource.ReportPath, report)
                    .Build();

                var sut = SutFixtureBuilder.Create()
                    .WithReportStorageService(reportStorageService)
                    .WithCompanyService(companyService)
                    .Build();

                // Act
                var result = await sut.GetReportInfoForDesignAsync(userId, reportPath, CultureInfo.CurrentCulture, accessToken, CancellationToken.None);

                // Assert
                var expectedReportInfo = ReportInfoTestBuilder.CreateWithAllProps(companyReportSource, report).Build();
                result.ShouldBeEquivalentTo(expectedReportInfo);
            }
        }

        private class SutFixtureBuilder : ITestBuilder<TaxBreakdownRetrievalStrategy>
        {
            private readonly IReportUserSecurityService reportUserSecurityService = ReportUserSecurityServiceStubBuilder.Create().Build();
            private ICompanyService companyService = CompanyServiceStubBuilder.Create().Build();
            private IReportStorageService reportStorageService = ReportStorageServiceStubBuilder.Create().Build();
            private readonly ITaxBreakdownTableService taxBreakdownTableService = Substitute.For<ITaxBreakdownTableService>();

            public TaxBreakdownRetrievalStrategy Build() =>
                new(
                    this.reportStorageService,
                    this.reportUserSecurityService,
                    this.companyService,
                    this.taxBreakdownTableService);

            public static SutFixtureBuilder Create() => new();

            public SutFixtureBuilder WithCompanyService(ICompanyService companyService)
            {
                this.companyService = companyService;
                return this;
            }

            public SutFixtureBuilder WithReportStorageService(IReportStorageService reportStorageService)
            {
                this.reportStorageService = reportStorageService;
                return this;
            }
        }
    }
}