namespace PaySpace.Venuta.Excel.Employees.Tests.Validators
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation.TestHelper;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
    using Microsoft.Extensions.Localization;

    using MockQueryable;

    using Moq;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Employees.Validators;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Employees;

    using Xunit;

    public class EmployeeLeaveAdjustmentValidationTests
    {
        private readonly Mock<ApplicationContext> applicationContext;
        private readonly Mock<IEmployeeLeaveSettingService> employeeLeaveSettingService;
        private readonly Mock<IEmployeeLeaveService> employeeLeaveService;
        private readonly Mock<IAuthorizationService> authorisationService;
        private readonly Mock<ISecurityProfile> profile;
        private readonly Mock<ITenantProvider> tenantProvider;
        private readonly Mock<IEmployeeLeaveConcessionService> employeeLeaveConcessionService;
        private readonly Mock<IEmployeeLeaveSetupService> employeeLeaveSetupService;
        private readonly Mock<ICompanyRunService> companyRunService;

        private readonly MutatorContext context;
        private readonly EmployeeLeaveAdjustmentValidator validator;

        public EmployeeLeaveAdjustmentValidationTests()
        {
            var localizer = new Mock<IStringLocalizer<EmployeeLeaveAdjustment>>();
            var dtoLocalizer = new Mock<IStringLocalizer<EmployeeLeaveAdjustmentDto>>();
            var modelMetaDataProvider = new Mock<IModelMetadataProvider>();
            var objectModelValidator = new Mock<IObjectModelValidator>();
            var serviceProvider = new Mock<IServiceProvider>();
            var scopedCache = new Mock<IScopedCache>();

            this.applicationContext = new Mock<ApplicationContext>();
            this.employeeLeaveSettingService = new Mock<IEmployeeLeaveSettingService>();
            this.employeeLeaveService = new Mock<IEmployeeLeaveService>();
            this.authorisationService = new Mock<IAuthorizationService>();
            this.profile = new Mock<ISecurityProfile>();
            this.tenantProvider = new Mock<ITenantProvider>();
            this.employeeLeaveConcessionService = new Mock<IEmployeeLeaveConcessionService>();
            this.employeeLeaveSetupService = new Mock<IEmployeeLeaveSetupService>();
            this.companyRunService = new Mock<ICompanyRunService>();

            localizer.Setup(_ => _[It.IsAny<string>()]).Returns<string>(label => new LocalizedString(label, label));
            dtoLocalizer.Setup(_ => _[It.IsAny<string>()]).Returns<string>(label => new LocalizedString(label, label));
            this.profile.Setup(_ => _.IsFullAccess(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Adjustment)).Returns(true);
            this.authorisationService.Setup(_ => _.AuthorizeAsync(null, It.IsAny<EmployeeLeaveAdjustmentDto>(), It.IsAny<IList<IAuthorizationRequirement>>()))
                .ReturnsAsync(AuthorizationResult.Success());

            this.context = new MutatorContext(this.profile.Object, 1, null);
            this.validator = new EmployeeLeaveAdjustmentValidator(
                localizer.Object,
                dtoLocalizer.Object,
                this.applicationContext.Object,
                this.employeeLeaveSettingService.Object,
                this.employeeLeaveService.Object,
                this.employeeLeaveConcessionService.Object,
                this.authorisationService.Object,
                modelMetaDataProvider.Object,
                objectModelValidator.Object,
                serviceProvider.Object,
                scopedCache.Object,
                this.tenantProvider.Object,
                this.employeeLeaveSetupService.Object,
                this.companyRunService.Object);
        }

        [Fact]
        public async Task Adjustment_LeaveTypeEmpty_Error()
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                RunId = 1
            };

            this.employeeLeaveService.Setup(_ => _.IsCompanyRunClosedAsync(0, 1)).ReturnsAsync(false);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorCode(ErrorCodes.Leave.LeaveTypeRequired);
        }

        [Fact]
        public async Task Adjustment_LeaveTypeInvalidForEmployee_Error()
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                RunId = 1,
                LeaveType = LeaveType.Annual
            };

            this.employeeLeaveService.Setup(_ => _.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today)).ReturnsAsync(false);
            this.employeeLeaveService.Setup(_ => _.IsCompanyRunClosedAsync(0, 1)).ReturnsAsync(false);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorCode(ErrorCodes.Leave.LeaveTypeNotConfigured);
        }

        [Fact]
        public async Task Adjustment_BucketEmpty_Error()
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                RunId = 1,
                LeaveType = LeaveType.Annual,
                NoOfDays = 1
            };

            this.employeeLeaveService.Setup(_ => _.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today)).ReturnsAsync(true);
            this.employeeLeaveService.Setup(_ => _.IsCompanyRunClosedAsync(0, 1)).ReturnsAsync(false);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorCode(ErrorCodes.Leave.NoBucket);
        }

        [Theory]
        [InlineData(LeaveType.Annual, 1L)]
        [InlineData(LeaveType.Special, 1L)]
        [InlineData(LeaveType.Special, -2L)]
        public async Task Adjustment_BucketInvalid_Error(LeaveType leaveType, long? companyLeaveSetupId)
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                RunId = 1,
                LeaveType = leaveType,
                NoOfDays = 1,
                CompanyLeaveSetupId = companyLeaveSetupId
            };

            this.employeeLeaveService.Setup(_ => _.LeaveTypeValidAsync(1, leaveType, DateTime.Today)).ReturnsAsync(true);
            this.employeeLeaveSettingService.Setup(_ => _.GetBucketsAsync(1, leaveType, DateTime.Today)).ReturnsAsync(new List<(long, string)>());
            this.employeeLeaveService.Setup(_ => _.IsCompanyRunClosedAsync(0, 1)).ReturnsAsync(false);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorCode(ErrorCodes.Leave.LeaveBucketNotInvalid);
        }

        [Fact]
        public async Task Adjustment_CompanyRunClosed_Error()
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                RunId = 1,
                LeaveType = LeaveType.Annual,
                NoOfDays = 1,
                CompanyLeaveSetupId = -2
            };

            this.employeeLeaveService.Setup(_ => _.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today)).ReturnsAsync(true);
            this.employeeLeaveService.Setup(_ => _.IsCompanyRunClosedAsync(0, 1)).ReturnsAsync(true);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorCode(ErrorCodes.Leave.ModificationsDisabled);
        }

        [Fact]
        public async Task Adjustment_CompanyRunMissing_Error()
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                LeaveType = LeaveType.Annual,
                NoOfDays = 1,
                CompanyLeaveSetupId = -2
            };

            this.employeeLeaveService.Setup(_ => _.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today)).ReturnsAsync(true);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorCode(ErrorCodes.Leave.CompanyRunRequired);
        }

        [Fact]
        public async Task Adjustment_NoOfDaysEmpty_Error()
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                RunId = 1,
                LeaveType = LeaveType.Annual,
                NoOfDays = 0,
                CompanyLeaveSetupId = -2
            };

            this.employeeLeaveService.Setup(_ => _.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today)).ReturnsAsync(true);
            this.employeeLeaveService.Setup(_ => _.IsCompanyRunClosedAsync(0, 1)).ReturnsAsync(false);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorCode(ErrorCodes.Leave.ZeroLeaveDays);
        }

        [Fact]
        public async Task Adjustment_ValidModel_SuccesfulValidation()
        {
            // Given
            var model = new EmployeeLeaveAdjustmentDto
            {
                EmployeeId = 1,
                RunId = 1,
                LeaveType = LeaveType.Annual,
                NoOfDays = 1,
                CompanyLeaveSetupId = -2,
            };

            this.employeeLeaveService.Setup(_ => _.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today)).ReturnsAsync(true);
            this.employeeLeaveService.Setup(_ => _.IsCompanyRunClosedAsync(0, 1)).ReturnsAsync(false);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            Assert.Empty(result.Errors);
            Assert.True(result.IsValid);
        }

        [Fact]
        public async Task Create_LeaveAdjustment_Fails_When_EffectiveDate_IsAfter_PeriodEndDate()
        {
            // Arrange
            var model = new EmployeeLeaveAdjustmentDto { CompanyLeaveSetupId = 1, RunId = 1, EmployeeId = 1, };

            var leaveSetups = new List<EmployeeLeaveSetup>
            {
                new() { EffectiveDate = new DateTime(2025, 1, 1) }, // After the period end date
                new() { EffectiveDate = new DateTime(2023, 1, 1) } // Before the period end date
            }.BuildMock();

            this.tenantProvider.Setup(_ => _.GetCompanyId()).Returns(1);
            this.companyRunService.Setup(_ => _.GetRunSummary(It.IsAny<long>())).Returns(new CompanyRunSummaryResult() { PeriodEndDate = new DateTime(2024, 1, 1)});
            this.employeeLeaveSetupService.Setup(_ => _.GetLeaveSetups(It.IsAny<Tenant>())).Returns(leaveSetups);

            // Act
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Assert
            result.Errors.WithErrorCode(ErrorCodes.Leave.EffectiveDateIsAfterRunEndDate);
        }
    }
}