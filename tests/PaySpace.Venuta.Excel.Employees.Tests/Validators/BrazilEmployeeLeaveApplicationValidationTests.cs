namespace PaySpace.Venuta.Excel.Employees.Tests.Validators
{
    using System;
    using System.Collections.Generic;
    using System.Security.Claims;
    using System.Threading;
    using System.Threading.Tasks;

    using FluentValidation.TestHelper;

    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc.ModelBinding;
    using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
    using Microsoft.Extensions.Localization;

    using Moq;

    using NSubstitute;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Dto.Employees;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation;
    using PaySpace.Venuta.Excel.Abstractions.Models;
    using PaySpace.Venuta.Excel.Employees.Validators.BR;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Modules.Leave.Abstractions.Models;
    using PaySpace.Venuta.Security;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Storage;

    using Xunit;

    public class BrazilEmployeeLeaveApplicationValidationTests
    {
        private readonly BrazilEmployeeLeaveApplicationValidator validator;
        private readonly MutatorContext context;

        private readonly ICompanyRunService companyRunService = Substitute.For<ICompanyRunService>();
        private readonly IEmployeeLeaveService employeeLeaveService = Substitute.For<IEmployeeLeaveService>();
        private readonly IEmployeeLeaveValidationService employeeLeaveValidationService = Substitute.For<IEmployeeLeaveValidationService>();
        private readonly ICompanyService companyService = Substitute.For<ICompanyService>();
        private readonly ICompanyLeaveService companyLeaveService = Substitute.For<ICompanyLeaveService>();
        private readonly IEmployeeLeaveSettingService employeeLeaveSettingService = Substitute.For<IEmployeeLeaveSettingService>();
        private readonly IEmployeeLeaveValueService employeeLeaveValueService = Substitute.For<IEmployeeLeaveValueService>();
        // private readonly ICompanyRosterService companyRosterService = Substitute.For<ICompanyRosterService>();
        // private readonly IPublicHolidayService publicHolidayService = Substitute.For<IPublicHolidayService>();

        public BrazilEmployeeLeaveApplicationValidationTests()
        {
            var localizer = Substitute.For<IStringLocalizer<EmployeeLeaveAdjustment>>();
            var dtoLocalizer = Substitute.For<IStringLocalizer<EmployeeLeaveApplicationDto>>();
            var applicationContext = Substitute.For<ApplicationContext>();
            var brazilEmployeeLeaveBalanceService = Substitute.For<IBrazilEmployeeLeaveBalanceService>();
            var companySettingService = Substitute.For<ICompanySettingService>();
            var modelMetadataProvider = Substitute.For<IModelMetadataProvider>();
            var objectModelValidator = Substitute.For<IObjectModelValidator>();
            var serviceProvider = Substitute.For<IServiceProvider>();
            var scopedCache = Substitute.For<IScopedCache>();
            var fileManagerStorageService = Substitute.For<IFileManagerStorageService>();

            var authorizationService = Substitute.For<IAuthorizationService>();
            authorizationService.AuthorizeAsync(
                    Arg.Any<ClaimsPrincipal>(),
                    Arg.Any<EmployeeLeaveApplicationDto>(),
                    Arg.Any<IEnumerable<IAuthorizationRequirement>>())
                .Returns(Task.FromResult(AuthorizationResult.Success()));

            var profile = Substitute.For<ISecurityProfile>();
            profile.IsFullAccess(SystemAreas.Leave.Adjustment, SystemAreas.Leave.Adjustment).Returns(true);

            localizer[Arg.Any<string>()].Returns(args => new LocalizedString(args.Arg<string>(), args.Arg<string>()));
            dtoLocalizer[Arg.Any<string>()].Returns(args => new LocalizedString(args.Arg<string>(), args.Arg<string>()));

            localizer[Arg.Any<string>(), Arg.Any<object[]>()].Returns(args => new LocalizedString(args.Arg<string>(), args.Arg<string>()));
            dtoLocalizer[Arg.Any<string>(), Arg.Any<object[]>()].Returns(args => new LocalizedString(args.Arg<string>(), args.Arg<string>()));

            this.context = new MutatorContext(profile, 1, null);

            var tenantProvider = Substitute.For<ITenantProvider>();
            tenantProvider.GetCompanyId().Returns(1);

            this.validator = new BrazilEmployeeLeaveApplicationValidator(
                localizer,
                dtoLocalizer,
                applicationContext,
                brazilEmployeeLeaveBalanceService,
                this.employeeLeaveSettingService,
                this.employeeLeaveService,
                this.employeeLeaveValueService,
                this.companyService,
                companySettingService,
                this.companyLeaveService,
                this.companyRunService,
                authorizationService,
                modelMetadataProvider,
                objectModelValidator,
                serviceProvider,
                scopedCache,
                this.employeeLeaveValidationService,
                tenantProvider,
                fileManagerStorageService);
        }

        [Fact]
        public async Task Application_thirteenthCheque_Not_Annual_Error()
        {
            // Given
            var model = new EmployeeLeaveApplicationDto
            {
                EmployeeId = 1,
                LeaveTransactionType = LeaveEntryType.LeaveApplication,
                RunId = 1,
                CompanyLeaveSetupId = -2,
                LeaveType = LeaveType.Sick,
                LeaveStatus = LeaveStatus.Approved,
                LeaveStartDate = DateTime.Today,
                LeaveEndDate = DateTime.Today.AddDays(1),
                NoOfDays = 2,
                Days = 2,
                ThirteenCheque = true
            };
            var settings = new EmployeeLeaveApplicationSettings
            {
                LeaveSettings = new EmployeeLeaveSettings(),
                LeaveValue = new LeaveValue(2, 0, 0, 0, 0),
                Waiting = new List<DateTime>(),
                Approved = new List<DateTime>(),
                Holidays = new List<DateTime>()
            };

            this.companyRunService.GetRunPeriodAsync(1).Returns(new CompanyRunPeriodResult { Status = RunStatus.Open });
            this.employeeLeaveService.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today).Returns(true);
            this.employeeLeaveValidationService
                .ValidateCompanyRunAsync(It.Is<Tenant>(_ => _.CompanyId == 1 && _.EmployeeId == 1), 0, 1, true)
                .Returns((true, string.Empty));
            this.employeeLeaveValidationService.IsLeaveSchemeActiveAsync(1, model.LeaveStartDate, model.LeaveEndDate).Returns(false);
            this.companyService.GetTaxCountryCodeAsync(1).Returns(BrazilConstants.BrazilTaxCountryCode);

            this.context.Add("Entity", new EmployeeLeaveAdjustment());
            this.context.Add("LeaveApplicationSettings", settings);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Leave.ThirteenthChequeNotAllowed);
        }

        [Fact]
        public async Task Application_thirteenthCheque_Not_Brazil_Error()
        {
            // Given
            var model = new EmployeeLeaveApplicationDto
            {
                EmployeeId = 1,
                LeaveTransactionType = LeaveEntryType.LeaveApplication,
                RunId = 1,
                CompanyLeaveSetupId = -2,
                LeaveType = LeaveType.Annual,
                LeaveStatus = LeaveStatus.Approved,
                LeaveStartDate = DateTime.Today,
                LeaveEndDate = DateTime.Today.AddDays(1),
                NoOfDays = 2,
                Days = 2,
                ThirteenCheque = true
            };
            var settings = new EmployeeLeaveApplicationSettings
            {
                LeaveSettings = new EmployeeLeaveSettings(),
                LeaveValue = new LeaveValue(2, 0, 0, 0, 0),
                Waiting = new List<DateTime>(),
                Approved = new List<DateTime>(),
                Holidays = new List<DateTime>(),
                Balance = new LeaveBalance
                {
                    TimeSpan = new TimeSpan(30, 0, 0, 0)
                }
            };

            this.companyRunService.GetRunPeriodAsync(1).Returns(new CompanyRunPeriodResult { Status = RunStatus.Open });
            this.employeeLeaveService.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today).Returns(true);
            this.employeeLeaveValidationService.
                ValidateCompanyRunAsync(It.Is<Tenant>(_ => _.CompanyId == 1 && _.EmployeeId == 1), 0, 1, true)
                .Returns((true, string.Empty));
            this.employeeLeaveValidationService.IsLeaveSchemeActiveAsync(1, model.LeaveStartDate, model.LeaveEndDate).Returns(false);
            this.companyService.GetCountryIdAsync(1).Returns(1);

            this.context.Add("Entity", new EmployeeLeaveAdjustment());
            this.context.Add("LeaveApplicationSettings", settings);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Leave.ThirteenthChequeNotAllowed);
        }

        [Fact]
        public async Task Application_StartDate_Error()
        {
            // Given
            var model = new EmployeeLeaveApplicationDto
            {
                EmployeeId = 1,
                LeaveTransactionType = LeaveEntryType.LeaveApplication,
                RunId = 1,
                CompanyLeaveSetupId = -2,
                LeaveType = LeaveType.Annual,
                LeaveStatus = LeaveStatus.Approved,
                LeaveStartDate = DateTime.Today,
                LeaveEndDate = DateTime.Today.AddDays(1),
                NoOfDays = 2,
                Days = 2
            };

            var entity = new EmployeeLeaveAdjustment
            {
                EmployeeId = 1,
                CompanyLeaveSetupId = -2,
                LeaveType = LeaveType.Annual,
                Status = LeaveStatus.Approved,
                StartDate = DateTime.Today,
                EndDate = DateTime.Today.AddDays(1),
                TotalDays = 2
            };

            var settings = new EmployeeLeaveApplicationSettings
            {
                LeaveSettings = new EmployeeLeaveSettings()
                {
                    OffDays = 1,
                    InactiveWorkingDays = new List<DayOfWeek>() { DayOfWeek.Saturday, DayOfWeek.Sunday }
                },
                LeaveValue = new LeaveValue(2, 0, 0, 0, 0),
                Waiting = new List<DateTime>(),
                Approved = new List<DateTime>(),
                Holidays = new List<DateTime>(),

                Balance = new LeaveBalance
                {
                    TimeSpan = new TimeSpan(30, 0, 0, 0)
                }
            };

            this.companyService.GetTaxCountryIdAsync(1).Returns(BrazilConstants.BrazilTaxCountryId);
            this.companyRunService.GetRunPeriodAsync(1).Returns(new CompanyRunPeriodResult { Status = RunStatus.Open });
            this.companyRunService.FutureRunExistsAsync(Arg.Any<long>(), Arg.Any<DateTime?>()).Returns(Task.FromResult(true));
            this.employeeLeaveService.LeaveTypeValidAsync(1, LeaveType.Annual, DateTime.Today).Returns(true);
            this.employeeLeaveService.GetCompanySchemeIdAsync(1, model.CompanyLeaveSetupId, DateTime.Today).Returns(1);
            this.employeeLeaveValidationService
                .ValidateCompanyRunAsync(Arg.Is<Tenant>(t => t.CompanyId == 1 && t.EmployeeId == 1), 0, 1, true)
                .Returns((true, string.Empty));
            this.employeeLeaveValidationService.IsLeaveSchemeActiveAsync(1, model.LeaveStartDate, model.LeaveEndDate).Returns(true);
            this.companyLeaveService
                .GetPriorityLeaveSetupAsync(1, model.LeaveType, model.CompanyLeaveSetupId, model.LeaveStartDate.Value)
                .Returns(new CompanyLeaveDetail { ConsecutiveDays = 2, OffDays = 1 });
            this.employeeLeaveSettingService
                .GetRosterInactiveWorkingDaysAsync(1, model.EmployeeId)
                .Returns(new List<DayOfWeek>() { DayOfWeek.Saturday, DayOfWeek.Sunday });
            this.employeeLeaveValueService
                .GetDisabledStartDatesAsync(model.LeaveType, model.CompanyLeaveSetupId, model.LeaveStartDate, 1, model.EmployeeId, 1)
                .Returns(new List<DateTime>() { DateTime.Today });

            IEnumerable<DayOfWeek> dayOfWeek = new List<DayOfWeek>()
            {
                DayOfWeek.Saturday,
                DayOfWeek.Sunday
            };

            this.employeeLeaveValueService.GetDisabledStartDaysAsync(1, model.EmployeeId, 1)
                .Returns(Task.FromResult(dayOfWeek));

            this.employeeLeaveValidationService
                .ValidateLeaveParcelsAsync(entity, 1, settings.Balance.Days, null, CancellationToken.None)
                .Returns((true, string.Empty));

            this.context.Add("Entity", entity);
            this.context.Add("LeaveApplicationSettings", settings);

            // When
            var result = await this.validator.ValidateAsync(this.context, model, RuleSetNames.Create, CancellationToken.None);

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Leave.StartDateInvalid);
        }
    }
}