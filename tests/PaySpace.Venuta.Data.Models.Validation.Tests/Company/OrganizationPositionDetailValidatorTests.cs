namespace PaySpace.Venuta.Data.Models.Validation.Tests.Company
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using FluentValidation;

    using Microsoft.Extensions.Localization;

    using MockQueryable;

    using Moq;

    using NSubstitute;

    using PaySpace.Cache;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Organization;
    using PaySpace.Venuta.Data.Models.Validation.Company;
    using PaySpace.Venuta.Modules.Employee.Positions.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;

    using Xunit;

    public class OrganizationPositionDetailValidatorTests
    {
        private readonly OrganizationPositionDetailValidator validator;
        private readonly IStringLocalizer<OrganizationPositionDetail> localizer;
        private const int CompanyId = 158;
        private readonly Mock<ApplicationContext> context;

        public OrganizationPositionDetailValidatorTests()
        {
            this.context = new Mock<ApplicationContext>();

            this.localizer = Substitute.For<IStringLocalizer<OrganizationPositionDetail>>();
            this.localizer[Arg.Any<string>()].Returns((ci) => new LocalizedString(ci.ArgAt<string>(0), ci.ArgAt<string>(0)));
            this.localizer[Arg.Any<string>(), Arg.Any<object[]>()].Returns((ci) => new LocalizedString(ci.ArgAt<string>(0), ci.ArgAt<string>(1)));

            var organizationPositionService = Substitute.For<IOrganizationPositionService>();
            organizationPositionService.GetPositionDetailsByCompanyId(Arg.Any<long>()).Returns(GetPositionDetails());
            organizationPositionService.GetPositionsByCompanyId(Arg.Any<long>()).Returns(GetPositions());

            var employeePositionService = Substitute.For<IEmployeePositionService>();
            employeePositionService.LinkedToOrganizationPositionAsync(Arg.Any<long>()).Returns(true);

            var companyJobManagementService = Substitute.For<ICompanyJobManagementService>();

            var companyPaymentModuleService = Substitute.For<ICompanyPaymentModuleService>();
            companyPaymentModuleService.IsMasterOrPremierAsync(Arg.Any<long>()).Returns(true);

            var companyService = Substitute.For<ICompanyService>();
            companyService.HasAdvancedPositionManagementAsync(Arg.Any<long>()).Returns(true);

            var scopedCache = new Mock<IScopedCache>();
            scopedCache.Setup(_ => _.GetOrCreateAsync(It.IsAny<string>(), It.IsAny<Func<Task<bool>>>()))
                .Returns(Task.FromResult(true));

            var tenantProvider = new Mock<ITenantProvider>();
            tenantProvider.Setup(_ => _.GetCompanyId()).Returns(CompanyId);

            this.validator = new OrganizationPositionDetailValidator(
                this.localizer,
                organizationPositionService,
                employeePositionService,
                companyJobManagementService,
                companyPaymentModuleService,
                tenantProvider.Object,
                companyService,
                scopedCache.Object,
                this.context.Object);

        }

        [Fact]
        public async Task Create_Should_Succeed_Given_ValidPositionDetail()
        {
            // Arrange
            var model = new OrganizationPositionDetail
            {
                PositionDetailId = 0,
                PositionCode = "POS3",
                GradeId = 1,
                OrganizationPosition = new()
                {
                    Description = "Position 3"
                },
                EffectiveDate = DateTime.Now.Date
            };

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));

            // Assert
            Assert.True(result.IsValid);
        }

        [Fact]
        public async Task Create_Should_Fail_Given_InvalidInactiveDate()
        {
            // Arrange
            var model = new OrganizationPositionDetail
            {
                PositionDetailId = 0,
                PositionCode = "POS3",
                GradeId = 1,
                OrganizationPosition = new()
                {
                    Description = "Position 3"
                },
                EffectiveDate = DateTime.Now.Date,
                InactiveDate = DateTime.Now.Date.AddDays(-1)
            };

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));

            // Assert
            Assert.Contains(result.Errors, e => e.ErrorMessage == this.localizer.GetString("lblInvalidInactiveDate"));
        }

        [Fact]
        public async Task Create_Should_Fail_Given_DescriptionExceedsCharacterLimit()
        {
            // Arrange
            var model = new OrganizationPositionDetail
            {
                PositionDetailId = 0,
                PositionCode = "POS3",
                GradeId = 1,
                OrganizationPosition = new()
                {
                    Description = string.Concat(Enumerable.Repeat("1", 81))
                },
                EffectiveDate = DateTime.Now.Date
            };

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));

            // Assert
            Assert.Contains(result.Errors, e => e.ErrorMessage == this.localizer.GetString("lblDescriptionLength"));
        }

        [Fact]
        public async Task Create_Should_Fail_Given_InvalidGrade()
        {
            // Arrange
            var model = new OrganizationPositionDetail
            {
                PositionDetailId = 0,
                PositionCode = "POS3",
                GradeId = null,
                OrganizationPosition = new()
                {
                    Description = "Position 3"
                },
                EffectiveDate = DateTime.Now.Date
            };

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));

            // Assert
            Assert.Contains(result.Errors, e => e.ErrorMessage == this.localizer.GetString("lblGradeRequired"));
        }

        [Fact]
        public async Task Create_Should_Fail_Given_Duplicate_Description()
        {
            // Arrange
            var model = new OrganizationPositionDetail
            {
                PositionDetailId = 0,
                PositionCode = "POS3",
                GradeId = 1,
                OrganizationPosition = new()
                {
                    Description = "Position 2"
                },
                EffectiveDate = DateTime.Now.Date
            };

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));

            // Assert
            Assert.Contains(result.Errors, e => e.ErrorMessage == this.localizer.GetString("lblDuplicatePositions"));
        }

        [Fact]
        public async Task Create_Should_Fail_Given_Duplicate()
        {
            // Arrange
            var model = new OrganizationPositionDetail
            {
                PositionDetailId = 0,
                PositionCode = "POS2",
                GradeId = 1,
                OrganizationPosition = new()
                {
                    Description = "Position 2"
                },
                EffectiveDate = DateTime.Now.Date
            };

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));

            // Assert
            Assert.Contains(result.Errors, e => e.ErrorMessage == this.localizer.GetString("lblDuplicatePositionDetails"));
        }

        private static IQueryable<OrganizationPositionDetail> GetPositionDetails() => new List<OrganizationPositionDetail>
        {
            new()
            {
                PositionDetailId = 1,
                PositionCode = "POS1",
                EffectiveDate = DateTime.Now.Date,
                OrganizationPosition = new()
                {
                    Description = "Position 1"
                }
            },
            new()
            {
                PositionDetailId = 2,
                PositionCode = "POS2",
                EffectiveDate = DateTime.Now.Date,
                OrganizationPosition = new()
                {
                    Description = "Position 2"
                }
            }
        }.BuildMock();

        private static IQueryable<OrganizationPosition> GetPositions() => new List<OrganizationPosition>
        {
            new()
            {
               OrganizationPositionId = 1,
               CompanyId = CompanyId,
               Description = "Position 1"
            },
            new()
            {
               OrganizationPositionId = 1,
               CompanyId = CompanyId,
               Description = "Position 2"
            }
        }.BuildMock();
    }
}
