namespace PaySpace.Venuta.Data.Models.Validation.Tests.Company.UK
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using FluentValidation;
    using FluentValidation.TestHelper;

    using Microsoft.Extensions.Localization;

    using Moq;

    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Company.UK;
    using PaySpace.Venuta.Data.Models.Validation.Tests.Helpers;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Components;

    using Xunit;

    public class UKCompanyRunValidatorTests
    {
        private readonly UKCompanyRunValidator validator;
        private readonly Mock<IComponentValueService> componentValueService;
        private readonly Mock<ICompanyService> companyService;
        private readonly Mock<ICompanyRunService> companyRunService;
        private readonly Mock<ICalculationWebApiClient> calculationWebApiClient;
        private readonly Mock<ICompanyFrequencyService> companyFrequencyService;
        private readonly Mock<ITenantProvider> tenantProvider;
        private readonly Mock<ApplicationContext> context;
        public UKCompanyRunValidatorTests()
        {
            var localizer = new Mock<IStringLocalizer<CompanyRun>>();

            this.tenantProvider = new Mock<ITenantProvider>();
            this.componentValueService = new Mock<IComponentValueService>();
            this.companyService = new Mock<ICompanyService>();
            this.companyRunService = new Mock<ICompanyRunService>();
            this.calculationWebApiClient = new Mock<ICalculationWebApiClient>();
            this.companyFrequencyService = new Mock<ICompanyFrequencyService>();
            this.context = new Mock<ApplicationContext>();

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns("GB");
            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(It.IsAny<long>()))
                .Returns(Task.FromResult((int)TaxCountry.UnitedKingdom));

            this.companyRunService.Setup(_ => _.GetPayCalendarStartAndEndDateAsync(It.IsAny<long>()))
                .ReturnsAsync(((DateTime? StartDate, DateTime? EndDate))
                    (new DateTime(2025, 1, 6), new DateTime(2025, 2, 5)));

            CompanyRunHelper.SetupMocks(localizer, this.tenantProvider, this.companyRunService, this.calculationWebApiClient, this.companyFrequencyService);

            this.validator = new UKCompanyRunValidator(
                localizer.Object,
                this.context.Object,
                this.tenantProvider.Object,
                this.componentValueService.Object,
                this.companyService.Object,
                this.companyRunService.Object,
                this.calculationWebApiClient.Object,
                this.companyFrequencyService.Object);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Record_Cannot_Update_Paydate_Outside_Allocated_Period(string ruleSetName)
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.PayDate = new DateTime(2025, 2, 7);
            model.RunType = RunType.Interim;

            var validationContext = ValidationContext<CompanyRun>.CreateWithOptions(model, opt => opt.IncludeRuleSets(ruleSetName));
            CompanyRunHelper.SetValidationInfoContext(validationContext, false);

            // Act
            var result = await this.validator.ValidateAsync(validationContext);

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.PayDateOutsideAllocatedPeriodFormatted);
        }

        [Fact]
        public async Task Record_Cannot_Close_With_Previous_Open_Runs()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.Status = RunStatus.Closed;

            var existingCompanyRuns = new List<CompanyRun>
            {
                new() { Status = RunStatus.Open, PeriodStartDate = model.PeriodStartDate.AddDays(-1) }
            };

            this.companyRunService.Setup(_ => _.GetAllCompanyRunsAsync(It.IsAny<long>()))
                .ReturnsAsync(existingCompanyRuns);

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.CloseRunsBeforeCurrent);
        }

        [Fact]
        public async Task Record_Cannot_Have_Invalid_Run_Order_Combination()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.Status = RunStatus.Future;

            var conflictingCompanyRuns = new List<CompanyRun>
            {
                new() { Status = RunStatus.Open, OrderNumber = model.OrderNumber + 1, PeriodStartDate = model.PeriodStartDate }
            };

            this.companyRunService.Setup(_ => _.GetAllCompanyRunsAsync(It.IsAny<long>()))
                .ReturnsAsync(conflictingCompanyRuns);

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.InvalidRunOrderCheckSequence);
        }

        [Fact]
        public async Task Record_Cannot_Create_OrderNumber_PayDate()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.RunType = RunType.Interim;
            model.OrderNumber = 2;

            this.companyRunService.Setup(_ => _.GetPrevTaxPeriodCodeAsync(It.IsAny<long>(), It.IsAny<long>(), It.IsAny<UserType>()))
                .Returns(Task.FromResult(model.PeriodCode));

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));
            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.InvalidPayDateRunOrder);
        }

        [Fact]
        public async Task Record_Cannot_Update_PayDate_Before_StartDate()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.PayDate = model.PeriodStartDate.AddDays(-1);

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.PayDateBeforeStartDate);
        }
    }
}