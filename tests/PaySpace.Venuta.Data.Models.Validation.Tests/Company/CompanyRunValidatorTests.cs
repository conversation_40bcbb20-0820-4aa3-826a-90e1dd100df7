namespace PaySpace.Venuta.Data.Models.Validation.Tests.Company
{
    using System;
    using System.Threading.Tasks;

    using FluentValidation;
    using FluentValidation.TestHelper;

    using Microsoft.Extensions.Localization;

    using Moq;

    using PaySpace.Calculation.WebApi.Client.Interfaces;
    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Company;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Company;
    using PaySpace.Venuta.Data.Models.Validation.Tests.Helpers;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Company;
    using PaySpace.Venuta.Services.Components;

    using Xunit;

    public class CompanyRunValidatorTests
    {
        private readonly CompanyRunValidator validator;
        private readonly Mock<IComponentValueService> componentValueService;
        private readonly Mock<ICompanyService> companyService;
        private readonly Mock<ICompanyRunService> companyRunService;
        private readonly Mock<ICalculationWebApiClient> calculationWebApiClient;
        private readonly Mock<ICompanyFrequencyService> companyFrequencyService;
        private readonly Mock<ITenantProvider> tenantProvider;
        private readonly Mock<ApplicationContext> context;
        public CompanyRunValidatorTests()
        {
            var localizer = new Mock<IStringLocalizer<CompanyRun>>();

            this.tenantProvider = new Mock<ITenantProvider>();
            this.componentValueService = new Mock<IComponentValueService>();
            this.companyService = new Mock<ICompanyService>();
            this.companyRunService = new Mock<ICompanyRunService>();
            this.calculationWebApiClient = new Mock<ICalculationWebApiClient>();
            this.companyFrequencyService = new Mock<ICompanyFrequencyService>();
            this.context = new Mock<ApplicationContext>();

            this.componentValueService.Setup(_ => _.HasComponentsForRunAsync(It.IsAny<long>()))
                .Returns(Task.FromResult(false));
            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(It.IsAny<long>()))
                .Returns(Task.FromResult(1));

            this.companyRunService.Setup(_ => _.DoesPublicHolidayExistAsync(It.IsAny<long>(), It.IsAny<DateTime>())).Returns(Task.FromResult(false));

            CompanyRunHelper.SetupMocks(localizer, this.tenantProvider, this.companyRunService, this.calculationWebApiClient, this.companyFrequencyService);

            this.validator = new CompanyRunValidator(
                                localizer.Object,
                                this.context.Object,
                                this.tenantProvider.Object,
                                this.componentValueService.Object,
                                this.companyService.Object,
                                this.companyRunService.Object,
                                this.calculationWebApiClient.Object,
                                this.companyFrequencyService.Object);
        }

        [Fact]
        public async Task Record_Cannot_Delete()
        {
            // Given
            this.componentValueService.Setup(_ => _.HasComponentsForRunAsync(It.IsAny<long>())).Returns(Task.FromResult(true));

            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();

            var validationContext = ValidationContext<CompanyRun>.CreateWithOptions(model, opt => opt.IncludeRuleSets(RuleSetNames.Delete));
            CompanyRunHelper.SetValidationInfoContext(validationContext, true);

            // Act
            var result = await this.validator.ValidateAsync(validationContext);

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.UnableToDeleteDueToDependancyError);
        }

        [Fact]
        public async Task Record_Cannot_Update_Paydate_Required()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.PayDate = null;

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.PayDateRequired);
        }

        [Fact]
        public async Task Record_Cannot_Update_Paydate_Holiday()
        {
            this.companyRunService.Setup(_ => _.DoesPublicHolidayExistAsync(It.IsAny<long>(), It.IsAny<DateTime>())).Returns(Task.FromResult(true));

            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.PayDate = new DateTime(2025, 1, 1);

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.PayDateOnPublicHoliday);
        }

        [Fact]
        public async Task Record_Cannot_Update_PayDate_Before_StartDate()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.PayDate = model.PeriodStartDate.AddDays(-1);

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.PayDateBeforeStartDate);
        }

        [Fact]
        public async Task Record_Cannot_Update_PeriodCode_Format()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.PeriodCode = "202513";

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.InvalidPeriodCodeFormat);
        }

        [Fact]
        public async Task Record_Cannot_Update_Calculation_In_Progress()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.Status = RunStatus.Closed;

            this.calculationWebApiClient.Setup(_ => _.IsFrequencyCalculationInProgressAsync(It.IsAny<long>())).Returns(Task.FromResult(true));

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.CalculationInProgress);
        }

        [Fact]
        public async Task Record_Cannot_Update_DuplicateEntries()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.RunDescription = "Run 2";

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.DuplicateRuns);
        }

        [Fact]
        public async Task Record_Cannot_Update_Paydate_Sunday()
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.PayDate = new DateTime(2025, 1, 26); //Sunday

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Update));

            // Assert
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.PayDateIsSunday);
        }

        [Theory]
        [InlineData(RuleSetNames.Update, false)]
        [InlineData(RuleSetNames.Update, true)]
        public async Task Record_Cannot_Update_Closed_To_Future(string ruleSetName, bool isBureau)
        {
            // Arrange
            this.companyRunService.Setup(_ => _.GetRunStatusAsync(It.IsAny<long>())).Returns(Task.FromResult(RunStatus.Closed));

            var model = CompanyRunHelper.CreateCompanyRun();
            model.Status = RunStatus.Future;

            var validationContext = ValidationContext<CompanyRun>.CreateWithOptions(model, opt => opt.IncludeRuleSets(ruleSetName));
            CompanyRunHelper.SetValidationInfoContext(validationContext, isBureau);

            // Act
            var result = await this.validator.ValidateAsync(validationContext);

            // Assert
            if (isBureau)
            {
                result.Errors.WithoutErrorMessage(ErrorCodes.CompanyRun.ClosedCannotBeFutureRun);
            }
            else
            {
                result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.ClosedCannotBeFutureRun);
            }
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task RunDescription_Required_Given_Null_Fail(string action)
        {
            // Given
            var model = CompanyRunHelper.CreateCompanyRun();
            model.RunDescription = null;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(action));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.RunDescriptionRequired);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task OrderNumber_Required_Fail(string action)
        {
            // Given
            var model = CompanyRunHelper.CreateCompanyRun();
            model.OrderNumber = default;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(action));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.OrderNumberRequired);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(1)]
        public async Task Record_Add_Interim_Run_Against_Main(int days)
        {
            // Arrange
            var model = CompanyRunHelper.CreateCompanyRun();
            model.RunType = RunType.Interim;
            model.PeriodEndDate = model.PeriodEndDate.AddDays(days);

            // Act
            var result = await this.validator.ValidateAsync(model, opt => opt.IncludeRuleSets(RuleSetNames.Create));

            // Assert
            if (days != 0)
            {
                result.Errors.WithErrorMessage(ErrorCodes.CompanyRun.InterimRunMustBeLinkedToMain);
            }
            else
            {
                result.Errors.WithoutErrorMessage(ErrorCodes.CompanyRun.InterimRunMustBeLinkedToMain);
            }
        }
    }
}