namespace PaySpace.Venuta.Data.Models.Validation.Tests.Employees
{
    using System.Threading.Tasks;

    using FluentValidation;
    using FluentValidation.TestHelper;

    using Microsoft.Extensions.Localization;

    using Moq;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Tests.Helpers;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees;

    using Xunit;

    public class AddressValidatorTests
    {
        private readonly TestAddressValidator validator;
        private readonly Mock<ICompanyService> companyService;
        private readonly Mock<ApplicationContext> applicationContext;
        private readonly Mock<ITenantProvider> tenantProvider;
        private readonly AddressService addressSanitization;

        public AddressValidatorTests()
        {
            var tenantProvider = new Mock<ITenantProvider>();

            this.companyService = new Mock<ICompanyService>();
            this.applicationContext = new Mock<ApplicationContext>();
            this.tenantProvider = new Mock<ITenantProvider>();
            this.addressSanitization = new AddressService();

            tenantProvider.Setup(_ => _.GetCompanyId()).Returns(158);
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.validator = new TestAddressValidator(
                StringLocalizerHelper.CreateMock<AddressValidator>(),
                this.tenantProvider.Object,
                this.applicationContext.Object);
        }

        [Fact]
        public async Task EmployeeAddress_Country_Required()
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.CountryId = null;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            var x = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.CountryRequired);
        }

        [Fact]
        public async Task EmployeeAddress_Province_Required()
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.ProvinceId = null;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.ProvinceRequired);
        }

        [Fact]
        public async Task EmployeeAddress_StreetNumber_Length_Invalid()
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.StreetNumber = "123456789"; // max = 8

            // When
            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.ShouldHaveValidationErrorFor(_ => _.StreetNumber).WithErrorCode("MaximumLengthValidator");
        }

        [Fact]
        public async Task EmployeeAddress_UnitNumber_Length_Invalid()
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.UnitNumber = "123456789"; // max = 8

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.UnitNumberLengthError);
        }

        [Fact]
        public async Task EmployeeAddress_Complex_Length_Invalid()
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.Complex = "12345678901234567890123456789"; // max = 25

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.ComplexLengthError);
        }

        [Theory]
        [InlineData(AddressType.Street)]
        [InlineData(AddressType.Physical)]
        public void EmployeeAddress_SpecialServices_Is_Empty(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);

            // When
            model.SpecialServices = "SomeRandomValue";
            this.addressSanitization.Sanitize(model);

            // Then
            Assert.Equal(string.Empty, model.SpecialServices);
        }

        [Theory]
        [InlineData(AddressType.Postal)]
        [InlineData(AddressType.PrivateBag)]
        public void EmployeeAddress_StreetNumber_Is_Empty(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);

            // When
            model.StreetNumber = "1234";
            this.addressSanitization.Sanitize(model);

            // Then
            Assert.Equal(string.Empty, model.StreetNumber);
        }

        [Theory]
        [InlineData(AddressType.Postal)]
        [InlineData(AddressType.PrivateBag)]
        public void EmployeeAddress_Complex_Is_Empty(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);

            // When
            model.Complex = "1234";

            this.addressSanitization.Sanitize(model);

            // Then
            Assert.Equal(string.Empty, model.Complex);
        }

        [Theory]
        [InlineData(AddressType.Postal)]
        [InlineData(AddressType.PrivateBag)]
        [InlineData(AddressType.Physical)]
        [InlineData(AddressType.Street)]
        public void EmployeeAddress_Complex_Is_Not_Empty(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);
            var complex = "1234";

            // When
            model.Complex = complex;
            model.SameAsPostal = true;

            // Then
            Assert.Equal(complex, model.Complex);
        }

        [Theory]
        [InlineData(AddressType.Postal)]
        [InlineData(AddressType.PrivateBag)]
        public void EmployeeAddress_UnitNumber_Is_NUll(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);

            // When
            model.UnitNumber = "1234";
            this.addressSanitization.Sanitize(model);

            // Then
            Assert.Null(model.UnitNumber);
        }

        [Theory]
        [InlineData(AddressType.Postal)]
        [InlineData(AddressType.PrivateBag)]
        [InlineData(AddressType.Physical)]
        [InlineData(AddressType.Street)]
        public void EmployeeAddress_UnitNumber_Is_Not_NUll(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);
            var unitNumber = "1234";

            // When
            model.UnitNumber = "1234";
            model.SameAsPostal = true;

            // Then
            Assert.Equal(unitNumber, model.UnitNumber);
        }

        [Fact]
        public async Task EmployeeAddress_IsCareOfAddress_Invalid()
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.IsCareofAddress = true;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.IsCareofAddressCantBeSet);
        }

        [Fact]
        public async Task EmployeeAddress_CareOfIntermediary_Invalid()
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.CareOfIntermediary = "1234";

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.CareOfIntermediaryCantBeSet);
        }

        [Theory]
        [InlineData(AddressType.Postal)]
        [InlineData(AddressType.PrivateBag)]
        [InlineData(AddressType.Street)]
        public async Task EmployeeAddress_CareOfIntermediary_Required(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);
            model.CareOfIntermediary = string.Empty;
            model.IsCareofAddress = true;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.CareOfIntermediaryRequred);
        }

        [Theory]
        [InlineData(AddressType.Postal)]
        [InlineData(AddressType.PrivateBag)]
        [InlineData(AddressType.Physical)]
        [InlineData(AddressType.Street)]
        public async Task EmployeeAddress_AddressLine1_And_AddressLine3_Required(AddressType addressType)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, addressType);
            model.AddressLine1 = string.Empty;
            model.AddressLine3 = string.Empty;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.AddressLine1Required);
            result.Errors.WithErrorMessage(ErrorCodes.Employee.AddressLine3Required);
        }

        [Theory]
        [InlineData("ZA", "1", ErrorCodes.Employee.AddressCodeLengthError)]
        [InlineData("ZA", "12345", ErrorCodes.Employee.AddressCodeLengthError)]
        [InlineData("ZA", "1234", null)]
        [InlineData("ZA", "", ErrorCodes.Employee.AddressCodeRequired)]
        [InlineData("ZA", "a343", ErrorCodes.Employee.AddressCodeCanContainNumberOnly)]
        [InlineData("ZA", "0000", ErrorCodes.Employee.AddressCodeCannotContainOnlyZeros)]
        [InlineData("ANY_OTHER_COUNTRY", "1234567890123", ErrorCodes.Employee.AddressCodeLengthRangeError)]
        [InlineData("ANY_OTHER_COUNTRY", "1", null)]
        [InlineData("ANY_OTHER_COUNTRY", "", ErrorCodes.Employee.AddressCodeRequired)]
        [InlineData("ANY_OTHER_COUNTRY", "12345", null)]
        [InlineData("ANY_OTHER_COUNTRY", "aa343", null)]
        public async Task EmployeeAddress_AddressCode_Validation(string countryCode, string code, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);
            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);

            model.AddressCode = code;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Empty(result.Errors);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("BR", "06455-000", 78, null)]
        [InlineData("BR", "1234", 78, ErrorCodes.Employee.AddressCodeInvalidFormat)]
        [InlineData("BR", "1234", 1, null)]
        [InlineData("BR", "12343333333333333", 1, ErrorCodes.Employee.AddressCodeLengthRangeError)]
        public async Task EmployeeAddress_AddressCode_Validation_Brazil(string countryCode, string code, int addressCountry, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);
            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);

            model.AddressCode = code;
            model.CountryId = addressCountry;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Empty(result.Errors);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("GB", "GIR 0AA", 7, null)]
        [InlineData("GB", "ABC 123", 7, ErrorCodes.Employee.InvalidUKAddressCode)]
        [InlineData("GB", "12343333333333333", 1, ErrorCodes.Employee.AddressCodeLengthRangeError)]
        public async Task EmployeeAddress_AddressCode_Validation_UK(string countryCode, string code, int addressCountry, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);
            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);

            model.AddressCode = code;
            model.CountryId = addressCountry;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Empty(result.Errors);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("AU", "0255", 62, null)]
        [InlineData("AU", "0199", 62, ErrorCodes.Employee.AustraliaAddressCodeFormatError)]
        [InlineData("AU", "9999", 62, null)]
        [InlineData("AU", "99999", 62, ErrorCodes.Employee.AustraliaAddressCodeLengthError)]
        public async Task EmployeeAddress_AddressCode_Validation_AU(string countryCode, string code, int addressCountry, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);
            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);

            model.AddressCode = code;
            model.CountryId = addressCountry;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Empty(result.Errors);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("IE", "A65 F4E2", 43, null, RuleSetNames.Create)]
        [InlineData("IE", "A65TF4E2", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Create)]
        [InlineData("IE", "A65F4E2", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Create)]
        [InlineData("IE", "a65 f4e2", 43, null, RuleSetNames.Create)]
        [InlineData("IE", "a65f4e2", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Create)]
        [InlineData("IE", "0199", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Create)]
        [InlineData("IE", "", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Create)]
        [InlineData("IE", "A65 F4E2", 43, null, RuleSetNames.Update)]
        [InlineData("IE", "A65TF4E2", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Update)]
        [InlineData("IE", "A65F4E2", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Update)]
        [InlineData("IE", "a65 f4e2", 43, null, RuleSetNames.Update)]
        [InlineData("IE", "a65f4e2", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Update)]
        [InlineData("IE", "0199", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Update)]
        [InlineData("IE", "", 43, ErrorCodes.Employee.IrelandAddressCodeFormatError, RuleSetNames.Update)]
        public async Task EmployeeAddress_AddressCode_Validation_IE(string countryCode, string code, int addressCountry, string errorCode, string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);
            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);

            model.AddressCode = code;
            model.CountryId = addressCountry;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Empty(result.Errors);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
                result.Errors.WithoutErrorMessage(ErrorCodes.Employee.AddressCodeLengthRangeError);
            }
        }

        [Theory]
        [InlineData("ZA", "1", null)]
        [InlineData("ZA", "abcdefghijklmnopqrstuvwxyz12", ErrorCodes.Employee.AddressLine1LengthError)] // 26 limit
        [InlineData("ANY_OTHER_COUNTRY", "1", null)]
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyza", null)] // 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz", ErrorCodes.Employee.AddressLine1LengthError)] // 50 limit
        public async Task EmployeeAddress_Street_AddressLIne1_Validation(string countryCode, string addressLine, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Street);

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);

            model.AddressLine1 = addressLine;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Equal(0, result.Errors.Count);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("ZA", "1", null)]
        [InlineData("ZA", "abcdefghijklmnopqrstuvwxyzabcdef", null)] // 33 limit
        [InlineData("ZA", "abcdefghijklmnopqrstuvwxyzabcdefgh", ErrorCodes.Employee.AddressLine2MaxLength)] // 33 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuv", null)]// 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyza", null)] // 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz", ErrorCodes.Employee.AddressLine2MaxLength)] // 50 limit
        public async Task EmployeeAddress_Street_AddressLIne2_Validation(string countryCode, string addressLine, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Street);

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);

            model.AddressLine2 = addressLine;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Equal(0, result.Errors.Count);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("ZA", "a", null)]
        [InlineData("ZA", "abcdefghijklmnopqrstu", null)] // 21 limit
        [InlineData("ZA", "abcdefghijklmnopqrstuv", ErrorCodes.Employee.AddressLine3MaxLength)] // 21 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuv", null)]// 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyza", null)] // 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz", ErrorCodes.Employee.AddressLine3MaxLength)] // 50 limit
        public async Task EmployeeAddress_Street_AddressLIne3_Validation(string countryCode, string addressLine, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Street);

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);

            model.AddressLine3 = addressLine;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Equal(0, result.Errors.Count);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("ZA", "1", null)]
        [InlineData("ZA", "abcdefgh", null)] // 8 limit
        [InlineData("ZA", "abcdefghi", ErrorCodes.Employee.AddressLine1LengthError)] // 8 limit
        [InlineData("ANY_OTHER_COUNTRY", "1", null)]
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyza", null)] // 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz", ErrorCodes.Employee.AddressLine1LengthError)] // 50 limit
        public async Task EmployeeAddress_Postal_AddressLIne1_Validation(string countryCode, string addressLine, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Postal);

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);

            model.AddressLine1 = addressLine;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Equal(0, result.Errors.Count);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("ZA", "1", null)]
        [InlineData("ZA", "abcdefghijklmnopqrstuv", null)] // 22 limit
        [InlineData("ZA", "abcdefghijklmnopqrstuvw", ErrorCodes.Employee.AddressLine2MaxLength)] // 22 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuv", null)]// 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyza", null)] // 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz", ErrorCodes.Employee.AddressLine2MaxLength)] // 50 limit
        public async Task EmployeeAddress_Postal_AddressLIne2_Validation(string countryCode, string addressLine, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Postal);

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);

            model.AddressLine2 = addressLine;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Equal(0, result.Errors.Count);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData("ZA", "a", null)]
        [InlineData("ZA", "abcdefghijklmnopqrstu", null)] // 21 limit
        [InlineData("ZA", "abcdefghijklmnopqrstuv", ErrorCodes.Employee.AddressLine3MaxLength)] // 21 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuv", null)]// 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyza", null)] // 50 limit
        [InlineData("ANY_OTHER_COUNTRY", "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyz", ErrorCodes.Employee.AddressLine3MaxLength)] // 50 limit
        public async Task EmployeeAddress_Postal_AddressLIne3_Validation(string countryCode, string addressLine, string errorCode)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Postal);

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns(countryCode);
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Address>(), It.IsAny<string>())).Returns(true);

            model.AddressLine3 = addressLine;

            // When
            this.addressSanitization.Sanitize(model);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (string.IsNullOrEmpty(errorCode))
            {
                Assert.Equal(0, result.Errors.Count);
            }
            else
            {
                result.Errors.WithErrorMessage(errorCode);
            }
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task EmployeeAddress_StreetNumberTooLong_Fail(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);

            model.StreetNumber = "123456789";

            // When
            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.ShouldHaveValidationErrorFor(_ => _.StreetNumber);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task EmployeeAddress_MandatoryAddressFieldsSA_Pass(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);

            this.companyService.Setup(_ => _.GetTaxCountryCodeAsync(It.IsAny<long>())).ReturnsAsync("ZA");

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithoutErrorMessage(ErrorCodes.Employee.InvalidRequiredAddressesSA);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task EmployeeAddress_MandatoryAddressFieldsSA_Fail(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateAddress(EmployeeHelper.TestEmployeeId, AddressType.Physical);
            model.AddressLine2 = string.Empty;
            model.AddressLine3 = string.Empty;

            this.tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns("ZA");

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidRequiredAddressesSA);
        }
    }

    internal class TestAddressValidator : AddressValidator
    {
        public TestAddressValidator(IStringLocalizer<AddressValidator> localizer, ITenantProvider tenantProvider, ApplicationContext context)
            : base(localizer, tenantProvider, context)
        {
        }

        protected override void UpdateRules()
        {
            // noting to implement
        }
    }
}