namespace PaySpace.Venuta.Data.Models.Validation.Tests.Employees
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading.Tasks;

    using FluentValidation;
    using FluentValidation.TestHelper;

    using Microsoft.Extensions.Localization;

    using Moq;

    using NSubstitute;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Employees;
    using PaySpace.Venuta.Data.Models.Validation.Tests.Helpers;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Employees;

    using Xunit;

    public class EmployeeValidatorTests
    {
        private readonly EmployeeValidator validator;
        private readonly Mock<ApplicationContext> applicationContext;
        private readonly Mock<ICompanyService> companyService;
        private readonly Mock<ICompanySettingService> companySettingService;
        private readonly Mock<IEmployeeProfileService> employeeProfileService;
        private readonly Mock<IEmployeeService> employeeService;
        private readonly Mock<IEmploymentStatusService> employmentStatusService;
        private readonly Mock<IUserService> userService;
        private readonly Mock<IGenderService> genderService;
        private readonly AddressService addressService;

        public EmployeeValidatorTests()
        {
            var localizerFactory = new Mock<IStringLocalizerFactory>();
            var customFieldService = new Mock<ICustomFieldService>();
            var tenantProvider = new Mock<ITenantProvider>();
            var employeeAddressService = new Mock<IEmployeeAddressService>();
            var companyFrequencyService = new Mock<ICompanyFrequencyService>();
            var customFieldListValidator = new CustomFieldListValidator<EmployeeCustomFieldValue>(customFieldService.Object, CustomFieldHelper.CreateMock<EmployeeCustomFieldValue>().Object);
            var userRegionService = new Mock<IUserRegionService>();
            var countryServiceFactory = Substitute.For<ICountryServiceFactory>();
            countryServiceFactory.Create<ICustomFieldListValidator<EmployeeCustomFieldValue>>().Returns(customFieldListValidator);

            this.applicationContext = new Mock<ApplicationContext>();
            this.companyService = new Mock<ICompanyService>();
            this.companySettingService = new Mock<ICompanySettingService>();
            this.employeeProfileService = new Mock<IEmployeeProfileService>();
            this.employeeService = new Mock<IEmployeeService>();
            this.employmentStatusService = new Mock<IEmploymentStatusService>();
            var employeeHistoryService = new Mock<IEmployeeHistoryService>();
            this.genderService = new Mock<IGenderService>();

            this.userService = new Mock<IUserService>();

            this.addressService = new AddressService();

            tenantProvider.Setup(_ => _.GetCompanyId()).Returns(158);
            tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns("ZA");
            customFieldService.Setup(_ => _.GetCustomFieldFormFieldsAsync(It.IsAny<long>(), It.IsAny<string>())).Returns(Task.FromResult(new List<CustomFieldFormField>()));
            employeeAddressService.Setup(_ => _.GetEmployeeAddressTypesAsync(EmployeeHelper.TestEmployeeId)).Returns(Task.FromResult(EmployeeHelper.GetAddressTypes()));
            companyFrequencyService.Setup(_ => _.GetNameAsync(EmployeeHelper.TestEmployeeId)).Returns(Task.FromResult("Test"));
            userRegionService.Setup(_ => _.DoesEmailExistAsync(It.IsAny<string>())).Returns(Task.FromResult(false));

            this.genderService.Setup(_ => _.GetEmployeeGenderCodeAsync(It.IsAny<long>())).ReturnsAsync(GenderConstants.Codes.Male);
            this.genderService.Setup(_ => _.IsGenderValidForCountryAsync(It.IsAny<long>(), It.IsAny<int>())).ReturnsAsync(true);

            this.validator = new EmployeeValidator(
                this.applicationContext.Object,
                StringLocalizerHelper.CreateMock<EmployeeValidator>(),
                StringLocalizerHelper.CreateMock<AddressValidator>(),
                StringLocalizerHelper.CreateMock<EmployeeEmploymentStatus>(),
                StringLocalizerHelper.CreateMock<EmployeePayRate>(),
                localizerFactory.Object,
                customFieldService.Object,
                tenantProvider.Object,
                this.companyService.Object,
                this.companySettingService.Object,
                this.employeeService.Object,
                this.employeeProfileService.Object,
                employeeAddressService.Object,
                this.employmentStatusService.Object,
                this.userService.Object,
                employeeHistoryService.Object,
                companyFrequencyService.Object,
                countryServiceFactory,
                this.genderService.Object,
                userRegionService.Object);
        }

        [Theory]
        [InlineData(RuleSetNames.Create, 0)]
        [InlineData(RuleSetNames.Update, EmployeeHelper.TestEmployeeId)]
        public async Task Employee_Create_Or_Update_IsValid(string rulesetName, long employeeId)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(employeeId);

            // When
            this.addressService.Sanitize(model.Address);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(rulesetName));

            // Then
            Assert.Equal(0, result.Errors.Count);
        }

        [Fact]
        public async Task Employee_DateCreated_NotSet()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.EmpDateCreated = DateTime.Today;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.DateCreatedReadonly);
        }

        [Theory]
        [InlineData(RuleSetNames.Create, 0)]
        [InlineData(RuleSetNames.Update, EmployeeHelper.TestEmployeeId)]
        public async Task Employee_EmployeeNUmber_Required(string rulesetName, long employeeId)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(employeeId);
            model.EmployeeNumber = null;

            this.companyService.Setup(_ => _.ShouldGenerateEmployeeNumber(It.IsAny<long>())).Returns(Task.FromResult(false));

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(rulesetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmployeeNumberRequired);
        }

        [Fact]
        public async Task Employee_New_EmployeeNumber_Not_Exist()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();

            this.companyService.Setup(_ => _.ShouldGenerateEmployeeNumber(It.IsAny<long>())).Returns(Task.FromResult(false));
            this.employeeService.Setup(_ => _.CheckEmployeeNumberExists(It.IsAny<long>(), It.IsAny<string>())).Returns(Task.FromResult(true));

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmployeeNumberAlreadyExistsInThisCompany);
        }

        [Fact]
        public async Task Employee_Update_EmployeeNumber_Not_Exist()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            this.companyService.Setup(_ => _.ShouldGenerateEmployeeNumber(It.IsAny<long>())).Returns(Task.FromResult(false));
            this.employeeService.Setup(_ => _.CheckEmployeeNumberExistsAsync(It.IsAny<long>(), model.EmployeeId, It.IsAny<string>())).Returns(Task.FromResult(true));

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmployeeNumberAlreadyExistsInThisCompany);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_FirstName_Format_Invalid(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            model.FirstName = "Numeric Name 212121";

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(It.IsAny<long>())).ReturnsAsync((int)TaxCountry.SouthAfrica);

            // When
            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.ShouldHaveValidationErrorFor(_ => _.FirstName)
                .WithErrorMessage(ErrorCodes.Employee.FirstNameInvalidFormat);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_MiddleName_Format_Invalid(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            model.EmpMiddleName = "Numeric Name 212121";

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(It.IsAny<long>())).ReturnsAsync((int)TaxCountry.SouthAfrica);

            // When
            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.ShouldHaveValidationErrorFor(_ => _.EmpMiddleName)
                .WithErrorMessage(ErrorCodes.Employee.MiddleNameInvalidFormat);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_LastName_Format_Invalid(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            model.LastName = "Numeric Name 212121";

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(It.IsAny<long>())).ReturnsAsync((int)TaxCountry.SouthAfrica);
            this.employmentStatusService.Setup(_ => _.GetEmployeeNatureOfPersonCodeAsync(It.IsAny<long>())).ReturnsAsync("A");

            // When
            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.ShouldHaveValidationErrorFor(_ => _.LastName)
                .WithErrorMessage(ErrorCodes.Employee.LastNameInvalidFormat);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_Initials_Format_Invalid(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            model.EmpInitials = "Nê, ë";

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(It.IsAny<long>())).ReturnsAsync((int)TaxCountry.SouthAfrica);

            // When
            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.ShouldHaveValidationErrorFor(_ => _.EmpInitials)
                .WithErrorMessage(ErrorCodes.Employee.InitialsInvalidFormat);
        }

        [Theory]
        [InlineData(RuleSetNames.Create, 0, "BR")]
        [InlineData(RuleSetNames.Update, EmployeeHelper.TestEmployeeId, "BR")]
        public async Task Employee_EmployeeNumber_Length_Invalid_Brazil(string rulesetName, long employeeId, string countryCode)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(employeeId);
            model.EmployeeNumber = "1234567890123456789011234567999"; // 30 character limit

            this.companyService.Setup(_ => _.GetTaxCountryCodeAsync(It.IsAny<long>())).ReturnsAsync(countryCode);
            this.companyService.Setup(_ => _.ShouldGenerateEmployeeNumber(It.IsAny<long>())).Returns(Task.FromResult(false));
            this.employeeService.Setup(_ => _.CheckEmployeeNumberExistsAsync(It.IsAny<long>(), model.EmployeeId, It.IsAny<string>())).Returns(Task.FromResult(false));

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(rulesetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmployeeNumberMaxLength);
        }

        [Theory]
        [InlineData(RuleSetNames.Create, 0)]
        [InlineData(RuleSetNames.Update, EmployeeHelper.TestEmployeeId)]
        public async Task Employee_EmployeeNumber_Length_Invalid(string rulesetName, long employeeId)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(employeeId);
            model.EmployeeNumber = "123456789012345678901"; // 20 character limit

            this.companyService.Setup(_ => _.ShouldGenerateEmployeeNumber(It.IsAny<long>())).Returns(Task.FromResult(false));
            this.employeeService.Setup(_ => _.CheckEmployeeNumberExistsAsync(It.IsAny<long>(), model.EmployeeId, It.IsAny<string>())).Returns(Task.FromResult(false));

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(rulesetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmployeeNumberMaxLength);
        }

        [Fact]
        public async Task Employee_EmployeeNumber_Change_NotAllowed()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            this.companyService.Setup(_ => _.ShouldGenerateEmployeeNumber(It.IsAny<long>())).Returns(Task.FromResult(true));
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), nameof(Employee.EmployeeNumber))).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmployeeNumberChangeNotAllowed);
        }

        [Theory]
        [InlineData(true, true)]
        [InlineData(true, false)]
        [InlineData(false, true)]
        [InlineData(false, false)]
        public async Task Employee_Empty_Email_Allowed(bool userExist, bool emailHasChanged)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            model.Email = string.Empty;

            this.userService.Setup(_ => _.IsEmployeeLinkedToUserAsync(EmployeeHelper.TestEmployeeId)).Returns(Task.FromResult(userExist));
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), nameof(Employee.Email))).Returns(emailHasChanged);

            // When

            this.addressService.Sanitize(model.Address);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (emailHasChanged && userExist)
            {
                result.Errors.WithErrorMessage(ErrorCodes.Employee.EmailLinkedToUser);
            }

            if (!emailHasChanged || !userExist)
            {
                Assert.Equal(0, result.Errors.Count);
            }
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task Employee_Can_Delete(bool hasPayslipHeader)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            this.employeeProfileService.Setup(_ => _.CanDeleteAsync(It.IsAny<long>())).Returns(Task.FromResult(!hasPayslipHeader));

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Delete));

            // Then
            if (hasPayslipHeader)
            {
                result.Errors.WithErrorMessage(ErrorCodes.Employee.CanNotDeleteEmployee);
            }
            else
            {
                Assert.Equal(0, result.Errors.Count);
            }
        }

        [Theory]
        [InlineData(RuleSetNames.Update)]
        [InlineData(RuleSetNames.Create)]
        public async Task Employee_Initials_Length_Invalid(string rulesetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.EmpInitials = "123456"; // Max 5 characters

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(rulesetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InitialsMaxChar);
        }

        [Theory]
        [InlineData(RuleSetNames.Update)]
        [InlineData(RuleSetNames.Create)]
        public async Task Employee_Initials_Required(string rulesetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.EmpInitials = null;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(rulesetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InitialsRequired);
        }

        [Theory]
        [InlineData(RuleSetNames.Update)]
        [InlineData(RuleSetNames.Create)]
        public async Task Employee_WorkExtension_Length_Invalid(string rulesetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.WorkExtension = "1234567"; // Max 6 characters

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(rulesetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.WorkExtensionMaxChar);
        }

        [Theory]
        [InlineData("99010158001881", ErrorCodes.Employee.IdNumberNotCorrectLength)]
        [InlineData("990101580018", ErrorCodes.Employee.IdNumberNotCorrectLength)]
        [InlineData("9901014876080", ErrorCodes.Employee.GenderNotMatchingIdNumber)]
        [InlineData("9901015876188", ErrorCodes.Employee.CitizanshipNotMatchingIdNumber)]
        [InlineData("9901045876083", ErrorCodes.Employee.DateOfBirthNotMatchingIdNumber)]
        [InlineData("9955015876089", ErrorCodes.Employee.IdNumberInvalid)]
        [InlineData("9901555876089", ErrorCodes.Employee.IdNumberInvalid)]
        [InlineData("1111111111111", ErrorCodes.Employee.IdNumberInvalid)]
        public async Task Employee_IdNumber_Invalid(string idNumber, string expectedError)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.employmentStatusService.Setup(_ => _.GetEmployeeCurrentIdentityAsync(EmployeeHelper.TestEmployeeId)).ReturnsAsync(ValueTuple.Create(true, 1, idNumber, IdentityTypeCodes.ID));

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            if (!string.IsNullOrEmpty(expectedError))
            {
                result.Errors.WithErrorMessage(expectedError);
            }
            else
            {
                Assert.Equal(0, result.Errors.Count);
            }
        }

        [Theory]
        [InlineData("8412075576081", "07-12-1984")]
        [InlineData("8509255601082", "25-09-1985")]
        [InlineData("0002165576089", "16-02-2000")]
        [InlineData("3302015827089", "01-02-1933")]
        [InlineData("9901015576085", "01-01-1999")]
        public async Task Employee_IdNumber_IsValid(string idNumber, string birthDay)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);

            model.Birthday = DateTime.ParseExact(birthDay, "dd-MM-yyyy", CultureInfo.InvariantCulture);

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.employmentStatusService.Setup(_ => _.GetEmployeeCurrentIdentityAsync(EmployeeHelper.TestEmployeeId)).ReturnsAsync(ValueTuple.Create(true, 1, idNumber, IdentityTypeCodes.ID));
            this.genderService.Setup(_ => _.GetGenderCodeAsync(It.IsAny<int>())).ReturnsAsync(GenderConstants.Codes.Male);

            // When
            this.addressService.Sanitize(model.Address);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Update));

            // Then
            Assert.Equal(0, result.Errors.Count);
        }

        [Fact]
        public async Task Employee_BirthDay_Required()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.Birthday = DateTime.MinValue;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.BirthdayRequired);
        }

        [Fact]
        public async Task Employee_BirthDay_Invalid()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.Birthday = DateTime.Today.AddDays(PaySpaceConstants.MaxAgeAllowed * -1);

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidBirthDate);
        }

        [Fact]
        public async Task Employee_Gender_Required()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.GenderId = 0;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.GenderRequired);
        }

        [Theory]
        [InlineData("domain.com")]
        [InlineData("test@domaincom")]
        [InlineData("t@<EMAIL>")]
        [InlineData("test_testcom")]
        public async Task Employee_Email_Invalid(string email)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.Email = email;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidEmailFormat);
        }

        [Fact]
        public async Task Employee_Email_Employee_Exist()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.employeeService.Setup(_ => _.CheckEmployeeEmailExists(It.IsAny<string>(), It.IsAny<long>())).ReturnsAsync(true);
            this.userService.Setup(_ => _.CheckEmailExists(It.IsAny<string>(), It.IsAny<long>())).ReturnsAsync(false);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmailAlreadyExists);
        }

        [Fact]
        public async Task Employee_Email_User_Exist()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.employeeService.Setup(_ => _.CheckEmployeeEmailExists(It.IsAny<string>(), It.IsAny<long>())).ReturnsAsync(false);
            this.userService.Setup(_ => _.CheckEmailExists(It.IsAny<string>(), It.IsAny<long>())).ReturnsAsync(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.EmailAlreadyExists);
        }

        [Theory]
        [InlineData("[1]  1  1  1")]
        [InlineData("1a222ss")]
        public async Task Employee_WorkNumber_Invalid(string number)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.WorkNumber = number;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidWorkNumber);
        }

        [Theory]
        [InlineData("[1]  1  1  1")]
        [InlineData("1a222ss")]
        public async Task Employee_CellNumber_Invalid(string number)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.CellNumber = number;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidCellNumber);
        }

        [Theory]
        [InlineData("[1]  1  1  1")]
        [InlineData("1a222ss")]
        public async Task Employee_HomeNumber_Invalid(string number)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.HomeNumber = number;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidHomeNumber);
        }

        [Fact]
        public async Task Employee_MaritalStatus_Required()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();

            model.MaritalStatusId = null;

            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            this.companySettingService.Setup(_ => _.IsActiveAsync(It.IsAny<long>(), CompanySettingCode.BasicProfile.MaritalStatus)).ReturnsAsync(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.MaritalStatusRequired);
        }

        [Fact]
        public async Task Employee_EtiExempt_valid()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();

            model.EtiExempt = true;

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(It.IsAny<long>())).ReturnsAsync(1); // South africa

            // When
            this.addressService.Sanitize(model.Address);

            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            Assert.Equal(0, result.Errors.Count);
        }

        [Fact]
        public async Task Employee_ContactNumber_Required()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1));

            model.WorkNumber = string.Empty;
            model.HomeNumber = string.Empty;
            model.CellNumber = string.Empty;

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidOneNumberRequired);
        }

        [Fact]
        public async Task Employee_Frequency_Required()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();

            model.CompanyFrequencyId = null;

            this.companySettingService.Setup(_ => _.IsActiveAsync(It.IsAny<long>(), CompanySettingCode.BasicProfile.CompanyFrequency)).ReturnsAsync(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.CompanyFrequencyRequired);
        }

        [Fact]
        public async Task Employee_Race_Required()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.RaceId = null;

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.RaceRequired);
        }

        public async Task Employee_HomeNumber_NumberPrefixInvalid(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.WorkNumber = "+21564648461";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidSARSNumberFormat);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_WorkNumber_NumberPrefixInvalid(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.WorkNumber = "+21564648461";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidSARSNumberFormat);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_CellNumber_NumberPrefixInvalid(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.WorkNumber = "+21564648461";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidSARSNumberFormat);
        }

        [Theory]
        [InlineData("test@invalid%.com")]
        [InlineData("#<EMAIL>")]
        [InlineData("te$$<EMAIL>")]
        [InlineData("<EMAIL>*")]
        [InlineData("<EMAIL>")]
        [InlineData(" <EMAIL>")]
        [InlineData("<EMAIL> ")]
        public void Employee_EmailAdrress_InvalidSpecialCharacters(string email)
        {
            // When
            var result = ValidationHelper.EmailAddressContainsInvalidSpecialCharacters(email);

            // Then
            Assert.False(result);
        }

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("te$<EMAIL>")]
        [InlineData("1valid#@valid.co.za")]
        [InlineData("1valid#@valid.com")]
        [InlineData("test'<EMAIL>")]
        public void Employee_EmailAdrress_ValidEmailAdress(string email)
        {
            // When
            var result = ValidationHelper.EmailAddressContainsInvalidSpecialCharacters(email);

            // Then
            Assert.True(result);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_HomeNumber_OnlyNumericCharactersAllowed(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.HomeNumber = "00123456789test";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.NoNonNumericOrWhiteSpacesAllowed);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_WorkNumber_OnlyNumericCharactersAllowed(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.WorkNumber = "00123456789test";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.NoNonNumericOrWhiteSpacesAllowed);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_CellNumber_OnlyNumericCharactersAllowed(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.CellNumber = "00123456789t";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.NoNonNumericOrWhiteSpacesAllowed);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_HomeNumber_NoWhiteSpacesAllowed(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.HomeNumber = "0012 3456789";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.NoNonNumericOrWhiteSpacesAllowed);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_WorkNumber_NoWhiteSpacesAllowed(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.WorkNumber = "0012 3456789";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.NoNonNumericOrWhiteSpacesAllowed);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Employee_CellNumber_NoWhiteSpacesAllowed(string ruleSetName)
        {
            // Given
            var model = EmployeeHelper.CreateEmployee(EmployeeHelper.TestEmployeeId);
            model.CellNumber = "0012 3456789";

            this.companyService.Setup(_ => _.GetTaxCountryIdAsync(158)).Returns(Task.FromResult(1)); // South africa
            this.applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);

            // When
            var result = await this.validator.ValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.NoNonNumericOrWhiteSpacesAllowed);
        }
    }
}