namespace PaySpace.Venuta.Data.Models.Validation.Tests.Employees
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using FluentValidation;
    using FluentValidation.TestHelper;

    using Microsoft.Extensions.Localization;

    using Moq;

    using NSubstitute;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.CustomFields;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Employees;
    using PaySpace.Venuta.Data.Models.Validation.Tests.Helpers;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Employees;

    using Xunit;

    public class EmployeeAddressValidatorTests
    {
        private readonly EmployeeValidator employeeValidator;

        public EmployeeAddressValidatorTests()
        {

            var localizerFactory = new Mock<IStringLocalizerFactory>();
            var applicationContext = new Mock<ApplicationContext>();
            var customFieldService = new Mock<ICustomFieldService>();
            var tenantProvider = new Mock<ITenantProvider>();
            var companyService = new Mock<ICompanyService>();
            var companySettingService = new Mock<ICompanySettingService>();
            var employeeService = new Mock<IEmployeeService>();
            var employeeProfileService = new Mock<IEmployeeProfileService>();
            var employeeAddressService = new Mock<IEmployeeAddressService>();
            var employmentStatusService = new Mock<IEmploymentStatusService>();
            var userService = new Mock<IUserService>();
            var employeeHistoryService = new Mock<IEmployeeHistoryService>();
            var companyFrequencyService = new Mock<ICompanyFrequencyService>();
            var userRegionService = new Mock<IUserRegionService>();
            var customFieldListValidator = new CustomFieldListValidator<EmployeeCustomFieldValue>(customFieldService.Object, CustomFieldHelper.CreateMock<EmployeeCustomFieldValue>().Object);
            var countryServiceFactory = Substitute.For<ICountryServiceFactory>();
            var genderService = Substitute.For<IGenderService>();

            countryServiceFactory.Create<ICustomFieldListValidator<EmployeeCustomFieldValue>>().Returns(customFieldListValidator);

            tenantProvider.Setup(_ => _.GetCompanyId()).Returns(158);
            customFieldService.Setup(_ => _.GetCustomFieldFormFieldsAsync(It.IsAny<long>(), It.IsAny<string>())).Returns(Task.FromResult(new List<CustomFieldFormField>()));
            applicationContext.Setup(_ => _.IsFieldModified(It.IsAny<Employee>(), It.IsAny<string>())).Returns(true);
            employeeAddressService.Setup(_ => _.GetEmployeeAddressTypesAsync(EmployeeHelper.TestEmployeeId)).Returns(Task.FromResult(EmployeeHelper.GetAddressTypes()));
            companyFrequencyService.Setup(_ => _.GetNameAsync(EmployeeHelper.TestEmployeeId)).Returns(Task.FromResult("Test"));
            userRegionService.Setup(_ => _.DoesEmailExistAsync(It.IsAny<string>())).Returns(Task.FromResult(false));

            this.employeeValidator = new EmployeeValidator(
                applicationContext.Object,
                StringLocalizerHelper.CreateMock<EmployeeValidator>(),
                StringLocalizerHelper.CreateMock<AddressValidator>(),
                StringLocalizerHelper.CreateMock<EmployeeEmploymentStatus>(),
                StringLocalizerHelper.CreateMock<EmployeePayRate>(),
                localizerFactory.Object,
                customFieldService.Object,
                tenantProvider.Object,
                companyService.Object,
                companySettingService.Object,
                employeeService.Object,
                employeeProfileService.Object,
                employeeAddressService.Object,
                employmentStatusService.Object,
                userService.Object,
                employeeHistoryService.Object,
                companyFrequencyService.Object,
                countryServiceFactory,
                genderService,
                userRegionService.Object);
        }

        [Fact]
        public async Task EmployeeAddress_Physical_Address_Required()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            model.Address.Remove(model.Address.First(_ => _.AddressType == AddressType.Physical));

            // When
            var result = await this.employeeValidator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.PhysicalAddressRequired);
        }

        [Fact]
        public async Task EmployeeAddress_Duplicate_AddressType_NotAllowed()
        {
            // Given
            var model = EmployeeHelper.CreateEmployee();
            foreach (var address in model.Address)
            {
                address.AddressType = AddressType.Postal;
            }

            // When
            var result = await this.employeeValidator.ValidateAsync(model, options => options.IncludeRuleSets(RuleSetNames.Create));

            // Then
            result.Errors.WithErrorMessage(ErrorCodes.Employee.DuplicateAddressTypes);
        }
    }
}