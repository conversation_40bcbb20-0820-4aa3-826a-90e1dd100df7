namespace PaySpace.Venuta.Data.Models.Validation.Tests.Employees.India
{

    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using FluentValidation.TestHelper;

    using Microsoft.Extensions.Localization;

    using Moq;

    using NSubstitute;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.CustomFields.Results;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Validation.Employees;
    using PaySpace.Venuta.Data.Models.Validation.Employees.IN;
    using PaySpace.Venuta.Data.Models.Validation.Tests.Helpers;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Messaging.Users.Services;
    using PaySpace.Venuta.Modules.CompanySettings.Abstractions;
    using PaySpace.Venuta.Modules.EmployeeHistory.Abstractions;
    using PaySpace.Venuta.Security.Abstractions;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Abstractions.Company;
    using PaySpace.Venuta.Services.Employees;

    using Xunit;

    public class IndiaEmployeeValidator_BirthdayTests
    {
        private readonly EmployeeValidator validator;

        public IndiaEmployeeValidator_BirthdayTests()
        {
            var employeeLocalizer = StringLocalizerHelper.CreateMock<EmployeeValidator>();
            var addressLocalizer = StringLocalizerHelper.CreateMock<AddressValidator>();
            var statusLocalizer = StringLocalizerHelper.CreateMock<EmployeeEmploymentStatus>();
            var payRateLocalizer = StringLocalizerHelper.CreateMock<EmployeePayRate>();

            var tenantProvider = new Mock<ITenantProvider>();
            tenantProvider.Setup(_ => _.GetCompanyId()).Returns(158);
            tenantProvider.Setup(_ => _.GetTaxCountryCode()).Returns("IN");

            var customFieldService = new Mock<ICustomFieldService>();
            customFieldService
                .Setup(_ => _.GetCustomFieldFormFieldsAsync(It.IsAny<long>(), It.IsAny<string>()))
                .ReturnsAsync(new List<CustomFieldFormField>());

            var employeeAddressService = new Mock<IEmployeeAddressService>();
            employeeAddressService
                .Setup(_ => _.GetEmployeeAddressTypesAsync(It.IsAny<long>()))
                .ReturnsAsync(EmployeeHelper.GetAddressTypes());

            var companyFrequencyService = new Mock<ICompanyFrequencyService>();
            companyFrequencyService
                .Setup(_ => _.GetNameAsync(It.IsAny<long>()))
                .ReturnsAsync("Test");

            var userRegionService = new Mock<IUserRegionService>();
            userRegionService
                .Setup(_ => _.DoesEmailExistAsync(It.IsAny<string>()))
                .ReturnsAsync(false);

            var countryServiceFactory = Substitute.For<ICountryServiceFactory>();
            countryServiceFactory.Create<ICustomFieldListValidator<EmployeeCustomFieldValue>>()
                .Returns(new CustomFieldListValidator<EmployeeCustomFieldValue>(
                    customFieldService.Object,
                    CustomFieldHelper.CreateMock<EmployeeCustomFieldValue>().Object));

            var genderService = new Mock<IGenderService>();
            genderService.Setup(_ => _.GetEmployeeGenderCodeAsync(It.IsAny<long>()))
                         .ReturnsAsync(GenderConstants.Codes.Male);
            genderService.Setup(_ => _.IsGenderValidForCountryAsync(It.IsAny<long>(), It.IsAny<int>()))
                         .ReturnsAsync(true);

            this.validator = new IndiaEmployeeValidator(
                Mock.Of<ApplicationContext>(),
                employeeLocalizer,
                addressLocalizer,
                statusLocalizer,
                payRateLocalizer,
                Mock.Of<IStringLocalizerFactory>(),
                customFieldService.Object,
                tenantProvider.Object,
                Mock.Of<ICompanyService>(),
                Mock.Of<ICompanySettingService>(),
                Mock.Of<IEmployeeService>(),
                Mock.Of<IEmployeeProfileService>(),
                employeeAddressService.Object,
                Mock.Of<IEmploymentStatusService>(),
                Mock.Of<IUserService>(),
                Mock.Of<IEmployeeHistoryService>(),
                companyFrequencyService.Object,
                countryServiceFactory,
                genderService.Object,
                userRegionService.Object
            );
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Should_Have_Error_When_Birthday_Is_Less_Than_14_Years_Ago(string ruleSetName)
        {
            var model = EmployeeHelper.CreateEmployee();
            model.Birthday = DateTime.Today.AddYears(-10).AddDays(1);

            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(ruleSetName));

            result.Errors.WithErrorMessage(ErrorCodes.Employee.InvalidMinimumAge);
        }
    }
}
