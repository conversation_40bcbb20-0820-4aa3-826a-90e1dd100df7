namespace PaySpace.Venuta.Data.Models.Validation.Tests.Employees.Spain
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading.Tasks;

    using FluentValidation.TestHelper;

    using Microsoft.Extensions.Localization;

    using Moq;

    using NSubstitute;

    using PaySpace.Venuta.Abstractions;
    using PaySpace.Venuta.Data.Models.CustomFields.Values;
    using PaySpace.Venuta.Data.Models.Employees;
    using PaySpace.Venuta.Data.Models.Enums;
    using PaySpace.Venuta.Data.Models.Validation.Employees.ES;
    using PaySpace.Venuta.DependencyInjection;
    using PaySpace.Venuta.Infrastructure;
    using PaySpace.Venuta.Modules.Leave.Abstractions;
    using PaySpace.Venuta.Services;
    using PaySpace.Venuta.Services.Abstractions;
    using PaySpace.Venuta.Services.Employees;

    using Xunit;

    public class SpainEmployeeSuspensionValidatorTests
    {
        private readonly SpainEmployeeSuspensionValidator validator;

        public SpainEmployeeSuspensionValidatorTests()
        {
            var localizer = new Mock<IStringLocalizer<EmployeeSuspension>>();
            localizer.Setup(_ => _[It.IsAny<string>()])
                .Returns<string>(label => new LocalizedString(label, label));

            var stringLocalizerFactory = new Mock<IStringLocalizerFactory>();
            stringLocalizerFactory.Setup(x => x.Create(It.IsAny<Type>()))
                .Returns(localizer.Object);

            var customFieldService = new Mock<ICustomFieldService>();
            var customFieldListValidator = new CustomFieldListValidator<EmployeeSuspensionCustomFieldValue>
                (customFieldService.Object, Helpers.CustomFieldHelper.CreateMock<EmployeeSuspensionCustomFieldValue>().Object);

            var countryServiceFactory = Substitute.For<ICountryServiceFactory>();
            countryServiceFactory.Create<ICustomFieldListValidator<EmployeeSuspensionCustomFieldValue>>()
                .Returns(customFieldListValidator);

            var tenantProvider = new Mock<ITenantProvider>();
            tenantProvider.Setup(_ => _.GetCompanyId())
                .Returns(158);
            tenantProvider.Setup(_ => _.GetUserId())
                .Returns(1);
            tenantProvider.Setup(_ => _.GetTaxCountryCode())
                .Returns("ES");

            var enumService = new Mock<IEnumService>();
            var suspensionReasons = new List<EnumSuspensionReason>
            {
                new () { SuspensionReasonId = 20, SuspensionCode = "20" },
                new () { SuspensionReasonId = 21, SuspensionCode = "21" },
                new () { SuspensionReasonId = 25, SuspensionCode = "25" },
                new () { SuspensionReasonId = 26, SuspensionCode = "26" },
                new () { SuspensionReasonId = 27, SuspensionCode = "27" }
            };
            enumService.Setup(s => s.GetSuspensionReasonAsync((int)TaxCountry.Spain, It.IsAny<CultureInfo?>()))
                .ReturnsAsync(suspensionReasons);

            this.validator = new SpainEmployeeSuspensionValidator(
                Mock.Of<ICompanyService>(),
                stringLocalizerFactory.Object,
                Mock.Of<IEmployeeService>(),
                Mock.Of<ICompanyRunEntityService>(),
                Mock.Of<ICustomFieldService>(),
                tenantProvider.Object,
                Mock.Of<IEmployeeLeaveService>(),
                Mock.Of<IEmployeeSuspensionService>(),
                Mock.Of<IEmploymentStatusService>(),
                countryServiceFactory,
                enumService.Object,
                Mock.Of<IGenderService>());
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Should_Have_Error_When_TransactionType_Is_Null(string action)
        {
            var model = new EmployeeSuspension { TransactionType = null };

            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(action));

            result.ShouldHaveValidationErrorFor(x => x.TransactionType)
                  .WithErrorMessage(ErrorCodes.Suspension.EsTransactionTypeRequired);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Should_Not_Have_Error_When_TransactionType_Is_Absenteeism(string action)
        {
            var model = new EmployeeSuspension { TransactionType = TransactionType.Absenteeism };

            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(action));

            result.ShouldNotHaveValidationErrorFor(x => x.TransactionType);
        }

        [Theory]
        [InlineData(RuleSetNames.Create)]
        [InlineData(RuleSetNames.Update)]
        public async Task Should_Not_Have_Error_When_TransactionType_Is_TemporaryIncapacity(string action)
        {
            var model = new EmployeeSuspension { TransactionType = TransactionType.TemporaryIncapacity };

            var result = await this.validator.TestValidateAsync(model, options => options.IncludeRuleSets(action));

            result.ShouldNotHaveValidationErrorFor(x => x.TransactionType);
        }

        [Theory]
        [InlineData(TransactionType.Absenteeism, 20, true)]
        [InlineData(TransactionType.Absenteeism, 25, false)]
        [InlineData(TransactionType.TemporaryIncapacity, 25, true)]
        [InlineData(TransactionType.TemporaryIncapacity, 21, false)]
        [InlineData((TransactionType)999, 20, false)] // Unsupported transaction type
        public async Task HasCorrectSuspensionReasonForType_ReturnsExpectedResult(TransactionType transactionType, int suspensionReasonId, bool expected)
        {
            var employeeSuspension = new EmployeeSuspension
            {
                TransactionType = transactionType,
                SuspensionReasonId = suspensionReasonId
            };

            var result = await this.validator.TestValidateAsync(employeeSuspension, options => options.IncludeRuleSets(RuleSetNames.Create));
            result.ShouldNotHaveValidationErrorFor(x => x.SuspensionReasonId);
        }
    }
}